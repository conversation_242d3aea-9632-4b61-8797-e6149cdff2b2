var detailUrl = OPAL.API.aaUrl + '/alarmOperateAssess/getAlarmOperateAsseessDetail';
$(function() {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        /**
         * 初始化
         */
        init: function() {
            /**
             *绑定事件
             */
            this.bindUi();
            page.logic.initTable();
        },
        bindUi: function() {
           
        },
        data: {
            // 设置查询参数
            param: {}
        },
        logic: {
            /**
             * 初始化数据
             */
            setData: function(data) {
                var param={};
                param.startTime=OPAL.util.dateFormat(OPAL.util.strToDate(data.startTime),"yyyy-MM-dd HH:mm:ss");
                param.endTime=OPAL.util.dateFormat(OPAL.util.strToDate(data.endDate),"yyyy-MM-dd HH:mm:ss");
                param.id=data.id;
                param.type=data.type;
                param.checkModel=data.checkModel;
                page.data.param = param;
                $("#tableDetail").bootstrapTable('refresh', {
                    "url": detailUrl,
                    "pageNumber":1
                });
            },
            initTable: function() {
                OPAL.ui.initBootstrapTable("tableDetail", {
                    pageSize:20,
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1 ) * data.pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'startTime',
                        align: 'center',
                        title: '发生时间',
                        width: '150px',
                        formatter: function(value, row, index) {
                            return OPAL.util.dateFormat(OPAL.util.strToDate(value), "yyyy-MM-dd HH:mm:ss");
                        }
                    }, {
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        field: 'prdtCellName',
                        title: '生产单元',
                        width: '120px'
                    }, {
                        field: 'tag',
                        title: '位号',
                        width: '100px'

                    }, {
                        title: "级别",
                        field: 'craftRankName',
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "描述",
                        field: 'des',
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        align: 'center',
                        width: '100px'
                    }, {
                        title: '事件类型',
                        field: 'eventTypeName',
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "当前值",
                        field: 'nowValue',
                        align: 'right',
                        width: '100px'
                    }],
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                }, page.logic.queryParams)
            },
            queryParams: function (p) { // 设置查询参数
                var params = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    now: Math.random()
                };
                return $.extend(true,page.data.param,params);
            }
        }
    };
    page.init();
    window.page = page;
});
