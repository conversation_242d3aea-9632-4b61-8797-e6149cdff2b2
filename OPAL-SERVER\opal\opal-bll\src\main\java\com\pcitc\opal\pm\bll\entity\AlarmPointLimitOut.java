package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.bll.entity.BasicEntity;
import lombok.Data;

import java.util.Date;


/*
 * 报警点实体
 * 模块编号：pcitc_opal_bll_class_AlarmPointEntity
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点实体
 */
@Data
public class AlarmPointLimitOut {

	/**
     * 装置编码
     */
    private String  UnitCode;
    /**
     * 装置名称
     */
    private String UnitName;
    /**
     *
     */
    private String DcsTagCode;
    /**
     *
     */
    private String MonLevelName;
    /**
     * 上限值
     */
    private Double UpLimitValue;
    /**
	 * 下限值
     */
    private Double DownLimitValue;
    /**
     * 上限值包含
     */
    private Integer UpLimitInclude;
    /**
     * 下限值包含
     */
    private Integer DownLimitInclude;
}
