var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var UnitPrdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var relevantTagConfigUrl = OPAL.API.afUrl + '/relevantTagConfig';
var relevantTagConfigDtlUrl = OPAL.API.afUrl + '/relevantTagConfig/getRelevantTagConfigDtl';
var deleteRelevantTagConfigDtlUrl = OPAL.API.afUrl + '/relevantTagConfig/deleteRelevantTagConfigDtl';
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.None;
var tag, alarmPointId;
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
            
        },
        bindUI: function () {
            $("#searchBtn").click(function() {
                page.logic.search();
            })
            $('#saveBtn').click(function () {
                page.logic.save();
            });
            $("#addBtn").click(function() {
                page.logic.add();
            })
            $("#delBtn").click(function() {
                page.logic.delAll();
            })
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
            $('#closePage').click(function () {
                page.logic.closeLayer(false);
            })
        },
        data: {
            param: {}
        },
        logic: {
            /**
             * 保存
             */
            save: function () {
                $('#unitId').next('.textbox').find('input').attr('name', 'unitId');
                $('#unitId').next('.textbox').addClass('form-control-tree');
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data = OPAL.form.getETCollectionData('AddOrEditModal');

                //处理提交类型
                var ajaxType = "POST";
                if (pageMode == PageModelEnum.NewAdd) {
                    window.pageLoadMode = PageLoadMode.Reload;
                }
                else if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                    window.pageLoadMode = PageLoadMode.Refresh;
                }
                $.ajax({
                    url: relevantTagConfigUrl,
                    async: false,
                    type: ajaxType,
                    data: JSON.stringify(data),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！",{
                                time: 1000
                            },function() {
                                $("#relevantTagConfigId").val(result);
                                page.data.param = {
                                    "relevantTagConfigId":result
                                }
                                pageMode = PageModelEnum.Edit;
                                $("#addBtn").attr("disabled",false);
                                $("#delBtn").attr("disabled",false);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        window.pageLoadMode = PageLoadMode.None;
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                //初始化装置树
                page.logic.initUnitTree();
                $("#pageTitle").text(data.title);
                if (pageMode == PageModelEnum.NewAdd) {
                    page.logic.initTable();
                    return;
                }
                page.data.param = {
                    "relevantTagConfigId":data.relevantTagConfigId
                }
                $("#addBtn").attr("disabled",false);
                $("#delBtn").attr("disabled",false);
                page.logic.initTable();
                page.logic.reloadTable();
                $.ajax({
                    url: relevantTagConfigUrl + "/" + data.relevantTagConfigId + "?now=" + Math.random(),
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function (data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        OPAL.form.setData('AddOrEditModal', entity);
                        $("#unitId").combotree('setValue', entity['unitId']);
                        page.logic.searchUnitPrdt(entity["unitId"]);
                        $("#prdtCellId").val(entity["prdtCellId"]);
                        $("#tag").val(entity.tag);
                    },
                    error: function (XMLHttpRequest, textStatus) {}
                });
            },
            /**
             * 初始化表格
             */
            initTable: function () {
                OPAL.ui.initBootstrapTable("table",{
                    cache:false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '90px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "序号",
                        field: '',
                        rowspan: 1,
                        align: 'center',
                        width: '80px',
                        formatter: function (value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1) * pageSize;
                        },
                    }, {
                        title: "相关位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellSname',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "装置",
                        field: 'unitSname',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "计量单位",
                        field: 'measUnitName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "描述",
                        field: 'des',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }],
                    onLoadSuccess: function (data) {
                        if(data.rows.length > 0){
                            $("#unitId").combo('disable');
                            $("#prdtCellId").attr('disabled',true);
                            $("#searchBtn").hide();
                        }else {
                            $("#unitId").combo('enable');
                            $("#prdtCellId").attr('disabled',false);
                            $("#searchBtn").show();
                        }
                        var tds = $('#table').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                },page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param,param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a  name="TableView" href="javascript:window.page.logic.delSingle(\'' + rowData.relevantTagConfigDtlId + '\')" >删除</a>'
                ]
            },
            /**
             * 搜索
             */
            reloadTable: function () {
                $("#table").bootstrapTable('refresh', {
                    url: relevantTagConfigDtlUrl,
                    "pageNumber":1
                });
            },
            /**
             * 装置新增或者编辑详细页面
             */
            search: function () {
                if($("#unitId").val() =='') {
                    layer.msg("请选择装置！");
                    return;
                }
                if($("#prdtCellId").val() =='') {
                    layer.msg("请选择生产单元！");
                    return;
                }
                layer.open({
                    type: 2,
                    title: '&nbsp;',
                    closeBtn: 1,
                    area: ['99%', '99%'],
                    shadeClose: false,
                    content: 'RelevantTagConfigAdd.html?'+ Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "unitId": $("#unitId").val(),
                            "prdtCellId": $("#prdtCellId").val(),
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            $("#tag").val(tag);
                            $("#alarmPointId").val(alarmPointId);
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            add: function() {
                layer.open({
                    type: 2,
                    title: '&nbsp;',
                    closeBtn: 1,
                    area: ['99%', '99%'],
                    shadeClose: false,
                    content: 'RelevantTagConfigDtlAdd.html?'+ Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "unitId": $("#unitId").val(),
                            "relevantTagConfigId": $("#relevantTagConfigId").val(),
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.reloadTable();
                            window.pageLoadMode = PageLoadMode.Refresh;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 批量删除
             */
            delAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections');
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.relevantTagConfigDtlId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: deleteRelevantTagConfigDtlUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function (id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: deleteRelevantTagConfigDtlUrl,
                        async: false,
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect("unitId", commonUnitTreeUrl, "id", "parentId", "sname", {
                    multiple: false,
                    onlyLeafCheck: true,
                    async:false,
                    data: {
                        'enablePrivilege': true
                    },
                    onSelect: function(node) {
                        $("#tag").val('');
                        page.logic.searchUnitPrdt(node.id);
                        // OPAL.ui.getComboMultipleSelect("prdtCellId", UnitPrdtCellUrl, {
                        //     keyField: "prdtCellId",
                        //     valueField: "sname",
                        //     async:false,
                        //     data: {
                        //         "unitId": node.id
                        //     },
                        //     treeviewConfig: {
                        //         multiple: false,
                        //         cascadeCheck: false,
                        //         onlyLeafCheck: true,
                        //         hasDownArrow: true,
                        //         lines: false,
                        //         animate: false,
                        //     }
                        // }, false, function() {
                        //     $("#prdtCellId").combotree('selectFirstRecord');
                        // });
                    }
                }, false, function() {
                });
                $("#unitId").combotree("getValues");
            },
            /**
             * 查询装置下的生产单元
             */
            // searchUnitPrdt: function(unitId) {
            //     OPAL.ui.getComboMultipleSelect("prdtCellId", UnitPrdtCellUrl, {
            //             keyField: "prdtCellId",
            //             valueField: "sname",
            //             async: false,
            //             data: {
            //                 "unitId": unitId,
            //                 "isAll":true
            //             },
            //             treeviewConfig: {
            //                 multiple: false,
            //                 cascadeCheck: false,
            //                 onlyLeafCheck: true,
            //                 hasDownArrow: true,
            //                 lines: false
            //             }
            //         }, true,
            //         function() {
            //             $("#tag").val('');
            //         }
            //     );
            // },
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getCombobox("prdtCellId", UnitPrdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    async: false,
                    selectFirstRecord: true,
                    data: {
                        "unitId": unitId,
                        'isAll': false
                    },
                }, function(){
                    $("#prdtCellIds option:first").text('全部');
                }, function() {
                    // $("#tag").val('');
                    $("#prdtCellIds option:first").text('全部');
                });
            },
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal',{
                    rules: {
                        unitId: {
                            required: true
                        },
                        name: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        prdtCellId:{
                            required: true
                        },
                        tag:{
                            required: true
                        },
                    }
                })
                
            }
        }

    }
    page.init();
    window.page = page;
});