package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.*;
import com.pcitc.opal.common.CommonEnum.DateTypeEnum;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.RelevantAlarmAnalysisRepositoryCustom;
import org.apache.commons.beanutils.converters.DateConverter;
import org.springframework.beans.factory.annotation.Autowired;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.*;

/*
 * RelevantAlarmAnalysis实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_RelevantAlarmAnalysisRepositoryImpl
 * 作       者：dageng.sun
 * 创建时间：2018/08/03
 * 修改编号：1
 * 描       述：RelevantAlarmAnalysis实体的Repository实现
 */
public class RelevantAlarmAnalysisRepositoryImpl extends BaseRepository<Object[], Long> implements RelevantAlarmAnalysisRepositoryCustom {
	@Autowired
	private DbConfig dbConfig;

	/**
	 * 相关性报警分析
	 * 
	 * <AUTHOR> 2018-08-03 
	 * @param alarmPointIds 报警点主键id数组
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param dateType 间粒度枚举
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getRelevantProcessAnalysis(Long[] alarmPointIds, Date startTime, Date endTime, DateTypeEnum dateType) {
		try {
			Map<String, Object> map=getHqlAndParams(alarmPointIds, startTime, endTime, dateType, 1);
			// 调用基类方法查询返回结果
			Query query = getEntityManager().createQuery((String) map.get("hql"));
            this.setParameterList(query, (Map<String, Object>)map.get("paramList"));
            return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}
	
	/**
	 * 相关性操作分析
	 * 
	 * <AUTHOR> 2018-08-03 
	 * @param alarmPointIds 报警点主键id数组
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param dateType 间粒度枚举
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getRelevantOperateAnalysis(Long[] alarmPointIds, Date startTime, Date endTime, DateTypeEnum dateType) {
		try {
			Map<String, Object> map=getHqlAndParams(alarmPointIds, startTime, endTime, dateType, 2);
			// 调用基类方法查询返回结果
			TypedQuery<Object[]> query = getEntityManager().createQuery((String)map.get("hql"), Object[].class);
            this.setParameterList(query, (Map<String, Object>)map.get("paramList"));
            return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 抽取公共方法，获得hql语句和params参数
	 * 
	 * <AUTHOR> 2017-10-30
	 * @param alarmPointIds 报警点主键id数组
	 * @param startTime 开始日期
     * @param endTime   结束日期
     * @param dateType 时间粒度
	 * @param type 事件类型
	 * @return Map<String,Object> 
	 */
	public Map<String, Object> getHqlAndParams(Long[] alarmPointIds, Date beginTime, Date endTime, CommonEnum.DateTypeEnum dateType, int type){
		// 查询字符串
		StringBuilder hql = new StringBuilder();
		//3.计算数据分组中的小时
		String queryTime = CommonPropertiesReader.getValue("query.time");
        DateConverter dateConverter = new DateConverter();
        dateConverter.setPattern("HH:mm:ss");
        Date dateTime = dateConverter.convert(Date.class, queryTime);
		Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateTime);
        int hours = calendar.get(Calendar.HOUR_OF_DAY);
        float h=(float)hours/(float)24;
		// 参数集合
		Map<String, Object> paramList = new HashMap<String, Object>();
		String selectTimeStr="";
		if(type == 1){
			switch (dateType) {
				case Hour:
					//selectTimeStr="concat(to_char(ae.alarmTime,'yyyy-MM-dd HH24'),':00')";
					selectTimeStr="concat("+ DbConversion.DbDateTransformYmdhToStr("ae.alarmTime")+",':00')";
					break;
				case Day:
					//selectTimeStr="to_char(ae.alarmTime-"+h+",'yyyy-MM-dd')";
					selectTimeStr=DbConversion.DbDateSubTransformYmdToStr("ae.alarmTime",h,hours);
					break;
				case Week:
//					Calendar cal = Calendar.getInstance();
//					cal.setTime(beginTime);
//					int w = cal.get(Calendar.DAY_OF_WEEK);
//					selectTimeStr="to_char(next_day(ae.alarmTime-7-"+h+","+w+"),'YYYY-MM-DD')";
					selectTimeStr = DbConversion.numtodsintervalYmdWeekHql("ae.alarmTime", hours, beginTime);
					break;
				case Month:
//						selectTimeStr="to_char(ae.alarmTime-"+h+",'yyyy-MM')";
					selectTimeStr=DbConversion.DbDateSubTransformYmToStr("ae.alarmTime",h,hours);
					break;
				default:
					break;
			}
		}else if(type == 2){
			switch (dateType) {
				case Hour:
//						selectTimeStr="concat(to_char(ae.startTime,'yyyy-MM-dd HH24'),':00')";
					selectTimeStr="concat("+ DbConversion.DbDateTransformYmdhToStr("ae.startTime")+",':00')";
					break;
				case Day:
//						selectTimeStr="to_char(ae.startTime-"+h+",'yyyy-MM-dd')";
					selectTimeStr=DbConversion.DbDateSubTransformYmdToStr("ae.startTime",h,hours);
					break;
				case Week:
//					Calendar cal = Calendar.getInstance();
//					cal.setTime(beginTime);
//					int w = cal.get(Calendar.DAY_OF_WEEK);
//					selectTimeStr="to_char(next_day(ae.startTime-7-"+h+","+w+"),'YYYY-MM-DD')";
					selectTimeStr = DbConversion.numtodsintervalYmdWeekHql("ae.startTime", hours, beginTime);
					break;
				case Month:
//						selectTimeStr="to_char(ae.startTime-"+h+",'yyyy-MM')";
					selectTimeStr=DbConversion.DbDateSubTransformYmToStr("ae.startTime",h,hours);
					break;
				default:
					break;
			}
		}

		// 装置
		hql.append("select max(ap.tag),count(*),"+selectTimeStr+",max(pc.sname),max(pc.unitId),ap.alarmPointId ");
		hql.append(" from AlarmEvent ae left join ae.alarmPoint ap left join ae.eventType et left join ap.prdtCell pc ");
		// 报警点id
		hql.append("where ae.companyId=:companyId and ap.alarmPointId in (:alarmPointIds) ");
		hql.append("and ae.alarmPointId is not null and ap.inUse =1"
				+ " and ae.alarmFlagId is not null "
				+ " and ae.priority is not null ");
		List<Long> alarmPointIdsList = Arrays.asList(alarmPointIds);
		paramList.put("alarmPointIds", alarmPointIdsList);
		// 日期,事件类型
		if(type == 1){
			hql.append("and ae.alarmTime between :beginTime and :endTime ");
			hql.append("and et.eventTypeId="+CommonEnum.EventTypeEnum.ProcessEvent.getIndex());
		}else if(type == 2){
			hql.append("and ae.startTime between :beginTime and :endTime ");
			hql.append("and et.parentId="+CommonEnum.EventTypeEnum.OperateEvent.getIndex());
		}
		paramList.put("beginTime", beginTime);
		paramList.put("endTime", endTime);
		// 分组和排序
		hql.append(" group by "+selectTimeStr+",ap.alarmPointId order by case when ap.alarmPointId="+alarmPointIds[0]+" then 0 else ap.alarmPointId end,"+selectTimeStr);
		CommonProperty commonProperty = new CommonProperty();
		paramList.put("companyId",commonProperty.getCompanyId());

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("hql", hql.toString());
		map.put("paramList", paramList);
		return map;
	}
	
}
