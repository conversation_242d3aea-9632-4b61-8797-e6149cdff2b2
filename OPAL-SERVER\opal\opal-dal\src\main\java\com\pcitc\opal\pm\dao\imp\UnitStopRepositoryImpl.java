package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.UnitStopRepositoryCustom;
import com.pcitc.opal.pm.pojo.UnitStop;

import java.util.Date;

public class UnitStopRepositoryImpl extends BaseRepository<UnitStop, Long> implements UnitStopRepositoryCustom {
    @Override
    public Date getMaxMntDate(Integer companyId) {
        String hql = "select max(mntDate) from UnitStop where companyId = " + companyId;
        try {
            return getEntityManager().createQuery(hql, Date.class).getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }
}
