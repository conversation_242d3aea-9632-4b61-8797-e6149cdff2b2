package com.pcitc.opal.ad.dao;

import java.util.List;

import com.pcitc.opal.ad.pojo.AlarmFlag;

/*
 * AlarmFlag实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmFlagRepositoryCustom
 * 作       者：kun.zhao
 * 创建时间：2017/10/19 
 * 修改编号：1
 * 描       述：AlarmFlag实体的Repository的JPA自定义接口
 */
public interface AlarmFlagRepositoryCustom {
	
	/**
	 * 得到所有已启用的报警标识实体集合
	 * 
	 * <AUTHOR> 2017-10-19
	 * @return 报警标识实体集合
	 */
	List<AlarmFlag> getAllAlarmFlag();

	/**
	 * 根据报警标识Id获取单条报警标识实体集合
	 *
	 * @param alarmFlagId 报警标识id
	 * <AUTHOR> 2017-10-30
	 * @return 报警标识实体
	 */
	AlarmFlag getSingleAlarmFlag(Long alarmFlagId);
}
