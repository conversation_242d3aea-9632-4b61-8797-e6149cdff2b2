package com.pcitc.opal.af.bll.entity;

/*
 * 报警次数统计统计数据实体
 * 作  　  者：shufei.sui
 * 创建时间：2019/09/26
 * 修改编号：1
 * 描       述：报警次数统计统计数据实体
 */
public class AlarmNumStattEntity {
    /**
     * 全部报警次数总计
     */
    private Long allTotalAlarmQuantity;
    /**
     * 报警次数总计
     */
    private Long totalAlarmQuantity;

    /**
     * 紧急报警次数
     */
    private Long emergencyAlarmQuantity;

    /**
     * 重要报警次数
     */
    private Long importantAlarmQuantity;

    /**
     * 一般报警次数
     */
    private Long generalAlarmQuantity;
    /**
     * 空报警次数
     */
    private Long nullAlarmQuantity;

    /**
     * 装置Id
     */
    private String unitId;
    /**
     * 名称
     */
    private String name;

    /**
     * 简称
     */
    private String sname;

    /**
     * 导出时的序号
     */
    private int index;

    /**
     * 工艺
     */
    private Long technology;
    /**
     * 设备
     */
    private Long device;
    /**
     * 安全
     */
    private Long safe;
    /**
     * 生产
     */
    private Long other;
    /**
     * -
     */
    private Long nothing;

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
    public Long getAllTotalAlarmQuantity() {
        return allTotalAlarmQuantity;
    }

    public void setAllTotalAlarmQuantity(Long allTotalAlarmQuantity) {
        this.allTotalAlarmQuantity = allTotalAlarmQuantity;
    }

    public Long getTotalAlarmQuantity() {
        return totalAlarmQuantity;
    }

    public void setTotalAlarmQuantity(Long totalAlarmQuantity) {
        this.totalAlarmQuantity = totalAlarmQuantity;
    }

    public Long getEmergencyAlarmQuantity() {
        return emergencyAlarmQuantity;
    }

    public void setEmergencyAlarmQuantity(Long emergencyAlarmQuantity) {
        this.emergencyAlarmQuantity = emergencyAlarmQuantity;
    }

    public Long getImportantAlarmQuantity() {
        return importantAlarmQuantity;
    }

    public void setImportantAlarmQuantity(Long importantAlarmQuantity) {
        this.importantAlarmQuantity = importantAlarmQuantity;
    }

    public Long getGeneralAlarmQuantity() {
        return generalAlarmQuantity;
    }

    public void setGeneralAlarmQuantity(Long generalAlarmQuantity) {
        this.generalAlarmQuantity = generalAlarmQuantity;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSname() {
        return sname;
    }

    public void setSname(String sname) {
        this.sname = sname;
    }

    public Long getNullAlarmQuantity() {
        return nullAlarmQuantity;
    }

    public void setNullAlarmQuantity(Long nullAlarmQuantity) {
        this.nullAlarmQuantity = nullAlarmQuantity;
    }

    public Long getTechnology() {
        return technology;
    }

    public void setTechnology(Long technology) {
        this.technology = technology;
    }

    public Long getDevice() {
        return device;
    }

    public void setDevice(Long device) {
        this.device = device;
    }

    public Long getSafe() {
        return safe;
    }

    public void setSafe(Long safe) {
        this.safe = safe;
    }

    public Long getOther() {
        return other;
    }

    public void setOther(Long other) {
        this.other = other;
    }

    public Long getNothing() {
        return nothing;
    }

    public void setNothing(Long nothing) {
        this.nothing = nothing;
    }
}
