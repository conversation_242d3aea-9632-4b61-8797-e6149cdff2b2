package com.pcitc.opal.cm.bll;

import com.pcitc.opal.cm.bll.entity.CraftChangeInfoEntity;
import com.pcitc.opal.cm.dao.imp.ChangeEventCondition;
import com.pcitc.opal.cm.pojo.ChangeEventEntity;
import com.pcitc.opal.cm.pojo.ChangeMonitoringChartEntity;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/*
 * 事件类型对照配置业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmEventTypeCompService
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：事件类型对照配置业务逻辑层接口
 */
@Service
public interface ChangeEventService {

	/**
	 * 根据“企业ID”查询“状态”等于“2非法”或“3未知”的<变更事件>
	 * (“发生时间”大于等于系统当前时间-30天)
	 * <AUTHOR>
	 * @param comId  企业ID
	 */

	List<ChangeEventCondition> getChangeEventInfo(Long comId) throws Exception;


	/**
	 * 更新变更事件信息
	 *
	 * @param craftChangeInfo 工艺变更单信息
	 * @return 返回结果信息类
	 * <AUTHOR> 2018-01-22
	 */
	CommonResult updateChangeEventInfo(Long eventId, CraftChangeInfoEntity craftChangeInfo)throws Exception;

	/**
	 * 变更监控页面获取图形数据
	 * @param unitIds     装置数组
	 * @param prdtCellIds 生产单元
	 * @param workTeamIds 班组
	 * @param tag         位号
	 * @param status      变更状态
	 * @param startTime   开始时间
	 * @param endTime     结束时间
	 * @return ChangeMonitoringChartEntity实体
	 */
	List<ChangeMonitoringChartEntity> getChangeMonitoringChart(String[] unitIds, Long[] prdtCellIds, Long workTeamIds, String tag, Integer status, Date startTime, Date endTime);

	/**
	 * 变更监控页面查询数据
	 * @param unitIds     装置数组
	 * @param prdtCellIds 生产单元
	 * @param workTeamIds 班组
	 * @param tag         位号
	 * @param status      变更状态
	 * @param startTime   开始时间
	 * @param endTime     结束时间
	 */
	PaginationBean<ChangeEventEntity> getChangeMonitoring(String[] unitIds, Long[] prdtCellIds, Long workTeamIds, String tag, Integer status, Date startTime, Date endTime, Pagination page);
}
