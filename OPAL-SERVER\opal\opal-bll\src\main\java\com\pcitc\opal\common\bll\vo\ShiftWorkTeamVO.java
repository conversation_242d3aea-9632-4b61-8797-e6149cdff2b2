package com.pcitc.opal.common.bll.vo;

import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/*
 * 班组排班实体
 * 模块编号：pcitc_opal_bll_class_ShiftWorkTeamVO
 * 作       者：xuelei.wang
 * 创建时间：2018-07-21
 * 修改编号：1
 * 描       述：班组排班实体
 */
@SuppressWarnings({"unchecked","rawtypes", "serial"})
public class ShiftWorkTeamVO extends BaseResRep implements Serializable {
    /**
     * 班次编码
     */
    private String shiftCode;
    /**
     * 轮班域ID
     */
    private Long shiftAreaId;
    /**
     * 班次名称
     */
    private String shiftName;
    /**
     * 班次简称
     */
    private String shiftSName;
    /**
     * 班组编码
     */
    private String workTeamCode;
    /**
     * 班组名称
     */
    private String workTeamName;
    /**
     * 班组简称
     */
    private String workTeamSName;
    /**
     * 班次日期
     */
    private Date shiftDate;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 班次排序
     */
    private Long shiftSortNum;
    /**
     * 班组排序
     */
    private Long workTeamSortNum;
    /**
     * 轮班域名称
     */
    private String shiftAreaName;
    /**
     * 班组code集合
     */
    private List<String> workTeamCodeList=new ArrayList();

    public ShiftWorkTeamVO(Long shiftSortNum) {
        this.shiftSortNum = shiftSortNum;
    }

    public ShiftWorkTeamVO() {
    }

    public String getShiftId() {
        return shiftCode;
    }

    public void setShiftId(String shiftCode) {
        this.shiftCode = shiftCode;
    }

    public String getShiftCode() {
        return shiftCode;
    }

    public void setShiftCode(String shiftCode) {
        this.shiftCode = shiftCode;
    }

    public String getShiftName() {
        return shiftName;
    }

    public void setShiftName(String shiftName) {
        this.shiftName = shiftName;
    }

    public String getShiftSName() {
        return shiftSName;
    }

    public void setShiftSName(String shiftSName) {
        this.shiftSName = shiftSName;
    }

    public String getWorkTeamName() {
        return workTeamName;
    }

    public void setWorkTeamName(String workTeamName) {
        this.workTeamName = workTeamName;
    }

    public String getWorkTeamSName() {
        return workTeamSName;
    }

    public void setWorkTeamSName(String workTeamSName) {
        this.workTeamSName = workTeamSName;
    }

    public Date getShiftDate() {
        return shiftDate;
    }

    public void setShiftDate(Date shiftDate) {
        this.shiftDate = shiftDate;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getWorkTeamSortNum() {
        return workTeamSortNum;
    }

    public void setWorkTeamSortNum(Long workTeamSortNum) {
        this.workTeamSortNum = workTeamSortNum;
    }

    public Long getShiftSortNum() {
        return shiftSortNum;
    }

    public void setShiftSortNum(Long shiftSortNum) {
        this.shiftSortNum = shiftSortNum;
    }

    public Long getShiftAreaId() {
        return shiftAreaId;
    }

    public void setShiftAreaId(Long shiftAreaId) {
        this.shiftAreaId = shiftAreaId;
    }

    public String getShiftAreaName() {
        return shiftAreaName;
    }

    public void setShiftAreaName(String shiftAreaName) {
        this.shiftAreaName = shiftAreaName;
    }

    public String getWorkTeamCode() {
        return workTeamCode;
    }

    public void setWorkTeamCode(String workTeamCode) {
        this.workTeamCode = workTeamCode;
    }

    public List<String> getWorkTeamCodeList() {
        return workTeamCodeList;
    }

    public void setWorkTeamCodeList(List<String> workTeamCodeList) {
        this.workTeamCodeList = workTeamCodeList;
    }
}
