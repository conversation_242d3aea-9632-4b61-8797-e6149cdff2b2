package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.dao.imp.UnMatchAlarmPointEntityVO;

import java.util.List;

public interface UnMatchAlarmPointRepositoryCustom {

    /**
     * 根据位号生产单元删除信息
     *
     * @param tag        位号
     * @param prdtCellId 生产单元
     */
    Integer deleteUmMatchByTagPrd(String tag, Long prdtCellId);

    /**
     * 查询未维护报警点
     *
     * @param tag        位号
     * @param prdtCellId 生产单元
     * @param dcsCode    dcs编号
     * @param unitIds 装置编码
     */
    PaginationBean<UnMatchAlarmPointEntityVO> getUnMatchAlarmPoint(String tag, Long prdtCellId, Long dcsCode, List<String> unitIds, Pagination page);
}
