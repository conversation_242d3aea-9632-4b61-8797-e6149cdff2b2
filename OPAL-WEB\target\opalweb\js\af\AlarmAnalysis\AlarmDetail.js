var getAlarmDetailUrl = OPAL.API.afUrl + '/alarmAnalysis/getAlarmDetail';
var getChangeAlarmDetailUrl = OPAL.API.afUrl + '/alarmAnalysis/getChangeAlarmDetail';
var getCausalAlarmAnalysisTableUrl = OPAL.API.commUrl + '/getCausalAlarmAnalysisTable';
var queryTimeArray; // 固定时间点 10：00
var dateJason;
var unitId; //  装置id
var alarmFlagId;
var alarmPointId;
var alarmFlagName; // 报警标识
var alarmValue; // 输入的修改值
var limitValueLength; // 限值的小数个数
var maxValue;
var limitValue;
var unitFlag; // 单位
var seriesOld; //原报警数
var upLimitValue, downLimitValue; //工艺卡片的范围
var leftValue, rightValue;
var startTime, endTime; // 开始时间 结束时间
var seriesNow = new Array();
$(function() {
	var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
	var page = {
		/**
		 * 初始化
		 */
		init: function() {
			this.bindUi();
			page.logic.getQueryTime()
		},
		bindUi: function() {
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                AlarmFlagCharts.resize();
            };
			// 关闭
			$('#closePageChild').click(function() {
				page.logic.closeLayer(false);
			});
			$('#sureBtn').click(function() {
				if ($('#alarmValue').val() == alarmValue) {
					layer.confirm('修改值须做修改！', {
						btn: ['确定']
					})
					return false;
				}
				page.logic.refreshDetail();
			})
		},
		data: {
			// 设置查询参数
			param: {}
		},
		logic: {
			/**
			 * 获得固定的时间点
			 */
			getQueryTime: function(dateTimeList, unitIdList, id) {
				OPAL.util.getQueryTime(function(queryTime) {
					queryTimeArray = queryTime;
				});
			},
			setData: function(data) {
				dateJason = data.dateJason;
                var dateJasonR =  $.parseJSON(dateJason);
                dateJasonR.pop();
                dateJason =  JSON.stringify(dateJasonR);
                alarmFlagId = data.alarmFlagId;
				alarmPointId = data.alarmPointId;
				unitId = data.unitId;
				startTime = OPAL.util.dateFormat(new Date($.parseJSON(dateJason)[0].value), 'yyyy-MM-dd HH:mm:ss');
				endTime = OPAL.util.dateFormat(new Date($.parseJSON(dateJason)[1].value), 'yyyy-MM-dd HH:mm:ss');
				page.logic.initTalbe();
				$.ajax({
					url: getAlarmDetailUrl,
					async: true,
					data: {
						unitId: unitId,
						dateJason: dateJason,
						alarmPointId: alarmPointId,
						alarmFlagId: alarmFlagId
					},
					dataType: "JSON",
					type: 'GET',
					success: function(result) {
						var res = $.ET.toObjectArr(result);
						if (res != null && res != undefined && res != '') {
							var alarmDuration = res[0].alarmDuration; // 报警总时长
							alarmFlagName = res[0].alarmFlagName; //报警标识名称
							var alarmTimes = res[0].alarmTimes; //报警次数
							alarmValue = res[0].alarmValue; // 修改值
							var avgConfirmTime = res[0].avgConfirmTime; //平均确认时间
							var confirmTimes = res[0].confirmTimes; // 确认次数
							var location = res[0].location; // 位置
							maxValue = res[0].maxValue;
							minValue = res[0].minValue;
							var nowAlarmTimes = res[0].nowAlarmTimes;
							var prdtCellSname = res[0].prdtCellSname; // 生产单元
							var reduceRate = res[0].reduceRate; //减少比例
							var tag = res[0].tag; // 位号
							downLimitValue = res[0].downLimitValue;
							upLimitValue = res[0].upLimitValue;
							unitFlag = res[0].unitFlag;
							limitValue = res[0].limitValue;
							limitValueLength = page.logic.limitValueLength(limitValue);
							var upAnddownLimitValue = page.logic.upAnddownLimitValue(downLimitValue, upLimitValue);
							page.logic.upValueRange(res);
							$('#tag').html(tag);
							$('#alarmFlagName').html(alarmFlagName);
							$('#prdtCellSname').html(prdtCellSname);
							$('#location').html(location);

							$('#alarmDuration').html(alarmDuration);
							$('#alarmTimes').html(alarmTimes);
							$('#confirmTimes').html(confirmTimes);
							$('#avgConfirmTime').html(avgConfirmTime);
							$('#upAnddownLimitValue').html(upAnddownLimitValue);
							$('#upLimitValue').val(upLimitValue);
							$('#downLimitValue').val(downLimitValue);
							var histogramData = res[0].histogramData;
							var lineChartData = res[0].lineChartData;
							if (alarmFlagName == 'PVHH' || alarmFlagName == 'PVHI' || alarmFlagName == 'PVLO' || alarmFlagName == 'PVLL') {
								$('#alarmValueBox').css('display', 'inline-block');
								$('#nowAlarmTimesBox');
								$('#reduceRateBox').css('display', 'inline-block');
								$('#upAnddownLimitValueBox');
								$('#alarmValue').val(alarmValue);
								$('#nowAlarmTimes').html(nowAlarmTimes);
                                $('#reduceRate').html(reduceRate + '%');
                                $('#leftUpValue').html(leftValue);
                                $('#rightUpValue').html(rightValue);

								page.logic.initCharts(histogramData, lineChartData);
							} else {
								$('#alarmValueBox').css('display', 'none');
								$('#nowAlarmTimesBox').css('display', 'none');
								$('#reduceRateBox').css('display', 'none');
								page.logic.initChartsOther(histogramData, lineChartData)
							}
						}
					},
					error: function(result) {
						var errorResult = $.parseJSON(result.responseText);
						layer.msg(errorResult.collection.error.message);
					}
				});
			},
			/**
			 * 初始化图表 (报警标识为PVHH、PVHI、PVLO、PVLL)
			 */
			initCharts: function(data1, data2) {
				var histogramData = JSON.parse(data1);
				var lineChartData = JSON.parse(data2);
				var seriesOld = new Array();
				// var seriesNow = new Array();
				var seriesAlarmValue = new Array();
				$.each(histogramData, function(i, el) {
					seriesOld[i] = [histogramData[i].key, histogramData[i].value.key];
					seriesNow[i] = [histogramData[i].key, histogramData[i].value.value];
				})
                $.each(lineChartData, function(i, el) {
                    seriesAlarmValue[i] = [lineChartData[i].key, lineChartData[i].value];
                })
				AlarmFlagCharts = echarts.init(document.getElementById('AlarmFlagCharts'));
				option = {
					tooltip: {
						trigger: 'item', //axis
						axisPointer: { // 坐标轴指示器，坐标轴触发有效
							type: '' // 默认为直线，可选为：'line' | 'shadow'
						}
					},
					grid: {
						left: '1%',
						right: '2%',
						bottom: '1%',
						top: '12%',
						height: '220px',
						containLabel: true
					},
					legend: {
						left: '80px',
						top: '7%',
						itemWidth: 20, //设置icon大小
						itemHeight: 10, //设置icon大小
						data: [{
							name: '报警数',
							icon: 'roundRect',
							textStyle: {
								color: '#9d9d9d',
							}
						}, {
							name: '报警值',
							icon: '',
							textStyle: {
								color: '#9d9d9d',
							}
						}, {
							name: '修改值',
							icon: 'line',
							textStyle: {
								color: '#9d9d9d',
							}
						}, {
							name: '报警值（限值）',
							icon: 'line',
							textStyle: {
								color: '#9d9d9d',
							}
						}]
					},
					xAxis: [{
						type: 'time',
						minInterval: 3600 * 24 * 1000,
						maxInterval: 3600 * 24 * 1000,
						min: function(value) {
							return Date.parse(startTime.replace(/-/g, "/")) - 3600 * 15 * 1000;
						},
						max: function(value) {
							return Date.parse(endTime.replace(/-/g, "/")) + 3600 * 5 * 1000 ;
						},
						axisLine: {
							onZero: false
						},
						axisLabel: {
							showMinLabel: false,
							showMaxLabel: false,
							formatter: function(value, index) {
								var date = new Date(value);
								var y = date.getFullYear();
								var m = date.getMonth() + 1;
								var d = date.getDate();
								return y + '-' + m + '-' + d;
							}
						},
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						},
					}],
					yAxis: [{
						name: '报警数',
						type: 'value',
						min: 0,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}, {
						name: '报警值(' + unitFlag + ')',
						nameLocation: 'end',
						position: 'right',
						// max: maxValue,
						// min: 0,
						//minInterval: 1,
                        //interval:1,
                        splitNumber:3,
                        axisLabel: {
                            formatter: function(value, index) {
								return value;
							}
						},
						type: 'value',
						splitLine: { //网格线
							show: false,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					series: [{
						name: '原报警数',
						type: 'bar',
						barWidth: '15',
						silent: false, // 图形是否不响应和触发鼠标事件
						itemStyle: {
							normal: {
								color: '#e3e8ed'
							},
							emphasis: {
								color: 'rgba(0, 0, 0, 0.1)'
							}
						},
						barGap: '-100%',
						data: [],
						tooltip: {
							formatter: function(params) {
								var seriesOldValue = seriesOld[(params.dataIndex)][1];
								var seriesNowValue = seriesNow[(params.dataIndex)][1];
								var name = seriesOld[(params.dataIndex)][0].replace(/\//g, "-");
								var d1 = Date.parse(new Date(seriesOld[(params.dataIndex)][0]));
								var d2 = d1 + (60 * 60 * 24 * 1000);
								var Y = (new Date(d2)).getFullYear();
								var M = (new Date(d2)).getMonth() + 1;
								var D = (new Date(d2)).getDate();
								if (Math.floor(M / 10) == 0) {
									M = '0' + M
								}
								if (Math.floor(D / 10) == 0) {
									D = '0' + D
								}
								var d3 = Y + "-" + M + "-" + D;
								var timeData = '从：' + name + ' ' + queryTimeArray + '<br>至：' + d3 + ' ' + queryTimeArray + '<br>原报警数：' + seriesOldValue + '&nbsp;&nbsp;&nbsp;&nbsp;现报警数：' + seriesNowValue
								return timeData;
							}
						}
					}, {
						name: '报警数',
						type: 'bar',
						barWidth: '15',
						itemStyle: {
							normal: {
								color: '#3398DB',
							}
						},
						data: [],
						tooltip: {
							formatter: function(params) {
								var seriesOldValue = seriesOld[(params.dataIndex)][1];
								var name = seriesOld[(params.dataIndex)][0].replace(/\//g, "-");
								var d1 = Date.parse(new Date(seriesOld[(params.dataIndex)][0]));
								var d2 = d1 + (60 * 60 * 24 * 1000);
								var d3 = moment(d2).format("YYYY-MM-DD");
								var timeData = '从：' + name + ' ' + queryTimeArray + '<br>至：' + d3 + ' ' + queryTimeArray + '<br>原报警数：' + seriesOldValue + '&nbsp;&nbsp;&nbsp;&nbsp;现报警数：' + params.value[1]
								return timeData;
							}
						}
					}, {
						name: '报警值',
						type: 'line',
						yAxisIndex: 1,
						smooth: true,
						itemStyle: {
							normal: {
								color: '#1cb964',
							}
						},
						data: seriesAlarmValue,
						tooltip: {
							formatter: function(params) {
								var timeData = '报警时间：' + params.value[0] + ' <br> 报警值：' + params.value[1]
								return timeData;
							}
						}
					}, {
						name: '报警值（限值）',
						type: 'line',
						yAxisIndex: 0,
						itemStyle: {
							normal: {
								color: '#ffc200'
							}
						},
						markLine: {
							lineStyle: {
								normal: {
									type: 'solid',
									color: '#ffc200'
								}
							},
							silent: true,
							label: {
								normal: {
									show: false
								}
							},
							data: [{
								name: '报警值（限值）',
								yAxis: 0
							}]
						}
					}, {
						name: '修改值',
						yAxisIndex: 1,
						type: 'line',
						itemStyle: {
							normal: {
								color: '#ff6b5c'
							}
						},
						markLine: {
							silent: true,
							label: {
								normal: {
									show: true,
									position: 'middle',
								}
							},
							lineStyle: {
								normal: {
									type: 'solid',
									color: '#ff6b5c'
								}
							},
							data: [{
								name: '修改值',
								yAxis: alarmValue
							}]
						}
					}]
				};
				if (alarmFlagName == 'PVHH' || alarmFlagName == 'PVHI') {
					option.yAxis[0].inverse = false;
					option.yAxis[1].inverse = false;
					option.yAxis[1].nameLocation = 'end';
					option.yAxis[1].min = (limitValue == ''&&limitValue!==0) ? minValue : limitValue;
                    option.yAxis[1].max = maxValue;
                    option.yAxis[1].interval= Math.floor((Math.abs(option.yAxis[1].max -option.yAxis[1].min)/3) * 100) / 100;
                    option.yAxis[1].interval = option.yAxis[1].interval<1?option.yAxis[1].interval = 1:Math.ceil(option.yAxis[1].interval);
					option.xAxis[0].position = 'bottom';
					option.legend.top = '7%';
					option.grid.top = '20%'
				} else if (alarmFlagName == 'PVLO' || alarmFlagName == 'PVLL') {
					option.yAxis[0].inverse = true;
					option.yAxis[1].nameLocation = 'start';
					// option.yAxis[1].max = (limitValue == '') ? ((maxValue == minValue)?(maxValue-0.00001):maxValue) : limitValue;
					option.yAxis[1].max = (limitValue == ''&&limitValue!==0) ? maxValue : limitValue;
					option.yAxis[1].min = minValue;
                    option.yAxis[1].interval= Math.floor((Math.abs(option.yAxis[1].max -option.yAxis[1].min)/3) * 100) / 100;
					option.yAxis[1].interval = option.yAxis[1].interval<1?option.yAxis[1].interval = 1:Math.ceil(option.yAxis[1].interval);
                    option.xAxis[0].position = 'top';
					option.legend.top = '7%';
					option.grid.top = '14%';
				}
				if (limitValue == ''&&limitValue !== 0) {
					option.legend.data[3] = {}; 
					option.series[3].markLine.data = []
				}
				option.series[0].data = seriesOld;
				option.series[1].data = seriesNow;
				AlarmFlagCharts.setOption(option);
			},
			/**
			 * 初始化图表 (报警标识除了PVHH、PVHI、PVLO、PVLL之外的其他的情况)
			 */
			initChartsOther: function(data1, data2) {
				var histogramData = JSON.parse(data1);
				var lineChartData = JSON.parse(data2);
				var seriesAlarmValue = new Array();
				$.each(histogramData, function(i, el) {
					seriesNow[i] = [histogramData[i].key, histogramData[i].value.value];
				})
                $.each(lineChartData, function(i, el) {
                    seriesAlarmValue[i] = [lineChartData[i].key, lineChartData[i].value];
                })
				AlarmFlagCharts = echarts.init(document.getElementById('AlarmFlagCharts'));
				option = {
					tooltip: {
						trigger: 'item', //axis
						axisPointer: { // 坐标轴指示器，坐标轴触发有效
							type: '' // 默认为直线，可选为：'line' | 'shadow'
						}
					},
					grid: {
						left: '1%',
						right: '3%',
						bottom: '1%',
						top: '16%',
						height: '230px',
						containLabel: true
					},
					legend: {
						left: '80px',
						top: '4%',
						itemWidth: 20, //设置icon大小
						itemHeight: 10, //设置icon大小
						data: [{
							name: '报警数',
							icon: 'roundRect',
							textStyle: {
								color: '#9d9d9d',
							}
						}, {
							name: '报警值',
							icon: '',
							textStyle: {
								color: '#9d9d9d',
							}
						}]
					},
					xAxis: [{
						type: 'time',
						minInterval: 3600 * 24 * 1000,
						maxInterval: 3600 * 24 * 1000,
						min: function(value) {
							return Date.parse(startTime.replace(/-/g, "/")) - 3600 * 15 * 1000;
						},
						max: function(value) {
							return Date.parse(endTime.replace(/-/g, "/")) + 3600 * 5 * 1000 ;
						},
						axisLine: {
							onZero: false
						},
						axisLabel: {
							showMinLabel: false,
							showMaxLabel: false,
							formatter: function(value, index) {
								var date = new Date(value);
								var y = date.getFullYear();
								var m = date.getMonth() + 1;
								var d = date.getDate();
								return y + '-' + m + '-' + d;
							}
						},
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						},
					}],
					yAxis: [{
						name: '报警数',
						type: 'value',
						min: 0,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}, {
						name: '报警值(' + unitFlag + ')',
						nameLocation: 'end',
						position: 'right',
						type: 'value',
						splitLine: { //网格线
							show: false,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					series: [{
						name: '报警数',
						type: 'bar',
						barWidth: '15',
						itemStyle: {
							normal: {
								color: '#3398DB',
							}
						},
						data: [],
						tooltip: {
							formatter: function(params) {
								var name = params.value[0].replace(/\//g, "-");
								var d1 = Date.parse(new Date(params.value[0]));
								var d2 = d1 + (60 * 60 * 24 * 1000);
								var Y = (new Date(d2)).getFullYear();
								var M = (new Date(d2)).getMonth() + 1;
								var D = (new Date(d2)).getDate();
								if (Math.floor(M / 10) == 0) {
									M = '0' + M
								}
								if (Math.floor(D / 10) == 0) {
									D = '0' + D
								}
								var d3 = Y + "-" + M + "-" + D;
								var timeData = '从：' + name + ' ' + queryTimeArray + '<br>至：' + d3 + ' ' + queryTimeArray + '<br>报警数：' + params.value[1]
								return timeData;
							}
						}
					}, {
						name: '报警值',
						type: 'line',
						yAxisIndex: 1,
						smooth: true,
						itemStyle: {
							normal: {
								color: '#1cb964',
							}
						},
						data: seriesAlarmValue,
						tooltip: {
							formatter: function(params) {
								var timeData = '报警时间：' + params.value[0] + ' <br> 报警值：' + params.value[1]
								return timeData;
							}
						}
					}]
				};
				option.series[0].data = seriesNow;
				if (seriesAlarmValue.length == 0) {
					option.legend.data[1] = []; 
					option.yAxis = [{
						name: '报警数',
						type: 'value',
						min: 0,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}];
					option.series[1].yAxisIndex = 0;
					option.xAxis[0].min = function(value) {
						return Date.parse(startTime.replace(/-/g, "/")) - 3600 * 15 * 1000;
					}
					option.xAxis[0].max = function(value) {
						return value.max + 3600 * 5 * 1000 ;
					}
				}else {
					option.yAxis[1].min = minValue;
					option.yAxis[1].max = maxValue;
					option.yAxis[1].name = '报警值(' + unitFlag + ')';
				}
				AlarmFlagCharts.setOption(option);
			},
			/**
			 * 刷新详情列表
			 */
			refreshDetail: function() {
				var flag1 = page.logic.checkRules(limitValue, $('#alarmValue').val());
				if (alarmFlagName == 'PVHH' || alarmFlagName == 'PVHI') { // 高
					var flag2 = page.logic.checklimtArea('H', limitValue, maxValue, $('#alarmValue').val());
				} else if (alarmFlagName == 'PVLO' || alarmFlagName == 'PVLL') { // 低
					var flag2 = page.logic.checklimtArea('L', limitValue, minValue, $('#alarmValue').val());
				}
				var flag3 = page.logic.checkAreas($('#alarmValue').val());
				if (!flag1) {
					layer.confirm('修改值的小数位数不可超过5位！', {
						btn: ['确定']
					})
					return false;
				} else if (flag2 != true) {
					layer.confirm(flag2, {
						btn: ['确定']
					})
					return false;
				} else if (!flag3) {
					layer.confirm('该修改值不在工艺卡片值范围，是否继续？', {
						btn: ['是', '否']
					}, function(index) {
						layer.close(index)
						page.logic.changeAlarmDetail()
					}, function(index) {
						layer.close(index)
					});
				} else {
					page.logic.changeAlarmDetail()
				}
			},
			changeAlarmDetail: function() {
				$.ajax({
					url: getChangeAlarmDetailUrl,
					async: true,
					data: {
						unitId: unitId,
						dateJason: dateJason,
						alarmPointId: alarmPointId,
						alarmFlagId: alarmFlagId,
						alarmValue: $('#alarmValue').val()
					},
					dataType: "JSON",
					type: 'GET',
					success: function(result) {
						var res = $.ET.toObjectArr(result);
						if (res != null && res != undefined && res != '') {
							if (res.length > 0) {
								var nowAlarmTimes = res[0].nowAlarmTimes;
								var reduceRate = res[0].reduceRate;
								$('#nowAlarmTimes').html(nowAlarmTimes);
								$('#reduceRate').html(reduceRate + "%");
								alarmValue = $('#alarmValue').val();
								page.logic.setCharts(res[0].histogramData);
							}
						}
					},
					error: function(result) {
						var errorResult = $.parseJSON(result.responseText);
						layer.msg(errorResult.collection.error.message);
					}
				});
			},
			/**
			 * 重新渲染chart
			 */
			setCharts: function(histogramData) {
				var seriesOld = new Array();
				// var seriesNow = new Array();
				var histogramData = JSON.parse(histogramData);
				$.each(histogramData, function(i, el) {
					seriesNow[i] = [histogramData[i].key, histogramData[i].value.value];
				})
				option.series[1].data = seriesNow;
				option.series[4].markLine.data[0].yAxis = alarmValue;
				AlarmFlagCharts.setOption(option)
			},
			/**
			 * 初始化表格
			 */
			initTalbe: function() {
				OPAL.ui.initBootstrapTable("table", {
					url: getCausalAlarmAnalysisTableUrl,
					columns: [{
						title: "序号",
						formatter: function(value, row, index) {
							var data = page.data.param;
							var pageNumber = data.pageNumber;
							var pageSize = data.pageSize;
							return index + 1 + (pageNumber - 1) * pageSize;
						},
						rowspan: 1,
						align: 'center'
					}, {
						field: 'prdtCellName',
						title: '生产单元',
						align: 'left',
					}, {
						field: 'tagName',
						title: '子位号',
						align: 'left',
					}, {
						field: 'alarmFlagName',
						title: '报警标识',
						align: 'center',
					}, {
						field: 'alarmTimes',
						title: '报警数',
						align: 'right',
					}, {
						field: 'forecast',
						title: '可预测性(%)',
						align: 'right',
					}, {
						field: 'important',
						title: '重要的(%)',
						align: 'right',
					}]
				}, page.logic.queryParams)
			},
			/**
			 * 查询参数
			 * @param params
			 * @returns {{pageSize: *, pageNumber: *}}
			 */
			queryParams: function(p) {
				var param = {
					unitId: unitId,
					pageSize: p.pageSize,
					pageNumber: p.pageNumber,
					sortOrder: p.sortOrder,
					startTime: startTime,
					endTime: endTime,
					alarmFlagId: alarmFlagId,
					alarmPointId: alarmPointId,
					now: Math.random()
				};
				page.data.param = {
					pageSize: p.pageSize,
					pageNumber: p.pageNumber,
				};
				return param;
			},
			/**
			 * 填写工艺卡片值
			 */
			upAnddownLimitValue: function(downLimitValue, upLimitValue) {
				var flagUp, flagDown, valueUp, valueDowm;
				var str = '';
				if (upLimitValue != '{}' && downLimitValue != '{}') {
					var downValue = JSON.parse(downLimitValue);
					var upValue = JSON.parse(upLimitValue);
					if (upValue.key == 1) {
						flagUp = '<='
					} else if (upValue.key == 0) {
						flagUp = '<'
					}
					if (downValue.key == 1) {
						flagDown = '>='
					} else if (downValue.key == 0) {
						flagDown = '>'
					}
					str = downValue.value + '~' + upValue.value
				} else if (upLimitValue != '{}' && downLimitValue == '{}') {
					var upValue = JSON.parse(upLimitValue);
					if (upValue.key == 1) {
						flagUp = '<='
					} else if (upValue.key == 0) {
						flagUp = '<'
					}
					str = flagUp + upValue.value
				} else if (upLimitValue == '{}' && downLimitValue != '{}') {
					var downValue = JSON.parse(downLimitValue);
					if (downValue.key == 1) {
						flagDown = '>='
					} else if (downValue.key == 0) {
						flagDown = '>'
					}
					str = flagDown + downValue.value
				} else if (upLimitValue == '{}' && downLimitValue == '{}') {
					str = ''
				}
				return str;
			},
            upValueRange:function(res) {
                if (res[0].alarmFlag == AlarmFlagEnum.PVHH || res[0].alarmFlag == AlarmFlagEnum.PVHI) {
                	if(res[0].limitValue==undefined||res[0].limitValue==null||(res[0].limitValue==''&&res[0].limitValue!==0)){
                        leftValue = res[0].minValue;
					}else {
                        leftValue = res[0].limitValue;
                    }
                    rightValue = res[0].maxValue;
                }
                else if (res[0].alarmFlag == AlarmFlagEnum.PVLO || res[0].alarmFlag == AlarmFlagEnum.PVLL) {
                    leftValue = res[0].minValue;
                    if(res[0].limitValue==undefined||res[0].limitValue==null||(res[0].limitValue==''&&res[0].limitValue!==0)){
                        rightValue = res[0].maxValue;
                    }else {
                        rightValue = res[0].limitValue;
                    }

                }
            },
			/**
			 * 校验修改值的数值规则
			 */
			checkRules: function(limtStr, strVal) {
				r = '^-?\\d+(\\.\\d{0,5})?$'
				return (new RegExp(r)).test(strVal);
			},
			/**
			 * 校验报警值的数值是否在限值和报警值范围内
			 */
			checklimtArea: function(flag, limitValue, alarmValue, strVal) {
				if (flag == 'H') {
					if(limitValue == ''&&limitValue !== 0){
						limitValue = minValue;
						if (strVal >= limitValue && strVal <= alarmValue) {
							return true;
						} else {
							return '修改值须大于等于该位号的最小报警值，小于等于最大报警值！报警值范围为（' + limitValue + '（最小报警值）～' + alarmValue + '（最大报警值））'
						}
					}else {
						if (strVal >= limitValue && strVal <= alarmValue) {
							return true;
						} else {
							return '修改值须大于等于该位号的限值，小于等于最大报警值！报警值范围为（' + limitValue + '（限值）～' + alarmValue + '（最大报警值））'
						}
					}
					
				} else if (flag == 'L') {
					if(limitValue == ''&&limitValue !== 0){
						limitValue = maxValue;
						if (strVal <= limitValue && strVal >= alarmValue) {
							return true;
						} else {
							return '修改值须大于等于该位号的最小报警值，小于等于最大报警值！报警值范围为（' + alarmValue + '（最小报警值）～' + limitValue + '（最大报警值））'
						}	
					}else {
						if (strVal <= limitValue && strVal >= alarmValue) {
							return true;
						} else {
							return '修改值须大于等于该位号的最小报警值，小于等于最大报警值！报警值范围为（' + alarmValue + '（最小报警值）～' + limitValue + '（限值））'
						}
					}
				}
			},
			/**
			 * 校验修改值的数值是否在工艺范围
			 */
			checkAreas: function(strVal) {
				var upValue = $('#upLimitValue').val();
				var downValue = $('#downLimitValue').val();
				var limitFlag, limitUp, limitDown;
				if (upValue != '{}' && downValue != '{}') {
					var upVal = JSON.parse(upValue);
					var downVal = JSON.parse(downValue);
					if (upVal.key == 1) {
						flagUp = '<=';
						limitUp = strVal <= upVal.value
					} else if (upVal.key == 0) {
						flagUp = '<';
						limitUp = strVal < upVal.value
					}
					if (downVal.key == 1) {
						flagDown = '>=';
						limitDown = strVal >= downVal.value
					} else if (downVal.key == 0) {
						flagDown = '>';
						limitDown = strVal > downVal.value
					}
					if (limitUp && limitDown) {
						limitFlag = true
					} else {
						limitFlag = false
					}
				} else if (upValue != '{}' && downValue == '{}') {
					var upVal = JSON.parse(upValue);
					if (upVal.key == 1) {
						flagUp = '<='
						limitFlag = strVal <= upVal.value
					} else if (upVal.key == 0) {
						flagUp = '<'
						limitFlag = strVal < upVal.value
					}
				} else if (upValue == '{}' && downValue != '{}') {
					var downVal = JSON.parse(downValue);
					if (downVal.key == 1) {
						flagDown = '>=';
						limitFlag = strVal >= downVal.value
					} else if (downVal.key == 0) {
						flagDown = '>';
						limitFlag = strVal > downVal.value
					}
				} else if (upValue == '{}' && downValue == '{}') {
					return true;
				}
				return limitFlag;
			},
			limitValueLength: function(limtStr) {
				if (limtStr != '') {
					if (String(limtStr).indexOf(".") == -1) { // 整数
						var n = 0;
					} else {
						var n = String(limtStr).split('.')[1].length
					}
				} else {
					n = 2
				}

				return n;
			},
			/**
			 * 关闭弹出层
			 */
			closeLayer: function(isRefresh) {
				parent.layer.close(index);
			},
		}
	}
	page.init();
	window.page = page;


})