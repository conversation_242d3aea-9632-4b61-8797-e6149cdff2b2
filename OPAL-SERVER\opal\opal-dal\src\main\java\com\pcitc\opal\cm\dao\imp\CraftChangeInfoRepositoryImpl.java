package com.pcitc.opal.cm.dao.imp;

import com.pcitc.opal.cm.dao.CraftChangeInfoRepositoryCustom;
import com.pcitc.opal.cm.pojo.CraftChangeInfo;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.dao.BaseRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/*
 * CraftChangeInfo实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_CraftChangeInfoRepositoryImpl
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：CraftChangeInfo实体的Repository实现
 */
public class CraftChangeInfoRepositoryImpl extends BaseRepository<CraftChangeInfo, Long> implements CraftChangeInfoRepositoryCustom {

    /**
     * 新增工艺变更单信息
     *
     * @param craftChangeInfo 工艺变更单信息
     * @return 返回结果信息类
     * <AUTHOR> 2018-01-22
     */
    @Override
    @Transactional
    public CommonResult addCraftChangeInfo(CraftChangeInfo craftChangeInfo) {
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(craftChangeInfo);
            commonResult.setResult(craftChangeInfo);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    @Override
    public Date getCraftChangeInfoMaxRlsTime(Long comId) {
        try {
            StringBuilder hql = new StringBuilder(
                    "select max(t.rlsTime) from CraftChangeInfo t where t.companyId =");
            hql.append(comId);
            Query query = this.getEntityManager().createQuery(hql.toString());
            String time = String.valueOf(query.getSingleResult());

            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date returnTime = df.parse(time);
            return returnTime;
        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public CraftChangeInfo getCraftChangeInfoInfo(String unitCode, String tag, String alarmFlagName, Date startTime,
                                                  String nowValue, Integer val) {
        try {
            if (StringUtils.isBlank(unitCode) || StringUtils.isBlank(tag)
                    || StringUtils.isBlank(alarmFlagName) || StringUtils.isBlank(nowValue)
                    || startTime == null || val == null) {
                return null;
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startTime);
            calendar.add(Calendar.DATE, val);
            Date startTime1 = calendar.getTime();
            StringBuilder hql = new StringBuilder(
                    "from CraftChangeInfo t where t.unitCode =:unitCode ");
            hql.append("and t.tagCode =:tag ");
            hql.append("and :alarmFlagName like concat('%' ,t.submitUserName,'%') ");
            hql.append("and cast(t.afterValue as double) =cast(:nowValue as double) ");
            hql.append("and t.submitTime <=:startTime ");
            hql.append("and t.rlsTime >=:startTime1 order by t.rls_time desc ");
            TypedQuery<CraftChangeInfo> query = this.getEntityManager().createQuery(hql.toString(), CraftChangeInfo.class);
            query.setParameter("unitCode", unitCode);
            query.setParameter("tag", tag);
            query.setParameter("alarmFlagName", alarmFlagName);
            query.setParameter("nowValue", nowValue);
            query.setParameter("startTime", startTime);
            query.setParameter("startTime1", startTime1);
            query.setMaxResults(1);
            return query.getSingleResult();

        } catch (Exception ex) {
            return null;
        }
    }

}
