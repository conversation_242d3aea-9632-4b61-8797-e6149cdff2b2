<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,Chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="../../../js/common/html5shiv/html5shiv-printshiv.min.js"></script>
    <script src="../../../js/common/respond/respond.min.js"></script>
    <script src="../../../js/common/global/ie-css3.htc"></script>
    <![endif]-->
    <title>报警时长统计</title>
    <meta name="keywords" content="报警时长统计,报警分析">
    <meta name="description" content="报警时长统计分析页面">

    <!-- CSS 样式表 -->
    <link rel="stylesheet" href="../../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../../css/font-awesome.css">
    <link rel="stylesheet" href="../../../js/common/layer/skin/layer.css">
    <link rel="stylesheet" href="../../../js/common/jquery-combotree-1.5.3/themes/bootstrap/easyui.css">
    <link rel="stylesheet" href="../../../js/common/jquery-combotree-1.5.3/themes/icon.css">
    <link rel="stylesheet" href="../../../css/bootstrap-table.css">
    <link rel="stylesheet" href="../../../css/animate.css">
    <link rel="stylesheet" href="../../../css/style.css">
    <link rel="stylesheet" href="../../../css/opal-global.css">

    <style>
        /* 页面特定样式 */
        .tab-content > .tab-pane,
        .pill-content > .pill-pane {
            display: block;
            height: 0;
            overflow-y: hidden;
        }

        .tab-content > .active,
        .pill-content > .active {
            height: auto;
        }

        li.active a span {
            color: #73adef;
        }

        .alarm-public-box {
            float: none;
            margin-top: 10px;
            margin-bottom: 15px;
        }

        .alarm-analysis-div-box {
            height: 440px !important;
        }

        .change-maintenance-number-box {
            font-size: 12px;
        }

        .loading {
            background: rgba(0, 0, 0, 0.25) url(../../../images/loading.gif) no-repeat center 50% / 60px 60px;
            z-index: 9999;
        }

        .form-control {
            display: inline-block !important;
        }

        /* 优化搜索表单布局 */
        .search-form-container {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .search-form-row {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }

        .search-form-item {
            display: flex;
            align-items: center;
            white-space: nowrap;
        }

        .search-form-item label {
            margin-right: 8px;
            margin-bottom: 0;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .search-form-row {
                flex-direction: column;
                align-items: stretch;
            }

            .search-form-item {
                justify-content: space-between;
            }
        }
    </style>
</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <!-- 页面标题和导出按钮 -->
    <div class="ibox-title">
        <h5 class="alarm-public-title-dis">报警时长统计</h5>
        <div class="ibox-tools" style="margin-top: -15px; margin-right: 15px;">
            <button id="AlarmDurationStattExport" type="button" class="btn btn-file btn-tile"
                    style="background: #F59C1A; border: none; border-radius: 4px;">
                <img src="../../../images/doen.png" class="excel-image-icon" alt="导出图标">
                &nbsp;&nbsp;导出
            </button>
        </div>
    </div>

    <!-- 隐藏字段 -->
    <input type="hidden" id="upLimitValue">
    <input type="hidden" id="downLimitValue">

    <!-- 搜索表单区域 -->
    <div class="ibox-content col-md-12">
        <div class="search-form-container">
            <form id="searchForm" onsubmit="return false;">
                <div class="search-form-row">
                    <div class="search-form-item">
                        <input type="checkbox" id="checkShop" name="checkShop"
                               class="checkbox-box-style" checked>
                        <label for="checkShop" style="margin-left: 8px;">按车间显示</label>
                    </div>

                    <div class="search-form-item">
                        <label for="unitIds">装置：</label>
                        <input name="unitIds" id="unitIds" class="easyui-combotree"
                               style="width: 180px; height: 32px; color: #999;">
                    </div>

                    <div class="search-form-item">
                        <label for="priority">优先级：</label>
                        <input name="priority" id="priority" class="easyui-combotree"
                               style="width: 150px;">
                    </div>
                </div>

                <div class="search-form-row">
                    <div class="search-form-item">
                        <label for="startTime">时间：</label>
                        <input name="startTime" id="startTime" type="text"
                               class="laydate-icon input-text-box lay-time-height"
                               placeholder="开始时间">
                        <span class="alarm-time-to-style" style="margin: 0 8px;">至</span>
                        <input name="endTime" id="endTime" type="text"
                               class="laydate-icon input-text-box lay-time-height"
                               placeholder="结束时间">
                    </div>

                    <div class="search-form-item">
                        <label for="alarmFlagId">报警标识：</label>
                        <input name="alarmFlagId" id="alarmFlagId" class="easyui-combotree"
                               style="width: 180px; height: 30px;">
                    </div>

                    <div class="search-form-item">
                        <button id="btnSearch" type="button"
                                class="btn btn-success alarm-assess-input-style first-page-search">
                            <i class="fa fa-search"></i> 查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 车间显示模式 -->
    <div class="contentcj" style="display: none;">
        <div class="col-md-12 alarm-public-style-bg">
            <div class="tab-content">
                <!-- 车间图表 -->
                <div class="tab-pane fade in active">
                    <div id="floodAlarmChartcj" class="flood-alarm-chart"
                         style="height: 400px; min-height: 300px;"></div>
                    <h3 style="text-align: center; margin: 10px 0; color: #333;">车间</h3>
                </div>

                <!-- 装置图表 -->
                <div class="tab-pane fade in active">
                    <div id="floodAlarmChartzz" class="flood-alarm-chart"
                         style="height: 400px; min-height: 300px;"></div>
                    <h3 style="text-align: center; margin: 10px 0; color: #333;">装置</h3>
                </div>

                <!-- 生产单元图表 -->
                <div class="tab-pane fade in active">
                    <div id="floodAlarmChartdy" class="flood-alarm-chart"
                         style="height: 400px; min-height: 300px;"></div>
                    <h3 style="text-align: center; margin: 10px 0; color: #333;">生产单元</h3>
                </div>
            </div>
        </div>

        <!-- 车间模式数据表格 -->
        <div class="col-md-12 alarm-dis-bg alarm-dis-top" style="height: 410px;">
            <div class="col-md-12 Retursd_list" style="margin-top: 20px;">
                <div class="tab-content">
                    <div class="tab-pane fade active in">
                        <table id="MostAlarmOperateTablecj"
                               class="table table-striped alarm-public-table-style"
                               data-resizable="true">
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 默认显示模式 -->
    <div class="contentmr" style="display: none;">
        <div class="col-md-12 alarm-public-style-bg" style="height: 410px;">
            <p class="alarm-public-title-style"></p>

            <!-- 切换标签 -->
            <ul class="myTab nav nav-tabs alarm-tab-content tab-ul-box" style="margin-top: -50px;">
                <li class="active" showFlag="imgShow">
                    <a href="#home" data-toggle="tab" role="tab" aria-controls="home" aria-selected="true">
                        <img src="../../../images/one1.png" class="alarm-graphic-span-box" alt="图形显示">
                        <span class="alarm-number-span-dis">图形显示</span>
                    </a>
                </li>
                <li showFlag="tableShow">
                    <a href="#ios" data-toggle="tab" role="tab" aria-controls="ios" aria-selected="false">
                        <img src="../../../images/tweo.png" class="alarm-graphic-span-box" alt="列表显示">
                        <span class="alarm-number-span-dis">列表显示</span>
                    </a>
                </li>
            </ul>

            <!-- 标签内容 -->
            <div class="tab-content">
                <div class="tab-pane fade in active" id="home" role="tabpanel" aria-labelledby="home-tab">
                    <div id="floodAlarmChart" class="flood-alarm-chart"
                         style="height: 350px; min-height: 300px;"></div>
                </div>
                <div class="tab-pane fade" id="ios" role="tabpanel" aria-labelledby="ios-tab"
                     style="height: 350px;">
                    <table id="floodTable"
                           data-reorderable-columns="true"
                           class="table table-striped table-hover alarm-public-table-style"
                           data-resizable="true">
                    </table>
                </div>
            </div>
        </div>

        <!-- 默认模式数据表格 -->
        <div class="col-md-12 alarm-dis-bg alarm-dis-top" style="height: 410px;">
            <div class="col-md-12 Retursd_list" style="margin-top: 20px;">
                <div class="tab-content">
                    <div class="tab-pane fade active in">
                        <table id="MostAlarmOperateTable"
                               class="table table-striped alarm-public-table-style"
                               data-resizable="true">
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导出表单 -->
    <form id="form" method="post" style="display: none;" aria-hidden="true">
        <input type="hidden" name="titles" id="titlesExport">
        <input type="hidden" name="pageSize" id="pageSizeExport">
        <input type="hidden" name="pageNumber" id="pageNumberExport">
        <input type="hidden" name="unitIds" id="unitIdsExport">
        <input type="hidden" name="startTime" id="startTimeForm">
        <input type="hidden" name="endTime" id="endTimeForm">
        <input type="hidden" name="priority" id="priorityFrom">
        <input type="hidden" name="unitCodes" id="unitCodesFrom">
        <input type="hidden" name="workshopCodes" id="workshopCodesFrom">
        <input type="hidden" name="prdtCellId" id="prdtCellIdFrom">
        <input type="hidden" name="alarmFlagId" id="alarmFlagIdForm">
    </form>
</div>

<!-- 加载遮罩层 -->
<div class="modal fade loading" id="myModal" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel" aria-hidden="true"
     data-backdrop="static" data-keyboard="false">
</div>

<!-- JavaScript 文件 -->
<!-- 核心库 -->
<script src="../../../js/common/jquery.min.js"></script>
<script src="../../../js/common/Jquery.ETTool-1.0.js"></script>
<script src="../../../js/common/bootstrap.min.js"></script>

<!-- 工具库 -->
<script src="../../../js/common/CommonEnum.js"></script>
<script src="../../../js/common/opal-util.js"></script>
<script src="../../../js/common/opal-env.js"></script>
<script src="../../../js/common/moment/moment.min.js"></script>

<!-- 图表和UI组件 -->
<script src="../../../js/common/echarts.min.js"></script>
<script src="../../../js/common/laydate/laydate.js"></script>
<script src="../../../js/common/layer/layer.min.js"></script>

<!-- 表格组件 -->
<script src="../../../js/common/bootstrap-table.js"></script>
<script src="../../../js/common/bootstrap-table-zh-CN.js"></script>
<script src="../../../js/common/bootstrap-table-resizable.js"></script>
<script src="../../../js/common/colResizable-1.6.min.js"></script>

<!-- 树形插件 -->
<script src="../../../js/common/jquery-combotree-1.5.3/jquery.combotree.min.js"></script>

<!-- 页面特定脚本 -->
<script src="../../../js/af/AlarmDurationStatt/Index.js"></script>
</body>

</html>