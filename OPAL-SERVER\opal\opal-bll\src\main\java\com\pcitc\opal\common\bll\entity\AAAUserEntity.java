package com.pcitc.opal.common.bll.entity;

import java.io.Serializable;
import java.util.Calendar;

/*
 * AAAUser实体,对新旧AAA User实体进行封装兼容
 * 模块编号： pcitc_opal_bll_class_AAAUserEntity
 * 作       者：xuelei.wang
 * 创建时间：2018/3/2
 * 修改编号：1
 * 描       述：AAAUser实体,对新旧AAA User实体进行封装兼容
 */
public class AAAUserEntity implements Serializable {
    /**
     * 登录名
     */
    private String userCode;
    /**
     * 生日
     */
    private Calendar birthday;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 是否启用
     */
    private Boolean enabled;
    /**
     * 手机
     */
    private String mobile;
    /**
     * 名称
     */
    private String name;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 电话
     */
    private String tel;

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public Calendar getBirthday() {
        return birthday;
    }

    public void setBirthday(Calendar birthday) {
        this.birthday = birthday;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }
}
