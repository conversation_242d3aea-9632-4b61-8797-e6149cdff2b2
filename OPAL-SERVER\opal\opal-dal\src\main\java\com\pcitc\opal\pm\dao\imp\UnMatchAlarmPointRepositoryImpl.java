package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.UnMatchAlarmPointRepositoryCustom;
import com.pcitc.opal.pm.pojo.UnMatchAlarmPoint;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @USER: chenbo
 * @DATE: 2023/4/24
 * @TIME: 9:17
 * @DESC:
 **/
public class UnMatchAlarmPointRepositoryImpl extends BaseRepository<UnMatchAlarmPoint, Long> implements UnMatchAlarmPointRepositoryCustom {

    @Transactional
    @Override
    public Integer deleteUmMatchByTagPrd(String tag, Long prdtCellId) {

        String hql = "delete from UnMatchAlarmPoint where tag = :tag and prdtCellId = :prdtCellId and companyId = :companyId";

        Query query = getEntityManager().createQuery(hql);
        query.setParameter("tag", tag);
        query.setParameter("prdtCellId", prdtCellId);
        query.setParameter("companyId", Long.valueOf(new CommonProperty().getCompanyId()));

        return query.executeUpdate();
    }

    @Override
    public PaginationBean<UnMatchAlarmPointEntityVO> getUnMatchAlarmPoint(String tag, Long prdtCellId, Long dcsCode, List<String> unitIds, Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("select new com.pcitc.opal.pm.dao.imp.UnMatchAlarmPointEntityVO(" +
                    "t.unMatchAlarmPointId ,t.companyId,t.dcsCode,t.prdtCellId,t.tag,t.writeDate,t.opcCodeId,d.name,p.sname,p.unitId, un.sname) " +
                    "from UnMatchAlarmPoint t " +
                    "left join t.prdtCell p " +
                    "left join DcsCode d on t.dcsCode =d.dcsCodeId " +
                    "inner join Unit un on p.unitId = un.stdCode " +
                    " where 1=1 ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();

            if (!StringUtils.isEmpty(tag)) {
                hql.append(" and (t.tag like :tag escape '/') ");
                paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
            }
            if (null != prdtCellId){
                hql.append(" and t.prdtCellId = :prdtCellId ");
                paramList.put("prdtCellId",prdtCellId);
            }
            if (null != dcsCode&& dcsCode != -1){
                hql.append(" and t.dcsCode = :dcsCode ");
                paramList.put("dcsCode",dcsCode);
            }
            if (CollectionUtils.isNotEmpty(unitIds)) {
                hql.append(" and un.stdCode in (:unitId) ");
                paramList.put("unitId", unitIds);
            }
            CommonProperty commonProperty = new CommonProperty();
            hql.append(" and t.companyId = :companyId ");
            paramList.put("companyId",Long.valueOf(commonProperty.getCompanyId()));
            hql.append(" order by t.writeDate desc,p.sortNum asc,p.sname asc");

            Long count =Long.valueOf(this.findCusCount(hql.toString(),paramList));
            BaseRepository<UnMatchAlarmPointEntityVO, Long> br =new BaseRepository();
            return br.findCusTomAll(this.getEntityManager(),page,count, hql.toString(), paramList,UnMatchAlarmPointEntityVO.class);
        } catch (Exception ex) {
            throw ex;
        }
    }
}
