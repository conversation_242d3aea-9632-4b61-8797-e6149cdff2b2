package com.pcitc.opal.ac.bll.entity;
import com.pcitc.opal.common.CommonEnum;

import java.util.Date;

/*
 * 报警变更方案附加信息
 * 模块编号：pcitc_opal_bll_class_AlarmChangePlanExtrEntity
 * 作    者：dageng.sun
 * 创建时间：2018-01-30
 * 修改编号：1
 * 描    述：报警变更方案附加信息
 */
public class AlarmChangePlanExtrEntity {

    /**
     * 报警变更方案附加信息ID
     */
    private Long planExtrId;

    /**
     * 报警变更方案ID
     */
    private Long planId;

    /**
     * 业务类型(1下发；2确认)
     */
    private Integer businessType;
    
    /**
     * 业务类名称
     */
    @SuppressWarnings("unused")
	private String businessTypeName;

    /**
     * 操作人ID
     */
    private String opUserId;

    /**
     * 操作人名称
     */
    private String opUserName;

    /**
     * 操作时间
     */
    private Date opTime;

    /**
     * 执行人ID
     */
    private String executeUserId;

    /**
     * 执行人名称
     */
    private String executeUserName;

    /**
     * 备注
     */
    private String remark;

    public Long getPlanExtrId() {
        return planExtrId;
    }

    public void setPlanExtrId(Long planExtrId) {
        this.planExtrId = planExtrId;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getOpUserId() {
        return opUserId;
    }

    public void setOpUserId(String opUserId) {
        this.opUserId = opUserId;
    }

    public String getOpUserName() {
        return opUserName;
    }

    public void setOpUserName(String opUserName) {
        this.opUserName = opUserName;
    }

    public String getExecuteUserId() {
        return executeUserId;
    }

    public void setExecuteUserId(String executeUserId) {
        this.executeUserId = executeUserId;
    }

    public String getExecuteUserName() {
        return executeUserName;
    }

    public void setExecuteUserName(String executeUserName) {
        this.executeUserName = executeUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getOpTime() {
        return opTime;
    }

    public void setOpTime(Date opTime) {
        this.opTime = opTime;
    }

	public String getBusinessTypeName() {
		return CommonEnum.BusinessTypeEnum.getName(businessType);
	}

	public void setBusinessTypeName(String businessTypeName) {
		this.businessTypeName = businessTypeName;
	}

}

