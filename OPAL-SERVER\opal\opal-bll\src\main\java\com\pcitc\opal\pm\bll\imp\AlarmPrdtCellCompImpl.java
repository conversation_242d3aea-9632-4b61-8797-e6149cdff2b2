package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.TagExtraMessageRepository;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.CommonEnum.PageModelEnum;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.AlarmPrdtCellCompService;
import com.pcitc.opal.pm.bll.entity.AlarmPrdtCellCompEntity;
import com.pcitc.opal.pm.bll.entity.PrdtCellEntity;
import com.pcitc.opal.pm.dao.AlarmPrdtCellCompRepository;
import com.pcitc.opal.pm.dao.imp.DataServerMonDTO;
import com.pcitc.opal.pm.dao.imp.DataServerMonVO;
import com.pcitc.opal.pm.pojo.AlarmPrdtCellComp;
import com.pcitc.opal.pm.pojo.PrdtCell;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.List;

/*
 * 生产单元对照配置业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmPrdtCellCompImpl
 * 作	者：jiangtao.xue
 * 创建时间：2018/04/04
 * 修改编号：1
 * 描	述：生产单元对照配置业务逻辑层实现类
 */
@Service
@Component
public class AlarmPrdtCellCompImpl implements AlarmPrdtCellCompService {
	/**
	 * 实例化数据访问层接口
	 */
	@Autowired
	private AlarmPrdtCellCompRepository alarmPrdtCellCompRepository;

	@Autowired
	private AlarmEventRepository alarmEventRepository;

	@Autowired
	private TagExtraMessageRepository temRepository;

	@Autowired
	private BasicDataService basicDataService;

	/**
	 * 新增生产单元对照配置
	 * 
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompEntity 生产单元对照配置实体
	 * @return 生产单元对照配置实体
	 * @throws Exception 
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addAlarmPrdtCellComp(AlarmPrdtCellCompEntity alarmPrdtCellCompEntity) throws Exception {
		// 实体转换为持久层实体
		AlarmPrdtCellComp alarmPrdtCellComp = ObjectConverter.entityConverter(alarmPrdtCellCompEntity, AlarmPrdtCellComp.class);
		// 数据校验
		//alarmPrdtCellCompValidation(alarmPrdtCellComp);
		CommonProperty commonProperty = new CommonProperty();
		alarmPrdtCellComp.setCompanyId(Integer.valueOf(commonProperty.getCompanyId()));
		// 赋值  创建人、创建名称、创建时间
		CommonUtil.returnValue(alarmPrdtCellComp, PageModelEnum.NewAdd.getIndex());
		CommonResult commonResult = alarmPrdtCellCompRepository.addAlarmPrdtCellComp(alarmPrdtCellComp);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false){
			throw new Exception(commonResult.getMessage());
		}
		return commonResult;
	}

	/**
	 * 删除生产单元对照配置维护数据
	 * 
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompIds  生产单元对照配置维护主键Id集合
	 * @return 生产单元对照配置主键id集合
	 * @throws Exception 
	 */
	@Override
	public CommonResult deleteAlarmPrdtCellComp(Long[] alarmPrdtCellCompIds) throws Exception {
		// 判断ID集合是否可用
		if (alarmPrdtCellCompIds == null || alarmPrdtCellCompIds.length <= 0) {
			throw new Exception("没有需要删除的生产单元对照配置数据！");
		}
		List<AlarmPrdtCellComp> anlyAlarmPrdtCellCompList = alarmPrdtCellCompRepository.getAlarmPrdtCellComp(alarmPrdtCellCompIds);
		if (anlyAlarmPrdtCellCompList == null || anlyAlarmPrdtCellCompList.isEmpty())
			return new CommonResult();
		Long[] anlyAlarmPrdtCellCompIdList = anlyAlarmPrdtCellCompList.stream().map(item -> item.getAlarmPrdtCellCompId()).toArray(Long[]::new);
		// 调用DAL删除方法
		CommonResult commonResult = alarmPrdtCellCompRepository.deleteAlarmPrdtCellComp(anlyAlarmPrdtCellCompIdList);

		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
		return commonResult;
	}

	/**
	 * 修改生产单元对照配置
	 * 
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompEntity 生产单元对照配置实体类
	 * @return 生产单元对照配置实体类
	 * @throws Exception 
	 */
	@Override
	public CommonResult updateAlarmPrdtCellComp(AlarmPrdtCellCompEntity alarmPrdtCellCompEntity) throws Exception {
		// 实体转换持久层实体
		AlarmPrdtCellComp alarmPrdtCellComp = ObjectConverter.entityConverter(alarmPrdtCellCompEntity, AlarmPrdtCellComp.class);
		// 校验
		//alarmPrdtCellCompValidation(alarmPrdtCellComp);
		// 实体转换为持久层实体
		alarmPrdtCellComp = alarmPrdtCellCompRepository.getSingleAlarmPrdtCellComp(alarmPrdtCellCompEntity.getAlarmPrdtCellCompId());
		CommonUtil.objectExchange(alarmPrdtCellCompEntity, alarmPrdtCellComp);
		// 赋值 修改人、修改名称、修改时间
		CommonUtil.returnValue(alarmPrdtCellComp, PageModelEnum.Edit.getIndex());
		//企业
		CommonProperty commonProperty = new CommonProperty();
		alarmPrdtCellComp.setCompanyId(Integer.valueOf(commonProperty.getCompanyId()));
		// 调用DAL更新方法
		CommonResult commonResult = alarmPrdtCellCompRepository.updateAlarmPrdtCellComp(alarmPrdtCellComp);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
		return commonResult;
	}

	/**
	 * 通过生产单元对照配置ID获取单条数据
	 * 
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompId 生产单元对照配置主键id
	 * @throws Exception
	 * @return 生产单元对照配置实体类
	 */
	@Override
	public AlarmPrdtCellCompEntity getSingleAlarmPrdtCellComp(Long alarmPrdtCellCompId) throws Exception {
		AlarmPrdtCellComp alarmPrdtCellComp = alarmPrdtCellCompRepository.getSingleAlarmPrdtCellComp(alarmPrdtCellCompId);
		AlarmPrdtCellCompEntity afce = ObjectConverter.entityConverter(alarmPrdtCellComp, AlarmPrdtCellCompEntity.class);
		afce.setDcsName(alarmPrdtCellComp.getDcsCode().getName());
		afce.setPrdtCellName(alarmPrdtCellComp.getPrdtCell().getName());
		return afce;
	}

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2018-04-04
	 * @param dcsCodeId DCS名称
	 * @param opcCodeId OPC名称
	 * @param prdtCellSource 源报警生产单元
	 * @param unitCodes 装置id集合
	 * @param prdtCellIds   本系统生产单元
	 * @param inUse   是否使用
	 * @param page 翻页实现类
	 * @return 翻页对象
	 * @throws Exception 
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<AlarmPrdtCellCompEntity> getAlarmPrdtCellComp(Long dcsCodeId,Long opcCodeId, String prdtCellSource, String[] unitCodes,Long[] prdtCellIds,Integer inUse, Pagination page) throws Exception {
		PaginationBean<AlarmPrdtCellComp> listAlarmPrdtCellComp = alarmPrdtCellCompRepository.getAlarmPrdtCellComp(dcsCodeId,opcCodeId,prdtCellSource,unitCodes,prdtCellIds,inUse, page);
		PaginationBean<AlarmPrdtCellCompEntity> returnAlarmPrdtCellComp = new PaginationBean<>(page,listAlarmPrdtCellComp.getTotal());
		returnAlarmPrdtCellComp.setPageList(ObjectConverter.listConverter(listAlarmPrdtCellComp.getPageList(), AlarmPrdtCellCompEntity.class));

		List<UnitEntity> unitEntities  = basicDataService.getUnitListByIds(returnAlarmPrdtCellComp.getPageList().stream().map(x->x.getUnitId()).distinct().toArray(String[]::new),false);
		returnAlarmPrdtCellComp.getPageList().stream().forEach(x->{
			AlarmPrdtCellComp apc = listAlarmPrdtCellComp.getPageList().stream().filter(y->y.getAlarmPrdtCellCompId().equals(x.getAlarmPrdtCellCompId())).findFirst().orElse(null);
			UnitEntity unitEntity = unitEntities.stream().filter(u->u.getStdCode().equals(x.getUnitId())).findFirst().orElse(new UnitEntity());
			x.setDcsName(apc.getDcsCode()==null?"":apc.getDcsCode().getName());
			x.setOpcName(apc.getOpcCode()==null?"":apc.getOpcCode().getName());
			x.setUnitName(unitEntity.getSname());
			x.setPrdtCellName(apc.getPrdtCell().getSname());
		});
		return returnAlarmPrdtCellComp;
	}
	/**
	 *根据生产单元名称获取源报警生产单元
	 *
	 * <AUTHOR> 2018-04-17
	 * @param prdtCellSource 源报警生产单元
	 * @param opcCode opc编码
	 * @param dcsCode dcs编码
	 * @throws Exception 
	 * @return AlarmPrdtCellCompEntity实体
	 */
	public AlarmPrdtCellCompEntity getPrdtCellInPrdtCellComp(String prdtCellSource,Long opcCode,Long dcsCode) throws Exception {
		AlarmPrdtCellComp alarmPrdtCellComp = alarmPrdtCellCompRepository.getPrdtCellInPrdtCellComp(prdtCellSource,opcCode,dcsCode);
		AlarmPrdtCellCompEntity alarmPrdtCellCompEntity = ObjectConverter.entityConverter(alarmPrdtCellComp, AlarmPrdtCellCompEntity.class);
		return alarmPrdtCellCompEntity;
	}

	@Override
	public List<DataServerMonVO> getDataServerMon(String unitCodes) {
		return alarmPrdtCellCompRepository.getDataServerMon(unitCodes);
	}

	//region 私有方法

	/**
	 * 校验
	 *
	 * <AUTHOR> 2018-04-04
	 * @param entity 生产单元对照配置实体
	 * @throws Exception
	 */
	private void alarmPrdtCellCompValidation(AlarmPrdtCellComp entity) throws Exception {
		CommonResult commonResult = null;
		// 实体不能为空
		if (entity == null) {
			throw new Exception("没有生产单元对照配置数据！");
		}
		// 调用DAL与数据库相关的校验
		commonResult = alarmPrdtCellCompRepository.alarmPrdtCellCompValidation(entity);

		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
	}

	@Override
	public List<PrdtCell> getPrdtCellByDcsName(Long dcsCodeId) {
		return alarmPrdtCellCompRepository.getPrdtCellByDcsName(dcsCodeId);
	}

	//endregion
}
