package com.pcitc.opal.af.bll.imp;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.af.bll.SequentialEventAnalysisService;
import com.pcitc.opal.af.bll.entity.SeqEventAnalysisEntity;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;

/*
 * 时序事件分析业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_SequentialEventAnalysisImpl
 * 作       者：dageng.sun
 * 创建时间：2017/11/14
 * 修改编号：1
 * 描       述：时序事件分析业务逻辑层接口 
 */
@Service
public class SequentialEventAnalysisImpl implements SequentialEventAnalysisService {

	/**
	 * 实例化数据访问层接口
	 */
	@Autowired
	private AlarmEventRepository repo;
	/**
	 * 公共方法操作类
	 */
	@Autowired
    private BasicDataService basicDataService;

	/**
	 * 查询时序事件分析图形业务逻辑层
	 * 
	 * <AUTHOR> 2017-11-16
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @return Map<String, Object> 返回Map<String, Object>对象
	 * @throws Exception 
	 */
	@Override
	public Map<String, Object> getSequentialEventGraph(String[] unitCodes, Long[] prdtCellIds, Date beginTime, Date endTime)
			throws Exception {
		Map<String, Object> map=new LinkedHashMap<String, Object>();//返回map对象
		SimpleDateFormat sdfDate=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		// 获取事件集合
		List<AlarmEvent> listProcess = repo.getSequentialEventGraph(unitCodes, prdtCellIds, beginTime, endTime);
		//报警（级别B）
		SeqEventAnalysisEntity seaeB=new SeqEventAnalysisEntity();
		List<ArrayList<Object>> yaxisB=new ArrayList<ArrayList<Object>>();
		List<String> tipsB=new ArrayList<String>();
		//报警（级别A）工艺卡片
		SeqEventAnalysisEntity seaeACraft=new SeqEventAnalysisEntity();
		List<ArrayList<Object>> yaxisACraft=new ArrayList<ArrayList<Object>>();
		List<String> tipsACraft=new ArrayList<String>();
		//报警（级别A）联锁
		SeqEventAnalysisEntity seaeALock=new SeqEventAnalysisEntity();
		List<ArrayList<Object>> yaxisALock=new ArrayList<ArrayList<Object>>();
		List<String> tipsALock=new ArrayList<String>();
		//报警（级别A）工艺卡片且联锁
		SeqEventAnalysisEntity seaeACraftLock=new SeqEventAnalysisEntity();
		List<ArrayList<Object>> yaxisACraftLock=new ArrayList<ArrayList<Object>>();
		List<String> tipsACraftLock=new ArrayList<String>();
		//操作数
		SeqEventAnalysisEntity seaeOperate=new SeqEventAnalysisEntity();
		List<ArrayList<Object>> yaxisOperate=new ArrayList<ArrayList<Object>>();
		List<String> tipsOperate=new ArrayList<String>();
		//其他
		SeqEventAnalysisEntity seaeOther=new SeqEventAnalysisEntity();
		List<ArrayList<Object>> yaxisOther=new ArrayList<ArrayList<Object>>();
		List<String> tipsOther=new ArrayList<String>();
		//列表
		List<AlarmEventEntity> listEntity=new ArrayList<AlarmEventEntity>();
		List<UnitEntity> units = basicDataService.getUnitListByIds(unitCodes, true);
		for(AlarmEvent ae:listProcess){
			if(ae.getEventTypeId().longValue()==CommonEnum.EventTypeEnum.ProcessEvent.getIndex().longValue()){
				ArrayList<Object> al=new ArrayList<Object>();
				String craftRank=ae.getAlarmPoint().getCraftRank().intValue()==1?CommonEnum.CraftRankEnum.A.getName():CommonEnum.CraftRankEnum.B.getName();
				if(CommonEnum.CraftRankEnum.B.getName().equals(craftRank)){
					al.add(sdfDate.format(ae.getStartTime()));
					al.add(3);
					yaxisB.add(al);
					tipsB.add("时间："+sdfDate.format(ae.getStartTime())+"<br>"+ae.getAlarmPoint().getTag()+"&nbsp;&nbsp;&nbsp;&nbsp;"+ae.getAlarmFlag().getName()+"&nbsp;&nbsp;&nbsp;&nbsp;级别："+craftRank);
				}else if(CommonEnum.CraftRankEnum.A.getName().equals(craftRank)){
					if((ae.getAlarmPoint().getCraftDownLimitValue()!=null||ae.getAlarmPoint().getCraftUpLimitValue()!=null)&&ae.getAlarmPoint().getInterlockDownLimitValue()==null&&ae.getAlarmPoint().getInterlockUpLimitValue()==null){
						al.add(sdfDate.format(ae.getStartTime()));
						al.add(3);
						yaxisACraft.add(al);
						tipsACraft.add("时间："+sdfDate.format(ae.getStartTime())+"<br>"+ae.getAlarmPoint().getTag()+"&nbsp;&nbsp;&nbsp;&nbsp;"+ae.getAlarmFlag().getName()+"&nbsp;&nbsp;&nbsp;&nbsp;级别："+craftRank);
					}else if((ae.getAlarmPoint().getInterlockDownLimitValue()!=null||ae.getAlarmPoint().getInterlockUpLimitValue()!=null)&&ae.getAlarmPoint().getCraftDownLimitValue()==null&&ae.getAlarmPoint().getCraftUpLimitValue()==null){
						al.add(sdfDate.format(ae.getStartTime()));
						al.add(3);
						yaxisALock.add(al);
						tipsALock.add("时间："+sdfDate.format(ae.getStartTime())+"<br>"+ae.getAlarmPoint().getTag()+"&nbsp;&nbsp;&nbsp;&nbsp;"+ae.getAlarmFlag().getName()+"&nbsp;&nbsp;&nbsp;&nbsp;级别："+craftRank);
					}else if((ae.getAlarmPoint().getInterlockDownLimitValue()!=null||ae.getAlarmPoint().getInterlockUpLimitValue()!=null)&&(ae.getAlarmPoint().getCraftDownLimitValue()!=null||ae.getAlarmPoint().getCraftUpLimitValue()!=null)){
						al.add(sdfDate.format(ae.getStartTime()));
						al.add(3);
						yaxisACraftLock.add(al);
						tipsACraftLock.add("时间："+sdfDate.format(ae.getStartTime())+"<br>"+ae.getAlarmPoint().getTag()+"&nbsp;&nbsp;&nbsp;&nbsp;"+ae.getAlarmFlag().getName()+"&nbsp;&nbsp;&nbsp;&nbsp;级别："+craftRank);
					}else{
						al.add(sdfDate.format(ae.getStartTime()));
						al.add(1);
						yaxisOther.add(al);
						tipsOther.add("时间："+sdfDate.format(ae.getStartTime())+"<br>"+ae.getAlarmPoint().getTag()+"&nbsp;&nbsp;&nbsp;&nbsp;"+ae.getAlarmFlag().getName());
					}
				}else{
					al.add(sdfDate.format(ae.getStartTime()));
					al.add(1);
					yaxisOther.add(al);
					tipsOther.add("时间："+sdfDate.format(ae.getStartTime())+"<br>"+ae.getAlarmPoint().getTag()+"&nbsp;&nbsp;&nbsp;&nbsp;"+ae.getAlarmFlag().getName());
				}
			}else if(String.valueOf(ae.getEventTypeId()).startsWith(CommonEnum.EventTypeEnum.OperateEvent.getIndex().toString())){
				ArrayList<Object> al=new ArrayList<Object>();
				al.add(sdfDate.format(ae.getStartTime()));
				al.add(2);
				yaxisOperate.add(al);
				tipsOperate.add("时间："+sdfDate.format(ae.getStartTime())+"<br>"+ae.getAlarmPoint().getTag()+"&nbsp;&nbsp;&nbsp;&nbsp;"+ae.getAlarmFlag().getName());
			}else{
				ArrayList<Object> al=new ArrayList<Object>();
				al.add(sdfDate.format(ae.getStartTime()));
				al.add(1);
				yaxisOther.add(al);
				tipsOther.add("时间："+sdfDate.format(ae.getStartTime())+"<br>"+ae.getAlarmPoint().getTag()+"&nbsp;&nbsp;&nbsp;&nbsp;"+ae.getAlarmFlag().getName());
			}
			AlarmEventEntity aee=new AlarmEventEntity();
			aee.setEventId(ae.getEventId());
			aee.setEventTypeId(ae.getEventTypeId());
			aee.setStartTime(ae.getStartTime());
			aee.setStartTimeStr(sdfDate.format(ae.getStartTime()));
			UnitEntity unit = units.stream().filter(u -> u.getStdCode().equals(ae.getAlarmPoint().getPrdtCell().getUnitId())).findFirst().orElse(null);
			aee.setUnitName(unit.getSname());
			aee.setPrdtCellName(ae.getAlarmPoint().getPrdtCell().getSname());
			aee.setAlarmPointTag(ae.getAlarmPoint().getTag());
			aee.setCraftRank(ae.getAlarmPoint().getCraftRank());
			aee.setAlarmFlagName(ae.getAlarmFlag().getName());
			aee.setPriority(ae.getPriority());
			aee.setEventTypeName(ae.getEventType().getName());
			aee.setNowValue(ae.getNowValue());
			aee.setMeasUnitName(ae.getAlarmPoint().getMeasUnit().getName() + "("+ ae.getAlarmPoint().getMeasUnit().getSign() + ")");
			aee.setDes(ae.getDes());
			aee.setLocation(ae.getAlarmPoint().getLocation());
			listEntity.add(aee);
		};
		//报警（B级别）
		seaeB.setYaxisList(yaxisB);
		seaeB.setTipsList(tipsB);
		map.put("processB", seaeB);
		//报警（级别A）工艺卡片
		seaeACraft.setYaxisList(yaxisACraft);
		seaeACraft.setTipsList(tipsACraft);
		map.put("processACraft", seaeACraft);
		//报警（级别A）联锁
		seaeALock.setYaxisList(yaxisALock);
		seaeALock.setTipsList(tipsALock);
		map.put("processALock", seaeALock);
		//报警（级别A）工艺卡片且联锁
		seaeACraftLock.setYaxisList(yaxisACraftLock);
		seaeACraftLock.setTipsList(tipsACraftLock);
		map.put("processACraftLock", seaeACraftLock);
		//操作数
		seaeOperate.setYaxisList(yaxisOperate);
		seaeOperate.setTipsList(tipsOperate);
		map.put("processOperate", seaeOperate);
		//其他
		seaeOther.setYaxisList(yaxisOther);
		seaeOther.setTipsList(tipsOther);
		map.put("processOther", seaeOther);
		//列表
		map.put("listEntity", listEntity);
		return map;
	}
	
}
