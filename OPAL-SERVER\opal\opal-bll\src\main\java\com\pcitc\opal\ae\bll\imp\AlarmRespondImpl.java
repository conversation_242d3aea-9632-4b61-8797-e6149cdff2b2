package com.pcitc.opal.ae.bll.imp;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.pcitc.opal.aa.bll.entity.PeakAlarmRateEntity;
import com.pcitc.opal.ad.bll.entity.*;
import com.pcitc.opal.ad.dao.AlarmRecRepository;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.DateHelper;
import com.pcitc.opal.common.pojo.DateRange;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.pcitc.opal.ad.dao.AlarmEventViewExRepository;
import com.pcitc.opal.ad.pojo.AlarmEventViewEx;
import com.pcitc.opal.ae.bll.AlarmRespondService;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.DateRangeEntity;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;

import pcitc.imp.common.ettool.utils.ObjectConverter;

import static java.util.stream.Collectors.toList;

/*
 * 报警响应业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmRespondImpl
 * 作       者：dageng.sun
 * 创建时间：2017/10/24
 * 修改编号：1
 * 描       述：报警响应业务逻辑层实现类
 */
@Service
@Component
public class AlarmRespondImpl implements AlarmRespondService {

	/**
	 * 实例化数据访问层接口
	 */
	@Autowired
	private AlarmEventViewExRepository repo;
	@Autowired
	private BasicDataService basicDataService;
	@Autowired
    private ShiftService shiftService;
	@Autowired
	private AlarmRecRepository alarmRecRepository;
	
	/**
	 * 报警响应分页查询
	 * 
	 * <AUTHOR> 2017-10-24
	 * @param unitCodes 装置id数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 位号
	 * @param alarmFlagId 报警表示
	 * @param priority 优先级
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param isTimeResponse 是否及时响应
	 * @param workTeamIds 班组编号
	 * @param responseDuration 响应时长
	 * @param page 翻页实现类
	 * @return
	 * @throws Exception 
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<AlarmRecEntity> getAlarmRespond(String[] unitCodes,
			Long[] prdtCellIds, String tag, Long alarmFlagId, Integer priority, Date beginTime, Date endTime,
			Integer isTimeResponse, Long[] workTeamIds, Integer responseDuration, Pagination page) throws Exception {
		List<UnitEntity> unitEntityList = null;
		if(ArrayUtils.isEmpty(unitCodes)){
			unitEntityList = basicDataService.getUnitList( true);
			unitCodes=unitEntityList.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
		}
		if (workTeamIds == null) {
			workTeamIds = new Long[]{};
        }
        List<Long> workTeamIdList = Arrays.asList(workTeamIds);
        List<DateRangeEntity> dateRangeList = new ArrayList<>();
        List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
        if (workTeamIdList.size() != 0 && unitCodes != null && unitCodes.length == 1) {
            shiftWorkList = shiftService.getShiftList(unitCodes[0], beginTime, endTime, workTeamIdList);
            dateRangeList = shiftWorkList.stream().map(item -> new DateRangeEntity(item.getStartTime(), item.getEndTime())).collect(toList());
        }
		List<DateRange> dateList = ObjectConverter.listConverter(dateRangeList, DateRange.class);
		List<Object[]> objectList = alarmRecRepository.getAlarmRespond(unitCodes, prdtCellIds, tag, alarmFlagId, priority, beginTime, endTime, isTimeResponse, dateList, responseDuration, page);
		Object objectTotalObj = alarmRecRepository.getAlarmRespondTotal(unitCodes, prdtCellIds, tag, alarmFlagId, priority, beginTime, endTime, isTimeResponse, dateList, responseDuration);

		if(unitEntityList ==null){
			String[] filterunitCodes = objectList.stream().map(e -> e[9].toString()).distinct().toArray(String[]::new);
			unitEntityList = basicDataService.getUnitListByIds(filterunitCodes, false);
		}

		Long aLong = Long.valueOf(objectTotalObj.toString());

		PaginationBean<AlarmRecEntity> returnAlarmViewExEvent = new PaginationBean<>(page, aLong);

		//查询当前条件所有数据，计算响应率
		Object alarmRespondTotal = alarmRecRepository.getAlarmRespondTotal(unitCodes, prdtCellIds, tag, alarmFlagId, priority, beginTime, endTime, null, dateList, null);
		Long totalCount= Long.valueOf(alarmRespondTotal.toString());

		Long count =  0L;
		count = aLong;
//		if (null != responseDuration && 5==responseDuration){
//			count = 0L;
//		}else {
//			count = aLong;
//		}


		String respondRate="0.00%";
		if(totalCount!=0){
			respondRate= Math.round((float) count / (float) totalCount * 10000) / 100f +"%";
		}

		List<AlarmRecEntity> pageList = new ArrayList<>();
		for (Object[] o:objectList){
			AlarmRecEntity alarmEventViewExEntity = new AlarmRecEntity();
			Date alarmTime = DateHelper.parseDate(o[0].toString().substring(0,19));
			Date responseTime = null != o[1]?DateHelper.parseDate(o[1].toString().substring(0,19)):null;
			String unitCode = o[2].toString();
			UnitEntity unit = unitEntityList.stream().filter(u -> o[9].toString().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
			String unitName = unit.getSname();
			Long prdtcellId = Long.valueOf(o[3].toString());
			String prdtcellName = o[10].toString();
			String tag1 = o[4].toString();
			String des = null!=o[5]?o[5].toString():null;
			Long alarmFlagId1 = Long .valueOf(o[6].toString());
			String alarmFlagName = o[11].toString();
			Integer priority1 = Integer.valueOf(o[7].toString());
			Long prescribedResponseDuration = Long.valueOf(o[8].toString());
			alarmEventViewExEntity.setAlarmTime(alarmTime);
			alarmEventViewExEntity.setResponseTime(responseTime);
			alarmEventViewExEntity.setUnitName(unitName);
			alarmEventViewExEntity.setPrdtCellName(prdtcellName);
			alarmEventViewExEntity.setTag(tag1);
			alarmEventViewExEntity.setDes(des);
			alarmEventViewExEntity.setAlarmFlagId(alarmFlagId1);
			alarmEventViewExEntity.setAlarmFlagName(alarmFlagName);
			alarmEventViewExEntity.setPriority(priority1);
			alarmEventViewExEntity.setPriorityName(CommonEnum.InstrmtPriorityEnum.getName(priority1));
			alarmEventViewExEntity.setPrescribedResponseDuration(prescribedResponseDuration);
			alarmEventViewExEntity.setResponseTimeRate(respondRate);
			alarmEventViewExEntity.setLocation(o[12].toString());
			pageList.add(alarmEventViewExEntity);
		}
		returnAlarmViewExEvent.setPageList(pageList);

		if (dateRangeList.size() == 0 && workTeamIdList.size() == 0) {
			Date minDate=returnAlarmViewExEvent.getPageList().stream().reduce((item1,item2)->item1.getAlarmTime().getTime()<item2.getAlarmTime().getTime()?item1:item2).orElse(new AlarmRecEntity()).getAlarmTime();
	        Date maxDate=returnAlarmViewExEvent.getPageList().stream().reduce((item1,item2)->item1.getAlarmTime().getTime()>item2.getAlarmTime().getTime()?item1:item2).orElse(new AlarmRecEntity()).getAlarmTime();
	        if (minDate != null && maxDate != null) {
				shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCodes), minDate, maxDate);
			}
		}

		int i=0;
		for(AlarmRecEntity aevee:returnAlarmViewExEvent.getPageList()){
			aevee.setTeam(shiftWorkList.parallelStream().filter(item -> aevee.getAlarmTime().getTime() >= item.getStartTime().getTime() && aevee.getAlarmTime().getTime() < item.getEndTime().getTime()).findFirst().orElse(new ShiftWorkTeamEntity()).getWorkTeamSName());

			if(null != aevee.getResponseTime() && aevee.getResponseTime().getTime()>endTime.getTime()){
				aevee.setResponseTime(null);
			}
			if(aevee.getResponseTime()!=null) {
				aevee.setActualResponseDuration(aevee.getResponseTime().getTime()/1000-aevee.getAlarmTime().getTime()/1000);
			}
			aevee.setResponseTimeRate(respondRate);
		}
		return returnAlarmViewExEvent;
	}

	@Override
	public  PaginationBean<AlarmRespondEntity> getAlarmRespondForInterface(String[] workUnitIds, Date startTime, Date endTime, Pagination page) throws Exception {
		List<UnitEntity> unitList=null;
		if (workUnitIds!=null && workUnitIds.length > 0) {
			unitList=basicDataService.getUnitListByWorkshopIds(workUnitIds, false);
		}else{
			unitList=basicDataService.getUnitList(false);
		}
        String[] unitCodes=unitList.stream().map(x->x.getStdCode()).distinct().toArray(String[]::new);


        Long[] workTeamIds = new Long[]{};

        List<Long> workTeamIdList = Arrays.asList(workTeamIds);
        List<DateRangeEntity> dateRangeList = new ArrayList<>();
        List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
        if (workTeamIdList.size() != 0 && unitCodes != null && unitCodes.length == 1) {
            shiftWorkList = shiftService.getShiftList(unitCodes[0], startTime, endTime, workTeamIdList);
            dateRangeList = shiftWorkList.stream().map(item -> new DateRangeEntity(item.getStartTime(), item.getEndTime())).collect(toList());
        }

		if (null == unitCodes || unitCodes.length == 0){
		    return null;
        }
		PaginationBean<AlarmEventViewEx> listAlarmEventViewEx = repo.getAlarmRespondFor20S(unitCodes, null, null, null, null, startTime, endTime, 1, null, 20, page);
		PaginationBean<AlarmEventViewExEntity> returnAlarmViewExEvent = new PaginationBean<AlarmEventViewExEntity>(page,listAlarmEventViewEx.getTotal());
		returnAlarmViewExEvent.setPageList(ObjectConverter.listConverter(listAlarmEventViewEx.getPageList(), AlarmEventViewExEntity.class));

        PaginationBean<AlarmRespondEntity> returnBean = new PaginationBean<>(page,listAlarmEventViewEx.getTotal());

        if (dateRangeList.size() == 0 && workTeamIdList.size() == 0) {
            Date minDate=returnAlarmViewExEvent.getPageList().stream().reduce((item1,item2)->item1.getAlarmTime().getTime()<item2.getAlarmTime().getTime()?item1:item2).orElse(new AlarmEventViewExEntity()).getAlarmTime();
            Date maxDate=returnAlarmViewExEvent.getPageList().stream().reduce((item1,item2)->item1.getAlarmTime().getTime()>item2.getAlarmTime().getTime()?item1:item2).orElse(new AlarmEventViewExEntity()).getAlarmTime();
            if (minDate != null && maxDate != null)
                shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCodes!=null?unitCodes:new String[]{}), minDate, maxDate);
        }

		int i=0;
		for(AlarmEventViewExEntity aevee:returnAlarmViewExEvent.getPageList()){
			AlarmEventViewEx aeve=listAlarmEventViewEx.getPageList().get(i);
			UnitEntity ue=unitList.stream().filter(ul->aeve.getAlarmPoint().getPrdtCell().getUnitId().equals(ul.getStdCode())).findFirst().orElse(new UnitEntity());
			aevee.setUnitSname(ue.getSname());
			aevee.setPrdtCellSname(aeve.getAlarmPoint().getPrdtCell().getSname());
			aevee.setTeam(shiftWorkList.parallelStream().filter(item -> aevee.getAlarmTime().getTime() >= item.getStartTime().getTime() && aevee.getAlarmTime().getTime() < item.getEndTime().getTime()).findFirst().orElse(new ShiftWorkTeamEntity()).getWorkTeamSName());
			aevee.setTag(aeve.getAlarmPoint().getTag());
			aevee.setLocation(aeve.getAlarmPoint().getLocation());
			aevee.setAlarmFlagName(aeve.getAlarmFlag().getName());
			if(aeve.getResponseTime()!=null && aeve.getResponseTime().getTime()>endTime.getTime()){
				aevee.setResponseTime(null);
			}
			if(aevee.getResponseTime()!=null)
				aevee.setActualResponseDuration(aeve.getResponseTime().getTime()/1000-aeve.getAlarmTime().getTime()/1000);
			aevee.setPrescribedResponseDuration(aeve.getTagExtraMessage().getPrescribedResponseDuration());
			aevee.setDes(aeve.getDes());
			i++;
		}


		List<AlarmRespondEntity> list = new ArrayList<>();
		list = ObjectConverter.listConverter(returnAlarmViewExEvent.getPageList(), AlarmRespondEntity.class);
        returnBean.setPageList(list);
		return returnBean;
	}

	@Override
	public List<UnRespondEntity> getAlarmRespondOfUnRespond(Date startTime, Date endTime, String[] unitIds) throws Exception {

		List<UnitEntity> unitList = basicDataService.getUnitListByIds(unitIds, false);

		List<AlarmRecEntity> alarmRecEntityList = new ArrayList<>();
		Date now = DateHelper.now();
		List<Object[]> objectList = alarmRecRepository.getAlarmRespondOfUnRespond(unitIds, startTime, endTime, now);
		Map<String,Object> map = new HashMap<String,Object>();
		for (Object[] o:objectList){
			AlarmRecEntity alarmEventEntity = new AlarmRecEntity();
			String unitCode = o[0].toString();
			UnitEntity unit = unitList.stream().filter(u -> o[0].toString().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
			String unitName = unit.getSname();
			String tag = o[1].toString();
			map.put(tag,o[2]);
			BigDecimal order = (BigDecimal)map.get(tag);
			double d = (order==null?0:order.doubleValue());
			Long actualResponseDuration = Math.round(d);
			alarmEventEntity.setUnitCode(unitCode);
			alarmEventEntity.setUnitName(unitName);
			alarmEventEntity.setTag(tag);
			alarmEventEntity.setActualResponseDuration(actualResponseDuration);
			alarmRecEntityList.add(alarmEventEntity);
		}
		Map<String, List<AlarmRecEntity>> collect = alarmRecEntityList.stream().collect(Collectors.groupingBy(AlarmRecEntity::getUnitName));
		List<UnRespondEntity> returnList = new ArrayList<>();
		for (Map.Entry<String, List<AlarmRecEntity>> entry : collect.entrySet()) {
			UnRespondEntity unRespondEntity = new UnRespondEntity();
			unRespondEntity.setUnitName(entry.getKey());
			unRespondEntity.setAlarmCount(entry.getValue().size());
			unRespondEntity.setUnitCode(entry.getValue().get(0).getUnitCode());
			List<TagAndDurationEntity> tagAndDurationEntities = new ArrayList<>();
			for (AlarmRecEntity entity:entry.getValue()) {
				TagAndDurationEntity tagAndDurationEntity = new TagAndDurationEntity();
				tagAndDurationEntity.setTag(entity.getTag());
				/*Double l = Double.valueOf(entity.getActualResponseDuration()) / 60;
				DecimalFormat sf = new DecimalFormat("#.00");
				String format = sf.format(l);*/
				double div = div(entity.getActualResponseDuration(), 60, 1);
				//tagAndDurationEntity.setDuration(Double.parseDouble(format));
				tagAndDurationEntity.setDuration(div);
				tagAndDurationEntities.add(tagAndDurationEntity);
			}
			unRespondEntity.setTagAndDurationEntityList(tagAndDurationEntities);
			returnList.add(unRespondEntity);
		}
		//没有数值的装置赋值为0
		for (UnitEntity unitEntity:unitList) {
			if (!returnList.stream().filter(w -> w.getUnitCode().equals(unitEntity.getStdCode())).findAny().isPresent()) {
				UnRespondEntity unRespondEntity = new UnRespondEntity();
				unRespondEntity.setUnitName(unitEntity.getSname());
				unRespondEntity.setAlarmCount(0);
				unRespondEntity.setUnitCode(unitEntity.getStdCode());
				List<TagAndDurationEntity> tagAndDurationEntities = new ArrayList<>();
				unRespondEntity.setTagAndDurationEntityList(tagAndDurationEntities);
				returnList.add(unRespondEntity);
			}
		}
		return returnList;
	}

	/**
	 * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指
	 * 定精度，以后的数字四舍五入。
	 * @param v1 被除数
	 * @param v2 除数
	 * @param scale 表示表示需要精确到小数点以后几位。
	 * @return 两个参数的商
	 */
	public static double div(double v1,double v2,int scale){
		if(scale<0){
			throw new IllegalArgumentException(
					"The scale must be a positive integer or zero");
		}
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b1.divide(b2,scale,BigDecimal.ROUND_HALF_UP).doubleValue();
	}
}
