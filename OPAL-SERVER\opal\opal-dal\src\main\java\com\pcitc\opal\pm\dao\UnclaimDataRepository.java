package com.pcitc.opal.pm.dao;

import com.pcitc.opal.pm.pojo.AlarmEventCache;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * UnclaimData实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_UnclaimDataRepository
 * 作       者：xuelei.wang
 * 创建时间：2018-04-16
 * 修改编号：1
 * 描       述：UnclaimData实体的Repository实现   
 */
public interface UnclaimDataRepository extends JpaRepository<AlarmEventCache, Long>, UnclaimDataRepositoryCustom {

}
