package com.pcitc.opal.pm.bll.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @USER: chenbo
 * @DATE: 2022/12/1
 * @TIME: 17:03
 * @DESC:
 **/
@Getter
@AllArgsConstructor
public class SendMsgConfig {
    private String url;
    private String userId;
    private String passWord;
    private String spNumber;
    private Integer companyId;
}
