package com.pcitc.opal.ak.dao.imp;

import com.pcitc.opal.ak.dao.AlarmKnowlgManagmtRepositoryCustom;
import com.pcitc.opal.ak.pojo.AlarmKnowlgManagmt;
import com.pcitc.opal.as.pojo.AlarmStdManagmt;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.TypedQuery;
import java.util.*;


/*
 * 报警知识管理实体的Repository的JPA接口实现
 * 模块编号： pcitc_opal_dal_class_AlarmKnowlgManagmtRepositoryImpl
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理实体的Repository的JPA接口实现
 */
public class AlarmKnowlgManagmtRepositoryImpl extends BaseRepository<AlarmKnowlgManagmt, Long>
		implements AlarmKnowlgManagmtRepositoryCustom {

    /**
     * 新增报警知识维护数据
     *
     * <AUTHOR> 2018-03-09
     * @param alarmKnowlgManagmt 报警知识实体
     * @throws Exception
     * @return CommonResult 消息结果类
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addAlarmKnowlgManagmt(AlarmKnowlgManagmt alarmKnowlgManagmt){
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(alarmKnowlgManagmt);
            commonResult.setResult(alarmKnowlgManagmt);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 加载报警知识维护主数据
     *
     * @param eventId       报警事件id
     * @param alarmPointId  报警点id
     * @param alarmFlagId   报警标识id
     * @param page   分页信息
     * @return 报警知识维护数据集合
     * @throws Exception
     * <AUTHOR> 2018-03-09
     */
    @Override
    public PaginationBean<AlarmKnowlgManagmt> getAlarmKnowlgManagmt(Long eventId,Long alarmPointId,Long alarmFlagId, Pagination page){
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from AlarmKnowlgManagmt t left join fetch  t.alarmEvent a  " +
                    "where a.companyId=:companyId and t.eventId =:eventId " +
                    "or (a.alarmPointId = :alarmPointId " +
                    "and a.alarmFlagId  = :alarmFlagId) " +
                    "order by (case when t.eventId=:eventId then 1 else 0 end) desc,t.crtDate desc");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("eventId", eventId);
            paramList.put("alarmPointId", alarmPointId);
            paramList.put("alarmFlagId", alarmFlagId);
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
    }

}
