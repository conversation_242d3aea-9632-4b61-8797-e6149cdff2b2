$(function () {
    var addUrl = OPAL.API.asUrl + '/alarmStdManagmt';
    var updateUrl = OPAL.API.pmUrl + '/AlarmExamineRec/saveAlarmExamineRec';
    var downloadFileUrl = OPAL.API.asUrl + '/alarmStdManagmt/downloadFile';
    var getUserNameUrl = OPAL.API.commUrl + '/getSysUserInfo';
    var getSysDateUrl = OPAL.API.commUrl + '/getSysDateTime';
    var pageMode = PageModelEnum.NewAdd;
    window.pageLoadMode = PageLoadMode.Refresh;
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var uploader;
    var alarmExamDetail = {};
    var saveData = {};
    var page = {
        init: function () {
            this.bindUI();
            //初始化时间
            page.logic.initDate();
            //初始化上传人
            page.logic.initUserName();
            page.logic.upload();
        },
        bindUI: function () {
            /*关闭弹窗*/
            $('#closePage').click(function () {
                page.logic.closeLayer(false);
            });
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            });
            //保存
            $("#saveAddModal").click(function () {
                page.logic.save('0');
            })
            //提交
            $("#submitAddModal").click(function () {
                page.logic.save('1');
            })
            //清除附件
            $("#btnClear").click(function () {
                uploader.removeFile(uploader.files[0]);
            });
            //删除附件
            $("#delAtt").click(function () {
                page.logic.delAtt();
            })
            $("#attName").click(function () {
                page.logic.download();
            })
        },
        logic: {
            //初始化时间
            initDate: function () {
                var date = laydate.render({
                    elem: '#recoveryTime', //指定元素
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd HH:mm:ss', //日期格式
                });
                // $.ajax({
                //     url: getSysDateUrl,
                //     async: false,
                //     dataType: "json",
                //     success: function (data) {
                //         var dataArr = $.ET.toObjectArr(data);
                //         var date = laydate.render({
                //             elem: '#recoveryTime', //指定元素
                //             type: 'datetime',
                //             trigger: 'click',
                //             btns: ['clear', 'confirm'],
                //             format: 'yyyy-MM-dd HH:mm:ss', //日期格式
                //             max: dataArr[0].value //最大日期
                //         });
                //         $("#recoveryTime").val(dataArr[0].value);
                //     },
                //     error: function (jqXHR, textStatus, errorThrown) { }
                // });
            },
            //初始化登录人
            initUserName: function () {
                $.ajax({
                    url: getUserNameUrl,
                    async: true,
                    dataType: "json",
                    success: function (data) {
                        var dataArr = $.ET.toObjectArr(data);
                        $("#uplUserName").val(dataArr[1].value);
                    },
                    error: function (jqXHR, textStatus, errorThrown) { }
                });
            },
            //上传
            upload: function () {
                uploader = new plupload.Uploader({
                    browse_button: 'fileId', //触发文件选择对话框的按钮，为那个元素id
                    url: addUrl, //服务器端的上传页面地址
                    flash_swf_url: '../../../js/common/plupload/js/Moxie.swf', //swf文件，当需要使用swf方式进行上传时需要配置该参数
                    multi_selection: false,
                    multipart_params: saveData
                });
                uploader.init();
                uploader.bind('FilesAdded', function (uploader, files) {
                    if (uploader.files.length > 1) {
                        uploader.removeFile(uploader.files[0]);
                    }
                    $("#uplAttaName").val(files[0].name);
                });
                uploader.bind('FilesRemoved', function (uploader, files) {
                    $("#uplAttaName").val("");
                });
                uploader.bind('FileUploaded', function (uploader, file, responseObject) {
                    var result = responseObject.response;
                    if (result.indexOf('collection') < 0) {
                        layer.msg(result);
                        setTimeout(function () {
                            page.logic.closeLayer(true);
                        }, 1000)
                    } else {
                        layer.msg(result.collection.error.message);
                    }
                });
                uploader.bind('Error', function (uploader, errObject) {
                    errObject.file.status = 2;
                    layer.msg(JSON.parse(errObject.response).collection.error.message)
                });
            },
            /**
             * 保存
             */
            save: function (type) {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    // if ($("#uplAttaName").val() == '' && $("#attName").html() == '') {
                    //     $('.error-msg-file').html('这是必填字段');
                    //     $('#uplAttaName').css({borderColor: '#cc5965'});
                    // }
                    return;
                }
                // 保存0 提交1
                $("#status").val(type);
                // var data = OPAL.form.getETCollectionData("AddOrEditModal");
                saveData = OPAL.form.getData('AddOrEditModal');
                uploader.settings.multipart_params = saveData;
                uploader.settings.url = updateUrl;
                //已存附件和上传附件为空 不保存
                if ($("#uplAttaName").val() == '' && $("#attName").html() == '') {
                    layer.msg("附件不能为空！");
                    return;
                }
                if ($("#uplAttaName").val() == '') { //附件未修改
                    saveData.name = $("#attName").html();
                    $.ajax({
                        url: updateUrl,
                        async: false,
                        type: "POST",
                        data: saveData,
                        // // contentType: "application/json;charset=utf-8",
                        success: function (result, XMLHttpRequest) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    page.logic.closeLayer(true);
                                });
                            } else {
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                } else if ($("#uplAttaName") != '') { //附件修改
                    uploader.start();
                }

            },
            //数据校验
            formValidate: function () {
                // $.validator.addMethod("dateCheck", function (value, element) {
                //     var returnVal = true;
                //     var reg = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$/;
                //     if (!reg.test(value)) returnVal = false;
                //     return returnVal;
                // }, "时间格式错误！"); //验证错误信息
                OPAL.form.formValidate('AddOrEditModal', {
                    rules: {
                        recoveryTime: {
                            required: true
                        },
                        reasonAnly: {
                            required: true
                        }
                    },
                    messages: {
                        uplAttaName: {
                            rangelength: "文件名最大字符长度100"
                        }
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                $("#pageTitle").text(data.title);
                alarmExamDetail = JSON.parse(data.row);
                if (pageMode == PageModelEnum.Edit) {
                    $("#alarmExamineRecId").val(alarmExamDetail.alarmExamineRecId);
                    $("#recoveryTime").val(alarmExamDetail.recoveryTime);
                    $("#reasonAnly").val(alarmExamDetail.reasonAnly);
                    $("#attName").html(alarmExamDetail.uplAttaName);
                    $("#uplAttaId").val(alarmExamDetail.uplAttaId);
                }
                $("#alarmRecId").val(alarmExamDetail.alarmRecId);
                $("#uplAttaName").val('');
                if (alarmExamDetail.uplAttaName != '') {
                    $("#AttachmentBox").show();
                }
                OPAL.form.setData('formDetail', alarmExamDetail);
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                parent.layer.close(index);
            },
            //删除附件
            delAtt: function () {
                layer.confirm('确定删除此附件吗？', {
                    btn: ['确定', '取消']
                }, function (index) {
                    $("#attName").html("");
                    $("#AttachmentBox").hide();
                    layer.close(index);
                }, function (index) { });
            },
            download: function () {
                $('#formExPort').attr('action', downloadFileUrl);
                $('#uplAttaName1').val($("#attName").html());
                $('#uplAttaId1').val($("#uplAttaId").val());
                $('#formExPort').submit();
            }
        }
    }
    page.init();
    window.page = page;
})