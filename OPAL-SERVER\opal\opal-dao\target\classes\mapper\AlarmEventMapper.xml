<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.ad.dao.AlarmEventDAO">

    <select id="getNotDisposeData" resultType="com.pcitc.opal.ad.entity.AlarmEventEntity">
        select *
        from t_ad_alarmevent
        where event_id > #{currentId}
          and company_id = #{companyId}
        order by event_id
    </select>

    <select id="selectFloodAlarmPointCount" resultType="com.pcitc.opal.ad.vo.FloodAlarmPointCountVO">
        SELECT COUNT(*)   as                    alarmCount,
               STR_TO_DATE(concat(date_format(ALARM_TIME, '%Y-%m-%d %H:'),
                                  truncate((cast(date_format(T.ALARM_TIME, '%i') as SIGNED) / 10), 0), '0'),
                           '%Y-%m-%d %H:%i:%s') alarmTime,
               a.tag      as                    tag,
               a.location as                    location,
               r.sname    as                    prdtCellName,
               u.sname    as                    unitName,
               af.name    as                    alarmFlag,
               a.monitor_type as monitorType
        FROM T_AD_ALARMEVENT T
                 INNER JOIN T_PM_ALARMPOINT A ON T.ALARM_POINT_ID = A.ALARM_POINT_ID and A.In_Use = 1
                 INNER JOIN T_PM_PRDTCELL R ON A.PRDTCELL_ID = R.PRDTCELL_ID
                 inner join t_pm_unit u on R.unit_code = u.std_code
                 inner join t_ad_alarmflag af on t.alarm_flag_id = af.alarm_flag_id
        WHERE T.EVENT_TYPE_ID = 1001
            <if test="unitCodeList != null and unitCodeList.size() > 0">
                and t.unit_code in
                <foreach collection="unitCodeList" item="unitCod" index="index" open="(" close=")" separator=",">
                    #{unitCod}
                </foreach>
            </if>
            <if test="prdtIds != null and prdtIds.size() > 0">
                and t.prdtcell_id in
                <foreach collection="prdtIds" item="prdtId" index="index" open="(" close=")" separator=",">
                    #{prdtId}
                </foreach>
            </if>
          and t.alarm_time between #{startTime} and #{endTime}
          AND T.ALARM_POINT_ID is not null
          and T.ALARM_FLAG_ID is not null
          and T.priority is not null
        GROUP BY T.ALARM_POINT_ID, T.ALARM_FLAG_ID, alarmTime
    </select>

    <select id="getAlarmEventTable" resultType="com.pcitc.opal.ad.vo.AlarmEventTableVO">
        select
        ae.event_id as eventId,
        ae.event_type_id as eventTypeId,
        ae.alarm_point_id  as alarmPointId,
        ae.alarm_flag_id as alarmFlagId,
        ae.start_time as startTime,
        ae.alarm_time as alarmTime,
        ae.priority as priority,
        ae.limit_value as limitValue,
        ae.in_shelved as inShelved,
        ae.in_suppressed as inSuppressed,
        ae.operator as operator,
        ae.des as des,
        ae.previous_value as previousValue,
        ae.now_value as nowValue,
        ae.parameter as parameter,
        ae.unit_code as unitCode,
        ae.prdtcell_id  as prdtCellId,
        ae.tag as alarmPointTag,
        ae.tag as tag,
        ae.alarm_flag as alarmFlagType,
        ae.priority_cache as priorityCache,
        ae.dcs_code as dcsCode,
        ae.write_time as writeTime,
        ap.location as location,
        ap.measunit_id as measUnitId,
        ap.alarm_point_hh as alarmPointHH,
        ap.alarm_point_hi as alarmPointHI,
        ap.alarm_point_lo as alarmPointLO,
        ap.alarm_point_ll as alarmPointLL,
        ap.craft_up_limit_include as craftUpLimitInclude,
        ap.craft_down_limit_include as craftDownLimitInclude,
        ap.craft_up_limit_value as craftUpLimitValue,
        ap.craft_down_limit_value as craftDownLimitValue,
        ap.interlock_up_limit_include as interlockUpLimitInclude,
        ap.interlock_down_limit_include as interlockDownLimitInclude,
        ap.interlock_up_limit_value as interlockUpLimitValue,
        ap.interlock_down_limit_value as interlockDownLimitValue,
        ap.craft_rank as craftRank,
        unit.sname as unitName,
        prdtcell.sname as prdtCellName,
        af.name as alarmFlagName,
        et.name as eventTypeName,
        mu.name as measUnitName,
        aed.DEL_STATUS as delStatus,
        aed.crt_date as delApplyDate,
        aed.crt_user_name as delApplyUser,
        ap.monitor_type as monitorType
        from T_AD_ALARMEVENT ae
        left join T_PM_ALARMPOINT ap on ae.ALARM_POINT_ID = ap.ALARM_POINT_ID
        left join T_PM_UNIT unit on ae.UNIT_CODE = unit.STD_CODE
        left join T_PM_PRDTCELL prdtcell on ap.PRDTCELL_ID = prdtcell.PRDTCELL_ID and unit.STD_CODE = prdtcell.unit_code
        left join T_AD_ALARMFLAG af on ae.ALARM_FLAG_ID = af.ALARM_FLAG_ID
        left join T_PM_EVENTTYPE et on ae.EVENT_TYPE_ID = et.EVENT_TYPE_ID
        left join T_PM_MEASUNIT mu on ap.MEASUNIT_ID = mu.MEASUNIT_ID
        left join T_AD_ALARMEVENTDELAPPROVE aed on ae.event_id = aed.event_id
        where 1=1  and case when ae.ALARM_POINT_ID is not null then ap.in_use else 1  end =1
        and exists (select * from t_ad_alarmflagcomp afc where (afc.alarm_flag_id=af.alarm_flag_id and afc.in_use=1) or ae.alarm_flag_id is null)
        <if test="vo.eventTypeIds != null and vo.eventTypeIds.size() > 0">
            and ae.EVENT_TYPE_ID in
            <foreach collection="vo.eventTypeIds" item="eventTypeId" index="index" open="(" close=")" separator=",">
                #{eventTypeId}
            </foreach>
        </if>
        <if test="vo.prdtCellIds != null and vo.prdtCellIds.size() > 0">
            and ae.prdtcell_id in
            <foreach collection="vo.prdtCellIds" item="prdtCellId" index="index" open="(" close=")" separator=",">
                #{prdtCellId}
            </foreach>
        </if>
        <if test="vo.alarmFlagIds != null">
            and ae.ALARM_FLAG_ID in
            <foreach collection="vo.alarmFlagIds" item="alarmFlagId" index="index" open="(" close=")" separator=",">
                #{alarmFlagId}
            </foreach>
        </if>
        <if test="vo.tag != null">
            <bind name="tagLike" value="'%' + vo.tag + '%'"/>
            and ae.tag like #{tagLike}
        </if>
        <if test="vo.priority != null">
            and ae.priority in
            <foreach collection="vo.priority" item="p" index="index" open="(" close=")" separator=",">
                #{p}
            </foreach>
        </if>
        <if test="vo.monitorType != null">
            and ap.monitor_type = #{vo.monitorType}
        </if>
        <if test="vo.beginTime != null and vo.endTime != null">
            and ae.start_time between #{vo.beginTime} and #{vo.endTime}
        </if>
        <if test="vo.craftRank != null">
            and ap.craft_rank = #{vo.craftRank}
        </if>
        <if test="vo.delstatus != null">
            <if test="vo.delstatus == 0">
                and aed.DEL_STATUS is null
            </if>
            <if test="vo.delstatus != 0">
                and aed.DEL_STATUS = #{vo.delstatus}
            </if>
        </if>
        <if test="vo.unitIds != null and vo.unitIds.size() > 0">
            and unit.STD_CODE in
            <foreach collection="vo.unitIds" item="unitId" index="index" open="(" close=")" separator=",">
                #{unitId}
            </foreach>
        </if>
        <if test="vo.isWorkTeam != null and vo.isWorkTeam == 1">
            and
            <foreach collection="vo.dateRangeList" item="dateRange" index="index" open="(" close=")" separator=" or ">
                (ae.start_Time &gt;= #{dateRange.startTime} and ae.start_Time &lt; #{dateRange.endTime})
            </foreach>
        </if>
        order by ae.start_time desc
    </select>

</mapper>
