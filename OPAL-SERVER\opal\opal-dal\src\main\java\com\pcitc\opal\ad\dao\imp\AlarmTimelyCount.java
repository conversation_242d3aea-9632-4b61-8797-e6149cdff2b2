package com.pcitc.opal.ad.dao.imp;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

/*
 * 变更事件查询工艺变更单条件实体
 * 模块编号：pcitc_pojo_ChangeEventCondition
 * 作       者：xuelei.wang
 * 创建时间：2017/09/30
 * 修改编号：1
 * 描       述：变更事件查询工艺变更单条件实体
 */
public class AlarmTimelyCount {

    public AlarmTimelyCount() {
    }
    public AlarmTimelyCount(
                            String unitCode,
                            Long timelyCount) {
        this.unitCode = unitCode;
        this.timelyCount = timelyCount;
    }


    /**
     *装置编码
     */
    private String unitCode;

    /**
     * 及时次数
     */
    private Long timelyCount;

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Long getTimelyCount() {
        return timelyCount;
    }

    public void setTimelyCount(Long timelyCount) {
        this.timelyCount = timelyCount;
    }
}
