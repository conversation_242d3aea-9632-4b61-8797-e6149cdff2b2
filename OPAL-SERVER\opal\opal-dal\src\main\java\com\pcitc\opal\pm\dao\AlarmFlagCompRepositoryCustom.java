package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.AlarmFlagComp;

import java.util.List;

/*
 * AlarmFlagComp实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmFlagCompRepositoryCustom
 * 作	者：jiangtao.xue
 * 创建时间：2018/03/30
 * 修改编号：1
 * 描	述：AlarmFlagComp实体的Repository的JPA自定义接口 
 */
public interface AlarmFlagCompRepositoryCustom {

	/**
	 * 校验数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompEntity 计量单位实体
	 * @return 返回结果信息类
	 */
	CommonResult alarmFlagCompValidation(AlarmFlagComp alarmFlagCompEntity);

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompEntity
	 *            计量单位实体
	 * @return 返回结果信息类
	 */
	CommonResult addAlarmFlagComp(AlarmFlagComp alarmFlagCompEntity);

	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompIds
	 *            计量单位ID集合
	 * @return 返回结果信息类
	 */
	CommonResult deleteAlarmFlagComp(Long[] alarmFlagCompIds);

	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompEntity
	 * @return 返回结果信息类
	 */
	CommonResult updateAlarmFlagComp(AlarmFlagComp alarmFlagCompEntity);

	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompId
	 *            计量单位ID
	 * @return 计量单位实体
	 */
	AlarmFlagComp getSingleAlarmFlagComp(Long alarmFlagCompId);

	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompIds
	 *            计量单位ID集合
	 * @return 计量单位实体集合
	 */
	List<AlarmFlagComp> getAlarmFlagComp(Long[] alarmFlagCompIds);

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param dcsCodeId DCS名称
	 * @param alarmFlagSource 源报警事件标识
	 * @param alarmFlagId 本系统报警标识
	 * @param inUse   是否使用
	 * @param page 翻页实现类
	 * @return 计量单位实体集合
	 */
	PaginationBean<AlarmFlagComp> getAlarmFlagComp(Long dcsCodeId, String alarmFlagSource, Long alarmFlagId,Integer inUse, Pagination page);
	
}
