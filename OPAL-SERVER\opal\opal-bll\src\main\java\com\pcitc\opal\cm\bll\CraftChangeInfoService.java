package com.pcitc.opal.cm.bll;

import com.pcitc.opal.cm.bll.entity.CraftChangeInfoEntity;
import com.pcitc.opal.cm.dao.imp.ChangeEventCondition;
import com.pcitc.opal.cm.pojo.CraftChangeInfo;
import com.pcitc.opal.common.CommonResult;
import org.springframework.stereotype.Service;

import java.util.Date;

/*
 * 事件类型对照配置业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmEventTypeCompService
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：事件类型对照配置业务逻辑层接口
 */
@Service
public interface CraftChangeInfoService {

	/**
	 * 根据“企业ID”查询<工艺变更单信息>最大的“发布时间”
	 *
	 * <AUTHOR>
	 * @param comId  企业ID
	 */
	Date getCraftChangeInfoMaxRlsTime(Long  comId) throws Exception;


	/**
	 * 新增工艺变更单信息
	 *
	 * @param craftChangeInfo 工艺变更单信息
	 * @return 返回结果信息类
	 * <AUTHOR> 2018-01-22
	 */
	CommonResult addCraftChangeInfo(CraftChangeInfo craftChangeInfo)throws Exception;

	/**
	 * 根据【变更事件】“装置编码（工艺）”、“位号（工艺）”、“报警标识”、“发生时间”查询<工艺变更单信息>数据
	 *
	 * <AUTHOR>
	 * @param changeEventCondition  变更事件查询工艺变更单条件实体
	 */
	CraftChangeInfoEntity getCraftChangeInfoInfo(ChangeEventCondition changeEventCondition, Integer val) throws Exception;

	/**
	 * 通过id查询对应的变更单信息
	 * @param craftChangeInfoId 变更单id
	 * @return
	 */
	CraftChangeInfo getCraftChangeInfoInfoById(Long craftChangeInfoId);
}
