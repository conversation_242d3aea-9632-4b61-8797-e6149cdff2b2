package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.FactoryDemoRepositoryCustom;
import com.pcitc.opal.pm.pojo.FactoryDemo;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Factory实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_FactoryRepositoryImpl
 * 作       者：dongsheng.zhao
 * 创建时间：2017/09/17
 * 修改编号：1
 * 描       述：Factory实体的Repository实现   
 */
public class FactoryDemoRepositoryImpl extends BaseRepository<FactoryDemo, Long> implements FactoryDemoRepositoryCustom {

	/**
	 * 唯一性校验
	 * <AUTHOR> 2017-09-18
	 * @param factoryEntity 工厂实体
	 * @return 查询返回信息类
	 */
	@Override
	public CommonResult factoryValidation(FactoryDemo factoryEntity) {
		CommonResult commonResult = new CommonResult();
		try {
			// 名称+企业ID 唯一验证
			StringBuilder hql = new StringBuilder(
					"from FactoryDemo t where t.name =:name and t.companyId=:companyId and t.factoryId<>:factoryId");
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("name", factoryEntity.getName());
			paramList.put("companyId", factoryEntity.getCompanyId());
			paramList.put("factoryId", factoryEntity.getFactoryId());

			long index = this.getCount(hql.toString(), paramList);
			if (index > 0) {
				commonResult.setIsSuccess(false);
				commonResult.setMessage("此名称已存在！");
				return commonResult;
			}

			// 简称+企业ID 唯一验证
			hql = new StringBuilder(
					"from FactoryDemo t where t.sname =:sname and t.companyId=:companyId and t.factoryId<>:factoryId");
			paramList = new HashMap<String, Object>();
			paramList.put("sname", factoryEntity.getSname());
			paramList.put("companyId", factoryEntity.getCompanyId());
			paramList.put("factoryId", factoryEntity.getFactoryId());
			index = this.getCount(hql.toString(), paramList);
			if (index > 0) {
				commonResult.setIsSuccess(false);
				commonResult.setMessage("此简称已存在！");
				return commonResult;
			}
		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
			commonResult.setResult(null);
		}
		return commonResult;
	}

	/**
	 * 新增产品
	 * <AUTHOR> 2017-09-11
	 * @param factoryEntity
	 *            添加的实体
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addFactory(FactoryDemo factoryEntity) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			this.getEntityManager().persist(factoryEntity);
			commonResult.setResult(factoryEntity);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	/**
	 * 删除工厂
	 * <AUTHOR> 2017-09-18
	 * @param factoryIds 工厂ID集合
	 * @return 消息结果类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteFactory(Long[] factoryIds) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = "from FactoryDemo t where t.factoryId in (:factoryIds)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			List<Long> factoryIdList = Arrays.asList(factoryIds);
			paramList.put("factoryIds", factoryIdList);

			TypedQuery<FactoryDemo> query = getEntityManager().createQuery(hql, FactoryDemo.class);
			this.setParameterList(query, paramList);
			List<FactoryDemo> factoryDemoList = query.getResultList();
			factoryDemoList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	/**
	 * 更新工厂
	 * <AUTHOR> 2017-09-18
	 * @param factoryEntity 工厂实体
	 * @return 消息结果类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult updateFactory(FactoryDemo factoryEntity) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			getEntityManager().merge(factoryEntity);
			commonResult.setResult(factoryEntity);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	/**
	 * 获取工厂实体
	 * <AUTHOR>  2017-09-18
	 * @param factoryId 工厂ID
	 * @return 工厂实体
	 */
	@Override
	public FactoryDemo getSingleFactory(Long factoryId) {
		return getEntityManager().find(FactoryDemo.class, factoryId);
	}

	/**
	 * 获取工厂实体
	 * <AUTHOR> 2017-09-18
	 * @param factoryIds 工厂ID集合
	 * @return 工厂实体集合
	 */
	@Override
	public List<FactoryDemo> getFactory(Long[] factoryIds) {
		try {
			// 查询字符串
			String hql = "from FactoryDemo t ";
			Map<String, Object> paramList = new HashMap<String, Object>();
			if (factoryIds.length > 0) {
				hql += " where t.factoryId in (:factoryIds)";
				List<Long> factoryIdsList = Arrays.asList(factoryIds);
				paramList.put("factoryIds", factoryIdsList);
			}
			TypedQuery<FactoryDemo> query = getEntityManager().createQuery(hql, FactoryDemo.class);
			this.setParameterList(query, paramList);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 获取工厂实体（分页）
	 * <AUTHOR> 2017-09-18
	 * @param name  名称
	 * @param stdCode 标准码
	 * @param inUse 是否启用
	 * @param page 分页参数
	 * @return 工厂实体（分页）
	 */
	@Override
	public PaginationBean<FactoryDemo> getFactory(String name, String stdCode, Integer inUse, Pagination page) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("from FactoryDemo t where 1=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			// 名称/简称
			if (!StringUtils.isEmpty(name)) {
				hql.append("  and (t.name like :name)");
				paramList.put("name", "%" + this.sqlLikeReplace(name) + "%");
			}
			// 调用基类方法查询返回结果
			return this.findAll(page, hql.toString(), paramList);
		} catch (Exception ex) {
			throw ex;
		}

	}

}
