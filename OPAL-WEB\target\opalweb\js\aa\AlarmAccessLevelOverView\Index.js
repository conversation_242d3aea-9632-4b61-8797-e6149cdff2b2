var searchUrl = OPAL.API.aaUrl + '/alarmPriorityAssess';
var getAlarmAssessFirstPageUrl = OPAL.API.aaUrl + '/alarmAssessFirstPages/getAlarmAssessFirstPage';
var getAlarmStatisticsUrl = OPAL.API.aaUrl + '/alarmAssessFirstPages/getAlarmStatistics';
var getQueryTimeUrl = OPAL.API.commUrl + '/getQueryTime';
var alarmLevelUrl = OPAL.API.aaUrl + '/alarmAssessFirstPages/getAssessLevel';
var dateTimeList = '';
var unitIdList = '';
var alarmTime = '';
var alarmNumberCharts, avgAlarmRateCharts, peakAlarmRateCharts, excitationRateCharts;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            var flagArray = page.logic.setFlag();
            this.bindUi();
            // 初始化 报警时间的时间点
            page.logic.getQueryTime();
            //设置时间插件
            page.logic.initTime();
            // 报警评估等级
            page.logic.initAlarmLevel();
            // 跳转评估详情页
            page.logic.jumpPage();
            // 默认查当前时间的数据
            page.logic.searchPage();
        },
        bindUi: function () {
            //查询
            // $('#search').click(function () {
            //     var nowDate = new Date(page.logic.getCurrentTime())
            //     var brforeDate = new Date(page.logic.getCurrentTime())
            //     brforeDate.setDate(nowDate.getDate() - 6);
            //     $("#NowTimeSpan").html(OPAL.util.dateFormat(brforeDate, "yyyy-MM-dd") + "至" + OPAL.util.dateFormat(nowDate, "yyyy-MM-dd"));
            //     page.logic.searchPage();
            //     page.logic.initAlarmLevel();
            // })
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                // alarmNumberCharts.resize();
                // avgAlarmRateCharts.resize();
                // peakAlarmRateCharts.resize();
                // excitationRateCharts.resize();
            };
        },
        logic: {
            /**
             * 转换 new Date() 日期格式转换为 yyyy-MM-dd HH:mm:ss 格式
             */
            getCurrentTime: function () {
                alarmTime = $('#alarmTime').val();
                alarmDate = $('#alarmTime').val();
                if (alarmTime == '' || alarmTime == null || alarmTime == undefined) {
                    OPAL.util.getShowTime(function (data) {
                        alarmDate = OPAL.util.dateFormat(OPAL.util.strToDate(data[1]["value"]), "yyyy-MM-dd");
                        alarmTime = data[1]["value"];
                    })
                    return alarmTime;
                } else {
                    return alarmTime + " " + OPAL.util.dateFormat(new Date(), "HH:mm:ss");
                }
            },
            /**
             * 初始化页面
             */
            initPage: function (flagArray) {
                $.ajax({
                    url: getAlarmAssessFirstPageUrl,
                    async: true,
                    data: {
                        dateTime: page.logic.getCurrentTime()
                    },
                    dataType: "JSON",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        // $("#aveCounts").html(JSON.parse(dataArr[0].assessLevelEntity).avgAlarmNum);
                        // $("#totalCounts").html(JSON.parse(dataArr[0].assessLevelEntity).totalAlarmNum);
                        dateTimeList = dataArr[0].dateTimeList;
                        unitIdList = dataArr[0].unitIdList;
                        assessLevelData = JSON.parse(dataArr[0].assessLevelEntity);
                        // gridViewDataList = JSON.parse(dataArr[0].gridViewEntityList);
                        // variationTrendDataList = JSON.parse(dataArr[0].variationTrendEntityList);
                        // variationTrendDateArr = JSON.parse(dataArr[0].variationTrendDate);
                        page.logic.initAssessLevel(assessLevelData);
                        // page.logic.initTable(gridViewDataList);
                        // page.logic.initCharts(variationTrendDataList, variationTrendDateArr);
                        flagArray[0] = true;
                        if (flagArray[1]) {
                            $('#search').attr('disabled', false);
                        }
                        //注册左侧统计值点击跳转
                        page.logic.alarmAnalysisJumpLeft();
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            /**
             * 初始化 评估定级 数据
             */
            initAssessLevel: function (dataObj) {
                $('#assessLevel5').html((dataObj.level5 == null) ? 0 : (dataObj.level5));
                $('#assessLevel4').html((dataObj.level4 == null) ? 0 : (dataObj.level4));
                $('#assessLevel3').html((dataObj.level3 == null) ? 0 : (dataObj.level3));
                $('#assessLevel2').html((dataObj.level2 == null) ? 0 : (dataObj.level2));
                $('#assessLevel1').html((dataObj.level1 == null) ? 0 : (dataObj.level1));
            },
            /**
             * 初始化表格
             */
            /** 
            initTable: function (dataArr) {
                var _html = '';
                $.each(dataArr, function (i, el) {
                    var avgAlarmContrastImg = page.logic.getStateComparisonEnum(el['avgAlarmContrast']);
                    var peakAlarmContrastImg = page.logic.getStateComparisonEnum(el['peakAlarmContrast']);
                    var excitationContrastImg = page.logic.getStateComparisonEnum(el['excitationContrast']);
                    _html +=
                        '<tr>' +
                        '<td><a href="javascript:;" style="text-decoration: underline;color: #348fe2" class="alarmAnalysisJumpLeft" dataId="' + el['unitId'] + '">' + el['unitName'] + '</a></td>' +
                        '<td class="right-post">' + el['avgAlarmRate'] + '<img src="../../../images/' + avgAlarmContrastImg + '.png" class="alarm-level-image-box"></td>' +
                        '<td class="right-post">' + el['peakAlarmRate'] + '<img src="../../../images/' + peakAlarmContrastImg + '.png" class="alarm-level-image-box"></td>' +
                        '<td class="right-post">' + el['excitationRate'] + '<img src="../../../images/' + excitationContrastImg + '.png" class="alarm-level-image-box"></td>' +
                        '<td class="center-list">' +
                        '<a class="alarm-level-style assessLevelColor' + el['assessLevel'] + '">' + el['assessLevelName'] + '</a>' +
                        '</td>' +
                        '</tr>';
                });
                $('#table tbody').html(_html)
            },
            */

            /**
             * 判断 上升下降率
             */
            getStateComparisonEnum: function (contrast) {
                switch (contrast) {
                    case 1:
                        return 'uprat'
                        break;
                    case 2:
                        return 'down'
                        break;
                    case 3:
                        return 'rope'
                        break;
                };
            },
            /**
             * 拼接 页面统计值 部分
             */
            /** 
            setAlarmStatisticsData: function (list) {
                var html = '';
                $.each(list, function (i, el) {
                    html +=
                        '<div class="col-sm-4" style="padding: 0 10px;">' +
                        '<span class="alarm-assess-right-span"></span>' +
                        '<p class="alarm-assess-right-p"><a href="javascript:;" style="text-decoration: underline;color: #348fe2" class="alarmAnalysisJumpRight" dataId="' + el['unitId'] + '">' + el['unitName'] + '</a></p>' +
                        '<p class="alarm-assess-right-p1 oppt' + (i + 1) + '">' + el['value'] + '</p>' +
                        '</div>'
                });
                return html;
            },
            */

            /**
             * 设置日期插
             */
            initTime: function () {
                OPAL.ui.initDateTimePicker({
                    "ctrId": "alarmTime"
                })
            },
            /**
             * 初始化图表
             */
            /** 
            initCharts: function (data, xAxisArray) {
                if (alarmNumberCharts && !alarmNumberCharts.isDisposed()) {
                    alarmNumberCharts.clear();
                    alarmNumberCharts.dispose();
                }
                if (avgAlarmRateCharts && !avgAlarmRateCharts.isDisposed()) {
                    avgAlarmRateCharts.clear();
                    avgAlarmRateCharts.dispose();
                }
                if (peakAlarmRateCharts && !peakAlarmRateCharts.isDisposed()) {
                    peakAlarmRateCharts.clear();
                    peakAlarmRateCharts.dispose();
                }
                if (excitationRateCharts && !excitationRateCharts.isDisposed()) {
                    excitationRateCharts.clear();
                    excitationRateCharts.dispose();
                }
                if (data[0] == undefined || data[0] == null || data[0].length == 0) {
                    alarmNumberCharts = OPAL.ui.chart.initEmptyChart('Alarmverification');
                    avgAlarmRateCharts = OPAL.ui.chart.initEmptyChart('Alarmverif');
                    peakAlarmRateCharts = OPAL.ui.chart.initEmptyChart('Alarmver');
                    excitationRateCharts = OPAL.ui.chart.initEmptyChart('Alarmvermust');
                    return;
                }
                var alarmNumberArray = data[0];
                var avgAlarmRateArray = data[1];
                var peakAlarmRateArray = data[2];
                var excitationRateArray = data[3];
                var legendArray = new Array;
                var alarmRate = new Array;
                var alarmNumberRateArr = new Array;
                var avgAlarmRateRateArr = new Array;
                var peakAlarmRateRateArr = new Array;
                var excitationRateRateArr = new Array;
                var alarmDateArray = new Array;
                // 给图例赋值 和 获得x坐标的日期数组
                $.each(alarmNumberArray, function (i, el) {
                    legendArray.push(el.unitName);
                    alarmDateArray = el.alarmDate;
                })
                page.logic.setRateArr(alarmNumberArray, alarmNumberRateArr);
                page.logic.setRateArr(avgAlarmRateArray, avgAlarmRateRateArr);
                page.logic.setRateArr(peakAlarmRateArray, peakAlarmRateRateArr);
                page.logic.setRateArr(excitationRateArray, excitationRateRateArr);
                alarmNumberCharts = echarts.init(document.getElementById('Alarmverification'));
                avgAlarmRateCharts = echarts.init(document.getElementById('Alarmverif'));
                peakAlarmRateCharts = echarts.init(document.getElementById('Alarmver'));
                excitationRateCharts = echarts.init(document.getElementById('Alarmvermust'));
                var option = page.logic.setOptions(legendArray, xAxisArray);
                alarmNumberCharts.setOption(option);
                avgAlarmRateCharts.setOption(option);
                peakAlarmRateCharts.setOption(option);
                excitationRateCharts.setOption(option);
                page.logic.changeOption(alarmNumberCharts, legendArray, alarmNumberRateArr, '报警数', xAxisArray);
                page.logic.changeOption(avgAlarmRateCharts, legendArray, avgAlarmRateRateArr, '平均报警率', xAxisArray);
                page.logic.changeOption(peakAlarmRateCharts, legendArray, peakAlarmRateRateArr, '峰值报警率', xAxisArray);
                page.logic.changeOption(excitationRateCharts, legendArray, excitationRateRateArr, '扰动率', xAxisArray);
            },
            */

            /**
             * 设置图表的配置
             *
             */
            /** 
            setOptions: function (legendArray, xAxisArray) {
                var option = {
                    title: {
                        text: '',
                        left: 'center',
                    },
                    tooltip: {
                        trigger: 'item',
                    },
                    legend: {
                        data: legendArray,
                        padding: 15, // 图例内边距，单位px，默认各方向内边距为5，
                        // 接受数组分别设定上右下左边距，同css
                        right: 34,
                    },
                    toolbox: {
                        show: false,
                        feature: {
                            magicType: {
                                show: true,
                            },
                        }
                    },
                    xAxis: {
                        type: 'category', //类目轴
                        boundaryGap: false,
                        data: xAxisArray,
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#ebf2f4']
                            }
                        },
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#333',
                                width: 1, //这里是为了突出显示加上的  
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#333', //改变x轴字体颜色
                            },
                        },

                    },
                    yAxis: {
                        type: 'value',
                        splitLine: {
                            lineStyle: {
                                color: ['#ebf2f4'],
                            }
                        },
                        axisTick: { //坐标轴 刻度显示
                            show: false,
                        },
                        axisLine: {
                            // show: false,
                            lineStyle: {
                                color: '#333',
                                width: 1,
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#333', //改变y轴字体颜色
                            },
                        },
                    },
                    grid: {
                        top: '40px',
                        left: '45px',
                        right: '35px',
                        height: '300px'
                    },
                    series: [{
                        name: legendArray[0],
                        type: 'line',
                        smooth: true,
                        radius: '95%',
                        max: 100,
                        itemStyle: {
                            normal: {
                                color: '#5BA5E8',
                            },
                        },
                        lineStyle: {
                            normal: {
                                color: '#5BA5E8',
                                width: 2,
                            },
                        }

                    }, {
                        name: legendArray[1],
                        type: 'line',
                        smooth: true,
                        radius: '95%',
                        itemStyle: {
                            normal: {
                                color: '#5de433',
                                width: 5,
                            },
                        },
                        lineStyle: {
                            normal: {
                                color: '#5de433',
                                width: 2,
                            },
                        },
                    }, {
                        name: legendArray[2],
                        type: 'line',
                        smooth: true,
                        radius: '95%',
                        max: 100,
                        itemStyle: {
                            normal: {
                                color: '#FF5B57',
                            },
                        },
                        lineStyle: {
                            normal: {
                                color: '#FF5B57',
                                width: 2,
                            },
                        },
                    }, {
                        name: legendArray[3],
                        type: 'line',
                        smooth: true,
                        radius: '95%',
                        max: 100,
                        itemStyle: {
                            normal: {
                                color: '#F7AE43',
                            },
                        },
                        lineStyle: {
                            normal: {
                                color: '#F7AE43',
                                width: 2,
                            },
                        },
                    }]
                };
                return option;
            },
            changeOption: function (charts, legendArray, dataArr, promptData, xAxisArray) {
                charts.setOption({
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            var endTime = JSON.parse(dateTimeList)[1].value;
                            var endTimeValue = moment(endTime).format("YYYY-MM-DD HH:mm:ss");
                            var lastTime = xAxisArray[(xAxisArray.length - 1)];
                            var name = params[0].name.replace(/\//g, "-");
                            var d1 = Date.parse(new Date(params[0].name));
                            var d2 = d1 + (60 * 60 * 24 * 1000);
                            var d3 = moment(d2).format("YYYY-MM-DD");

                            if (params[0].name != lastTime) {
                                var timeData1 = '从： ' + name + ' ' + queryTimeValue + ' 至：' + d3 + ' ' + queryTimeValue
                            } else {
                                var timeData1 = '从： ' + name + ' ' + queryTimeValue + ' 至：' + endTimeValue
                            }
                            if (promptData == '扰动率') {
                                if (params[1] == null || params[1] == undefined || params[1] == '') {
                                    var timeData2 = '<br> <span style="display:inline-block;width:9px;height:9px;background:#5BA5E8;border-radius: 50%"></span> ' + params[0].seriesName + '：' + params[0].value + '%'
                                } else if (params[2] == null || params[2] == undefined || params[2] == '') {
                                    var timeData2 = '<br> <span style="display:inline-block;width:9px;height:9px;background:#5BA5E8;border-radius: 50%"></span> ' + params[0].seriesName + '：' + params[0].value +
                                        '%<br> <span style="display:inline-block;width:9px;height:9px;background:#5de433;border-radius: 50%"></span> ' + params[1].seriesName + '：' + params[1].value + '%'
                                } else if (params[3] == null || params[3] == undefined || params[3] == '') {
                                    var timeData2 = '<br> <span style="display:inline-block;width:9px;height:9px;background:#5BA5E8;border-radius: 50%"></span> ' + params[0].seriesName + '：' + params[0].value +
                                        '%<br> <span style="display:inline-block;width:9px;height:9px;background:#5de433;border-radius: 50%"></span> ' + params[1].seriesName + '：' + params[1].value +
                                        '%<br> <span style="display:inline-block;width:9px;height:9px;background:#FF5B57;border-radius: 50%"></span> ' + params[2].seriesName + '：' + params[2].value + '%'
                                } else {
                                    var timeData2 = '<br> <span style="display:inline-block;width:9px;height:9px;background:#5BA5E8;border-radius: 50%"></span> ' + params[0].seriesName + '：' + params[0].value +
                                        '%<br> <span style="display:inline-block;width:9px;height:9px;background:#5de433;border-radius: 50%"></span> ' + params[1].seriesName + '：' + params[1].value +
                                        '%<br> <span style="display:inline-block;width:9px;height:9px;background:#FF5B57;border-radius: 50%"></span> ' + params[2].seriesName + '：' + params[2].value +
                                        '%<br> <span style="display:inline-block;width:9px;height:9px;background:#F7AE43;border-radius: 50%"></span> ' + params[3].seriesName + '：' + params[3].value + '%'
                                }
                            } else {
                                if (params[1] == null || params[1] == undefined || params[1] == '') {
                                    var timeData2 = '<br> <span style="display:inline-block;width:9px;height:9px;background:#5BA5E8;border-radius: 50%"></span> ' + params[0].seriesName + '：' + params[0].value
                                } else if (params[2] == null || params[2] == undefined || params[2] == '') {
                                    var timeData2 = '<br> <span style="display:inline-block;width:9px;height:9px;background:#5BA5E8;border-radius: 50%"></span> ' + params[0].seriesName + '：' + params[0].value +
                                        '<br> <span style="display:inline-block;width:9px;height:9px;background:#5de433;border-radius: 50%"></span> ' + params[1].seriesName + '：' + params[1].value
                                } else if (params[3] == null || params[3] == undefined || params[3] == '') {
                                    var timeData2 = '<br> <span style="display:inline-block;width:9px;height:9px;background:#5BA5E8;border-radius: 50%"></span> ' + params[0].seriesName + '：' + params[0].value +
                                        '<br> <span style="display:inline-block;width:9px;height:9px;background:#5de433;border-radius: 50%"></span> ' + params[1].seriesName + '：' + params[1].value +
                                        '<br> <span style="display:inline-block;width:9px;height:9px;background:#FF5B57;border-radius: 50%"></span> ' + params[2].seriesName + '：' + params[2].value
                                } else {
                                    var timeData2 = '<br> <span style="display:inline-block;width:9px;height:9px;background:#5BA5E8;border-radius: 50%"></span> ' + params[0].seriesName + '：' + params[0].value +
                                        '<br> <span style="display:inline-block;width:9px;height:9px;background:#5de433;border-radius: 50%"></span> ' + params[1].seriesName + '：' + params[1].value +
                                        '<br> <span style="display:inline-block;width:9px;height:9px;background:#FF5B57;border-radius: 50%"></span> ' + params[2].seriesName + '：' + params[2].value +
                                        '<br> <span style="display:inline-block;width:9px;height:9px;background:#F7AE43;border-radius: 50%"></span> ' + params[3].seriesName + '：' + params[3].value
                                }
                            }

                            str = timeData1 + timeData2
                            return str;
                        }
                    },
                    series: [{
                        name: legendArray[0],
                        data: dataArr[0]
                    }, {
                        name: legendArray[1],
                        data: dataArr[1]
                    }, {
                        name: legendArray[2],
                        data: dataArr[2]
                    }, {
                        name: legendArray[3],
                        data: dataArr[3]
                    }]
                })
            },
            setRateArr: function (arr1, arr2) {
                $.each(arr1, function (i, el) {
                    arr2.push(el.alarmRate);
                })
            },
            */

            /**
             * 初始化统计值
             */
            initStatistics: function (flagArray) {
                $.ajax({
                    url: getAlarmStatisticsUrl,
                    async: true,
                    data: {
                        queryTime: page.logic.getCurrentTime()
                    },
                    dataType: "JSON",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        $.each(dataArr, function (i, el) {
                            var jsonArray = $.parseJSON(el['unitList']);
                            if (el['name'] == "alarmDisturbance") {
                                $('#alarmDisturbance').html(page.logic.setAlarmStatisticsData(jsonArray));
                            }
                            if (el['name'] == "alarmConfigure") {
                                $('#alarmConfigure').html(page.logic.setAlarmStatisticsData(jsonArray));
                            }
                            if (el['name'] == "alarmHold") {
                                $('#alarmHold').html(page.logic.setAlarmStatisticsData(jsonArray));
                            }
                            if (el['name'] == "alarmRecovery") {
                                $('#alarmRecovery').html(page.logic.setAlarmStatisticsData(jsonArray));
                            }
                        })
                        flagArray[1] = true;
                        if (flagArray[0]) {
                            $('#search').attr('disabled', false);
                        }
                        //注册跳转事件
                        page.logic.alarmAnalysisJumpRight();
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            /**
             * 获得固定的时间点
             */
            getQueryTime: function () {
                $.ajax({
                    url: getQueryTimeUrl,
                    async: false,
                    data: '',
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        queryTimeValue = res[0].value;
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            // 报警评估等级
            initAlarmLevel: function () {
                $.ajax({
                    url: alarmLevelUrl,
                    type: 'GET',
                    data: {
                        isAll: true
                    },
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        var indexs = 0;
                        for (var i = res.length - 1; i > 0; i--) {
                            $(".alarmAssGrade").eq(indexs).html(res[i].value);
                            indexs++;
                        }
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },

            /**
             * 设置标志位
             */
            setFlag: function () {
                var flagArray = new Array;
                flagArray[0] = false;
                flagArray[1] = false;
                return flagArray;
            },
            /**
             * 点击跳转 评估详情页 时 传递数据
             */
            jumpPage: function () {
                $('.clickJump').click(function () {
                    var assessLevel = $(this).attr('dataId');
                    layer.open({
                        type: 2,
                        title: '',
                        closeBtn: false,
                        area: ['95%', '95%'],
                        shadeClose: false,
                        scrollbar: false,
                        content: '../AlarmLevelAssessOverViewDetailPage/Index.html?' + Math.random(),
                        success: function (layero, index) {
                            var body = layer.getChildFrame('body', index);
                            var iframeWin = window[layero.find('iframe')[0]['name']];
                            var data = {
                                dateTimeList: dateTimeList,
                                unitIdList: unitIdList,
                                assessLevel: assessLevel,
                                alarmTime: alarmTime
                            };
                            iframeWin.page.logic.setData(data);
                        }
                    })
                })
            },
            /**
             * 左侧统计数据点击跳转
             */
            alarmAnalysisJumpLeft: function () {
                $('.alarmAnalysisJumpLeft').click(function () {
                    var unitId = $(this).attr('dataId');
                    var unitName = $(this).text();
                    layer.open({
                        type: 2,
                        title: '',
                        closeBtn: false,
                        area: ['97%', '97%'],
                        shadeClose: false,
                        scrollbar: false,
                        content: '../../af/AlarmAnalysis/Index.html?' + Math.random(),
                        success: function (layero, index) {
                            var body = layer.getChildFrame('body', index);
                            var iframeWin = window[layero.find('iframe')[0]['name']];
                            var data = {
                                dateTimeList: dateTimeList,
                                unitIdList: unitIdList,
                                unitId: unitId,
                                unitName: unitName,
                                alarmTime: alarmDate
                            };
                            iframeWin.page.logic.setData(data);
                        }

                    })
                })
            },
            /**
             * 右侧统计数据点击跳转
             */
            alarmAnalysisJumpRight: function () {
                $('.alarmAnalysisJumpRight').click(function () {
                    var unitId = $(this).attr('dataId');
                    var unitName = $(this).text();
                    layer.open({
                        type: 2,
                        title: '',
                        closeBtn: false,
                        area: ['97%', '97%'],
                        shadeClose: false,
                        scrollbar: false,
                        content: '../../af/AlarmAnalysis/Index.html?' + Math.random(),
                        success: function (layero, index) {
                            var body = layer.getChildFrame('body', index);
                            var iframeWin = window[layero.find('iframe')[0]['name']];
                            var data = {
                                dateTimeList: dateTimeList,
                                unitIdList: unitIdList,
                                unitId: unitId,
                                unitName: unitName,
                                alarmTime: alarmDate
                            };
                            iframeWin.page.logic.setData(data);
                        }

                    })
                })
            },
            searchPage: function () {
                var flagArray = page.logic.setFlag();
                // $('#search').attr('disabled', true);
                page.logic.initPage(flagArray);
                // page.logic.initStatistics(flagArray);
            }
        }
    };
    page.init();
    window.page = page;
})



