package com.pcitc.opal.pm.dao;

import com.pcitc.opal.pm.pojo.CraftAlarmRateConf;
import org.springframework.data.jpa.repository.JpaRepository;


/*
 *CraftAlarmRateConf实体的Repository的JPA标准接口
 * 模块编号：pcitc_opal_dal_interface_CraftAlarmRateConfRepository
 * 作       者：shufei.sui
 * 创建时间：2019/12/11
 * 修改编号：1
 * 描       述：CraftAlarmRateConf实体的Repository实现
 */
public interface CraftAlarmRateConfRepository extends JpaRepository<CraftAlarmRateConf,Long>, CraftAlarmRateConfRepositoryCustom {
}
