package com.pcitc.opal.ac.bll.entity;

import java.util.Date;

/*
 * 报警点视图实体
 * 模块编号：pcitc_pojo_class_AlarmPointViewEntity
 * 作    者：dageng.sun
 * 创建时间：2018/01/26
 * 修改编号：1
 * 描    述：报警点视图实体
 */
public class AlarmPointViewEntity{
    
	
	/**
     * ID
     */
    private Long id;

    /**
     * 报警点ID
     */
    private Long alarmPointId;

    /**
     * 生产单元ID
     */
    private Long prdtCellId;

    /**
     * 位号
     */
    private String tag;

    /**
     * 位置
     */
    private String location;

    /**
     * 报警标识
     */
    private Long alarmFlagId;

    /**
     * 状态
     */
    private Integer status;
    
    /**
     * 生产单元名称
     */
    private String prdtCellName;
    
    /**
     * 报警标识名称
     */
    private String alarmFlagName;
    
    /**
	 * 开始时间
	 */
	private Date startTime;
	
	/**
	 * 结束时间
	 */
	private Date endTime;
	
	/**
	 * 是否可以变更
	 */
	private boolean changeStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    public Long getPrdtCellId() {
        return prdtCellId;
    }

    public void setPrdtCellId(Long prdtCellId) {
        this.prdtCellId = prdtCellId;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

	public String getPrdtCellName() {
		return prdtCellName;
	}

	public void setPrdtCellName(String prdtCellName) {
		this.prdtCellName = prdtCellName;
	}

	public String getAlarmFlagName() {
		return alarmFlagName;
	}

	public void setAlarmFlagName(String alarmFlagName) {
		this.alarmFlagName = alarmFlagName;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public boolean getChangeStatus() {
		return changeStatus;
	}

	public void setChangeStatus(boolean changeStatus) {
		this.changeStatus = changeStatus;
	}

}
