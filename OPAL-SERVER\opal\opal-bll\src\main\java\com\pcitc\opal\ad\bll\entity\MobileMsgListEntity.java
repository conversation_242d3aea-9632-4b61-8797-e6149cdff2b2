package com.pcitc.opal.ad.bll.entity;

import java.util.Date;

/*
 * 报警短信记录
 * 模块编号：pcitc_pojo_class_MobileMsgListEntity
 * 作       者：shufei.sui
 * 创建时间：2020/11/25
 * 修改编号：1
 * 描       述：报警短信记录实体
 */
public class MobileMsgListEntity {

    /**
     * 装置编码
     */
    private String unitCode;
    /**
     * 装置名称
     */
    private String unitName;

    /**
     * 生产单元ID
     */
    private Integer prdtCellId;
    /**
     * 生产单元名称
     */
    private String prdtcellName;

    /**
     * 位号
     */
    private String tag;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;
    /**
     * 报警标识名称
     */
    private String alarmFlagName;

    /**
     * 报警时间
     */
    private Date alarmTime;

    /**
     * 接收人
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 状态（1已发送；2发送失败；）
     */
    private Integer status;

    private String statusShow;

    /**
     * 返回结果
     */
    private String result;

    /**
     * 发送时间
     */
    private Date sendTime;

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getPrdtCellId() {
        return prdtCellId;
    }

    public void setPrdtCellId(Integer prdtCellId) {
        this.prdtCellId = prdtCellId;
    }

    public String getPrdtcellName() {
        return prdtcellName;
    }

    public void setPrdtcellName(String prdtcellName) {
        this.prdtcellName = prdtcellName;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusShow() {
        return statusShow;
    }

    public void setStatusShow(String statusShow) {
        this.statusShow = statusShow;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }
}
