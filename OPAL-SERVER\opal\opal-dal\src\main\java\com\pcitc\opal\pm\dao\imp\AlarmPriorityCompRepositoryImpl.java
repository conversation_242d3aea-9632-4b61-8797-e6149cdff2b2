package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmPriorityCompRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmPriorityComp;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * AlarmPriorityComp实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_AlarmPriorityCompRepositoryImpl
 * 作    者：zheng.yang
 * 创建时间：2018/03/30
 * 修改编号：1
 * 描    述：AlarmPriorityComp实体的Repository实现
 */
public class AlarmPriorityCompRepositoryImpl extends BaseRepository<AlarmPriorityComp,Long> implements AlarmPriorityCompRepositoryCustom{

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * 新增数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityComp 报警标识对照实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addPriorityComp(AlarmPriorityComp alarmPriorityComp) throws Exception {
        CommonResult commonResult = new CommonResult();
        this.getEntityManager().persist(alarmPriorityComp);
        try {
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功");
            commonResult.setResult(alarmPriorityComp);
        } catch (Exception e) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(e.getMessage());
        }
        return commonResult;
    }

    /**
     * 删除数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompIds 报警标识对照主键Id集合
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult deleteAlarmPriorityComp(Long[] alarmPriorityCompIds) throws Exception {
        CommonResult commonResult = new CommonResult();
        try {
            String hql = "from AlarmPriorityComp t where t.alarmPriorityCompId in (:alarmPriorityCompIds)";
            TypedQuery<AlarmPriorityComp> query = getEntityManager().createQuery(hql, AlarmPriorityComp.class);
            query.setParameter("alarmPriorityCompIds",Arrays.asList(alarmPriorityCompIds));
            List<AlarmPriorityComp> alarmPriorityCompList = query.getResultList();
            alarmPriorityCompList.forEach(x->{
                this.getEntityManager().remove(x);
            });
            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception e) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(e.getMessage());
        }
        return commonResult;
    }

    /**
     * 更新数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityComp 报警标识对照实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult updateAlarmPriorityComp(AlarmPriorityComp alarmPriorityComp) throws Exception {
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(alarmPriorityComp);
            commonResult.setResult(alarmPriorityComp);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    /**
     * 获取单条数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompId 报警标识对照ID
     * @return AlarmPriorityCompEntity 报警标识对照实体类
     * @throws Exception
     */
    @Override
    public AlarmPriorityComp getSingleAlarmPriorityComp(Long alarmPriorityCompId) throws Exception {
        try {
            String hql = "from AlarmPriorityComp apc left join fetch apc.dcsCode where apc.alarmPriorityCompId =:alarmPriorityCompId ";
            TypedQuery<AlarmPriorityComp> query = this.getEntityManager().createQuery(hql, AlarmPriorityComp.class);
            query.setParameter("alarmPriorityCompId", alarmPriorityCompId);
            return query.getSingleResult();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 获取多条数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompIds 报警标识对照ID集合
     * @return List<AlarmPriorityComp> 报警标识对照实体类集合
     * @throws Exception
     */
    @Override
    public List<AlarmPriorityComp> getAlarmPriorityCompList(Long[] alarmPriorityCompIds) throws Exception {
        String hql = "from AlarmPriorityComp apc where apc.alarmPriorityCompId in(:alarmPriorityCompIds)";
        TypedQuery<AlarmPriorityComp> query = this.getEntityManager().createQuery(hql, AlarmPriorityComp.class);
        query.setParameter("alarmPriorityCompIds", Arrays.asList(alarmPriorityCompIds));
        return query.getResultList();
    }

    /**
     * 获取分页数据
     *
      * <AUTHOR> 2018-03-30
     * @param dcsCodeId DCS编码ID
     * @param prioritySource 源报警优先级
     * @param priority    报警优先级ID
     * @param inUse 是否启用
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmPriorityCompEntity> 翻页对象
     */
    @Override
    public PaginationBean<AlarmPriorityComp> getAlarmPriorityComp(Long dcsCodeId, String prioritySource, Integer priority, Integer inUse, Pagination page) throws Exception {
        try {
            StringBuilder hql =new StringBuilder("from AlarmPriorityComp apc left join fetch apc.dcsCode dcs where 1=1");
            Map<String, Object> paramList = new HashMap<String, Object>();
            if(!StringUtils.isEmpty(dcsCodeId)){
                hql.append(" and apc.dcsCodeId=:dcsCodeId");
                paramList.put("dcsCodeId", dcsCodeId);
            }
            if(!StringUtils.isEmpty(prioritySource)) {
                hql.append(" and upper(apc.prioritySource) like upper(:prioritySource) escape '/'");
                paramList.put("prioritySource", "%" + sqlLikeReplace(prioritySource) + "%");
            }
            if(priority != null) {
                hql.append(" and apc.priority=:priority");
                paramList.put("priority", priority);
            }
            if(inUse != null) {
                hql.append(" and apc.inUse=:inUse");
                paramList.put("inUse", inUse);
            }
            hql.append(" order by dcs.name,apc.prioritySource");
            PaginationBean<AlarmPriorityComp> bean = this.findAll(page, hql.toString(), paramList);
            return bean;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 校验数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityComp 报警优先级对照实体
     * @return CommonResult 消息结果类
     */
    @Override
    public CommonResult alarmPriorityCompValidation(AlarmPriorityComp alarmPriorityComp) {
        CommonResult commonResult = new CommonResult();
        try {
            String hql = "from AlarmPriorityComp apc where apc.prioritySource=:prioritySource and apc.dcsCodeId=:name and" +
                    " apc.alarmPriorityCompId<>:alarmPriorityCompId";
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("prioritySource",alarmPriorityComp.getPrioritySource());
            paramList.put("alarmPriorityCompId", alarmPriorityComp.getAlarmPriorityCompId() == null?0:alarmPriorityComp.getAlarmPriorityCompId());
            paramList.put("name", alarmPriorityComp.getDcsCodeId());
            long count = this.getCount(hql, paramList);
            if(count > 0){
                commonResult.setMessage("该DCS下源报警优先级已存在！");
                commonResult.setIsSuccess(false);
                return commonResult;
            }
        } catch (Exception e) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(e.getMessage());
            commonResult.setResult(null);
        }
        return commonResult;
    }
}
