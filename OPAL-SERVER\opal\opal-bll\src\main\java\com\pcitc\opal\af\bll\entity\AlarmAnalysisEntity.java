package com.pcitc.opal.af.bll.entity;
import com.pcitc.opal.common.bll.entity.AlarmEventExEntity;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import java.util.List;
/*
 * 报警分析实体
 * 模块编号：pcitc_opal_bll_class_AlarmAnalysisEntity
 * 作  　者：xuelei.wang
 * 创建时间：2017-10-31
 * 修改编号：1
 * 描    述：报警分析实体
 */
public class AlarmAnalysisEntity {
    /**
     * 报警优先级国际标准列表
     */
    private List<DictionaryEntity> isoPriority;
    /**
     * 报警优先级系统现状
     */
    private List<DictionaryEntity> systemPriority;
    /**
     * 最多报警TOP20
     */
    private List<AlarmEventExEntity> mostAlarm;
    /**
     * 最多操作TOP20
     */
    private List<AlarmEventExEntity> mostAlarmOperate;
    /**
     * 报警总数
     */
    private Long alarmCount;
    /**
     * 操作总数
     */
    private Long operateCount;

    public List<DictionaryEntity> getIsoPriority() {
        return isoPriority;
    }

    public void setIsoPriority(List<DictionaryEntity> isoPriority) {
        this.isoPriority = isoPriority;
    }

    public List<DictionaryEntity> getSystemPriority() {
        return systemPriority;
    }

    public void setSystemPriority(List<DictionaryEntity> systemPriority) {
        this.systemPriority = systemPriority;
    }

    public List<AlarmEventExEntity> getMostAlarm() {
        return mostAlarm;
    }

    public void setMostAlarm(List<AlarmEventExEntity> mostAlarm) {
        this.mostAlarm = mostAlarm;
    }

    public List<AlarmEventExEntity> getMostAlarmOperate() {
        return mostAlarmOperate;
    }

    public void setMostAlarmOperate(List<AlarmEventExEntity> mostAlarmOperate) {
        this.mostAlarmOperate = mostAlarmOperate;
    }

    public Long getAlarmCount() {
        return alarmCount;
    }

    public void setAlarmCount(Long alarmCount) {
        this.alarmCount = alarmCount;
    }

    public Long getOperateCount() {
        return operateCount;
    }

    public void setOperateCount(Long operateCount) {
        this.operateCount = operateCount;
    }
}