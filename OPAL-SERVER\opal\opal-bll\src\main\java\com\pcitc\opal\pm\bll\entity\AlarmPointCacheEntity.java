package com.pcitc.opal.pm.bll.entity;

import java.util.Date;

public class AlarmPointCacheEntity {
    /**
     * 报警点ID
     */
    private Long alarmPointId;

    /**
     * 写入时间
     */
    private Date writeTime;

    /**
     * 读取时间
     */
    private Date readTime;

    /**
     * 数据状态（0未读取；1已读取）
     */
    private Integer stateFlag;

    /**
     * OPC编码
     */
    private Long opcCode;

    /**
     * 生产单元
     */
    private String prdtCell;

    /**
     * 位号
     */
    private String tag;

    /**
     * 限值类型
     */
    private String limitType;

    /**
     * 数值
     */
    private Long limitValue;

    /**
     * 变量名称
     */
    private String variableName;

    /**
     * 质量戳
     */
    private String qualityFlag;
    /**
     * 装置编码
     */
    private String unitId;

    private String unitSname;
    /**
     * 生产单元ID
     */
    private Long prdtCellId;
    /**
     * 高高报
     */
    private Double hh;
    /**
     * 高报
     */
    private Double ph;
    /**
     * 低报
     */
    private Double pl;
    /**
     * 低低报
     */
    private Double ll;
    /**
     * 是否虚表
     */
    private String virtualRealityFlagStr;
    /**
     * 报警点类型
     */
    private String  alarmPointTypeStr;
    /**
     * 监测类型（1物料；2能源；3质量）
     */
    private String monitorTypeStr;
    /**
     * 仪表类型（1监测表；2控制表）
     */
    private String instrmtTypeStr;
    /**
     * 是否启用（1是；0否）
     */
    private String inUseStr;
    /**
     * 位置
     */
    private String location;
    /**
     * 计量单位
     */
    private String measUnit;
    /**
     * PID图号
     */
    private String pid;
    /**
     * 排序
     */
    private String sortNum;
    /**
     *描述
     */
    private String des;

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    public Date getWriteTime() {
        return writeTime;
    }

    public void setWriteTime(Date writeTime) {
        this.writeTime = writeTime;
    }

    public Date getReadTime() {
        return readTime;
    }

    public void setReadTime(Date readTime) {
        this.readTime = readTime;
    }

    public Integer getStateFlag() {
        return stateFlag;
    }

    public void setStateFlag(Integer stateFlag) {
        this.stateFlag = stateFlag;
    }

    public Long getOpcCode() {
        return opcCode;
    }

    public void setOpcCode(Long opcCode) {
        this.opcCode = opcCode;
    }

    public String getPrdtCell() {
        return prdtCell;
    }

    public void setPrdtCell(String prdtCell) {
        this.prdtCell = prdtCell;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getLimitType() {
        return limitType;
    }

    public void setLimitType(String limitType) {
        this.limitType = limitType;
    }

    public Long getLimitValue() {
        return limitValue;
    }

    public void setLimitValue(Long limitValue) {
        this.limitValue = limitValue;
    }

    public String getVariableName() {
        return variableName;
    }

    public void setVariableName(String variableName) {
        this.variableName = variableName;
    }

    public String getQualityFlag() {
        return qualityFlag;
    }

    public void setQualityFlag(String qualityFlag) {
        this.qualityFlag = qualityFlag;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public Long getPrdtCellId() {
        return prdtCellId;
    }

    public void setPrdtCellId(Long prdtCellId) {
        this.prdtCellId = prdtCellId;
    }

    public String getUnitSname() {
        return unitSname;
    }

    public void setUnitSname(String unitSname) {
        this.unitSname = unitSname;
    }

    public Double getHh() {
        return hh;
    }

    public void setHh(Double hh) {
        this.hh = hh;
    }

    public Double getPh() {
        return ph;
    }

    public void setPh(Double ph) {
        this.ph = ph;
    }

    public Double getPl() {
        return pl;
    }

    public void setPl(Double pl) {
        this.pl = pl;
    }

    public Double getLl() {
        return ll;
    }

    public void setLl(Double ll) {
        this.ll = ll;
    }

    public String getVirtualRealityFlagStr() {
        return virtualRealityFlagStr;
    }

    public void setVirtualRealityFlagStr(String virtualRealityFlagStr) {
        this.virtualRealityFlagStr = virtualRealityFlagStr;
    }

    public String getAlarmPointTypeStr() {
        return alarmPointTypeStr;
    }

    public void setAlarmPointTypeStr(String alarmPointTypeStr) {
        this.alarmPointTypeStr = alarmPointTypeStr;
    }

    public String getMonitorTypeStr() {
        return monitorTypeStr;
    }

    public void setMonitorTypeStr(String monitorTypeStr) {
        this.monitorTypeStr = monitorTypeStr;
    }

    public String getInstrmtTypeStr() {
        return instrmtTypeStr;
    }

    public void setInstrmtTypeStr(String instrmtTypeStr) {
        this.instrmtTypeStr = instrmtTypeStr;
    }

    public String getInUseStr() {
        return inUseStr;
    }

    public void setInUseStr(String inUseStr) {
        this.inUseStr = inUseStr;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getMeasUnit() {
        return measUnit;
    }

    public void setMeasUnit(String measUnit) {
        this.measUnit = measUnit;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getSortNum() {
        return sortNum;
    }

    public void setSortNum(String sortNum) {
        this.sortNum = sortNum;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
