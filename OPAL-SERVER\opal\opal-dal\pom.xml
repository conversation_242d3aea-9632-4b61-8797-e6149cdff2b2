<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pcitc.opal</groupId>
		<artifactId>opal</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<artifactId>opal-dal</artifactId>
	<packaging>jar</packaging>
	<dependencies>
		<!-- Spring data JPA, default tomcat pool, exclude it -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.tomcat</groupId>
					<artifactId>tomcat-jdbc</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- <dependency> <groupId>mysql</groupId> <artifactId>mysql-connector-java</artifactId> 
			</dependency> -->
		<!-- Oracle JDBC driver -->
		<dependency>
			<groupId>com.github.noraui</groupId>
			<artifactId>ojdbc7</artifactId>
			<version>12.1.0.2</version>
		</dependency>
		<!-- HikariCP connection pool -->
		<dependency>
			<groupId>com.zaxxer</groupId>
			<artifactId>HikariCP</artifactId>
		</dependency>



		<dependency>
			<groupId>com.pcitc.opal</groupId>
			<artifactId>opal-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pcitc.opal</groupId>
			<artifactId>opal-pojo</artifactId>
		</dependency>
	</dependencies>
</project>