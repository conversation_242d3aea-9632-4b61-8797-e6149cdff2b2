var getMostAlarmTopUrl = OPAL.API.commUrl + '/getMostAlarmTop';
var getMostAlarmOperateTopUrl = OPAL.API.commUrl + "/getMostAlarmOperateTop";
var getAlarmPriorityByUnitIdUrl = OPAL.API.commUrl + "/getAlarmPriorityByUnitId";
var getISOAlarmListUrl = OPAL.API.commUrl + "/getISOAlarmList";
var getAlarmAnalysisDataUrl = OPAL.API.afUrl + '/alarmAnalysis/getAlarmAnalysisData';
var getContinuousAlarmListUrl = OPAL.API.afUrl + '/alarmAnalysis/getContinuousAlarmList';
var getShelvedAlarmListUrl = OPAL.API.afUrl + '/alarmAnalysis/getShelvedAlarmList';
var getSuppressedAlarmListUrl = OPAL.API.afUrl + '/alarmAnalysis/getSuppressedAlarmList';
var mostAlarmCharts, mostAlarmOperateCharts;
var ISOAlarmEmergency; //报警优先级国际标准 紧急
var ISOAlarmImportance; // 报警优先级国际标准 重要
var ISOAlarmNormal; // 报警优先级国际标准 一般
var startDate, endDate, startTime, endTime; // 开始日期 结束日期 开始时间 结束时间
var newEndTime;
var unitId, unitName; //  装置id
var AlarmEventTotalCount; // 总报警数
var operateCount; // 总操作数
var dateTimeList; //时间列表
var endFlag;
var topOperateCount=20;
var topCount=20;
$(function () {
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var page = {
        /**
         * 初始化
         */
        init: function () {

            this.bindUi();
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                mostAlarmCharts.resize();
                mostAlarmOperateCharts.resize();
            };
            // 跳转到优先级评估页面
            // page.logic.priorityAssessJump()
            $(window).resize(function () {
                $('#continuousAlarmTable').bootstrapTable('resetView');
                $('#suppressedAlarmTable').bootstrapTable('resetView');
                $('#shelvedAlarmTable').bootstrapTable('resetView');
                $('#MostAlarmTable').bootstrapTable('resetView');
                $('#MostAlarmOperateTable').bootstrapTable('resetView');
            });
            /**
             * Top20和Top10数量切换
             */
            $("#btnTop10").click(function(){
                $(this).removeClass("active").addClass("active").removeClass("btn-select").addClass("btn-select");
                $("#btnTop20").removeClass("active").removeClass("btn-select").removeClass("btn-unselect").addClass("btn-unselect");
                topCount=10;
                page.logic.queryMostAlarm();
            });
            $("#btnTop20").click(function(){
                $(this).removeClass("active").addClass("active").removeClass("btn-select").addClass("btn-select");
                $("#btnTop10").removeClass("active").removeClass("btn-select").removeClass("btn-unselect").addClass("btn-unselect");
                topCount=20;
                page.logic.queryMostAlarm();
            });
            /**
             * Top20和Top10操作切换
             */
            $("#btnTopOperate10").click(function(){
                $(this).removeClass("active").addClass("active").removeClass("btn-select").addClass("btn-select");
                $("#btnTopOperate20").removeClass("active").removeClass("btn-select").removeClass("btn-unselect").addClass("btn-unselect");
                topOperateCount=10;
                page.logic.queryMostOperate();
            });
            $("#btnTopOperate20").click(function(){
                $(this).removeClass("active").addClass("active").removeClass("btn-select").addClass("btn-select");
                $("#btnTopOperate10").removeClass("active").removeClass("btn-select").removeClass("btn-unselect").addClass("btn-unselect");
                topOperateCount=20;
                page.logic.queryMostOperate();
            });
        },
        data: {
            // 设置查询参数
            param: {},
            param1:{top:20}
        },
        bindUi: function () {
            /**
             * 导航切换
             */
            $('.myTab li').click(function () {
                var flag = $(this).attr('showFlag');
                if (flag == 'imgShow') {
                    $(this).find('img').attr('src', '../../../images/one1.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/tweo.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                } else if (flag == 'tableShow') {
                    $(this).find('img').attr('src', '../../../images/tweos.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                } else if (flag == 'unitShow') {
                    $(this).find('img').attr('src', '../../../images/treese.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/tweo.png');
                }
            });
            // 关闭
            $('#closePage').click(function () {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer(false);
            })
        },
        logic: {
            /**
             * 从父页面传来值
             */
            setData: function (data) {
                page.logic.initMostTable();
                var dataArr = JSON.parse(data.dateTimeList);
                dateTimeList = data.dateTimeList;
                endDate = data.alarmTime;
                endFlag = JSON.parse(dateTimeList)[5].value
                $.each(dataArr, function (i, el) {
                    if (el['key'] == 'startTime') {
                        startDate = OPAL.util.dateFormat(new Date(el['value']), 'yyyy-MM-dd');
                        startTime = OPAL.util.dateFormat(new Date(el['value']), 'yyyy-MM-dd HH:mm:ss');
                    }
                    if (el['key'] == 'endTime') {
                        // endDate = OPAL.util.dateFormat(new Date(el['value']), 'yyyy-MM-dd');
                        endTime = OPAL.util.dateFormat(new Date(el['value']), 'yyyy-MM-dd HH:mm:ss');
                    }
                })
                //初始化查询结束时间(不传endFlag)
                page.logic.initQueryEndTime(endTime,endFlag);
                unitId = data.unitId;
                unitName = data.unitName;
                $('#unitNameShow').html('装置：' + unitName);
                $('#timeShow').html('时间：' + startDate + ' 至 ' + endDate);
                // 设置 加载 持续报警 table
                page.logic.initAlarmTable();
                page.data.param1= {
                    startTime: startTime,
                    endTime: newEndTime,
                    unitId: unitId,
                    top: 20
                };
                $.ajax({
                    url: getAlarmAnalysisDataUrl,
                    async: true,
                    data: {
                        startTime: startTime,
                        endTime: newEndTime,
                        unitId: unitId,
                        top: 20
                    },
                    dataType: "JSON",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        if (res != null && res != undefined && res != '') {
                            // 报警总数
                            AlarmEventTotalCount = res[0].alarmCount;
                            $('#AlarmEventTotalCount').html(AlarmEventTotalCount);
                            // 操作总数
                            operateCount = res[0].operateCount;
                            $('#operateCount').html(operateCount)
                            // 最频繁报警
                            page.logic.initMostAlarmCharts(res[0].mostAlarm);
                            page.logic.initMostAlarmTable(res[0].mostAlarm);
                            // 最频繁操作
                            page.logic.initMostAlarmOperateCharts(res[0].mostAlarmOperate);
                            page.logic.initMostAlarmOperateTable(res[0].mostAlarmOperate);
                            // 请求 环形图 中的 国际标准数据
                            page.logic.initISOAlarmList(res[0].isoPriority);
                            // 优先级 环形图
                            page.logic.initAlarmPriorityCharts(res[0].systemPriority);
                        }

                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });

            },
            /**
             * 查询最多操作
             */
            queryMostAlarm:function(){
                page.data.param1.top=topCount;
                $.ajax({
                    url: getMostAlarmTopUrl,
                    async: true,
                    data: page.data.param1,
                    dataType: "JSON",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        if (res != null && res != undefined && res != '') {
                            page.logic.initMostAlarmCharts(res);
                            page.logic.initMostAlarmTable(res);
                        }
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            /**
             * 查询最多操作
             */
            queryMostOperate:function(){
                page.data.param1.top=topOperateCount;
                $.ajax({
                    url:getMostAlarmOperateTopUrl,
                    async: true,
                    data: page.data.param1,
                    dataType: "text",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        if (res != null && res != undefined && res != '') {
                            page.logic.initMostAlarmOperateCharts(res);
                            page.logic.initMostAlarmOperateTable(res);
                        }
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            /**
             * 加载 最频繁报警 数据
             */
            initMostAlarmCharts: function (data) {
                var res=data;
                if(typeof(res)=="string")
                    res = JSON.parse(data);
                if (mostAlarmCharts && !mostAlarmCharts.isDisposed()) {
                    mostAlarmCharts.clear();
                    mostAlarmCharts.dispose();
                }
                if (res == undefined || res == null || res.length == 0) {
                    mostAlarmCharts = OPAL.ui.chart.initEmptyChart('shock-alarm-chart');
                    return;
                }
                mostAlarmCharts = echarts.init(document.getElementById('shock-alarm-chart'));
                option = {
                    color: ['#3398DB'],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { // 坐标轴指示器，坐标轴触发有效
                            type: '' // 默认为直线，可选为：'line' | 'shadow'
                        },
                        formatter: function (params) {
                            if (params[0] != null && params[0] != undefined && params[0] != '') {
                                var tag = params[0].name;
                                var count = params[0].value;
                                var dataIndex = params[0].dataIndex;
                                var flagName = res[dataIndex].flagName;
                            }
                            var str = '位号：' + tag + '&nbsp;&nbsp;&nbsp;报警标识：' + flagName + '<br>报警数：' + count
                            return str;
                        }
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        bottom: '1%',
                        top: '5%',
                        height: '220px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        axisTick: {
                            alignWithLabel: true
                        },
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 0,
                            rotate: 15,
                            textStyle: {
                                fontSize: 10
                            }
                        },
                    },

                    ],
                    yAxis: [{
                        type: 'value',
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },

                    }],
                    series: [{
                        type: 'bar',
                        barWidth: '15',
                        data: []
                    }]
                };
                mostAlarmCharts.setOption(option);
                var totalArray = new Array();
                var tagArray = new Array();
                for (var i = 0; i < res.length; i++) {
                    totalArray.push(res[i]["count"]);
                    tagArray.push(res[i]['tag']);
                }
                option.xAxis[0].data = tagArray;
                option.series[0].data = totalArray;
                mostAlarmCharts.setOption(option);
                mostAlarmCharts.on('click', function (params) {
                    page.logic.alarmDetailJump(params.dataIndex, res);
                })
            },
            /**
             * 加载 最频繁操作 数据
             */
            initMostAlarmOperateCharts: function (data) {
                var res=data;
                if(typeof(res)=="string")
                    res = JSON.parse(data);
                if (mostAlarmOperateCharts && !mostAlarmOperateCharts.isDisposed()) {
                    mostAlarmOperateCharts.clear();
                    mostAlarmOperateCharts.dispose();
                }
                if (res == undefined || res == null || res.length == 0) {
                    mostAlarmOperateCharts = OPAL.ui.chart.initEmptyChart('shock-alarm-chart2');
                    return;
                }
                mostAlarmOperateCharts = echarts.init(document.getElementById('shock-alarm-chart2'));
                option = {
                    color: ['#00acac'],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { // 坐标轴指示器，坐标轴触发有效
                            type: '' // 默认为直线，可选为：'line' | 'shadow'
                        },
                        formatter: function (params) {
                            if (params[0] != null && params[0] != undefined && params[0] != '') {
                                var tag = params[0].name;
                                var count = params[0].value;
                                var dataIndex = params[0].dataIndex;
                                var flagName = res[dataIndex].flagName;
                            }
                            var str = '位号：' + tag + '&nbsp;&nbsp;&nbsp;报警标识：' + flagName + '<br>操作数：' + count
                            return str;
                        }
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        bottom: '1%',
                        top: '5%',
                        height: '220px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        axisTick: {
                            alignWithLabel: true
                        },
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 0,
                            rotate: 15,
                            textStyle: {
                                fontSize: 10
                            }
                        }
                    }],
                    yAxis: [{
                        type: 'value',
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                    }],
                    series: [{
                        // name: '',
                        type: 'bar',
                        barWidth: '15',
                        data: []
                    }]
                };
                mostAlarmOperateCharts.setOption(option);
                var totalArray = new Array();
                var tagArray = new Array();
                for (var i = 0; i < res.length; i++) {
                    totalArray.push(res[i]["count"]);
                    tagArray.push(res[i]['tag']);
                }
                option.xAxis[0].data = tagArray;
                option.series[0].data = totalArray;
                mostAlarmOperateCharts.setOption(option);
                mostAlarmOperateCharts.on('click', function (params) {
                    page.logic.operateDetailJump(params.dataIndex, res);
                })

            },
            /**
             * 加载 优先级 环形图 标准数据
             */
            initISOAlarmList: function (data) {
                var res = JSON.parse(data);
                var emHtml, imHtml, noHtml;
                $.each(res, function (i, el) {
                    if (el.key == '紧急') {
                        ISOAlarmEmergency = (el.value).split('%')[0];
                        emHtml = el.value;
                    } else if (el.key == '重要') {
                        ISOAlarmImportance = (el.value).split('%')[0];
                        imHtml = el.value;
                    } else if (el.key == '一般') {
                        ISOAlarmNormal = (el.value).split('%')[0];
                        noHtml = el.value;
                    }
                })
                $('#ISOAlarmEmergencyCount').text(emHtml);
                $('#ISOAlarmImportanceCount').text(imHtml);
                $('#ISOAlarmNormalCount').text(noHtml);
            },
            /**
             * 加载 优先级 环形图 数据
             */
            initAlarmPriorityCharts: function (data) {
                var EmergencyCount, ImportanceCount, NormalCount;
                var _emHtml, _imHtml, _noHtml;
                var res = JSON.parse(data);

                function toNumberPoint(percent) {
                    var str = percent.replace("%", "");
                    str = str / 100;
                    return Number(str);
                }

                $.each(res, function (i, el) {
                    if (el.key == 1) {
                        _emHtml = el.value;
                        EmergencyCount = toNumberPoint(_emHtml) * AlarmEventTotalCount;

                    } else if (el.key == 2) {
                        _imHtml = el.value;
                        ImportanceCount = toNumberPoint(_imHtml) * AlarmEventTotalCount;

                    } else if (el.key == 3) {
                        _noHtml = el.value;
                        NormalCount = toNumberPoint(_noHtml) * AlarmEventTotalCount;

                    }
                });
                if (_emHtml == undefined || _emHtml == null || _emHtml == '') {
                    _emHtml = '0%'
                }
                ;
                if (_imHtml == undefined || _imHtml == null || _imHtml == '') {
                    _imHtml = '0%'
                }
                ;
                if (_noHtml == undefined || _noHtml == null || _noHtml == '') {
                    _noHtml = '0%'
                }
                ;
                $('#emCount').html(_emHtml);
                $('#imCount').html(_imHtml);
                $('#norCount').html(_noHtml);
                var AlarmPriority = echarts.init(document.getElementById('alarm-chart-box-style'));
                var option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: function (params) {
                            var str = params.name + '<br>占比：' + params.percent + '%'
                            return str;
                        }
                    },
                    graphic: {
                        type: 'text',
                        left: 'center',
                        top: 'center',
                        style: {
                            text: '国际标准',
                            textStyle: { //标题内容的样式
                                color: '#333',
                                fontStyle: 'normal',
                                fontWeight: "normal",
                                fontFamily: "san-serif", //主题文字字体，默认微软雅黑
                                fontSize: 12 //主题文字字体大小，
                            },
                            textAlign: 'center',
                            width: 30,
                            height: 30
                        }
                    },
                    legend: {
                        data: [{
                            name: '紧急报警',
                            icon: 'bar',
                            textStyle: {
                                color: '#333',

                            }
                        }, {
                            name: '重要报警',
                            icon: 'bar',
                            textStyle: {
                                color: '#333',
                            }
                        }, {
                            name: '一般报警',
                            icon: 'bar',
                            fontsize: '1',
                            textStyle: {
                                color: '#333',
                                fontsize: 2,
                            }
                        }],
                        left: 50,
                        itemWidth: 10, //设置icon大小
                        itemHeight: 10, //设置icon大小
                    },
                    series: [{
                        name: '',
                        type: 'pie',
                        radius: ['50%', '65%'],
                        color: ['#ff7f6c', '#ffac28', '#2cc780'],
                        avoidLabelOverlap: false,
                        label: {
                            normal: {
                                show: false,
                                position: 'left'
                            },
                            emphasis: {
                                show: false,
                                textStyle: {
                                    fontSize: '30',
                                    fontWeight: 'bold'
                                }
                            }
                        },
                        labelLine: {
                            normal: {
                                show: false
                            }
                        },
                        data: [{
                            value: EmergencyCount,
                            name: '紧急报警'
                        }, {
                            value: ImportanceCount,
                            name: '重要报警'
                        }, {
                            value: NormalCount,
                            name: '一般报警'
                        },]
                    }, {
                        name: '',
                        type: 'pie',
                        radius: ['23%', '35%'],
                        color: ['#ff7f6c', '#ffac28', '#2cc780'],
                        avoidLabelOverlap: false,
                        label: {
                            normal: {
                                show: false,
                            },
                            emphasis: {
                                show: false,
                                textStyle: {
                                    fontSize: '30',
                                    fontWeight: 'bold'
                                }
                            }
                        },
                        labelLine: {
                            normal: {
                                show: false
                            }
                        },
                        data: [{
                            value: ISOAlarmEmergency,
                            name: '紧急报警'
                        }, {
                            value: ISOAlarmImportance,
                            name: '重要报警'
                        }, {
                            value: ISOAlarmNormal,
                            name: '一般报警'
                        },]
                    }]
                };
                AlarmPriority.setOption(option);
                page.logic.priorityAssessJump(AlarmPriority)
            },
            /**
             * 加载 table
             */
            initAlarmTable: function () {
                //加载 持续报警 table 
                OPAL.ui.initBootstrapTable("continuousAlarmTable", {
                    pageSize: 5,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1 ) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'alarmTime',
                        title: '报警时间',
                        align: 'center',
                        width: '150px'
                    }, {
                        field: 'tag',
                        title: '位号',
                        align: 'left',
                        width: '100px'
                    }, {
                        field: 'des',
                        title: '描述',
                        align: 'left',
                        width: '100px'
                    }, {
                        field: 'alarmFlagName',
                        title: '报警标识',
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'handleTime',
                        title: '处理时间',
                        align: 'center',
                        width: '150px'
                    }, {
                        field: 'continuousHour',
                        title: '时长(小时)',
                        align: 'right',
                        width: '100px'
                    }, {
                        field: 'priorityName',
                        title: '优先级',
                        align: 'center',
                        width: '80px'
                    }],
                    onLoadSuccess: function (data) {
                        //设置鼠标浮动提示
                        var tds = $('#continuousAlarmTable').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        });
                        // 持续报警数目
                        var continuousAlarmCount = data.total;
                        $('#continuousAlarmCount').html(continuousAlarmCount);
                    }
                }, page.logic.queryParams);
                var continuousAlarmTableOption = $('#continuousAlarmTable').bootstrapTable('getOptions');
                continuousAlarmTableOption.pageList = [5, 10, 20, 50, 100];
                continuousAlarmTableOption.url = getContinuousAlarmListUrl;
                $('#continuousAlarmTable').bootstrapTable('refreshOptions', continuousAlarmTableOption);
                // 加载 屏蔽报警 table 
                OPAL.ui.initBootstrapTable("suppressedAlarmTable", {
                    pageSize: 5,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1 ) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        field: 'startTime',
                        title: '屏蔽/取消屏蔽时间',
                        align: 'center',
                        width: '120px'
                    }, {
                        field: 'tag',
                        title: '位号',
                        align: 'left',
                        width: '100px'
                    }, {
                        field: 'des',
                        title: '描述',
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "事件类型",
                        field: 'eventTypeName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        field: 'alarmFlagName',
                        title: '报警标识',
                        align: 'center',
                        width: '100px'
                    }, {
                        field: 'workTeamSName',
                        title: '班组',
                        align: 'center',
                        width: '100px'
                    }],
                    onLoadSuccess: function (data) {
                        //设置鼠标浮动提示
                        var tds = $('#suppressedAlarmTable').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        });
                        // 屏蔽报警数目
                        var suppressedAlarmCount = data.total;
                        $('#suppressedAlarmCount').html(suppressedAlarmCount);
                    }
                }, page.logic.queryParams);
                var suppressedAlarmTableOption = $('#suppressedAlarmTable').bootstrapTable('getOptions');
                suppressedAlarmTableOption.pageList = [5, 10, 20, 50, 100];
                suppressedAlarmTableOption.url = getSuppressedAlarmListUrl;
                $('#suppressedAlarmTable').bootstrapTable('refreshOptions', suppressedAlarmTableOption);
                // 加载 搁置报警 table 
                OPAL.ui.initBootstrapTable("shelvedAlarmTable", {
                    pageSize: 5,
                    pageList: [5, 10, 20, 50, 100],
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1 ) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        field: 'startTime',
                        title: '搁置时间',
                        align: 'center',
                        width: '120px'
                    }, {
                        field: 'tag',
                        title: '位号',
                        align: 'left',
                        width: '100px'
                    },  {
                        field: 'des',
                        title: '描述',
                        align: 'left',
                        width: '100px'
                    }, {
                        field: 'alarmFlagName',
                        title: '报警标识',
                        align: 'center',
                        width: '100px'
                    }, {
                        field: 'workTeamSName',
                        title: '班组',
                        align: 'center',
                        width: '100px'
                    }],
                    onLoadSuccess: function (data) {
                        //设置鼠标浮动提示
                        var tds = $('#shelvedAlarmTable').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                        // 搁置报警数目
                        var shelvedAlarmCount = data.total;
                        $('#shelvedAlarmCount').html(shelvedAlarmCount);
                    }
                }, page.logic.queryParams)
                var shelvedAlarmTableOption = $('#shelvedAlarmTable').bootstrapTable('getOptions');
                shelvedAlarmTableOption.pageList = [5, 10, 20, 50, 100];
                shelvedAlarmTableOption.url = getShelvedAlarmListUrl;
                $('#shelvedAlarmTable').bootstrapTable('refreshOptions', shelvedAlarmTableOption);
            },
            /**
             * 加载 最频繁操作和报警的 table
             */
            initMostTable: function () {
                $('#MostAlarmTable').bootstrapTable({
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'tag',
                        title: '位号',
                        align: 'left',
                        width: '100px'
                    }, {
                        field: 'flagName',
                        title: '报警标识',
                        align: 'center',
                        width: '100px'
                    }, {
                        field: 'count',
                        title: '报警数',
                        align: 'right',
                        width: '70px'
                    }, {
                        field: 'percentage',
                        title: '百分比(%)',
                        align: 'right',
                        width: '70px'
                    }, {
                        field: 'priorityName',
                        title: '优先级',
                        align: 'center',
                        width: '70px'
                    }, {
                        field: 'unitName',
                        title: '装置',
                        align: 'left',
                        width: '120px'
                    }, {
                        field: 'prdtName',
                        title: '生产单元',
                        align: 'left',
                        width: '120px'
                    }],
                    sidePagination: "client",
                    pagination: false,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    showColumns: false,
                    pageList: [5]
                });
                $('#MostAlarmOperateTable').bootstrapTable({
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'tag',
                        title: '位号',
                        align: 'left',
                        width: '100px'
                    }, {
                        field: 'flagName',
                        title: '报警标识',
                        align: 'center',
                        width: '100px'
                    }, {
                        field: 'count',
                        title: '操作数',
                        align: 'right',
                        width: '70px'
                    }, {
                        field: 'unitName',
                        title: '装置',
                        align: 'left',
                        width: '120px'
                    }, {
                        field: 'prdtName',
                        title: '生产单元',
                        align: 'left',
                        width: '120px'
                    }],
                    sidePagination: "client",
                    pagination: false,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    showColumns: false,
                    pageList: [5]
                });

            },
            initMostAlarmTable: function (data) {
                var res=data;
                if(typeof(res)=="string")
                    res = JSON.parse(data);
                $("#MostAlarmTable").bootstrapTable("removeAll");
                if (res == null || res == undefined || res.length == 0) {
                    return;
                }
                $("#MostAlarmTable").bootstrapTable("load", res);
                $("#MostAlarmTable").bootstrapTable("refresh");
                //设置鼠标浮动提示
                var tds = $('#MostAlarmTable').find('tbody tr td');
                $.each(tds, function (i, el) {
                    $(this).attr("title", $(this).text())
                });
                // 点击表格跳转到报警详情
                $('#MostAlarmTable tbody tr').click(function () {
                    $("#MostAlarmTable tbody tr").removeClass("selectedd");
                    $(this).addClass("selectedd");
                    var i = $(this).attr('data-index');
                    if (data.length != 0) {
                        page.logic.alarmDetailJump(i, res);
                    }
                })
            },
            initMostAlarmOperateTable: function (data) {
                var res=data;
                if(typeof(res)=="string")
                    res = JSON.parse(data);
                $("#MostAlarmOperateTable").bootstrapTable("removeAll");
                if (res == null || res == undefined || res.length == 0) {
                    return;
                }
                $("#MostAlarmOperateTable").bootstrapTable("load", res);
                $("#MostAlarmOperateTable").bootstrapTable("refresh");
                //设置鼠标浮动提示
                var tds = $('#MostAlarmOperateTable').find('tbody tr td');
                $.each(tds, function (i, el) {
                    $(this).attr("title", $(this).text())
                })
                // 点击表格跳转到操作详情
                $('#MostAlarmOperateTable tbody tr').click(function () {
                    $("#MostAlarmOperateTable tbody tr").removeClass("selectedd");
                    $(this).addClass("selectedd");
                    var i = $(this).attr('data-index');
                    if (data.length != 0) {
                        page.logic.operateDetailJump(i, res);
                    }
                })
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    unitId: unitId,
                    startTime: startTime,
                    endTime: newEndTime,
                    now: Math.random()
                };
                page.data.param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                };
                return param;
            },
            /**
             * 最频繁操作的 跳转 到操作详情
             */
            operateDetailJump: function (dataIndex, res) {
                // var dataIndex = params.dataIndex; // 当前index
                var alarmPointId = res[dataIndex].alarmPointId;
                var alarmFlagId = res[dataIndex].alarmFlagId;
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: false,
                    shadeClose: false,
                    scrollbar: false,
                    area: ['95%', '95%'],
                    content: 'OperateDetail.html?'+ Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            dateJason: dateTimeList,
                            alarmPointId: alarmPointId,
                            alarmFlagId: alarmFlagId
                        };
                        iframeWin.page.logic.setData(data);
                    }
                })
            },
            /**
             * 最频繁报警的 跳转 到报警详情
             */
            alarmDetailJump: function (dataIndex, res) {
                var alarmPointId = res[dataIndex].alarmPointId;
                var alarmFlagId = res[dataIndex].alarmFlagId;
                var flagName = res[dataIndex].flagName;
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: false,
                    shadeClose: false,
                    scrollbar: false,
                    area: ['95%', '95%'],
                    content: 'AlarmDetail.html?'+ Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            unitId: unitId,
                            dateJason: dateTimeList,
                            alarmPointId: alarmPointId,
                            alarmFlagId: alarmFlagId
                        };
                        iframeWin.page.logic.setData(data);
                    }
                })
            },
            /**
             * 跳转到优先级评估页面
             */
            priorityAssessJump: function (AlarmPriority) {
                AlarmPriority.on('click', function () {
                    layer.open({
                        type: 2,
                        title: '',
                        closeBtn: false,
                        shadeClose: false,
                        scrollbar: false,
                        area: ['98%', '98%'],
                        content: '../../aa/AlarmPriorityAssess/Index.html?'+ Math.random()+"&priorityAssessReadOnlyFlag=true",
                        success: function (layero, index) {
                            var body = layer.getChildFrame('body', index);
                            var iframeWin = window[layero.find('iframe')[0]['name']];
                            var data = {
                                unitId: unitId,
                                unitName: unitName,
                                startDate: startDate,
                                endDate: endDate,
                                startTime: startTime,
                                endTime: endTime,
                                endFlag: endFlag,
                                priorityAssessReadOnlyFlag: true
                            };
                            iframeWin.page.logic.setData(data);
                        }
                    })
                })
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.layer.close(index);
            },
            /**
             * 获取查询结束时间
             */
            initQueryEndTime: function (endTime, endFlag) {
                if (endFlag == "<") {
                    newEndTime= moment(endTime).subtract(1, 'seconds').format('YYYY-MM-DD HH:mm:ss');;
                }
                else {
                    newEndTime= endTime;
                }
            }
        }
    }
    page.init();
    window.page = page;

})