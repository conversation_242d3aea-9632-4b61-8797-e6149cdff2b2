package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

import javax.persistence.Column;

/*
 * 装置实体
 * 模块编号：pcitc_opal_bll_class_UnitEntity
 * 作    者：xuelei.wang
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描   述：装置实体
 */
public class DBUnitEntity extends BasicEntity {

    /**
     * 装置ID
     */
    private Long unitId;

    /**
     * 班组名称
     */
    private String workTeamName;
    /**
     * 名称
     */
    private String name;

    /**
     * 简称
     */
    private String sname;

    /**
     * 排序
     */
    private Integer sortNum;

    /**
     * 描述
     */
    private String des;
    /**
     * 编码
     */
    private String stdCode;
    /**
     * 工厂名称
     */
    private String factoryName;
    /**
     * 车间名称
     */
    private String workshopName;
    /**
     * 轮班域名称
     */
    private String shiftWorkName;
    /**
     * 工厂ID
     */
    private Long factoryId;
    /**
     * 车间ID
     */
    private Long workshopId;
    /**
     * 轮班域Id
     */
    private Long shiftAreaId;
    /**
     * 操作工人数
     */
    private Long operatorNum;
    /**
     * 企业ID
     */
    @Column(name = "company_id")
    //企业
    private Integer companyId;

    public Integer getCompanyId() {
        return companyId;
    }
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }


    public Long getUnitCode() {
        return unitId;
    }

    public void setUnitCode(Long unitId) {
        this.unitId = unitId;
    }

    public Long getWorkshopId() {
        return workshopId;
    }

    public void setWorkshopId(Long workshopId) {
        this.workshopId = workshopId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSname() {
        return sname;
    }

    public void setSname(String sname) {
        this.sname = sname;
    }

    public String getStdCode() {
        return stdCode;
    }

    public void setStdCode(String stdCode) {
        this.stdCode = stdCode;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getWorkTeamName() {
        return workTeamName;
    }

    public void setWorkTeamName(String workTeamName) {
        this.workTeamName = workTeamName;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    public String getWorkshopName() {
        return workshopName;
    }

    public void setWorkshopName(String workshopName) {
        this.workshopName = workshopName;
    }

    public String getShiftWorkName() {
        return shiftWorkName;
    }

    public void setShiftWorkName(String shiftWorkName) {
        this.shiftWorkName = shiftWorkName;
    }

    public Long getShiftAreaId() {
        return shiftAreaId;
    }

    public void setShiftAreaId(Long shiftAreaId) {
        this.shiftAreaId = shiftAreaId;
    }

    public Long getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Long factoryId) {
        this.factoryId = factoryId;
    }

    public Long getOperatorNum() {
        return operatorNum;
    }

    public void setOperatorNum(Long operatorNum) {
        this.operatorNum = operatorNum;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }
}