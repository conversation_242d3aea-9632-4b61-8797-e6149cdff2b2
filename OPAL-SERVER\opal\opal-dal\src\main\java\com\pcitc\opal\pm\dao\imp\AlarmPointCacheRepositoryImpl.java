package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.*;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmPointCacheRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmPointCache;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

public class AlarmPointCacheRepositoryImpl extends BaseRepository<AlarmPointCache,Long> implements AlarmPointCacheRepositoryCustom {
    @Autowired
    private DbConfig dbConfig;
    /**
     * 报警点对比查询(新增)
     *
     * <AUTHOR> 2018-08-29
     * @param unitCodes 装置编码数组
     * @param tag 位号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 分页对象
     * @return PaginationBean 返回实体分页对象
     */
    @Override
    public PaginationBean<Object[]> getAlarmPointCacheList(String[] unitCodes, String tag, Date startTime, Date endTime, Pagination page) {
        Map<String, Object> paramList = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTimeStr="";
        String endTimeStr="";
        if(startTime!=null){
            startTimeStr = sdf.format(startTime);
        }
        if(endTime!=null){
            endTimeStr = sdf.format(endTime);
        }
        StringBuilder sql = new StringBuilder("select distinct p.unit_code,p.prdtcell_id,p.sname,t.* from(" +
                "select max(t1.write_time) write_time," +
                "t1.opc_code ," +
                "t1.prdt_cell," +
                "t1.tag," +
                "max(case t1.limit_type when UPPER('HH') then t1.limit_value when UPPER('PVHHALM') then t1.limit_value else null end ) hh," +
                "max(case t1.limit_type when UPPER('PH') then t1.limit_value when UPPER('PVHIALM') then t1.limit_value else null end ) ph," +
                "max(case t1.limit_type when UPPER('PL') then t1.limit_value when UPPER('PVLOALM') then t1.limit_value else null end ) pl," +
                "max(case t1.limit_type when UPPER('LL') then t1.limit_value when UPPER('PVLLALM') then t1.limit_value else null end ) ll, " +
                "max(t1.state_flag) state_flag " +
                "from t_pm_alarmpoint_cache t1 where 1=1");
        if(startTime!=null){
//                sql.append(" and t1.write_time >=to_date(:startTimeStr,'yyyy-mm-dd hh24:mi:ss')");
            sql.append(" and t1.write_time >="+ DbConversion.DbDateTransformYmdhmsToDate(":startTimeStr"));
            paramList.put("startTimeStr",startTimeStr);
        }
        if(endTime!=null){
//                sql.append(" and t1.write_time <to_date(:endTimeStr,'yyyy-mm-dd hh24:mi:ss')");
            sql.append(" and t1.write_time <"+ DbConversion.DbDateTransformYmdhmsToDate(":endTimeStr"));
            paramList.put("endTimeStr",endTimeStr);
        }
//        else{
//            if(startTime!=null){
//                sql.append(" and t1.write_time >=str_to_date(:startTimeStr,'%Y-%m-%d %H:%i:%s')");
//                paramList.put("startTimeStr",startTimeStr);
//            }
//            if(endTime!=null){
//                sql.append(" and t1.write_time <str_to_date(:endTimeStr,'%Y-%m-%d %H:%i:%s')");
//                paramList.put("endTimeStr",endTimeStr);
//            }
//        }
        sql.append(" group by t1.opc_code,t1.prdt_cell,t1.tag) t " +
                " inner join t_ad_alarmprdtcellcomp comp on t.opc_code = comp.opc_code_id and t.prdt_cell = comp.prdtcell_source " +
                " inner join t_pm_prdtcell p on comp.PRDTCELL_ID = p.PRDTCELL_ID " +
                "where comp.company_id=:companyId and p.company_id=:companyId and t.tag not in (select ap.tag from t_pm_alarmpoint ap where comp.prdtcell_id = ap.prdtcell_id and ap.TAG = t.TAG) and t.state_flag=2");

        if (ArrayUtils.isNotEmpty(unitCodes)) {
            sql.append(" and p.unit_code in (:unitCodes) ");
            paramList.put("unitCodes", Arrays.asList(unitCodes));
        }
        if(StringUtils.isNotEmpty(tag)){
            sql.append("  and (upper(t.tag) like upper(:tag) escape '/') ");
            paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
        }
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId",commonProperty.getCompanyId());
        sql.append(" order by p.unit_code,p.sname,t.tag");
        String countSql = "SELECT COUNT(*) FROM ( " +sql.toString() +" ) T2";
        Query countQuery = this.getEntityManager().createNativeQuery(countSql);
        this.setParameterList(countQuery, paramList);
        Long count=new BigDecimal(countQuery.getResultList().get(0)+"").longValue();

        PaginationBean resultList = new PaginationBean(page, count);
        Query query = this.getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, paramList);
        query.setFirstResult(resultList.getBeginIndex()).setMaxResults(resultList.getPageSize());
        ArrayList resultList1 = (ArrayList) query.getResultList();
        resultList.setPageList(resultList1);
        return resultList;
    }
    /**
     * 报警点对比查询(删除)
     *
     * <AUTHOR> 2018-08-29
     * @param unitCodes 装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param tag 位号
     * @param inUse 是否启用
     * @param page 分页对象
     * @return PaginationBean 返回实体分页对象
     */
    @Override
    public PaginationBean<Object[]> getAlarmPointCacheList(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inUse, Pagination page) {
        Map<String, Object> paramList = new HashMap<>();
        StringBuilder sql = new StringBuilder("select " +
                "p.unit_code," +
                "p.sname," +
                "ap.tag," +
                "ap.craft_rank," +
                "ap.location," +
                "ap.craft_up_limit_value," +
                "ap.craft_down_limit_value," +
                "ap.craft_up_limit_include," +
                "ap.craft_down_limit_include," +
                "ap.interlock_up_limit_value," +
                "ap.interlock_down_limit_value," +
                "ap.interlock_up_limit_include," +
                "ap.interlock_down_limit_include," +
                "ap.in_use," +
                "ap.alarm_point_id"+
                " from t_pm_alarmpoint ap,t_pm_prdtcell p " +
                " where ap.company_id=:companyId and p.company_id=:companyId and ap.prdtcell_id=p.prdtcell_id " +
                " and (ap.prdtcell_id,ap.tag) not in (" +
                "  select distinct comp.prdtcell_id,t.tag from t_pm_alarmpoint_cache t,t_ad_alarmprdtcellcomp comp" +
                " where comp.company_id=:companyId and t.opc_code=comp.opc_code_id and t.prdt_cell=comp.prdtcell_source) ");
        if (ArrayUtils.isNotEmpty(unitCodes)) {
            sql.append(" and p.unit_code in (:unitCodes) ");
            paramList.put("unitCodes", Arrays.asList(unitCodes));
            // 生产单元
            if (unitCodes.length == 1 && ArrayUtils.isNotEmpty(prdtCellIds)) {
                sql.append(" and p.prdtcell_id in (:prdtCellIds) ");
                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
            }
        }
        if(StringUtils.isNotEmpty(tag)){
            sql.append("  and (upper(ap.tag) like upper(:tag) escape '/') ");
            paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
        }
        if(inUse !=-1){
            sql.append(" and ap.in_use =:inUse");
            paramList.put("inUse",inUse);
        }
        sql.append(" order by p.unit_code,p.sname,ap.tag");
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId",commonProperty.getCompanyId());
        String countSql = "SELECT COUNT(*) FROM ( " +sql.toString() +" ) T1";
        Query countQuery = this.getEntityManager().createNativeQuery(countSql);
        this.setParameterList(countQuery, paramList);
        Long count=new BigDecimal(countQuery.getResultList().get(0)+"").longValue();

        PaginationBean resultList = new PaginationBean(page, count);
        Query query = this.getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, paramList);
        query.setFirstResult(resultList.getBeginIndex()).setMaxResults(resultList.getPageSize());
        ArrayList resultList1 = (ArrayList) query.getResultList();
        resultList.setPageList(resultList1);
        return resultList;
    }
}
