package com.pcitc.opal.ac.bll.entity;

import com.pcitc.opal.common.CommonEnum;

import java.util.Date;
/*
 * 报警变更方案审批记录实体
 * 模块编号：pcitc_opal_bll_class_AlarmChangePlanAproEntity
 * 作       者：dageng.sun
 * 创建时间：2018/01/23
 * 修改编号：1
 * 描       述：报警变更方案审批记录实体
 */
public class AlarmChangePlanAproEntity {

    /**
     * 报警变更方案审批记录ID
     */
    private Long aproId;

    /**
     * 报警变更方案ID
     */
    private Long planId;

    /**
     * 审批状态（1通过；0驳回）
     */
    private Integer aproStatus;
    
    /**
     * 审批状态名称
     */
    @SuppressWarnings("unused")
	private String aproStatusName;

    /**
     * 审批意见
     */
    private String aproOpnion;

    /**
     * 审批时间
     */
    private Date aproTime;

    /**
     * 审批人ID
     */
    private String aproUserId;

    /**
     * 审批人名称
     */
    private String aproUserName;

	public Long getAproId() {
		return aproId;
	}

	public void setAproId(Long aproId) {
		this.aproId = aproId;
	}

	public Long getPlanId() {
		return planId;
	}

	public void setPlanId(Long planId) {
		this.planId = planId;
	}

	public Integer getAproStatus() {
		return aproStatus;
	}

	public void setAproStatus(Integer aproStatus) {
		this.aproStatus = aproStatus;
	}

	public String getAproOpnion() {
		return aproOpnion;
	}

	public void setAproOpnion(String aproOpnion) {
		this.aproOpnion = aproOpnion;
	}

	public Date getAproTime() {
		return aproTime;
	}

	public void setAproTime(Date aproTime) {
		this.aproTime = aproTime;
	}

	public String getAproUserId() {
		return aproUserId;
	}

	public void setAproUserId(String aproUserId) {
		this.aproUserId = aproUserId;
	}

	public String getAproUserName() {
		return aproUserName;
	}

	public void setAproUserName(String aproUserName) {
		this.aproUserName = aproUserName;
	}

	public String getAproStatusName() {
		return CommonEnum.AproStatusEnum.getName(aproStatus);
	}

	public void setAproStatusName(String aproStatusName) {
		this.aproStatusName = aproStatusName;
	}
}
