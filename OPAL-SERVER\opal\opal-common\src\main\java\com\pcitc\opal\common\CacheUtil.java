package com.pcitc.opal.common;

import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.Element;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Method;
import java.util.List;
import java.util.regex.Pattern;

/*
 * 文件类描述
 * 模块编号：CacheUtil
 * 作	者：jiangtao.xue
 * 创建时间：2018/4/12
 * 修改编号：1
 * 描	述：文件类描述
 */
public class CacheUtil {
    @Target({ java.lang.annotation.ElementType.METHOD })
    @Retention(RetentionPolicy.RUNTIME)
    public @interface CacheRemove {
        String value();
        String[] key();
    }

    @Aspect
    @Component
    public class CacheRemoveAspect {
        @Pointcut(value = "(execution(* *.*(..)) && @annotation(CacheRemove))")
        private void pointcut() {}

    }

    @AfterReturning(value = "pointcut()")
    private void process(JoinPoint joinPoint){
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        CacheRemove cacheRemove = method.getAnnotation(CacheRemove.class);

        if (cacheRemove != null){
            String value = cacheRemove.value();
            String[] keys = cacheRemove.key(); //需要移除的正则key

            List cacheKeys = cacheKeys(value);
            for (String key : keys){
                Pattern pattern = Pattern.compile(key);
                for (Object cacheKey: cacheKeys) {
                    String cacheKeyStr = String.valueOf(cacheKey);
                    if (pattern.matcher(cacheKeyStr).find()){
                        remove(value, cacheKeyStr);
                    }
                }
            }
        }
    }



    private static final String path = "/ehcache.xml";
    private static CacheManager cacheManager = CacheManager.create(path);

    public static Object get(String cacheName, String key) {
        Element element = getCache(cacheName).get(key);
        return element == null ? null : element.getObjectValue();
    }

    public static void put(String cacheName, String key, Object value) {
        Element element = new Element(key, value);
        getCache(cacheName).put(element);
    }

    public static void remove(String cacheName, String key) {
        getCache(cacheName).remove(key);
    }

    public static List cacheKeys(String cacheName){
        return getCache(cacheName).getKeys();
    }

    /**
     * 获得一个Cache，没有则创建一个。
     * @param cacheName
     * @return
     */
    private static Cache getCache(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache == null) {
            cacheManager.addCache(cacheName);
            cache = cacheManager.getCache(cacheName);
            cache.getCacheConfiguration().setEternal(true);
        }
        return cache;
    }

    public static CacheManager getCacheManager() {
        return cacheManager;
    }
}
