var addUrl = OPAL.API.pmUrl + '/unitPerson';
var getSingleUrl = OPAL.API.pmUrl + '/unitPerson'+'/single';
var factoryUrl = OPAL.API.pmUrl + '/unitPerson' + "/factoryList";
var workshopUrl = OPAL.API.pmUrl + '/unitPerson' +"/workShop";
var unitUrl=OPAL.API.pmUrl + '/unitPerson'+"/getUnits";
var inUseUrl = OPAL.API.commUrl + "/getInUse";
var shiftAreaUrl = OPAL.API.commUrl + "/getAllShiftAreaList";
var pageMode = PageModelEnum.NewAdd;
var selectValue='';
var selectFirstValue=true;
window.pageLoadMode = PageLoadMode.Refresh;
var unitArr = [];
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
            page.logic.initFactory();
        },
        bindUI: function () {
            $('#saveAddModal').click(function () {
                page.logic.save();
            });
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
            $('#closePage').click(function () {
                page.logic.closeLayer(false);
            })
        },
        logic: {
            /**
             * 保存
             */
            save: function () {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                // var data=OPAL.form.getETCollectionData("AddOrEditModal");
                //处理提交类型
                var ajaxType = "POST";
                if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                }

                $.ajax({
                    url: addUrl,
                    async: false,
                    type: ajaxType,
                    data: $("form").serialize(),
                    processData: false,
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！",{
                                time: 1000
                            },function() {
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                $("#pageTitle").text(data.title);
                if (pageMode == PageModelEnum.NewAdd) {
                    return;
                }
                $.ajax({
                    url: getSingleUrl + "/" + data.unitPersonId + "?now=" + Math.random(),
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function (data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        //加载车间有延迟,使用配置方式
                        selectFirstValue=false;
                        selectValue=entity['unitPersonId'];
                        OPAL.form.setData('AddOrEditModal', entity);
                    },
                    complete: function (XMLHttpRequest, textStatus) {
                    },
                    error: function (XMLHttpRequest, textStatus) {
                    }
                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                parent.layer.close(index);
            },
            /**
             * 表单校验
             */
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal',{
                    rules: {
                        factoryId: {
                            required: true
                        },
                        workshopId:{
                            required: true,
                        },
                        name: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        sname: {
                            required: true,
                            rangelength: [0, 100]
                        },

                        operNum: {
                            required: true,
                            digits: true,
                            min: 1
                        },
                        unitId: {
                            required: true
                        },

                    }
                });
            },
            /**
             * 初始化工厂
             */
            initFactory: function () {
                OPAL.ui.getCombobox("factoryCode", factoryUrl, {
                    async: false,
                    keyField: "stdCode",
                    valueField: "sname",
                    selectFirstRecord: true,
                    data: {
                        isAll: false
                    }
                }, function () {
                        page.logic.initWorkshop($("#factoryCode").val());
                }, function (selectedValue) {
                        page.logic.initWorkshop(selectedValue);
                });
            },
            /**
             * 初始化车间
             */
            initWorkshop: function (factoryCode) {
                if(factoryCode == null || factoryCode == '' || factoryCode == undefined){
                    $("#workShopCode").empty();
                    $("#unitId").empty();
                    $("#unitCode").text('');
                    $("#name").text('');
                    unitArr=[];
                    return;
                }
                OPAL.ui.getCombobox("workShopCode", workshopUrl, {
                    keyField: "workShopCode",
                    async: false,
                    valueField: "sname",
                    selectFirstRecord: selectFirstValue,
                    selectValue:selectValue,
                    data: {
                        factoryCode: factoryCode,
                        isAll: false
                    }
                }, function () {
                        page.logic.initUnit($("#workShopCode").val());
                }, function (selectedValue) {
                        page.logic.initUnit(selectedValue);
                });
            },
            /***
             * 初始化装置
             */
            initUnit:function(workshopCode){
                if(workshopCode == null || workshopCode == '' || workshopCode == undefined){
                    $("#unitId").empty();
                    $("#unitCode").val('');
                    $("#name").val('');
                    unitArr=[];
                    return;
                }
                OPAL.ui.getCombobox("unitId", unitUrl, {
                    keyField: "stdCode",
                    async: false,
                    valueField: "sname",
                    selectFirstRecord: true,
                    data: {
                        workShopCode: workshopCode,
                        isAll: false
                    }
                },function(code,arr){
                    unitArr=arr;
                    page.logic.initUnitCodeAndName(code);
                },function (code) {
                    page.logic.initUnitCodeAndName(code);
                    
                });
            },
            initUnitCodeAndName: function (code) {
                if(code == null || code == '' || code == undefined) {
                    $("#unitCode").val('');
                    $("#name").val('');
                    return;
                }
                $("#unitCode").val(code);
                for(var i=0;i<unitArr.length;i++) {
                    if(unitArr[i].stdCode == code){
                        $("#name").val(unitArr[i].name);
                        break;
                    }
                }
            }
        }

    }
    page.init();
    window.page = page;
})