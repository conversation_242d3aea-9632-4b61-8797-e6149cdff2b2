package com.pcitc.opal.pm.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.pm.pojo.AlarmPointView;

/*
 * ChangeTagList实体的Repository的JPA标准接口
 * 模块编号： pcitc_opal_dal_interface_ChangeTagListRepository
 * 作       者：dageng.sun
 * 创建时间：2018/1/22
 * 修改编号：1
 * 描       述：ChangeTagList实体的Repository的JPA标准接口
 */
public interface AlarmPointViewRepository extends JpaRepository<AlarmPointView, Long>,AlarmPointViewRepositoryCustom {
	
}
