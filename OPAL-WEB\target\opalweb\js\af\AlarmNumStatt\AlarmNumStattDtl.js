var detailUrl = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumStattDtl';
$(function() {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        /**
         * 初始化
         */
        init: function() {
            /**
             *绑定事件
             */
            this.bindUi();
            page.logic.initTable();
        },
        bindUi: function() {
           
        },
        data: {
            // 设置查询参数
            param: {}
        },
        logic: {
            /**
             * 初始化数据
             */
            setData: function(data) {
                var priorityNameHtml = '';
                if (data.priorityName.includes(1)) {
                    priorityNameHtml = priorityNameHtml+ ' 紧急';
                }
                if (data.priorityName.includes(2)) {
                    priorityNameHtml = priorityNameHtml+ ' 重要';
                }
                if (data.priorityName.includes(3)) {
                    priorityNameHtml = priorityNameHtml+ ' 一般';
                }
                if (data.priorityName.includes(9)) {
                    priorityNameHtml = priorityNameHtml+ ' 空';
                }
                if (data.priorityName.includes(-1)) {
                    priorityNameHtml = '全部'
                }
                $('.priorityName').html(priorityNameHtml);
                $('.unitName').html(data.unitName);
                $('.startTime').html(data.startTime);
                $('.endTime').html(data.endTime);
                var param={};
                param.startTime=data.startTime;
                param.endTime=data.endTime;
                param.unitCode=data.unitId;
                param.priority=data.priorityName;
                param.alarmFlagId=data.alarmFlagId;
                page.data.param = param;
                $("#tableDetail").bootstrapTable('refresh', {
                    "url": detailUrl,
                    "pageNumber":1
                });
            },
            initTable: function() {
                OPAL.ui.initBootstrapTable("tableDetail", {
                    pageSize:20,
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1 ) * data.pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "报警时间",
                        field: 'alarmTime',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        field: 'cellSname',
                        title: '生产单元',
                        width: '120px'
                    }, {
                        field: 'tag',
                        title: '位号',
                        width: '100px'

                    }, {
                        title: "描述",
                        field: 'des',
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "当前值",
                        field: 'nowValue',
                        align: 'right',
                        width: '100px'
                    }],
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                }, page.logic.queryParams)
            },
            queryParams: function (p) { // 设置查询参数
                var params = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    now: Math.random()
                };
                return $.extend(true,page.data.param,params);
            }
        }
    };
    page.init();
    window.page = page;
});
