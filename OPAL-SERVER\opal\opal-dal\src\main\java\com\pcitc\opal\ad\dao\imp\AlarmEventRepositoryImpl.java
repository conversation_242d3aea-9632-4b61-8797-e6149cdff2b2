package com.pcitc.opal.ad.dao.imp;

import com.pcitc.opal.ad.dao.AlarmEventRepositoryCustom;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.CommonEnum.EventTypeEnum;
import com.pcitc.opal.common.CommonEnum.TimeFilterTypeEnum;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.common.pojo.DateRange;
import com.pcitc.opal.pm.dao.AlarmPointDelConfigRepository;
import com.pcitc.opal.pm.pojo.AlarmPointDelConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import javax.annotation.Resource;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.Transactional;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/*
 * AlarmEvent实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_AlarmEventRepositoryImpl
 * 作       者：kun.zhao
 * 创建时间：2017/10/09
 * 修改编号：1
 * 描       述：AlarmEvent实体的Repository实现
 */
@Slf4j
public class AlarmEventRepositoryImpl extends BaseRepository<AlarmEvent, Long> implements AlarmEventRepositoryCustom {

    @Resource
    AlarmPointDelConfigRepository alarmPointDelConfigRepository;

    @Autowired
    private DbConfig dbConfig;

    /**
     * 获取报警事件分页数据
     *
     * @param unitCodes     装置编码数组
     * @param prdtCellIds   生产单元ID数组
     * @param eventTypeIds  事件类型ID数组
     * @param alarmPointTag 报警点位号
     * @param alarmFlagId   报警标识ID
     * @param priority      优先级
     * @param craftRank     级别
     * @param timeType      时间筛选类型
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param dateRangeList 日期区间列表
     * @param page          分页参数
     * @return 报警事件实体集合
     * @throws Exception
     * <AUTHOR> 2019-12-30
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Transactional
    @Override
    public PaginationBean<Object[]> getAlarmEventGroup(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds,
                                                       String alarmPointTag, Long alarmFlagId, Integer priority, Integer craftRank, TimeFilterTypeEnum timeType,
                                                       Date startTime, Date endTime, List dateRangeList, Pagination page) throws Exception {
        try {
            List<DateRange> dateList = ObjectConverter.listConverter(dateRangeList, DateRange.class);
            // 查询字符串
            StringBuilder hql = new StringBuilder("select ae.unit_code,pc.prdtcell_id,pc.sname,ap.alarm_point_id,ap.tag,ap.des,af.alarm_flag_id,af.name, COUNT(*) as alarmnum FROM t_ad_alarmevent ae \n" +
                    "inner join   t_pm_alarmpoint ap on ap.alarm_point_id=ae.alarm_point_id\n" +
                    "inner join  t_ad_alarmflag af on af.alarm_flag_id=ae.alarm_flag_id\n" +
                    "inner join  t_pm_prdtcell pc on pc.prdtcell_id=ae.prdtcell_id\n");
//                    "inner join  t_pm_eventtype et on et.event_type_id=ae.event_type_id\n"
//                    "left join  t_pm_measunit mu on ap.measunit_id= mu.measunit_id  ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            //过滤报警标识
            StringBuilder hqlWhere = new StringBuilder("where ae.priority is not null and ap.in_use = 1 ");
            hqlWhere.append(" and exists (select 1 from t_ad_alarmflagcomp afc where afc.alarm_flag_id=af.alarm_flag_id and afc.in_use=1) ");
            //过滤事件ID
            if (ArrayUtils.isNotEmpty(eventTypeIds)) {
                hqlWhere.append(" and ae.event_type_id in (:eventTypeIds) ");
                paramList.put("eventTypeIds", Arrays.asList(eventTypeIds));
            }
            // 过滤生产单元
            if (ArrayUtils.isNotEmpty(prdtCellIds)) {
                hqlWhere.append("and ae.prdtcell_id in (:prdtCellIds) ");
                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
            }
            // 过滤报警点标识
            if (alarmFlagId != null) {
                hqlWhere.append("and ae.alarm_flag_id=:alarmFlagId ");
                paramList.put("alarmFlagId", alarmFlagId);
            }
            //过滤优先级
            if (priority != null) {
                hqlWhere.append("  and ae.priority =:priority ");
                paramList.put("priority", priority);
            }
            // 过滤日期
            if (startTime != null && endTime != null) {
                if (timeType.equals(TimeFilterTypeEnum.StartTime)) {
                    hqlWhere.append("and ae.start_time between :beginTime and :endTime ");
                } else {
                    hqlWhere.append("and ae.alarm_time between :beginTime and :endTime ");
                }
                paramList.put("beginTime", startTime);
                paramList.put("endTime", endTime);
            }
            // 过滤级别
            if (craftRank != null) {
                hqlWhere.append("  and ap.craft_rank =:craftRank ");
                paramList.put("craftRank", craftRank);
            }
            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hqlWhere.append("and ae.unit_code in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
//            paramList.put("companyId", commonProperty.getCompanyId());

            hql.append(hqlWhere.toString());
            //分组:根据查询结果的“装置、生产单元、位号、描述、报警标识”进行分组，统计数量
            hql.append(" group by ae.unit_code, ae.prdtcell_id,  ae.alarm_point_id, ae.tag, ap.des, ae.alarm_flag_id ");
            // 排序
            hql.append(" order by ae.unit_code , ae.prdtcell_id , ae.tag, ae.alarm_flag_id ");

            return findAllNativeQuery(page, hql.toString(), paramList);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 获取报警事件分页数据
     *
     * @param unitCodes     装置编码数组
     * @param prdtCellIds   生产单元ID数组
     * @param eventTypeIds  事件类型ID数组
     * @param alarmPointTag 报警点位号
     * @param alarmFlagId   报警标识ID
     * @param priority      优先级
     * @param craftRank     级别
     * @param timeType      时间筛选类型
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param dateRangeList 日期区间列表
     * @param page          分页参数
     * @return 报警事件实体集合
     * @throws Exception
     * <AUTHOR> 2017-10-09
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Transactional
    @Override
    public PaginationBean<AlarmEvent> getAlarmEvent(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds,
                                                    String alarmPointTag, Long alarmFlagId, Integer priority, Integer craftRank, TimeFilterTypeEnum timeType,
                                                    Date startTime, Date endTime, List dateRangeList, Pagination page) throws Exception {
        try {
            List<DateRange> dateList = ObjectConverter.listConverter(dateRangeList, DateRange.class);
            // 查询字符串
            StringBuilder hql = new StringBuilder();
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 排序
            String sort = "";
            if (timeType.equals(TimeFilterTypeEnum.StartTime)) {
                sort = " order by ae.startTime desc, ap.tag asc, ae.alarmFlagId asc ";
            } else {
                sort = " order by ae.alarmTime desc, ap.tag asc, ae.alarmFlagId asc ";
            }
            // 获取hql
            if (dateList == null || dateList.size() == 0) {
                hql.append(this.getQueryString(unitCodes, prdtCellIds, alarmFlagId, timeType, startTime, endTime, eventTypeIds, null, craftRank, alarmPointTag, priority, paramList));
            } else {
                this.saveDateRangeList(dateList);
                hql.append(this.getQueryString(unitCodes, prdtCellIds, alarmFlagId, timeType, null, null, eventTypeIds, null, craftRank, alarmPointTag, priority, paramList));
                if (timeType.equals(TimeFilterTypeEnum.StartTime)) {
                    hql.append(" and exists(select dr from  DateRange dr where ae.startTime >=dr.startTime and ae.startTime<dr.endTime) ");
                } else {
                    hql.append(" and exists(select dr from  DateRange dr where ae.alarmTime >=dr.startTime and ae.alarmTime<dr.endTime) ");
                }
            }
            hql.append(sort);
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    @Transactional
    public PaginationBean<AlarmEvent> getAlarmEventList(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds, String alarmPointTag, Long[] alarmFlagIds, Integer priority, Integer[] prioritys, Integer monitorType, Integer craftRank, TimeFilterTypeEnum timeType, Date startTime, Date endTime, List dateRangeList, Pagination page, Integer isMatching) throws Exception {
        try {
            List<DateRange> dateList = ObjectConverter.listConverter(dateRangeList, DateRange.class);
            // 查询字符串
            StringBuilder hql = new StringBuilder();
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 排序
            String sort = "";
            //tag和alarmFlagId排序影响性能，暂时注释掉
            if (timeType.equals(TimeFilterTypeEnum.StartTime)) {
                sort = " order by ae.startTime desc ";
//                sort = " order by ae.startTime desc, ap.tag asc, ae.alarmFlagId asc ";
            } else {
                sort = " order by ae.alarmTime desc ";
//                sort = " order by ae.alarmTime desc, ap.tag asc, ae.alarmFlagId asc ";
            }
            // 获取hql
            if (dateList == null || dateList.size() == 0) {
                hql.append(this.getQueryStringForAlarmEvent(unitCodes, prdtCellIds, null, timeType, startTime, endTime, eventTypeIds, null, craftRank, alarmPointTag, priority,  monitorType,prioritys, paramList, isMatching));
                // 过滤报警点标识
                if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                    //hql.append("and ae.alarmFlagId in (:alarmFlagIds) ");
                    for (int i = 0; i < alarmFlagIds.length; i++) {
                        if (alarmFlagIds[i] == (-9L)) {
                            hql.append(" and ( ae.alarmFlagId in (:alarmFlagIds) or ae.alarmFlagId is null) ");
                            break;
                        }
                        if (i == alarmFlagIds.length - 1) {
                            hql.append(" and  ae.alarmFlagId in (:alarmFlagIds) ");
                        }
                    }
                    paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
                }
            } else {
                this.saveDateRangeList(dateList);
                hql.append(this.getQueryStringForAlarmEvent(unitCodes, prdtCellIds, null, timeType, startTime, endTime, eventTypeIds, null, craftRank, alarmPointTag, priority, monitorType, prioritys, paramList, isMatching));

                if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                    for (int i = 0; i < alarmFlagIds.length; i++) {
                        if (alarmFlagIds[i] == (-9L)) {
                            hql.append(" and ( ae.alarmFlagId in (:alarmFlagIds) or ae.alarmFlagId is null) ");
                            break;
                        }
                        if (i == alarmFlagIds.length - 1) {
                            hql.append(" and  ae.alarmFlagId in (:alarmFlagIds) ");
                        }
                    }

                    paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
                }
//                if (timeType.equals(TimeFilterTypeEnum.StartTime)) {
//                    hql.append(" and exists(select dr from  DateRange dr where ae.startTime >=dr.startTime and ae.startTime<dr.endTime and dr.status = :status) ");
//                } else {
//                    hql.append(" and exists(select dr from  DateRange dr where ae.alarmTime >=dr.startTime and ae.alarmTime<dr.endTime and dr.status = :status) ");
//                }
//                paramList.put("status", 1);
                // 过滤日期List<DateRange> dateList
                for (int i=0;i<dateList.size();i++){
                    if (i>0){
                        hql.append(" or ");
                    }else {
                        hql.append("and (");
                    }
                    if (timeType.equals(TimeFilterTypeEnum.StartTime)) {
                        hql.append(" ae.startTime >= :beginTime");
                        hql.append(String.valueOf(i));
                        hql.append(" and ae.startTime < :endTime");
                        hql.append(String.valueOf(i));
                    }else {
                        hql.append(" ae.alarmTime >= :beginTime");
                        hql.append(String.valueOf(i));
                        hql.append(" and ae.alarmTime < :endTime");
                        hql.append(String.valueOf(i));
                    }
                    paramList.put("beginTime"+String.valueOf(i), dateList.get(i).getStartTime());
                    paramList.put("endTime"+String.valueOf(i), dateList.get(i).getEndTime());
                }
                hql.append(") ");
            }
            hql.append(sort);
            PaginationBean<AlarmEvent> rt =this.findAll(page, hql.toString(), paramList);
            return rt;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 获取报警事件分页数据
     *
     * @param unitCodes     装置编码数组
     * @param prdtCellIds   生产单元ID数组
     * @param alarmPointTag 报警点位号
     * @param alarmFlagId   报警标识ID
     * @param startTime     发生时间范围起始
     * @param endTime       发生时间范围结束
     * @param page          分页参数
     * @return 报警事件数据
     * @throws Exception
     * <AUTHOR> 2018-03-09
     */
    public PaginationBean<AlarmEvent> getAlarmEvent(String[] unitCodes, Long[] prdtCellIds, String alarmPointTag,
                                                    Long alarmFlagId, Date startTime, Date endTime, Pagination page) throws Exception {
        try {
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 查询字符串
            StringBuilder hql = new StringBuilder("select ae from AlarmEvent ae ");

            StringBuilder hqlWhere = new StringBuilder("where 1=1 and ae.eventTypeId=:eventTypeId  ");
            paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());

            // 关联报警标识
            hql.append("inner join fetch ae.alarmFlag af ");
            // 关联生产单元
            hql.append("inner join fetch ae.prdtCell pc ");

            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hqlWhere.append("and ae.unitCode in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            // 过滤生产单元
            if (ArrayUtils.isNotEmpty(prdtCellIds)) {
                hqlWhere.append("and pc.prdtCellId in (:prdtCellIds) ");
                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
            }
            //过滤位号
            if (!StringUtils.isEmpty(alarmPointTag)) {
                hqlWhere.append("  and (upper(ap.tag) like upper(:tag) escape '/') ");
                paramList.put("tag", "%" + this.sqlLikeReplace(alarmPointTag) + "%");
            }
            // 过滤报警点标识
            if (alarmFlagId != null) {
                hqlWhere.append("and ae.alarmFlagId=:alarmFlagId ");
                paramList.put("alarmFlagId", alarmFlagId);
            }
            // 过滤日期
            if (startTime != null && endTime != null) {
                hqlWhere.append("and ae.alarmTime between :beginTime and :endTime ");

                paramList.put("beginTime", startTime);
                paramList.put("endTime", endTime);
            }

            hql = hql.append(hqlWhere).append(" order by ae.alarmTime desc ");

            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("查询异常" + e);
        }
    }


    /**
     * 获取坏点分布分页数据
     *
     * @param unitCodes     装置编码数组
     * @param prdtCellIds   生产单元id数组
     * @param tag           位号
     * @param priority      优先级
     * @param beginTime     报警事件的开始间
     * @param endTime       报警事件的结束时间
     * @param dateRangeList 日期区间列表
     * @param page          翻页实现类
     * @return PaginationBean<AlarmEvent> 返回AlarmEvent实体分页对象
     * @throws Exception 
     * <AUTHOR> 2017-10-23
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    @Transactional
    @Deprecated
    public PaginationBean<AlarmEvent> getAlarmBadpvDistribution(String[] unitCodes, Long[] prdtCellIds, String tag,
                                                                Integer priority, Date beginTime, Date endTime, List dateRangeList, Pagination page) throws Exception {
        try {
            List<DateRange> dateList = ObjectConverter.listConverter(dateRangeList, DateRange.class);
            // 参数集合
            Map<String, Object> paramList = new HashMap<>();
            String hql = "";
            if (dateList == null || dateList.size() == 0) {
                hql = getQueryString(unitCodes, prdtCellIds, null, TimeFilterTypeEnum.ALarmTime, beginTime, endTime, new Long[]{CommonEnum.EventTypeEnum.ProcessEvent.getIndex()}, null, null, tag, priority, paramList);
                hql += "and af.alarmFlagId in (:alarmFlagIds) order by ae.alarmTime desc ";
            } else {
                this.saveDateRangeList(dateList);
                hql = getQueryString(unitCodes, prdtCellIds, null, TimeFilterTypeEnum.ALarmTime, null, null, new Long[]{CommonEnum.EventTypeEnum.ProcessEvent.getIndex()}, null, null, tag, priority, paramList);
                hql += "and af.alarmFlagId in (:alarmFlagIds) and exists(select dr from  DateRange dr where ae.alarmTime >=dr.startTime and ae.alarmTime<dr.endTime) order by ae.alarmTime desc ";
            }
            List<Long> alarmFlagIdsList = new ArrayList<>();
            alarmFlagIdsList.add((long) CommonEnum.AlarmFlagEnum.BADOC.getIndex());
            alarmFlagIdsList.add((long) CommonEnum.AlarmFlagEnum.BADPV.getIndex());
            alarmFlagIdsList.add((long) CommonEnum.AlarmFlagEnum.IOP.getIndex());
            alarmFlagIdsList.add((long) CommonEnum.AlarmFlagEnum.IOP_.getIndex());
            paramList.put("alarmFlagIds", alarmFlagIdsList);
            // 调用基类方法查询返回结果
            PaginationBean<AlarmEvent> bean = this.findAll(page, hql, paramList);
            return bean;
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 分页获取数据
     * <p>
     *  * <AUTHOR> 2018-11-19
     *
     * @param unitCodes   装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param tag         位号
     * @param beginTime   报警事件的开始间
     * @param endTime     报警事件的结束时间
     * @param page        翻页实现类
     * @return PaginationBean<Object [ ]> 返回分页对象
     * @throws Exception 
     */
    @Override
    public PaginationBean<Object[]> getAlarmBadpvDistribution(String[] unitCodes, Long[] prdtCellIds, String tag, Date beginTime, Date endTime, Pagination page) throws Exception {
        //根据查询条件查询<报警事件><报警点>数据，过滤“事件类型”为“1001（过程报警）”
        // 、“报警标识ID”为“9”（BADOC）或“10”（BADPV）或（16）IOP或（12）IOP-，
        // 按照<报警事件>“报警点ID”分组，根据“坏点次数”倒序、位号正序排列展示在网格列表中。
        try {
            Map<String, Object> paramList = new HashMap<>();
            StringBuilder hql = new StringBuilder("select ae.alarmPointId,max(pc.unitId),max(pc.sname),max(ap.tag),max(ap.location),count(*) from AlarmEvent ae ");
            // 关联报警点
            hql.append(" inner join ae.alarmPoint ap ");
            // 关联报警标识
            hql.append(" inner join ae.alarmFlag af ");
            // 关联生产单元
            hql.append(" inner join ap.prdtCell pc ");
            // 关联报警事件
            hql.append(" inner join ae.eventType et ");
            hql.append(" where  ae.priority is not null and ae.companyId=:companyId ");
            //启用的报警点
            hql.append(" and ap.inUse =1 ");
            hql.append(" and ae.eventTypeId = " + EventTypeEnum.ProcessEvent.getIndex() + " ");
            hql.append(" and af.alarmFlagId in (:alarmFlagIds) ");
            List<Long> alarmFlagIdsList = new ArrayList<>();
            alarmFlagIdsList.add((long) CommonEnum.AlarmFlagEnum.BADOC.getIndex());
            alarmFlagIdsList.add((long) CommonEnum.AlarmFlagEnum.BADPV.getIndex());
            alarmFlagIdsList.add((long) CommonEnum.AlarmFlagEnum.IOP.getIndex());
            alarmFlagIdsList.add((long) CommonEnum.AlarmFlagEnum.IOP_.getIndex());
            paramList.put("alarmFlagIds", alarmFlagIdsList);
            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append("and pc.unitId in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            // 过滤生产单元
            if (ArrayUtils.isNotEmpty(prdtCellIds)) {
                hql.append("and pc.prdtCellId in (:prdtCellIds) ");
                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
            }
            //过滤位号
            if (!StringUtils.isEmpty(tag)) {
                hql.append("  and (upper(ap.tag) like upper(:tag) escape '/') ");
                paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
            }
            // 过滤日期
            if (beginTime != null && endTime != null) {
                hql.append("and ae.alarmTime between :beginTime and :endTime ");
                paramList.put("beginTime", beginTime);
                paramList.put("endTime", endTime);
            }
            hql.append(" group by ae.alarmPointId order by count(*) desc,max(ap.tag)");
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());

            Pattern r = Pattern.compile("(.*)\\s+order by.*?", Pattern.CASE_INSENSITIVE);
            Matcher m = r.matcher(hql);
            String countSql = m.matches() ? m.group(1).trim() : hql.toString();
            Pattern r1 = Pattern.compile(".*?from\\s(.*)", Pattern.CASE_INSENSITIVE);
            Matcher m1 = r1.matcher(countSql);
            countSql = m1.matches() ? m1.group(1).trim() : hql.toString();
            countSql = "SELECT COUNT(*) FROM  " + countSql;
            TypedQuery<Long> countQuery = getEntityManager().createQuery(countSql, Long.class);
            this.setParameterList(countQuery, paramList);
            List<Long> countList = countQuery.getResultList();
            PaginationBean resultList = new PaginationBean(page, Long.parseLong(countList.size() + ""));
            Query query = getEntityManager().createQuery(hql.toString());
            this.setParameterList(query, paramList);
            query.setFirstResult(resultList.getBeginIndex()).setMaxResults(resultList.getPageSize());
            List resultList1 = query.getResultList();
            resultList.setPageList(resultList1);
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 分页获取详情数据
     * <p>
     *  * <AUTHOR> 2018-11-19
     *
     * @param alarmPointId 报警点id
     * @param beginTime    报警事件的开始间
     * @param endTime      报警事件的结束时间
     * @param page         翻页实现类
     * @return PaginationBean<AlarmEvent> 返回AlarmEvent分页对象
     * @throws Exception 
     */
    @Override
    public PaginationBean<AlarmEvent> getAlarmBadpvDistributionDtl(Long alarmPointId, Date beginTime, Date endTime, Pagination page) throws Exception {
        //根据查询条件查询<报警事件><报警点>数据，过滤“事件类型”为“1001（过程报警）”、
        // “报警标识ID”为“9”（BADOC）或“10”（BADPV）或（16）IOP或（12）IOP-，
        // 根据“报警时间”倒序、“位号”+“报警标识”正序排列展示在网格列表中。
        try {
            Map<String, Object> paramList = new HashMap<>();
            // 查询字符串
            StringBuilder hql = new StringBuilder("select ae from AlarmEvent ae ");
            // 关联报警点
            hql.append("inner join fetch ae.alarmPoint ap ");
            // 关联报警标识
            hql.append("inner join fetch ae.alarmFlag af ");
            // 关联报警事件类型
            hql.append("inner join fetch ae.eventType et where ae.companyId=:companyId and ae.priority is not null and ae.eventTypeId = " + EventTypeEnum.ProcessEvent.getIndex() + " ");
            //启用的报警点
            hql.append(" and ap.inUse =1 ");
            hql.append(" and af.alarmFlagId in (:alarmFlagIds) ");
            List<Long> alarmFlagIdsList = new ArrayList<>();
            alarmFlagIdsList.add((long) CommonEnum.AlarmFlagEnum.BADOC.getIndex());
            alarmFlagIdsList.add((long) CommonEnum.AlarmFlagEnum.BADPV.getIndex());
            alarmFlagIdsList.add((long) CommonEnum.AlarmFlagEnum.IOP.getIndex());
            alarmFlagIdsList.add((long) CommonEnum.AlarmFlagEnum.IOP_.getIndex());
            paramList.put("alarmFlagIds", alarmFlagIdsList);
            hql.append(" and ae.alarmPointId = :alarmPointId");
            paramList.put("alarmPointId", alarmPointId);
            // 过滤日期
            if (beginTime != null && endTime != null) {
                hql.append(" and ae.alarmTime between :beginTime and :endTime ");
                paramList.put("beginTime", beginTime);
                paramList.put("endTime", endTime);
            }
            hql.append(" order by ae.alarmTime desc, ap.tag,af.name");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            PaginationBean<AlarmEvent> bean = this.findAll(page, hql.toString(), paramList);
            return bean;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 获取报警数量前20数据
     *
     * @param unitCodes     装置编码数组
     * @param prdtCellIds   生产单元id数组
     * @param wokrUnitCodes 工厂下装置编码数组
     * @param startTime     查询时间范围起始
     * @param endTime       查询时间范围结束
     * @param startFlag     开始时间标识
     * @param endFlag       结束时间标识
     * @param topType       Top20,Top10切换选择
     * @return 报警数量前20数据
     * @throws Exception 
     * <AUTHOR> 2017-10-30
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<Object[]> getAlarmNumberTop20(String[] unitCodes, Long[] prdtCellIds, String[] wokrUnitCodes, Date startTime, Date endTime, String startFlag, String endFlag, Integer topType) {
        try {
            // 查询字符串
            String statisticsHql = "select min(ap.tag),min(af.name),count(ap.alarmPointId),min(ae.priority),min(pc.name),pc.unitId,ap.alarmPointId,ae.alarmFlagId,min(ap.location) ";
            StringBuilder hql = new StringBuilder(" from AlarmEvent ae"
                    + " inner join ae.alarmPoint ap"
                    + " inner join ae.prdtCell pc"
                    + " inner join ae.alarmFlag af"
                    + " where ap.inUse = 1 "
                    + "and ae.eventTypeId=" + EventTypeEnum.ProcessEvent.getIndex()
                    + " and ae.alarmTime " + startFlag + " :startTime and ae.alarmTime " + endFlag + ":endTime ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
            if (unitCodes != null && unitCodes.length > 0) {
                hql.append(" and ae.unitCode in (:unitIds)");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            if (prdtCellIds != null && prdtCellIds.length > 0) {
                hql.append(" and ae.prdtCellId in (:prdtCellIds)");
                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
            }
            if (wokrUnitCodes != null && wokrUnitCodes.length > 0) {
                hql.append(" and ae.unitCode in (:workUnitIds)");
                paramList.put("workUnitIds", Arrays.asList(wokrUnitCodes));
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
//            paramList.put("companyId", commonProperty.getCompanyId());
            String groupByhql = " group by ae.unitCode,ae.prdtCellId,ae.alarmPointId,ae.alarmFlagId order by count(ae.alarmPointId) desc";
            Query query = getEntityManager().createQuery(statisticsHql + hql.toString() + groupByhql);
            this.setParameterList(query, paramList);
            query.setFirstResult(0).setMaxResults(topType);
            return query.getResultList();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 获取报警数量前20数据
     *
     * @param unitCodes     装置编码数组
     * @param prdtCellIds   生产单元id数组
     * @param wokrUnitCodes 工厂下装置编码数组
     * @param startTime     查询时间范围起始
     * @param endTime       查询时间范围结束
     * @param startFlag     开始时间标识
     * @param endFlag       结束时间标识
     * @param topType       Top20,Top10切换选择
     * @return 报警数量前20数据
     * @throws Exception 
     * <AUTHOR> 2019-09-30
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<Object[]> getAlarmNumberTop20(String[] unitCodes, Long[] alarmFlagIds, Long[] prdtCellIds, String[] wokrUnitCodes, Date startTime, Date endTime, String startFlag, String endFlag, Integer topType, Integer[] priority, Boolean priorityFlag, Integer isElimination) {
        try {
            // 查询字符串
            String statisticsHql = "select min(ap.tag),min(af.name),count(ap.alarmPointId),min(ae.priority)" +
                    ",min(pc.name),pc.unitId,ap.alarmPointId,ae.alarmFlagId,min(ap.location), ap.monitorType";
            StringBuilder hql = new StringBuilder(" from AlarmEvent ae"
                    + " inner join ae.alarmPoint ap"
                    + " inner join ap.prdtCell pc"
                    + " inner join ae.alarmFlag af"
                    + " where ae.eventTypeId=" + EventTypeEnum.ProcessEvent.getIndex()
                    + " and ae.companyId=:companyId and ae.alarmTime " + startFlag + " :startTime and ae.alarmTime " + endFlag + ":endTime "
                    + " and case when ae.alarmPointId is not null then ap.inUse else 1  end =1 "
            );

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            //开始过滤需要剔除的数据
//            if (isElimination == 0){
//                List<AlarmPointDelConfig> alarmPointDelConfigs = alarmPointDelConfigRepository.selectAlarmPointDelConfigDetail(unitCodes, startTime, endTime);
//                //查询剔除配置信息
//                for (AlarmPointDelConfig a : alarmPointDelConfigs) {
//                    hql.append(" and ( ");
//                    if (a.getAlarmPointId()!=""){
//                        hql.append("ap.alarmPointId not in (").append((a.getAlarmPointId())).append(") ");
//                        hql.append(" or ");
//                    }
//                    //过滤报警标识
//                    if (!a.getAlarmFlagId().equals("")){
//                        hql.append("af.alarmFlagId not in (").append((a.getAlarmFlagId())).append(") ");
//                        hql.append(" or ");
//                    }
//                    //过滤时间
//                    hql.append("ae.alarmTime not between '").append(sdf.format(a.getDelStartTime())).append("' and '").append(sdf.format(a.getDelEndTime())).append("' ");
//                    hql.append(" ) ");
//                }
//
//            }


            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
            if (unitCodes != null && unitCodes.length > 0) {
                hql.append(" and pc.unitId in (:unitIds)");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            if (prdtCellIds != null && prdtCellIds.length > 0) {
                hql.append(" and ap.prdtCellId in (:prdtCellIds)");
                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
            }
            if (wokrUnitCodes != null && wokrUnitCodes.length > 0) {
                hql.append(" and pc.unitId in (:workUnitIds)");
                paramList.put("workUnitIds", Arrays.asList(wokrUnitCodes));
            }
            // 过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ae.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ae.priority is null ");
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarmFlagId in (:alarmFlagIds) or ae.alarmFlagId is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarmFlagId in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            Integer companyId = new CommonProperty().getCompanyId();
            //如果获取不到企业id则根据装置获取企业id
            if (companyId != null) {
                paramList.put("companyId", companyId);
            } else {
                paramList.put("companyId", getCompanyIdByUnit(unitCodes));
            }
            String groupByhql = " group by pc.unitId,ap.prdtCellId,ap.alarmPointId,ae.alarmFlagId,ap.monitorType order by count(ap.alarmPointId) desc,min(ap.tag) asc,min(af.name) asc";
            Query query = getEntityManager().createQuery(statisticsHql + hql.toString() + groupByhql);
            this.setParameterList(query, paramList);
            query.setFirstResult(0).setMaxResults(topType);
            return query.getResultList();
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List getAlarmNumberAll(String[] workshopCodes,String[] unitCode,Long[] prdtCellId,String[] unitCodes, Long[] alarmFlagIds, Long[] prdtCellIds, String[] wokrUnitCodes, Date startTime, Date endTime, String startFlag, String endFlag, Integer topType, Integer[] priority, Boolean priorityFlag, Integer isElimination,Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("select min(ap.tag),min(af.name),count(ap.alarm_point_id),min(ae.priority),min(pc.name),pc.unit_code,ap.alarm_point_id,ae.alarm_flag_id,min(ap.location) from t_ad_alarmevent ae"
                    + " left join t_pm_alarmpoint ap on ae.alarm_point_id = ap.alarm_point_id "
                    + " inner join t_pm_prdtcell pc on ap.prdtcell_id = pc.prdtcell_id "
                    + " left join t_ad_alarmflag af on ae.alarm_flag_id = af.alarm_flag_id"
                    + " inner join t_pm_unit ut on ae.unit_code = ut.std_code"
                    + " inner join t_pm_workshop w on w.workshop_id  = ut.workshop_id"
                    + " where ae.event_type_id=" + EventTypeEnum.ProcessEvent.getIndex()
                    + " and ae.company_id=:companyId and ae.alarm_time " + startFlag + " :startTime and ae.alarm_time " + endFlag + ":endTime "
                    + " and case when ae.alarm_point_id is not null then ap.in_use else 1  end =1 "
            );

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();

            if (workshopCodes != null && workshopCodes.length > 0) {
                hql.append(" and w.std_code in (:workshopCodes)");
                paramList.put("workshopCodes", Arrays.asList(workshopCodes));
            }
            if (unitCode != null && unitCode.length > 0) {
                hql.append(" and pc.unit_code in (:unitCode)");
                paramList.put("unitCode", Arrays.asList(unitCode));
            }
            if (prdtCellId != null && prdtCellId.length > 0) {
                hql.append(" and ap.prdtcell_id in (:prdtCellId)");
                paramList.put("prdtCellId", Arrays.asList(prdtCellId));
            }

            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
//            if (unitCodes != null && unitCodes.length > 0) {
//                hql.append(" and pc.unit_code in (:unitIds)");
//                paramList.put("unitIds", Arrays.asList(unitCodes));
//            }
//            if (prdtCellIds != null && prdtCellIds.length > 0) {
//                hql.append(" and ap.prdtcell_id in (:prdtCellIds)");
//                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
//            }
//            if (wokrUnitCodes != null && wokrUnitCodes.length > 0) {
//                hql.append(" and pc.unit_code in (:workUnitIds)");
//                paramList.put("workUnitIds", Arrays.asList(wokrUnitCodes));
//            }
            // 过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ae.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ae.priority is null ");
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            Integer companyId = new CommonProperty().getCompanyId();
            //如果获取不到企业id则根据装置获取企业id
            if (companyId != null) {
                paramList.put("companyId", companyId);
            } else {
                paramList.put("companyId", getCompanyIdByUnit(unitCodes));
            }
            hql.append(" group by pc.unit_code,ap.prdtcell_id,ap.alarm_point_id,ae.alarm_flag_id ");
            hql.append(" order by count(ap.alarm_point_id) desc,min(ap.tag) asc,min(af.name) asc ")  ;
            if("oracle".equals(dbConfig.getDataBase())){
                hql.append(        ") tt\n"+
                        "         WHERE ROWNUM <= :big) table_alias\n" +
                        " WHERE table_alias.rowno > :smail");
                paramList.put("big", page.getPageNumber()*page.getPageSize());
                paramList.put("smail", (page.getPageNumber()-1)*page.getPageSize());
            }else{
                hql.append(        " limit :smail , :big" );
                paramList.put("big", page.getPageSize());
                paramList.put("smail", (page.getPageNumber()-1)*page.getPageSize());
            }
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List getAlarmNumberAllTotal(String[] workshopCodes, String[] unitCode, Long[] prdtCellId, String[] unitCodes, Long[] alarmFlagIds, Long[] prdtCellIds, String[] wokrUnitCodes, Date startTime, Date endTime, String startFlag, String endFlag, Integer topType, Integer[] priority, Boolean priorityFlag, Integer isElimination) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("select min(ap.tag),min(af.name),count(ap.alarm_point_id),min(ae.priority),min(pc.name),pc.unit_code,ap.alarm_point_id,ae.alarm_flag_id,min(ap.location) from t_ad_alarmevent ae"
                    + " left join t_pm_alarmpoint ap on ae.alarm_point_id = ap.alarm_point_id "
                    + " inner join t_pm_prdtcell pc on ap.prdtcell_id = pc.prdtcell_id "
                    + " left join t_ad_alarmflag af on ae.alarm_flag_id = af.alarm_flag_id"
                    + " inner join t_pm_unit ut on ae.unit_code = ut.std_code"
                    + " inner join t_pm_workshop w on w.workshop_id  = ut.workshop_id"
                    + " where ae.event_type_id=" + EventTypeEnum.ProcessEvent.getIndex()
                    + " and ae.company_id=:companyId and ae.alarm_time " + startFlag + " :startTime and ae.alarm_time " + endFlag + ":endTime "
                    + " and case when ae.alarm_point_id is not null then ap.in_use else 1  end =1 "
            );

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();

            if (workshopCodes != null && workshopCodes.length > 0) {
                hql.append(" and w.std_code in (:workshopCodes)");
                paramList.put("workshopCodes", Arrays.asList(workshopCodes));
            }
            if (unitCode != null && unitCode.length > 0) {
                hql.append(" and pc.unit_code in (:unitCode)");
                paramList.put("unitCode", Arrays.asList(unitCode));
            }
            if (prdtCellId != null && prdtCellId.length > 0) {
                hql.append(" and ap.prdtcell_id in (:prdtCellId)");
                paramList.put("prdtCellId", Arrays.asList(prdtCellId));
            }

            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
//            if (unitCodes != null && unitCodes.length > 0) {
//                hql.append(" and pc.unit_code in (:unitIds)");
//                paramList.put("unitIds", Arrays.asList(unitCodes));
//            }
//            if (prdtCellIds != null && prdtCellIds.length > 0) {
//                hql.append(" and ap.prdtcell_id in (:prdtCellIds)");
//                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
//            }
//            if (wokrUnitCodes != null && wokrUnitCodes.length > 0) {
//                hql.append(" and pc.unit_code in (:workUnitIds)");
//                paramList.put("workUnitIds", Arrays.asList(wokrUnitCodes));
//            }
            // 过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ae.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ae.priority is null ");
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            Integer companyId = new CommonProperty().getCompanyId();
            //如果获取不到企业id则根据装置获取企业id
            if (companyId != null) {
                paramList.put("companyId", companyId);
            } else {
                paramList.put("companyId", getCompanyIdByUnit(unitCodes));
            }
            hql.append(" group by pc.unit_code,ap.prdtcell_id,ap.alarm_point_id,ae.alarm_flag_id ");
            hql.append(" order by count(ap.alarm_point_id) desc,min(ap.tag) asc,min(af.name) asc ")  ;

            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }
    @Override
    public List getAlarmNumberAllType(String[] workshopCodes,String[] unitCode,Long[] prdtCellId,String[] unitCodes, Long[] alarmFlagIds, Long[] prdtCellIds, String[] wokrUnitCodes, Date startTime, Date endTime, String startFlag, String endFlag, Integer topType, Integer[] monitorType, Boolean priorityFlag, Integer isElimination,Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("select min(ap.tag),min(af.name),count(ap.alarm_point_id),min(ap.monitor_type ),min(pc.name),pc.unit_code,ap.alarm_point_id,ae.alarm_flag_id,min(ap.location) from t_ad_alarmevent ae"
                    + " left join t_pm_alarmpoint ap on ae.alarm_point_id = ap.alarm_point_id "
                    + " inner join t_pm_prdtcell pc on ap.prdtcell_id = pc.prdtcell_id "
                    + " left join t_ad_alarmflag af on ae.alarm_flag_id = af.alarm_flag_id"
                    + " inner join t_pm_unit ut on ae.unit_code = ut.std_code"
                    + " inner join t_pm_workshop w on w.workshop_id  = ut.workshop_id"
                    + " where ae.event_type_id=" + EventTypeEnum.ProcessEvent.getIndex()
                    + " and ae.company_id=:companyId and ae.alarm_time " + startFlag + " :startTime and ae.alarm_time " + endFlag + ":endTime "
                    + " and case when ae.alarm_point_id is not null then ap.in_use else 1  end =1 "
            );

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();

            if (workshopCodes != null && workshopCodes.length > 0) {
                hql.append(" and w.std_code in (:workshopCodes)");
                paramList.put("workshopCodes", Arrays.asList(workshopCodes));
            }
            if (unitCode != null && unitCode.length > 0) {
                hql.append(" and pc.unit_code in (:unitCode)");
                paramList.put("unitCode", Arrays.asList(unitCode));
            }
            if (prdtCellId != null && prdtCellId.length > 0) {
                hql.append(" and ap.prdtcell_id in (:prdtCellId)");
                paramList.put("prdtCellId", Arrays.asList(prdtCellId));
            }

            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
//            if (unitCodes != null && unitCodes.length > 0) {
//                hql.append(" and pc.unit_code in (:unitIds)");
//                paramList.put("unitIds", Arrays.asList(unitCodes));
//            }
//            if (prdtCellIds != null && prdtCellIds.length > 0) {
//                hql.append(" and ap.prdtcell_id in (:prdtCellIds)");
//                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
//            }
//            if (wokrUnitCodes != null && wokrUnitCodes.length > 0) {
//                hql.append(" and pc.unit_code in (:workUnitIds)");
//                paramList.put("workUnitIds", Arrays.asList(wokrUnitCodes));
//            }
            // 过滤专业
            if (monitorType != null && !priorityFlag) {
                hql.append(" and ap.monitor_type  in (:monitorType) ");
                paramList.put("monitorType", Arrays.asList(monitorType));
            }
            if (monitorType != null && priorityFlag) {
                hql.append(" and (ap.monitor_type  in (:monitorType) or ap.monitor_type  is null)");
                paramList.put("monitorType", Arrays.asList(monitorType));
            }
            if (monitorType == null && priorityFlag) {
                hql.append(" and ap.monitor_type  is null ");
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            Integer companyId = new CommonProperty().getCompanyId();
            //如果获取不到企业id则根据装置获取企业id
            if (companyId != null) {
                paramList.put("companyId", companyId);
            } else {
                paramList.put("companyId", getCompanyIdByUnit(unitCodes));
            }
            hql.append(" group by pc.unit_code,ap.prdtcell_id,ap.alarm_point_id,ae.alarm_flag_id ");
            hql.append(" order by count(ap.alarm_point_id) desc,min(ap.tag) asc,min(af.name) asc ")  ;
            if("oracle".equals(dbConfig.getDataBase())){
                hql.append(        ") tt\n"+
                        "         WHERE ROWNUM <= :big) table_alias\n" +
                        " WHERE table_alias.rowno > :smail");
                paramList.put("big", page.getPageNumber()*page.getPageSize());
                paramList.put("smail", (page.getPageNumber()-1)*page.getPageSize());
            }else{
                hql.append(        " limit :smail , :big" );
                paramList.put("big", page.getPageSize());
                paramList.put("smail", (page.getPageNumber()-1)*page.getPageSize());
            }
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List getAlarmNumberAllTotalType(String[] workshopCodes, String[] unitCode, Long[] prdtCellId, String[] unitCodes, Long[] alarmFlagIds, Long[] prdtCellIds, String[] wokrUnitCodes, Date startTime, Date endTime, String startFlag, String endFlag, Integer topType, Integer[] monitorType, Boolean priorityFlag, Integer isElimination) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("select min(ap.tag),min(af.name),count(ap.alarm_point_id),min(ap.monitor_type),min(pc.name),pc.unit_code,ap.alarm_point_id,ae.alarm_flag_id,min(ap.location) from t_ad_alarmevent ae"
                    + " left join t_pm_alarmpoint ap on ae.alarm_point_id = ap.alarm_point_id "
                    + " inner join t_pm_prdtcell pc on ap.prdtcell_id = pc.prdtcell_id "
                    + " left join t_ad_alarmflag af on ae.alarm_flag_id = af.alarm_flag_id"
                    + " inner join t_pm_unit ut on ae.unit_code = ut.std_code"
                    + " inner join t_pm_workshop w on w.workshop_id  = ut.workshop_id"
                    + " where ae.event_type_id=" + EventTypeEnum.ProcessEvent.getIndex()
                    + " and ae.company_id=:companyId and ae.alarm_time " + startFlag + " :startTime and ae.alarm_time " + endFlag + ":endTime "
                    + " and case when ae.alarm_point_id is not null then ap.in_use else 1  end =1 "
            );

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();

            if (workshopCodes != null && workshopCodes.length > 0) {
                hql.append(" and w.std_code in (:workshopCodes)");
                paramList.put("workshopCodes", Arrays.asList(workshopCodes));
            }
            if (unitCode != null && unitCode.length > 0) {
                hql.append(" and pc.unit_code in (:unitCode)");
                paramList.put("unitCode", Arrays.asList(unitCode));
            }
            if (prdtCellId != null && prdtCellId.length > 0) {
                hql.append(" and ap.prdtcell_id in (:prdtCellId)");
                paramList.put("prdtCellId", Arrays.asList(prdtCellId));
            }

            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
//            if (unitCodes != null && unitCodes.length > 0) {
//                hql.append(" and pc.unit_code in (:unitIds)");
//                paramList.put("unitIds", Arrays.asList(unitCodes));
//            }
//            if (prdtCellIds != null && prdtCellIds.length > 0) {
//                hql.append(" and ap.prdtcell_id in (:prdtCellIds)");
//                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
//            }
//            if (wokrUnitCodes != null && wokrUnitCodes.length > 0) {
//                hql.append(" and pc.unit_code in (:workUnitIds)");
//                paramList.put("workUnitIds", Arrays.asList(wokrUnitCodes));
//            }
            // 过滤专业
            if (monitorType != null && !priorityFlag) {
                hql.append(" and ap.monitor_type in (:monitorType) ");
                paramList.put("monitorType", Arrays.asList(monitorType));
            }
            if (monitorType != null && priorityFlag) {
                hql.append(" and (ap.monitor_type in (:monitorType) or ap.monitor_type is null)");
                paramList.put("monitorType", Arrays.asList(monitorType));
            }
            if (monitorType == null && priorityFlag) {
                hql.append(" and ap.monitor_type is null ");
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            Integer companyId = new CommonProperty().getCompanyId();
            //如果获取不到企业id则根据装置获取企业id
            if (companyId != null) {
                paramList.put("companyId", companyId);
            } else {
                paramList.put("companyId", getCompanyIdByUnit(unitCodes));
            }
            hql.append(" group by pc.unit_code,ap.prdtcell_id,ap.alarm_point_id,ae.alarm_flag_id ");
            hql.append(" order by count(ap.alarm_point_id) desc,min(ap.tag) asc,min(af.name) asc ")  ;

            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 报警数量评估——趋势图数据
     *
     * @param ids            ID数组（装置或单元）
     * @param queryType      查询类型（装置或单元）
     * @param dateType       时间粒度
     * @param startTime      时间范围起始
     * @param endTime        时间范围结束
     * @param hours          校正时间
     * @param timeType       时间类型
     * @param name           名称
     * @param queryCondition 额外过滤语句
     * @return 报警数量评估趋势图实体集合
     * <AUTHOR> 2017-10-30
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<Object[]> getAlarmNumberAssessTrendEntity(String[] ids, CommonEnum.EquipmentTypeEnum queryType, CommonEnum.DateTypeEnum dateType,
                                                          Date startTime, Date endTime, Integer hours, String timeType, String name, String queryCondition) {
        try {
            //String selectTimeStr = getDateType(dateType, startTime, hours, timeType);
            String selectTimeStr = "";
            if ("oracle".equals(dbConfig.getDataBase())) {
                selectTimeStr = " to_char(" + timeType + " - NUMTODSINTERVAL(8, 'HOUR'), 'YYYY-MM')";
            } else {
                selectTimeStr = " date_format(subdate(" + timeType + " ,interval 8 HOUR), '%Y-%m-%d')";
            }
            StringBuilder hql = new StringBuilder(
                    " select count(*), '" + name + "', " + selectTimeStr
                            + " from t_ad_AlarmEvent ae "
                            + " inner join t_pm_alarmpoint ap  on ae.alarm_point_id=ap.alarm_point_id "
                            + " inner join t_pm_prdtcell pc on pc.prdtcell_id=ap.prdtcell_id "
                            + " inner join t_pm_eventType et   on et.event_type_id=ae.event_type_id "
                            + " where " + queryCondition + " "
                            + " and ae.priority is not null and ae.company_id=:companyId ");
            Map<String, Object> paramList = new HashMap<String, Object>();

            if (ids != null && ids.length > 0) {
                if (queryType.equals(CommonEnum.EquipmentTypeEnum.PrdtCell)) {
                    hql.append("and ap.prdtcell_id in (:ids) ");
                    List<Long> idList = new ArrayList<>();
                    for (String s : ids) {
                        idList.add(Long.parseLong(s));
                    }
                    paramList.put("ids", idList);
                } else if (queryType.equals(CommonEnum.EquipmentTypeEnum.Unit)) {
                    hql.append("and pc.unit_code in (:ids) ");
                    paramList.put("ids", Arrays.asList(ids));
                }
            }
            if (startTime != null && endTime != null) {
                hql.append("and (ae." + timeType + " between :startTime and :endTime) ");
                paramList.put("startTime", startTime);
                paramList.put("endTime", endTime);
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            hql.append("group by " + selectTimeStr + " order by " + selectTimeStr);
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultLst = query.getResultList();
            return resultLst;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 报警数量评估——趋势图数据
     *
     * @param ids       ID数组（装置或单元）
     * @param queryType 查询类型（装置或单元）
     * @param dateType  时间粒度
     * @param startTime 时间范围起始
     * @param endTime   时间范围结束
     * @param hours     校正时间
     * @param queList   额外过滤语句
     * @return 报警数量评估趋势图实体集合
     * <AUTHOR> 2019-12-2
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<Object[]> getAlarmNumberAssessTrendEntityByTypeQue(String[] ids, CommonEnum.EquipmentTypeEnum queryType, CommonEnum.DateTypeEnum dateType,
                                                                   Date startTime, Date endTime, Integer hours, Map<String, String> queList) {

        String dataBase = dbConfig.getDataBase();
        try {
            StringBuilder hql = new StringBuilder("select countt,name,timedate from (");
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (queList != null && queList.size() > 0) {
                for (String key : queList.keySet()) {
                    String timeType = key;
                    String name = "";
                    String queryCondition = queList.get(key);
                    if (timeType.equals("start_time")) name = "操作数";
                    else name = "报警数";
                    String selectTimeStr = getDateTypeBySQL(dateType, startTime, hours, timeType);
                    //String selectTimeStr = " to_char(" + timeType + " - NUMTODSINTERVAL(8, 'HOUR'), 'YYYY-MM')";
                    hql.append(new StringBuilder(
                            " select '" + name + "' as name, " + selectTimeStr + " as timedate, count(1) countt "
                                    + " from t_ad_AlarmEvent ae "
                                    + " inner join t_pm_alarmpoint ap  on ae.alarm_point_id=ap.alarm_point_id "
//                                    + " inner join t_pm_prdtcell pc on ae.prdtcell_id=ap.prdtcell_id "
//                                    + " inner join t_pm_eventType et   on et.event_type_id=ae.event_type_id "
                                    + " where " + queryCondition + " and ae.company_id=:companyId  "
//                                    + " and ( ae.alarm_point_id IS NOT NULL ) "
//                                    + " and ae.priority is not null "
                                    + " and ap.in_use = 1 "));
                    //企业
                    CommonProperty commonProperty = new CommonProperty();
                    paramList.put("companyId", commonProperty.getCompanyId());
                    if (ids != null && ids.length > 0) {
                        if (queryType.equals(CommonEnum.EquipmentTypeEnum.PrdtCell)) {
                            hql.append("and ae.prdtcell_id in (:ids) ");
                            List<Long> idList = new ArrayList<>();
                            for (String s : ids) {
                                idList.add(Long.parseLong(s));
                            }
                            paramList.put("ids", idList);
                        } else if (queryType.equals(CommonEnum.EquipmentTypeEnum.Unit)) {
                            hql.append("and ae.unit_code in (:ids) ");
                            paramList.put("ids", Arrays.asList(ids));
                        }
                    }
                    if (startTime != null && endTime != null) {
                        hql.append("and (ae." + timeType + " between :startTime and :endTime) ");
                        paramList.put("startTime", startTime);
                        paramList.put("endTime", endTime);
                    }

                    hql.append(" group by ").append(selectTimeStr);
                    hql.append("  union all ");
                }
                hql.delete(hql.length() - 11, hql.length());
            }
            if ("oracle".equals(dataBase)) {
                hql.append(" ) order by name,timedate");
            } else {
                hql.append(" ) as t order by name,timedate");
            }
//            hql.append(" ) group by name,timedate order by name,timedate");
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultLst = query.getResultList();
            return resultLst;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 报警数量评估——趋势图数据
     *
     * @param ids       ID数组（装置或单元）
     * @param queryType 查询类型（装置或单元）
     * @param dateType  时间粒度
     * @param startTime 时间范围起始
     * @param endTime   时间范围结束
     * @param hours     校正时间
     * @param queList   额外过滤语句
     * @return 报警数量评估趋势图实体集合
     * <AUTHOR> 2019-12-2
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<Object[]> getAlarmNumberAssessTrendEntityByTypeQue(String[] ids, Long[] alarmFlagIds, CommonEnum.EquipmentTypeEnum queryType, CommonEnum.DateTypeEnum dateType,
                                                                   Date startTime, Date endTime, Integer hours, Map<String, String> queList, Integer[] priority, Boolean priorityFlag, Integer isElimination) {
        try {
            StringBuilder hql = new StringBuilder("select count(*),name,timedate from (");
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (queList != null && queList.size() > 0) {
                for (String key : queList.keySet()) {
                    String timeType = key;
                    String name = "";
                    String queryCondition = queList.get(key);
                    if (timeType.equals("start_time")) name = "操作数";
                    else name = "报警数";
                    String selectTimeStr = getDateTypeBySQL(dateType, startTime, hours, timeType);
                    //String selectTimeStr = " to_char(" + timeType + " - NUMTODSINTERVAL(8, 'HOUR'), 'YYYY-MM')";
                    hql.append(new StringBuilder(
                            " select '" + name + "' as name, " + selectTimeStr + " as timedate"
                                    + " from t_ad_AlarmEvent ae "
                                    + " left join t_pm_alarmpoint ap  on ae.alarm_point_id=ap.alarm_point_id "
                                    + " inner join t_pm_prdtcell pc on pc.prdtcell_id=ap.prdtcell_id "
                                    + " inner join t_pm_eventType et   on et.event_type_id=ae.event_type_id "
                                    + " where " + queryCondition + "  and ae.company_id=:companyId "));
                    hql.append(" and case when ae.alarm_point_id is not null then ap.in_use  end =1 ");
                    //企业
                    CommonProperty commonProperty = new CommonProperty();
                    paramList.put("companyId", commonProperty.getCompanyId());
                    if (ids != null && ids.length > 0) {
                        hql.append("and ae.unit_code in (:ids) ");
                        paramList.put("ids", Arrays.asList(ids));
                    }
                    // 过滤优先级
                    if (priority != null && !priorityFlag) {
                        hql.append(" and ae.priority in (:priority) ");
                        paramList.put("priority", Arrays.asList(priority));
                    }// 过滤报警标识
                    if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                        for (int i = 0; i < alarmFlagIds.length; i++) {
                            if (alarmFlagIds[i] == (-9L)) {
                                hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                                break;
                            }
                            if (i == alarmFlagIds.length - 1) {
                                hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                            }
                        }
                        paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
                    }
                    if (priority != null && priorityFlag) {
                        hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
                        paramList.put("priority", Arrays.asList(priority));
                    }
                    if (priority == null && priorityFlag) {
                        hql.append(" and ae.priority is null ");
                    }
                    if (startTime != null && endTime != null) {
                        hql.append("and (ae." + timeType + " between :startTime and :endTime) ");
                        paramList.put("startTime", startTime);
                        paramList.put("endTime", endTime);
                    }
                    hql.append("  union all ");
                }
                hql.delete(hql.length() - 11, hql.length());
            }
            hql.append(" ) " + DbConversion.aliasName() + " group by name,timedate order by name,timedate");
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultLst = query.getResultList();
            return resultLst;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 根据装置ID集合和时间区间获取该装置的报警事件列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCodes 装置编码集合
     * @param prdtIds   单元ID集合
     * @param page      分页信息
     * @return 屏蔽的报警事件列表
     * <AUTHOR> 2017-11-9
     */
    @Override
    public PaginationBean<AlarmEvent> getAlarmEventListByUnitId(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, Pagination page) throws Exception {
        try {
            // 按照发生时间倒序排列，如果发生时间相同，按照位号、报警标识正序排列。
            Map<String, Object> paramList = new HashMap<>();
            String hql = getQueryString(unitCodes, prdtIds, null, TimeFilterTypeEnum.StartTime, startTime, endTime, new Long[]{1006l, 1003l, 1004l},
                    new Long[]{EventTypeEnum.OperateEvent.getIndex(), EventTypeEnum.ChangeEvent.getIndex()}, null, paramList) + " order by ae.startTime desc,ap.tag,af.name asc ";
            return this.findAll(page, hql, paramList);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 根据报警点ID和报警标识ID获取屏蔽的报警事件列表
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @param page         分页信息
     * @return 屏蔽的报警事件列表
     * <AUTHOR> 2017-10-30
     */
    public PaginationBean<AlarmEvent> getOperateDetail(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId, Pagination page) {
        try {
            String hql = "from AlarmEvent t" +
                    " where t.alarmPointId = :alarmPointId and t.companyId=:companyId " +
                    " and (t.eventType.parentId = 30 or t.eventTypeId = 1006)  " +
                    " and t.alarmFlagId = :alarmFlagId" +
                    " and t.startTime between :startTime and :endTime " +
                    " order by t.startTime desc";
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("alarmPointId", alarmPointId);
            paramList.put("alarmFlagId", alarmFlagId);
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 根据报警点ID和报警标识ID获取操作详情图表数据
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @param hourTime     配置时间
     * @return 操作详情图表数据
     * <AUTHOR> 2017-10-30
     */
    @SuppressWarnings("rawtypes")
    public List getChartOperateDetail(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId, String hourTime) {
        String hql = "";
        try {
            hql = "select t.alarmPointId,t.alarmFlagId,count(0) as times," +
                    "               case" +
                    "                 when t.startTime >=" +
                    "                      " + DbConversion.dateFunction() + "(" + DbConversion.toCharFunction() + "(t.startTime, '" + DbConversion.dateYmdFunction() + "') ||'" + hourTime + "','" + DbConversion.dateYmdhmsFunction() + "') then" +
                    "                      cast(" + DbConversion.dateFunction() + "(" + DbConversion.toCharFunction() + "(t.startTime, '" + DbConversion.dateYmdFunction() + "'), '" + DbConversion.dateYmdFunction() + "') as date)" +
                    "                 else" +
                    "                  cast((" + DbConversion.dateFunction() + "(" + DbConversion.toCharFunction() + "(t.startTime, '" + DbConversion.dateYmdFunction() + "'),'" + DbConversion.dateYmdFunction() + "') - 1)as date)" +
                    "                 end as startDate" +
                    "          from AlarmEvent t " +
                    "          where (t.eventType.parentId = 30 or t.eventTypeId=1006) and t.companyId=:companyId " +
                    "          and t.alarmPointId = :alarmPointId " +
                    "          and t.alarmFlagId = :alarmFlagId" +
                    "          and t.startTime between :startTime and :endTime" +
                    " group by alarmPointId, alarmFlagId, " +
                    "               case" +
                    "                 when t.startTime >=" +
                    "                      " + DbConversion.dateFunction() + "(" + DbConversion.toCharFunction() + "(t.startTime, '" + DbConversion.dateYmdFunction() + "') ||'" + hourTime + "','" + DbConversion.dateYmdhmsFunction() + "') then" +
                    "                      cast(" + DbConversion.dateFunction() + "(" + DbConversion.toCharFunction() + "(t.startTime, '" + DbConversion.dateYmdFunction() + "'), '" + DbConversion.dateYmdFunction() + "') as date)" +
                    "                 else" +
                    "                  cast((" + DbConversion.dateFunction() + "(" + DbConversion.toCharFunction() + "(t.startTime, '" + DbConversion.dateYmdFunction() + "'),'" + DbConversion.dateYmdFunction() + "') - 1) as date)" +
                    "                 end " +
                    " order by alarmPointId,startDate";

            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("alarmPointId", alarmPointId);
            paramList.put("alarmFlagId", alarmFlagId);
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createQuery(hql.toString());
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }


    /**
     * 根据报警点ID和报警标识ID获取报警详情平均值，最大值，最小值
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @return 报警详情平均值，最大值，最小值
     * <AUTHOR> 2017-11-08
     */
    @SuppressWarnings("rawtypes")
    public List getAlarmAvgData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId) {
        String hql = "select round(avg(CAST(t.nowValue AS double )),2) as avgValue," +
                "max(CAST(t.nowValue AS double )) as maxValue,min(CAST(t.nowValue AS double )) as minValue " +
                " from AlarmEvent t" +
                " where t.alarmPointId = :alarmPointId and t.companyId=:companyId and " +
                " t.alarmFlagId = :alarmFlagId and" +
                " t.eventTypeId = :eventTypeId and" +
                " t.alarmTime between :startTime and :endTime" +
                " and F_OPAL_ISNUMERIC(t.nowValue) =1";

        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("alarmPointId", alarmPointId);
        paramList.put("alarmFlagId", alarmFlagId);
        paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
//企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        Query query = getEntityManager().createQuery(hql.toString());
        this.setParameterList(query, paramList);
        return query.getResultList();
    }

    /**
     * 获取报警事件优先级评估页面统计数据
     *
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param unitCodes   装置编码数组
     * @param prdtCellIds 生产单元ID数组
     * <AUTHOR> 2017-11-21
     */
    @SuppressWarnings("rawtypes")
    @Override
    public List getAlarmPriorityAssessStatisticData(Date startTime, Date endTime, String[] unitCodes, Long[] prdtCellIds) {
        StringBuilder hql = new StringBuilder(
                " select count(ae.eventId) as total, "
                        + " sum(case when ae.priority = :emergency then 1 else 0 end) as emergency, "
                        + " sum(case when ae.priority = :important then 1 else 0 end) as important, "
                        + " sum(case when ae.priority = :general then 1 else 0 end) as general "
                        + " from AlarmEvent ae "
//                        + " inner join ae.alarmPoint ap "
//                        + " inner join ap.prdtCell pc "
                        + " where ae.eventTypeId = :eventTypeId and ae.companyId=:companyId "
                        + " and ae.alarmTime between :startTime and :endTime "
                        + " and ae.priority is not null and ae.alarmFlagId is not null ");
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("emergency", CommonEnum.AlarmPriorityEnum.Emergency.getIndex());
        paramList.put("important", CommonEnum.AlarmPriorityEnum.Importance.getIndex());
        paramList.put("general", CommonEnum.AlarmPriorityEnum.Normal.getIndex());
        paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        // 过滤装置
        if (ArrayUtils.isNotEmpty(unitCodes)) {
            hql.append(" and ae.unitCode in (:unitIds) ");
            paramList.put("unitIds", Arrays.asList(unitCodes));
        }
        // 过滤生产单元
        if (ArrayUtils.isNotEmpty(prdtCellIds)) {
            hql.append(" and ae.prdtCellId in (:prdtCellIds) ");
            paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
        }
//企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        Query query = getEntityManager().createQuery(hql.toString());
        this.setParameterList(query, paramList);
        List resultList = query.getResultList();
        return resultList;
    }

    /**
     * 获取报警次数统计页面统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCodes 装置编码数组
     * @param priority  优先级
     * <AUTHOR> 2017-11-21
     */
    @SuppressWarnings("rawtypes")
    @Override
    public List getAlarmNumStattStatisticData(Date startTime, Date endTime,
                                              String[] unitCodes, Integer[] priority,
                                              Long[] alarmFlagIds, Boolean priorityFlag,
                                              Integer isElimination) {
        try {
            StringBuilder hql = new StringBuilder(
                    " select count(ae.event_id) as total, ae.unit_code as unitId, min(ut.name) as name,min(ut.sname)as sname, "
                            + " sum(case when ae.priority = :emergency then 1 else 0 end) as emergency, "
                            + " sum(case when ae.priority = :important then 1 else 0 end) as important, "
                            + " sum(case when ae.priority = :general then 1 else 0 end) as general, "
                            + " sum(case when ae.priority is null then 1 else 0 end) as nullAlarmQuantity "
                            + " from t_ad_alarmevent ae "
                            + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id "
                            + " inner join t_pm_unit ut on ae.unit_code = ut.std_code"
                            + " where ae.event_type_id = :eventTypeId and ae.company_id=:companyId "
                            + " and ae.alarm_time between :startTime and :endTime "
                            + " and case when ae.alarm_point_id is not null then ap.in_use  end =1 "
            );
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("emergency", CommonEnum.AlarmPriorityEnum.Emergency.getIndex());
            paramList.put("important", CommonEnum.AlarmPriorityEnum.Importance.getIndex());
            paramList.put("general", CommonEnum.AlarmPriorityEnum.Normal.getIndex());
            paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append(" and ae.unit_code in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            // 过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ae.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ae.priority is null ");
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            hql.append(" group by ae.unit_code "
                    + "  order by total desc");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List getUnitMonitorAlarmNumStat(Date startTime, Date endTime, String[] unitCodes, Integer[] monitorType,
                                           Long[] alarmFlagIds, Integer isElimination) {
        StringBuilder hql = new StringBuilder(
                " select count(ae.event_id) as total, ae.unit_code as unitId, min(ut.name) as name,min(ut.sname)as sname, "
                        + " sum(case when ap.monitor_type = :technology then 1 else 0 end) as technology, "
                        + " sum(case when ap.monitor_type = :device then 1 else 0 end) as device, "
                        + " sum(case when ap.monitor_type = :safe then 1 else 0 end) as safe, "
                        + " sum(case when ap.monitor_type = :other then 1 else 0 end) as other, "
                        + " sum(case when ap.monitor_type = :nothing then 1 else 0 end) as nothing, "
                        + " sum(case when ap.monitor_type is null then 1 else 0 end) as nullAlarmQuantity "
                        + " from t_ad_alarmevent ae "
                        + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id "
                        + " inner join t_pm_unit ut on ae.unit_code = ut.std_code"
                        + " where ae.event_type_id = :eventTypeId and ae.company_id=:companyId "
                        + " and ae.alarm_time between :startTime and :endTime "
                        + " and case when ae.alarm_point_id is not null then ap.in_use  end =1 "
        );
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("device", CommonEnum.MonitorTypeEnum.Device.getIndex()); //设备
        paramList.put("technology", CommonEnum.MonitorTypeEnum.Technology.getIndex()); // 工艺
        paramList.put("safe", CommonEnum.MonitorTypeEnum.Safe.getIndex()); // 安全
        paramList.put("other", CommonEnum.MonitorTypeEnum.Other.getIndex()); // 生成
        paramList.put("nothing", CommonEnum.MonitorTypeEnum.Nothing.getIndex());// -
        paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        // 过滤装置
        if (ArrayUtils.isNotEmpty(unitCodes)) {
            hql.append(" and ae.unit_code in (:unitIds) ");
            paramList.put("unitIds", Arrays.asList(unitCodes));
        }
        // 过滤专业
        if (monitorType != null) {
            hql.append(" and ap.monitor_type in (:monitorType) ");
            paramList.put("monitorType", Arrays.asList(monitorType));
        }
        // 过滤报警标识
        if (alarmFlagIds != null && alarmFlagIds.length > 0) {
            for (int i = 0; i < alarmFlagIds.length; i++) {
                if (alarmFlagIds[i] == (-9L)) {
                    hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                    break;
                }
                if (i == alarmFlagIds.length - 1) {
                    hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                }
            }
            paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
        }
        hql.append(" group by ae.unit_code "
                + "  order by total desc");
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        List resultList = query.getResultList();
        return resultList;
    }

    @Override
    public List getAlarmNumStattStatisticDataWorkshop(Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination) {
        try {
            StringBuilder hql = new StringBuilder(
                    " select count(ae.event_id) as total, w.std_code as code, w.name as name,w.sname as sname, "
                            + " sum(case when ae.priority = :emergency then 1 else 0 end) as emergency, "
                            + " sum(case when ae.priority = :important then 1 else 0 end) as important, "
                            + " sum(case when ae.priority = :general then 1 else 0 end) as general, "
                            + " sum(case when ae.priority is null then 1 else 0 end) as nullAlarmQuantity "
                            + " from t_ad_alarmevent ae "
                            + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id "
                            + " inner join t_pm_prdtCell pc on pc.prdtcell_id =ae.prdtcell_id "
                            + " inner join t_pm_unit ut on ae.unit_code = ut.std_code"
                            + " inner join t_pm_workshop w on w.workshop_id  = ut.workshop_id"
                            + " where ae.event_type_id = :eventTypeId and ae.company_id=:companyId "
                            + " and ae.alarm_time between :startTime and :endTime "
                            + " and case when ae.alarm_point_id is not null then ap.in_use  end =1 "
            );
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("emergency", CommonEnum.AlarmPriorityEnum.Emergency.getIndex());
            paramList.put("important", CommonEnum.AlarmPriorityEnum.Importance.getIndex());
            paramList.put("general", CommonEnum.AlarmPriorityEnum.Normal.getIndex());
            paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append(" and ae.unit_code in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            // 过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ae.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ae.priority is null ");
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            hql.append(" group by w.std_code "
                    + "  order by total desc");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }
    @Override
    public List getAlarmNumStattStatisticDataWorkshopType(Date startTime, Date endTime, String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination) {
        try {
            StringBuilder hql = new StringBuilder(
                    " select count(ae.event_id) as total, w.std_code as code, w.name as name,w.sname as sname, "
                            + " sum(case when ap.monitor_type = :technology then 1 else 0 end) as technology, "
                            + " sum(case when ap.monitor_type = :device then 1 else 0 end) as device, "
                            + " sum(case when ap.monitor_type = :safe then 1 else 0 end) as safe, "
                            + " sum(case when ap.monitor_type = :other then 1 else 0 end) as other, "
                            + " sum(case when ap.monitor_type = :nothing then 1 else 0 end) as nothing, "
                            + " sum(case when ap.monitor_type is null then 1 else 0 end) as nullAlarmQuantity "
                            + " from t_ad_alarmevent ae "
                            + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id "
                            + " inner join t_pm_prdtCell pc on pc.prdtcell_id =ae.prdtcell_id "
                            + " inner join t_pm_unit ut on ae.unit_code = ut.std_code"
                            + " inner join t_pm_workshop w on w.workshop_id  = ut.workshop_id"
                            + " where ae.event_type_id = :eventTypeId and ae.company_id=:companyId "
                            + " and ae.alarm_time between :startTime and :endTime "
                            + " and case when ae.alarm_point_id is not null then ap.in_use  end =1 "
            );
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("device", CommonEnum.MonitorTypeEnum.Device.getIndex()); //设备
            paramList.put("technology", CommonEnum.MonitorTypeEnum.Technology.getIndex()); // 工艺
            paramList.put("safe", CommonEnum.MonitorTypeEnum.Safe.getIndex()); // 安全
            paramList.put("other", CommonEnum.MonitorTypeEnum.Other.getIndex()); // 生成
            paramList.put("nothing", CommonEnum.MonitorTypeEnum.Nothing.getIndex());// -
            paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append(" and ae.unit_code in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            // 过滤专业
            if (monitorType != null && !priorityFlag) {
                hql.append(" and ap.monitor_type in (:monitorType) ");
                paramList.put("monitorType", Arrays.asList(monitorType));
            }
            if (monitorType != null && priorityFlag) {
                hql.append(" and (ap.monitor_type in (:monitorType) or ap.monitor_type is null)");
                paramList.put("monitorType", Arrays.asList(monitorType));
            }
            if (monitorType == null && priorityFlag) {
                hql.append(" and ap.monitor_type is null ");
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            hql.append(" group by w.std_code "
                    + "  order by total desc");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List getAlarmNumStattStatisticDataUnit(String[] workshopCodes, Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination) {
        try {
            StringBuilder hql = new StringBuilder(
                    " select count(ae.event_id) as total, ut.std_code as code, ut.name as name,ut.sname as sname, "
                            + " sum(case when ae.priority = :emergency then 1 else 0 end) as emergency, "
                            + " sum(case when ae.priority = :important then 1 else 0 end) as important, "
                            + " sum(case when ae.priority = :general then 1 else 0 end) as general, "
                            + " sum(case when ae.priority is null then 1 else 0 end) as nullAlarmQuantity "
                            + " from t_ad_alarmevent ae "
                            + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id "
                            + " inner join t_pm_prdtCell pc on pc.prdtcell_id =ae.prdtcell_id "
                            + " inner join t_pm_unit ut on ae.unit_code = ut.std_code"
                            + " inner join t_pm_workshop w on w.workshop_id  = ut.workshop_id"
                            + " where ae.event_type_id = :eventTypeId and ae.company_id=:companyId "
                            + " and ae.alarm_time between :startTime and :endTime "
                            + " and case when ae.alarm_point_id is not null then ap.in_use  end =1 "
            );
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("emergency", CommonEnum.AlarmPriorityEnum.Emergency.getIndex());
            paramList.put("important", CommonEnum.AlarmPriorityEnum.Importance.getIndex());
            paramList.put("general", CommonEnum.AlarmPriorityEnum.Normal.getIndex());
            paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 过滤车间
            if (ArrayUtils.isNotEmpty(workshopCodes)) {
                hql.append(" and w.std_code in (:workshopCodes) ");
                paramList.put("workshopCodes", Arrays.asList(workshopCodes));
            }
            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append(" and ae.unit_code in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            // 过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ae.priority in (:priority) ");
                paramList.put("monitorType", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
                paramList.put("monitorType", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ae.priority is null ");
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            hql.append(" group by ut.std_code "
                    + "  order by total desc");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }
    @Override
    public List getAlarmNumStattStatisticDataUnitType(String[] workshopCodes, Date startTime, Date endTime, String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination) {
        try {
            StringBuilder hql = new StringBuilder(
                    " select count(ae.event_id) as total, ut.std_code as code, ut.name as name,ut.sname as sname, "
                            + " sum(case when ap.monitor_type = :technology then 1 else 0 end) as technology, "
                            + " sum(case when ap.monitor_type = :device then 1 else 0 end) as device, "
                            + " sum(case when ap.monitor_type = :safe then 1 else 0 end) as safe, "
                            + " sum(case when ap.monitor_type = :other then 1 else 0 end) as other, "
                            + " sum(case when ap.monitor_type = :nothing then 1 else 0 end) as nothing, "
                            + " sum(case when ap.monitor_type is null then 1 else 0 end) as nullAlarmQuantity "
                            + " from t_ad_alarmevent ae "
                            + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id "
                            + " inner join t_pm_prdtCell pc on pc.prdtcell_id =ae.prdtcell_id "
                            + " inner join t_pm_unit ut on ae.unit_code = ut.std_code"
                            + " inner join t_pm_workshop w on w.workshop_id  = ut.workshop_id"
                            + " where ae.event_type_id = :eventTypeId and ae.company_id=:companyId "
                            + " and ae.alarm_time between :startTime and :endTime "
                            + " and case when ae.alarm_point_id is not null then ap.in_use  end =1 "
            );
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("device", CommonEnum.MonitorTypeEnum.Device.getIndex()); //设备
            paramList.put("technology", CommonEnum.MonitorTypeEnum.Technology.getIndex()); // 工艺
            paramList.put("safe", CommonEnum.MonitorTypeEnum.Safe.getIndex()); // 安全
            paramList.put("other", CommonEnum.MonitorTypeEnum.Other.getIndex()); // 生成
            paramList.put("nothing", CommonEnum.MonitorTypeEnum.Nothing.getIndex());// -
            paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 过滤车间
            if (ArrayUtils.isNotEmpty(workshopCodes)) {
                hql.append(" and w.std_code in (:workshopCodes) ");
                paramList.put("workshopCodes", Arrays.asList(workshopCodes));
            }
            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append(" and ae.unit_code in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            // 过滤专业
            if (monitorType != null && !priorityFlag) {
                hql.append(" and ap.monitor_type in (:monitorType) ");
                paramList.put("monitorType", Arrays.asList(monitorType));
            }
            if (monitorType != null && priorityFlag) {
                hql.append(" and (ap.monitor_type in (:monitorType) or ap.monitor_type is null)");
                paramList.put("monitorType", Arrays.asList(monitorType));
            }
            if (monitorType == null && priorityFlag) {
                hql.append(" and ap.monitor_type is null ");
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            hql.append(" group by ut.std_code "
                    + "  order by total desc");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List getAlarmNumStattStatisticDataPrdtcell(String[] unitIds, Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination) {
        try {
            StringBuilder hql = new StringBuilder(
                    " select count(ae.event_id) as total, pc.prdtcell_id as code, pc.name as name,pc.sname as sname, "
                            + " sum(case when ae.priority = :emergency then 1 else 0 end) as emergency, "
                            + " sum(case when ae.priority = :important then 1 else 0 end) as important, "
                            + " sum(case when ae.priority = :general then 1 else 0 end) as general, "
                            + " sum(case when ae.priority is null then 1 else 0 end) as nullAlarmQuantity "
                            + " from t_ad_alarmevent ae "
                            + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id "
                            + " inner join t_pm_prdtCell pc on pc.prdtcell_id =ae.prdtcell_id "
                            + " inner join t_pm_unit ut on ae.unit_code = ut.std_code"
                            + " inner join t_pm_workshop w on w.workshop_id  = ut.workshop_id"
                            + " where ae.event_type_id = :eventTypeId and ae.company_id=:companyId "
                            + " and ae.alarm_time between :startTime and :endTime "
                            + " and case when ae.alarm_point_id is not null then ap.in_use  end =1 "
            );
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("emergency", CommonEnum.AlarmPriorityEnum.Emergency.getIndex());
            paramList.put("important", CommonEnum.AlarmPriorityEnum.Importance.getIndex());
            paramList.put("general", CommonEnum.AlarmPriorityEnum.Normal.getIndex());
            paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 过滤装置（单选柱状图的）
            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append(" and ae.unit_code in (:unitCode) ");
                paramList.put("unitCode", Arrays.asList(unitIds));
            }
            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append(" and ae.unit_code in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            // 过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ae.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ae.priority is null ");
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            hql.append(" group by pc.prdtcell_id "
                    + "  order by total desc");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }
    @Override
    public List getAlarmNumStattStatisticDataPrdtcellType(String[] unitIds, Date startTime, Date endTime, String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination) {
        try {
            StringBuilder hql = new StringBuilder(
                    " select count(ae.event_id) as total, pc.prdtcell_id as code, pc.name as name,pc.sname as sname, "
                            + " sum(case when ap.monitor_type = :technology then 1 else 0 end) as technology, "
                            + " sum(case when ap.monitor_type = :device then 1 else 0 end) as device, "
                            + " sum(case when ap.monitor_type = :safe then 1 else 0 end) as safe, "
                            + " sum(case when ap.monitor_type = :other then 1 else 0 end) as other, "
                            + " sum(case when ap.monitor_type = :nothing then 1 else 0 end) as nothing, "
                            + " sum(case when ae.priority is null then 1 else 0 end) as nullAlarmQuantity "
                            + " from t_ad_alarmevent ae "
                            + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id "
                            + " inner join t_pm_prdtCell pc on pc.prdtcell_id =ae.prdtcell_id "
                            + " inner join t_pm_unit ut on ae.unit_code = ut.std_code"
                            + " inner join t_pm_workshop w on w.workshop_id  = ut.workshop_id"
                            + " where ae.event_type_id = :eventTypeId and ae.company_id=:companyId "
                            + " and ae.alarm_time between :startTime and :endTime "
                            + " and case when ae.alarm_point_id is not null then ap.in_use  end =1 "
            );
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("device", CommonEnum.MonitorTypeEnum.Device.getIndex()); //设备
            paramList.put("technology", CommonEnum.MonitorTypeEnum.Technology.getIndex()); // 工艺
            paramList.put("safe", CommonEnum.MonitorTypeEnum.Safe.getIndex()); // 安全
            paramList.put("other", CommonEnum.MonitorTypeEnum.Other.getIndex()); // 生成
            paramList.put("nothing", CommonEnum.MonitorTypeEnum.Nothing.getIndex());// -
            paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 过滤装置（单选柱状图的）
            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append(" and ae.unit_code in (:unitCode) ");
                paramList.put("unitCode", Arrays.asList(unitIds));
            }
            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append(" and ae.unit_code in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            // 过滤专业
            if (monitorType != null && !priorityFlag) {
                hql.append(" and ap.monitor_type in (:monitorType) ");
                paramList.put("monitorType", Arrays.asList(monitorType));
            }
            if (monitorType != null && priorityFlag) {
                hql.append(" and (ap.monitor_type in (:monitorType) or ap.monitor_type is null)");
                paramList.put("monitorType", Arrays.asList(monitorType));
            }
            if (monitorType == null && priorityFlag) {
                hql.append(" and ap.monitor_type is null ");
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarm_flag_id in (:alarmFlagIds) or ae.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            hql.append(" group by pc.prdtcell_id "
                    + "  order by total desc");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    private Long[] String2Longr(String str) {
        Long[] lr = new Long[str.split(",").length];
        for (int i = 0; i < str.split(",").length; i++) {
            lr[i] = Long.parseLong(str.split(",")[i]);
        }
        return lr;
    }

    @Override
    public List getAlarmNumStattStatisticData(Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagId) {
        StringBuilder hql = new StringBuilder(
                " select count(ae.event_id) as total, ae.unit_code as unitId,ae.priority, "
                        + " sum(case when ae.priority = :emergency then 1 else 0 end) as emergency, "
                        + " sum(case when ae.priority = :important then 1 else 0 end) as important, "
                        + " sum(case when ae.priority = :general then 1 else 0 end) as general "
                        + " from t_ad_alarmevent ae "
                        + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id  "
                        + " left join t_pm_prdtCell pc on ae.prdtcell_id =pc.prdtcell_id "
                        + " where ae.event_type_id = :eventTypeId and ae.company_id=:companyId "
                        + " and ae.prdtcell_id is not null "
                        + " and ae.alarm_time between :startTime and :endTime "
                        + " and ae.alarm_point_id is not null "
                        + " and ae.unit_code in (:unitIds) ");
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("emergency", CommonEnum.AlarmPriorityEnum.Emergency.getIndex());
        paramList.put("important", CommonEnum.AlarmPriorityEnum.Importance.getIndex());
        paramList.put("general", CommonEnum.AlarmPriorityEnum.Normal.getIndex());
        paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        paramList.put("unitIds", Arrays.asList(unitCodes));

        if (priority != null && priority.length > 0) {
            for (int i = 0; i < priority.length; i++) {
                if (priority[i].equals(9)) {
                    hql.append(" and ( ae.priority in (:priority) or ae.priority is null) ");
                    break;
                }
                if (i == priority.length - 1) {
                    hql.append(" and  ae.priority in (:priority) ");
                }
            }
            paramList.put("priority", Arrays.asList(priority));
        }

        // 报警标识
        if (null != alarmFlagId) {
            hql.append(" and ae.alarm_flag_id in (:alarmFlagId) ");
            paramList.put("alarmFlagId", Arrays.asList(alarmFlagId));
        }

        hql.append(" group by ae.unit_code,ae.priority ");
        Integer companyId = new CommonProperty().getCompanyId();
        //如果获取不到企业id则根据装置获取
        if (companyId != null) {
            paramList.put("companyId", companyId);
        } else {
            paramList.put("companyId", getCompanyIdByUnit(unitCodes));
        }
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        //query.setFirstResult(0).setMaxResults(20);
        List resultList = query.getResultList();
        return resultList;
    }

    @Override
    public List getAlarmNumStattStatisticDataWorkshop(Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagId) {
        StringBuilder hql = new StringBuilder(
                " select count(ae.event_id) as total, w.std_code as code,w.sname as name,ae.priority, "
                        + " sum(case when ae.priority = :emergency then 1 else 0 end) as emergency, "
                        + " sum(case when ae.priority = :important then 1 else 0 end) as important, "
                        + " sum(case when ae.priority = :general then 1 else 0 end) as general "
                        + " from t_ad_alarmevent ae "
                        + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id  "
                        + " left join t_pm_prdtCell pc on ae.prdtcell_id =pc.prdtcell_id "
                        + " left join t_pm_unit u on pc.unit_code =u.std_code"
                        + " left join t_pm_workshop w on w.workshop_id  = u.workshop_id"
                        + " where ae.event_type_id = :eventTypeId and ae.company_id=:companyId "
                        + " and ae.prdtcell_id is not null "
                        + " and ae.alarm_time between :startTime and :endTime "
                        + " and ae.alarm_point_id is not null "
                        + " and ae.unit_code in (:unitIds) ");
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("emergency", CommonEnum.AlarmPriorityEnum.Emergency.getIndex());
        paramList.put("important", CommonEnum.AlarmPriorityEnum.Importance.getIndex());
        paramList.put("general", CommonEnum.AlarmPriorityEnum.Normal.getIndex());
        paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        paramList.put("unitIds", Arrays.asList(unitCodes));

        if (priority != null && priority.length > 0) {
            for (int i = 0; i < priority.length; i++) {
                if (priority[i].equals(9)) {
                    hql.append(" and ( ae.priority in (:priority) or ae.priority is null) ");
                    break;
                }
                if (i == priority.length - 1) {
                    hql.append(" and  ae.priority in (:priority) ");
                }
            }
            paramList.put("priority", Arrays.asList(priority));
        }

        // 报警标识
        if (null != alarmFlagId) {
            hql.append(" and ae.alarm_flag_id in (:alarmFlagId) ");
            paramList.put("alarmFlagId", Arrays.asList(alarmFlagId));
        }

        hql.append(" group by w.std_code,ae.priority ");
        Integer companyId = new CommonProperty().getCompanyId();
        //如果获取不到企业id则根据装置获取
        if (companyId != null) {
            paramList.put("companyId", companyId);
        } else {
            paramList.put("companyId", getCompanyIdByUnit(unitCodes));
        }
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        //query.setFirstResult(0).setMaxResults(20);
        List resultList = query.getResultList();
        return resultList;
    }

    @Override
    public List getAlarmNumStattStatisticDataUnit(String[] workshopCodes, Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagId) {
        StringBuilder hql = new StringBuilder(
                " select count(ae.event_id) as total, u.std_code as code,u.sname as name,ae.priority, "
                        + " sum(case when ae.priority = :emergency then 1 else 0 end) as emergency, "
                        + " sum(case when ae.priority = :important then 1 else 0 end) as important, "
                        + " sum(case when ae.priority = :general then 1 else 0 end) as general "
                        + " from t_ad_alarmevent ae "
                        + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id  "
                        + " left join t_pm_prdtCell pc on ae.prdtcell_id =pc.prdtcell_id "
                        + " left join t_pm_unit u on pc.unit_code =u.std_code"
                        + " left join t_pm_workshop w on w.workshop_id  = u.workshop_id"
                        + " where ae.event_type_id = :eventTypeId and ae.company_id=:companyId "
                        + " and ae.prdtcell_id is not null "
                        + " and ae.alarm_time between :startTime and :endTime "
                        + " and ae.alarm_point_id is not null "
                        + " and ae.unit_code in (:unitIds) ");
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("emergency", CommonEnum.AlarmPriorityEnum.Emergency.getIndex());
        paramList.put("important", CommonEnum.AlarmPriorityEnum.Importance.getIndex());
        paramList.put("general", CommonEnum.AlarmPriorityEnum.Normal.getIndex());
        paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        paramList.put("unitIds", Arrays.asList(unitCodes));

        if (priority != null && priority.length > 0) {
            for (int i = 0; i < priority.length; i++) {
                if (priority[i].equals(9)) {
                    hql.append(" and ( ae.priority in (:priority) or ae.priority is null) ");
                    break;
                }
                if (i == priority.length - 1) {
                    hql.append(" and  ae.priority in (:priority) ");
                }
            }
            paramList.put("priority", Arrays.asList(priority));
        }
        //车间过滤
        if (ArrayUtils.isNotEmpty(workshopCodes)){
            hql.append(" and w.std_code in (:workshopCodes)");
            paramList.put("workshopCodes",Arrays.asList(workshopCodes));
        }
        // 报警标识
        if (null != alarmFlagId) {
            hql.append(" and ae.alarm_flag_id in (:alarmFlagId) ");
            paramList.put("alarmFlagId", Arrays.asList(alarmFlagId));
        }

        hql.append(" group by u.std_code,ae.priority ");
        Integer companyId = new CommonProperty().getCompanyId();
        //如果获取不到企业id则根据装置获取
        if (companyId != null) {
            paramList.put("companyId", companyId);
        } else {
            paramList.put("companyId", getCompanyIdByUnit(unitCodes));
        }
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        //query.setFirstResult(0).setMaxResults(20);
        List resultList = query.getResultList();
        return resultList;
    }

    @Override
    public List getAlarmNumStattStatisticDataPrdtcell(String[] unitIds, Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagId) {
        StringBuilder hql = new StringBuilder(
                " select count(ae.event_id) as total, pc.prdtcell_id as code,pc.sname as name,ae.priority, "
                        + " sum(case when ae.priority = :emergency then 1 else 0 end) as emergency, "
                        + " sum(case when ae.priority = :important then 1 else 0 end) as important, "
                        + " sum(case when ae.priority = :general then 1 else 0 end) as general "
                        + " from t_ad_alarmevent ae "
                        + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id  "
                        + " left join t_pm_prdtCell pc on ae.prdtcell_id =pc.prdtcell_id "
                        + " left join t_pm_unit u on pc.unit_code =u.std_code"
                        + " left join t_pm_workshop w on w.workshop_id  = u.workshop_id"
                        + " where ae.event_type_id = :eventTypeId and ae.company_id=:companyId "
                        + " and ae.prdtcell_id is not null "
                        + " and ae.alarm_time between :startTime and :endTime "
                        + " and ae.alarm_point_id is not null "
                        + " and ae.unit_code in (:unitIds) ");
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("emergency", CommonEnum.AlarmPriorityEnum.Emergency.getIndex());
        paramList.put("important", CommonEnum.AlarmPriorityEnum.Importance.getIndex());
        paramList.put("general", CommonEnum.AlarmPriorityEnum.Normal.getIndex());
        paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        paramList.put("unitIds", Arrays.asList(unitCodes));

        if (priority != null && priority.length > 0) {
            for (int i = 0; i < priority.length; i++) {
                if (priority[i].equals(9)) {
                    hql.append(" and ( ae.priority in (:priority) or ae.priority is null) ");
                    break;
                }
                if (i == priority.length - 1) {
                    hql.append(" and  ae.priority in (:priority) ");
                }
            }
            paramList.put("priority", Arrays.asList(priority));
        }
        //装置过滤
        if (ArrayUtils.isNotEmpty(unitIds)){
            hql.append(" and u.std_code in (:unitIds)");
            paramList.put("unitIds",Arrays.asList(unitIds));
        }
        // 报警标识
        if (null != alarmFlagId) {
            hql.append(" and ae.alarm_flag_id in (:alarmFlagId) ");
            paramList.put("alarmFlagId", Arrays.asList(alarmFlagId));
        }

        hql.append(" group by pc.prdtcell_id,ae.priority ");
        Integer companyId = new CommonProperty().getCompanyId();
        //如果获取不到企业id则根据装置获取
        if (companyId != null) {
            paramList.put("companyId", companyId);
        } else {
            paramList.put("companyId", getCompanyIdByUnit(unitCodes));
        }
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        //query.setFirstResult(0).setMaxResults(20);
        List resultList = query.getResultList();
        return resultList;
    }

    /**
     * 获取报警时长分页数据
     *
     * @param unitCodes    装置编码数组
     * @param eventTypeIds 事件类型ID数组
     * @param priority     优先级
     * @param startTime    时间范围起始
     * @param endTime      时间范围结束
     * @param page         分页参数
     * @return 报警事件实体集合
     * @throws Exception
     * <AUTHOR> 2019-09-30
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Transactional
    @Override
    public List getAlarmDurationStatt(String[] unitCodes, Integer[] priority, Long[] eventTypeIds, Date startTime, Date endTime, Boolean priorityFlag, Pagination page) throws Exception {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder(
                    "SELECT * " +
                            "  FROM (SELECT tt.*, ROWNUM AS rowno " +
                            "  FROM ( " +
                            "select * from (\n" +
                            "\n" +
                            "select ut.*,rownum from (\n" +
                            "select * from (\n" +
                            "select ae.alarm_time,\n" +
                            "   min(ae.start_time),\n" +
                            "   min(ae.event_type_id),\n" +
                            "   min(pc.name) as pcname,\n" +
                            "   ap.tag,\n" +
                            "   min(ae.des),\n" +
                            "   min(af.name),\n" +
                            "   min(ae.priority),\n" +
                            "   min(ut.sname),\n" +
                            "    ae.alarm_flag_id," +
                            "    min(ae.prdtcell_id)," +
                            "    pc.unit_code as unit_code" +
                            "  from t_ad_AlarmEvent ae ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();

            //StringBuilder hqlWhere = new StringBuilder("where 1=1 ");
            // 关联报警点
            hql.append("left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id ");
            // 关联报警标识
            hql.append("left join t_ad_alarmflag af on ae.alarm_flag_id = af.alarm_flag_id ");
            // 关联生产单元
            hql.append("inner join t_pm_prdtCell pc on ae.prdtcell_id =pc.prdtcell_id ");
            // 关联报警事件
//            hql.append("inner join t_pm_eventtype et on ae.event_type_id =et.event_type_id ");


            hql.append("inner join t_pm_unit ut on pc.unit_code = ut.std_code ");

            hql.append("where  1 =1 and ae.company_id=:companyId ");
            if (ArrayUtils.isNotEmpty(eventTypeIds)) {
                hql.append(" and ae.event_type_id in (:eventTypeIds) ");
                paramList.put("eventTypeIds", Arrays.asList(eventTypeIds));
            }
            //过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ae.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ae.priority is null ");
            }

            // 过滤日期
            if (startTime != null && endTime != null) {

                hql.append("and ae.start_time >= :startTime ");
                hql.append("and ae.alarm_time <=:endTime ");

                paramList.put("startTime", startTime);
                paramList.put("endTime", endTime);
            }

            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append("and pc.unit_code in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            hql.append("  group by pc.unit_code,\n" +
                    "                          pc.prdtcell_id,\n" +
                    "                          ap.tag,\n" +
                    "                          ae.alarm_flag_id,\n" +
                    "                          ae.alarm_time "
                    + ")\n" +
                    "UNION     \n" +
                    " \n" +
                    "select * from  (               \n" +
                    "select alarm_time,\n" +
                    "                       start_time,\n" +
                    "                       event_type_id,\n" +
                    "                        pcname,\n" +
                    "                       tag,\n" +
                    "                       des,\n" +
                    "                       name,\n" +
                    "                       priority,\n" +
                    "                       sname,\n" +
                    "                       alarm_flag_id,\n" +
                    "   prdtcell_id," +
                    "                       unit_code " +
                    "from (\n" +
                    "select ae.alarm_time,\n" +
                    "       min(ae.start_time) as start_time,\n" +
                    "       max(ae.event_type_id) as event_type_id,\n" +
                    "       min(pc.name) as pcname,\n" +
                    "       ap.tag,\n" +
                    "       min(ae.des) as des,\n" +
                    "       min(af.name) as name,\n" +
                    "       min(ae.priority) as priority,\n" +
                    "       min(ut.sname) as sname,\n" +
                    "        ae.alarm_flag_id,\n" +
                    "       min(ae.prdtcell_id) as prdtcell_id,\n" +
                    "       min(af.name),\n" +
                    "       ae.unit_code," +
                    "       count(distinct event_type_id) a,\n" +
                    "       max(event_type_id) b\n" +
                    "              \n" +
                    "          from t_ad_AlarmEvent ae\n" +
                    "         left join t_pm_alarmPoint ap\n" +
                    "            on ap.alarm_point_id = ae.alarm_point_id\n" +
                    "         left join t_ad_alarmflag af\n" +
                    "            on ae.alarm_flag_id = af.alarm_flag_id\n" +
                    "         inner join t_pm_prdtCell pc\n" +
                    "            on ae.prdtcell_id = pc.prdtcell_id\n" +
                    "         inner join t_pm_unit ut\n" +
                    "            on pc.unit_code = ut.std_code\n" +
                    "         where 1 = 1 and ae.company_id=:companyId "
                    + " and ae.event_type_id  in (1001,1002, 1003, 1005)");

            //过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ae.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ae.priority is null ");
            }

            // 过滤日期
            if (startTime != null && endTime != null) {

                hql.append("and ae.start_time >= :startTime ");
                hql.append("and ae.alarm_time <=:endTime ");

                paramList.put("startTime", startTime);
                paramList.put("endTime", endTime);
            }

            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append("and pc.unit_code in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }

            hql.append("  group by ae.unit_code,\n" +
                    "                  pc.prdtcell_id,\n" +
                    "                  ap.tag,\n" +
                    "                  ae.alarm_flag_id,\n" +
                    "                  ae.alarm_time\n" +
                    "         /* order by ae.unit_code,\n" +
                    "                  pc.prdtcell_id,\n" +
                    "                  ap.tag,\n" +
                    "                  ae.alarm_flag_id,\n" +
                    "                  ae.alarm_time*/\n" +
                    "                  )where a=1 and b=1001)\n" +
                    ")  ut\n" +
                    "          order by ut.unit_code,\n" +
                    "                  --ut.prdtcell_id,\n" +
                    "                  ut.tag,\n" +
                    "                  ut.alarm_flag_id,\n" +
                    "                  ut.alarm_time\n" +
                    ")) tt\n" +
                    "         WHERE ROWNUM <= :big) table_alias\n" +
                    " WHERE table_alias.rowno > :smail");
            paramList.put("big", page.getPageNumber() * page.getPageSize() + 1);
            paramList.put("smail", (page.getPageNumber() - 1) * page.getPageSize());
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            //query.setFirstResult(0).setMaxResults(20);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }


    /**
     * 报警时长统计总条数
     *
     * @param unitIds      装置ID
     * @param priority     优先级
     * @param eventTypeIds 事件类型ID
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Object getAlarmDurationStattSum(String[] unitIds, Integer[] priority, Long[] eventTypeIds, Date startTime, Date endTime, Boolean priorityFlag) {
        StringBuilder hql = new StringBuilder(
                " select count(*) from (  "
                        + " select"
                        + " alarm_time,"
                        + " min(start_time),"
                        + " min(event_type_id),"
                        + " unit_code,"
                        + " tag,"
                        + " min(des),"
                        + " min(priority),"
                        + " max(sname),"
                        + " alarm_flag_id,"
                        + " prdtcell_id"
                        + "  from (select ae.alarm_time,"
                        + " ae.start_time,"
                        + " ae.event_type_id,"
                        + " ae.unit_code,"
                        + " ap.tag,"
                        + " ae.des,"
                        + " ae.priority,"
                        + " ut.sname,"
                        + " ae.alarm_flag_id,"
                        + " ae.prdtcell_id"
                        + " from t_ad_alarmevent ae "
                        + " inner join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id "
                        + " inner join t_pm_prdtCell pc on ae.prdtcell_id =pc.prdtcell_id "
                        + " inner join t_pm_unit ut on pc.unit_code = ut.std_code"
                        + " where ae.event_type_id in (:eventTypeIds) and ae.company_id=:companyId and ap.company_id=:companyId and pc.company_id=:companyId and ut.company_id=:companyId "
                        + " and ae.start_time >= :startTime  "
                        + " and ae.alarm_time <=:endTime "
                        + " and ap.in_use =1 ");
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("eventTypeIds", Arrays.asList(eventTypeIds));
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        // 过滤装置
        if (ArrayUtils.isNotEmpty(unitIds)) {
            hql.append(" and pc.unit_code in (:unitIds) ");
            paramList.put("unitIds", Arrays.asList(unitIds));
        }
        // 过滤优先级
        if (priority != null && !priorityFlag) {
            hql.append(" and ae.priority in (:priority) ");
            paramList.put("priority", Arrays.asList(priority));
        }
        if (priority != null && priorityFlag) {
            hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
            paramList.put("priority", Arrays.asList(priority));
        }
        if (priority == null && priorityFlag) {
            hql.append(" and ae.priority is null ");
        }

        // hql.append(" group by pc.unit_code,pc.prdtcell_id,ap.tag,ae.alarm_flag_id,ae.alarm_time ");
        hql.append("  order by pc.unit_code,"
                + "  pc.prdtcell_id,"
                + "  ap.tag,"
                + "  ae.alarm_flag_id,"
                + "  ae.alarm_time,"
                + "  ae.start_time)"
                + " group by unit_code,"
                + " prdtcell_id,"
                + " tag,"
                + " alarm_flag_id,"
                + " alarm_time");
        hql.append(" ) ");
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        return query.getSingleResult();
    }

    /**
     * 获取报警时长柱形图数据
     *
     * @param unitCodes    装置编码数组
     * @param eventTypeIds 事件类型ID数组
     * @param priority     优先级
     * @param startTime    时间范围起始
     * @param endTime      时间范围结束
     * @return 报警事件实体集合
     * @throws Exception
     * <AUTHOR> 2019-09-30
     */
    @SuppressWarnings("rawtypes")
    @Override
    public List getAlarmDurationStattTotal(String[] unitCodes, Integer[] priority, Long[] eventTypeIds, Date startTime, Date endTime, Boolean priorityFlag) {
        StringBuilder hql = new StringBuilder();
        if ("oracle".equals(dbConfig.getDataBase())) {
            hql.append("select to_number(to_char(sum(ceil((cast(start_time as date)-cast(alarm_time as date))) * 24*60*60 )/60,'FM99999999999999990.00')),a.sname,a.priority from ( ");
        } else {
            hql.append("select cast(date_format(sum(ceil(TIMESTAMPDIFF(second,cast(alarm_time as datetime),cast(start_time as datetime))/60/60/24) * 24*60*60 )/60,'FM99999999999999990.00') as decimal(20,2)),a.sname,a.priority from ( ");
        }
        hql.append(" select "
//                        + "ae.alarm_time,min(ae.start_time) as start_time,"
                + "case" +
                "       when ae.alarm_time <:startTime then :startTime else  ae.alarm_time" +
                "                end as alarm_time," +
                " case" +
                "       when min(ae.start_time) > :endTime then  :endTime else min(ae.start_time)" +
                "                end as start_time,"
                + "min(ut.sname)as sname,"
                + "min(ae.priority) as priority"
                + " from t_ad_alarmevent ae "
                + " left join t_pm_alarmPoint ap on ap.alarm_point_id=ae.alarm_point_id "
                + " inner join t_pm_prdtCell pc on ae.prdtcell_id =pc.prdtcell_id "
                + " inner join t_pm_unit ut on pc.unit_code = ut.std_code"
                + " where ae.event_type_id in (:eventTypeIds) and ae.company_id=:companyId and ap.company_id=:companyId and pc.company_id=:companyId and ut.company_id=:companyId "
                + " and ae.start_time >= :startTime  "
                + " and ae.alarm_time <=:endTime ");


        //hql.append(" and ae.priority is not null ");
        //hql.append(" and ap.in_use =1 ");
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("eventTypeIds", Arrays.asList(eventTypeIds));

        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        // 过滤装置
        if (ArrayUtils.isNotEmpty(unitCodes)) {
            hql.append(" and pc.unit_code in (:unitIds) ");
            paramList.put("unitIds", Arrays.asList(unitCodes));
        }
        // 过滤优先级
        if (priority != null && !priorityFlag) {
            hql.append(" and ae.priority in (:priority) ");
            paramList.put("priority", Arrays.asList(priority));
        }
        if (priority != null && priorityFlag) {
            hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
            paramList.put("priority", Arrays.asList(priority));
        }
        if (priority == null && priorityFlag) {
            hql.append(" and ae.priority is null ");
        }

        hql.append(" group by pc.unit_code,pc.prdtcell_id,ap.tag,ae.alarm_flag_id,ae.alarm_time ");
        hql.append(" order by ae.alarm_time desc,ap.tag desc,ae.alarm_flag_id desc");
        hql.append(" ) a  group by a.sname,a.priority");
//企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        List resultList = query.getResultList();
        return resultList;
    }

    @Override
    public List getAlarmDurationStattTotal2(String[] unitIds, Integer[] priority, Long[] eventTypeIds, Date startTime, Date endTime, Boolean priorityFlag) {
        try {
            StringBuilder hql = new StringBuilder();
            hql.append("select * from ("
                    + "select ae.alarm_time,"
                    + "        min(ae.start_time),"
                    + "        max(ae.event_type_id),"
                    + "        ae.unit_code,"
                    + "        ap.tag,"
                    + "        min(ae.priority),"
                    + "        min(ut.sname),"
                    + "         ae.alarm_flag_id,"
                    + "        min(ae.prdtcell_id),"
                    + "        min(af.name),"
                    + "        count(distinct event_type_id) a,"
                    + "        max(event_type_id) b"
                    + "     from t_ad_AlarmEvent ae"
                    + "       left join t_pm_alarmPoint ap"
                    + "          on ap.alarm_point_id = ae.alarm_point_id"
                    + "       left join t_ad_alarmflag af"
                    + "          on ae.alarm_flag_id = af.alarm_flag_id"
                    + "       inner join t_pm_prdtCell pc"
                    + "          on ae.prdtcell_id = pc.prdtcell_id"
                    + "       inner join t_pm_unit ut"
                    + "          on pc.unit_code = ut.std_code"
                    + "       where 1 = 1 and ae.company_id=:companyId and ap.company_id=:companyId and pc.company_id=:companyId and ut.company_id=:companyId "
                    + "         and ae.event_type_id in (:eventTypeIds)" + " and ae.start_time >= :startTime  " + " and ae.alarm_time <=:endTime "
            );

            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("eventTypeIds", Arrays.asList(eventTypeIds));

            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append(" and pc.unit_code in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitIds));
            }
            // 过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ae.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ae.priority in (:priority) or ae.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ae.priority is null ");
            }
            hql.append(" group by ae.unit_code,ae.prdtcell_id,ap.tag,ae.alarm_flag_id,ae.alarm_time"
                    + "   order by ae.unit_code,ae.prdtcell_id,ap.tag,ae.alarm_flag_id,ae.alarm_time"
                    + "   )where a=1 and b=1001");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List getSendMsgInfo(Date startTime, Date endTime) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("select ap.prdtcell_id, ae.tag, min(ap.mobile_phone), min(ae.alarm_time),  min(ae.des),min(af.name),min(ap.alarm_point_id),min(ae.alarm_flag_id)"
                    + " from t_ad_alarmEvent ae "
                    + " inner join t_pm_alarmpoint ap on ae.alarm_point_id = ap.alarm_point_id "
                    + " inner join T_PM_SendMsgAlarmFlagConf smafc on ae.alarm_flag_id =smafc.alarm_flag_id and ae.alarm_point_id =smafc.alarm_point_id "
                    + " inner join t_ad_alarmflag af on af.alarm_flag_id =ae.alarm_flag_id "
                    + " where  ap.in_use = 1 and ae.company_id=:companyId  and ap.company_id=:companyId  "
                    + " and ap.in_sendmsg =1 "
                    + " and ae.event_type_id = 1001 "
                    + " and ae.write_time >=(:startTime) "
                    + " and ae.write_time<= (:endTime) "
                    + "  group by ap.prdtcell_id,ae.tag,ae.alarm_flag_id"
            );
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 判断报警点在报警事件表中是否使用
     *
     * @param alarmPointId
     * @return
     * <AUTHOR> 2017-11-29
     */
    public Long getAlarmPointIsUseInAlarmEvent(Long alarmPointId) {
        String hql = "select count(*) from AlarmEvent ae where ae.companyId=:companyId and  ae.alarmPointId = :alarmPointId limit 1";
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("alarmPointId", alarmPointId);
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        Query query = getEntityManager().createQuery(hql);
        this.setParameterList(query, paramList);
        return (Long) query.getSingleResult();
    }

    /**
     * 根据报警点ID和报警标识ID获取报警详情-报警总时长
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @return 报警详情-报警总时长
     * <AUTHOR> 2017-11-08
     */
    @SuppressWarnings("rawtypes")
    public List getAlarmDurationData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId) {
        StringBuilder hql = new StringBuilder();
        if ("oracle".equals(dbConfig.getDataBase())) {
            hql.append(" select round(sum((min(case when t.event_type_id in(:hfEventTypeIds) then t.alarm_time else cast(:endTime as date) end) - t.alarmTime)* 24 * 60)/60,2) as alarmDuration");
        } else {
            hql.append(" select round(sum(alarmDuration)/60,2) from (" +
                    "select ((TIMESTAMPDIFF(second,t.alarm_time,min(case when t.event_type_id in(:hfEventTypeIds) then t.start_time else cast(:endTime as datetime) end))/60/60/24)* 24 * 60) as alarmDuration");
        }
        hql.append(" from t_ad_alarmevent t" +
                " where t.company_id=:companyId and t.alarm_point_id = :alarmPointId and " +
                " t.alarm_flag_id = :alarmFlagId and" +
                " t.start_time between :startTime and :endTime and" +
                " t.alarm_time between :startTime and :endTime" +
                " group by t.alarm_point_id,t.alarm_flag_id,t.alarm_time");
        if ("mysql".equals(dbConfig.getDataBase())) {
            hql.append(" ) a");
        }

        List<Long> hfIds = new ArrayList<>();
        hfIds.add(EventTypeEnum.RecoverUnconfirmedEvent.getIndex());
        hfIds.add(EventTypeEnum.RecoverConfirmedEvent.getIndex());
        hfIds.add(EventTypeEnum.RecoverEvent.getIndex());

        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("hfEventTypeIds", hfIds);
        paramList.put("alarmPointId", alarmPointId);
        paramList.put("alarmFlagId", alarmFlagId);
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        return query.getResultList();
    }


    /**
     * 根据报警点ID和报警标识ID获取报警详情-报警次数
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @return 报警详情-报警次数
     * <AUTHOR> 2017-11-08
     */
    @SuppressWarnings("rawtypes")
    public List getAlarmTimesData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId) {
        String hql =
                " select count(0) as alarmTimes " +
                        " from AlarmEvent t" +
                        " where t.companyId=:companyId and t.alarmPointId = :alarmPointId and " +
                        " t.alarmFlagId = :alarmFlagId and" +
                        " t.eventTypeId = :eventTypeId and" +
                        " t.alarmTime between :startTime and :endTime";
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
        paramList.put("alarmPointId", alarmPointId);
        paramList.put("alarmFlagId", alarmFlagId);
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
//企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        Query query = getEntityManager().createQuery(hql.toString());
        this.setParameterList(query, paramList);
        return query.getResultList();
    }

    /**
     * 根据报警点ID和报警标识ID获取报警详情-确认总时长
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @return 报警详情-确认总时长
     * <AUTHOR> 2017-11-08
     */
//    @SuppressWarnings("rawtypes")
//    public List getConfirmDurationData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId) {
//        String hql =
//                " select round(sum((min(case when t.eventTypeId in(:qrEventTypeIds) then t.startTime else null end) " +
//                        " - t.alarmTime)* 24 * 60)/60,2) as confirmDuration" +
//                        " from AlarmEvent t" +
//                        " where t.alarmPointId = :alarmPointId and " +
//                        " t.alarmFlagId = :alarmFlagId and" +
//                        " t.startTime between :startTime and :endTime and" +
//                        " t.alarmTime between :startTime and :endTime" +
//                        " group by t.alarmPointId,t.alarmFlagId,t.alarmTime";
//
//        List<Long> qrIds = new ArrayList<>();
//        qrIds.add(EventTypeEnum.RecoverConfirmedEvent.getIndex());
//        qrIds.add(EventTypeEnum.NoRecoverConfirmedEvent.getIndex());
//        qrIds.add(EventTypeEnum.ConfirmedEvent.getIndex());
//
//        Map<String, Object> paramList = new HashMap<String, Object>();
//        paramList.put("qrEventTypeIds", qrIds);
//        paramList.put("alarmPointId", alarmPointId);
//        paramList.put("alarmFlagId", alarmFlagId);
//        paramList.put("startTime", startTime);
//        paramList.put("endTime", endTime);
//
//        Query query = getEntityManager().createQuery(hql.toString());
//        this.setParameterList(query, paramList);
//        return query.getResultList();
//    }
    @SuppressWarnings("rawtypes")
    public List getConfirmDurationData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId) {
        StringBuilder hql = new StringBuilder();
        if ("oracle".equals(dbConfig.getDataBase())) {
            hql.append(" select round(sum((min(case when t.eventTypeId in(:qrEventTypeIds) then t.startTime else null end) " +
                    " - t.alarmTime)* 24 * 60)/60,2) as confirmDuration");
        } else {
//            hql.append(" select round(sum((min(case when t.eventTypeId in(:qrEventTypeIds) then t.startTime else null end) " +
//                    " - t.alarmTime)* 24 * 60)/60,2) as confirmDuration" );
//
//            hql.append(" select round(sum(alarmDuration)/60,2) from (" +
//                    "select ((TIMESTAMPDIFF(second,t.alarm_time,min(case when t.event_type_id in(:hfEventTypeIds) then t.start_time else cast(:endTime as datetime) end))/60/60/24)* 24 * 60) as alarmDuration");

            hql.append(" select round(sum(confirmDuration)/60,2) from (" +
                    "select ((TIMESTAMPDIFF(second,t.alarm_time,min(case when t.event_type_id in(:qrEventTypeIds) then t.start_time else null end))/60/60/24)* 24 * 60) as confirmDuration");
        }

        hql.append(" from t_ad_alarmevent t" +
                " where t.company_id=:companyId and t.alarm_point_id = :alarmPointId and " +
                " t.alarm_flag_id = :alarmFlagId and" +
                " t.start_time between :startTime and :endTime and" +
                " t.alarm_time between :startTime and :endTime" +
                " group by t.alarm_point_id,t.alarm_flag_id,t.alarm_time");
        if ("mysql".equals(dbConfig.getDataBase())) {
            hql.append(" ) a");
        }
        List<Long> qrIds = new ArrayList<>();
        qrIds.add(EventTypeEnum.RecoverConfirmedEvent.getIndex());
        qrIds.add(EventTypeEnum.NoRecoverConfirmedEvent.getIndex());
        qrIds.add(EventTypeEnum.ConfirmedEvent.getIndex());

        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("qrEventTypeIds", qrIds);
        paramList.put("alarmPointId", alarmPointId);
        paramList.put("alarmFlagId", alarmFlagId);
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
//企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
//        Query query = getEntityManager().createQuery(hql.toString());
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        System.out.println(query.getResultList());
        return query.getResultList();
    }

    /**
     * 根据报警点ID和报警标识ID获取报警详情-确认次数
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @return 报警详情-确认次数
     * <AUTHOR> 2017-11-08
     */
    @SuppressWarnings("rawtypes")
    public List getConfirmTimesData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId) {
        StringBuilder hql = new StringBuilder();
        if ("oracle".equals(dbConfig.getDataBase())) {
            hql.append(" select sum(min(case when t.event_type_id in (:qrEventTypeIds) then 1 else null end)) as confirmTimes");
        } else {
            hql.append(" select sum(confirmTimes) from (select (min(case when t.event_type_id in (:qrEventTypeIds) then 1 else null end)) as confirmTimes");
        }
        hql.append(" from t_ad_alarmevent t" +
                " where t.company_id=:companyId and t.alarm_point_id = :alarmPointId and " +
                " t.alarm_flag_id = :alarmFlagId and" +
                " t.event_type_id in (:qrEventTypeIds) and" +
                " t.start_time between :startTime and :endTime and" +
                " t.alarm_time between :startTime and :endTime" +
                " group by t.alarm_point_id,t.alarm_flag_id,t.alarm_time");
        if ("mysql".equals(dbConfig.getDataBase())) {
            hql.append(" ) a");
        }
        List<Long> qrIds = new ArrayList<>();
        qrIds.add(EventTypeEnum.RecoverConfirmedEvent.getIndex());
        qrIds.add(EventTypeEnum.NoRecoverConfirmedEvent.getIndex());
        qrIds.add(EventTypeEnum.ConfirmedEvent.getIndex());

        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("qrEventTypeIds", qrIds);
        paramList.put("alarmPointId", alarmPointId);
        paramList.put("alarmFlagId", alarmFlagId);
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
//企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
//        Query query = getEntityManager().createQuery(hql.toString());
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        System.out.println(query.getResultList());
        return query.getResultList();
    }

    /**
     * 根据报警点ID和报警标识ID获取报警详情-现报警数
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @param alarmValue   报警值
     * @return 报警详情-现报警数
     * <AUTHOR> 2017-11-08
     */
    @SuppressWarnings("rawtypes")
    public int getNowAlrmTimesData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId, Double alarmValue) {
        String thanFlag = "";
        if (alarmFlagId == CommonEnum.AlarmFlagEnum.PVHH.getIndex() || alarmFlagId == CommonEnum.AlarmFlagEnum.PVHI.getIndex()) {
            thanFlag = ">";
        } else if (alarmFlagId == CommonEnum.AlarmFlagEnum.PVLO.getIndex() || alarmFlagId == CommonEnum.AlarmFlagEnum.PVLL.getIndex()) {
            thanFlag = "<";
        }

        String hql =
                "select count(0) from AlarmEvent t" +
                        " where t.companyId=:companyId and t.alarmPointId = :alarmPointId and " +
                        " t.alarmFlagId = :alarmFlagId and" +
                        " t.eventTypeId = :eventTypeId and" +
                        " t.nowValue is not null and " +
                        " F_OPAL_COMPARE_NUMERIC(t.nowValue,:alarmValue,:thanFlag) = 1  and" +
                        " t.alarmTime between :startTime and :endTime";

        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
        paramList.put("alarmPointId", alarmPointId);
        paramList.put("alarmFlagId", alarmFlagId);
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        paramList.put("alarmValue", alarmValue);
        paramList.put("thanFlag", thanFlag);
//企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());

        Query query = getEntityManager().createQuery(hql.toString());
        this.setParameterList(query, paramList);
        List resultList = query.getResultList();
        int result = 0;
        if (resultList != null && resultList.size() > 0) {
            result = Integer.valueOf(resultList.get(0).toString());
        }
        return result;
    }

    /**
     * 根据报警点ID和报警标识ID获取报警详情-柱状图数据
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @param hourTime     配置时间
     * @param alarmValue   报警值
     * @param isOriginal   是否是原数据，true原数据，false更改后数据
     * @return 报警详情柱状图数据
     * <AUTHOR> 2017-11-08
     */
    @SuppressWarnings("rawtypes")
    public List getHistogramData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId, String hourTime, Double alarmValue, boolean isOriginal) {
        String strWhere = " 1=1 and ";

        Map<String, Object> paramList = new HashMap<>();
        paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
        paramList.put("alarmPointId", alarmPointId);
        paramList.put("alarmFlagId", alarmFlagId);
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        if (!isOriginal && alarmValue != null) {
            String thanFlag = "";
            if (alarmFlagId == CommonEnum.AlarmFlagEnum.PVHH.getIndex() || alarmFlagId == CommonEnum.AlarmFlagEnum.PVHI.getIndex()) {
                thanFlag = ">";
            } else if (alarmFlagId == CommonEnum.AlarmFlagEnum.PVLO.getIndex() || alarmFlagId == CommonEnum.AlarmFlagEnum.PVLL.getIndex()) {
                thanFlag = "<";
            }
            strWhere = " F_OPAL_COMPARE_NUMERIC(t.nowValue,:alarmValue,:thanFlag) = 1  and";
            paramList.put("alarmValue", alarmValue);
            paramList.put("thanFlag", thanFlag);
        }
        String hql = "select count(0) as times, case" +
                "                 when t.startTime >=" +
                "                      " + DbConversion.dateFunction() + "(concat(" + DbConversion.toCharFunction() + "(t.startTime, '" + DbConversion.dateYmdFunction() + "'),'" + hourTime + "'),'" + DbConversion.dateYmdhmsFunction() + "') then" +
                "                      " + DbConversion.dateFunction() + "(" + DbConversion.toCharFunction() + "(t.startTime, '" + DbConversion.dateYmdFunction() + "'), '" + DbConversion.dateYmdFunction() + "')" +
                "                 else" +
                "                  F_OPAL_DATESUB(" + DbConversion.dateFunction() + "(" + DbConversion.toCharFunction() + "(t.startTime, '" + DbConversion.dateYmdFunction() + "'),'" + DbConversion.dateYmdFunction() + "'),1,day)" +
                "               end as startDate" +
                "          FROM AlarmEvent t  " +
                " where t.companyId=:companyId and t.alarmPointId = :alarmPointId and " +
                " t.eventTypeId = :eventTypeId and " +
                " t.alarmFlagId = :alarmFlagId and" + strWhere +
                " t.startTime between :startTime and :endTime" +
                " group by " +
                "               case" +
                "                 when t.startTime >=" +
                "                      " + DbConversion.dateFunction() + "(concat(" + DbConversion.toCharFunction() + "(t.startTime, '" + DbConversion.dateYmdFunction() + "'),'" + hourTime + "'),'" + DbConversion.dateYmdhmsFunction() + "') then" +
                "                      " + DbConversion.dateFunction() + "(" + DbConversion.toCharFunction() + "(t.startTime, '" + DbConversion.dateYmdFunction() + "'), '" + DbConversion.dateYmdFunction() + "')" +
                "                 else" +
                "                  F_OPAL_DATESUB(" + DbConversion.dateFunction() + "(" + DbConversion.toCharFunction() + "(t.startTime, '" + DbConversion.dateYmdFunction() + "'),'" + DbConversion.dateYmdFunction() + "'),1,day)" +
                "                 end ";


        Query query = getEntityManager().createQuery(hql.toString());
        this.setParameterList(query, paramList);
        return query.getResultList();
    }

    /**
     * 根据报警点ID和报警标识ID获取报警详情-折线图数据
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @return 报警详情折线图数据
     * <AUTHOR> 2017-11-08
     */
    @SuppressWarnings("rawtypes")
    public List getLineChartData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId) {
        String hql = " select t.nowValue, " + DbConversion.DbDateTransformYmdhmsToChar("t.alarmTime") +
                "          FROM AlarmEvent t  " +
                " where t.companyId=:companyId  and t.alarmPointId = :alarmPointId and " +
                " t.alarmFlagId = :alarmFlagId and" +
                " t.eventTypeId = :eventTypeId and" +
                " t.alarmTime between :startTime and :endTime " +
                " and t.nowValue is not null " +
                " and F_OPAL_ISNUMERIC(t.nowValue) =1" +
                " order by t.startTime";
//        else{
//            hql = " select t.nowValue,date_format( t.alarmTime, '%Y-%m-%d %H:%i:%s') " +
//                    "          FROM AlarmEvent t  " +
//                    " where t.alarmPointId = :alarmPointId and " +
//                    " t.alarmFlagId = :alarmFlagId and" +
//                    " t.eventTypeId = :eventTypeId and" +
//                    " t.alarmTime between :startTime and :endTime " +
//                    " and F_OPAL_ISNUMERIC(t.nowValue) =1" +
//                    " order by t.startTime";
//        }
        Map<String, Object> paramList = new HashMap<>();
        paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
        paramList.put("alarmPointId", alarmPointId);
        paramList.put("alarmFlagId", alarmFlagId);
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
//企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());

        Query query = getEntityManager().createQuery(hql.toString());
        this.setParameterList(query, paramList);
        return query.getResultList();
    }

    /**
     * 持续报警分析分页查询
     *
     * @param unitCodes   装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param alarmFlagId 报警标识
     * @param beginTime   报警事件开始间
     * @param endTime     报警事件结束时间
     * @param page        翻页实现类
     * <AUTHOR> 2017-11-02
     * @return  持续报警事件实体
     */
    @Override
    public PaginationBean<AlarmEvent> getPersistentAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds, Long alarmFlagId,
                                                                 Date beginTime, Date endTime, Pagination page) {
        try {
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 查询字符串
            String hql = this.getQueryString(unitCodes, prdtCellIds, alarmFlagId, null, null, null, null, null, null,
                    paramList);
            StringBuilder hqlWhere = new StringBuilder();
            // 子查询 过滤查询时间范围内只有过程报警一个事件
            hqlWhere.append(" and ae.eventId in "
                    + "(select max(case when eventTypeId = :eventTypeId then eventId else null end) "
                    + " from AlarmEvent where companyId=:companyId and startTime between :beginTime and :endTime "
                    + " group by alarmPointId,alarmFlagId,alarmTime having count(*)=1)");
            paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
            // 过滤日期
            hqlWhere.append("and ae.companyId=:companyId and ae.alarmTime between :beginTime and :filterEndTime order by ae.alarmTime desc ");
            paramList.put("beginTime", beginTime);
            paramList.put("endTime", endTime);
            paramList.put("filterEndTime", DateUtils.addDays(endTime, -1));// --过滤报警超过24小时的数据
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());

            // 调用基类方法查询返回结果
            PaginationBean<AlarmEvent> pageListEvent = this.findAll(page, hql + hqlWhere.toString(), paramList);
            return pageListEvent;
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 搁置报警分析分页查询
     *
     * @param unitCodes     装置编码数组
     * @param prdtCellIds   生产单元id数组
     * @param alarmFlagId   报警标识
     * @param beginTime     报警事件开始间
     * @param endTime       报警事件结束时间
     * @param dateRangeList 日期区间对象集合
     * @param page          翻页实现类
     * @throws Exception
     * <AUTHOR> 2017-11-02
     * @return 报警事件的分页数据
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    @Transactional
    public PaginationBean<AlarmEvent> getShelveAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds, Long alarmFlagId,
                                                             Date beginTime, Date endTime, List dateRangeList, Pagination page) throws Exception {
        try {
            List<DateRange> dateList = ObjectConverter.listConverter(dateRangeList, DateRange.class);
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 查询字符串
            StringBuilder hql = new StringBuilder(this.getQueryString(unitCodes, prdtCellIds, alarmFlagId, TimeFilterTypeEnum.StartTime, beginTime, endTime,
                    new Long[]{EventTypeEnum.ShelveEvent.getIndex()}, null, null, paramList));
            if (dateList != null && dateList.size() != 0) {
                this.saveDateRangeList(dateList);
                hql.append(" and exists(select dr from  DateRange dr where ae.startTime >=dr.startTime and ae.startTime<dr.endTime) ");
            }
            //排序
            hql.append(" order by ae.startTime desc,ap.tag,af.name asc");
            // 调用基类方法查询返回结果
            PaginationBean<AlarmEvent> pageListEvent = this.findAll(page, hql.toString(), paramList);
            return pageListEvent;
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 屏蔽报警分析分页查询
     *
     * @param unitCodes     装置编码数组
     * @param prdtCellIds   生产单元id数组
     * @param alarmFlagId   报警标识
     * @param eventTypeIds  报警事件类型id数组
     * @param beginTime     报警事件开始间
     * @param endTime       报警事件结束时间
     * @param dateRangeList 日期区间对象集合
     * @param page          翻页实现类
     * @throws Exception
     * <AUTHOR> 2017-11-02
     * @return 报警事件的分页数据
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    @Transactional
    public PaginationBean<AlarmEvent> getShieldAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds, Long alarmFlagId, Long[] eventTypeIds,
                                                             Date beginTime, Date endTime, List dateRangeList, Pagination page) throws Exception {
        try {
            List<DateRange> dateList = ObjectConverter.listConverter(dateRangeList, DateRange.class);
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 查询字符串
            StringBuilder hql = new StringBuilder(
                    this.getQueryString(unitCodes, prdtCellIds, alarmFlagId, TimeFilterTypeEnum.StartTime, beginTime,
                            endTime, eventTypeIds, null, null, paramList));
            if (dateList != null && dateList.size() != 0) {
                this.saveDateRangeList(dateList);
                hql.append(" and exists(select dr from  DateRange dr where ae.startTime >=dr.startTime and ae.startTime<dr.endTime) ");
            }
            // 排序
            hql.append(" order by ae.startTime desc,ap.tag,af.name asc");
            // 调用基类方法查询返回结果
            PaginationBean<AlarmEvent> pageListEvent = this.findAll(page, hql.toString(), paramList);
            return pageListEvent;
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 查询报警数详情集合
     * <p>
     *  * <AUTHOR> 2017-12-06
     *
     * @param prdtCellId 生产单元id
     * @param unitCodes  装置编码集合
     * @param beginTime  查询开始时间
     * @param endTime    查询结束时间
     * @param page       分页对象
     * @return PaginationBean<AlarmEvent> 返回AlarmEvent实体分页对象
     */
    @Override
    public PaginationBean<AlarmEvent> getAlarmNumberDetail(Long prdtCellId, String[] unitCodes, Date beginTime, Date endTime, Pagination page) {
        // 查询字符串
        StringBuilder hql = new StringBuilder("select ae from AlarmEvent ae ");
        StringBuilder hqlWhere = new StringBuilder("where ae.eventTypeId=:eventTypeId and ae.companyId=:companyId");
        hqlWhere.append(" and ap.inUse = 1 ");
//                "and ae.alarmPointId is not null "
//                + " and ae.alarmFlagId is not null "
//                + " and ae.priority is not null "

        // 参数集合
        Map<String, Object> paramList = new HashMap<String, Object>();
        // 关联报警标识
        hql.append("inner join fetch ae.alarmFlag af ");
        // 关联报警点
        hql.append("inner join fetch ae.alarmPoint ap ");
        // 关联生产单元
        hql.append("inner join fetch ap.prdtCell pc ");
        paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
        if (prdtCellId != null) {
            hqlWhere.append("and pc.prdtCellId=:prdtCellId ");
            paramList.put("prdtCellId", prdtCellId);
        }
        if (unitCodes != null && unitCodes.length > 0) {
            hqlWhere.append("and pc.unitId in (:unitIds) ");
            paramList.put("unitIds", Arrays.asList(unitCodes));
        }
        // 过滤日期
        if (beginTime != null && endTime != null) {
            hqlWhere.append("and ae.alarmTime between :beginTime and :endTime order by ae.alarmTime desc,ap.tag,af.name ");
            paramList.put("beginTime", beginTime);
            paramList.put("endTime", endTime);
        }
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        // 调用基类方法查询返回结果
        PaginationBean<AlarmEvent> bean = this.findAll(page, hql.toString() + hqlWhere.toString(), paramList);
        return bean;
    }

    /**
     * 获取变更记录数据
     *
     * @param unitCodes     装置编码数组
     * @param prdtCellIds   生产单元ID数组
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param dateRangeList 日期区间列表
     * @param page          分页对象
     * @return 报警事件分页对象
     * @throws Exception
     * <AUTHOR> 2017-11-10
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    @Transactional
    public PaginationBean<AlarmEvent> getAlarmChangeRecord(String[] unitCodes, Long[] prdtCellIds, Date startTime,
                                                           Date endTime, List dateRangeList, Pagination page, Integer AlarmChangeRecordBusinessType) throws Exception {
        try {
            List<DateRange> dateList = ObjectConverter.listConverter(dateRangeList, DateRange.class);
            StringBuilder hql = new StringBuilder(
                    " select ae from AlarmEvent ae "
                            + " inner join fetch ae.alarmPoint ap "
                            + " inner join fetch ap.prdtCell pc "
                            + " left join fetch ae.alarmFlag af "
                            + " inner join fetch ae.eventType et "
                            + " where ap.inUse =1 and ae.eventTypeId = :eventTypeId and ae.companyId=:companyId and ap.companyId=:companyId and pc.companyId=:companyId ");
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (null == AlarmChangeRecordBusinessType || AlarmChangeRecordBusinessType == 1) {
                paramList.put("eventTypeId", EventTypeEnum.ChangeRecordEvent.getIndex());
            } else if (AlarmChangeRecordBusinessType == 2) {
                paramList.put("eventTypeId", EventTypeEnum.AdjustRecordEvent.getIndex());
            }
            if (unitCodes != null && unitCodes.length > 0) {
                hql.append(" and pc.unitId in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            if (prdtCellIds != null && prdtCellIds.length > 0) {
                hql.append(" and ap.prdtCellId in (:prdtCellIds) ");
                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
            }
            if (startTime != null && endTime != null) {
                hql.append(" and (ae.startTime between :startTime and :endTime) ");
                paramList.put("startTime", startTime);
                paramList.put("endTime", endTime);
            }
            if (dateList != null && dateList.size() > 0) {
                this.saveDateRangeList(dateList);
                hql.append(" and exists(select dr from  DateRange dr where ae.startTime >=dr.startTime and ae.startTime<dr.endTime) ");
            }
            hql.append(" order by F_OPAL_INCRAFTRANGE(ae.nowValue, ap.craftUpLimitInclude, ap.craftDownLimitInclude, ap.craftUpLimitValue, ap.craftDownLimitValue) asc ");
            hql.append(" , ae.startTime desc, ap.tag asc, ae.alarmFlagId asc ");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 查询时序事件分析图形业务数据访问对象层
     *
     * @param unitCodes   装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param beginTime   报警事件的开始间
     * @param endTime     报警事件的结束时间
     * <AUTHOR> 2017-11-16
     * @return List<AlarmEvent> 返回AlarmEvent实体集合
     */
    @Override
    public List<AlarmEvent> getSequentialEventGraph(String[] unitCodes, Long[] prdtCellIds, Date beginTime, Date endTime) {
        try {
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();

            String hql = this.getQueryString(unitCodes, prdtCellIds, null, TimeFilterTypeEnum.StartTime, beginTime,
                    endTime, null, null, null, paramList);
            hql += " order by ae.startTime desc,ap.tag,ae.alarmFlagId ";
            // 调用基类方法查询返回结果
            TypedQuery<AlarmEvent> query = getEntityManager().createQuery(hql, AlarmEvent.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取报警操作评估首页数据
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param unitCodes 装置编码集合
     * @param prdtIds   生产单元ID集合
     * @param dateType  查询日期类型(日,周,月)
     * @param hour      需要减去的小时数
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-20
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public List getAlarmOperateAssess(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, String dateType, int hour) throws Exception {
        String startDateStr = DateFormatUtils.format(DateUtils.addSeconds(startTime, -1), "yyyy-MM-dd HH:mm:ss");
        String selectClause;
        String whereClause;
        String groupByClause;
        Map<String, Object> paramList = new HashedMap();
        try {
            //1.计算查询类型
            if (prdtIds != null && prdtIds.length != 0) {
                selectClause = " ae.prdtCellId ";
                whereClause = " and ae.prdtCellId in(:prdtIds) ";
                groupByClause = ",ae.prdtCellId ";
                paramList.put("prdtIds", Arrays.asList(prdtIds));
            } else {
                selectClause = " ae.unitCode ";
                whereClause = " and ae.unitCode in(:unitIds) ";
                groupByClause = ",ae.unitCode ";
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            if (startTime != null) {
                paramList.put("startTime", startTime);
            }
            if (endTime != null) {
                paramList.put("endTime", endTime);
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            //此处F_OPAL_GETTIMESEGMENT使用占位符替换会有异常,暂时延后处理;
            String hql = "select " +
                    "F_OPAL_GETTIMESEGMENT(ae.startTime,'" + startDateStr + "'," + hour + ",'" + dateType.toUpperCase() + "')," +
                    "count(*)," +
                    " %s " +
                    " from AlarmEvent ae "
                    + " inner join ae.alarmPoint ap "
//                    + " inner join ap.prdtCell pc "
//                    + " inner join ae.eventType et "
                    + " where  ae.eventTypeId in (1006, 1003, 1004,30013002,3003,3004,3005,3006,3007,300101,300102) and ae.startTime >= :startTime and ae.startTime <=:endTime and ae.priority is not null and ae.alarmFlagId is not null and ap.inUse =1 " +
                    " and ae.companyId=:companyId " +
                    " %s " +
                    " group by F_OPAL_GETTIMESEGMENT(ae.startTime,'" + startDateStr + "'," + hour + ",'" + dateType.toUpperCase() + "')%s";
            hql = String.format(hql, selectClause, whereClause, groupByClause);
            Query query = getEntityManager().createQuery(hql);
            setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取最频繁的操作Top20
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param unitCodes 装置编码集合
     * @param prdtIds   生产单元ID集合
     * @param topType   Top20,Top10切换选择
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-20
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public List getAlarmOperateTop20(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, Integer topType) throws Exception {
        try {
            String hql = "select min(ap.tag),min(af.name),count(*),min(ae.priority),min(pc.sname),min(ae.unitCode),ae.alarmPointId,ae.alarmFlagId,ap.location from AlarmEvent ae "
                    + " inner join ae.alarmPoint ap "
                    + " inner join ap.prdtCell pc "
                    + " inner join ae.alarmFlag af "
//                    + " inner join ae.eventType et "
                    + " %s group by ae.unitCode,ae.prdtCellId,ae.alarmPointId,ae.alarmFlagId "
                    + " order by count(*) desc,min(ap.tag) asc,min(af.name) asc";

            Map<String, Object> paramList = new HashedMap();
            StringBuilder whereBuilder = new StringBuilder();
            whereBuilder.append(" where (ae.eventTypeId in  (1006, 1003, 1004, 30013002, 3003, 3004, 3005, 3006, 3007, 300101, 300102)) ");
            whereBuilder.append(" and ae.priority is not null and ap.inUse =1 and ae.companyId=:companyId ");
            if (prdtIds != null && prdtIds.length != 0) {
                whereBuilder.append(" and ae.prdtCellId in(:prdtIds) ");
                paramList.put("prdtIds", Arrays.asList(prdtIds));
            }
            if (unitCodes != null && unitCodes.length != 0) {
                whereBuilder.append(" and ae.unitCode in(:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            if (startTime != null) {
                whereBuilder.append(" and ae.startTime>=:startTime ");
                paramList.put("startTime", startTime);
            }
            if (endTime != null) {
                whereBuilder.append(" and ae.startTime<:endTime ");
                paramList.put("endTime", endTime);
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            hql = String.format(hql, whereBuilder.toString());
            Query query = getEntityManager().createQuery(hql);
            setParameterList(query, paramList);
            query.setMaxResults(topType);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取时间粒度字符串
     *
     * @param dateType  时间粒度
     * @param startTime 时间范围起始
     * @param hours     校正时间
     * @param timeType  时间类型
     * @return 分组条件——时间粒度字符串
     * <AUTHOR> 2017-11-07
     */
    private String getDateType(CommonEnum.DateTypeEnum dateType, Date startTime, Integer hours, String timeType) {
        String selectTimeStr = null;
        switch (dateType) {
            case Hour:
                selectTimeStr = "concat(F_OPAL_GetTimeSegment(ae." + timeType + ",'', 0, 'HOUR'),':00:00')";
                break;
            case Day:
                selectTimeStr = "F_OPAL_GetTimeSegment(ae." + timeType + ",'', " + hours + ", 'DAY')";
                break;
            case Week:
                Calendar cal = Calendar.getInstance();
                cal.setTime(startTime);
                int w = cal.get(Calendar.DAY_OF_WEEK);
                selectTimeStr = "to_char(next_day(ae." + timeType + "-7- " + hours + "/24," + w + "),'YYYY-MM-DD')";
                break;
            case Month:
                selectTimeStr = "F_OPAL_GetTimeSegment(ae." + timeType + ",'', " + hours + ", 'MONTH')";
                break;
            default:
                selectTimeStr = "F_OPAL_GetTimeSegment(ae." + timeType + ",'', " + hours + ", 'DAY')";
                break;
        }
        return selectTimeStr;
    }

    /**
     * 获取时间粒度字符串
     *
     * @param dateType  时间粒度
     * @param startTime 时间范围起始
     * @param hours     校正时间
     * @param timeType  时间类型
     * @return 分组条件——时间粒度字符串
     * <AUTHOR> 2019-12-30
     */
    private String getDateTypeBySQL(CommonEnum.DateTypeEnum dateType, Date startTime, Integer hours, String timeType) {
        String selectTimeStr = null;
        switch (dateType) {
            case Hour:
                //selectTimeStr = "concat(F_OPAL_GetTimeSegment(ae." + timeType + ",'', 0, 'HOUR'),':00:00')";
                //selectTimeStr = "concat(TO_CHAR(" + timeType + " - NUMTODSINTERVAL(0, 'HOUR'), 'YYYY-MM-DD HH24'),':00:00')";
                selectTimeStr = "concat(" + DbConversion.numtodsintervalYmdh(timeType) + ",':00:00')";
                ;
                break;
            case Day:
                //selectTimeStr = "F_OPAL_GetTimeSegment(ae." + timeType + ",'', " + hours + ", 'DAY')";
//                    selectTimeStr = "TO_CHAR(" + timeType + " - NUMTODSINTERVAL(" + hours + ", 'HOUR'),'YYYY-MM-DD')";
                selectTimeStr = DbConversion.numtodsintervalYmd(timeType, hours);
                break;
            case Week:
//                Calendar cal = Calendar.getInstance();
//                cal.setTime(startTime);
//                int w = cal.get(Calendar.DAY_OF_WEEK);
//                selectTimeStr = "to_char(next_day(ae." + timeType + "-7- " + hours + "/24," + w + "),'YYYY-MM-DD')";
                selectTimeStr = DbConversion.numtodsintervalYmdWeekSql(timeType, hours, startTime);
                break;
            case Month:
                //selectTimeStr = "F_OPAL_GetTimeSegment(ae." + timeType + ",'', " + hours + ", 'MONTH')";
//                selectTimeStr = "TO_CHAR(" + timeType + " - NUMTODSINTERVAL(" + hours + ", 'HOUR'), 'YYYY-MM')";
                selectTimeStr = DbConversion.numtodsintervalYm(timeType, hours);
                break;
            default:
                //selectTimeStr = "F_OPAL_GetTimeSegment(ae." + timeType + ",'', " + hours + ", 'DAY')";
//                    selectTimeStr = "TO_CHAR(" + timeType + " - NUMTODSINTERVAL( " + hours + ", 'HOUR'),'YYYY-MM-DD')";
                selectTimeStr = DbConversion.numtodsintervalYmd(timeType, hours);
                break;
        }
        return selectTimeStr;
    }

    /**
     * 构造查询语句
     *
     * @param unitCodes    装置编码数组
     * @param prdtCellIds  生产单元id数组
     * @param alarmFlagId  报警标识
     * @param beginTime    报警事件开始间
     * @param endTime      报警事件结束时间
     * @param eventTypeIds 报警事件类型ID集合
     * @param craftRank    级别(1A；2B)
     * @param paramList    参数集合
     * @return 查询语句
     * <AUTHOR> 2017-11-4
     */
    private String getQueryString(String[] unitCodes, Long[] prdtCellIds, Long alarmFlagId, TimeFilterTypeEnum timeType,
                                  Date beginTime, Date endTime, Long[] eventTypeIds, Long[] eventTypeParentIds, Integer craftRank,
                                  Map<String, Object> paramList) {
        return this.getQueryString(unitCodes, prdtCellIds, alarmFlagId, timeType, beginTime, endTime, eventTypeIds,
                eventTypeParentIds, craftRank, null, null, paramList);
    }

    /**
     * 构造查询语句
     *
     * @param unitCodes    装置编码数组
     * @param prdtCellIds  生产单元id数组
     * @param alarmFlagId  报警标识
     * @param beginTime    报警事件开始间
     * @param endTime      报警事件结束时间
     * @param eventTypeIds 报警事件类型ID集合
     * @param craftRank    级别(1A；2B)
     * @param tag          位号
     * @param priority     优先级
     * @param paramList    参数集合
     * @return 查询语句
     * <AUTHOR> 2017-11-4
     */
    private String getQueryString(String[] unitCodes, Long[] prdtCellIds, Long alarmFlagId, TimeFilterTypeEnum timeType, Date beginTime, Date endTime, Long[] eventTypeIds,
                                  Long[] eventTypeParentIds, Integer craftRank, String tag, Integer priority, Map<String, Object> paramList) {
        // 查询字符串
        StringBuilder hql = new StringBuilder("select ae from AlarmEvent ae ");

        StringBuilder hqlWhere = new StringBuilder("where ae.priority is not null and ae.companyId=:companyId and ap.companyId=:companyId and pc.companyId=:companyId ");
        //启用的报警点
        hqlWhere.append(" and ap.inUse =1 ");
        // 关联报警点
        hql.append("inner join fetch ae.alarmPoint ap ");
        // 关联报警标识
        hql.append("inner join fetch ae.alarmFlag af ");
        // 关联生产单元
        hql.append("inner join fetch ap.prdtCell pc ");
        // 关联报警事件
        hql.append("inner join fetch ae.eventType et ");
        // 关联计量单位
        hql.append("left join fetch ap.measUnit mu ");
        //过滤报警标识
        hqlWhere.append(" and exists (from AlarmFlagComp afc where afc.alarmFlagId=af.alarmFlagId and afc.inUse=1) ");
        //过滤事件ID
        if (ArrayUtils.isNotEmpty(eventTypeIds) && ArrayUtils.isNotEmpty(eventTypeParentIds)) {
            hqlWhere.append(" and (ae.eventTypeId in (:eventTypeIds) or et.parentId in (:eventTypeParentIds) ) ");
            paramList.put("eventTypeIds", Arrays.asList(eventTypeIds));
            paramList.put("eventTypeParentIds", Arrays.asList(eventTypeParentIds));
        } else {
            if (ArrayUtils.isNotEmpty(eventTypeIds)) {
                hqlWhere.append(" and ae.eventTypeId in (:eventTypeIds) ");
                paramList.put("eventTypeIds", Arrays.asList(eventTypeIds));
            }
            //过滤事件父ID
            if (ArrayUtils.isNotEmpty(eventTypeParentIds)) {
                hqlWhere.append(" and et.parentId in (:eventTypeParentIds) ");
                paramList.put("eventTypeParentIds", Arrays.asList(eventTypeParentIds));
            }
        }
        // 过滤生产单元
        if (ArrayUtils.isNotEmpty(prdtCellIds)) {
            hqlWhere.append("and pc.prdtCellId in (:prdtCellIds) ");
            paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
        }
        // 过滤报警点标识
        if (alarmFlagId != null) {
            hqlWhere.append("and ae.alarmFlagId=:alarmFlagId ");
            paramList.put("alarmFlagId", alarmFlagId);
        }
        //过滤位号
        if (!StringUtils.isEmpty(tag)) {
            hqlWhere.append("  and (upper(ap.tag) like upper(:tag) escape '/') ");
            paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
        }
        //过滤优先级
        if (priority != null) {
            hqlWhere.append("  and ae.priority =:priority ");
            paramList.put("priority", priority);
        }
        // 过滤日期
        if (beginTime != null && endTime != null) {
            if (timeType.equals(TimeFilterTypeEnum.StartTime)) {
                hqlWhere.append("and ae.startTime between :beginTime and :endTime ");
            } else {
                hqlWhere.append("and ae.alarmTime between :beginTime and :endTime ");
            }
            paramList.put("beginTime", beginTime);
            paramList.put("endTime", endTime);
        }
        // 过滤级别
        if (craftRank != null) {
            hqlWhere.append("  and ap.craftRank =:craftRank ");
            paramList.put("craftRank", craftRank);
        }
        // 过滤装置
        if (ArrayUtils.isNotEmpty(unitCodes)) {
            hqlWhere.append("and pc.unitId in (:unitIds) ");
            paramList.put("unitIds", Arrays.asList(unitCodes));
        }
        // 过滤报警点标识
        if (timeType.equals(TimeFilterTypeEnum.ALarmTime) && !StringUtils.isEmpty(tag)) {
            hqlWhere.append("  and ap.tag=:tag ");
            paramList.put("tag", tag);
        }
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());

        return hql.toString() + hqlWhere.toString();
    }

    /**
     * 构造查询语句
     *
     * @param unitCodes    装置编码数组
     * @param prdtCellIds  生产单元id数组
     * @param alarmFlagId  报警标识
     * @param beginTime    报警事件开始间
     * @param endTime      报警事件结束时间
     * @param eventTypeIds 报警事件类型ID集合
     * @param craftRank    级别(1A；2B)
     * @param tag          位号
     * @param priority     优先级
     * @param paramList    参数集合
     * @param isMatching
     * @return 查询语句
     * <AUTHOR> 2019-12-17
     */
    private String getQueryStringForAlarmEvent(String[] unitCodes, Long[] prdtCellIds, Long alarmFlagId, TimeFilterTypeEnum timeType, Date beginTime, Date endTime, Long[] eventTypeIds,
                                               Long[] eventTypeParentIds, Integer craftRank, String tag, Integer priority, Integer monitorType, Integer[] prioritys, Map<String, Object> paramList, Integer isMatching) {
        // 查询字符串
        StringBuilder hql = new StringBuilder("select ae from AlarmEvent ae ");

        StringBuilder hqlWhere = new StringBuilder("where 1=1 and  case when ae.alarmPointId is not null then ap.inUse else 1  end =1 and ae.eventTypeId is not null and ae.prdtCell is not null ");
//        StringBuilder hqlWhere = new StringBuilder("where 1=1 and ae.companyId=:companyId and ap.companyId=:companyId and pc.companyId=:companyId and case when ae.alarmPointId is not null then ap.inUse else 1  end =1 ");
        // 关联报警点
        hql.append("left join fetch ae.alarmPoint ap ");
        // 关联报警标识
        hql.append("left join fetch ae.alarmFlag af ");
        // 关联生产单元
        hql.append("left join fetch ae.prdtCell pc ");
        // 关联报警事件
        hql.append("left join fetch ae.eventType et ");
        // 关联计量单位
        hql.append("left join fetch ap.measUnit mu ");
        //过滤报警标识
//        hqlWhere.append(" and exists (from AlarmFlagComp afc where (afc.alarmFlagId=af.alarmFlagId and afc.inUse=1) or ae.alarmFlagId is null) ");

        //报警点是否匹配
        if (isMatching == 1) {
            hqlWhere.append(" and ae.alarmPointId is null ");
        } else if (isMatching == 2) {
            hqlWhere.append(" and ae.alarmPointId is not null ");
        }


        //过滤事件ID
        if (ArrayUtils.isNotEmpty(eventTypeIds) && ArrayUtils.isNotEmpty(eventTypeParentIds)) {
            hqlWhere.append(" and (ae.eventTypeId in (:eventTypeIds) or et.parentId in (:eventTypeParentIds) ) ");
            paramList.put("eventTypeIds", Arrays.asList(eventTypeIds));
            paramList.put("eventTypeParentIds", Arrays.asList(eventTypeParentIds));
        } else {
            if (ArrayUtils.isNotEmpty(eventTypeIds)) {
                hqlWhere.append(" and ae.eventTypeId in (:eventTypeIds) ");
                paramList.put("eventTypeIds", Arrays.asList(eventTypeIds));
            }
            //过滤事件父ID
            if (ArrayUtils.isNotEmpty(eventTypeParentIds)) {
                hqlWhere.append(" and et.parentId in (:eventTypeParentIds) ");
                paramList.put("eventTypeParentIds", Arrays.asList(eventTypeParentIds));
            }
        }
        // 过滤生产单元
        if (ArrayUtils.isNotEmpty(prdtCellIds)) {
            hqlWhere.append("and ae.prdtCellId in (:prdtCellIds) ");
            paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
        }
        // 过滤报警点标识
        if (alarmFlagId != null) {
            hqlWhere.append("and ae.alarmFlagId=:alarmFlagId ");
            paramList.put("alarmFlagId", alarmFlagId);
        }
        //过滤位号
        if (!StringUtils.isEmpty(tag)) {
            hqlWhere.append("  and (upper(case when ae.alarmPointId is null then upper(ae.tag)   else upper(ap.tag)  end) like upper(:tag) escape '/') ");
            paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
        }
        //过滤优先级
        if (priority != null) {
            if (priority != 9) {
                hqlWhere.append("  and ae.priority =:priority ");
                paramList.put("priority", priority);
            }
            if (priority == 9) {
                hqlWhere.append("  and ae.priority is null ");
            }
        }
        //过滤监测类型
        if (monitorType != null && monitorType!=-1) {
                hqlWhere.append("  and ap.monitorType = :monitorType ");
                paramList.put("monitorType", monitorType);
        }
        //过滤优先级
        if (ArrayUtils.isNotEmpty(prioritys)) {
            if (ArrayUtils.contains(prioritys, 9)) {
                hqlWhere.append("and ( ae.priority in (:prioritys) or ae.priority is null )");
            } else {
                hqlWhere.append("and ae.priority in (:prioritys) ");
            }
            paramList.put("prioritys", Arrays.asList(prioritys));
        }

        // 过滤日期
        if (beginTime != null && endTime != null) {
            if (timeType.equals(TimeFilterTypeEnum.StartTime)) {
                hqlWhere.append("and ae.startTime between :beginTime and :endTime ");
            } else {
                hqlWhere.append("and ae.alarmTime between :beginTime and :endTime ");
            }
            paramList.put("beginTime", beginTime);
            paramList.put("endTime", endTime);
        }
        // 过滤级别
        if (craftRank != null) {
            hqlWhere.append("  and ap.craftRank =:craftRank ");
            paramList.put("craftRank", craftRank);
        }
        // 过滤装置
        if (ArrayUtils.isNotEmpty(unitCodes)) {
            hqlWhere.append("and ae.unitCode in (:unitIds) ");
            paramList.put("unitIds", Arrays.asList(unitCodes));
        }
        // 过滤报警点标识
        if (timeType.equals(TimeFilterTypeEnum.ALarmTime) && !StringUtils.isEmpty(tag)) {
            hqlWhere.append("  and ap.tag=:tag ");
            paramList.put("tag", tag);
        }
        //企业
//        CommonProperty commonProperty = new CommonProperty();
//        paramList.put("companyId", commonProperty.getCompanyId());
        return hql.toString() + hqlWhere.toString();
    }



    /**
     * 查看报警事件中是否有报警点
     * <p>
     *  * <AUTHOR> 2017-12-07
     *
     * @param alarmPointIds 报警点维护主键Id集合
     * @return 
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<AlarmEvent> getListByAlarmPointIds(Long[] alarmPointIds) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("select ae from AlarmEvent ae where 1=1 and ae.companyId=:companyId ");
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 报警点Id集合
            if (ArrayUtils.isNotEmpty(alarmPointIds)) {
                hql.append("and ae.alarmPointId in (:alarmPointIds) ");
                paramList.put("alarmPointIds", Arrays.asList(alarmPointIds));
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());

            Query query = getEntityManager().createQuery(hql.toString());
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取报警事件实体
     *
     * @param alarmPointId 报警点Id
     * @param alarmFlagId  报警标识Id
     * @return AlarmEvent 获取报警事件实体
     * <AUTHOR> 2018-01-22
     * @return 
     */
    public AlarmEvent getAlarmEventByPointFlag(Long alarmPointId, Long alarmFlagId) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from AlarmEvent t where alarmPointId =:alarmPointId and alarmFlagId = :alarmFlagId and t.companyId=:companyId order by t.startTime desc");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("alarmPointId", alarmPointId);
            paramList.put("alarmFlagId", alarmFlagId);
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());

            TypedQuery<AlarmEvent> query = getEntityManager().createQuery(hql.toString(), AlarmEvent.class);
            this.setParameterList(query, paramList);
            List listResut = query.setFirstResult(0).setMaxResults(1).getResultList();
            if (listResut == null || listResut.size() == 0) {
                return null;
            } else {
                return (AlarmEvent) listResut.get(0);
            }
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 优先级评估-表格显示
     *
     * @param startTime 查询开始时间
     * @param endTime   查询结束时间
     * @param unitCode  装置编码
     * @return List<Object [ ]> 返回对象数组集合
     * <AUTHOR> 2018-01-22
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<Object[]> getAlarmPriorityAssessTable(Date startTime, Date endTime, String unitCode, Integer priority) {
        try {
            // 查询字符串
            String statisticsHql = "select max(ap.tag),max(af.name),max(pc.sname),count(ap.alarmPointId),max(ae.priority),max(ap.alarmPointId),ae.alarmFlagId ";
            StringBuilder hql = new StringBuilder(" from AlarmEvent ae"
                    + " inner join ae.alarmPoint ap"
                    + " inner join ap.prdtCell pc"
                    + " inner join ae.alarmFlag af"
                    + " where ae.eventTypeId=" + EventTypeEnum.ProcessEvent.getIndex());
            hql.append("  and ae.priority is not null and ap.inUse =1 and ae.companyId=:companyId and ap.companyId=:companyId and pc.companyId=:companyId ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            hql.append(" and ae.alarmTime between :startTime and :endTime ");
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
            // 装置
            hql.append(" and pc.unitId = :unitId");
            paramList.put("unitId", unitCode);
            //过滤优先级
            if (priority != null) {
                hql.append("  and ae.priority =:priority ");
                paramList.put("priority", priority);
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());

            String groupByhql = " group by ap.tag,ae.alarmFlagId,ae.priority order by min(ap.tag) asc,min(af.name) asc";
            Query query = getEntityManager().createQuery(statisticsHql + hql.toString() + groupByhql);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public PaginationBean<AlarmEvent> getAlarmDtail(String alarmPointTag, Long alarmFlagId, Long[] eventTypeIds, Date alarmTime, Date endTime, String dateType, Pagination page) throws Exception {
        Map<String, Object> paramList = new HashedMap();
        // 查询字符串
        StringBuilder hql = new StringBuilder("from AlarmEvent ae ");

        StringBuilder hqlWhere = new StringBuilder("where 1=1 and ap.inUse = 1 and ae.companyId=:companyId and ap.companyId=:companyId and pc.companyId=:companyId ");
        hqlWhere.append(" and ae.priority is not null ");
        // 关联报警点
        hql.append("inner join fetch ae.alarmPoint ap ");
        // 关联报警标识
        hql.append("inner join fetch ae.alarmFlag af ");
        // 关联生产单元
        hql.append("inner join fetch ap.prdtCell pc ");
        // 关联报警事件
        hql.append("inner join fetch ae.eventType et ");
        //过滤事件ID
        hqlWhere.append(" and (ae.eventTypeId in (:eventTypeIds)) ");
        paramList.put("eventTypeIds", Arrays.asList(eventTypeIds));
        //过滤报警标识id
        hqlWhere.append(" and ae.alarmFlagId=:alarmFlagId ");
        paramList.put("alarmFlagId", alarmFlagId);
        //过滤位号
        hqlWhere.append(" and ap.tag=:tag ");
        paramList.put("tag", alarmPointTag);
        //过滤时间
        if (dateType.equals(TimeFilterTypeEnum.ALarmTime.getName())) {
            hqlWhere.append("and ae.alarmTime between :alarmTime and :endTime ");
        } else {
            hqlWhere.append("and ae.startTime between :alarmTime and :endTime ");
        }
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        paramList.put("alarmTime", alarmTime);
        paramList.put("endTime", endTime);
        if (dateType.equals(TimeFilterTypeEnum.ALarmTime.getName())) {
            hqlWhere.append(" order by ae.alarmTime desc, ap.tag asc, ae.alarmFlagId asc ");
        } else {
            hqlWhere.append(" order by ae.startTime desc, ap.tag asc, ae.alarmFlagId asc ");
        }

        return this.findAll(page, hql.toString() + hqlWhere.toString(), paramList);
    }

    @Override
    public List<Object[]> getMonitoringData(String dateStr) {
        CommonProperty commonProperty = new CommonProperty();
        String dataBase = this.dbConfig.getDataBase();
        try {
            StringBuilder hql = new StringBuilder("select min(d.name),\n" +
                    "       min(pc.unit_code),\n" +
                    "       min(pc.sname),\n" +
                    "       min(o.name),\n" +
                    "       " + DbConversion.sysdateDatesub(6) + " as startdate,\n" +
                    "       pc.prdtcell_id\n" +
                    "  from t_pm_prdtcell pc\n" +
                    " inner join t_ad_alarmprdtcellcomp apc\n" +
                    "    on pc.unit_code = apc.unit_code\n" +
                    " inner join t_pm_opccode o\n" +
                    "    on apc.opc_code_id = o.opc_code_id\n" +
                    " inner join t_pm_dcscode d\n" +
                    "    on apc.dcs_code_id = d.dcs_code_id\n" +
                    " where pc.company_id='" + commonProperty.getCompanyId() + "' and apc.company_id='" + commonProperty.getCompanyId() + "' and apc.company_id='" + commonProperty.getCompanyId() + "' and pc.prdtcell_id in\n" +
                    "       (select pc.prdtcell_id\n" +
                    "          from t_pm_prdtcell pc\n" +
                    "         inner join t_pm_alarmpoint ap\n" +
                    "            on ap.prdtcell_id = pc.prdtcell_id\n");
            if ("oracle".equals(dbConfig.getDataBase())) {
                hql.append(
                        "         where pc.in_use = 1\n" +
                                "       pc.company_id='" + commonProperty.getCompanyId() + "' and ap.company_id='" + commonProperty.getCompanyId() + "' and ap.in_use = 1\n" +
                                "        minus\n" +
                                "        select distinct t.prdtcell_id\n" +
                                "          from t_ad_alarmevent t\n" +
                                "         where t.company_id='" + commonProperty.getCompanyId() + "' and t.start_time between " + DbConversion.sysdateDatesub(6) + " and sysdate)\n" +
                                " group by pc.prdtcell_id");
            } else {
                hql.append("          left join \n" +
                        "          (select distinct t.prdtcell_id\n" +
                        "        from t_ad_alarmevent t\n" +
                        "       where t.company_id='" + commonProperty.getCompanyId() + "' and t.start_time between " + DbConversion.sysdateDatesub(6) + " and now()\n" +
                        "       ) t1 on pc.prdtcell_id=t1.prdtcell_id\n" +
                        "       where \n" +
                        "            pc.in_use = 1\n" +
                        "         and ap.in_use = 1\n" +
                        "            )\n" +
                        "             group by pc.prdtcell_id");
            }

            Map<String, Object> paramList = new HashMap<String, Object>();
            //paramList.put("dateStr",dateStr);
            //Query query = getEntityManager().createQuery(hql.toString());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            //this.setParameterList(query, paramList);
            List<Object[]> alarmEventList = query.getResultList();
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public List<String> getListCodes(Date startTime, Date endTime, String strCodes) {
        CommonProperty commonProperty = new CommonProperty();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> list = new ArrayList<String>();
        String codeList = "";
        StringBuilder sbcodes = new StringBuilder();
        if (strCodes != null && !"".equals(strCodes)) {
            String[] codes = strCodes.split(",");
            for (String s : codes) {
                sbcodes.append("'").append(s).append("'").append(",");
            }
            codeList = sbcodes.substring(0, sbcodes.length() - 1);
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT R.Unit_Code\n" +
                "       FROM (select * from T_AD_ALARMEVENT where company_id='" + commonProperty.getCompanyId() + "' and EVENT_TYPE_ID = 1001 " +
                "       AND ALARM_TIME BETWEEN " + DbConversion.dateFunction() + "( '" + sdf.format(startTime) + "', '" + DbConversion.dateYmdhmsFunction() + "' ) AND " + DbConversion.dateFunction() + "( '" + sdf.format(endTime) + "', '" + DbConversion.dateYmdhmsFunction() + "')) T\n" +
                "       INNER JOIN T_PM_ALARMPOINT A ON T.ALARM_POINT_ID = A.ALARM_POINT_ID\n" +
                "       INNER JOIN T_PM_PRDTCELL R   ON A.PRDTCELL_ID = R.PRDTCELL_ID\n" +
                "       LEFT JOIN T_PM_UNITPERSON UP ON R.Unit_Code = UP.Unit_Code\n" +
                "       WHERE A.company_id='" + commonProperty.getCompanyId() + "' AND R.company_id='" + commonProperty.getCompanyId() + "'  \n" +
                "       T.ALARM_POINT_ID is not null and T.ALARM_FLAG_ID is not null and t.priority is not null\n" +
                "       AND A.In_Use = 1\n" +
                "       and R.Unit_Code in (" + codeList + ")\n" +
                "       GROUP BY R.Unit_Code order by count(R.Unit_Code) desc");

        Query query = getEntityManager().createNativeQuery(sql.toString());
        list = query.getResultList();
        return list;
    }

    @Override
    public List<AlarmRec> findAlarmEventInfoByAlarmTime(String startDate, String endDate, Integer companyId) {
        StringBuilder sql = new StringBuilder();
        sql.append("select UNIT_CODE,\n" +
                "       PRDTCELL_ID,\n" +
                "       DCS_CODE,\n" +
                "       EVENT_TYPE_ID,\n" +
                "       ALARM_POINT_ID,\n" +
                "       TAG,\n" +
                "       ALARM_FLAG_ID,\n" +
                "       ALARM_FLAG,\n" +
                "       ALARM_TIME,\n" +
                "       RECOVERY_TIME,\n" +
                "       PRIORITY,\n" +
                "       PRIORITY_CACHE,\n" +
                "       PREVIOUS_VALUE,\n" +
                "       NOW_VALUE,\n" +
                "       LIMIT_VALUE,\n" +
                "       IN_SHELVED,\n" +
                "       IN_SUPPRESSED,\n" +
                "       OPERATOR,\n" +
                "       PARAMETER,\n" +
                "       DES,\n" +
                "       SYSDATE() update_time,\n" +
                "       IS_ACK,event_id\n" +
                "FROM (SELECT LEAD(A.START_TIME, 1)\n" +
                "                  OVER (PARTITION BY UNIT_CODE,PRDTCELL_ID,TAG,ALARM_FLAG ORDER BY A.ALARM_TIME,A.START_TIME,A.EVENT_TYPE_ID) AS RECOVERY_TIME,\n" +
                "             SIGN(SUM(CASE\n" +
                "                          WHEN A.EVENT_TYPE_ID IN (1001, 1002, 1003, 1005) THEN\n" +
                "                              1\n" +
                "                          ELSE\n" +
                "                              0\n" +
                "                          END)\n" +
                "                      OVER (PARTITION BY UNIT_CODE,PRDTCELL_ID,TAG,ALARM_FLAG, A.ALARM_TIME ORDER BY UNIT_CODE,PRDTCELL_ID,TAG,ALARM_FLAG, A.ALARM_TIME,START_TIME,A.EVENT_TYPE_ID ROWS BETWEEN CURRENT\n" +
                "                          ROW AND UNBOUNDED FOLLOWING))                                                                       AS IS_ACK,\n" +
                "             UNIT_CODE,\n" +
                "             PRDTCELL_ID,\n" +
                "             DCS_CODE,\n" +
                "             EVENT_TYPE_ID,\n" +
                "             ALARM_POINT_ID,\n" +
                "             TAG,\n" +
                "             ALARM_FLAG_ID,\n" +
                "             ALARM_FLAG,\n" +
                "             ALARM_TIME,\n" +
                "             PRIORITY,\n" +
                "             PRIORITY_CACHE,\n" +
                "             PREVIOUS_VALUE,\n" +
                "             NOW_VALUE,\n" +
                "             LIMIT_VALUE,\n" +
                "             IN_SHELVED,\n" +
                "             IN_SUPPRESSED,\n" +
                "             OPERATOR,\n" +
                "             PARAMETER,\n" +
                "             DES,event_id\n" +
                "      FROM (SELECT UNIT_CODE,\n" +
                "                   PRDTCELL_ID,\n" +
                "                   DCS_CODE,\n" +
                "                   EVENT_TYPE_ID,\n" +
                "                   ALARM_POINT_ID,\n" +
                "                   START_TIME,\n" +
                "                   (CASE\n" +
                "                        WHEN A.ALARM_POINT_ID IS NOT NULL THEN (SELECT TPA.TAG\n" +
                "                                                                FROM T_PM_ALARMPOINT TPA\n" +
                "                                                                WHERE TPA.ALARM_POINT_ID = A.ALARM_POINT_ID)\n" +
                "                        ELSE A.TAG END)        TAG,\n" +
                "                   ALARM_FLAG_ID,\n" +
                "                   (CASE\n" +
                "                        WHEN A.ALARM_FLAG_ID IS NOT NULL THEN (SELECT TAAG.NAME\n" +
                "                                                               FROM T_AD_ALARMFLAG TAAG\n" +
                "                                                               WHERE TAAG.ALARM_FLAG_ID = A.ALARM_FLAG_ID)\n" +
                "                        ELSE A.ALARM_FLAG END) ALARM_FLAG,\n" +
                "                   ALARM_TIME,\n" +
                "                   PRIORITY,\n" +
                "                   PRIORITY_CACHE,\n" +
                "                   PREVIOUS_VALUE,\n" +
                "                   NOW_VALUE,\n" +
                "                   LIMIT_VALUE,\n" +
                "                   IN_SHELVED,\n" +
                "                   IN_SUPPRESSED,\n" +
                "                   OPERATOR,\n" +
                "                   PARAMETER,\n" +
                "                   DES,a.event_id\n" +
                "            FROM T_AD_ALARMEVENT A\n" +
                "            WHERE A.EVENT_TYPE_ID IN (1001, 1002, 1003, 1005)\n" +
                "              and A.company_id =:companyId\n" +
                "              AND A.START_TIME >= str_to_date(:startDate,'%Y-%m-%d %H:%i:%s') " +
                "              AND A.START_TIME <= str_to_date(:endDate,'%Y-%m-%d %H:%i:%s')) A) A " +
                "WHERE EVENT_TYPE_ID = 1001\n" +
                "  and A.alarm_point_id is not null\n" +
                "  and a.tag is not null\n" +
                "  and a.prdtcell_id is not null\n" +
                "  AND NOT EXISTS\n" +
                "  (SELECT 1\n" +
                "   FROM t_ad_alarmrec T2\n" +
                "   WHERE  a.event_id = t2.event_id)"
        );

        Map<String, Object> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("companyId", companyId);
        Query query = this.getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, param);
//        query.unwrap(NativeQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Object[]> resultList = query.getResultList();

        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        } else {
            ArrayList<AlarmRec> alarmRecs = new ArrayList<>();
            for (Object[] x : resultList) {
                AlarmRec alarmRec = new AlarmRec();
                alarmRec.setUnitCode(x[0] == null ? null : x[0].toString());
                alarmRec.setPrdtCellId(x[1] == null ? null : Long.parseLong(x[1].toString()));
                alarmRec.setDcsCode(x[2] == null ? null : x[2].toString());
                alarmRec.setEventTypeId(x[3] == null ? null : Long.valueOf(x[3].toString()));
                alarmRec.setAlarmPointId(x[4] == null ? null : Long.valueOf(x[4].toString()));
                alarmRec.setTag(x[5] == null ? null : x[5].toString());
                alarmRec.setAlarmFlagId(x[6] == null ? null : Long.valueOf(x[6].toString()));
                alarmRec.setAlarmFlagType(x[7] == null ? null : (x[7].toString()));
                alarmRec.setAlarmTime(x[8] == null ? null : (Date) x[8]);
                alarmRec.setRecoveryTime(x[9] == null ? null : (Date) (x[9]));
                alarmRec.setPriority(x[10] == null ? null : Integer.valueOf(x[10].toString()));
                alarmRec.setPriorityCache(x[11] == null ? null : x[11].toString());
                alarmRec.setPreviousValue(x[12] == null ? null : x[12].toString());
                alarmRec.setNowValue(x[13] == null ? null : x[13].toString());
                alarmRec.setLimitValue(x[14] == null ? null : Double.valueOf(x[14].toString()));
                alarmRec.setInShelved(x[15] == null ? null : Integer.valueOf(x[15].toString()));
                alarmRec.setInSuppressed(x[16] == null ? null : Integer.valueOf(x[16].toString()));
                alarmRec.setOperator(x[17] == null ? null : x[17].toString());
                alarmRec.setParameter(x[18] == null ? null : x[18].toString());
                alarmRec.setDes(x[19] == null ? null : x[19].toString());
                alarmRec.setUpdateTime(x[20] == null ? null : (Date) (x[20]));
                alarmRec.setIsAck(x[21] == null ? null : Integer.valueOf(x[21].toString()));
                alarmRec.setEventId(x[22] == null ? null : Long.valueOf(x[22].toString()));
                alarmRec.setCompanyId(companyId);
                alarmRecs.add(alarmRec);
            }


            return alarmRecs;
        }

    }


    @Override
    public List<AlarmRec> findAlarmEventInfoByStartTime(String startDate, String endDate, Integer companyId) {
        StringBuilder sql = new StringBuilder();
        sql.append("select UNIT_CODE,\n" +
                "       PRDTCELL_ID,\n" +
                "       DCS_CODE,\n" +
                "       EVENT_TYPE_ID,\n" +
                "       ALARM_POINT_ID,\n" +
                "       TAG,\n" +
                "       ALARM_FLAG_ID,\n" +
                "       ALARM_FLAG,\n" +
                "       ALARM_TIME,\n" +
                "       RESPONSE_TIME,\n" +
                "       PRIORITY,\n" +
                "       PRIORITY_CACHE,\n" +
                "       PREVIOUS_VALUE,\n" +
                "       NOW_VALUE,\n" +
                "       LIMIT_VALUE,\n" +
                "       IN_SHELVED,\n" +
                "       IN_SUPPRESSED,\n" +
                "       OPERATOR,\n" +
                "       PARAMETER,\n" +
                "       DES\n" +
                "FROM (SELECT LEAD(A.START_TIME, 1)\n" +
                "                  OVER (PARTITION BY UNIT_CODE,PRDTCELL_ID,TAG,ALARM_FLAG ORDER BY A.ALARM_TIME,A.START_TIME,A.EVENT_TYPE_ID) AS RESPONSE_TIME,\n" +
                "             UNIT_CODE,\n" +
                "             PRDTCELL_ID,\n" +
                "             DCS_CODE,\n" +
                "             EVENT_TYPE_ID,\n" +
                "             ALARM_POINT_ID,\n" +
                "             TAG,\n" +
                "             ALARM_FLAG_ID,\n" +
                "             ALARM_FLAG,\n" +
                "             ALARM_TIME,\n" +
                "             PRIORITY,\n" +
                "             PRIORITY_CACHE,\n" +
                "             PREVIOUS_VALUE,\n" +
                "             NOW_VALUE,\n" +
                "             LIMIT_VALUE,\n" +
                "             IN_SHELVED,\n" +
                "             IN_SUPPRESSED,\n" +
                "             OPERATOR,\n" +
                "             PARAMETER,\n" +
                "             DES\n" +
                "      FROM (SELECT UNIT_CODE,\n" +
                "                   a.PRDTCELL_ID,\n" +
                "                   DCS_CODE,\n" +
                "                   EVENT_TYPE_ID,\n" +
                "                   a.ALARM_POINT_ID,\n" +
                "                   START_TIME,\n" +
                "                   (CASE\n" +
                "                        WHEN A.ALARM_POINT_ID IS NOT NULL THEN (SELECT TPA.TAG\n" +
                "                                                                FROM T_PM_ALARMPOINT TPA\n" +
                "                                                                WHERE TPA.ALARM_POINT_ID = A.ALARM_POINT_ID)\n" +
                "                        ELSE A.TAG END)        TAG,\n" +
                "                   ALARM_FLAG_ID,\n" +
                "                   (CASE\n" +
                "                        WHEN A.ALARM_FLAG_ID IS NOT NULL THEN (SELECT TAAG.NAME\n" +
                "                                                               FROM T_AD_ALARMFLAG TAAG\n" +
                "                                                               WHERE TAAG.ALARM_FLAG_ID = A.ALARM_FLAG_ID)\n" +
                "                        ELSE A.ALARM_FLAG END) ALARM_FLAG,\n" +
                "                   ALARM_TIME,\n" +
                "                   PRIORITY,\n" +
                "                   PRIORITY_CACHE,\n" +
                "                   PREVIOUS_VALUE,\n" +
                "                   NOW_VALUE,\n" +
                "                   LIMIT_VALUE,\n" +
                "                   IN_SHELVED,\n" +
                "                   IN_SUPPRESSED,\n" +
                "                   OPERATOR,\n" +
                "                   PARAMETER,\n" +
                "                   a.DES\n" +
                "            FROM T_AD_ALARMEVENT A\n" +
                "                     left join t_pm_alarmpoint b on a.ALARM_POINT_ID = b.ALARM_POINT_ID\n" +
                "            WHERE A.EVENT_TYPE_ID IN (1001, 1002, 1003, 1004, 1005, 1006)\n" +
                "              AND A.START_TIME >= :startDate\n" +
                "              AND A.START_TIME <= :endDate\n" +
                "              and a.company_id = :companyId" +
                "              and ((a.ALARM_POINT_ID is not null and b.ALARM_POINT_ID is not null) or\n" +
                "                   (a.ALARM_POINT_ID is null))) A) A\n" +
                "WHERE EVENT_TYPE_ID = 1001\n" +
                "  AND NOT EXISTS\n" +
                "    (SELECT 1\n" +
                "     FROM t_ad_alarmrec T2\n" +
                "     WHERE A.UNIT_CODE = T2.UNIT_CODE\n" +
                "       AND A.PRDTCELL_ID = T2.PRDTCELL_ID\n" +
                "       AND A.TAG = T2.TAG\n" +
                "       AND A.ALARM_FLAG = T2.ALARM_FLAG\n" +
                "       AND A.ALARM_TIME = T2.ALARM_TIME\n" +
                "       and a.response_time is null)");
        Map<String, Object> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("companyId", companyId);
        Query query = this.getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, param);
        List<Object[]> resultList = query.getResultList();

        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        } else {
            ArrayList<AlarmRec> alarmRecs = new ArrayList<>();
            for (Object[] x : resultList) {
                AlarmRec alarmRec = new AlarmRec();
                alarmRec.setUnitCode(x[0] == null ? null : x[0].toString());
                alarmRec.setPrdtCellId(x[1] == null ? null : Long.parseLong(x[1].toString()));
                alarmRec.setDcsCode(x[2] == null ? null : x[2].toString());
                alarmRec.setEventTypeId(x[3] == null ? null : Long.valueOf(x[3].toString()));
                alarmRec.setAlarmPointId(x[4] == null ? null : Long.valueOf(x[4].toString()));
                alarmRec.setTag(x[5] == null ? null : x[5].toString());
                alarmRec.setAlarmFlagId(x[6] == null ? null : Long.parseLong(x[6].toString()));
                alarmRec.setAlarmTime(x[8] == null ? null : (Date) x[8]);
                alarmRec.setResponseTime(x[9] == null ? null : (Date) (x[9]));
                alarmRec.setPriority(x[10] == null ? null : Integer.valueOf(x[10].toString()));
                alarmRec.setPriorityCache(x[11] == null ? null : x[11].toString());
                alarmRec.setPreviousValue(x[12] == null ? null : x[12].toString());
                alarmRec.setNowValue(x[13] == null ? null : x[13].toString());
                alarmRec.setLimitValue(x[14] == null ? null : Double.valueOf(x[14].toString()));
                alarmRec.setInShelved(x[15] == null ? null : Integer.valueOf(x[15].toString()));
                alarmRec.setInSuppressed(x[16] == null ? null : Integer.valueOf(x[16].toString()));
                alarmRec.setOperator(x[17] == null ? null : x[17].toString());
                alarmRec.setParameter(x[18] == null ? null : x[18].toString());
                alarmRec.setDes(x[19] == null ? null : x[19].toString());
                alarmRecs.add(alarmRec);
            }


            return alarmRecs;
        }


    }

    @Override
    public List<AlarmEvent> selectDelAlarmEvent(List<AlarmPointDelConfig> alarmPointDelConfigs) {
        if (alarmPointDelConfigs.size() == 0) {
            return new ArrayList<AlarmEvent>();
        }

        StringBuilder hql = new StringBuilder("from AlarmEvent where 1=2");
        //查询剔除配置信息
        for (AlarmPointDelConfig a : alarmPointDelConfigs) {
            //过滤报警点
            hql.append(" or (( alarmPointId  in (").append(a.getAlarmPointId()).append(") ");
            //过滤报警标识,-9表示标识为空
            if (a.getAlarmFlagId().contains("-9")) {
                hql.append(" and (");
                hql.append("  alarmFlagId  in (").append(a.getAlarmFlagId()).append(") ");
                hql.append(" or alarmFlagId is null )");
            } else {
                hql.append(" and alarmFlagId  in (").append(a.getAlarmFlagId()).append(") ");
            }
            //过滤企业id
            hql.append(" and companyId = ").append(a.getCompanyId());
            //过滤时间
            hql.append(" and alarmTime  between '").append(org.apache.commons.lang.time.DateFormatUtils.format(a.getDelStartTime(), "yyyy-MM-dd HH:mm:ss")).append("' and '").append(org.apache.commons.lang.time.DateFormatUtils.format(a.getDelEndTime(), "yyyy-MM-dd HH:mm:ss")).append("' ))");
        }


        return getEntityManager().createQuery(hql.toString(), AlarmEvent.class).getResultList();
    }

    @Transactional
    @Override
    public void deleteBatch(List<Long> alarmEvents, Integer batchSize) {
        batchSize = batchSize == null ? 1000 : batchSize;

        List<List<Long>> lists = ListUtils.splitList(alarmEvents, batchSize);

        for (List<Long> list : lists) {

            String sql = "delete from AlarmEvent where eventId in (:eventId)";

            Query query = getEntityManager().createQuery(sql);

            query.setParameter("eventId", list);
            int i = query.executeUpdate();
            log.info("事件表删除--{}", i);

        }


//        for (int i = 0; i < alarmEvents.size(); i++) {
//            getEntityManager().remove(getEntityManager().merge(alarmEvents.get(i)));
//            if (i % batchSize == 0) {
//                getEntityManager().flush();
//                getEntityManager().clear();
//            }
//        }
//
//        getEntityManager().flush();
//        getEntityManager().clear();
    }

    /**
     * 获取报警次数统计页面统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCode  装置编码
     * @param priority  优先级
     * <AUTHOR> 2017-11-21
     */
    @Override
    public PaginationBean<AlarmNumStattDtlEntityVO> getAlarmNumStattDtl(Date startTime, Date endTime,
                                                                        String unitCode, Integer[] priority, Long[] alarmFlagIds, Pagination page) {
        try {
            StringBuilder hql = new StringBuilder(
                    " select new com.pcitc.opal.ad.dao.imp.AlarmNumStattDtlEntityVO(ae.alarmTime, pc.sname,ae.tag,ae.des,af.name,ae.nowValue) "
                            + " from AlarmEvent ae "
                            + " left join ae.alarmPoint ap  "
                            + " inner join ae.prdtCell pc  "
                            + " left join ae.alarmFlag af "
                            + " where ae.eventTypeId = :eventTypeId and ae.companyId=:companyId "
                            + " and ae.alarmTime between :startTime and :endTime "
                            + " and case when ae.alarmPointId is not null then ap.inUse  end =1 "
            );
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 过滤装置
            if (!StringUtils.isBlank(unitCode)) {
                hql.append(" and ae.unitCode =:unitCode ");
                paramList.put("unitCode", unitCode);
            }
            // 过滤优先级
            if (priority != null ) {
                List<Integer> prioritys = Arrays.asList(priority);
                if (!prioritys.contains(-1)) {
                    hql.append(" and (");
                    hql.append(" ae.priority in (:priority) ");
                    paramList.put("priority", prioritys);
                    if (prioritys.contains(9)) {
                        hql.append(" or ae.priority is null ");
                    }
                    hql.append(" )");
                }
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ae.alarmFlagId in (:alarmFlagIds) or ae.alarmFlagId is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ae.alarmFlagId in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            hql.append(" order by ae.alarmTime desc,ae.tag desc,af.name desc ");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());


            Long count = Long.valueOf(this.findCusCount(hql.toString(), paramList));
            BaseRepository<AlarmNumStattDtlEntityVO, Long> br = new BaseRepository();
            return br.findCusTomAll(this.getEntityManager(), page, count, hql.toString(), paramList, AlarmNumStattDtlEntityVO.class);

        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<AlarmEvent> getAlarmEventByCurrId(Long currentId) {
        String hql = "from AlarmEvent ae inner join ae.alarmPoint ap where ap.inUse = 1 and ae.eventTypeId in (1001,1002,1003,1005) ";

        HashMap<String, Object> param = new HashMap<>();

        if (currentId == null) {
            hql += " and ae.startTime >= :date";

            //当前时间减去一分钟
            Date date = new Date(System.currentTimeMillis() - 1000 * 60);
            param.put("date", date);

        } else {
            hql += " and ae.eventId > :currentId ";
            param.put("currentId", currentId);
        }

        //保证字段不为空
        hql += " and ae.alarmPointId is not null and ae.tag is not null and ae.prdtCellId is not null and ae.alarmFlagId in (1,2,3,4) order by ae.startTime ";

        Query query = getEntityManager().createQuery(hql);

        setParameterList(query, param);


        return query.getResultList();
    }

    @Override
    public HashMap<CommonEnum.AlarmPriorityEnum, String> getPriorityRatioInAp(String[] unitCodes, Long[] prdtCellIds) {

        HashMap<String, Object> param = new HashMap<>();

        String hql = "select ap.instrmtPriority,count(1) from AlarmPoint ap inner join PrdtCell prd on ap.prdtCellId = prd.prdtCellId where ap.companyId = :companyId and ap.instrmtPriority is not null and ap.inUse = 1 and prd.inUse = 1 ";

        param.put("companyId", new CommonProperty().getCompanyId());

        if (ArrayUtils.isNotEmpty(unitCodes)) {
            hql += " and prd.unitId in (:unitId) ";
            param.put("unitId", Arrays.asList(unitCodes));
        }

        if (ArrayUtils.isNotEmpty(prdtCellIds)) {
            hql += " and prd.prdtCellId in (:prdtCellId) ";
            param.put("prdtCellId", Arrays.asList(prdtCellIds));
        }

        hql += " group by ap.instrmtPriority ";
        Query query = getEntityManager().createQuery(hql);

        setParameterList(query, param);

        List<Object[]> resultList = query.getResultList();

        //key->优先级，value->比例
        HashMap<CommonEnum.AlarmPriorityEnum, String> returnMap = new HashMap<>();
        //初始化数据
        returnMap.put(CommonEnum.AlarmPriorityEnum.Importance, "0.00%");
        returnMap.put(CommonEnum.AlarmPriorityEnum.Emergency, "0.00%");
        returnMap.put(CommonEnum.AlarmPriorityEnum.Normal, "0.00%");

        if (CollectionUtils.isNotEmpty(resultList)) {
            //计算总数
            int sum = resultList.stream().mapToInt(x -> Integer.parseInt(x[1].toString())).sum();
            DecimalFormat decimalFormat = new DecimalFormat("0.00%");
            for (Object[] objects : resultList) {
                if (Objects.equals(objects[0], CommonEnum.AlarmPriorityEnum.Importance.getIndex())) {
                    returnMap.put(CommonEnum.AlarmPriorityEnum.Importance, decimalFormat.format(Float.parseFloat(objects[1].toString()) / sum));
                } else if (Objects.equals(objects[0], CommonEnum.AlarmPriorityEnum.Emergency.getIndex())) {
                    returnMap.put(CommonEnum.AlarmPriorityEnum.Emergency, decimalFormat.format(Float.parseFloat(objects[1].toString()) / sum));
                } else if (Objects.equals(objects[0], CommonEnum.AlarmPriorityEnum.Normal.getIndex())) {
                    returnMap.put(CommonEnum.AlarmPriorityEnum.Normal, decimalFormat.format(Float.parseFloat(objects[1].toString()) / sum));
                }
            }
        }
        return returnMap;
    }

    @Override
    public List<AlarmEvent> getAlarmEvent(Date start, Date end, String unitCode) {
        String hql = "from AlarmEvent where alarmTime between :start and :end and unitCode = :unitCode";

        TypedQuery<AlarmEvent> query = getEntityManager().createQuery(hql, AlarmEvent.class);
        query.setParameter("start", start);
        query.setParameter("end", end);
        query.setParameter("unitCode", unitCode);
        return query.getResultList();
    }

}

