package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointEntity;
import com.pcitc.opal.pm.bll.entity.DBUnitEntity;
import com.pcitc.opal.pm.bll.entity.MobileListEntity;
import com.pcitc.opal.pm.vo.AlarmPointTableVO;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.List;

/*
 * 报警点业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_AlarmPointService
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点业务逻辑层接口 
 */
@Service
public interface AlarmPointService {

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointEntity 报警点实体
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult addAlarmPoint(AlarmPointEntity alarmPointEntity) throws Exception;
	
	/**
	 * 删除报警点维护数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointIds 报警点维护主键Id集合
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult deleteAlarmPoint(Long[] alarmPointIds) throws Exception;
	
	/**
	 * 报警点更新数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointEntity 报警点实体
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult updateAlarmPoint(AlarmPointEntity alarmPointEntity) throws Exception;
	
	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointId 报警点ID
	 * @return AlarmPointEntity 报警点实体类
	 * @throws Exception 
	 */
	AlarmPointEntity getSingleAlarmPoint(Long alarmPointId) throws Exception;
	
	/**
	 * 获取分页数据
	 *
	 * <AUTHOR> 2017-10-11
	 * @param isExport 是否是导出
	 * @param unitCodes 装置编码
	 * @param prdtCellIds 生产单元id
	 * @param tag 位号
	 * @param typeId 报警点类型id
	 * @param inUse 是否启用
	 * @param instrmtPriority 仪表优先级
	 * @param page 翻页实现类
	 * @throws Exception 
	 * @return PaginationBean<AlarmPointEntity> 翻页对象
	 */
	PaginationBean<AlarmPointTableVO> getAlarmPoint(int isExport, String[] unitCodes, Long[] prdtCellIds, String tag, Long typeId, Integer inUse, Integer instrmtPriority, Pagination page) throws  Exception;
	List<AlarmPointTableVO> getAlarmPointExport(int isExport, String[] unitCodes, Long[] prdtCellIds, String tag, Long typeId,
													   Integer inUse, Integer instrmtPriority) throws Exception;
	/**
	 * 判断报警点在报警事件表中是否使用
	 *
	 * <AUTHOR> 2017-11-29
	 * @param alarmPointId 报警点ID
	 * @return CommonResult 消息结果类
	 */
	CommonResult getAlarmPointIsUseInAlarmEvent(Long alarmPointId);
	/**
	 *报警点维护数据导入
	 *
	 * <AUTHOR> 2017-11-16
	 * @param inputStream		Excel文件流
	 * @return String 返回字符串对象
	 */
	String importAlarmPoint(InputStream inputStream) throws Exception;
	/**
	 *停用报警点
	 *
	 * <AUTHOR> 2018-08-30
	 * @param alarmPointIds		报警点id集合
	 * @return String 返回字符串对象
	 */
	CommonResult stopAlarmPoint(Long[] alarmPointIds) throws Exception;

	/**
	 * 获取优先级列表
	 * @param isAll 是否显示全部
	 * @return
	 */
	List<DictionaryEntity> getInstrmtPriorityList(boolean isAll) throws Exception;

	/**
	 * 获取是否发送报警短信列表（1是，0否）
	 * @param isAll
	 * @return
	 */
    List<DictionaryEntity> getAlarmPriorityList(boolean isAll);

	/**
	 *  主页面查询
	 * @param unitCodes 装置id
	 * @param prdtCellIds 生产单元id
	 * @param tag  位号
	 * @param inSendMsg  是否发短信
	 * @param page  分页
	 * @return
	 * @throws Exception
	 */
	PaginationBean<AlarmPointEntity> getAlarmMsgConfig(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inSendMsg, Pagination page) throws Exception;

	/**
	 * 通过报警点IDs 获取信息列表
	 * @param alarmPointIds
	 * @return
	 * @throws Exception
	 */
	List<AlarmPointEntity> getAlarmMsgConfigByAlarmPointIds(Long[] alarmPointIds) throws Exception;

	/**
	 * 修改发送信息
	 * @param alarmPointIds
	 * @param inSendMsg
	 * @param mobilePhone
	 * @return
	 */
	CommonResult updateAlarmMsgConfig(Long[] alarmPointIds, Integer inSendMsg, String mobilePhone, Long[] mobileBookIds, Integer[] alarmFlagIds);

	List<DictionaryEntity> getAlarmFlagList();

    List<MobileListEntity> getMobileList(Long factoryId, Long workshopId, String unitId, String name, String phoneNum) throws Exception;

	List<DBUnitEntity> getUnitListByWorkshopId(Long workshopId, boolean isAll) throws Exception;

	CommonResult updateAlarmMsgConfigForInUse(Long[] alarmPointIds, Integer inSendMsg);

	List<AlarmPointEntity> getAlarmPonitListInfo(String[] unitIds,Long[] prdtCellIds,String tag,Integer inSendMsg) throws Exception;

	List<AlarmPointEntity> getAlarmPointList(Long[] alarmPointIds) throws Exception;

}
