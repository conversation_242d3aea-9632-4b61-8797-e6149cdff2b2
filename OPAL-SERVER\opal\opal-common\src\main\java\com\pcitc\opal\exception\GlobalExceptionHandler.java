package com.pcitc.opal.exception;

import com.pcitc.opal.common.CommonResult;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import pcitc.imp.common.ettool.baseresrep.ErrorInfo;
import pcitc.imp.common.ettool.utils.RestfulTool;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.sql.SQLException;

/**
 * 全局异常统一处理类
 * 模块编号：pcitc_opal_common_class_GlobalExceptionHandler
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/11
 * 修改编号：1
 * 描    述：全局异常统一处理类
 */
@ControllerAdvice
class GlobalExceptionHandler {
    @ExceptionHandler(value = Exception.class)
    public void defaultErrorHandler(HttpServletRequest req, HttpServletResponse response, Exception e) throws Exception {
        CommonResult commonResult = new CommonResult();
        //判断异常信息类型
        if (e.getClass().getTypeName().equals("org.springframework.dao.DataIntegrityViolationException")) {
            SQLException sqlException = ((org.hibernate.exception.ConstraintViolationException) e.getCause()).getSQLException();
            //判断异常源是否为空
            if (sqlException != null) {
                switch (sqlException.getErrorCode()) {
                    case 2292:
                    case 1451:
                        commonResult.setMessage("存在相关联的数据，删除失败！");
                        break;
                    default:
                        commonResult.setMessage(sqlException.getMessage());
                        break;
                }
            }
        }
        else if(e.getClass().getTypeName()=="java.lang.NullPointerException"){
            commonResult.setMessage("对象为null");
        }
        else {
            commonResult.setMessage(e.getMessage());
        }
        //输出异常的堆栈信息
        e.printStackTrace();
        // 构造错误返回值
        String collecionResult = RestfulTool.buildCollection(new ErrorInfo("", "00", commonResult.getMessage()), req.getRequestURI());

        //使用response返回
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value()); //设置状态码
        response.setContentType(MediaType.APPLICATION_JSON_VALUE); //设置ContentType
        response.setCharacterEncoding("UTF-8"); //避免乱码
        response.setHeader("Cache-Control", "no-cache, must-revalidate");
        response.getWriter().write(collecionResult);
    }



}
