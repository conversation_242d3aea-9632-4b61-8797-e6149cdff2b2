package com.pcitc.opal.common.annotation;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalTime;

@Aspect
@Component
@Slf4j
public class MeasureTimeAspect {
    @Around("@annotation(MeasureTime)")
    public Object measureExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        LocalTime start = LocalTime.now();
        Object result = joinPoint.proceed();
        LocalTime end = LocalTime.now();
        long millis = Duration.between(start, end).toMillis();
        log.info("方法{}执行时间了{}毫秒", joinPoint.getSignature().getName(), millis);
        return result;
    }
}
