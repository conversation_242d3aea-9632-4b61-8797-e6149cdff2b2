package com.pcitc.opal.common;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.io.Serializable;

/*
 * 工作流配置信息类
 * 模块编号： pcitc_opal_common_class_WorkFlowConfig
 * 作       者：xuelei.wang
 * 创建时间：2018/3/8
 * 修改编号：1
 * 描       述：工作流配置信息类
 */
@Configuration
@ConfigurationProperties(ignoreUnknownFields = false, prefix = "app")
@PropertySource(value ="classpath:workflow.properties")
public class WorkFlowConfig implements Serializable {


    private static final long serialVersionUID = -744671688211223497L;
    /**
     * 应用ID
     */
    private String appId;
    /**
     * 流程开始URL
     */
    private String startUrl;
    /**
     * 流程推进(执行)URL
     */
    private String completeUrl;
    /**
     * 获取token URL
     */
    private String tokenUrl;
    /**
     * 流程模板URL
     */
    private String flowCategoryUrl;
    /**
     * 分页获取待办流程列表URL
     */
    private String todoTaskUrl;
    /**
     * 流程退回URL
     */
    private String revertUrl;
    /**
     * 已办列表URL
     */
    private String doneTaskUrl;
    /**
     * 流程预览URL
     */
    private String previewUrl;
    /**
     * 流程SSO
     */
    private String ssoUrl;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getStartUrl() {
        return startUrl;
    }

    public void setStartUrl(String startUrl) {
        this.startUrl = startUrl;
    }

    public String getCompleteUrl() {
        return completeUrl;
    }

    public void setCompleteUrl(String completeUrl) {
        this.completeUrl = completeUrl;
    }

    public String getTokenUrl() {
        return tokenUrl;
    }

    public void setTokenUrl(String tokenUrl) {
        this.tokenUrl = tokenUrl;
    }

    public String getFlowCategoryUrl() {
        return flowCategoryUrl;
    }

    public void setFlowCategoryUrl(String flowCategoryUrl) {
        this.flowCategoryUrl = flowCategoryUrl;
    }


    public String getRevertUrl() {
        return revertUrl;
    }

    public void setRevertUrl(String revertUrl) {
        this.revertUrl = revertUrl;
    }

    public String getTodoTaskUrl() {
        return todoTaskUrl;
    }

    public void setTodoTaskUrl(String todoTaskUrl) {
        this.todoTaskUrl = todoTaskUrl;
    }

    public String getDoneTaskUrl() {
        return doneTaskUrl;
    }

    public void setDoneTaskUrl(String doneTaskUrl) {
        this.doneTaskUrl = doneTaskUrl;
    }

    public String getPreviewUrl() {
        return previewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }

    public String getSsoUrl() {
        return ssoUrl;
    }

    public void setSsoUrl(String ssoUrl) {
        this.ssoUrl = ssoUrl;
    }
}
