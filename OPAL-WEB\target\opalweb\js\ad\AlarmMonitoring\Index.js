var searchUrl = OPAL.API.adUrl + '/monitoringData/getMonitoringData';
$(function() {
    var page = {
        /**
         * 初始化
         */
        init: function() {
            this.bindUi();
            page.logic.initTable();
            page.logic.search();
        },
        bindUi: function() {
            //查询
            $('#search').click(function() {
                page.logic.search();
            })
        },
        data: {
            //查询参数
            param: {}
        },
        logic: {
            initTable: function() {
                $("#table").bootstrapTable({
                    cache: false,
                    pagination: false,
                    striped: true,
                    sidePagination: "server",
                    queryParamsType: "undefined",
                    // queryParams:  page.logic.queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "dcs",
                        field: 'dcsCodeName',
                        rowspan: 1,
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "装置",
                        field: 'unitCodeName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "生产单元",
                        field: 'prdtcellName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "OPC",
                        field: 'opcName',
                        rowspan: 1,
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "异常时间",
                        field: 'startTime',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "断开时长(分钟)",
                        field: 'duration',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }],

                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        $("#search").attr("disabled",false);
                        //设置鼠标浮动提示
                        var tds = $('#table').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                })
            },

            /**
             * 搜索
             */
            search: function() {
                $("#search").attr("disabled",true);
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl
                });
            }
        }
    };
    page.init();
    window.page = page;
});