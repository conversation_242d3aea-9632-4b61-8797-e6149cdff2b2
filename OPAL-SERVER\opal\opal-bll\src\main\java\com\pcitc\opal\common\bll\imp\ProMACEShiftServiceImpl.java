package com.pcitc.opal.common.bll.imp;

import com.pcitc.imp.common.restclient.RestfulAPIClient;
import com.pcitc.opal.common.CommonUtil;
import com.pcitc.opal.common.MicroServiceClient;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import com.pcitc.opal.common.bll.vo.ShiftAreaRelationVO;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.http.NameValuePair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.RestfulTool;

import java.util.*;
import java.util.stream.Collectors;

/*
 * 班组服务接口实现
 * 模块编号：pcitc_opal_bll_interface_ShiftServiceImpl
 * 作       者：xuelei.wang
 * 创建时间：2017-11-28
 * 修改编号：1
 * 描       述：班组服务接口实现
 */
@Service
@ConditionalOnProperty(name ="runtime_type",havingValue = "promace")
public class ProMACEShiftServiceImpl implements ShiftService {

    @Value("${promace.imp.shift.base.url}")
    private String shiftAddressBase;
    @Value("${fm_unit_type_code}")
    private String fmUnitTypeCode;
    /**
     * 获取班组列表
     *
     * @param unitCode  装置编码
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 班组信息列表
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    @Override
    public List<ShiftWorkTeamEntity> getShiftWorkTeamList(String unitCode, Date startTime, Date endTime) throws Exception {
        String startDateStr = DateFormatUtils.format(startTime, "yyyyMMddHHmmss");
        String endDateStr = DateFormatUtils.format(endTime, "yyyyMMddHHmmss");
        List<ShiftWorkTeamEntity> workTeamList = new ArrayList<>();
        String shiftAreaCode = getShiftAreaCodeByUnitCode(unitCode);
        try {
            String workTeamCollection = RestfulAPIClient.getRequest(shiftAddressBase + "/shiftareas/" + shiftAreaCode + "/shiftworkteams?$startTime=" + startDateStr + "&$endTime=" + endDateStr);
            List<ShiftWorkTeamEntity> result = RestfulTool.toResourceRepList(workTeamCollection, ShiftWorkTeamEntity.class);
            workTeamList.addAll(result);
        } catch (Exception ex) {
        }
        workTeamList=workTeamList.stream().filter(CommonUtil.distinctByKey(ShiftWorkTeamEntity::getWorkTeamCode)).collect(Collectors.toList());
        return workTeamList;
    }

    /**
     * 获取班组列表
     *
     * @param unitCodeList 装置编码集合
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 班组列表信息
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    @Override
    public List<ShiftWorkTeamEntity> getShiftWorkTeamList(List<String> unitCodeList, Date startTime, Date endTime) throws Exception {
        String startDateStr = DateFormatUtils.format(startTime, "yyyyMMddHHmmss");
        String endDateStr = DateFormatUtils.format(endTime, "yyyyMMddHHmmss");
        List<ShiftWorkTeamEntity> workTeamList = new ArrayList<>();
        for (String unitCode : unitCodeList) {
            String shiftAreaCode = getShiftAreaCodeByUnitCode(unitCode);
            try {
                String workTeamCollection = RestfulAPIClient.getRequest(shiftAddressBase + "/shiftareas/" + shiftAreaCode + "/shiftworkteams?$startTime=" + startDateStr + "&$endTime=" + endDateStr);
                List<ShiftWorkTeamEntity> result = RestfulTool.toResourceRepList(workTeamCollection, ShiftWorkTeamEntity.class);
                workTeamList.addAll(result);
            } catch (Exception ex) {
            }
        }
        workTeamList=workTeamList.stream().filter(CommonUtil.distinctByKey(ShiftWorkTeamEntity::getWorkTeamCode)).collect(Collectors.toList());
        return workTeamList;
    }

    /**
     * 根据班组名称集合及开始结束时间获取排班时间段列表
     *
     * @param unitCode       装置编码
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param workTeamIdList 班组Id集合
     * @return 排班时间段列表
     * @throws Exception
     * <AUTHOR> 2017-11-26
     */
    @Override
    public List<ShiftWorkTeamEntity> getShiftList(String unitCode, Date startTime, Date endTime, List<Long> workTeamIdList) throws Exception {
        if (unitCode == null) return new ArrayList();
        String startDateStr = DateFormatUtils.format(startTime, "yyyyMMddHHmmss");
        String endDateStr = DateFormatUtils.format(endTime, "yyyyMMddHHmmss");
        String shiftCode = getShiftAreaCodeByUnitCode(unitCode);
        //1.获取班组
        String workTeamCollection = RestfulAPIClient.getRequest(shiftAddressBase + "/shiftareas/" + shiftCode + "/shiftworkteams?$startTime=" + startDateStr + "&$endTime=" + endDateStr);
        List<ShiftWorkTeamEntity> workTeamList = new ArrayList<>();
        workTeamList = RestfulTool.toResourceRepList(workTeamCollection, ShiftWorkTeamEntity.class);
        //2.过滤指定名称的班组
        return workTeamList.stream().filter(item -> workTeamIdList.contains(item.getWorkTeamId())).collect(Collectors.toList());
    }

    /**
     * 获取指定班组的生产排班信息
     *
     * @param unitCode   装置ID
     * @param workTeamId 班组ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 生产排班信息集合
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    @Override
    public List<ShiftWorkTeamEntity> getShiftList(String unitCode, Long workTeamId, Date startTime, Date endTime) throws Exception {
        if (unitCode == null) return new ArrayList();
        String startDateStr = DateFormatUtils.format(startTime, "yyyyMMddHHmmss");
        String endDateStr = DateFormatUtils.format(endTime, "yyyyMMddHHmmss");
        String shiftCode = getShiftAreaCodeByUnitCode(unitCode);
        //1.获取班组
        String workTeamCollection = RestfulAPIClient.getRequest(shiftAddressBase + "/shiftareas/" + shiftCode + "/shiftworkteams?$startTime=" + startDateStr + "&$endTime=" + endDateStr);
        List<ShiftWorkTeamEntity> workTeamList = new ArrayList<>();
        try {
            workTeamList = RestfulTool.toResourceRepList(workTeamCollection, ShiftWorkTeamEntity.class);
        } catch (Exception ex) {
        }
        //2.过滤指定名称的班组
        return workTeamList.stream().filter(item -> item.getWorkTeamId().equals(workTeamId)).collect(Collectors.toList());
    }

    /**
     * 根据轮班域编码集合获取轮班域列表
     *
     * @param shiftAreaCodes 轮班域ID或编码集合
     * @return 轮班域集合
     * <AUTHOR> 2017-12-12
     */
    @Override
    public List<ShiftWorkTeamEntity> getShiftAreaList(List<String> shiftAreaCodes) throws Exception {
        List<ShiftWorkTeamEntity> workTeamList = new ArrayList<>();
        if (shiftAreaCodes == null || shiftAreaCodes.size() == 0) return workTeamList;
        try {
            String workTeamCollection = RestfulAPIClient.getRequest(shiftAddressBase + "/shiftareas");
            workTeamList = RestfulTool.toResourceRepList(workTeamCollection, ShiftWorkTeamEntity.class);
        } catch (Exception ex) {

        }
        workTeamList = workTeamList.stream().filter(item -> shiftAreaCodes.contains(item.getShiftCode())).collect(Collectors.toList());
        return workTeamList;
    }

    /**
     * 根据装置编码获取轮班域编码
     *
     * @param unitCode 装置编码
     * @return 轮班域编码
     * @throws Exception
     * <AUTHOR> 2018-9-6
     */
    public String getShiftAreaCodeByUnitCode(String unitCode) throws Exception {
        if (org.apache.commons.lang.StringUtils.isEmpty(unitCode)) return "";
        String url = shiftAddressBase + "/shiftAreaRelations?factoryCode=" + unitCode + "&factoryType=" + fmUnitTypeCode;
        //组装请求参数
        List<NameValuePair> paramList = new ArrayList<>();
        //发送请求
        String collection = MicroServiceClient.getRequest(url, paramList);
        List<ShiftAreaRelationVO> shiftAreaRelationVoList = RestfulTool.toResourceRepList(collection, ShiftAreaRelationVO.class);
        if (shiftAreaRelationVoList != null && shiftAreaRelationVoList.size() == 1) {
            return shiftAreaRelationVoList.stream().findFirst().orElse(new ShiftAreaRelationVO()).getShiftAreaCode();
        }
        return "";
    }
}
