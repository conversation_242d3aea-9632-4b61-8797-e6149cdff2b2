package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.pm.pojo.CraftAlarmRateConf;

import java.util.Date;
import java.util.List;

/*
 * CraftAlarmRateConf实体的Repository的JPA自定义接口
 * 模块编号：pcitc_opal_dal_interface_CraftAlarmRateConfRepositoryCustom
 * 作       者：shufei.sui
 * 创建时间：2019/12/11
 * 修改编号：1
 * 描       述：CraftAlarmRateConf实体的Repository的JPA自定义接口
 */
public interface CraftAlarmRateConfRepositoryCustom {


    List<CraftAlarmRateConf> getAllCraftAlarmRateConf();

    /**
     * 报警响应及时率 and 报警处置及时率
     *
     * @param unitId    装置id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param dateTypeEnum 查询小于30s或者小于30分钟
     * @return List<Object[]>
     */
    List<Object[]> getAlarmResponseRate(String[] unitId, Date startTime, Date endTime, CommonEnum.AlarmResponseAndAlarmHandlingDateTypeEnum dateTypeEnum);
}
