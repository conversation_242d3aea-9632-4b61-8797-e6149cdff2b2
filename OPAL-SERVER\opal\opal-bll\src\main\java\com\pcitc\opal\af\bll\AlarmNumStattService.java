package com.pcitc.opal.af.bll;

import com.pcitc.opal.aa.bll.entity.AlarmNumberAssessEntity;
import com.pcitc.opal.ad.dao.imp.AlarmNumStattDtlEntityVO;
import com.pcitc.opal.af.bll.entity.AlarmNumStattCommonEntity;
import com.pcitc.opal.af.bll.entity.AlarmNumStattDataCommonEntity;
import com.pcitc.opal.af.bll.entity.AlarmNumStattDataEntity;
import com.pcitc.opal.af.bll.entity.AlarmNumStattEntity;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public interface AlarmNumStattService {

    List<AlarmNumStattEntity> getAlarmNumStattStatisticData(String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Date startTime, Date endTime, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception;

    /**
     *  统计装置专业报警数
     * @param unitCodes 装置编码
     * @param monitorType 专业类型
     * @param alarmFlagIds 报警标识
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param endFlag
     * @param isElimination
     * @return
     * @throws Exception
     */
    List<AlarmNumStattEntity> getUnitMonitorAlarmNumStat(String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds,
                                                         Date startTime, Date endTime, String endFlag,
                                                         Integer isElimination) throws Exception;

    List<AlarmNumStattCommonEntity> getAlarmNumStattStatisticDataWorkshop(String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Date startTime, Date endTime, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception;

    List<AlarmNumStattCommonEntity> getAlarmNumStattStatisticDataWorkshopType(String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds, Date startTime, Date endTime, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception;

    List<AlarmNumStattCommonEntity> getAlarmNumStattStatisticDataUnit(String[] workshopCodes, String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Date startTime, Date endTime, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception;

    List<AlarmNumStattCommonEntity> getAlarmNumStattStatisticDataUnitType(String[] workshopCodes, String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds, Date startTime, Date endTime, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception;

    List<AlarmNumStattCommonEntity> getAlarmNumStattStatisticDataPrdtcell(String[] unitIds, String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Date startTime, Date endTime, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception;

    List<AlarmNumStattCommonEntity> getAlarmNumStattStatisticDataPrdtcellType(String[] unitIds, String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds, Date startTime, Date endTime, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception;

    /**
     * 工艺系统调用--报警次数统计接口
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    List<AlarmNumStattDataEntity> getAlarmNumStattStatisticData(String[] unitCodes, Integer[] priority, Long[] alarmFlagId, Date startTime, Date endTime) throws Exception;

    /**
     * 工艺系统调用--报警次数统计接口(车间)
     * @param unitCodes
     * @param priority
     * @param alarmFlagId
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    List<AlarmNumStattDataCommonEntity> getAlarmNumStattStatisticDataWorkshop(String[] unitCodes, Integer[] priority, Long[] alarmFlagId, Date startTime, Date endTime) throws Exception;

    /**
     * 工艺系统调用--报警次数统计接口(装置)
     * @param workshopCodes
     * @param unitCodes
     * @param priority
     * @param alarmFlagId
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    List<AlarmNumStattDataCommonEntity> getAlarmNumStattStatisticDataUnit(String[] workshopCodes, String[] unitCodes, Integer[] priority, Long[] alarmFlagId, Date startTime, Date endTime) throws Exception;

    /**
     * 工艺系统调用--报警次数统计接口(生产单元)
     * @param unitIds
     * @param unitCodes
     * @param priority
     * @param alarmFlagId
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    List<AlarmNumStattDataCommonEntity> getAlarmNumStattStatisticDataPrdtcell(String[] unitIds, String[] unitCodes, Integer[] priority, Long[] alarmFlagId, Date startTime, Date endTime) throws Exception;

    /**
     * 报警数量统计——报警数趋势图
     * @param startTime
     * @param endTime
     * @param unitIds
     * @param priority
     * @param priorityFlag
     * @return
     * @throws Exception
     */
    List<AlarmNumberAssessEntity> getAlarmNumberList(Date startTime, Date endTime, Long[] alarmFlagId, String[] unitIds, Integer[] priority, Boolean priorityFlag, Integer isElimination) throws Exception;

    /**
     * 报警数量统计——
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    PaginationBean<AlarmNumStattDtlEntityVO> getAlarmNumStattDtl(
            Date startTime, Date endTime, String unitCode, Integer[] priority, Long[] alarmFlagIds, Pagination page) throws Exception;


}
