var relevantAlarmAnalysisUrl = OPAL.API.afUrl + '/relevantTagConfig/getRelevantAlarmAnalysis';
var relevantTagUrl = OPAL.API.afUrl + '/relevantTagConfig/getRelevantTag';
var alarmPointIds = [];
var tagArray = [];
var relevantTag = [];
var alarmChart;
var operateChart;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            /**
             *绑定事件
             */
            this.bindUi();
            page.logic.initAlarmChart();
            page.logic.initOperateChart();
            page.logic.initTable();
            OPAL.util.extendDate();
            /**
             * 初始化日期时间选择控件组
             */
            OPAL.ui.initDateTimePeriodPicker({
                'dateTypeCtrId': 'timeGranularity'
            });
            
            OPAL.util.getQueryTime(function(data){
                queryHour=moment(data,"HH:mm:ss").get('hour');
                if(queryHour==undefined){
                    queryHour=0;
                }
            });
        },
        bindUi: function () {
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                alarmChart.resize();
                operateChart.resize();
            };
            /**
             * 查询
             */
            $('#btnSearch').click(function () {
                page.logic.search();
                
            })
            //表格自适应页面拉伸宽度
            $(window).resize(function () {
                $('#table').bootstrapTable('resetView');
            });
        },
        data: {
            param: {},
            subParam: {}
        },
        logic: {
            setData:function(data) {
                $("#mainTag").text(data.tag);
                alarmPointIds.push(data.alarmPointId);
                tagArray.push(data.tag);
                $.ajax({
                    url: relevantTagUrl + "?now=" + Math.random(),
                    type: "get",
                    async: true,
                    data: {"relevantTagConfigId":data.relevantTagConfigId},
                    dataType: "json",
                    success: function (data) {
                        var entity = $.ET.toObjectArr(data);
                        relevantTag = [];
                        for(x in entity) {
                            alarmPointIds.push(entity[x].alarmPointId);
                            relevantTag.push(entity[x].tag);
                        }
                        if(relevantTag.length >0){
                            $("#relevantTag").text(relevantTag.join(","));
                        }
                        data.tag= data.tag+"(主)";
                        relevantTag.unshift(data.tag);
                    },
                    error: function (XMLHttpRequest, textStatus) {}
                });
            },
            /***
             * 查询
             */
            search: function () {
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                page.data.param = OPAL.form.getData("searchForm");
                page.data.param.alarmPointIds = alarmPointIds;
                // page.data.param.alarmPointIds = [6187,6186]; //报警
                // page.data.param.alarmPointIds = [5151,5164]; //操作
                $("#btnSearch").attr('disabled', true);
                $.ajax({
                    url: relevantAlarmAnalysisUrl + "?now=" + Math.random(),
                    type: "get",
                    async: true,
                    data: page.data.param,
                    dataType: "json",
                    success: function (data) {
                        page.logic.initAlarmChart(data.processNumber);
                        page.logic.initOperateChart(data.operateNumber);
                        $("#table").bootstrapTable("load", data.table);
                    },
                    error: function (XMLHttpRequest, textStatus) {},
                    complete: function () {
                        $("#btnSearch").attr('disabled', false);
                    }
                });
               
            },
            initAlarmChart: function(data) {
                if (alarmChart && !alarmChart.isDisposed()) {
                    alarmChart.clear();
                    alarmChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    alarmChart = OPAL.ui.chart.initEmptyChart('alarmChart');
                    return;
                }
                alarmChart = echarts.init(document.getElementById('alarmChart'));
                var option = {
                    color: ['#6699CC', '#669999', '#CC99CC', '#66CCCC', '#9999CC'],
                    legend: {
                        data: [],
                        left: 50,
                        right: 100,
                        icon: 'bar',
                        itemWidth: 10, //设置icon大小
                        itemHeight: 10, //设置icon大小
                        // top:2
                    },
                    grid: {
                        left: '1%',
                        right: '3%',
                        // bottom: '1%',
                        top: '18%',
                        height: '200px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 0,
                            textStyle: {
                                color: '#333',
                            },
                            formatter: function (value, index) {
                                if (value.length > 10) {
                                    value = value.substring(11, 16);
                                }
                                if ($("#timeGranularity").val() == 'day' || $("#timeGranularity").val() == 'week') {
                                    value = value.substring(5, 11);
                                }
                                return value;
                            }
                        }
                    }],
                    tooltip: {
                        trigger: 'item',
                        formatter: function (param) {
                            return option.series[param['seriesIndex']]['tooltip'][param['dataIndex']];
                        }
                    },
                    yAxis: [{
                        type: 'value',
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 'auto',
                            textStyle: {
                                color: '#333',
                                fontSize: 8,
                                fontStyle: 'normal'
                            }
                        }
                    }],
                    series: [],
                    tableData: [],
                    dataZoom: [{
                        orient: "horizontal",
                        show: true,
                        start: 0,
                        end: 100,
                        height: 20,
                        bottom: 2,
                    }, {
                        type: 'slider',
                        yAxisIndex: 0,
                        filterMode: 'none',
                        width: 20,


                    }]
                };
                // option.color = data[0]['color'];
                // option.legend.data = relevantTag;
                for (var i = 0; i < data.length; i++) {
                    option.legend.data.push({
                        name: data[i]['tag'],
                        icon: 'bar',
                    });
                    if (option.xAxis[0].data.length == 0) {
                        option.xAxis[0].data = data[i]['xaxis'];
                    }
                    var customData = new Array();
                    for (var j = 0; j < data[i]['processCounts'].length; j++) {
                        customData.push({
                            value: data[i]['processCounts'][j] == "0" ? undefined : data[i]['processCounts'][j],
                            itemStyle: {
                                normal: {
                                    //color: data[i]['color']
                                }
                            }
                        });
                    }
                    option.series.push({
                        name: data[i]['tag'],
                        type: 'bar',
                        barMaxWidth: '30',
                        data: customData,
                        tooltip: data[i]['processTips']
                    });
                }
                alarmChart.setOption(option);
            },
            initOperateChart: function(data){
                if (operateChart && !operateChart.isDisposed()) {
                    operateChart.clear();
                    operateChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    operateChart = OPAL.ui.chart.initEmptyChart('operateChart');
                    return;
                }
                operateChart = echarts.init(document.getElementById('operateChart'));
                var option = {
                    color: ['#6699CC', '#669999', '#CC99CC', '#66CCCC', '#9999CC'],
                    legend: {
                        data: [],
                        left: 50,
                        right: 100,
                        icon: 'bar',
                        itemWidth: 10, //设置icon大小
                        itemHeight: 10, //设置icon大小
                        // top:2
                    },
                    grid: {
                        left: '1%',
                        right: '3%',
                        // bottom: '1%',
                        top: '18%',
                        height: '200px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 0,
                            textStyle: {
                                color: '#333',
                            },
                            formatter: function (value, index) {
                                if (value.length > 10) {
                                    value = value.substring(11, 16);
                                }
                                if ($("#timeGranularity").val() == 'day' || $("#timeGranularity").val() == 'week') {
                                    value = value.substring(5, 11);
                                }
                                return value;
                            }
                        }
                    }],
                    tooltip: {
                        trigger: 'item',
                        formatter: function (param) {
                            return option.series[param['seriesIndex']]['tooltip'][param['dataIndex']];
                        }
                    },
                    yAxis: [{
                        type: 'value',
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 'auto',
                            textStyle: {
                                color: '#333',
                                fontSize: 8,
                                fontStyle: 'normal'
                            }
                        }
                    }],
                    series: [],
                    tableData: [],
                    dataZoom: [{
                        orient: "horizontal",
                        show: true,
                        start: 0,
                        end: 100,
                        height: 20,
                        bottom: 2,
                    }, {
                        type: 'slider',
                        yAxisIndex: 0,
                        filterMode: 'none',
                        width: 20,


                    }]
                };
                // option.color = data[0]['color'];
                // option.legend.data = relevantTag;
                for (var i = 0; i < data.length; i++) {
                    option.legend.data.push({
                        name: data[i]['tag'],
                        icon: 'bar',
                    });
                    if (option.xAxis[0].data.length == 0) {
                        option.xAxis[0].data = data[i]['xaxis'];
                    }
                    var customData = new Array();
                    for (var j = 0; j < data[i]['processCounts'].length; j++) {
                        customData.push({
                            value: data[i]['processCounts'][j] == "0" ? undefined : data[i]['processCounts'][j],
                            itemStyle: {
                                normal: {
                                    //color: data[i]['color']
                                }
                            }
                        });
                    }
                    option.series.push({
                        name: data[i]['tag'],
                        type: 'bar',
                        barMaxWidth: '30',
                        data: customData,
                        tooltip: data[i]['processTips']
                    });
                }
                operateChart.setOption(option);
            },
            initTable:function(){
                $("#table").bootstrapTable({
                    cache:false,
                    columns: [{
                        title: "装置",
                        field: 'unitSname',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "生产单元",
                        field: 'prdtCellSname',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "报警数",
                        field: 'processCount',
                        rowspan: 1,
                        align: 'center',
                    }, {
                        title: "操作数",
                        field: 'operateCount',
                        rowspan: 1,
                        align: 'center',
                    }],
                    formatNoMatches: function() {
                        return "";
                    },
                    formatLoadingMessage: function() {
                        return "";
                    },
                    sidePagination: "client",
                    cache: false,
                    pagination: true, //是否分页
                    pageSize: 10,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [10,20, 50, 100],
                    striped: true,
                });
            }
            
        }
    };
    page.init();
    window.page = page;
});