package com.pcitc.opal.af.bll.entity;

/*
 * 因果报警分析-二级列表报警详情数据实体
 * 模块编号：pcitc_opal_bll_class_CausalAlarmAnalysisTableEntity
 * 作       者：kun.zhao
 * 创建时间：2017/11/16
 * 修改编号：1
 * 描       述：因果报警分析-二级列表报警详情数据实体
 */
public class CausalAlarmAnalysisTableEntity {
	/**
     * 生产单元名称
     */
    private String prdtCellName;

    /**
     * 位号
     */
    private String tagName;

    /**
     * 报警标识名称
     */
    private String alarmFlagName;

    /**
     * 报警次数
     */
    private Long alarmTimes;

    /**
     * 可预测性
     */
    private Object forecast;

    /**
     * 重要的
     */
    private Object important;

	/**
	 * 位置描述
	 */
	private String location;

	public String getPrdtCellName() {
		return prdtCellName;
	}

	public void setPrdtCellName(String prdtCellName) {
		this.prdtCellName = prdtCellName;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public String getAlarmFlagName() {
		return alarmFlagName;
	}

	public void setAlarmFlagName(String alarmFlagName) {
		this.alarmFlagName = alarmFlagName;
	}

	public Long getAlarmTimes() {
		return alarmTimes;
	}

	public void setAlarmTimes(Long alarmTimes) {
		this.alarmTimes = alarmTimes;
	}

	public Object getForecast() {
		return forecast;
	}

	public void setForecast(Object forecast) {
		this.forecast = forecast;
	}

	public Object getImportant() {
		return important;
	}

	public void setImportant(Object important) {
		this.important = important;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}
}
