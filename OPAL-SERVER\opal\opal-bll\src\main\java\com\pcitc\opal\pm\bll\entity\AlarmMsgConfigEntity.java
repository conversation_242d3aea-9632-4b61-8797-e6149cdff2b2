package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

public class AlarmMsgConfigEntity extends BasicEntity {
    /**
     * 报警点短信配置ID
     */

    private Long alarmMsgConfId;
    /**
     * 报警点ID
     */

    private Long alarmPointId;
    /**
     * 报警点标识ID
     */

    private Long alarmFlagId;
    /**
     * 电话本ID
     */

    private String mobileListId;
    /**
     * 时间间隔
     */

    private Long timeInterval;

    /**
     * 装置
     */
    private String unitName;
    /**
     * 生产单元
     */
    private String prdtcellName;
    /**
     * 位号
     */
    private String tag;
    /**
     * 是否发送报警短信
     */
    private String inSendMsg;
    /**
     * 报警标识
     */
    private String alarmFlagName;
    /**
     * 电话本
     */
    private String mobileListStr;

    private String mobileBook;




    public Long getAlarmMsgConfId() {
        return alarmMsgConfId;
    }

    public void setAlarmMsgConfId(Long alarmMsgConfId) {
        this.alarmMsgConfId = alarmMsgConfId;
    }

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public String getMobileListId() {
        return mobileListId;
    }

    public void setMobileListId(String mobileListId) {
        this.mobileListId = mobileListId;
    }

    public Long getTimeInterval() {
        return timeInterval;
    }

    public void setTimeInterval(Long timeInterval) {
        this.timeInterval = timeInterval;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPrdtcellName() {
        return prdtcellName;
    }

    public void setPrdtcellName(String prdtcellName) {
        this.prdtcellName = prdtcellName;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getInSendMsg() {
        return inSendMsg;
    }

    public void setInSendMsg(String inSendMsg) {
        this.inSendMsg = inSendMsg;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

    public String getMobileListStr() {
        return mobileListStr;
    }

    public void setMobileListStr(String mobileListStr) {
        this.mobileListStr = mobileListStr;
    }

    public String getMobileBook() {
        return mobileBook;
    }

    public void setMobileBook(String mobileBook) {
        this.mobileBook = mobileBook;
    }
}
