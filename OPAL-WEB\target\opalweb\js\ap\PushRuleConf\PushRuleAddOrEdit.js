var addUrl = OPAL.API.apUrl + '/PushRuleConf/addAlarmPushRule';
var searchUrl = OPAL.API.apUrl + '/systRunParaConf';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var GroupListUrl = OPAL.API.apUrl + "/Group/getAllGroup"
var getSingleUrl = OPAL.API.apUrl + "/PushRuleAddOrEdit/getRuleDetailList"
var delUrl = OPAL.API.apUrl + '/PushRuleAddOrEdit/delete';
window.pageLoadMode = PageLoadMode.None;
// 是否下拉框
var inUseList = [
    { value: 1, text: '是' },
    { value: 0, text: '否' }
];
//报警标识下拉框
var alarmFlagList = [
    { value: 1, text: '高高报PVHH' },
    { value: 2, text: '高报PVHI' },
    { value: 3, text: '低报PVLO' },
    { value: 4, text: '低低报PVLL' }
];
//超时推送时间下拉框
var startPushPeriodList = [
    { value: 0, text: 0 },
    { value: 5, text: 5 },
    { value: 10, text: 10 },
    { value: 20, text: 20 },
    { value: 30, text: 30 },
    { value: 60, text: 60 },
    { value: 120, text: 120 }
];
//周期推送时间下拉框
var cyclePeriodList = [
    { value: 5, text: 5 },
    { value: 10, text: 10 },
    { value: 20, text: 20 },
    { value: 30, text: 30 },
    { value: 60, text: 60 },
    { value: 120, text: 120 }
];
// 群组
var groupList = [];
var BusinessType = ''; //业务类型
var tableArr = [];
var groupStr = ''; //群组下拉框拼接
var startPushPeriodStr = ''; //推送间隔时间
var cyclePeriodStr = ''; //周期间隔时间
var inUseStr = ''; //报警结束推送标志、周期推送标志
var alarmFlagStr = ''; //报警标识拼接select
var alarmPushRuleId = ''; //主键id
// var tableArr = [
//     {
//         "alarmPushRuleId": 12,
//         "groupId": "2",
//         "alarmFlag": "",
//         "startPushPeriod": "10",
//         "cycleFlag": "1",
//         "cyclePeriod": "5",
//         "alarmEndPushFlag": "0"
//     },
//     {
//         "alarmPushRuleId": 13,
//         "groupId": "1",
//         "alarmFlag": "",
//         "startPushPeriod": "30",
//         "cycleFlag": "0",
//         "cyclePeriod": null,
//         "alarmEndPushFlag": "1"
//     },
//     {
//         "alarmPushRuleId": 14,
//         "groupId": "1",
//         "alarmFlag": "",
//         "startPushPeriod": "60",
//         "cycleFlag": "1",
//         "cyclePeriod": "30",
//         "alarmEndPushFlag": "1"
//     }
// ];
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        /**
         * 初始化
         */
        init: function () {
            //绑定事件
            this.bindUI();
            //初始化下拉框
            page.logic.selectContent();
            //初始化表格
            page.logic.initTable();
        },
        /**
         * 绑定事件
         */
        bindUI: function () {
            // 新增
            $('#saveAddModal').click(function () {
                page.logic.save();
            })
            //添加一行
            $('#addRule').click(function () {
                $('#table tbody').find('.no-records-found').remove();
                let obj = {
                    'groupId': '',
                    'alarmFlag': '',
                    'startPushPeriod': 0,
                    'cycleFlag': 1,
                    'cyclePeriod': 5,
                    'alarmEndPushFlag': 1
                }
                tableArr.push(obj);
                let idx = tableArr.length;
                let delTd = '<td style="text-align: center;"><a name="TableDelete" href="javascript:window.page.logic.delSingle(' + idx + ')">删除</a> </td>';
                let groupTd = '<td style="text-align: left;"><select class="drop-down-selects form-control groupId">' + groupStr + '</select></td>';
                let startPushPeriodTd = '<td style="text-align: left;" title="0510203060120"><select class="drop-down-selects form-control startPushPeriod">' + startPushPeriodStr + '</select></td>';
                let cycleFlagTd = '<td style="text-align: left;"><select class="drop-down-selects form-control cycleFlag">' + inUseStr + '</select></td>';
                let cyclePeriodTd = '<td style="text-align: left;"><select class="drop-down-selects form-control cyclePeriod">' + cyclePeriodStr + '</select></td>';
                let alarmEndPushFlagTd = '<td style="text-align: left;"><select class="drop-down-selects form-control alarmEndPushFlag">' + inUseStr + '</select></td>';
                let alarmFlagTd = '<td style="text-align: left;" title="0510203060120"><select class="drop-down-selects form-control alarmFlag">' + alarmFlagStr + '</select></td>';
                if (BusinessType == 1) {
                    $('#table tbody').append('<tr>' + delTd + groupTd + startPushPeriodTd + cycleFlagTd + cyclePeriodTd + alarmEndPushFlagTd + '</tr>')
                } else if (BusinessType == 2) {
                    $('#table tbody').append('<tr>' + delTd + alarmFlagTd + groupTd + alarmEndPushFlagTd + '</tr>');
                }
            })
            $('#table tbody a').live('click', function () {
                var that = $(this);
                if (pageMode == PageModelEnum.View) {
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    if (that.attr('data-val') != '' && that.attr('data-val') != undefined) {
                        $.ajax({
                            url: delUrl,
                            async: false,
                            data: JSON.stringify([that.attr('data-val')]),
                            processData: false,
                            contentType: "application/json;charset=utf-8",
                            dataType: "text",
                            type: 'DELETE', //PUT DELETE POST
                            success: function (result) {
                                if (result.indexOf('collection') < 0) {
                                    layer.msg(result, {
                                        time: 1000
                                    }, function () {
                                        that.parent().parent().remove();
                                        tableArr.pop();
                                    });
                                } else {
                                    result = JSON.parse(result)
                                    layer.msg(result.collection.error.message)
                                }
                            },
                            error: function (result) {
                                var errorResult = $.parseJSON(result.responseText);
                                layer.msg(errorResult.collection.error.message);
                            }
                        })
                    } else {
                        layer.msg("删除成功！", {
                            time: 1000
                        }, function () {
                            that.parent().parent().remove();
                            tableArr.pop();
                        });
                    }
                }, function (index) {
                    layer.close(index)
                });
            })
            $('#table tbody .cycleFlag').live('change', function () {
                let val = $(this).val();
                if (val == '0') {
                    $(this).parent().siblings('td').find('.cyclePeriod').attr('disabled', true);
                    $(this).parent().siblings('td').find('.cyclePeriod').val('');
                } else {
                    $(this).parent().siblings('td').find('.cyclePeriod').removeAttr('disabled');
                }
            })
            $('.closeBtn').click(function () {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer(false);
            })
        },
        data: {
            // 设置查询参数
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            // 获取群组下拉框数据
            initGroup: async function () {
                $.ajax({
                    url: GroupListUrl + "?now=" + Math.random(),
                    type: "get",
                    async: false,
                    dataType: "json",
                    success: function (data) {
                        groupList = $.ET.toObjectArr(data);
                        let option = '';
                        for (let i = 0; i < groupList.length; i++) {
                            option += '<option value="' + groupList[i].groupId + '">' + groupList[i].name + '</option>'
                        }
                        groupStr = option;
                    },
                    error: function (result) {
                    }
                });
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function (value, row, index) {
                var rowData = arguments[1];
                return [
                    '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + index + '\')" >删除</a> '
                ]
            },
            /**
             * 初始化表格
             */
            initTable: function () {
                $("#table").bootstrapTable({
                    cache: false,
                    pagination: false,
                    striped: true,
                    sidePagination: "server",
                    queryParamsType: "undefined",
                    queryParams: page.logic.queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    clickToSelect: true,
                    columns: [
                        {
                            title: "操作",
                            field: 'event_cancel',
                            rowspan: 1,
                            align: 'center',
                            width: '60px',
                            formatter: page.logic.onActionRenderer
                        }, {
                            field: 'alarmFlag',
                            title: '报警标识',
                            align: 'left',
                            width: '100px',
                        }, {
                            field: 'groupId',
                            title: '群组',
                            align: 'left',
                            width: '100px',
                        }, {
                            field: 'startPushPeriod',
                            title: '超时推送时间',
                            align: 'left',
                            width: '100px',
                        }, {
                            field: 'cycleFlag',
                            title: '是否周期推送',
                            align: 'left',
                            width: '100px',
                        }, {
                            field: 'cyclePeriod',
                            title: '周期推送时间',
                            align: 'left',
                            width: '100px',
                        }, {
                            field: 'alarmEndPushFlag',
                            title: '报警结束推送',
                            align: 'left',
                            width: '100px',
                        }],

                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#table').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                })

            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                page.logic.initGroup().then(x => {
                    pageMode = data.pageMode;
                    BusinessType = data.BusinessType;
                    alarmPushRuleId = data.alarmPushRuleId;
                    // 隐藏列
                    if (BusinessType == 1) {
                        $('#table').bootstrapTable('hideColumn', 'alarmFlag');
                    } else {
                        $('#table').bootstrapTable('hideColumn', 'cyclePeriod');
                        $('#table').bootstrapTable('hideColumn', 'startPushPeriod');
                        $('#table').bootstrapTable('hideColumn', 'cycleFlag');
                    }
                    //新增
                    if (pageMode == PageModelEnum.NewAdd) {
                        return;
                    } else {
                        $('#name').attr('disabled',true);
                        $('#name').val(data.name);
                        $('#alarmPushRuleId').val(data.alarmPushRuleId);
                    }
                    $.ajax({
                        url: getSingleUrl + "?now=" + Math.random(),
                        type: "get",
                        async: true,
                        data: {
                            alarmPushRuleId: data.alarmPushRuleId
                        },
                        dataType: "json",
                        success: function (data) {
                            var entity = $.ET.toObjectArr(data);
                            tableArr = entity;
                            if (tableArr.length > 0) {
                                $('#table tbody').find('.no-records-found').remove();
                                let delTd = '';
                                tableArr.forEach((item, index) => {
                                    if (pageMode == PageModelEnum.View) {
                                        delTd = '<td style="text-align: center; width: 60px;"><a name="TableDelete" style="color: #999;" data-val="' + item.apRuleDetailId + '">删除</a> </td>';
                                    } else {
                                        delTd = '<td style="text-align: center; width: 60px;"><a name="TableDelete" data-val="' + item.apRuleDetailId + '">删除</a> </td>';
                                    }
                                    let groupTd = '<td style="text-align: left; width: 100px;"><select class="drop-down-selects form-control groupId" data-val="' + item.groupId + '">' + groupStr + '</select></td>';
                                    let startPushPeriodTd = '<td style="text-align: left; width: 100px;"><select class="drop-down-selects form-control startPushPeriod" data-val="' + item.startPushPeriod + '">' + startPushPeriodStr + '</select></td>';
                                    let cycleFlagTd = '<td style="text-align: left; width: 100px; "><select class="drop-down-selects form-control cycleFlag" data-val="' + item.cycleFlag + '">' + inUseStr + '</select></td>';
                                    let cyclePeriodTd = '<td style="text-align: left; width: 100px; "><select class="drop-down-selects form-control cyclePeriod" data-val="' + item.cyclePeriod + '">' + cyclePeriodStr + '</select></td>';
                                    let alarmEndPushFlagTd = '<td style="text-align: left; width: 100px;"><select class="drop-down-selects form-control alarmEndPushFlag" data-val="' + item.alarmEndPushFlag + '">' + inUseStr + '</select></td>';
                                    let alarmFlagTd = '<td style="text-align: left; width: 100px;" title="0510203060120"><select class="drop-down-selects form-control alarmFlag" data-val="' + item.alarmFlagId + '">' + alarmFlagStr + '</select></td>';
                                    if (BusinessType == 1) {
                                        $('#table tbody').append('<tr class="tbody-tr">' + delTd + groupTd + startPushPeriodTd + cycleFlagTd + cyclePeriodTd + alarmEndPushFlagTd + '</tr>')
                                    } else {
                                        $('#table tbody').append('<tr class="tbody-tr">' + delTd + alarmFlagTd + groupTd + alarmEndPushFlagTd + '</tr>')
                                    }
                                })
                                //回显表格下拉框
                                page.logic.setTableSelectValue();
                            }
                            //详情
                            if (pageMode == PageModelEnum.View) {
                                $("select").attr('disabled', 'disabled');
                                $("#addRule").hide();
                                $("#saveAddModal").hide();
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    });
                })
            },
            //渲染select
            selectContent: function () {
                //报警标识
                alarmFlagList.forEach((item, index) => {
                    alarmFlagStr += '<option value="' + item.value + '">' + item.text + '</option>'
                })
                //推送间隔时间
                startPushPeriodList.forEach((item, index) => {
                    startPushPeriodStr += '<option value="' + item.value + '">' + item.text + '</option>'
                })
                //周期间隔时间
                cyclePeriodList.forEach((item, index) => {
                    cyclePeriodStr += '<option value="' + item.value + '">' + item.text + '</option>'
                })
                //报警结束推送标志、周期推送标志
                inUseList.forEach((item, index) => {
                    inUseStr += '<option value="' + item.value + '">' + item.text + '</option>'
                })
            },
            // 回显表格下拉框数据
            setTableSelectValue: function () {
                //填充 群组
                var groupIdArr = $('.groupId');
                $.each(groupIdArr, function (i, el) {
                    $(el).val($(el).attr('data-val'));
                })
                // 开始推送间隔时间
                var startPushPeriodArr = $('.startPushPeriod');
                $.each(startPushPeriodArr, function (i, el) {
                    $(el).val($(el).attr('data-val'));
                })
                // 是否周期推送标志
                var cycleFlagArr = $('.cycleFlag');
                $.each(cycleFlagArr, function (i, el) {
                    $(el).val($(el).attr('data-val'));
                    if ($(el).attr('data-val') == '0') {
                        $(el).parent().siblings().children('.cyclePeriod').attr('disabled', true);
                    }
                })
                // 周期间隔时间
                var cyclePeriodArr = $('.cyclePeriod');
                $.each(cyclePeriodArr, function (i, el) {
                    $(el).val($(el).attr('data-val'));
                })
                // 报警结束推送标志
                var alarmEndPushFlagArr = $('.alarmEndPushFlag');
                $.each(alarmEndPushFlagArr, function (i, el) {
                    $(el).val($(el).attr('data-val'));
                })
                // 报警标识
                var alarmFlagIdArr = $('.alarmFlag');
                $.each(alarmFlagIdArr, function (i, el) {
                    $(el).val($(el).attr('data-val'));
                })
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            //表单验证
            formValidate: function () {
                OPAL.form.formValidate('searchForm', {
                    rules: {
                        name: {
                            required: true,
                            rangelength: [0, 100]
                        }
                    }
                })
            },
            //保存
            save: function () {
                page.logic.formValidate();
                if (!$('#searchForm').valid()) {
                    return;
                }
                if (tableArr.length != 0) {
                    //填充 群组
                    var groupIdArr = $('.groupId');
                    $.each(groupIdArr, function (i, el) {
                        tableArr[i].groupId = $(el).val();
                    })
                    // 开始推送间隔时间
                    var startPushPeriodArr = $('.startPushPeriod');
                    $.each(startPushPeriodArr, function (i, el) {
                        tableArr[i].startPushPeriod = $(el).val();
                    })
                    // 是否周期推送标志
                    var cycleFlagArr = $('.cycleFlag');
                    $.each(cycleFlagArr, function (i, el) {
                        tableArr[i].cycleFlag = $(el).val();
                    })
                    // 周期间隔时间
                    var cyclePeriodArr = $('.cyclePeriod');
                    $.each(cyclePeriodArr, function (i, el) {
                        tableArr[i].cyclePeriod = $(el).val();
                    })
                    // 报警结束推送标志
                    var alarmEndPushFlagArr = $('.alarmEndPushFlag');
                    $.each(alarmEndPushFlagArr, function (i, el) {
                        tableArr[i].alarmEndPushFlag = $(el).val();
                    })
                    // 报警标识
                    var alarmFlagIdArr = $('.alarmFlag');
                    let verify = false;
                    $.each(alarmFlagIdArr, function (i, el) {
                        tableArr[i].alarmFlagId = $(el).val();
                        if ($(el).val() == '' || $(el).val() == null) {
                            layer.msg('请选择报警标识！');
                            verify = true;
                            return false;
                        }
                    })
                    if (verify) {
                        return;
                    }
                    var arr = new Array();
                    $.each(tableArr, function (i, el) {
                        arr.push(el);
                    })
                    var data = $.ET.toCollectionJson(arr);
                    var obj = {
                        name: $('#name').val(),
                        pushType: BusinessType,
                        alarmPushRuleId: alarmPushRuleId,
                        'collectionStr': JSON.stringify(data)
                    };
                    $.ajax({
                        url: addUrl,
                        type: 'post',
                        async: true,
                        data: JSON.stringify(obj),
                        dataType: "text",
                        processData: false,
                        contentType: "application/json;charset=utf-8",
                        success: function (result, XMLHttpRequest) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result);
                            } else {
                                layer.msg(result.collection.error.message)
                            }
                            page.logic.closeLayer(true);
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    });
                } else {
                    layer.msg('没有数据，不能保存！')
                }

            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                window.parent.pageLoadMode = isRefresh;
                parent.layer.close(index);
            },
        }
    }
    page.init();
    window.page = page;
})
