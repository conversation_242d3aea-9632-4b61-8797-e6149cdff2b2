package com.pcitc.opal.common.bll;

import com.pcitc.opal.aa.bll.entity.AlarmNumberAssessEntity;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.af.bll.entity.CausalAlarmAnalysisEntity;
import com.pcitc.opal.af.bll.entity.CausalAlarmAnalysisTableEntity;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.entity.*;
import com.pcitc.opal.common.bll.entity.FactoryEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.common.bll.entity.WorkshopEntity;
import com.pcitc.opal.pm.bll.entity.*;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/*
 * 基础数据接口
 * 模块编号：pcitc_opal_bll_interface_BasicDataService
 * 作       者：dongsheng.zhao
 * 创建时间：2017/09/17
 * 修改编号：1
 * 描       述：基础数据接口 
 */
@Service
public interface BasicDataService {

    /**
     * 获取装置树形结构
     *
     * @param enablePrivilege 是否启用权限过滤
     * @return 树形装置集合
     * <AUTHOR> 2017-09-25
     */
    List<OrgEntity> getAllUnitTree(boolean enablePrivilege) throws Exception;

    /**
     * 获取生产装置列表
     *
     * @param enablePrivilege 是否启用权限过滤
     * @return 生产装置列表
     * <AUTHOR> 2017-09-26
     */
    List<UnitEntity> getUnitList(boolean enablePrivilege) throws Exception;

    /**
     * 根据装置编码集合获取装置列表
     *
     * @param unitCodes         装置编码集合
     * @param enablePrivilege 是否启用权限过滤
     * @return 装置列表
     * <AUTHOR> 2017-09-26
     */
    List<UnitEntity> getUnitListByIds(String[] unitCodes, boolean enablePrivilege) throws Exception;

    /**
     * 根据车间编码集合获取该车间下所有的已启用的装置列表
     *
     * @param workshopCodes  车间编码集合
     * @param enablePrivilege 是否启用权限过滤
     * @return 装置集合
     * @throws Exception
     * <AUTHOR> 2017-11-7
     */
    List<UnitEntity> getUnitListByWorkshopIds(String[] workshopCodes, boolean enablePrivilege) throws Exception;

    /**
     * 根据车间编码集合获取车间列表
     *
     * @param workshopCodes 车间编码集合
     * @return 车间集合
     * @throws Exception
     * <AUTHOR> 2017-11-7
     */
     List<WorkshopEntity> getWorkshopListByWorkshopIds(String[] workshopCodes) throws Exception;

    /**
     * 是否启用
     *
     * @param isAll 是否显示全部
     * @return 是否启用结果集
     * <AUTHOR> 2017-09-26
     */
    List<DictionaryEntity> getInUse(boolean isAll);

    /**
     * 获取报警点级别结果集
     *
     * @param isAll 是否显示全部
     * @return 获取报警点级别结果集
     * <AUTHOR> 2017-11-10
     */
    List<DictionaryEntity> getCraftRankList(boolean isAll);

    /**
     * 获取监测类型列表
     *
     * @param isAll 是否显示全部
     * @return 监测类型列表
     * <AUTHOR> 2017-09-26
     */
    List<DictionaryEntity> getMonitorTypeList(boolean isAll);

    /**
     * 获取监测类型列表
     *
     * @return 监测类型列表
     * <AUTHOR> 2017-09-26
     */
    List<DictionaryEntity> getAlarmPriorityList(boolean isAll);

    /**
     * 获取仪表类型列表
     *
     * @param isAll 是否显示全部
     * @return 仪表类型列表
     * <AUTHOR> 2017-10-9
     * 1监测表；2控制表
     */
    List<DictionaryEntity> getInstrmtTypeList(boolean isAll);

    /**
     * 获取虚实标记列表
     *
     * @param isAll 是否显示全部
     * @return 虚实标记列表
     * <AUTHOR> 2017-10-9
     * 0实表(按读数)；1虚表(按用量)
     */
    List<DictionaryEntity> getVirtualRealityFlagList(boolean isAll);

    /**
     * 获取报警标识列表
     *
     * @param isAll 是否显示全部
     * @return 报警标识列表
     * <AUTHOR> 2017-10-9
     * 1PVHH、2PVHI、3PVLO、4PVLL
     */
    List<DictionaryEntity> getAllAlarmFlagList(boolean isAll);

    /**
     * 获取报警标识列表
     *
     * @param isAll 是否显示全部
     * @return 报警标识列表
     * <AUTHOR> 2017-10-9
     * 1PVHH、2PVHI、3PVLO、4PVLL
     */
    List<DictionaryEntity> getAlarmFlagList(boolean isAll);

    /**
     * 获取发生时间列表
     *
     * @param isAll 是否显示全部
     * @return 发生时间列表
     * <AUTHOR> 2017-10-9
     * 0自定义、1近七天、2近半个月、3近一个月、4近两个月、5近三个月、6近半年、7近一年
     */
    List<DictionaryEntity> getStartTimeList(boolean isAll);

    /**
     * 获取报警点类型列表
     *
     * @param isAll 是否显示全部
     * @return 报警点类型列表
     * <AUTHOR> 2017-10-9
     */
    List<AlarmPointTypeEntity> getAlarmPointTypeList(boolean isAll);

    /**
     * 根据装置编码获取生产单元
     *
     * @param unitCode  装置编码
     * @param isAll   是否显示全部
     * @return 生产单元列表
     * @throws Exception
     * <AUTHOR> 2017-10-09
     */
    List<PrdtCellEntity> getUnitPrdtCell(String unitCode, boolean isAll);

    /**
     * 获取所有已启用计量单位列表
     *
     * @param isAll 是否显示全部
     * @return 计量单位列表
     * <AUTHOR> 2017-10-10
     */
    List<MeasUnitEntity> getMeasUnitList(boolean isAll);

    /**
     * 获取国际报警标准值列表
     *
     * @return 国际报警标准值列表
     * <AUTHOR> 2017-10-17
     */
    List<DictionaryEntity> getISOAlarmList();

    /**
     * 查询时间
     *
     * @return 查询时间
     * <AUTHOR> 2017-10-17
     */
    List<DictionaryEntity> getQueryTime();

    /**
     * 初始化自定义显示日期(近7天)
     * 如果当前系统时间大于等于8:00:00（公共方法），则结束日期默认为“当天日期”（开始日期默认为“当天日期-6”）
     * 否则结束日期默认为“当天日期-1”（开始日期默认为“当天日期-7”）
     *
     * @param date 当前时间
     * @return 开始日期结束日期集合
     * <AUTHOR> 2017-10-19
     */
    List<DictionaryEntity> getShowTime(Date date);

    /**
     * 与getShowTime方法类似，去除是否大于八点的判断，默认查询当前日期-6到当前时间
     *
     * @param date 当前时间
     * @return 开始日期结束日期集合
     */
    List<DictionaryEntity> getCurrentTime(Date date);
    /**
     * 根据开始日期和结束日期确定查询的开始时间和结束时间
     * 如果“当前系统时间”大于等于“结束日期+1 8:00:00” 结束时间小于"结束日期+1天 8:00:00" 开始时间大于等于“开始日期 8:00:00”
     * 否则 结束时间小于等于"当前系统时间" 开始时间大于等于“开始日期 8:00:00”
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 开始时间和结束时间集合
     * <AUTHOR> 2017-10-20
     */
    Map<String, Object> getSearchTime(Date startDate, Date endDate) throws Exception;

    /**
     * 根据开始日期和结束日期确定查询的开始时间和结束时间
     * 如果“当前系统时间”大于等于“结束日期+1 8:00:00” 结束时间小于"结束日期+1天 8:00:00" 开始时间大于等于“开始日期 8:00:00”
     * 否则 结束时间小于等于"当前系统时间" 开始时间大于等于“开始日期 8:00:00”
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 开始时间和结束时间集合
     * <AUTHOR> 2017-10-20
     */
    Map<String, Object> getSearchTimeByDate(Date startDate, Date endDate) throws Exception;

    /**
     * 获取查询时间的小时数
     *
     * @return 查询时间的小时数
     * @throws Exception
     * <AUTHOR> 2017-11-2
     */
    int getQueryTimeHour() throws Exception;

    /**
     * 根据给定的日期时间计算查询开始时间和查询结束时间和一周之前的开始日期和结束日期
     * <p>
     * 原始需求:
     * 1) 若当天时间小于8：00:00（公共方法），系统默认显示“当天-1”的日期，否则默认显示当前系统日期；如果当前系统时间小于8点，结束日
     * 期小于当前系统日期，否则结束日期小于等于当天日期；时间格为“yyyy-MM-dd”,查询七天的报警事件；
     * 例如：
     * a）当前系统时间为“2017-10-18 9:00:00”（大于8点（公共方法）），则开始日期为2017-10-12，结束日期为2017-10-18；查询时间段为
     * “2017-10-12 8:00:00” 至“当前系统时间”；
     * b）如果当前系统时间为“2017-10-18 5:00：00（小于8点（公共方法）），开始日期为“2017-10-11”，结束日期为2017-10-17”；查询时
     * 间段为“2017-10-11 8:00:00” 至“当前系统时间”；
     * 2）如果“当前系统时间”大于等于“查询日期+1 8:00:00”，则开始时间大于等于“查询日期-6 8:00:00”， 结束时间小于“查询日期+1天 
     * 8:00:00”；否则，开始时间大于等于“查询日期-6 8:00:00”，结束时间小于等于“当前系统时间”；(开始时间计算规则，结束时间计算规
     * 则（公共方法）)
     * <p>
     * 需求分析(已确认):
     * <p>
     * 1.控制显示,如果当天时间小于8:00:00,则系统默认显示当天-1的日期;
     * <p>
     * 2.如果当前系统时间小于8点: 结束日期 < 当前系统日期;  结束日期=当前日期-1天; 开始日期=结束日期-6          startDate <= 条件 <  endDate
     * 如果当前系统时间大于8点:  结束日期 <=当天日期;    结束日期=当前日期    ; 开始日期=结束日期-6           startDate <= 条件 <= endDate
     * <p>
     * 3.查询日期+1 8:00:00 <= 当前系统时间              结束日期=查询日期+1  ;  开始时间=查询日期-6          startDate <= 条件 <  endDate
     * 查询日期+1 8:00:00 >  当前系统时间              结束日期=当前日期    ;  开始时间=查询日期-6          startDate <= 条件 <= endDate
     *
     * @param date 给定的日期
     * @return 开始时间和结束时间List
     * <AUTHOR> 2017-10-22
     */
    List<DictionaryEntity> getQueryStartAndEndDate(Date date) throws Exception;

    List<DictionaryEntity> getQueryStartAndEndDate(Date startTime, Date endTime) throws Exception;
    /**
     * 获取报警级别列表
     *
     * @param isAll 是否显示全部
     * @return 警级别列表
     * <AUTHOR> 2017-10-17
     */
    List<DictionaryEntity> getAlarmStateComparisonList(boolean isAll);

    /**
     * 获取报警状态列表
     *
     * @param isAll 是否显示全部
     * @return 报警状态列表
     * <AUTHOR> 2017-10-17
     */
    List<DictionaryEntity> getAlarmLevelList(boolean isAll);

    /**
     * 根据日期时间段和间隔分钟数获取时间段数量
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param interval  间隔分钟数
     * @return 数量
     * <AUTHOR> 2017-10-18
     */
    List<DictionaryEntity> getCountByTimeInterval(Date startDate, Date endDate, int interval);
    /**
     * 根据报警开始日期和报警结束日期获取装置的总的报警数
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param unitCode    装置编码
     * @return 装置的总的报警数
     * <AUTHOR> 2017-10-30
     */
    List<DictionaryEntity> getAlarmEventTotalCount(Date startTime, Date endTime, String unitCode);


    /**
     * 根据报警开始日期和报警结束日期获取装置的总的报警数
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param unitCodes    装置编码
     * @return 装置的总的报警数
     * <AUTHOR> 2017-10-30
     */
    List<DIcCodeCountEntity> getAlarmEventTotalCountByUnits( String[] unitCodes,Date startTime, Date endTime);

    /**
     * 根据报警开始日期和报警结束日期获取装置的总的操作数
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param unitCode    装置编码
     * @return 装置的总的报警数
     * <AUTHOR> 2017-10-30
     */
    List<DictionaryEntity> getUnitTotalOperate(Date startTime, Date endTime, String unitCode);

    /**
     * 根据装置编码获取装置的操作人工数
     *
     * @param unitCode    装置编码
     * @return 装置的操作人数
     * <AUTHOR> 2017-10-18
     */
    List<DictionaryEntity> getUnitPerson(String unitCode);

    /**
     * 获取所有生产单元列表
     *
     * @return 生产单元列表
     * <AUTHOR> 2017-10-19
     */
    List<PrdtCellEntity> getAllPrdtCellList() throws Exception;

    /**
     * 根据开始和结束日期获取Top N 最频繁的报警
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCode    装置编码
     * @param top       选择Top N数据
     * @return 最频繁报警Top N集合
     * <AUTHOR> 2017-10-30
     */
    List<AlarmEventExEntity> getMostAlarmTop(Date startTime, Date endTime, String unitCode, Integer top) throws Exception;

    /**
     * 根据开始和结束日期获取Top N 最频繁的操作
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCode    装置编码
     * @param top       选择Top N数据
     * @return 最频繁操作Top N集合
     * <AUTHOR> 2017-10-30
     */
    List<AlarmEventExEntity> getMostAlarmOperateTop(Date startTime, Date endTime, String unitCode, Integer top) throws Exception;

    /**
     * 根据开始时间和结束时间及装置ID获取该装置的报警优先级比率
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCode    装置编码
     * @return 报警优先级分组总数
     * <AUTHOR> 2017-10-30
     */
    List<DictionaryEntity> getAlarmPriorityByUnitId(Date startTime, Date endTime, String unitCode);

    /**
     * 获取服务器系统日期时间
     *
     * @return 服务器系统日期时间
     * <AUTHOR> 2017-10-31
     */
    List<DictionaryEntity> getSysDateTime();

    /**
     * 根据报警事件父ID获取报警事件ID列表
     *
     * @param parentId 父ID
     * @return 报警事件ID列表
     * <AUTHOR> 2017-11-12
     */
    Long[] getEventTypeIdsByParentId(Long parentId);

    /**
     * 获取报警数和操作数列表
     *
     * @param startTime  开始日期
     * @param endTime    结束日期
     * @param unitCodes    装置编码集合
     * @param prdtIds    生产单元ID集合
     * @param checkModel 模式 0:装置模式,1:工厂车间模式
     * @param dateType   查询日期类型(日:day,周:week,月:month,小时:hour)
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-17
     */
    List<AlarmNumberAssessEntity> getOperateAndAlarmNumberList(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, int checkModel, CommonEnum.DateTypeEnum dateType) throws Exception;

    /**
     * 获取报警数量评估实体集合
     *
     * <AUTHOR> 2017-12-25
     * @param startTime 时间范围开始
     * @param endTime   时间范围结束
     * @param dateType  时间粒度
     * @param ids       ID数组
     * @param queryType 查询类型
     * @return 报警数量评估实体集合
     * @throws Exception
     * @throws ParseException
     */
    List<AlarmNumberAssessEntity> getAlarmNumberAssessList(Date startTime, Date endTime, CommonEnum.DateTypeEnum dateType, String[] ids, CommonEnum.EquipmentTypeEnum queryType) throws Exception, ParseException;


    List<AlarmNumberAssessEntity> getAlarmNumberAssessList(Date startTime, Date endTime,Long[] alarmFlagId, CommonEnum.DateTypeEnum dateType, String[] ids, CommonEnum.EquipmentTypeEnum queryType ,Integer[] priority, Boolean priorityFlag,  Integer isElimination) throws Exception, ParseException;

    /**
     * 获取图表X轴显示日期区间列表
     *
     * @param startTime 查询开始日期时间
     * @param endTime   查询结束日期时间
     * @param dateType  日期类型(day|month|week|hour)
     * @return
     * <AUTHOR> 2017-11-18
     */
    List<DictionaryEntity> getChartDateShowPeriod(Date startTime, Date endTime, String dateType);

    /**
     * 查看因果报警分析-一级列表数据
     *
     * @param startTime   时间范围起始
     * @param endTime     时间分为结束
     * @param unitCodes     装置ID数组
     * @param prdtCellIds 生产单元ID数组
     * @param page        分页对象
     * @return 一级列表数据
     * <AUTHOR> 2017-11-16
     */
    PaginationBean<CausalAlarmAnalysisEntity> getCausalAlarmAnalysis(Date startTime, Date endTime,
                                                                     String[] unitCodes, Long[] prdtCellIds, Pagination page) throws Exception;

    /**
     * 查看因果报警分析-二级列表数据
     *
     * @param startTime    时间范围起始
     * @param endTime      时间分为结束
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @param unitCode       装置编码
     * @param page         分页对象
     * @return 二级列表数据
     * <AUTHOR> 2017-11-16
     */
    PaginationBean<CausalAlarmAnalysisTableEntity> getCausalAlarmAnalysisTable(Date startTime, Date endTime,
                                                                               Long alarmPointId, Long alarmFlagId, String unitCode, Pagination page);

    /**
     * 获取报警国际标准列表
     *
     * @return 报警国际标准列表
     * <AUTHOR> 2017-11-22
     */
    List<DictionaryEntity> getAlarmIso();

    /**
     * 持续报警分析分页查询
     *
     * @param unitCodes     装置编码集合
     * @param prdtCellIds 生产单元ID集合
     * @param alarmFlagId 报警标识
     * @param isHandle    是否处理（0:未处理,1:已处理,-1:全部）
     * @param beginTime   报警事件开始间
     * @param endTime     报警事件结束时间
     * @param page        分页实现类
     * @return 报警事件实体集合
     * @throws Exception 
     * <AUTHOR> 2017-11-2 update by xuelei.wang 2017-11-24
     */
    PaginationBean<AlarmEventEntity> getPersistentAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds,
                                                                Integer alarmFlagId, Integer isHandle, Date beginTime, Date endTime, Pagination page) throws Exception;
    List<AlarmEventEntity> getAlarmAmount(String[] unitCodes, Date beginTime, Date endTime) throws Exception;

    /**
     * 根据中装置ID获取操作工人数
     *
     * @param unitCode 装置编码
     * @return 操作工人数
     * @throws Exception
     * <AUTHOR> 2017-11-30
     */
    Long getOperatePersonCountByUnitId(String unitCode) throws Exception;

    /**
     * 根据装置编码集合获装置人员列表
     *
     * @param unitCodes 装置编码集合
     * @return 装置人员列表
     * @throws Exception
     * <AUTHOR> 2017-12-11
     */
    List<UnitPersonEntity> getUnitPersonListByUnitIds(List<String> unitCodes) throws Exception;

    /**
     * 根据工厂ID集合获取工厂列表
     *
     * @param factoryIds
     * @return
     * <AUTHOR> 2017-12-11
     */
    List<FactoryEntity> getFactoryListById(Long[] factoryIds) throws Exception;


    /**
     * 获取已经授权的装置的属性列表
     * @param userId 用户ID
     * @return
     * <AUTHOR> 2017-12-20
     */
    List<AuthorizeEntity> getAuthorizePropertyList(String userId) ;

    /**
     * 获取报警变更方案状态枚举列表
     *
     * @param isAll       是否显示全部
     * @return 报警变更方案状态枚举列表
     * <AUTHOR> 2018-1-19
     */
    List<DictionaryEntity> getAlarmChangePlanStatusList(boolean isAll) throws Exception;
   /**
     * 获取是否屏蔽枚举集合
     *
     * @param isAll 是否显示全部
     * @return 是否屏蔽枚举集合
     * <AUTHOR> 2018-1-19
     */
    List<DictionaryEntity> getInSuppressedList(boolean isAll) throws Exception;
    /**
     * 根据报警标识和报警点集合获取变更未完成的集合
     *
     * @param alarmFlagList   报警标识列表
     * @param alarmPointList  报警点列表
     * @return 变更未完成的集合
     * <AUTHOR> 2018-01-23
     */
    List<DictionaryEntity> getUnfinishedPlanList(List<Long> alarmFlagList,List<Long> alarmPointList) throws Exception;
    /**
     * 获取报警变更方案申请工艺类型状态枚举列表
     *
     * @param isAll 是否显示全部
     * @return 报警变更方案申请工艺类型状态枚举列表
     * <AUTHOR> 2019-04-11
     */
    List<DictionaryEntity> getTecAlarmChangeBizApplyEnumList(boolean isAll) throws Exception;
    /**
     * 获取报警变更方案申请状态枚举列表
     *
     * @param isAll 是否显示全部
     * @return 报警变更方案申请状态枚举列表
     * <AUTHOR> 2018-1-19
     */
    List<DictionaryEntity> getAlarmChangeBizApplyEnumList(boolean isAll) throws Exception;
    /**
     * 获取报警变更方案下发状态枚举列表
     *
     * @param isAll 是否显示全部
     * @return 报警变更方案下发状态枚举列表
     * <AUTHOR> 2018-1-19
     */
    List<DictionaryEntity> getAlarmChangeBizIssueList(boolean isAll) throws Exception;
    /**
     * 获取报警变更方案确认状态枚举列表
     *
     * @param isAll 是否显示全部
     * @return 报警变更方案确认状态枚举列表
     * <AUTHOR> 2018-1-19
     */
    List<DictionaryEntity> getAlarmChangeBizConfirmList(boolean isAll) throws Exception;
    /**
     * 获取报警制度管理分类枚举
     *
     * @param isAll 是否显示全部
     * @return 报警状态列表
     * <AUTHOR> 2018-02-28
     */
    List<DictionaryEntity> getAlarmStdManagmtCatgrList(boolean isAll);
    /**
     * 根据Code获取系统运行参数
     *
     * @param code      参数编码
     * @return          参数信息
     * @throws Exception
     * <AUTHOR> 2018-3-13
     */
    SystRunParaConfEntity getSystRunParamByCode(String code) throws Exception;
    /**
     * 获取变更方案审核状态枚举
     *
     * @param isAll 是否显示全部
     * @return 更方案审核状态枚举列表
     * <AUTHOR> 2018-03-14
     */
    List<DictionaryEntity> getAlarmChangePlanApproStatusList(boolean isAll);
    /**
     * 获取流程预览URL
     *
     * @return 预览地址
     * @throws Exception
     * <AUTHOR> 2018-03-14
     */
    String getWorkFlowPreviewURL() throws Exception;

    /**
     * 获取已启用 DcsCode 列表
     *
     * @param isAll 是否显示全部
     * @return
     * <AUTHOR> 2018-03-30
     */
    List<DcsCodeEntity> getDcsCodeList(boolean isAll);
    /**
     * 获取已启用 OpcCode 列表
     *
     * @param isAll 是否显示全部
     * @return
     * <AUTHOR> 2018-08-23
     */
    List<OpcCodeEntity> getOpcCodeList(boolean isAll);

    /**
     * 获取已启用事件类型列表
     *
     * @param isAll 是否显示全部
     * @return
     * <AUTHOR> 2018-03-30
     */
    List<EventTypeEntity> getEventTypeList(boolean isAll) throws Exception;
    /**
     * 获取未匹配计量单位枚举
     *
     * @param isAll 是否显示全部
     * @return 未匹配计量单位枚举
     * <AUTHOR> 2018-04-19
     */
    List<DictionaryEntity> getMeasUnitUnmatchData(boolean isAll);
    /**
     * 获取未匹配报警点枚举
     *
     * @param isAll 是否显示全部
     * @return 未匹配报警点枚举
     * <AUTHOR> 2018-04-25
     */
    List<DictionaryEntity> getAlarmPointUnmatchData(boolean isAll);

    /**
     * 获取系统参数配置参数值
     *
     * @param code 参数编码
     * @return 参数值
     * <AUTHOR> 2018-07-25
     */
    String getSystRunParaConfParaValue(String code);

    String getCurrentUser();

    /**
     * 根据装置获取企业id
     * @param unit 装置id集合
     * @return
     */
    public Integer getCompanyIdByUnit(String[] unit);

    /**
     * 获取剔除数据详细信息
     *
     * @param unitCodes 装置集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return AlarmPointDelConfigDetailEntity
     */
    public List<AlarmPointDelConfigDetailEntity> getAlarmPointDelConfigDetail(String[] unitCodes, Date startTime, Date endTime);
}
