package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.AlarmPointView;

/*
 * ChangeTagList实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_ChangeTagListRepositoryCustom
 * 作       者：dageng.sun
 * 创建时间：2018/1/22
 * 修改编号：1
 * 描       述：ChangeTagList实体的Repository的JPA自定义接口
 */
public interface AlarmPointViewRepositoryCustom {

	/**
	 * 位号查询
	 * 
	 * <AUTHOR> 2018-01-23 
	 * @param unitCode 装置编码
	 * @param tag 位号
	 * @param page 分页对象
	 * @return PaginationBean<AlarmPointView> 返回AlarmPointView实体分页对象
	 */
	PaginationBean<AlarmPointView> getAlarmPointView(String unitCode,String tag,Pagination page);
	
}
