package com.pcitc.opal.aa.bll.entity;

/*
 * 报警数量评估趋势图实体
 * 模块编号：pcitc_opal_bll_class_AlarmNumberAssessTrendEntity
 * 作  　者：kun.zhao
 * 创建时间：2017-10-30
 * 修改编号：1
 * 描    述：报警数量评估趋势图实体
 */
public class AlarmNumberAssessTrendEntity {
	
    /**
     * 名称
     */
    private String name;
    /**
     * 时间字符串
     */
    private String timeStr;
    /**
     * 数量
     */
    private Long count;
    
	public AlarmNumberAssessTrendEntity() {}
	public AlarmNumberAssessTrendEntity(Long count, String name,  String timeStr) {
		this.name = name;
		this.timeStr = timeStr;
		this.count = count;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Long getCount() {
		return count;
	}
	public void setCount(Long count) {
		this.count = count;
	}
	public String getTimeStr() {
		return timeStr;
	}
	public void setTimeStr(String timeStr) {
		this.timeStr = timeStr;
	}
}
