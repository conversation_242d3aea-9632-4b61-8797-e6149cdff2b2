package com.pcitc.opal.ad.dao.imp;

import com.pcitc.opal.ad.dao.MobileMsgListRepositoryCustom;
import com.pcitc.opal.ad.pojo.MobileMsgList;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class MobileMsgListRepositoryImpl extends BaseRepository<MobileMsgList, Long> implements MobileMsgListRepositoryCustom {
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult add(MobileMsgList mobileMsgList) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(mobileMsgList);
            commonResult.setResult(mobileMsgList);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    @Override
    public PaginationBean<MobileMsgList> getAlarmMsgConfig(String[] unitCodes, Long[] prdtCellIds, String tag,Integer status, Date startSendTime, Date endSendTime, Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder();
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
           hql.append(" from MobileMsgList t where t.sendTime between :startSendTime and :endSendTime ");
            paramList.put("startSendTime", startSendTime);
            paramList.put("endSendTime", endSendTime);

            hql.append(" and t.companyId = :companyId ");
            paramList.put("companyId", new CommonProperty().getCompanyId());

           //装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append(" and t.unitCode in (:unitCode) ");
                paramList.put("unitCode", Arrays.asList(unitCodes));
            }
            //生产单元
            if (ArrayUtils.isNotEmpty(prdtCellIds)) {
                hql.append(" and t.prdtCellId in (:prdtCellId) ");
                paramList.put("prdtCellId", Arrays.asList(prdtCellIds));
            }
            //位号
            if (StringUtils.isNotEmpty(tag)){
                hql.append(" and t.tag like upper(:tag) escape '/' ");
                paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
            }
            //状态
            if (null != status && status != -1){
                hql.append(" and t.status = :status ");
                paramList.put("status", status);
            }
            hql.append(" order by t.sendTime desc ");
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception e) {
            throw e;
        }
    }
}
