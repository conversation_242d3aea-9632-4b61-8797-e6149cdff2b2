package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmFlagCompRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmFlagComp;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * AlarmFlagComp实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_AlarmFlagCompRepositoryImpl
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/30
 * 修改编号：1
 * 描    述：AlarmFlagComp实体的Repository实现
 */
public class AlarmFlagCompRepositoryImpl extends BaseRepository<AlarmFlagComp, Long> implements AlarmFlagCompRepositoryCustom {

    /**
     * 唯一性校验
     *
     * @param alarmFlagCompEntity 报警标识对照实体
     * @return 查询返回信息类
     * <AUTHOR> 2018-03-30
     */
    @Override
    public CommonResult alarmFlagCompValidation(AlarmFlagComp alarmFlagCompEntity) {
        CommonResult commonResult = new CommonResult();
        try {
            // “源报警标识”唯一性校验，提示：“源报警标识已存在！”
            StringBuilder hql = new StringBuilder(
                    "from AlarmFlagComp t where t.alarmFlagSource =:alarmFlagSource and t.dcsCodeId =:name and t.alarmFlagCompId<>:alarmFlagCompId");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("alarmFlagSource", alarmFlagCompEntity.getAlarmFlagSource());
            paramList.put("alarmFlagCompId", alarmFlagCompEntity.getAlarmFlagCompId() == null ? 0 : alarmFlagCompEntity.getAlarmFlagCompId());
            paramList.put("name",alarmFlagCompEntity.getDcsCodeId());
            long index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("该DCS下源报警标识已存在！");
            }

        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    /**
     * 新增报警标识对照
     *
     * @param alarmFlagCompEntity 添加的实体
     * <AUTHOR> 2018-03-30
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addAlarmFlagComp(AlarmFlagComp alarmFlagCompEntity) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(alarmFlagCompEntity);
            commonResult.setResult(alarmFlagCompEntity);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 删除报警标识对照
     *
     * @param alarmFlagCompIds 报警标识对照ID集合
     * @return 消息结果类
     * <AUTHOR> 2018-03-30
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult deleteAlarmFlagComp(Long[] alarmFlagCompIds) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            String hql = " from AlarmFlagComp t where t.alarmFlagCompId in (:alarmFlagCompIds)";
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> alarmFlagCompIdsList = Arrays.asList(alarmFlagCompIds);
            paramList.put("alarmFlagCompIds", alarmFlagCompIdsList);

            TypedQuery<AlarmFlagComp> query = getEntityManager().createQuery(hql, AlarmFlagComp.class);
            this.setParameterList(query, paramList);
            List<AlarmFlagComp> alarmFlagCompList = query.getResultList();
            alarmFlagCompList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 更新报警标识对照
     *
     * @param alarmFlagCompEntity 报警标识对照实体
     * @return 消息结果类
     * <AUTHOR> 2018-03-30
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult updateAlarmFlagComp(AlarmFlagComp alarmFlagCompEntity) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(alarmFlagCompEntity);
            commonResult.setResult(alarmFlagCompEntity);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 获取报警标识对照实体
     *
     * @param alarmFlagCompId 报警标识对照ID
     * @return 报警标识对照实体
     * <AUTHOR>  2018-03-30
     */
    @Override
    public AlarmFlagComp getSingleAlarmFlagComp(Long alarmFlagCompId) {
        try {
            return getEntityManager().find(AlarmFlagComp.class, alarmFlagCompId);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取报警标识对照实体
     *
     * @param alarmFlagCompIds 报警标识对照ID集合
     * @return 报警标识对照实体集合
     * <AUTHOR> 2018-03-30
     */
    @Override
    public List<AlarmFlagComp> getAlarmFlagComp(Long[] alarmFlagCompIds) {
        try {
            // 查询字符串
            String hql = "from AlarmFlagComp t ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (alarmFlagCompIds.length > 0) {
                hql += " where t.alarmFlagCompId in (:alarmFlagCompIds)";
                List<Long> alarmFlagCompIdsList = Arrays.asList(alarmFlagCompIds);
                paramList.put("alarmFlagCompIds", alarmFlagCompIdsList);
            }
            TypedQuery<AlarmFlagComp> query = getEntityManager().createQuery(hql, AlarmFlagComp.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取报警标识对照实体（分页）
     *
     * @param dcsCodeId DCS名称
     * @param alarmFlagSource 源报警事件标识
     * @param alarmFlagId 本系统报警标识
     * @param inUse   是否使用
     * @param page 翻页实现类
     * @return 报警标识对照实体（分页）
     * <AUTHOR> 2018-03-30
     */
    @Override
    public PaginationBean<AlarmFlagComp> getAlarmFlagComp(Long dcsCodeId, String alarmFlagSource, Long alarmFlagId,Integer inUse, Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from AlarmFlagComp t where 1=1 ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();

            if (dcsCodeId != null) {
                hql.append("  and dcsCodeId = :dcsCodeId ");
                paramList.put("dcsCodeId", dcsCodeId);
            }
            // 源报警事件标识
            if (!StringUtils.isEmpty(alarmFlagSource)) {
                hql.append("  and (t.alarmFlagSource like :alarmFlagSource)");
                paramList.put("alarmFlagSource", "%" + this.sqlLikeReplace(alarmFlagSource) + "%");
            }
            if (alarmFlagId != null) {
                hql.append("  and alarmFlagId = :alarmFlagId ");
                paramList.put("alarmFlagId", alarmFlagId);
            }
            if (inUse != null) {
                hql.append("  and inUse = :inUse ");
                paramList.put("inUse", inUse);
            }
            hql.append(" order by t.dcsCode.name, alarmFlagSource");
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
    }

}
