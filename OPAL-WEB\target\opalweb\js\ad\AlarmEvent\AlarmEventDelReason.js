var delUrl = OPAL.API.adUrl + '/alarmEvents/commitAlarmEvent';
var anlyUrl = OPAL.API.adUrl + '/alarmEvents/getAnlyByEvent';
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.None;
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var idsArray = '';
    var page = {
        init: function () {
            this.bindUI();
        },
        bindUI: function () {
            $('#saveAddModal').click(function () {
                page.logic.submit();
            });
            $('.closeBtn').click(function () {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer(false);
            })
        },
        logic: {
            /**
             * 提交
             */
            submit: function () {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    layer.msg('请填写剔除原因！');
                    return;
                }
                var data = {
                    eventIds: idsArray,
                    reason: $('#reason').val()
                }
                $.ajax({
                    url: delUrl,
                    async: false,
                    data: data,
                    dataType: "text", //后台返回为汉字时,dataType=text
                    // contentType: "application/json",
                    type: 'POST', //PUT DELETE POST
                    success: function (result) {
                        window.pageLoadMode = PageLoadMode.Refresh;
                        layer.msg(result, {
                            time: 1000
                        }, function () {
                            page.logic.closeLayer(true);
                        });
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                idsArray = data.idsArray;
                var data = {
                    eventIds: idsArray
                }
                $.ajax({
                    url: anlyUrl,
                    async: false,
                    data: data,
                    type: 'get', //PUT DELETE POST
                    success: function (result) {
                        if (result.length > 0) {
                            $('#reason').val(result[0].reasonDes)
                        }
                    }
                })

            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal', {
                    rules: {
                        reason: {
                            required: true
                        },
                    }
                })

            }
        }

    }
    page.init();
    window.page = page;
})