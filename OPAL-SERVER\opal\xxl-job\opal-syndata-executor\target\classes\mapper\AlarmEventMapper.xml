<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.mapper.AlarmEventMapper">

    <resultMap id="BaseResultMap" type="com.pcitc.opal.pojo.AlarmEvent">
            <id property="eventId" column="event_id" jdbcType="BIGINT"/>
            <result property="eventTypeId" column="event_type_id" jdbcType="BIGINT"/>
            <result property="alarmPointId" column="alarm_point_id" jdbcType="BIGINT"/>
            <result property="alarmFlagId" column="alarm_flag_id" jdbcType="BIGINT"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="alarmTime" column="alarm_time" jdbcType="TIMESTAMP"/>
            <result property="priority" column="priority" jdbcType="BIGINT"/>
            <result property="limitValue" column="limit_value" jdbcType="BIGINT"/>
            <result property="inShelved" column="in_shelved" jdbcType="BIGINT"/>
            <result property="inSuppressed" column="in_suppressed" jdbcType="BIGINT"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
            <result property="des" column="des" jdbcType="VARCHAR"/>
            <result property="previousValue" column="previous_value" jdbcType="VARCHAR"/>
            <result property="nowValue" column="now_value" jdbcType="VARCHAR"/>
            <result property="parameter" column="parameter" jdbcType="VARCHAR"/>
            <result property="unitCode" column="unit_code" jdbcType="VARCHAR"/>
            <result property="prdtcellId" column="prdtcell_id" jdbcType="BIGINT"/>
            <result property="dcsCode" column="dcs_code" jdbcType="VARCHAR"/>
            <result property="tag" column="tag" jdbcType="VARCHAR"/>
            <result property="alarmFlag" column="alarm_flag" jdbcType="VARCHAR"/>
            <result property="priorityCache" column="priority_cache" jdbcType="VARCHAR"/>
            <result property="writeTime" column="write_time" jdbcType="TIMESTAMP"/>
            <result property="companyId" column="company_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        event_id,event_type_id,alarm_point_id,
        alarm_flag_id,start_time,alarm_time,
        priority,limit_value,in_shelved,
        in_suppressed,operator,des,
        previous_value,now_value,parameter,
        unit_code,prdtcell_id,dcs_code,
        tag,alarm_flag,priority_cache,
        write_time,company_id
    </sql>
    <select id="selectByMoreThanEventIdToEnterpriseDB" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_ad_alarmevent
        where
        event_id > #{eventId,jdbcType=NUMERIC}
        order by event_id limit #{batchSize,jdbcType=NUMERIC}
    </select>
</mapper>
