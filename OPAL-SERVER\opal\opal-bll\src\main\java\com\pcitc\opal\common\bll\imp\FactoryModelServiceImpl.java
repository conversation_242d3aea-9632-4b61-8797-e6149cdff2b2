package com.pcitc.opal.common.bll.imp;

import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.AAAService;
import com.pcitc.opal.common.bll.FactoryModelService;
import com.pcitc.opal.common.bll.entity.*;
import com.pcitc.opal.common.bll.vo.AreaVO;
import com.pcitc.opal.common.bll.vo.FactoryVO;
import com.pcitc.opal.common.bll.vo.UnitVO;
import com.pcitc.opal.common.bll.vo.WorkShopVO;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pcitc.imp.common.ettool.utils.ObjectConverter;
import pcitc.imp.common.ettool.utils.RestfulTool;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 调用ProMACE平台的工厂模型服务
 */
@Component
@ConditionalOnProperty(name = "runtime_type", havingValue = "promace")
public class FactoryModelServiceImpl implements FactoryModelService {
    @Value("${factorymodel.base.url}")
    private String fmBaseUrl;
    @Value("${factorymodel.bizCode}")
    private String bizCode;
    @Value("${factorymodel.workshopTypeCode}")
    private String workshopTypeCode;
    @Value("${factorymodel.factoryTypeCode}")
    private String factoryTypeCode;
    @Autowired
    private AAAService aaaService;

    /**
     * 获取装置树形结构
     *
     * @param enablePrivilege 是否启用权限过滤
     * @return 树形实体集合
     * <AUTHOR> 2017-09-25
     */
    @Override
    public List<OrgEntity> getAllUnitTree(boolean enablePrivilege) throws Exception {
        try {
            List<OrgEntity> orgEntityList = new ArrayList<>();
            List<UnitEntity> unitList = this.getUnitList(enablePrivilege);
            if (unitList.size() == 0) return orgEntityList;
            List<WorkshopEntity> wsList = this.getWorkshopListByWorkshopIds(unitList.stream().map(u -> u.getWorkshopCode()).distinct().toArray(String[]::new));
            if (wsList.size() == 0) return orgEntityList;
            List<FactoryEntity> facList = this.getFactoryList(wsList.stream().map(w -> w.getFactoryCode()).distinct().toArray(String[]::new));
            if (facList.size() == 0) return orgEntityList;
            for (UnitEntity u : unitList) {
                OrgEntity unitEntity = new OrgEntity();
                unitEntity.setId(u.getStdCode());
                unitEntity.setOriginalId(u.getStdCode());
                unitEntity.setType(2);
                unitEntity.setSname(u.getSname());
                unitEntity.setSortNum(u.getSortNum());
                orgEntityList.add(unitEntity);
                unitEntity.setParentId(u.getWorkshopCode());
                OrgEntity wsEntity = orgEntityList.stream().filter(i -> i.getType() == 1 && i.getOriginalId().equals(u.getWorkshopCode())).findFirst().orElse(null);
                if (wsEntity == null) {
                    WorkshopEntity findWSEntity = wsList.stream().filter(w -> w.getStdCode().equals(u.getWorkshopCode())).findFirst().orElse(null);
                    if (findWSEntity != null) {
                        wsEntity = new OrgEntity();
                        wsEntity.setId(findWSEntity.getStdCode());
                        wsEntity.setOriginalId(findWSEntity.getStdCode());
                        wsEntity.setType(1);
                        wsEntity.setSname(findWSEntity.getSname());
                        wsEntity.setSortNum(findWSEntity.getSortNum());
                        wsEntity.setParentId(findWSEntity.getFactoryCode());
                        orgEntityList.add(wsEntity);
                    }
                }
                if (wsEntity != null) {
                    String factoryCode = wsEntity.getParentId();
                    OrgEntity facEntity = orgEntityList.stream().filter(i -> i.getType() == 0 && i.getOriginalId().equals(factoryCode)).findFirst().orElse(null);
                    if (facEntity == null && facList != null && facList.size() != 0) {
                        FactoryEntity findFacEntity = facList.stream().filter(f -> f.getOrgCode().equals(factoryCode)).findFirst().orElse(null);
                        if (findFacEntity == null) continue;
                        facEntity = new OrgEntity();
                        facEntity.setId(findFacEntity.getOrgCode());
                        facEntity.setOriginalId(findFacEntity.getOrgCode());
                        facEntity.setType(0);
                        facEntity.setSname(findFacEntity.getOrgAlias());
                        facEntity.setSortNum(findFacEntity.getSortNum());
                        orgEntityList.add(facEntity);
                        facEntity.setParentId("0");
                    }
                }
            }
            orgEntityList = orgEntityList.stream()
                    .sorted(Comparator.comparing(OrgEntity::getType)
                            .thenComparing(item -> item.getSortNum() == null ? 0 : item.getSortNum(), Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(OrgEntity::getSname, ComparatorList.orderByASC()))
                    .collect(Collectors.toList());
            return orgEntityList;
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取生产装置列表
     *
     * @param enablePrivilege 是否启用权限过滤
     * @return 生产装置列表
     * <AUTHOR> 2017-09-26
     */
    @Override
    public List<UnitEntity> getUnitList(boolean enablePrivilege) throws Exception {
        return this.getUnitListByIds(null, false, enablePrivilege);
    }

    /**
     * 根据装置编码集合获取装置列表
     *
     * @param unitCodes       装置编码集合
     * @param isAll           是否显示全部
     * @param enablePrivilege 是否启用权限过滤
     * @return 装置列表
     * <AUTHOR> 2017-09-26
     */
    @Override
    public List<UnitEntity> getUnitListByIds(String[] unitCodes, boolean isAll, boolean enablePrivilege) throws Exception {
        List<UnitEntity> unitEntityList = new ArrayList<>();
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("inUse", Integer.toString(CommonEnum.InUseEnum.Yes.getIndex())));
        //处理装置权限
        boolean authResult = this.handlerAuth(list, unitCodes, enablePrivilege);
        if (authResult == false) {
            return unitEntityList;
        }
        //发送请求
        String unitCollection = MicroServiceClient.getRequest(fmBaseUrl + "/plants", list);
        List<UnitVO> unitVOList = RestfulTool.toResourceRepList(unitCollection, UnitVO.class);
        //实体转换
        if (unitVOList != null) {
            unitEntityList = this.convertToEntity(unitVOList);
        }
        //处理全部
        if (isAll && unitEntityList.size() > 1) {
            UnitEntity unitEntity = new UnitEntity();
            unitEntity.setStdCode("-1");
            unitEntity.setName("全部");
            unitEntity.setSname("全部");
            unitEntityList.add(0, unitEntity);
        }
        return unitEntityList;
    }

    /**
     * 根据车间编码集合获取该车间下所有的已启用的装置列表
     *
     * @param workshopCodes
     * @param enablePrivilege enablePrivilege
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-7
     */
    @Override
    public List<UnitEntity> getUnitListByWorkshopIds(String[] workshopCodes, boolean enablePrivilege) throws Exception {
        List<UnitEntity> unitEntityList = new ArrayList<>();
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("inUse", Integer.toString(CommonEnum.InUseEnum.Yes.getIndex())));
        //处理装置权限
        boolean authResult = this.handlerAuth(list, null, enablePrivilege);
        if (authResult == false) {
            return unitEntityList;
        }
        if (workshopCodes != null && workshopCodes.length > 0) {
            list.add(new BasicNameValuePair("$orgCodes", String.join(",", workshopCodes)));
        }
        //限制区域类型为装置
        list.add(new BasicNameValuePair("areaTypeCode", "plants"));
        //发送请求
        String unitCollection = MicroServiceClient.getRequest(fmBaseUrl + "/areaDictionaries", list);
        List<AreaVO> unitVOList = RestfulTool.toResourceRepList(unitCollection, AreaVO.class);
        //实体转换
        if (unitVOList != null) {
            for (AreaVO areaVO : unitVOList) {
                UnitEntity unitEntity = new UnitEntity();
                unitEntity.setStdCode(areaVO.getAreaCode());
                unitEntity.setName(areaVO.getAreaName());
                unitEntity.setSname(areaVO.getAreaAlias());
                unitEntity.setInUse(areaVO.getInUse());
                unitEntity.setSortNum(areaVO.getSortNum());
                unitEntity.setDes(areaVO.getDes());
                unitEntityList.add(unitEntity);
                unitEntity.setWorkshopName(areaVO.getOrgAlias());
                unitEntity.setWorkshopCode(areaVO.getOrgCode());
            }
        }
        return unitEntityList;
    }

    /**
     * 根据车间编码集合获取车间列表
     *
     * @param workshopCodes 车间编码
     * @return 车间集合
     * @throws Exception
     * <AUTHOR> 2017-11-7
     */
    @Override
    public List<WorkshopEntity> getWorkshopListByWorkshopIds(String[] workshopCodes) throws Exception {
        List<WorkshopEntity> workshopEntityList = new ArrayList<>();
        String workshopUrl = String.format("%s/bizOrgMains/%s/bizOrgDTLs", fmBaseUrl, bizCode);
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("inUse", Integer.toString(CommonEnum.InUseEnum.Yes.getIndex())));
        list.add(new BasicNameValuePair("orgTypeCode", workshopTypeCode));
        if (workshopCodes != null && workshopCodes.length != 0) {
            list.add(new BasicNameValuePair("$orgCodes", String.join(",", workshopCodes)));
        }
        //发送请求
        String factoryCollection = MicroServiceClient.getRequest(workshopUrl, list);
        List<WorkShopVO> workShopVOList = RestfulTool.toResourceRepList(factoryCollection, WorkShopVO.class);

        //实体转换
        if (workShopVOList != null) {
            for (WorkShopVO vo : workShopVOList) {
                WorkshopEntity entity = new WorkshopEntity();
                entity.setStdCode(vo.getOrgCode());
                entity.setName(vo.getOrgName());
                entity.setSname(vo.getOrgAlias());
                entity.setDes(vo.getDes());
                entity.setSortNum(vo.getSortNum());
                entity.setFactorySname(vo.getParentOrgAlias());
                entity.setFactoryCode(vo.getParentOrgCode());
                workshopEntityList.add(entity);
            }
        }
        return workshopEntityList;
    }

    /**
     * 根据工厂编码查询工厂集合
     *
     * @param factoryCodes
     * @return 工厂实体集合
     * @throws Exception
     */
    public List<FactoryEntity> getFactoryList(String[] factoryCodes) throws Exception {
        List<FactoryEntity> factoryEntityList = new ArrayList<>();
        String factoryUrl = String.format("%s/bizOrgMains/%s/bizOrgDTLs", fmBaseUrl, bizCode);
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("inUse", Integer.toString(CommonEnum.InUseEnum.Yes.getIndex())));
        list.add(new BasicNameValuePair("orgTypeCode", factoryTypeCode));
        if (factoryCodes != null && factoryCodes.length != 0) {
            list.add(new BasicNameValuePair("$orgCodes", String.join(",", factoryCodes)));
        }
        //发送请求
        String factoryCollection = MicroServiceClient.getRequest(factoryUrl, list);
        List<FactoryVO> factoryVOList = RestfulTool.toResourceRepList(factoryCollection, FactoryVO.class);
        //实体转换
        if (factoryVOList != null)
            factoryEntityList = ObjectConverter.listConverter(factoryVOList, FactoryEntity.class);
        return factoryEntityList;
    }

    /**
     * 处理装置权限相关的逻辑
     *
     * @param params          请求参数集合
     * @param unitCodes       装置编码集合
     * @param enablePrivilege 是否启用装置权限
     * @return true：继续发送请求，false ：返回空集合
     * @throws Exception
     * <AUTHOR>
     */
    private Boolean handlerAuth(List<NameValuePair> params, String[] unitCodes, Boolean enablePrivilege) throws Exception {
        List<String> unitCodesWithAuth;
        //获取用户有权限的装置编码
        if (enablePrivilege) {
            CommonProperty cp = new CommonProperty();
            List<AAAPropertyValueEntity> pvList = aaaService.getAuthPropertyValueList(cp.getUserId());
            unitCodesWithAuth = pvList.stream().map(pv -> pv.getValue()).collect(Collectors.toList());
            if (unitCodes != null && unitCodes.length != 0) {
                unitCodesWithAuth.retainAll(CollectionUtils.arrayToList(unitCodes));
            }
            if (CollectionUtils.isEmpty(unitCodesWithAuth)) { //没有权限，直接返回
                return false;
            }
            params.add(new BasicNameValuePair("$areaCodes", String.join(",", unitCodesWithAuth)));
        } else {
            if (unitCodes != null && unitCodes.length != 0) {
                params.add(new BasicNameValuePair("$areaCodes", String.join(",", unitCodes)));
            }
        }
        return true;
    }

    /**
     * 转换VO到实体
     *
     * @param unitVOList
     * @return 装置集合
     * @throws Exception
     */
    private List<UnitEntity> convertToEntity(List<UnitVO> unitVOList) throws Exception {
        List<UnitEntity> entities = new ArrayList<>();
        for (UnitVO vo : unitVOList) {
            UnitEntity entity = new UnitEntity();
            entity.setName(vo.getPlantName());
            entity.setStdCode(vo.getPlantCode());
            entity.setSname(vo.getPlantAlias());
            entity.setSortNum(vo.getSortNum());
            entity.setDes(vo.getDes());
            entity.setWorkshopCode(vo.getOrgCode());
            entity.setWorkshopName(vo.getOrgAlias());
            entities.add(entity);
        }
        return entities;
    }
}
