var pmUrl = OPAL.API.pmUrl;
var addUrl = pmUrl + '/measUnits';
var searchUrl = pmUrl + '/measUnits';
var getSingleUrl = pmUrl + '/measUnits';
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.None;
$(function () {
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
        },
        bindUI: function () {
            $('#saveAddModal').click(function () {
                page.logic.save();
            });
            $('.closeBtn').click(function () {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer(false);
            })
            $('#closePage').click(function () {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer(false);
            })
        },
        logic: {
            save: function () {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data = OPAL.form.getETCollectionData('AddOrEditModal');
                //处理提交类型
                var ajaxType = 'POST';
                if (pageMode == PageModelEnum.NewAdd) {
                    window.pageLoadMode = PageLoadMode.Reload;
                } else if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                    window.pageLoadMode = PageLoadMode.Refresh;
                }

                $.ajax({
                    url: addUrl,
                    type: ajaxType,
                    async: true,
                    data: JSON.stringify(data),
                    dataType: "text",
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！",{
                                time: 1000
                            },function() {
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                $("#pageTitle").text(data.title);
                if (pageMode == PageModelEnum.NewAdd) {
                    $('input[name=inUse]').attr('disabled', 'disabled');
                    return;
                }
                $.ajax({
                    url: getSingleUrl + "/" + data.measUnitId + "?now=" + Math.random(),
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function (data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        OPAL.form.setData('AddOrEditModal', entity);
                    },
                    error: function (XMLHttpRequest, textStatus) {
                        layer.msg('网络错误')
                    }
                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            /**
             * 表单验证
             */
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal',{
                    rules: {
                        name: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        sign: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        sortNum: {
                            required: true,
                            digits:true,
                            min: 0
                        }
                    }
                })
            }
        }
    }
    page.init();
    window.page = page;

})