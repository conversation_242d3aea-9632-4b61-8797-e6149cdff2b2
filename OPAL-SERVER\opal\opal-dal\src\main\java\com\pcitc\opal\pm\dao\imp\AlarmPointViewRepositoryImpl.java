package com.pcitc.opal.pm.dao.imp;

import java.util.HashMap;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.apache.commons.lang3.StringUtils;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmPointViewRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmPointView;

/*
 * ChangeTagList实体的Repository的JPA接口实现
 * 模块编号： pcitc_opal_dal_class_ChangeTagListRepositoryImpl
 * 作       者：dageng.sun
 * 创建时间：2018/1/22
 * 修改编号：1
 * 描       述：ChangeTagList实体的Repository的JPA接口实现
 */
public class AlarmPointViewRepositoryImpl extends BaseRepository<AlarmPointView, Long> implements AlarmPointViewRepositoryCustom {

	@PersistenceContext
    private EntityManager entityManager;
	

	/**
	 * 位号查询
	 * 
	 * <AUTHOR> 2018-01-23 
	 * @param unitCode 装置编码
	 * @param tag 位号
	 * @param page 分页对象
	 * @return PaginationBean<AlarmPointView> 返回AlarmPointView实体分页对象
	 */
	@Override
	public PaginationBean<AlarmPointView> getAlarmPointView(String unitCode, String tag, Pagination page) {
		try {
            // 查询字符串
            String statisticsHql = "select apv ";
            StringBuilder hql = new StringBuilder(" from AlarmPointView apv"
                    + " inner join apv.prdtCell pc"
                    + " inner join apv.alarmFlag af");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 装置
            hql.append(" where pc.unitId = :unitId");
            paramList.put("unitId", unitCode);
            // 位号
            if (!StringUtils.isEmpty(tag)) {
            	hql.append("  and (upper(apv.tag) like upper(:tag) escape '/') ");
                paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
            }
            String groupByhql = " order by apv.tag asc,af.name asc";
            return this.findAll(page, statisticsHql + hql.toString() + groupByhql, paramList);
        } catch (Exception e) {
            throw e;
        }
	}
	
}
