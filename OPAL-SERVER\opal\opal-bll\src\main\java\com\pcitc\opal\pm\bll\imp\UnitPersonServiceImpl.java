package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.entity.FactoryEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.common.bll.entity.WorkshopEntity;
import com.pcitc.opal.common.bll.vo.AreaVO;
import com.pcitc.opal.common.bll.vo.FactoryVO;
import com.pcitc.opal.common.bll.vo.UnitVO;
import com.pcitc.opal.common.bll.vo.WorkShopVO;
import com.pcitc.opal.pm.bll.UnitPersonService;
import com.pcitc.opal.pm.bll.entity.UnitPersonEntity;
import com.pcitc.opal.pm.dao.UnitPersonRepository;
import com.pcitc.opal.pm.pojo.UnitPerson;
import org.apache.commons.lang.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;
import pcitc.imp.common.ettool.utils.RestfulTool;

import java.lang.ref.SoftReference;
import java.util.*;

/**
 * 未分类数据查询业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_Impl_UnitPersonServiceImpl
 * 作       者：zhipeng.guo
 * 创建时间：2018-12-17
 * 修改编号：1
 * 描       述：操作报警装置维护实现
 */

@Service
public class UnitPersonServiceImpl implements UnitPersonService {

    @Autowired
    UnitPersonRepository unitPersonRepository;

    @Value("${factorymodel.base.url}")
    private String fmUrl;
    @Value("${factorymodel.bizCode}")
    private String bizCode;
    @Value("${factorymodel.workshopTypeCode}")
    private String workshopTypeCode;

    /**
     * promence接口查询所有结果缓存
     * 在新增编辑后置空
     */
    private SoftReference<List<UnitEntity>> listCache=new SoftReference<>(null);


    /**
     * 定时任务  每1分钟一次自动清空缓存
     */
    @Scheduled(cron ="0 */1 * * * ? " )
    private  void clearListCache(){
        listCache.clear();
    }

    /**
     * 调用promence接口查询封装装置的名称简称
     * @param unitPerson  暂时只用到装置编码
     * @param name   装置名称
     * @param workShopName 车间名称
     * <AUTHOR>
     * @return
     */
    private List<UnitPersonEntity> getAll(UnitPerson unitPerson,String  name,String workShopName) throws Exception {

        if(unitPerson!=null&&"".equals(unitPerson.getUnitId())){
            unitPerson.setUnitId(null);
        }
        PageRequest pageRequest=PageRequest.of(0, Integer.MAX_VALUE);
        //人员关联表数据库查询结果
        ExampleMatcher matcher= ExampleMatcher.matching()
                .withIgnoreCase() //忽略大小写匹配
                //where ... like ?%
                .withMatcher("unitId",
                        ExampleMatcher.GenericPropertyMatchers.contains())
                .withIgnoreNullValues();
        Example<UnitPerson> example=Example.of(unitPerson, matcher);
        List<UnitPerson> content = unitPersonRepository.findAll(example,pageRequest).getContent();

        //unitPerson表数据转换结果
        List<UnitPersonEntity> DBlist = ObjectConverter.listConverter(content, UnitPersonEntity.class);
        //封装最终筛选结果
        List<UnitPersonEntity> result=new LinkedList<>();
       //调用远程接口  查询出所有装置信息
        List<UnitEntity> unitNameList = getUnitById(null);
        //保存装置信息筛选结果
        List<UnitEntity> unitNameListResult=new ArrayList<>();
        //先筛选掉装置信息中名称不符合的
        if(name!=null){
            if(workShopName!=null){
                unitNameList.forEach(e->{
                    if(e.getWorkshopSname().contains(workShopName)&&( e.getName().contains(name)||e.getSname().contains(name))){
                        unitNameListResult.add(e);
                    }
                });
            }else {
                unitNameList.forEach(e->{
                    if( e.getName().contains(name)||e.getSname().contains(name)){
                        unitNameListResult.add(e);
                    }
                });
            }
        }else{
            if(workShopName!=null){
                unitNameList.forEach(e->{
                    if(e.getWorkshopSname().contains(workShopName)){
                        unitNameListResult.add(e);
                    }
                });
            }else {
                unitNameListResult.addAll(unitNameList);
            }
        }
        int unitNameListResultSize = unitNameListResult.size();
        //对比两组数据的code
        DBlist.forEach(e->{
            String unitId = e.getUnitId();
            for(int i=0;i<unitNameListResultSize;i++){
                UnitEntity entity = unitNameListResult.get(i);
                if(unitId.equals(entity.getStdCode())){
                    //数据匹配  赋值 装进result
                    setValue(e,entity);
                    result.add(e);
                    break;
                }
            }
        });
        return result;
    }
    /**
     * 调用promence接口查询封装装置的名称简称 车间简称   按车间名称   名称  简称排序
     * @param unitPerson  暂时只用到装置编码
     * @param UnitName   装置名称
     * @param workShopName  车间名称
     * <AUTHOR>
     * @return
     */
    @Override
    public List<UnitPersonEntity> query(UnitPerson unitPerson, String UnitName, String workShopName) throws Exception {

        List<UnitPersonEntity> result = getAll(unitPerson, UnitName,workShopName);
        //字符串排序规则
        Comparator<String> stringComparator = ComparatorList.orderByASC();
        //排序规则
        Comparator <UnitPersonEntity>comparator= new Comparator<UnitPersonEntity>() {
            @Override
            public int compare(UnitPersonEntity u1, UnitPersonEntity u2) {
                if(u1==null){
                    return -1;
                }else if(u2==null){
                    return 1;
                }
                int workShopCom = stringComparator.compare(u1.getWorkShopSname(), u2.getWorkShopSname());
                if(workShopCom!=0){
                    return workShopCom;
                }
                int nameCom = stringComparator.compare(u1.getName(), u2.getName());
                if(nameCom!=0){
                    return nameCom;
                }
                return stringComparator.compare(u1.getSName(), u2.getSName());
            }
        };
        //排序
        result.sort(comparator);

        return result;
    }

    /**
     * 调用promence分页接口查询封装装置的名称简称 车间简称
     * @param unitPerson  暂时只用到装置编码
     * @param UnitName   装置名称
     * @param workShopName  车间名称
     * @param pageNumber 当前页  从1开始
     * @param pageSize 数据量
     * <AUTHOR>
     * @return
     */
    @Override
    public PaginationBean<UnitPersonEntity> getInPage(UnitPerson unitPerson, String UnitName, String workShopName, Integer pageNumber, Integer pageSize) throws Exception {
        List<UnitPersonEntity> query = query(unitPerson, UnitName, workShopName);
        pageNumber=pageNumber==null||pageNumber<1?1:pageNumber;
        pageSize=pageSize==null||pageSize<1?10:pageSize;
        //左闭右开
        int beginIndex=pageNumber*pageSize-pageSize;
        int endIndex=beginIndex+pageSize;
        endIndex=endIndex>=query.size()?query.size():endIndex;

        List<UnitPersonEntity>result=new ArrayList<>(pageSize);
        for(int i=beginIndex;i<endIndex;i++){
            result.add(query.get(i));
        }
        PaginationBean <UnitPersonEntity>paginationBean=new PaginationBean<UnitPersonEntity>();
        paginationBean.setPageList(result);
        //页码从1开始
        paginationBean.setPageNumber(pageNumber);
        paginationBean.setPageSize(pageSize);
        paginationBean.setTotal(query.size());
        paginationBean.setTotalPage(query.size() == 0 ? 1 : (int) Math.ceil((double) query.size() / (double) pageSize));
        return paginationBean;
    }

    /**
     * 将source中的部分值赋值给target
     * <AUTHOR>
     */
    private void setValue(UnitPersonEntity target,UnitEntity source){
        target.setName(source.getName());
        target.setsName(source.getSname());
        target.setWorkShopName(source.getWorkshopName());
        target.setWorkShopSname(source.getWorkshopSname());
    }
    /**
     * 调用远程接口  根据装置编码获得装置信息
     * @param unitCodes 装置编码
     * @return
     * @throws Exception
     */
    private List<UnitEntity> getUnitById(String unitCodes)throws Exception{
        //当要的是所有的装置信息时,有缓存返回缓存
        if(unitCodes==null&&listCache.get()!=null){
            synchronized (listCache){
                return listCache.get();
            }
        }
        List<UnitEntity> unitEntityList = new ArrayList<>();
        List<UnitVO> unitVOList = getUnitVO(unitCodes);
        //实体转换
        if (unitVOList != null) {
            for (UnitVO vo : unitVOList) {
                UnitEntity entity = new UnitEntity();
                CommonUtil.objectExchange(vo, entity);
                entity.setStdCode(vo.getPlantCode());
                entity.setWorkshopName(vo.getOrgName());
                entity.setWorkshopCode(vo.getOrgCode());
                entity.setWorkshopSname(vo.getOrgAlias());
                entity.setName(vo.getPlantName());
                entity.setSname(vo.getPlantAlias());
                unitEntityList.add(entity);
            }
        }

        if(unitEntityList.size()<1){
            return null;
        }
        //只缓存所有的装置信息
        if(unitCodes==null){
            synchronized (listCache){
                listCache=new SoftReference<>(unitEntityList);
            }
        }
        return unitEntityList;
    }

    /**
     * 根据装置编码获得装置相关信息
     * @param unitCodes 装置编码
     * @return
     * @throws Exception
     */
    private List<UnitVO> getUnitVO(String unitCodes)throws Exception{
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("inUse", Integer.toString(CommonEnum.InUseEnum.Yes.getIndex())));
        //处理装置权限

//        if (page != null) {
//            list.add(new BasicNameValuePair("$skip", String.valueOf((page.getPageNumber() - 1) * page.getPageSize())));
//            list.add(new BasicNameValuePair("$top", String.valueOf(page.getPageSize())));
//        }
//        if (org.apache.commons.lang3.StringUtils.isNotEmpty(plantName)) {
//            list.add(new BasicNameValuePair("plantName", plantName));
//            list.add(new BasicNameValuePair("plantAlias", plantName));
//        }
        //list.add(new BasicNameValuePair("$orgName", "测试金陵热电"));
        if(unitCodes!=null){
            list.add(new BasicNameValuePair("$areaCodes", unitCodes));
        }
        //发送请求
        String unitCollection = MicroServiceClient.getRequest(fmUrl + "/plants", list);
        List<UnitVO> result;
        try{
            //只读容器
            result= Collections.unmodifiableList(RestfulTool.toResourceRepList(unitCollection, UnitVO.class));
        }catch (Exception e){
            result=new ArrayList<>();
        }
        return result;
    }

    /**
     * 新增
     * @param unitPerson  包含人数和code
     * @return  校验结果或者保存成功
     * <AUTHOR>
     */
    @Override
    public CommonResult addUnitPerson(UnitPerson unitPerson) throws Exception {
        CommonResult cm=new CommonResult();
        cm.setIsSuccess(true);
        cm.setMessage("保存成功");
        CommonResult commonResult = unitPersonRepository.unitValidation(unitPerson);
        if(!commonResult.getIsSuccess()) throw new Exception(commonResult.getMessage());
        // 赋值 创建人、创建名称、创建时间
        CommonUtil.returnValue(unitPerson, CommonEnum.PageModelEnum.NewAdd.getIndex());
        unitPerson.setInUse(1);
        unitPerson.setUnitPersonId(null);
        unitPersonRepository.save(unitPerson);
        //清空缓存
        synchronized (listCache){
            listCache.clear();
        }
        return cm;
    }

    /**
     * 编辑装置人员信息
     * @param unitPerson  需要编辑的对象
     * <AUTHOR>
     * @throws Exception
     */
    @Override
    public CommonResult edditUnitPerson(UnitPerson unitPerson) throws Exception {
        CommonResult cm=new CommonResult();
        CommonResult commonResult = unitPersonRepository.unitValidation(unitPerson);
        if(!commonResult.getIsSuccess()) throw new Exception(commonResult.getMessage());

        UnitPerson one = unitPersonRepository.findById(unitPerson.getUnitPersonId()).orElse(null);

        // 赋值 修改人等
        CommonUtil.returnValue(unitPerson, CommonEnum.PageModelEnum.Edit.getIndex());
        if(one!=null){
            unitPerson.setCrtDate(one.getCrtDate());
            unitPerson.setCrtUserId(one.getCrtUserId());
            unitPerson.setCrtUserName(one.getCrtUserName());
            unitPerson.setInUse(one.getInUse());
            unitPersonRepository.save(unitPerson);
            cm.setIsSuccess(true);
            cm.setMessage("保存成功");
            //清空缓存
            synchronized (listCache){
                listCache.clear();
            }
        }else {
            cm.setIsSuccess(false);
            cm.setMessage("错误的ID");
        }
        return cm;
    }
    /**
     * 根据车间编码获取车间编码下的装置，调用promence
     * @param workShopCode  车间编码
     * <AUTHOR>
     * @throws Exception
     */
    @Override
    public List<UnitEntity> getUnitListByWorkshopIds(String workShopCode) throws Exception {
        String [] workshopCodes={workShopCode};
        List<UnitEntity> unitEntityList = new ArrayList<>();
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("inUse", Integer.toString(CommonEnum.InUseEnum.Yes.getIndex())));

        if (workshopCodes != null && workshopCodes.length > 0) {
            list.add(new BasicNameValuePair("$orgCodes", String.join(",", workshopCodes)));
        }
        //限制区域类型为装置
        list.add(new BasicNameValuePair("areaTypeCode","plants"));
        //发送请求
        String unitCollection = MicroServiceClient.getRequest(fmUrl + "/areaDictionaries", list);
        List<AreaVO> unitVOList = RestfulTool.toResourceRepList(unitCollection, AreaVO.class);
        //实体转换
        if (unitVOList != null) {
            unitVOList.forEach(e->{
                UnitEntity entity=new UnitEntity();
                entity.setStdCode(e.getAreaCode());
                entity.setName(e.getAreaName());
                entity.setSname(e.getAreaAlias());
                unitEntityList.add(entity);
            });
        }
        return unitEntityList;
    }
    /**
     * 获取工厂集合调用promence
     * <AUTHOR>
     * @throws Exception
     */
    @Override
    public List<FactoryEntity> getFactoryList() throws Exception {
        List<FactoryEntity> factoryEntityList = new ArrayList<>();
        String factoryTypeCode="1005";
        String orderBy="";
        String factoryUrl = String.format("%s/bizOrgMains/%s/bizOrgDTLs", fmUrl, bizCode);
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("inUse", Integer.toString(1)));
        list.add(new BasicNameValuePair("orgTypeCode",factoryTypeCode));

        if (StringUtils.isNotEmpty(orderBy)) {
            list.add(new BasicNameValuePair("$orderby", orderBy));
        }else {
            list.add(new BasicNameValuePair("$orderby","sortNum asc,orgAlias asc"));
        }
        //发送请求
        String factoryCollection = MicroServiceClient.getRequest(factoryUrl, list);
        List<FactoryVO> factoryVOList = RestfulTool.toResourceRepList(factoryCollection, FactoryVO.class);


        //实体转换
        List<FactoryEntity> result=new ArrayList();
        factoryVOList.forEach(e->{
            FactoryEntity entity=new FactoryEntity();
            entity.setName(e.getOrgName());
            entity.setSname(e.getOrgAlias());
            entity.setStdCode(e.getOrgCode());
            result.add(entity);
        });

        return result;
    }
    /**
     * 根据工厂编码获取车间列表调用promence
     * <AUTHOR>
     * @throws Exception
     */
    @Override
    public List<WorkshopEntity> getWorkShop(String...factoryCodes) throws Exception {

        List<WorkshopEntity> workShopEntityList = new ArrayList<>();
        List<UnitEntity> unitEntities = new ArrayList<>();
        String orderBy=null;
        String workshopUrl = String.format("%s/bizOrgMains/%s/bizOrgDTLs", fmUrl, bizCode);
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("inUse", Integer.toString(1)));
        list.add(new BasicNameValuePair("orgTypeCode", workshopTypeCode));
        if (factoryCodes != null && factoryCodes.length != 0) {
            list.add(new BasicNameValuePair("$orgCodes", String.join(",", factoryCodes)));
            list.add(new BasicNameValuePair("$operType", "children"));
        }
//        if (StringUtils.isNotEmpty(orderBy)) {
//            list.add(new BasicNameValuePair("$orderby", orderBy));
//        }else {
        list.add(new BasicNameValuePair("$orderby","sortNum asc,orgAlias asc"));
//        }
        //发送请求
        String factoryCollection = MicroServiceClient.getRequest(workshopUrl, list);
        List<WorkShopVO> workShopVOList = RestfulTool.toResourceRepList(factoryCollection, WorkShopVO.class);

        //实体转换
        if (workShopVOList != null) {
            for (WorkShopVO vo : workShopVOList) {
                WorkshopEntity entity = new WorkshopEntity();
                CommonUtil.objectExchange(vo, entity);
                entity.setWorkShopCode(vo.getOrgCode());
                entity.setName(vo.getOrgName());
                entity.setSname(vo.getOrgAlias());
                workShopEntityList.add(entity);
            }
        }
        return workShopEntityList;
    }
    /**
     * 根据ID物理删除人员装置表中的数据
     * @param unitPersonId  人员装置表ID
     * <AUTHOR>
     * @throws Exception
     */
    @Override
    public CommonResult deleteUnitPerson(Long...unitPersonId) throws Exception {
        CommonResult cm=new CommonResult();
        for(Long id:unitPersonId){
            UnitPerson one = unitPersonRepository.findById(id).orElse(null);
            if(one!=null){
                unitPersonRepository.deleteById(id);
                cm.setIsSuccess(true);
                cm.setMessage("删除成功");
            }
        }
        return cm;
    }
    /**
     * 获取单个装置人员信息  调用远程接口
     * @param unitPersonId   装置人员ID
     * <AUTHOR>
     * @throws Exception
     */
    @Override
    public UnitPersonEntity getSingle(Long unitPersonId) throws Exception {
        UnitPerson one = unitPersonRepository.findById(unitPersonId).orElse(null);
        UnitEntity unitEntity = getUnitById(one.getUnitId()).get(0);
        if(unitEntity==null){
            unitEntity=new UnitEntity();
        };
        UnitPersonEntity unitPersonEntity = ObjectConverter.entityConverter(one, UnitPersonEntity.class);
        unitPersonEntity.setName(unitEntity.getName());
        unitPersonEntity.setsName(unitEntity.getSname());
        unitPersonEntity.setWorkShopName(unitEntity.getWorkshopName());
        unitPersonEntity.setWorkShopCode(unitEntity.getWorkshopCode());
        List<WorkshopEntity> workshopListByWorkshopIds = getWorkshopListByWorkshopIds(unitEntity.getWorkshopCode());
        if(workshopListByWorkshopIds.size()>0){
            WorkshopEntity workshopEntity = workshopListByWorkshopIds.get(0);
            unitPersonEntity.setFactoryCode(workshopEntity.getFactoryCode());
        }
        return unitPersonEntity;
    }
    /**
     * 根据车间编码集合获取车间列表
     * @param workshopCodes 车间编码
     * @return 车间集合
     * @throws Exception
     * <AUTHOR> 2017-11-7
     */
    private List<WorkshopEntity> getWorkshopListByWorkshopIds(String...workshopCodes) throws Exception {
        List<WorkshopEntity> workshopEntityList = new ArrayList<>();
        String workshopUrl = String.format("%s/bizOrgMains/%s/bizOrgDTLs", fmUrl, bizCode);
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("inUse", Integer.toString(CommonEnum.InUseEnum.Yes.getIndex())));
        list.add(new BasicNameValuePair("orgTypeCode", workshopTypeCode));
        if (workshopCodes != null && workshopCodes.length != 0) {
            list.add(new BasicNameValuePair("$orgCodes", String.join(",", workshopCodes)));
        }
        //发送请求
        String factoryCollection = MicroServiceClient.getRequest(workshopUrl, list);
        List<WorkShopVO> workShopVOList = RestfulTool.toResourceRepList(factoryCollection, WorkShopVO.class);

        //实体转换
        if (workShopVOList != null) {
            for (WorkShopVO vo : workShopVOList) {
                WorkshopEntity entity = new WorkshopEntity();
                entity.setStdCode(vo.getOrgCode());
                entity.setName(vo.getOrgName());
                entity.setSname(vo.getOrgAlias());
                entity.setDes(vo.getDes());
                entity.setSortNum(vo.getSortNum());
                entity.setFactorySname(vo.getParentOrgAlias());
                entity.setFactoryCode(vo.getParentOrgCode());
                workshopEntityList.add(entity);
            }
        }
        return workshopEntityList;
    }



}
