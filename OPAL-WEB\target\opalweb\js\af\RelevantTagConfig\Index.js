var relevantTagConfigUrl = OPAL.API.afUrl+ '/relevantTagConfig';
var UnitPrdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var isRefresh = false;
window.pageLoadMode = PageLoadMode.None;
var RelevantTagBusinessType;
var isLoading = true;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            RelevantTagBusinessType = page.logic.getQueryParam('RelevantTagBusinessType');
            if (RelevantTagBusinessType != 2) {
                RelevantTagBusinessType = 1;
                $('#pageTitle').text('相关位号配置');
                document.title = '相关位号配置';
            }
            if(RelevantTagBusinessType == 2) {
                $('#pageTitle').text('相关位号分析');
                document.title = '相关位号分析';
                $("#addBtn").hide();
                $("#delBtn").hide();
            }
            //绑定事件
            this.bindUI();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化表格
            page.logic.initTable();

            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("RelevantTagConfig");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
            page.logic.search();
        },
        /**
         * 绑定事件
         */
        bindUI: function () {
            // 新增
            $('#addBtn').click(function () {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            })
            //批量删除
            $('#delBtn').click(function () {
                page.logic.delAll();
            })
            //查询
            $('#searchBtn').click(function () {
                isLoading = false;
                page.logic.search();
            })
        },
        data: {
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化表格
             */
            initTable: function () {
                OPAL.ui.initBootstrapTable("table",{
                    cache:false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '130px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "序号",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'center',
                        width: '120px',
                        formatter: function (value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1) * pageSize;
                        },
                    }, {
                        title: "主位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                        width: '180px'
                    }, {
                        title: "装置",
                        field: 'unitSname',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellSname',
                        rowspan: 1,
                        align: 'center',
                        width: '70px'
                    }, {
                        title: "创建时间",
                        field: 'crtDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "创建人",
                        field: 'crtUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "维护时间",
                        field: 'mntDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "维护人",
                        field: 'mntUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }]
                },page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param,param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                if(RelevantTagBusinessType == 1) {
                   return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.edit(\'' + rowData.relevantTagConfigId + '\')">编辑</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.relevantTagConfigId + '\')" >删除</a> '
                    ] 
                }
                if(RelevantTagBusinessType == 2) {
                    return [
                    '<a  name="TableView"  href="javascript:window.page.logic.analysis(\'' + rowData.relevantTagConfigId + '\',\'' + rowData.alarmPointId + '\',\'' + rowData.tag + '\')" >分析</a>'
                    ]
                }
            },
            /**
             * 搜索
             */
            search: function () {
                page.data.param = OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": relevantTagConfigUrl,
                    "pageNumber":1
                });
            },
            /**
             * 批量删除
             */
            delAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections');
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.relevantTagConfigId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: relevantTagConfigUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function (id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: relevantTagConfigUrl,
                        async: false,
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 新增
             */
            add: function () {
                var pageMode = PageModelEnum.NewAdd;
                var title = "相关性位号配置新增";
                page.logic.detail(title, "", pageMode);
            },
            /**
             * 编辑
             * @param prdtCellId
             */
            edit: function (relevantTagConfigId) {
                var pageMode = PageModelEnum.Edit;
                var title = "相关性位号配置编辑";
                page.logic.detail(title, relevantTagConfigId, pageMode);
            },
            /**
             * 装置新增或者编辑详细页面
             */
            detail: function (title, relevantTagConfigId, pageMode) {
                layer.open({
                    type: 2,
                    title: title,
                    closeBtn: 1,
                    area: ['900px', '380px'],
                    shadeClose: false,
                    content: 'RelevantTagConfigAddOrEdit.html?'+ Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "relevantTagConfigId": relevantTagConfigId,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            analysis:function(relevantTagConfigId, alarmPointId, tag) {
                layer.open({
                    type: 2,
                    title: '相关性报警分析',
                    closeBtn: 1,
                    area: ['100%', '100%'],
                    shadeClose: false,
                    content: 'RelevantAlarmAnalysis.html?'+ Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "relevantTagConfigId": relevantTagConfigId,
                            "alarmPointId": alarmPointId,
                            "tag": tag
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect("unitIds", commonUnitTreeUrl, "id", "parentId", "sname", {
                    data: {
                        'enablePrivilege': true,
                    },
                    onChange: function (node) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds");
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }

                    }
                }, false, function () {

                });
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function (unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', UnitPrdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },
            /**
             * 获取页面URL参数
             * @param  {name}
             */
            getQueryParam: function (name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
                var r = window.location.search.substr(1).match(reg); //匹配目标参数
                if (r != null) return unescape(r[2]);
                return null; //返回参数值
            },
        }
    }
    page.init();
    window.page = page;
})