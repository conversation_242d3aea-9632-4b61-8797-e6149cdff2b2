package com.pcitc.opal.pm.dao.imp;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

/*
 * 变更事件查询工艺变更单条件实体
 * 模块编号：pcitc_pojo_ChangeEventCondition
 * 作       者：xuelei.wang
 * 创建时间：2017/09/30
 * 修改编号：1
 * 描       述：变更事件查询工艺变更单条件实体
 */
public class DataServerMonDTO {

    public DataServerMonDTO() {
    }
    public DataServerMonDTO(String sname,
                            String unitCode,
                            Long dcsCodeId,
                            Long opcCodeId,
                            Date activeTime) {
        this.sname = sname;
        this.unitCode = unitCode;
        this.dcsCodeId = dcsCodeId;
        this.opcCodeId = opcCodeId;
        this.activeTime = activeTime;
    }


    /**
     *装置简称
     */
    private String sname;

    /**
     *装置编码
     */
    private String unitCode;

    /**
     * dcs_code
     */
    private Long dcsCodeId;

    /**
     * opc_code
     */
    private Long opcCodeId;

    /**
     *执行时间s
     */
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+08:00")
    private Date activeTime;

    public String getsName() {
        return sname;
    }

    public void setsName(String sname) {
        this.sname = sname;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Long getDcsCodeId() {
        return dcsCodeId;
    }

    public void setDcsCodeId(Long dcsCodeId) {
        this.dcsCodeId = dcsCodeId;
    }

    public Long getOpcCodeId() {
        return opcCodeId;
    }

    public void setOpcCodeId(Long opcCodeId) {
        this.opcCodeId = opcCodeId;
    }

    public Date getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(Date activeTime) {
        this.activeTime = activeTime;
    }
}
