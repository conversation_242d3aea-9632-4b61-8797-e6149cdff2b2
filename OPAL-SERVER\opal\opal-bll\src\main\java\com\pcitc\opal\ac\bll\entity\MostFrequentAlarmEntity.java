package com.pcitc.opal.ac.bll.entity;

import java.util.Date;

/*
 * 最频繁的报警实体
 * 模块编号：pcitc_opal_bll_class_MostFrequentAlarmEntity
 * 作       者：dageng.sun
 * 创建时间：2018/01/22
 * 修改编号：1
 * 描       述：最频繁的报警实体
 */
public class MostFrequentAlarmEntity {
	
	/**
     * 报警点位号
     */
    private String tag;
    
    /**
     * 报警标识名称
     */
    private String alarmFlagName;
	
    /**
     * 生产单元名称
     */
    private String prdtCellName;
    
    /**
	 * 报警数
	 */
	private Long alarmCount;
	
	/**
	 * 是否可以变更
	 */
	private boolean changeStatus;
	
	/**
	 * 开始时间
	 */
	private Date startTime;
	
	/**
	 * 结束时间
	 */
	private Date endTime;
	
	/**
     *报警点ID
     */
    private Long alarmPointId;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public String getAlarmFlagName() {
		return alarmFlagName;
	}

	public void setAlarmFlagName(String alarmFlagName) {
		this.alarmFlagName = alarmFlagName;
	}

	public String getPrdtCellName() {
		return prdtCellName;
	}

	public void setPrdtCellName(String prdtCellName) {
		this.prdtCellName = prdtCellName;
	}

	public Long getAlarmCount() {
		return alarmCount;
	}

	public void setAlarmCount(Long alarmCount) {
		this.alarmCount = alarmCount;
	}

	public boolean getChangeStatus() {
		return changeStatus;
	}

	public void setChangeStatus(boolean changeStatus) {
		this.changeStatus = changeStatus;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Long getAlarmPointId() {
		return alarmPointId;
	}

	public void setAlarmPointId(Long alarmPointId) {
		this.alarmPointId = alarmPointId;
	}

	public Long getAlarmFlagId() {
		return alarmFlagId;
	}

	public void setAlarmFlagId(Long alarmFlagId) {
		this.alarmFlagId = alarmFlagId;
	}

}
