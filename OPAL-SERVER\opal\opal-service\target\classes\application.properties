﻿#web服务端口
server.port=8068
server.servlet.context-path=/opalservice
server.tomcat.max-swallow-size=100MB
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.serialization.write-dates-as-timestamps=true
#spring boot 设置文件上传最大限制
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
#spring.jpa
spring.jpa.show-sql=true
#mi

##在建表的时候，将默认的存储引擎切换为 InnoDB 用的
spring.jpa.database=MYSQL
spring.jpa.database-platform=com.pcitc.opal.oracle.dialect.CustomMysqlDialect
spring.jpa.open-in-view=true
spring.jpa.properties.hibernate.proc.param_null_passing=true
spring.jpa.hibernate.show_sql=true
spring.jpa.hibernate.jdbc.batch_size=30
spring.jpa.hibernate.cache.use_second_level_cache=false
hibernate.proc.param_null_passing=true

mybatis.mapper-locations=classpath:mapper/*.xml

spring.datasource.url=************************************************************************************************************************************************************************************
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.username=ENC(Qz1FpdH8oKcBGIpWVQ2oHL3hK/r8+iRk)
#spring.datasource.password=ENC(4ZYIk0I4t2PUDZbwUF5eVOzearfH033W)
spring.datasource.username=root
spring.datasource.password=Mes_opal123
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=50
jasypt.encryptor.password=mesopal
spring.cache.type=redis
# 缓存过期时间（毫秒）
spring.cache.redis.time-to-live=180000
#-----redis相关配置
# Redis数据库索引（默认为0）
spring.redis.database=0
# Redis服务器地址
spring.redis.host=*************
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=mes_opal
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=20
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=5
# 连接超时时间（毫秒）
spring.redis.timeout=10000

#应用配置
imp.appcode=demo
#平台服务地址配置
imp.bizlogsvc_address_base=**************:30009/bizlog
#AAA使用版本   old:老版本(镇海),new:新版本,promace:promace
aaa_version=promace
#aaa_version=old/new时  AAA 组织机构和用户服务访问地址资源服务访问地址
aaa.aaa_address_base=http://*************:8080/IP/AAA/LoginService
#aaa_version=PORMACE时 AAA组织机构和用户服务访问地址
aaa.organduser.url=http://**************:8804/tenants/aaa.platform
#aaa_version=PROMACE时 AAA 资源服务访问地址
aaa.resource.url=http://**************:8804/tenants/aaa.platform/ResourceService
#aaa_version=PROMACE时 装置属性
aaa.appCode=aaa.platform_SJZ_CZBJ
aaa.resource.opalunitProperty=aaa.platform_SJZ_CZBJ_UNIT
#aaa_version=PROMACE时 企业属性
aaa.appcompany=aaa.platform_SJZ_CZBJ
aaa.resource.opalCompanyProperty=aaa.platform_SJZ_CZBJ_COMPANY
#运行环境类型promace,other
runtime_type=other
#runtime_type=other时 AAA 班组WebService地址(后缀不用带"/")
aaa.shiftcalendarsvc_address_base=http://**************:8080
#runtime_type=PROMACE时  PORMACE 班组
promace.imp.shift.base.url=http://shift.wsm.qlsh.promace.sinopec.com
fm_unit_type_code=plants
#runtime_type=PROMACE时  工厂模型-根Url
factorymodel.base.url=http://pm.wsm.qlsh.promace.sinopec.com
factorymodel.bizCode=qlsh
factorymodel.factoryTypeCode=1005
factorymodel.workshopTypeCode=1007
#工艺系统审批地址
tec.audit.url=http://************:8080/IP/WebService/OpeAlarmCraftIndexService.asmx

craft.mon.trendChart.url = http://*************:8080/CTM/WebAPI/TJNGLargeDisplay/QueryOpeMonTrendChart

#附件上传浏览器地址
alfresco_browser_url=http://**************:28080/alfresco/api/-default-/public/cmis/versions/1.1/browser
#附件上传工具用户名
alfresco_username=admin
#附件上传工具密码
alfresco_password=123456
#附件上传工具目录名
alfresco_folder_name=OPAL
#附件上传工具文件大小限制,如果不配置此值，或者value配置为空，则认为不限制上传文件大小
alfresco_file_size=50
#项目启动时是否扫描剔除
isScanDel=false
# 分页缓存过期时间
cache.timeout=3