package com.pcitc.opal.ad.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.pm.pojo.TagExtraMessage;

/*
 * TagExtraMessage实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_TagExtraMessageRepository
 * 作       者：dageng.sun
 * 创建时间：2017/12/07 
 * 修改编号：1
 * 描       述：TagExtraMessage实体的Repository实现   
 */
public interface TagExtraMessageRepository extends JpaRepository<TagExtraMessage, Long>, TagExtraMessageRepositoryCustom {

}
