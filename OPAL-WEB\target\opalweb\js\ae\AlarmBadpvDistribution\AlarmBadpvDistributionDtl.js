var searchUrl = OPAL.API.aeUrl + '/alarmBadpvDistribution/getAlarmBadpvDistributionDtl';
$(function() {
    var page = {
        /**
         * 初始化
         */
        init: function() {
            this.bindUi();
            page.logic.initTable();
        },
        bindUi: function() {
             // 关闭
            $('#closePage').click(function() {
                page.logic.closeLayer(false);
            });
        },
        data: {
            // 设置查询参数
            param: {}
        },
        logic: {
            changeTeam: function(t) {
                page.logic.initWorkTeam();
             },
            initTable: function() {
                OPAL.ui.initBootstrapTable("table", {
                    height:372,
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            var tableOption = $('#table').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            return (index + 1) + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "报警时间",
                        field: 'alarmTime',
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "班组",
                        field: 'workTeamSName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "描述",
                        field: 'des',
                        rowspan: 1,
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "优先级",
                        field: 'priorityName',
                        rowspan: 1,
                        align: 'center',
                        width: '60px'
                    }],
                    onLoadSuccess: function (e) {
                        $("#rankShow").text(e.rows[0].craftRankName);
                        //设置鼠标浮动提示
                        var tds = $('#table').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 搜索
             */
            search: function() {
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            setData: function(data) {
                $("#unitNameShow").text(data.unitName);
                $("#prdtCellShow").text(data.prdtCellName);
                $("#tagShow").text(data.tag);
                $("#startTime").text(data.startTime);
                $("#endTime").text(data.endTime);
                page.data.param =data;
                page.logic.search();
            }
            

        }
    };
    page.init();
    window.page = page;
});