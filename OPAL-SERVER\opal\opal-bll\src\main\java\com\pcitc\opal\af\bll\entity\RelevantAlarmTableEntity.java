package com.pcitc.opal.af.bll.entity;

/*
 * 相关性报警表格实体
 * 模块编号：pcitc_opal_bll_class_RelevantAlarmTableEntity
 * 作       者：dageng.sun
 * 创建时间：2018/08/03
 * 修改编号：1
 * 描       述：相关性报警表格实体
 */
public class RelevantAlarmTableEntity {
	
	/**
	 * 装置简称
	 */
	private String unitSname;
	
	/**
	 * 生产单元简称
	 */
	private String prdtCellSname;
	
	/**
	 * 位号
	 */
	private String tag;
	
	/**
	 * 报警数
	 */
	private Long processCount;
	
	/**
	 * 操作数
	 */
	private Long operateCount;

	public String getUnitSname() {
		return unitSname;
	}

	public void setUnitSname(String unitSname) {
		this.unitSname = unitSname;
	}

	public String getPrdtCellSname() {
		return prdtCellSname;
	}

	public void setPrdtCellSname(String prdtCellSname) {
		this.prdtCellSname = prdtCellSname;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public Long getProcessCount() {
		return processCount;
	}

	public void setProcessCount(Long processCount) {
		this.processCount = processCount;
	}

	public Long getOperateCount() {
		return operateCount;
	}

	public void setOperateCount(Long operateCount) {
		this.operateCount = operateCount;
	}
}
