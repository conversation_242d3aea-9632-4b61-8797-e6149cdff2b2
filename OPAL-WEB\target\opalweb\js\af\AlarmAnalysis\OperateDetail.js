var getOperateDetailUrl = OPAL.API.afUrl + '/alarmAnalysis/getOperateDetail';
var getGridOperateDetailUrl = OPAL.API.afUrl + '/alarmAnalysis/getGridOperateDetail';
var queryTimeArray;
var alarmPointId;
var alarmFlagId;
var endFlag;
var dateJason;
var newStarArr = new Array; // 所有开始时间的数据
var newEndArr = new Array; // 所以结束时间的数组
var startTime, endTime;
var myChart;
$(function() {
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var page = {
        /**
         * 初始化
         */
        init: function() {

            this.bindUi();

        },
        bindUi: function() {
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                myChart.resize();
            };
            // 关闭
            $('#closePageChild').click(function() {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer(false);
            })
        },
        data: {
            // 设置查询参数
            param: {}
        },
        logic: {
            setData: function(data) {
                page.logic.getQueryTime();
                dateJason = data.dateJason;
                alarmPointId = data.alarmPointId;
                alarmFlagId = data.alarmFlagId;
                var dateArr = JSON.parse(dateJason);
                $.each(dateArr, function(i, el) {
                    if (el['key'] == 'startTime') {
                        startTime = OPAL.util.dateFormat(new Date(el['value']), 'yyyy-MM-dd HH:mm:ss');
                    } else if (el['key'] == 'endTime') {
                        endTime = OPAL.util.dateFormat(new Date(el['value']), 'yyyy-MM-dd HH:mm:ss');
                    } else if (el['key'] == 'endFlag') {
                        endFlag = el['value'];
                    }
                })
                $.ajax({
                    url: getOperateDetailUrl,
                    async: true,
                    data: {
                        dateJason: dateJason,
                        alarmPointId: alarmPointId,
                        alarmFlagId: alarmFlagId
                    },
                    dataType: "JSON",
                    type: 'GET',
                    success: function(result) {
                        var res = $.ET.toObjectArr(result);
                        if (res != null && res != undefined && res != '') {
                            var tag = res[0].tag;
                            var alarmFlagName = res[0].alarmFlagName;
                            var prdtCellSname = res[0].prdtCellSname;
                            var location = res[0].location;
                            $('#tag').html(tag);
                            $('#alarmFlagName').html(alarmFlagName);
                            $('#prdtCellSname').html(prdtCellSname);
                            $('#location').html(location);
                            page.logic.initCharts(res[0]);

                        }
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });

            },
            /**
             * 初始化图表
             */
            initCharts: function(data) {
                var seriesData = JSON.parse(data.operatorTimes);
                var xAxisData = JSON.parse(data.positionDate);
                myChart = echarts.init(document.getElementById('charts'));
                option = {
                    color: ['#3398DB'],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { // 坐标轴指示器，坐标轴触发有效
                            type: '' // 默认为直线，可选为：'line' | 'shadow'
                        },
                        formatter: function(params) {
                            var endTime = JSON.parse(dateJason)[1].value;
                            var endTimeValue = OPAL.util.dateFormat(new Date(endTime), 'yyyy-MM-dd HH:mm:ss');
                            var lastTime = xAxisData[(xAxisData.length - 1)];
                            var name = params[0].name.replace(/\//g, "-");
                            var d1 = Date.parse(new Date(params[0].name));
                            var d2 = d1 + (60 * 60 * 24 * 1000);
                            var d3 = moment(d2).format("YYYY-MM-DD");
                            if (params[0].name != lastTime) {
                                var timeData = '从： ' + name + ' ' + queryTimeArray + ' 至：' + d3 + ' ' + queryTimeArray
                            } else {
                                var timeData = '从： ' + name + ' ' + queryTimeArray + ' 至：' + endTimeValue
                            }
                            if (params[0] != null && params[0] != undefined && params[0] != '') {
                                var count = params[0].value;
                            }
                            var str = timeData + '<br>操作数：' + count
                            return str;
                        }
                    },
                    grid: {
                        left: '1%',
                        right: '3%',
                        bottom: '1%',
                        top: '14%',
                        height: '200px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: xAxisData,
                        axisTick: {
                            alignWithLabel: true
                        },

                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                    }, ],
                    yAxis: [{
                        type: 'value',
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                    }],
                    series: [{
                        type: 'bar',
                        barWidth: '15',
                        data: seriesData,
                    }],
                    dataZoom: [{
                        type: 'slider',
                        yAxisIndex: 0,
                        filterMode: 'none',
                        width: 20
                    }]
                };
                myChart.setOption(option);
                var index;
                for (var i = seriesData.length - 1; i >= 0; i--) {
                    if (seriesData[i] != 0) {
                        index = i;
                        break;
                    }
                }
                var starArr = xAxisData;
                var er;
                $.each(starArr, function(i, el) {
                    newStarArr.push(el.replace(/\//g, "-") + ' ' + queryTimeArray);
                    var d1 = Date.parse(new Date(el));
                    var d2 = d1 + (60 * 60 * 24 * 1000);
                    var d3 =moment(d2).format("YYYY-MM-DD");
                    if (i == starArr.length - 1) {
                        er = endTime;
                    } else {
                        er = d3 + ' ' + queryTimeArray;
                    }
                    newEndArr.push(er);
                })
                startTime = newStarArr[index];
                endTime = newEndArr[index];
                if(index != seriesData.length-1){
                    endFlag = '<'
                }
                page.logic.changChartColor(myChart,option,index);
                page.logic.initTable();
                myChart.on('click', function(params) {
                    var i = params.dataIndex;
                    page.logic.changChartColor(myChart,option,i);
                    // myChart.setOption(option);
                    startTime = newStarArr[i];
                    endTime = newEndArr[i];
                    if(i != seriesData.length-1){
                        endFlag = '<'
                    }
                    $('#table').bootstrapTable('refresh');
                })
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    startTime: startTime,
                    endTime: endTime,
                    endFlag: endFlag,
                    alarmPointId: alarmPointId,
                    alarmFlagId: alarmFlagId,
                    now: Math.random()
                }
                page.data.param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                };
                return param;
            },
            changChartColor: function(myChart,option,i) {
                option.series[0].itemStyle = {
                        normal: {
                            color: function(params) {
                                if (params.dataIndex == i) {
                                    return "#ffac28";
                                } else {
                                    return "#2a8ce4";
                                }
                            }
                        }
                    };
                myChart.setOption(option);
            },
            /**
             * 初始化table
             */
            initTable: function() {
                OPAL.ui.initBootstrapTable("table", {
                    url: getGridOperateDetailUrl,
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1 ) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'startTime',
                        title: '操作时间',
                        align: 'center',
                        width: '170px'
                    }, {
                        field: 'priorityName',
                        title: '优先级',
                        align: 'center',
                    }, {
                        field: 'des',
                        title: '描述',
                        align: 'left',
                        width: "250px"
                    }, {
                        field: 'previousValue',
                        title: '调整前的值',
                        align: 'right',
                        width: '100px'
                    }, {
                        field: 'nowValue',
                        title: '当前值',
                        align: 'right',
                        width: '100px'
                    }, {
                        field: 'eventTypeName',
                        title: '操作类型',
                        align: 'center',
                    }, {
                        field: 'operator',
                        title: '操作人',
                        align: 'center',
                    }]
                }, page.logic.queryParams)
                $(window).resize(function () {
                    $('#table').bootstrapTable('resetView');
                });
            },
            /**
             * 获得固定的时间点
             */
            getQueryTime: function() {
                OPAL.util.getQueryTime(function(queryTime) {
                    queryTimeArray = queryTime;
                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function(isRefresh) {
                // parent.isRefresh = isRefresh;
                parent.layer.close(index);
            },
        }
    }
    page.init();
    window.page = page;
})