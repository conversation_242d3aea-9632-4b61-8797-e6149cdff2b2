package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

import java.util.Date;

/*
 * AlarmPointComp实体的Repository的JPA自定义接口
 * 模块编号：pcitc_opal_dal_interface_AlarmPointCompRepositoryCustom
 * 作	者：zheng.yang
 * 创建时间：2018/08/29
 * 修改编号：1
 * 描	述：AlarmPointComp实体的Repository的JPA自定义接口
 */
public interface AlarmPointCacheRepositoryCustom {

    /**
     * 报警点对比查询(新增)
     *
     * <AUTHOR> 2018-08-29
     * @param unitCodes 装置编码数组
     * @param tag 位号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 分页对象
     * @return PaginationBean 返回实体分页对象
     */
    PaginationBean<Object[]> getAlarmPointCacheList(String[] unitCodes, String tag, Date startTime,Date endTime,Pagination page) throws Exception;
    /**
     * 报警点对比查询(删除)
     *
     * <AUTHOR> 2018-08-29
     * @param unitCodes 装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param tag 位号
     * @param inUse 是否启用
     * @param page 分页对象
     * @return PaginationBean 返回实体分页对象
     */
    PaginationBean<Object[]> getAlarmPointCacheList(String[] unitCodes, Long[] prdtCellIds,String tag,Integer inUse, Pagination page) throws Exception;
}
