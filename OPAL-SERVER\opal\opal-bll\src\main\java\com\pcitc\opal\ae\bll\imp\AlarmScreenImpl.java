package com.pcitc.opal.ae.bll.imp;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.ae.bll.AlarmScreenService;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.CommonEnum.TimeFilterTypeEnum;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.DateRangeEntity;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;

import pcitc.imp.common.ettool.utils.ObjectConverter;

/*
 * 报警屏蔽业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmScreenImpl
 * 作       者：kun.zhao
 * 创建时间：2017/10/23
 * 修改编号：1
 * 描       述：报警屏蔽业务逻辑层实现类
 */
@Service
public class AlarmScreenImpl implements AlarmScreenService {

	@Autowired
	private AlarmEventRepository alarmEventRepository;
	@Autowired
	private BasicDataService basicDataService;
	@Autowired
    private ShiftService shiftService;
	
	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-10-23
	 * @param unitCodes		装置编码数组
	 * @param prdtCellIds	生产单元ID数组
	 * @param workTeamIds	班组ID数组
	 * @param alarmPointTag 报警点位号
	 * @param alarmFlagId	报警标识ID
	 * @param priority		优先级
	 * @param startTime		发生时间范围起始
	 * @param endTime		发生时间范围结束
	 * @param page			分页参数
	 * @return 报警屏蔽事件数据
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<AlarmEventEntity> getAlarmEvent(String[] unitCodes, Long[] prdtCellIds, Long[] workTeamIds, String alarmPointTag,
			Long alarmFlagId,Long eventTypeId, Integer priority, Date startTime, Date endTime, Pagination page) throws Exception {
		List<UnitEntity> units = null;
		if(unitCodes == null) {
			units = basicDataService.getUnitList(true);
			unitCodes = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
		}
		// 班组
		if (workTeamIds == null) {
			workTeamIds = new Long[]{};
        }
		List<Long> workTeamIdList = Arrays.asList(workTeamIds);
        List<DateRangeEntity> dateRangeList = new ArrayList<>();
        List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
        if (workTeamIdList.size() != 0 && unitCodes != null && unitCodes.length == 1) {
            shiftWorkList = shiftService.getShiftList(unitCodes[0], startTime, endTime, workTeamIdList);
            dateRangeList = shiftWorkList.stream().map(item -> new DateRangeEntity(item.getStartTime(), item.getEndTime())).collect(Collectors.toList());
        }
		Long[] eventTypeIds = null;
		if(eventTypeId == null || eventTypeId == -1){
			eventTypeIds = new Long[] {3003L,3004L};
		} else {
			eventTypeIds = new Long[] {eventTypeId};
		}
		PaginationBean<AlarmEvent> listAlarmEvent = alarmEventRepository.getAlarmEvent(unitCodes, prdtCellIds, eventTypeIds
				, alarmPointTag, alarmFlagId, priority, null, TimeFilterTypeEnum.StartTime, startTime, endTime, dateRangeList, page);
	    PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<>(page,
	    		listAlarmEvent.getTotal());
	    returnAlarmEvent.setPageList(ObjectConverter.listConverter(listAlarmEvent.getPageList(), AlarmEventEntity.class));
	    // 装置不选或班组全选时补全班组信息
	    if (dateRangeList.size()==0&&workTeamIdList.size()==0) {
            Date minDate=returnAlarmEvent.getPageList().stream().reduce((item1,item2)->item1.getStartTime().getTime()<item2.getStartTime().getTime()?item1:item2).orElse(new AlarmEventEntity()).getStartTime();
            Date maxDate=returnAlarmEvent.getPageList().stream().reduce((item1,item2)->item1.getStartTime().getTime()>item2.getStartTime().getTime()?item1:item2).orElse(new AlarmEventEntity()).getStartTime();
            if(minDate!=null&&maxDate!=null) {
            	shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCodes), minDate, maxDate);
            }
        }
        if(units == null) {
			// 通过公共方法获取装置
			String[] filterunitCodes = listAlarmEvent.getPageList().stream().map(e -> e.getAlarmPoint().getPrdtCell().getUnitId()).distinct().toArray(String[]::new);
			units = basicDataService.getUnitListByIds(filterunitCodes, false);
		}
        // 映射字段
 		for (int i = 0; i < returnAlarmEvent.getPageList().size(); i++) {
 			AlarmEventEntity alarmEventEntity = returnAlarmEvent.getPageList().get(i);
 			AlarmEvent alarmEvent = listAlarmEvent.getPageList().get(i);
 			// 填充装置简称
 			UnitEntity unit = units.stream().filter(u -> alarmEvent.getAlarmPoint().getPrdtCell().getUnitId().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
 			alarmEventEntity.setUnitName(unit.getSname());
 			// 填充生产单元简称
 			alarmEventEntity.setPrdtCellName(alarmEvent.getAlarmPoint().getPrdtCell().getSname());
 			// 填充班组名称
			alarmEventEntity.setWorkTeamSName(shiftWorkList.parallelStream().filter(item -> alarmEventEntity.getStartTime().getTime() >= item.getStartTime().getTime() && alarmEventEntity.getStartTime().getTime() < item.getEndTime().getTime()).findFirst().orElse(new ShiftWorkTeamEntity()).getWorkTeamSName());
 			// 填充报警点位号
 			alarmEventEntity.setAlarmPointTag(alarmEvent.getAlarmPoint().getTag());
 			// 填充报警标识名称
 			alarmEventEntity.setAlarmFlagName(alarmEvent.getAlarmFlag().getName());
 			// 填充事件类型
 			alarmEventEntity.setEventTypeName(alarmEvent.getEventType().getName());
			alarmEventEntity.setLocation(alarmEvent.getAlarmPoint().getLocation());
 		}
        return returnAlarmEvent;
	}
}
