package com.pcitc.opal.ap.dao.imp;


import com.pcitc.opal.pm.pojo.Unit;

import java.util.List;

/*
 * 报警点实体
 * 模块编号：pcitc_pojo_class_Group
 * 作       者：guoganxin
 * 创建时间：2023/04/16
 * 修改编号：1
 * 描       述：群组
 */
public class RelAndDetailListEntityVO {


    /**
     * 规则名称
     */
    private String pushRuleName;

    /**
     * 报警专业（1 工艺、2 设备、3 安全、4 环保、5 质量、6 火灾）
     */
    private Integer alarmSpeciality;

    /**
     * 报警专业名称（1 工艺、2 设备、3 安全、4 环保、5 质量、6 火灾）
     */
    private String specialityName;

    /**
     * 优先级（1 紧急；2重要；3 一般）
     */
    private Long priority;

    /**
     * 优先级名称（1 紧急；2重要；3 一般）
     */
    private String priorityName;

    /**
     * 描述
     */
    private String des;

    /**
     * 禁用的装置
     */
    private List<String> punits;

    /**
     * 关联的装置
     */
    private List<String> cunits;

    public String getPushRuleName() {
        return pushRuleName;
    }

    public void setPushRuleName(String pushRuleName) {
        this.pushRuleName = pushRuleName;
    }

    public Integer getAlarmSpeciality() {
        return alarmSpeciality;
    }

    public void setAlarmSpeciality(Integer alarmSpeciality) {
        this.alarmSpeciality = alarmSpeciality;
        switch(alarmSpeciality){
            case 1 :
                this.specialityName ="工艺";
                break;
            case 2 :
                this.specialityName ="设备";
                break;
            case 3 :
                this.specialityName ="安全";
                break;
            case 4 :
                this.specialityName ="环保";
                break;
            case 5 :
                this.specialityName ="质量";
                break;
            case 6 :
                this.specialityName ="火灾";
                break;
            default :
        }
    }

    public String getSpecialityName() {
        return specialityName;
    }

    public void setSpecialityName(String specialityName) {
        this.specialityName = specialityName;
    }

    public Long getPriority() {
        return priority;
    }

    public void setPriority(Long priority) {
        this.priority = priority;
        switch(Integer.valueOf(Math.toIntExact(this.priority))){
            case 1 :
                this.priorityName ="紧急";
                break;
            case 2 :
                this.priorityName ="重要";
                break;
            case 3 :
                this.priorityName ="一般";
                break;
            default :
        }
    }

    public String getPriorityName() {
        return priorityName;
    }

    public void setPriorityName(String priorityName) {
        this.priorityName = priorityName;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public List<String> getPunits() {
        return punits;
    }

    public void setPunits(List<String> punits) {
        this.punits = punits;
    }

    public List<String> getCunits() {
        return cunits;
    }

    public void setCunits(List<String> cunits) {
        this.cunits = cunits;
    }
}
