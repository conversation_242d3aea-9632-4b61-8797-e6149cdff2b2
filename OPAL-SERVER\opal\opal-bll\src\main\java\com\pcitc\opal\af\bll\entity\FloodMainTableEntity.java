package com.pcitc.opal.af.bll.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/*
 * 高频报警分析列表展示实体
 * 模块编号：pcitc_opal_bll_class_FloodMainTableEntity
 * 作  　者：xuelei.wang
 * 创建时间：2017-11-16
 * 修改编号：1
 * 描    述：高频报警分析列表展示实体
 */
@SuppressWarnings("serial")
public class FloodMainTableEntity implements Serializable {

    public FloodMainTableEntity(String name, Long counts, String timePercent, Long bloodAlarmCounts) {
        this.name = name;
        this.counts = counts;
        this.timePercent = timePercent;
        this.bloodAlarmCounts = bloodAlarmCounts;
    }
    public FloodMainTableEntity(){

    };

    /**
     * 装置或者单元编码
     */
    private String id;
    /**
     * 高频报警二级列表
     */
    private List<FloodSubTableEntity> subList=new ArrayList<>();
    /**
     * 名称
     */
    private String name;
    /**
     * 次数
     */
    private Long counts;
    /**
     * 时间百分比(%)
     */
    private String timePercent;
    /**
     * 报警数（高频）
     */
    private Long bloodAlarmCounts;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCounts() {
        return counts;
    }

    public void setCounts(Long counts) {
        this.counts = counts;
    }

    public String getTimePercent() {
        return timePercent;
    }

    public void setTimePercent(String timePercent) {
        this.timePercent = timePercent;
    }

    public Long getBloodAlarmCounts() {
        return bloodAlarmCounts;
    }

    public void setBloodAlarmCounts(Long bloodAlarmCounts) {
        this.bloodAlarmCounts = bloodAlarmCounts;
    }

    public List<FloodSubTableEntity> getSubList() {
        return subList;
    }

    public void setSubList(List<FloodSubTableEntity> subList) {
        this.subList = subList;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}