var searchUrl = OPAL.API.adUrl + '/alarmEvents/removeList';
var eventTypeUrl = OPAL.API.adUrl + '/alarmEvents/getAllEventType';
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var alarmFlagListUrl = OPAL.API.adUrl + '/alarmEvents/getAlarmFlagList';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var alarmPriorityListUrl = OPAL.API.afUrl + "/alarmDurationStatt/getAlarmPriorityList";
var startTimeListUrl = OPAL.API.commUrl + '/getStartTimeList';
var getQueryTimeUrl = OPAL.API.commUrl + '/getQueryTime';
var getQueryStartAndEndDateUrl = OPAL.API.commUrl + '/getShowTime';
var getCraftRankListUrl = OPAL.API.commUrl + '/getCraftRankList';
var workTeamUrl = OPAL.API.commUrl + "/getWorkTeam";
var exportAlarmEventUrl = OPAL.API.adUrl + '/alarmEvents/exportAlarmRemoveEvent';
var delUrl = OPAL.API.adUrl + '/alarmEvents/deleteAlarmEvent';
var monitorTypeUrl = OPAL.API.commUrl + "/getMonitorTypeList"; //专业
var queryTimeArray;
var setStartTime, setNowTime;
var isLoading = true;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            this.bindUi();
            //扩展日期插件
            OPAL.util.extendDate();
            // 初始化 报警时间的时间点
            page.logic.getQueryTime();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化表格
            page.logic.initTable();
            //初始化事件类型
            page.logic.initEventType();
            //初始化优先级
            page.logic.initAlarmPriorityList();
            //初始化 发生时间
            page.logic.initStartTimeList();
            //初始化 专业
            page.logic.initMonitorType();
            $('#workTeamIds').html("");
            $("#workTeamIds").prop('disabled', true);

            if (isLoading && (page.data.param.unitIds == null || page.data.param.unitIds == undefined || page.data.param.unitIds.length == 0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("AlarmEvent");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }

            //默认查询数据
            setTimeout(function () {
                if ($("#alarmFlagId").val() != null && $("#craftRank").val() != null && $("#priority").val() != null) {
                    page.logic.search();
                }
            }, 500);
        },
        bindUi: function () {
            //选择发生时间
            $('#occurrenceTime').change(function () {
                page.logic.timeChange();
            });
            //导出
            $('#AlarmPointExport').click(function () {
                page.logic.exportExcel();
            });
            //查询
            $('#search').click(function () {
                isLoading = false;
                page.logic.search();
            })
            //批量剔除
            $('#AlarmPointDel').click(function () {
                page.logic.delAll();
            });
        },
        data: {
            //查询参数
            param: {}
        },
        logic: {
            /**
             * 批量剔除
             */
            delAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.eventId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要剔除的数据");
                    return;
                }
                var data = {
                    eventIds: idsArray
                }
                layer.open({
                    type: 2,
                    title: '剔除原因',
                    closeBtn: 1,
                    area: ['780px', '280px'],
                    shadeClose: false,
                    content: 'AlarmEventDelReason.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "idsArray": idsArray,
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            //初始化表格
            initTable: function () {
                var tableColumns = [{
                    title: "序号", formatter: function (value, row, index) {
                        var data = page.data.param;
                        return index + 1 + (data.pageNumber - 1) * data.pageSize;
                    }, rowspan: 1, align: 'center', width: '80px'
                }, {
                    title: "剔除状态", field: 'delStatusShow', rowspan: 1, align: 'center', width: '150px'
                }, {
                    title: "装置", field: 'unitName', rowspan: 1, align: 'left', width: '120px'
                }, {
                    title: "生产单元", field: 'prdtCellName', rowspan: 1, align: 'left', width: '120px'
                }, {
                    title: "班组", field: 'workTeamSName', rowspan: 1, align: 'center', width: '100px'
                }, {
                    title: "发生时间", field: 'startTime', rowspan: 1, align: 'center', width: '150px'
                }, {
                    title: "参数名称", field: 'location', rowspan: 1, align: 'left', width: '150px'
                }, {
                    title: "位号", field: 'alarmPointTag', rowspan: 1, align: 'left', width: '100px'
                }, {
                    title: "报警等级", field: 'alarmFlagName', rowspan: 1, align: 'center', width: '100px'
                }, {
                    title: "优先级", field: 'priorityName', rowspan: 1, align: 'center', width: '60px'
                }, {
                    title: "专业", field: 'monitorTypeStr', rowspan: 1, align: 'left', width: '60px'
                }, {
                    title: "申请人", field: 'delApplyUser', rowspan: 1, align: 'left', width: '100px'
                }, {
                    title: "申请时间", field: 'delApplyDate', rowspan: 1, align: 'left', width: '100px'
                }, {
                    title: "计量单位", field: 'measUnitName', rowspan: 1, align: 'left', width: '100px'
                }];
                tableColumns.unshift({
                    field: 'state', checkbox: true, rowspan: 1, align: 'center', width: '50px'
                });
                tableColumns.push({
                    title: "高高报", field: 'alarmPointHH', rowspan: 1, align: 'center', width: '100px'
                }, {
                    title: "高报", field: 'alarmPointHI', rowspan: 1, align: 'center', width: '100px'
                }, {
                    title: "低报", field: 'alarmPointLO', rowspan: 1, align: 'center', width: '100px'
                }, {
                    title: "低低报", field: 'alarmPointLL', rowspan: 1, align: 'center', width: '100px'
                })

                OPAL.ui.initBootstrapTable2("table", {
                    columns: tableColumns
                }, page.logic.queryParams, "search")
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 获得固定的时间点
             */
            getQueryTime: function () {
                OPAL.util.getQueryTime(function (queryTime) {
                    queryTimeArray = queryTime.split(':');
                    // 初始化 开始时间和结束时间
                    page.logic.getQueryStartAndEndDate();
                    // 初始化 时间设置
                    page.logic.initTime();
                });
            },
            // 初始化 开始时间和结束时间
            getQueryStartAndEndDate: function () {
                $.ajax({
                    url: getQueryStartAndEndDateUrl,
                    async: false,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        setStartTime = dataArr[0].value;
                        setNowTime = dataArr[2].value;
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 设置日期插件
             */
            initTime: function () {
                OPAL.ui.initDateTimePeriodPicker({
                    format: 'yyyy-MM-dd HH:mm:ss',
                    type: 'datetime',
                }, function () {
                    page.logic.initWorkTeam();
                });
            },
            /**
             * 更改发生时间
             */
            timeChange: function () {
                var _V = $('#occurrenceTime').val();
                $('#startTime').attr('disabled', true);
                $('#endTime').attr('disabled', true);
                switch (_V) {
                    case '1':
                        $('#startTime').val(setStartTime);
                        $('#endTime').val(setNowTime);
                        break;
                    case '2':
                        page.logic.setTime('d', -15);
                        break;
                    case '3':
                        page.logic.setTime('d', -30);
                        break;
                    case '4':
                        page.logic.setTime('d', -60);
                        break;
                    case '5':
                        page.logic.setTime('d', -90);
                        break;
                    case '6':
                        page.logic.setTime('d', -180);
                        break;
                    case '7':
                        page.logic.setTime('y', -1);
                        break;
                    default:
                        $('#startTime').attr('disabled', false);
                        $('#endTime').attr('disabled', false);
                        $('#startTime').val(setStartTime);
                        $('#endTime').val(setNowTime);
                        break;
                }
            },
            /**
             * 设置查询日期
             */
            setTime: function (str, num) {
                var myDate = new Date(setNowTime);
                var currentTime = new Date(myDate.getFullYear(), myDate.getMonth(), myDate.getDate(), queryTimeArray[0], queryTimeArray[1], queryTimeArray[2]);
                laydate.render({
                    elem: '#startTime',
                    format: 'yyyy-MM-dd HH:mm:ss',
                    value: new Date(currentTime.dateAdd(str, num))
                });
                laydate.render({
                    elem: '#endTime',
                    format: 'yyyy-MM-dd HH:mm:ss',
                    value: myDate
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function (nodes) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $("#workTeamIds").prop('disabled', false);
                            page.logic.initWorkTeam();
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('#workTeamIds').html("");
                            $("#workTeamIds").prop('disabled', true);
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    }
                }, false, function () {
                    $("#searchPrdt").combotree("checkAllNodes");
                });
            },
            /**
             * 搜索
             */
            search: function () {
                if (!OPAL.util.checkDateIsValid()) {
                    return false;
                }
                $("#search").attr('disabled', true);

                page.data.param = OPAL.form.getData("searchForm", true);
                page.data.param.eventTypeIds.push(1001);
                //page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                //page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 初始化事件类型
             */
            initEventType: function () {
                OPAL.ui.getEasyUIComboTreeSelect('eventTypeIds', eventTypeUrl, 'eventTypeId', 'parentId', 'name', {
                    showParentNodeText: true
                }, false, function () {

                });
            },
            /**
             * 初始化查询 报警标识
             */
            initAlarmFlagList: function () {
                // OPAL.ui.getCombobox("alarmFlagId", alarmFlagListUrl, {
                //     selectFirstRecord: true,
                //     data: {
                //         'isAll': true
                //     }
                // }, null);

                OPAL.ui.getComboMultipleSelect('alarmFlagId', alarmFlagListUrl, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#alarmFlagId").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#alarmFlagId").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化查询 优先级
             */
            initAlarmPriorityList: function () {
                // OPAL.ui.getCombobox("priority", alarmPriorityListUrl, {
                //     selectValue: -1,
                //     data: {
                //         'isAll': true
                //     }
                // }, function (e) {
                //     $("#priority option").eq(0).html("全部");
                // });
                OPAL.ui.getComboMultipleSelect('priority', alarmPriorityListUrl, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#priority").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#priority").combotree("checkAllNodes");
                    //初始化报警标识
                    page.logic.initAlarmFlagList();
                });
            },
            /**
             * 初始化检测类型
             */
            initMonitorType: function () {
                OPAL.ui.getCombobox('monitorType', monitorTypeUrl, {
                    selectValue: 9,
                    async: false,
                    isAll: true
                }, null);
            },
            /**
             * 初始化查询 发生时间
             */
            initStartTimeList: function () {
                OPAL.ui.getCombobox("occurrenceTime", startTimeListUrl, {
                    selectValue: 0,
                }, null);
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function (unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },

            /**
             * 初始化班组选择
             */
            initWorkTeam: function () {
                var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                if (unitIds.length != 1) return;
                if ($("#startTime").val() == '' || $("#endTime").val() == '') return;
                OPAL.ui.getCombobox("workTeamIds", workTeamUrl, {
                    keyField: "workTeamId",
                    valueField: "workTeamSName",
                    selectFirstRecord: true,
                    mapManyValues: true, //是否一条记录匹配多个隐藏值
                    mapManyDataFieldName: 'workTeamIdList',
                    data: {
                        "startTime": $("#startTime").val(),
                        "endTime": $("#endTime").val(),
                        "unitId": unitIds[0],
                    }
                }, null);
            },

            /**
             * 设置参数
             */
            setData: function () {
                page.data.param = OPAL.form.getData('searchForm');
            },
            /**
             * 导出
             */
            exportExcel: function () {
                $("#AlarmPointExport").attr("disabled", true);
                var titleArray = new Array();
                var tableTitle = $('#table').bootstrapTable('getOptions').columns[0];
                $.each(tableTitle, function (i, el) {
                    if (i >= 2) {
                        titleArray.push({
                            'key': el.field,
                            'value': el.title
                        })
                    }
                })
                var data = {};
                var pageSize = $('#table').bootstrapTable('getOptions').pageSize;
                var pageNumber = $('#table').bootstrapTable('getOptions').pageNumber;
                data.titles = JSON.stringify(titleArray);
                data.pageSize = pageSize;
                data.pageNumber = pageNumber;
                page.logic.setData();
                $.extend(data, page.data.param);
                $('#formExPort').attr('action', exportAlarmEventUrl);
                $('#titles').val(data.titles);
                $('#pageSize').val(data.pageSize);
                $('#pageNumber').val(data.pageNumber);
                $('#unitIds1').val(data.unitIds);
                $('#prdtCellIds1').val(data.prdtCellIds);
                $('#eventTypeIds1').val(data.eventTypeIds);
                $('#workTeamIds1').val(data.workTeamIds);
                $('#alarmPointTag1').val(data.alarmPointTag);
                $('#alarmFlagId1').val(data.alarmFlagId);
                $('#craftRank1').val(data.craftRank);
                $('#priority1').val(data.priority);
                $('#monitorType1').val(data.monitorType);
                $('#startTime1').val(data.startTime);
                $('#endTime1').val(data.endTime);
                $('#isMatching1').val(data.isMatching);
                var options = {
                    success: function () {
                        $("#AlarmPointExport").attr("disabled", false);
                    },
                    error: function () {

                    }
                };

                $("#formExPort").ajaxSubmit(options);
                $('#formExPort').submit();
            }

        }
    };
    page.init();
    window.page = page;
});