package com.pcitc.opal.aa.bll.entity;

public class AlarmNumberAssessDateTopEntity {
    /**
     * 装置名称
     */
    private String unitName;
    /**
     * 装置简称
     */
    private String unitSName;
    /**
     * 装置编码
     */
    private String unitCode;
    /**
     * 位号
     */
    private String tag;
    /**
     * 报警数量
     */
    private Long alarmCount;

    /**
     * 报警标识Id
     */
    private Long alarmFlagId;

    /**
     * 报警标识
     */
    private String alarmFlag;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 优先级展示
     */
    private String priorityName;

    /**
     * 位置
     */
    private String location;

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getUnitSName() {
        return unitSName;
    }

    public void setUnitSName(String unitSName) {
        this.unitSName = unitSName;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Long getAlarmCount() {
        return alarmCount;
    }

    public void setAlarmCount(Long alarmCount) {
        this.alarmCount = alarmCount;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public String getAlarmFlag() {
        return alarmFlag;
    }

    public void setAlarmFlag(String alarmFlag) {
        this.alarmFlag = alarmFlag;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getPriorityName() {
        return priorityName;
    }

    public void setPriorityName(String priorityName) {
        this.priorityName = priorityName;
    }
}
