package com.pcitc.opal.common.bll.entity;

/*
 * 组织实体
 * 模块编号：pcitc_opal_bll_class_OrgEntity
 * 作    者：xuelei.wang
 * 创建时间：2017-09-25
 * 修改编号：1
 * 描   述：组织实体
 */
public class OrgEntity {

    /**
     * ID
     */
    private String id;
    /**
     * 数据类型 0:工厂 1:车间 2:装置
     */
    private int type;
    /**
     * 原始编码
     */
    private String originalId;
    /**
     * 父节点ID
     */
    private String parentId;

    /**
     * 排序
     */
    private Integer sortNum;

    /**
     * 简称
     */
    private String sname;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getSname() {
        return sname;
    }

    public void setSname(String sname) {
        this.sname = sname;
    }

    public String getOriginalId() {
        return originalId;
    }

    public void setOriginalId(String originalId) {
        this.originalId = originalId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
