package com.pcitc.opal.af.bll.entity;

import java.io.Serializable;
import java.util.List;

/*
 * 高频报警分析图表展示实体
 * 模块编号：pcitc_opal_bll_class_FloodAlarmChartEntity
 * 作  　者：xuelei.wang
 * 创建时间：2017-11-16
 * 修改编号：1
 * 描    述：高频报警分析图表展示实体
 */
@SuppressWarnings("serial")
public class FloodAlarmChartEntity implements Serializable {
    public FloodAlarmChartEntity(List<FloodAlarmAnalysisEntity> list, String floodName, String name) {
        this.list = list;
        this.floodName = floodName;
        this.name = name;
    }
    /**
     * 分析实体列表
     */
    private List<FloodAlarmAnalysisEntity> list;
    /**
     * 高频报警的名称
     */
    private String floodName;

    /**
     * 装置或者生产单元的名称
     */
    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<FloodAlarmAnalysisEntity> getList() {
        return list;
    }
    public void setList(List<FloodAlarmAnalysisEntity> list) {
        this.list = list;
    }

    public String getFloodName() {
        return floodName;
    }

    public void setFloodName(String floodName) {
        this.floodName = floodName;
    }
}