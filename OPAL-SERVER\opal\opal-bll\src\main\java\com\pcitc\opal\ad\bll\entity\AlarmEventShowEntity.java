package com.pcitc.opal.ad.bll.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class AlarmEventShowEntity {

    /**
     * 发生时间起始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startTime;

    /**
     * 装置名称
     */
    private String unitName;

    /**
     * 生产单元名称
     */
    private String prdtCellName;

    /**
     * 班组简称
     */
    private String workTeamSName;

    /**
     * 报警点位号
     */
    private String alarmPointTag;


    /**
     * 级别(1A；2B)
     */
    @SuppressWarnings("unused")
    private String craftRankName;

    /**
     * 描述
     */
    private String des;


    /**
     * 报警标识名称
     */
    private String alarmFlagName;


    /**
     * 事件类型名称
     */
    private String eventTypeName;


    /**
     * 优先级名称
     */
    @SuppressWarnings("unused")
    private String priorityName;

    /**
     * 先前值
     */
    private String previousValue;

    /**
     * 值
     */
    private String nowValue;

    /**
     * 限值
     */
    private Double limitValue;

    /**
     * 计量单位名称
     */
    private String measUnitName;

    /**
     * 报警点说明
     */
    private String alarmPointExplain;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPrdtCellName() {
        return prdtCellName;
    }

    public void setPrdtCellName(String prdtCellName) {
        this.prdtCellName = prdtCellName;
    }

    public String getWorkTeamSName() {
        return workTeamSName;
    }

    public void setWorkTeamSName(String workTeamSName) {
        this.workTeamSName = workTeamSName;
    }

    public String getAlarmPointTag() {
        return alarmPointTag;
    }

    public void setAlarmPointTag(String alarmPointTag) {
        this.alarmPointTag = alarmPointTag;
    }

    public String getCraftRankName() {
        return craftRankName;
    }

    public void setCraftRankName(String craftRankName) {
        this.craftRankName = craftRankName;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

    public String getEventTypeName() {
        return eventTypeName;
    }

    public void setEventTypeName(String eventTypeName) {
        this.eventTypeName = eventTypeName;
    }

    public String getPriorityName() {
        return priorityName;
    }

    public void setPriorityName(String priorityName) {
        this.priorityName = priorityName;
    }

    public String getPreviousValue() {
        return previousValue;
    }

    public void setPreviousValue(String previousValue) {
        this.previousValue = previousValue;
    }

    public String getNowValue() {
        return nowValue;
    }

    public void setNowValue(String nowValue) {
        this.nowValue = nowValue;
    }

    public Double getLimitValue() {
        return limitValue;
    }

    public void setLimitValue(Double limitValue) {
        this.limitValue = limitValue;
    }

    public String getMeasUnitName() {
        return measUnitName;
    }

    public void setMeasUnitName(String measUnitName) {
        this.measUnitName = measUnitName;
    }

    public String getAlarmPointExplain() {
        return alarmPointExplain;
    }

    public void setAlarmPointExplain(String alarmPointExplain) {
        this.alarmPointExplain = alarmPointExplain;
    }
}
