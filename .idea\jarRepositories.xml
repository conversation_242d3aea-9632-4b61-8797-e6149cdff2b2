<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="sinopec" />
      <option name="name" value="sinopec" />
      <option name="url" value="http://nexus.icloud.sinopec.com/nexus/content/repositories/thirdparty/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyun" />
      <option name="name" value="aliyun" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyun-repos" />
      <option name="name" value="aliyun-repos" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="P00A800BEE-Snapshot" />
      <option name="name" value="P00A800BEE-Snapshot" />
      <option name="url" value="http://nexus.pcitc.com/nexus/content/repositories/P00A800BEE-Snapshot" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="P00A800BEE-Release" />
      <option name="name" value="P00A800BEE-Release" />
      <option name="url" value="http://nexus.pcitc.com/nexus/content/repositories/P00A800BEE-Release" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="workFlowMavenCenter" />
      <option name="name" value="MavenMirror" />
      <option name="url" value="http://nexus.paas.sinopec.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus" />
      <option name="name" value="nexus" />
      <option name="url" value="http://nexus.paas.sinopec.com/nexus/content/groups/Sinopec-IMP-Public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="pcitc-thirdparty" />
      <option name="name" value="pcitc-thirdparty" />
      <option name="url" value="http://nexus.pcitc.com/nexus/content/repositories/thirdparty/" />
    </remote-repository>
  </component>
</project>