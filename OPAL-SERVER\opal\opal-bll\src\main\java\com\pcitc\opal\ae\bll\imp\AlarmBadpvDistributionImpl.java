package com.pcitc.opal.ae.bll.imp;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.ae.bll.AlarmBadpvDistributionService;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.DateRangeEntity;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;

import pcitc.imp.common.ettool.utils.ObjectConverter;


/*
 * 报警坏点分布业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmBadpvDistributionImpl
 * 作       者：dageng.sun
 * 创建时间：2017/10/23
 * 修改编号：1
 * 描       述：报警坏点分布业务逻辑层实现类
 */
@Service
@Component
public class AlarmBadpvDistributionImpl implements AlarmBadpvDistributionService {

    /**
     * 实例化数据访问层接口
     */
    @Autowired
    private AlarmEventRepository repo;

    @Autowired
    private BasicDataService basicDataService;

    @Autowired
    private ShiftService shiftService;

    /**
     * 获取分页数据
     * 
     * <AUTHOR> 2017-10-23
     * @param unitCodes       装置编码数组
     * @param prdtCellIds   生产单元id数组
     * @param tag           位号
     * @param priority      优先级
     * @param beginTime     报警事件的开始间
     * @param endTime       报警事件的结束时间
     * @param workTeamIds   班组Id集合
     * @param page          翻页实现类  
     * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity分页对象
     * @throws Exception 
     */
    @SuppressWarnings("unchecked")
    @Override
    @Deprecated
    public PaginationBean<AlarmEventEntity> getAlarmBadpvDistribution(String[] unitCodes, Long[] prdtCellIds, String tag,
                                                                      Integer priority, Date beginTime, Date endTime, Long[] workTeamIds, Pagination page) throws Exception {
    	List<UnitEntity> unitList = null;
        if(ArrayUtils.isEmpty(unitCodes)){
			unitList = basicDataService.getUnitList( true);
			unitCodes=unitList.stream().map(x->x.getStdCode()).distinct().toArray(String[]::new);
		}
    	if (workTeamIds == null) {
            workTeamIds = new Long[]{};
        }
        List<Long> workTeamIdList = Arrays.asList(workTeamIds);
        List<DateRangeEntity> dateRangeList = new ArrayList<>();
        List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
        if (workTeamIdList.size() != 0 && unitCodes != null && unitCodes.length == 1) {
            shiftWorkList = shiftService.getShiftList(unitCodes[0], beginTime, endTime, workTeamIdList);
            dateRangeList = shiftWorkList.stream().map(item -> new DateRangeEntity(item.getStartTime(), item.getEndTime())).collect(Collectors.toList());
        }

        PaginationBean<AlarmEvent> listAlarmEvent = repo.getAlarmBadpvDistribution(unitCodes, prdtCellIds, tag, priority, beginTime, endTime, dateRangeList, page);
        PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<>(page, listAlarmEvent.getTotal());
        returnAlarmEvent.setPageList(ObjectConverter.listConverter(listAlarmEvent.getPageList(), AlarmEventEntity.class));

        if (dateRangeList.size()==0&&workTeamIdList.size()==0) {
            Date minDate=returnAlarmEvent.getPageList().stream().reduce((item1,item2)->item1.getAlarmTime().getTime()<item2.getAlarmTime().getTime()?item1:item2).orElse(new AlarmEventEntity()).getAlarmTime();
            Date maxDate=returnAlarmEvent.getPageList().stream().reduce((item1,item2)->item1.getAlarmTime().getTime()>item2.getAlarmTime().getTime()?item1:item2).orElse(new AlarmEventEntity()).getAlarmTime();
            if(minDate!=null&&maxDate!=null) {
                shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCodes!=null?unitCodes:new String[]{}),minDate, maxDate);
            }
        }
        if(unitList == null) {
            unitList = basicDataService.getUnitListByIds(listAlarmEvent.getPageList().stream().map(x->x.getAlarmPoint().getPrdtCell().getUnitId()).distinct().toArray(String[]::new), false);
        }
        int i = 0;
        for (AlarmEventEntity aee : returnAlarmEvent.getPageList()) {
            AlarmEvent ae = listAlarmEvent.getPageList().get(i);
            UnitEntity ue=unitList.stream().filter(ul->ae.getAlarmPoint().getPrdtCell().getUnitId().equals(ul.getStdCode())).findFirst().orElse(new UnitEntity());
            aee.setUnitName(ue.getSname());
            aee.setPrdtCellName(ae.getAlarmPoint().getPrdtCell().getSname());
            aee.setAlarmPointTag(ae.getAlarmPoint().getTag());
            aee.setAlarmFlagName(ae.getAlarmFlag().getName());
            aee.setWorkTeamSName(shiftWorkList.parallelStream().filter(item -> aee.getAlarmTime().getTime() >= item.getStartTime().getTime() && aee.getAlarmTime().getTime() < item.getEndTime().getTime()).findFirst().orElse(new ShiftWorkTeamEntity()).getWorkTeamSName());
            i++;
        }
        return returnAlarmEvent;
    }

    /**
     * 获取分页数据
     *
     * <AUTHOR> 2018-11-19
     * @param unitCodes       装置编码数组
     * @param prdtCellIds   生产单元id数组
     * @param tag           位号
     * @param beginTime     报警事件的开始间
     * @param endTime       报警事件的结束时间
     * @param page          翻页实现类
     * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity分页对象
     * @throws Exception 
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<AlarmEventEntity> getAlarmBadpvDistribution(String[] unitCodes, Long[] prdtCellIds, String tag,
                                                                      Date beginTime, Date endTime, Pagination page) throws Exception {
        List<UnitEntity> unitList = null;
        if(ArrayUtils.isEmpty(unitCodes)){
            unitList = basicDataService.getUnitList( true);
            unitCodes=unitList.stream().map(x->x.getStdCode()).distinct().toArray(String[]::new);
        }

        PaginationBean<Object[]> listAlarmEvent = repo.getAlarmBadpvDistribution(unitCodes, prdtCellIds, tag, beginTime, endTime, page);
        PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<>(page, listAlarmEvent.getTotal());
        List<AlarmEventEntity> alarmEventEntityList = new ArrayList<>();

        if(unitList == null) {
            unitList = basicDataService.getUnitListByIds(listAlarmEvent.getPageList().stream().map(x->x[1].toString()).distinct().toArray(String[]::new), false);
        }
        for(Object[] o : listAlarmEvent.getPageList()){
            AlarmEventEntity e = new AlarmEventEntity();
            e.setAlarmPointId(Long.parseLong(o[0]+""));
            UnitEntity ue=unitList.stream().filter(x->o[1].toString().equals(x.getStdCode())).findFirst().orElse(new UnitEntity());
            e.setUnitName(ue.getSname());
            e.setPrdtCellName(o[2]+"");
            e.setTag(o[3]+"");
            e.setAlarmPointLocation(o[4]==null?"":o[4]+"");
            e.setCount(Long.parseLong(o[5]+""));
            alarmEventEntityList.add(e);
        }
        returnAlarmEvent.setPageList(alarmEventEntityList);
        return returnAlarmEvent;
    }
    /**
     * 获取报警坏点详情分页数据
     *
     * <AUTHOR> 2018-11-19
     * @param alarmPointId   报警点id
     * @param beginTime     报警事件的开始间
     * @param endTime       报警事件的结束时间
     * @param page          翻页实现类
     * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity分页对象
     * @throws Exception 
     */
    @Override
    public PaginationBean<AlarmEventEntity> getAlarmBadpvDistributionDtl(Long alarmPointId, Date beginTime, Date endTime, Pagination page) throws Exception {
        PaginationBean<AlarmEvent> alarmEventList = repo.getAlarmBadpvDistributionDtl(alarmPointId, beginTime, endTime, page);
        PaginationBean<AlarmEventEntity> returnList = new PaginationBean<AlarmEventEntity>(page,alarmEventList.getTotal());
        returnList.setPageList(ObjectConverter.listConverter(alarmEventList.getPageList(), AlarmEventEntity.class));
        Date minDate=returnList.getPageList().stream().reduce((item1,item2)->item1.getAlarmTime().getTime()<item2.getAlarmTime().getTime()?item1:item2).orElse(new AlarmEventEntity()).getAlarmTime();
        Date maxDate=returnList.getPageList().stream().reduce((item1,item2)->item1.getAlarmTime().getTime()>item2.getAlarmTime().getTime()?item1:item2).orElse(new AlarmEventEntity()).getAlarmTime();
        List<String> unitCodes = alarmEventList.getPageList().stream().map(x -> x.getAlarmPoint().getPrdtCell().getUnitId()).distinct().collect(Collectors.toList());
        List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
        if(minDate!=null&&maxDate!=null) {
            shiftWorkList = shiftService.getShiftWorkTeamList(unitCodes,minDate, maxDate);
        }
        int i=0;
        for(AlarmEventEntity e : returnList.getPageList()){
            AlarmEvent ae = alarmEventList.getPageList().get(i);
            e.setAlarmFlagName(ae.getAlarmFlag().getName());
            ShiftWorkTeamEntity shiftWorkTeamEntity = shiftWorkList.parallelStream().filter(item -> e.getAlarmTime().getTime() >= item.getStartTime().getTime() && e.getAlarmTime().getTime() < item.getEndTime().getTime()).findFirst().orElse(new ShiftWorkTeamEntity());
            e.setWorkTeamSName(shiftWorkTeamEntity.getWorkTeamSName());
            e.setCraftRank(ae.getAlarmPoint().getCraftRank());
            i++;
        }
        return returnList;
    }

}
