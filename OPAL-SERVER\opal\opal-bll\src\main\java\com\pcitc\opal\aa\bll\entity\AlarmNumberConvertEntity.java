package com.pcitc.opal.aa.bll.entity;

import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;

import java.util.List;
import java.util.Map;

/*
 * 报警数量评估-报警数实体
 * 模块编号：pcitc_opal_service_class_AlarmNumberAssess
 * 作  　者：dageng.sun
 * 创建时间：2017-10-30
 * 修改编号：1
 * 描    述：报警数量评估-报警数实体
 */
public class AlarmNumberConvertEntity {
	
	/**
	 * 装置名称/生产单元名称的编码
	 */
	private String id;
	
	/**
	 * 装置名称/生产单元名称
	 */
	private String name;
	
	/**
	 * 横坐标集合
	 */
	private List<String> xaxis;
	
	/**
	 * 提示语集合
	 */
	private List<String> tip;
	
	/**
	 * 报警数集合
	 */
	private List<Long> counts;
	
	/**
     * 数据信息
     */
    private List<AlarmNumberEntity> list;
    
    /**
     * 总报警数
     */
    private long sum;
    
    /**
     * 报警平均数
     */
    private float avg;
    
    /**
     * 图例的数据数组
     */
    private List<String> legend;
    
    /**
     * 图例的颜色数组
     */
    private List<String> color;

    private List<String> barNames;

    private Map<String, List<ShiftWorkTeamEntity>> groupworkTeamList;

    private List<ShiftWorkTeamEntity> shiftWorkTeamEntityList;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public List<String> getXaxis() {
		return xaxis;
	}

	public void setXaxis(List<String> xaxis) {
		this.xaxis = xaxis;
	}

	public List<String> getTip() {
		return tip;
	}

	public void setTip(List<String> tip) {
		this.tip = tip;
	}

	public List<Long> getCounts() {
		return counts;
	}

	public void setCounts(List<Long> counts) {
		this.counts = counts;
	}

	public List<AlarmNumberEntity> getList() {
		return list;
	}

	public void setList(List<AlarmNumberEntity> list) {
		this.list = list;
	}

	public long getSum() {
		return sum;
	}

	public void setSum(long sum) {
		this.sum = sum;
	}

	public float getAvg() {
		return avg;
	}

	public void setAvg(float avg) {
		this.avg = avg;
	}

	public List<String> getLegend() {
		return legend;
	}

	public void setLegend(List<String> legend) {
		this.legend = legend;
	}

	public List<String> getColor() {
		return color;
	}

	public void setColor(List<String> color) {
		this.color = color;
	}

	public List<String> getBarNames() {
		return barNames;
	}

	public void setBarNames(List<String> barNames) {
		this.barNames = barNames;
	}

	public Map<String, List<ShiftWorkTeamEntity>> getGroupworkTeamList() {
		return groupworkTeamList;
	}

	public void setGroupworkTeamList(Map<String, List<ShiftWorkTeamEntity>> groupworkTeamList) {
		this.groupworkTeamList = groupworkTeamList;
	}

	public List<ShiftWorkTeamEntity> getShiftWorkTeamEntityList() {
		return shiftWorkTeamEntityList;
	}

	public void setShiftWorkTeamEntityList(List<ShiftWorkTeamEntity> shiftWorkTeamEntityList) {
		this.shiftWorkTeamEntityList = shiftWorkTeamEntityList;
	}
}
