package com.pcitc.opal.common.bll;

import com.pcitc.opal.common.bll.entity.OrgEntity;
import com.pcitc.opal.common.bll.entity.FactoryEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.common.bll.entity.WorkshopEntity;

import java.util.List;

/**
 * 工厂模型接口
 */
public interface FactoryModelService {
    /**
     * 获取装置树形结构
     *
     * @param enablePrivilege 是否启用权限过滤
     * @return 树形实体集合
     * <AUTHOR> 2017-09-25
     */
    List<OrgEntity> getAllUnitTree(boolean enablePrivilege) throws Exception;

    /**
     * 获取生产装置列表
     *
     * @param enablePrivilege 是否启用权限过滤
     * @return 生产装置列表
     * <AUTHOR> 2017-09-26
     */
    List<UnitEntity> getUnitList(boolean enablePrivilege) throws Exception;

    /**
     * 根据装置编码集合获取装置列表
     *
     * @param unitCodes         装置编码集合
     * @param isAll              是否显示全部
     * @param enablePrivilege 是否启用权限过滤
     * @return 装置列表
     * <AUTHOR> 2017-09-26
     */
    List<UnitEntity> getUnitListByIds(String[] unitCodes, boolean isAll, boolean enablePrivilege) throws Exception;

    /**
     * 根据车间编码集合获取该车间下所有的已启用的装置列表
     *
     * @param workshopCodes
     * @param enablePrivilege 是否启用权限过滤
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-7
     */
    List<UnitEntity> getUnitListByWorkshopIds(String[] workshopCodes, boolean enablePrivilege) throws Exception;

    /**
     * 根据车间编码集合获取车间列表
     *
     * @param workshopCodes
     * @return 车间集合
     * @throws Exception
     * <AUTHOR> 2017-11-8
     */
    List<WorkshopEntity> getWorkshopListByWorkshopIds(String[] workshopCodes) throws Exception;
}
