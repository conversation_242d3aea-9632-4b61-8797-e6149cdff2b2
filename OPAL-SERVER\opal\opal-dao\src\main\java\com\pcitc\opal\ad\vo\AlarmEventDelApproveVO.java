package com.pcitc.opal.ad.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AlarmEventDelApproveVO implements Serializable {

    /**
     * 报警剔除配置ID
     */
    private Integer alarmEventDelApproveId;


    /**
     * 剔除原因
     */
    private String reason;

    /**
     * 剔除状态（0未提交；1已提交；2已通过；3已驳回）
     */
    private Integer delStatus;
    private String delStatusShow;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 班组ID
     */
    private Long workTeamId;
    /**
     * 班组名称
     */
    private String workTeamName;
    /**
     * 持续时长
     */
    private String continuousHour;
    /**
     * 装置ID
     */
    private String unitCode;
    /**
     * 装置名称
     */
    private String unitName;

    /**
     * 生产单元名称
     */
    private Long prdtCellId;
    private String prdtCellName;

    /**
     * 事件类型ID
     */
    private Long eventTypeId;

    /**
     * 事件类型名称
     */
    private String eventTypeName;

    /**
     * 报警点位号
     */
    private String alarmPointTag;

    /**
     * 报警点位置
     */
    private String location;

    /**
     * 报警点ID
     */
    private Long alarmPointId;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     * 报警标识名称
     */
    private String alarmFlagName;

    /**
     * 优先级(1紧急；2重要；3一般)
     */
    private Integer priority;

    /**
     * 优先级名称
     */
    private String priorityName;

    /**
     * 报警点高高报
     */
    private Double alarmPointHH;
    /**
     * 报警点高报
     */
    private Double alarmPointHI;
    /**
     * 报警点低报
     */
    private Double alarmPointLO;
    /**
     * 报警点低低报
     */
    private Double alarmPointLL;

    /**
     * 发生时间起始
     */
    private Date startTime;

    /**
     * 发生时间结束
     */
    private Date endTime;

    /**
     * 报警时间
     */
    private Date alarmTime;

    /**
     * 先前值
     */
    private String previousValue;

    /**
     * 值
     */
    private String nowValue;

    /**
     * 限值
     */
    private Double limitValue;

    /**
     * 计量单位id
     */
    private String measUnitId;

    /**
     * 计量单位名称
     */
    private String measUnitName;

    private String sign;

    /**
     * 描述
     */
    private String des;

    /**
     * 位号
     */
    private String tag;

    /**
     * 报警点位置
     */
    private String alarmPointLocation;
    /**
     * 级别(1A；2B)
     */
    private Integer craftRank;
    /**
     * 级别(1A；2B)
     */
    @SuppressWarnings("unused")
    private String craftRankName;
    /**
     * 发生时间起始
     */
    private String startTimeStr;
    /**
     * 处理时间（为空时默认赋当前时间）
     */
    private Date handleTime;

    /**
     * 处理时间（为空时不赋默认值）
     */
    private Date recoveryTime;
    /**
     * 班组简称
     */
    private String workTeamSName;
    /**
     * 班组编码
     */
    private String workTeamCode;
    /**
     * 工艺卡片值
     */
    private String craftLimitValue;

    /**
     * 联锁值
     */
    private String interlockLimitValue;


    /**
     * 创建时间
     */

    private Date crtDate;

    /**
     * 创建人ID
     */
    private String crtUserId;

    /**
     * 创建人名称
     */
    private String crtUserName;

    /**
     * 审批时间
     */
    private Date aProTime;

    /**
     * 审批人ID
     */
    private String aProUserId;

    /**
     * 审批人名称
     */
    private String aProUserName;


}
