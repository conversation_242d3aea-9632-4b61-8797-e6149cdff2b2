package com.pcitc.opal.ac.dao;
import com.pcitc.opal.ac.pojo.AlarmChangePlan;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * AlarmChangePlan实体的Repository的JPA标准接口
 * 模块编号： pcitc_opal_dal_interface_AlarmChangePlanRepository
 * 作       者：xuelei.wang
 * 创建时间：2018/1/19
 * 修改编号：1
 * 描       述：AlarmChangePlan实体的Repository的JPA标准接口
 */
public interface AlarmChangePlanRepository extends JpaRepository<AlarmChangePlan, Long>, AlarmChangePlanRepositoryCustom {
}
