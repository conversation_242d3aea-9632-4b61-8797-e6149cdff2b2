package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.AlarmPointCacheEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/*
 * 报警点对比业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmPointCompService
 * 作       者：zheng.yang
 * 创建时间：2018/08/29
 * 修改编号：1
 * 描       述：报警点对比业务逻辑层接口
 */
@Service
public interface AlarmPointCompService {

    /**
     * 获取分页数据(新增)
     *
      * <AUTHOR> 2018-08-29
     * @param unitCodes 装置编码集合
     * @param tag 位号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmPointEntity> 翻页对象
     */
    PaginationBean<AlarmPointCacheEntity> getAlarmPointCacheList(String[] unitCodes, String tag, Date startTime, Date endTime, Pagination page) throws Exception;
    /**
     * 获取分页数据(删除)
     *
      * <AUTHOR> 2018-08-29
     * @param unitCodes 装置编码集合
     * @param prdtCellIds 生产单元id
     * @param tag 位号
     * @param inUse 是否启用
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmPointEntity> 翻页对象
     */
    PaginationBean<AlarmPointEntity> getAlarmPointCacheList(String[] unitCodes, Long[] prdtCellIds,String tag,Integer inUse, Pagination page) throws Exception;
    /**
     * 获取导出数据(新增)
     *
      * <AUTHOR> 2018-11-05
     * @param unitCodes 装置编码集合
     * @param tag 位号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 翻页实现类
     * @throws Exception 
     * @return List<AlarmPointCacheEntity>
     */
    List<AlarmPointCacheEntity> getExportAlarmPointCompList(String[] unitCodes, String tag, Date startTime, Date endTime, Pagination page) throws Exception;
}
