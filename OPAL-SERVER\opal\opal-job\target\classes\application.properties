﻿#web服务端口
server.port=8080

#配置数据---Oracle--开发环境
#OPAL_DB=10.238.220.173:1521:opal
#OPAL_DB_USERNAME=mes_opal_dev
#OPAL_DB_PASSWORD=mes_opal_dev


##配置数据---Oracle--测试环境
#OPAL_DB= *************:1521:opal
#OPAL_DB_USERNAME=mes_opal
#OPAL_DB_PASSWORD=mes_opal

#spring.datasource.driverClassName=oracle.jdbc.OracleDriver
#spring.datasource.url=jdbc:oracle:thin:@${OPAL_DB}
#spring.datasource.username=${OPAL_DB_USERNAME}
#spring.datasource.password=${OPAL_DB_PASSWORD}
#spring.jpa.database-platform=com.pcitc.opal.oracle.dialect.CustomOracleDialect

##配置数据---mysql--测试环境
#bendi
#OPAL_DB_USERNAME=ENC(A15zeTQez77wt0cipth+Us1uYUKnYJoE)
#OPAL_DB_PASSWORD=ENC(A15zeTQez77wt0cipth+Us1uYUKnYJoE)
#spring.datasource.url=ENC(tPfCShw1QVz0wJ8hQT2bkVjTUlPx+R45pfdriFotQ6r1HG169olpHcxWrgmIomQTiSK7zqXMw9eDq7blyPpJmBf60EwvczfI4YkB4ICLvcMsVWa0PQGHwHdeR6mSKaPP7L71VjMHJhrKUJw0i/okYTtWHNXpUyPpX6BTz+4OSjy8yIkD7DfJQkHSVjiBfa/giDzFYOdJ3QRTpPyWg1YaVA==)
#多租
#OPAL_DB_USERNAME=ENC(sWmjf7PK80I/xxDHtNlxpNbEtMFNGfrKmujOQ4Q+UOk=)
#OPAL_DB_PASSWORD=ENC(sWmjf7PK80I/xxDHtNlxpNbEtMFNGfrKmujOQ4Q+UOk=)
#spring.datasource.url=ENC(VhhTrPArDhn9agGdpgCkKSkzDUv7eGwd7B04SWHQoLo/3Nw6NPfTSB6UrHzW+sFNbyOQj0rZnu7k3QpaWZ7tjWq7TcxgHqpc7vw55rWyhWRVaxTvyqW5ikEr320pr3u+ll9tvCPQOgGbojXn3vKKhgQNHUpUI/MP/oKbD7CO7P1kHHE87ykow8xSGSTNmBiiP8fd5GxbaxubukmK3nIZYBT85/ebrn6l)
OPAL_DB_USERNAME=mes_opal_mtenant
OPAL_DB_PASSWORD=mes_opal_mtenant
spring.datasource.url=*********************************************************************************************************************************************************

#总部多租
#spring.datasource.url=**************************************************************************************************************************************************
#OPAL_DB_USERNAME=mes_opal
#OPAL_DB_PASSWORD=Mes_opal123


#guangshihua
#OPAL_DB_USERNAME=ENC(A15zeTQez77wt0cipth+Us1uYUKnYJoE)
#OPAL_DB_PASSWORD=ENC(TUDQR0EbFW9p87fbt3PcXz0wQ87LEMym)
#spring.datasource.url=ENC(H78ZvvXA2cwQtB9iyHNeI4Yssqmk3u906HsA/a/YjSZtLM5nQG9z4WLqfv3ofz9m0qCbl0wIP0tC29TumGExMp68FbyGqPj1rb88sLuf9+2UQa0qDShgFCbHQcmtmUDyB0bOe39yq4EL+ly7bppixQuG6jMeSJAf/EZqAzqpXe0werTn8rUa38jqJpx1O2X2SQIJY9dvorPS6/+kAB8pkQ==)

spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=${OPAL_DB_USERNAME}
spring.datasource.password=${OPAL_DB_PASSWORD}





##在建表的时候，将默认的存储引擎切换为 InnoDB 用的
spring.jpa.database= MYSQL
spring.jpa.database-platform=com.pcitc.opal.oracle.dialect.CustomMysqlDialect
#mi
#jasypt.encryptor.password=mesopal
#hibernate.dialect.storage_engine =innodb


# HikariCP settings
# spring.datasource.hikari.*
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.minimum-idle=20
spring.datasource.hikari.maximum-pool-size=100
# 设置连接有效时间
spring.datasource.hikari.max-lifetime=500000
#连接测试查询
spring.datasource.hikari.connection-test-query=SELECT 1


spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.serialization.write-dates-as-timestamps=true
#spring.jpa
spring.jpa.hibernate.show_sql = true
spring.jpa.show_sql = true
spring.jpa.open-in-view= true
spring.jpa.properties.hibernate.proc.param_null_passing=true
hibernate.proc.param_null_passing=true
spring.jpa.hibernate.jdbc.batch_size = 30
spring.jpa.hibernate.cache.use_second_level_cache = false
spring.jpa.properties.hibernate.jdbc.batch_size=100
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates =true


#spring boot 设置文件上传最大限制
#spring.http.multipart.max-file-size=50MB
#spring.http.multipart.max-request-size=50MB
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
server.tomcat.max-swallow-size=100MB
#应用配置
imp.appcode = demo
#平台服务地址配置
imp.bizlogsvc_address_base = **************:30009/bizlog



#AAA 组织机构和用户服务访问地址资源服务访问地址
aaa.aaa_address_base=http://*************:8080/IP/AAA/LoginService

#PORMACE AAA组织机构和用户服务访问地址
aaa.organduser.url=http://*************
#PROMACE AAA资源服务访问地址
aaa_code =OPAL
aaa.appCode = ${aaa_code}
aaa_resouce_url=http://*************
aaa.resource.url=${aaa_resouce_url}/ResourceService

#PROMACE 装置属性
#aaa_resource_oaplrunitProperty = OPAL_TEST_UNIT_PROPERTY
aaa_resource_oaplrunitProperty =OPALUNIT_PROPERTY
aaa.resource.opalunitProperty =${aaa_resource_oaplrunitProperty}

#AAA使用版本   old:老版本(镇海),new:新版本,promace:promace
aaa_version=new


#AAA 班组WebService地址(后缀不用带"/")
aaa.shiftcalendarsvc_address_base=http://**************:8080
#PORMACE 班组
promace.imp.shift.base.url=http://shift.wsm.qlsh.promace.sinopec.com


#工厂模型-根Url
#factorymodel.base.url=http://**************:30625/FactoryModelService/rents/MESTEST/
factorymodel.base.url=http://pm.wsm.qlsh.promace.sinopec.com
fm_bizCode=qlsh
fm_unit_type_code=plants
factorymodel.bizCode=${fm_bizCode}
fm_factoryTypeCode=1005
factorymodel.factoryTypeCode=${fm_factoryTypeCode}
fm_workshopTypeCode=1007
factorymodel.workshopTypeCode=${fm_workshopTypeCode}


#运行环境类型promace,other
runtime_type=other
#是否开启装置数据权限 1：开启，0：不开启
aaa_auth=1 


#短信-job执行表达式（每1分钟执行一次）
job_run_expression_authpropertyjob=0 0/1 * * * ?
#镇海短信服务ESB地址
#msgUrl =http://**************:6666/ESB.OnRamp.TwoWay.Basic/ProcessRequestResponse.svc?token=PhmBXj1jxfTNxpHBQHjyZ8pO35jnisNNO7Zp0uUnvOc%3D

#广石化短信服务开关
jobenable=false
#广石化定时任务配置
job_run_expression_gshjob=0 0 2 * * ?
job_run_distributionServes_NBDW=http://*************:8080/MasterDataService/rents/CZBJ/distributionServes/restful/CZBJ?modelCode=NBDW
job_run_distributionServes_DVPEQUIPMENT=http://*************:8080/MasterDataService/rents/CZBJ/distributionServes/restful/CZBJ?modelCode=DVPEQUIPMENT


companyId=24