package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.CommonEnum;

import java.util.Date;

/*
 * 报警事件缓存表
 * 模块编号：pcitc_opal_bll_class_AlarmEventCacheEntity
 * 作    者：xuelei.wang
 * 创建时间：2018-04-16 19:34:50
 * 修改编号：1
 * 描    述：报警事件缓存表
 */
public class AlarmEventCacheEntity {

    /**
     * 报警事件ID
     */
    private Long eventId;

    /**
     * 写入时间
     */
    private Date writeTime;

    /**
     * DCS编码
     */
    private Long dcsCode;

    /**
     * 读取时间
     */
    private Date readTime;

    /**
     * 数据状态
     */
    private Long stateFlag;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 生产单元
     */
    private String prdtCell;

    /**
     * 报警点
     */
    private String alarmPoint;

    /**
     * 报警标识
     */
    private String alarmFlag;

    /**
     * 事件发生时间
     */
    private Date startTime;

    /**
     * 报警时间
     */
    private Date alarmTime;

    /**
     * 优先级
     */
    private String priority;
    /**
     * 优先级标识
     */
    private Integer priorityId;
    /**
     * 先前值
     */
    private String previousValue;

    /**
     * 值
     */
    private String nowValue;

    /**
     * 限值
     */
    private Integer limitValue;

    /**
     * 计量单位
     */
    private String measUnit;

    /**
     * 是否搁置
     */
    private Integer inShelved;

    /**
     * 是否屏蔽
     */
    private Integer inSuppressed;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 报警描述
     */
    private String des;

    /**
     * 参数
     */
    private String parameter;

    /**
     * 动作
     */
    private String action;

    /**
     * 报警状态
     */
    private String active;

    /**
     * 确认状态
     */
    private String acked;
    /**
     * dcs名称
     */
    private String dcsName;
    /**
     * 不一致原因 1不存在 2不一致
     */
    private Long reson;
    /**
     * 计量单位不一致原因
     */
    private String resonName;
    /**
     * 不一致原因 1不存在 2不一致
     */
    private String alarmPointReasonName;
    /**
     * OPC
     */
    private Long opcCode;

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public Date getWriteTime() {
        return writeTime;
    }

    public void setWriteTime(Date writeTime) {
        this.writeTime = writeTime;
    }

    public Long getDcsCode() {
        return dcsCode;
    }

    public void setDcsCode(Long dcsCode) {
        this.dcsCode = dcsCode;
    }

    public Date getReadTime() {
        return readTime;
    }

    public void setReadTime(Date readTime) {
        this.readTime = readTime;
    }

    public Long getStateFlag() {
        return stateFlag;
    }

    public void setStateFlag(Long stateFlag) {
        this.stateFlag = stateFlag;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getPrdtCell() {
        return prdtCell;
    }

    public void setPrdtCell(String prdtCell) {
        this.prdtCell = prdtCell;
    }

    public String getAlarmPoint() {
        return alarmPoint;
    }

    public void setAlarmPoint(String alarmPoint) {
        this.alarmPoint = alarmPoint;
    }

    public String getAlarmFlag() {
        return alarmFlag;
    }

    public void setAlarmFlag(String alarmFlag) {
        this.alarmFlag = alarmFlag;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getPreviousValue() {
        return previousValue;
    }

    public void setPreviousValue(String previousValue) {
        this.previousValue = previousValue;
    }

    public String getNowValue() {
        return nowValue;
    }

    public void setNowValue(String nowValue) {
        this.nowValue = nowValue;
    }

    public Integer getLimitValue() {
        return limitValue;
    }

    public void setLimitValue(Integer limitValue) {
        this.limitValue = limitValue;
    }

    public String getMeasUnit() {
        return measUnit;
    }

    public void setMeasUnit(String measUnit) {
        this.measUnit = measUnit;
    }

    public Integer getInShelved() {
        return inShelved;
    }

    public void setInShelved(Integer inShelved) {
        this.inShelved = inShelved;
    }

    public Integer getInSuppressed() {
        return inSuppressed;
    }

    public void setInSuppressed(Integer inSuppressed) {
        this.inSuppressed = inSuppressed;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getParameter() {
        return parameter;
    }

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getActive() {
        return active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getAcked() {
        return acked;
    }

    public void setAcked(String acked) {
        this.acked = acked;
    }

    public String getDcsName() {
        return dcsName;
    }

    public void setDcsName(String dcsName) {
        this.dcsName = dcsName;
    }

    public Integer getPriorityId() {
        return priorityId;
    }

    public void setPriorityId(Integer priorityId) {
        this.priorityId = priorityId;
    }

    public Long getReson() {
        return reson;
    }

    public void setReson(Long reson) {
        this.reson = reson;
    }


    public String getResonName() {
        if(reson == null){
            return "";
        }else {
            return CommonEnum.MeasUnitUnmatchEnum.getName(reson.intValue());
        }
    }

    public void setResonName(String resonName) {
        this.resonName = resonName;
    }

    public String getAlarmPointReasonName() {
        if(reson == null){
            return "";
        }else {
            return CommonEnum.AlarmPointUnmatchEnum.getName(reson.intValue());
        }
    }

    public void setAlarmPointReasonName(String alarmPointReasonName) {
        this.alarmPointReasonName = alarmPointReasonName;
    }

    public Long getOpcCode() {
        return opcCode;
    }

    public void setOpcCode(Long opcCode) {
        this.opcCode = opcCode;
    }
}

