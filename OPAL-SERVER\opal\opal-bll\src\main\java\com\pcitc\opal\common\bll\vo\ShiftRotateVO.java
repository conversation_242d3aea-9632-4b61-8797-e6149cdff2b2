package com.pcitc.opal.common.bll.vo;

import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;

/*
 * 轮班表实体
 * 模块编号：pcitc_opal_bll_class_ShiftRotateVO
 * 作       者：xuelei.wang
 * 创建时间：2018-07-21
 * 修改编号：1
 * 描       述：轮班表实体
 */
public class ShiftRotateVO extends BaseResRep implements Serializable {
    /**
     * 轮班表ID
     */
    private Long shiftRotateId;
    /**
     * 轮班表Code
     */
    private String shiftRotateCode;
    /**
     * 轮班表名称
     */
    private String name;
    /**
     * 轮班域编码
     */
    private String shiftAreaCode;

    public Long getShiftRotateId() {
        return shiftRotateId;
    }

    public void setShiftRotateId(Long shiftRotateId) {
        this.shiftRotateId = shiftRotateId;
    }

    public String getShiftRotateCode() {
        return shiftRotateCode;
    }

    public void setShiftRotateCode(String shiftRotateCode) {
        this.shiftRotateCode = shiftRotateCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShiftAreaCode() {
        return shiftAreaCode;
    }

    public void setShiftAreaCode(String shiftAreaCode) {
        this.shiftAreaCode = shiftAreaCode;
    }
}
