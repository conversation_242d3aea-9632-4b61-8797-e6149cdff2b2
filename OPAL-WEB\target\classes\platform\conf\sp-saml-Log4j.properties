log4j.rootLogger=DEBUG,A1,C3


### 应用于控制台 

log4j.appender.A1=com.sinopec.siam.apache.log4j.ConsoleAppender 
log4j.appender.A1.Threshold=DEBUG
log4j.appender.A1.Target=System.out
log4j.appender.A1.layout=com.sinopec.siam.apache.log4j.PatternLayout 
log4j.appender.A1.layout.ConversionPattern=[%-5p] %d{yyyy-MM-dd HH\:mm\:ss,SSS}\:%c[%M][line\:%L] %m%n
#log4j.appender.A1.logfile.File=d:/siamlogs/a.log
#log4j.appender.CONSOLE.layout.ConversionPattern=[start]%d{DATE}[DATE]%n%p[PRIORITY]%n%x[NDC]%n%t[THREAD] n%c[CATEGORY]%n%m[MESSAGE]%n%n 


#应用于文件 

#log4j.appender.B2=com.sinopec.siam.apache.log4j.FileAppender 
#log4j.appender.B2.Threshold=WARN
#log4j.appender.B2.File=logs/hrcpom_ERROR.log 
#log4j.appender.B2.Append=false 
#log4j.appender.B2.layout=com.sinopec.siam.apache.log4j.PatternLayout 
#log4j.appender.B2.layout.ConversionPattern=[%-5p] %d{yyyy-MM-dd HH\\\:mm\\\:ss}\\\: %c %m%n




#周期性生成日志
#每天产生一个日志文件
# 						1)'.'yyyy-MM: 每月
#                     2)'.'yyyy-ww: 每周 
#                     3)'.'yyyy-MM-dd: 每天
#                     4)'.'yyyy-MM-dd-a: 每天两次
#                     5)'.'yyyy-MM-dd-HH: 每小时
#                     6)'.'yyyy-MM-dd-HH-mm: 每分钟

com.sinopec.siam.apache.log4j.DailyRollingFileAppender
log4j.appender.C3=com.sinopec.siam.apache.log4j.DailyRollingFileAppender 
log4j.appender.C3.Threshold=DEBUG  
#log4j.appender.C3.File=logs/server1/sipc_sso.log
log4j.appender.C3.Encoding=utf-8
log4j.appender.C3.File=logs/gateway/saml_auth.log
log4j.appender.C3.DatePattern='.'yyyy-MM-dd
log4j.appender.C3.layout=com.sinopec.siam.apache.log4j.PatternLayout   
log4j.appender.C3.layout.ConversionPattern=[%-5p] %d{yyyy-MM-dd HH\:mm\:ss,SSS}\:%c[%M][line\:%L] %m%n


log4j.logger.com.sinopec.siam.opensaml.saml2.metadata.provider.AbstractReloadingMetadataProvider=DEBUG,C4
log4j.appender.C4=com.sinopec.siam.apache.log4j.DailyRollingFileAppender 
#log4j.appender.C3.File=logs/server1/sipc_sso.log
log4j.appender.C4.Encoding=utf-8
log4j.appender.C4.File=logs/gateway/metadata_down.log
log4j.appender.C4.DatePattern='.'yyyy-MM-dd
log4j.appender.C4.layout=com.sinopec.siam.apache.log4j.PatternLayout   
log4j.appender.C4.layout.ConversionPattern=[%-5p] %d{yyyy-MM-dd HH\:mm\:ss,SSS}\:%c[%M][line\:%L] %m%n

#timer_metadata.log

# 应用于文件回滚 

#log4j.appender.R=com.sinopec.siam.apache.log4j.RollingFileAppender 
#log4j.appender.R.Threshold=DEBUG
#log4j.appender.R.File=logs/xlfeng_INFO.log
#log4j.appender.R.Append=true 
#log4j.appender.R.MaxFileSize=10MB
#log4j.appender.R.MaxBackupIndex=10 
#log4j.appender.R.layout=com.sinopec.siam.apache.log4j.PatternLayout 
#log4j.appender.R.layout.ConversionPattern=[%-5p] %d{yyyy-MM-dd HH\:mm\:ss}\: %c %m%n

