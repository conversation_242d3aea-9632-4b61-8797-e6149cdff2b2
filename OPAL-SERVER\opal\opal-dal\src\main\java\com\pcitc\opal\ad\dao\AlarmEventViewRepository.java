package com.pcitc.opal.ad.dao;

import com.pcitc.opal.ad.pojo.AlarmEventView;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * 事件类型每10分钟数据视图实体的Repository的JPA标准接口
 * 模块编号：pcitc_opal_dal_interface_AlarmEventViewRepository
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/19
 * 修改编号：1
 * 描    述：事件类型每10分钟数据视图实体的Repository实现
 */
public interface AlarmEventViewRepository extends JpaRepository<AlarmEventView, Long>, AlarmEventViewRepositoryCustom {

}
