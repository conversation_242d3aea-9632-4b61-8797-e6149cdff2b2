package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.AlarmExamineRec;

import java.util.Date;

public interface AlarmExamineRecRepositoryCustom {

    PaginationBean<AlarmExamineRec> getAlarmExamineRec(String[] unitIds, Long[] prdtCellIds, String tag, Integer alarmFlagId, Integer priority, Date startTime, Date endTime, Integer alarmDuration, Integer examineStatus, Pagination page);
}
