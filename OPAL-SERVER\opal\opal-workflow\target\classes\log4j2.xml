<?xml version="1.0" encoding="UTF-8"?>
<!-- Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，
 你会看到log4j2内部各种详细输出。可以设置成OFF(关闭)或Error(只输出错误信息)
-->
<Configuration status="OFF">
    <Properties>
        <Property name="fileName">../../opal-logs/workflow</Property>
        <Property name="backup">../../opal-logs/workflow/backup</Property>
    </Properties>
    <Appenders>
        <Console name="console" target="SYSTEM_OUT">
            <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
        </Console>
        <RollingRandomAccessFile name="infoFile" fileName="${fileName}/workflow.log" immediateFlush="false"
                                 filePattern="${backup}/opalservice-%i.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [%t] %-5level %logger{36} %L %M - %msg%xEx%n" />
            <Policies>
                <SizeBasedTriggeringPolicy size="1 MB"/>
            </Policies>
            <Filters>
                <!-- 记录info级别以上信息,调试时可以设置成info debug等 -->
                <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY" />
            </Filters>
             <DefaultRolloverStrategy max="5"/>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="errorFile" fileName="${fileName}/workflow-error.log" immediateFlush="false"
                                 filePattern="${backup}/opalservice-error-%i.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [%t] %-5level %logger{36} %L %M - %msg%xEx%n" />
            <Policies>
                <SizeBasedTriggeringPolicy size="1 MB"/>
            </Policies>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY" />
            </Filters>
            <DefaultRolloverStrategy max="5"/>
        </RollingRandomAccessFile>
    </Appenders>
    <Loggers>
        <!-- AsyncRoot - 异步记录日志 - 需要LMAX Disruptor的支持 -->
        <AsyncRoot level="debug" additivity="false">
            <AppenderRef ref="console"/>
            <AppenderRef ref="infoFile"/>
            <AppenderRef ref="errorFile"/>
        </AsyncRoot>
    </Loggers>
</Configuration>