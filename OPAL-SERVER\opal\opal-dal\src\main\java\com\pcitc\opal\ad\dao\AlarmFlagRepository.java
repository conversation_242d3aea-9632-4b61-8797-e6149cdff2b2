package com.pcitc.opal.ad.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.ad.pojo.AlarmFlag;

/*
 * AlarmFlag实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmFlagRepository
 * 作       者：kun.zhao
 * 创建时间：2017/10/19 
 * 修改编号：1
 * 描       述：AlarmFlag实体的Repository的JPA标准接口  
 */
public interface AlarmFlagRepository extends JpaRepository<AlarmFlag, Long>, AlarmFlagRepositoryCustom {
	
}
