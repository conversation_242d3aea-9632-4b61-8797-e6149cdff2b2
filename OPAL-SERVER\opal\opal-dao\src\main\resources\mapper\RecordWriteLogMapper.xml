<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.ad.dao.RecordWriteLogDAO">

    <select id="getHistoryData" resultType="com.pcitc.opal.ad.entity.RecordWriteLogEntity">
        select * from t_record_writelog where is_history = 1 and company_id = #{copmanyId}
    </select>

</mapper>
