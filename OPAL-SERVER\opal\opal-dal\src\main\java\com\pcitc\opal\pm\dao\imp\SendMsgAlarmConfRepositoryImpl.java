package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.SendMsgAlarmConfRepositoryCustom;
import com.pcitc.opal.pm.dao.SystRunParaConfRepository;
import com.pcitc.opal.pm.pojo.AlarmRecVo;
import com.pcitc.opal.pm.pojo.SendMsgAlarmConf;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SendMsgAlarmConfRepositoryImpl extends BaseRepository<SendMsgAlarmConf, Long> implements SendMsgAlarmConfRepositoryCustom {

    @Autowired
    SystRunParaConfRepository systRunParaConfRepository;

    @Transactional
    @Override
    public List<AlarmRecVo> getMsgAlarmConf(Integer companyId) {
        //系统参数延迟时间分钟
        String delayTime = systRunParaConfRepository.findParaValueByCode("AlarmEventMsgDelayedTime", companyId);



        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //报警开始时间
        long start = System.currentTimeMillis() - 60 * 60 * 1000 - Long.parseLong(delayTime) * 60 * 1000;
        String startDate = sdf.format(start);
        //报警结束时间
        long end = System.currentTimeMillis() - Long.parseLong(delayTime) * 60 * 1000;
        String endDate = sdf.format(end);


        String hql = "select new com.pcitc.opal.pm.pojo.AlarmRecVo(t3.alarmRecId,t3.prdtCellId,t3.alarmPointId,t3.alarmFlagId,t3.recoveryTime,t3.alarmTime,t4.tag,t4.location,t5.name, t6.mobileListIds, t6.timeInterval, t4.instrmtPriority, t7.sname, t3.unitName) " +
                " from AlarmRecVo t3, AlarmPoint t4, AlarmFlag t5, SendMsgAlarmConf t6 ,Unit t7 " +
                " where t3.alarmFlagId = t5.alarmFlagId and t3.alarmPointId = t4.alarmPointId and t4.inUse = 1 " +
                " and t4.alarmPointId = t6.alarmPointId and t6.alarmFlagId = t3.alarmFlagId " +
                " and t3.unitName = t7.stdCode" +
                " and (t3.prdtCellId, t3.alarmPointId, t3.alarmFlagId) in " +
                " (select distinct t1.prdtCellId, t2.alarmPointId, t2.alarmFlagId from AlarmPoint t1 , SendMsgAlarmConf t2 where t1.alarmPointId = t2.alarmPointId and t1.inUse = '1' and t1.inSendMsg = '1' and t1.companyId = "+ companyId + "and t2.companyId = "+ companyId + ")" +
                " and t3.alarmTime between  str_to_date('"+ startDate +"','%Y-%m-%d %H:%i:%s') and  str_to_date('"+ endDate +"','%Y-%m-%d %H:%i:%s') " +
                " and t3.companyId = "+ companyId + "and t4.companyId = " + companyId + " and t6.companyId = " + companyId;

        TypedQuery query = this.getEntityManager().createQuery(hql, AlarmRecVo.class);
        return query.getResultList();
    }

    @Transactional
    @Override
    public Integer updateTimeIntervalBySendMsgAlarmConf(Long timeInterval, Long sendMsgAlarmConfId) {
        String sql = "update t_pm_sendmsgalarmconf set TIME_INTERVAL = :timeInterval where SENDMSG_ALARM_CONF_ID = :sendMsgAlarmConfId";

        Query nativeQuery = this.getEntityManager().createNativeQuery(sql);
        nativeQuery.setParameter("timeInterval", timeInterval)
                .setParameter("sendMsgAlarmConfId", sendMsgAlarmConfId);
        return nativeQuery.executeUpdate();
    }

    @Transactional
    @Override
    public Integer updateSendMsgAlarmConfBySendMsgAlarmConfId(Long[] sendMsgAlarmConfId, String mobileBookId, Long timeInterval) {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE SendMsgAlarmConf set timeInterval=:timeInterval , mobileListIds=:mobileBookId  where sendMsgAlarmConfId in (:ids) and companyId = :companyId");
        List<Long> ids = Arrays.asList(sendMsgAlarmConfId);
        return getEntityManager().createQuery(sql.toString())
                .setParameter("timeInterval", timeInterval)
                .setParameter("mobileBookId", mobileBookId)
                .setParameter("ids", ids)
                .setParameter("companyId", new CommonProperty().getCompanyId())
                .executeUpdate();
    }

    @Override
    public Long[] findAlarmPointIdBySendMsgAlarmConfId(Long[] sendMsgAlarmConfId) {
        Map<String, Object> param = new HashMap<>();
        StringBuilder hql = new StringBuilder();
        hql.append(" select DISTINCT alarmPointId from SendMsgAlarmConf where sendMsgAlarmConfId in (:ids) ");
        param.put("ids", Arrays.asList(sendMsgAlarmConfId));
        Query query = getEntityManager().createQuery(hql.toString());
        setParameterList(query, param);
        List<Long> resultList = query.getResultList();
        Long[] arr = new Long[resultList.size()];
        return resultList.toArray(arr);
    }

    @Override
    public PaginationBean<SendMsgAlarmConf> findSendMsgAlarmConfByInfo(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inSendMsg, Integer timeInterval, Pagination page){

        Map<String,Object> param = new HashMap<String,Object>();


        String sql = "select new com.pcitc.opal.pm.pojo.SendMsgAlarmConf(t1, pt, un) " +
                "from SendMsgAlarmConf t1 " +
                "inner join  t1.alarmFlag af " +
                "inner join  t1.alarmPoint ap "+
                "inner join PrdtCell pt on ap.prdtCellId = pt.prdtCellId " +
                "inner join Unit un on pt.unitId = un.stdCode " +
                "where 1=1 ";

        // 装置
        if (ArrayUtils.isNotEmpty(unitCodes)) {
            sql+=(" and pt.unitId in (:unitIds) ");
            List<String> unitIdsList = Arrays.asList(unitCodes);
            param.put("unitIds", unitIdsList);
            // 生产单元
            if (unitCodes.length == 1 && ArrayUtils.isNotEmpty(prdtCellIds)) {
                sql+=(" and pt.prdtCellId in (:prdtCellIds) ");
                List<Long> prdtCellIdsList = Arrays.asList(prdtCellIds);
                param.put("prdtCellIds", prdtCellIdsList);
            }
        }
        // 位号
        if (!StringUtils.isEmpty(tag)) {
            sql+=("and upper(ap.tag) like upper(:tag) escape '/' ");
            param.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
        }
        //是否发送短信
        if (null != inSendMsg && inSendMsg != -1) {
            sql+=(" and ap.inSendMsg =(:inSendMsg) ");
            param.put("inSendMsg",inSendMsg);
        }
        //时间间隔
        if(timeInterval !=null ){
            sql+=(" and t1.timeInterval = (:timeInterval) ");
            param.put("timeInterval",Long.valueOf(timeInterval.toString()));
        }

        return this.findAll(page, sql, param);
    }

    @Override
    public List<Object[]> findSendMsgAlarmConfBySendMsgAlarmConfId(Long[] alarmMsgConfId) {
        StringBuilder sql = new StringBuilder();
        Map<String,Object> param = new HashMap<String,Object>();
        sql.append("select ac.SENDMSG_ALARM_CONF_ID," +
                "  ac.alarm_point_id," +
                "  ac.alarm_flag_id," +
                "  ac.MOBILE_LIST_IDS," +
                "  ac.time_interval, " +
                "  ap.tag," +
                "  pc.name as prdtCellName," +
//                "  pc.prdtcell_id," +
                "  af.name as flagName," +
                "  pu.name as unitName, \n" +
                "  ap.in_sendmsg "+
                "  from t_pm_sendmsgalarmconf ac\n" +
                "  left join t_pm_alarmpoint ap\n" +
                "    on ac.alarm_point_id = ap.alarm_point_id\n" +
                "  left join t_pm_prdtcell pc\n" +
                "    on ap.prdtcell_id = pc.prdtcell_id\n" +
                "  left join t_ad_alarmflag af\n" +
                "    on ac.alarm_flag_id = af.alarm_flag_id\n" +
                "  left join t_pm_unit pu\n" +
                "    on pc.unit_code = pu.std_code\n" +
                "    where ap.in_use=1 " +
                " and ac.company_id = :companyId and ap.company_id = :companyId " +
                " and pc.company_id = :companyId and pu.company_id = :companyId ");
        sql.append(" and ac.SENDMSG_ALARM_CONF_ID in (:alarmMsgConfIds)");

        param.put("companyId", new CommonProperty().getCompanyId());
        List<Long> alarmMsgConfIds = Arrays.asList(alarmMsgConfId);
        param.put("alarmMsgConfIds",alarmMsgConfIds);

        Query query=getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query,param);
        List<Object[]> list = query.getResultList();
        return list;
    }

    @Override
    public SendMsgAlarmConf getAlarmMsgConfigByInfo(Long ap, Long af, Long timeInterval) {
        try {
            StringBuilder hql = new StringBuilder();;
            Map<String,Object> param = new HashMap<>();
            hql.append(" from  SendMsgAlarmConf where alarmPointId=:alarmPointId  and  alarmFlagId=:alarmFlagId and timeInterval = :timeInterval");
            param.put("alarmPointId", (ap));
            param.put("alarmFlagId",(af));
            param.put("timeInterval",(timeInterval));
            TypedQuery<SendMsgAlarmConf>  query = this.getEntityManager().createQuery(hql.toString(),SendMsgAlarmConf.class);
            this.setParameterList(query,param);
            List<SendMsgAlarmConf> resultList = query.getResultList();
            if (CollectionUtils.isEmpty(resultList)){
                return null;
            }else {
                return resultList.get(0);
            }
        } catch (Exception e) {
            return null;
        }

    }
}
