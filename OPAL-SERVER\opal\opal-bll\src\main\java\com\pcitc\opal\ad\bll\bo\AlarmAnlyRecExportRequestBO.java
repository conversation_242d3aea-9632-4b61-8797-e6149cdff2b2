package com.pcitc.opal.ad.bll.bo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;

@Getter
@Setter
public class AlarmAnlyRecExportRequestBO {

    private String[] unitIds;

    private Long[] prdtCellIds;

    private Long alarmFlagId;

    private String tag;

    private Integer alarmStatus;

    private Integer anlyStatus;

    private Integer[] prioritys;

    private Integer[] monitorType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
