package com.pcitc.opal.ac.dao;

import java.util.List;

import com.pcitc.opal.ac.pojo.AlarmChangePlanApro;
import com.pcitc.opal.common.CommonResult;

/*
 * 报警变更方案审批记录实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmChangePlanAproRepositoryCustom
 * 作       者：kun.zhao
 * 创建时间：2017/01/19
 * 修改编号：1
 * 描       述：报警变更方案审批记录实体的Repository的JPA自定义接口 
 */
public interface AlarmChangePlanAproRepositoryCustom {
	
	/**
	 * 删除报警变更方案审批记录实体
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangePlanAproIds 报警变更方案审批记录Id数组
	 * @return 返回结果信息类
	 */
	CommonResult deleteAlarmChangePlanApro(Long[] alarmChangePlanAproIds);

	
	/**
	 * 通过报警变更方案Id获取多条数据
	 * 
	 * <AUTHOR> 2018-01-19 报警变更方案Id数组
	 * @param alarmChangePlanIds 报警变更方案Id数组
	 * @return 报警变更方案审批记录实体集合
	 */
	List<AlarmChangePlanApro> getalarmChangePlanAproByPlanIds(Long[] alarmChangePlanIds);
	
	/**
	 * 审批信息
	 * 
	 * <AUTHOR> 2018-01-23 
	 * @param planId 报警变更方案ID
	 * @return List<AlarmChangePlanApro> 返回AlarmChangePlanApro实体集合
	 */
	List<AlarmChangePlanApro> getAlarmChangePlanApro (Long planId);
	
	/**
	 * 审核信息
	 * 
	 * <AUTHOR> 2018-01-30 
	 * @param planId 报警变更方案ID
	 * @return AlarmChangePlanApro 返回AlarmChangePlanApro实体
	 */
	AlarmChangePlanApro getAlarmChangePlanAproByPlanId (Long planId);

	/**
	 * 变更方案审批记录
	 *
	 * <AUTHOR> 2018-3-13
	 * @param alarmChangePlanApro 报警变更方案审批记录实体
	 * @return 返回结果信息类
	 */
	CommonResult addAlarmChangePlanApro(AlarmChangePlanApro alarmChangePlanApro);
}
