package com.pcitc.opal.as.bll;

import com.pcitc.opal.as.bll.entity.AlarmStdManagmtEntity;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import org.springframework.stereotype.Service;

import java.util.Date;

/*
 * 报警制度管理业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmStdManagmtService
 * 作    者：jiangtao.xue
 * 创建时间：2018/02/28
 * 修改编号：1
 * 描    述：报警制度管理业务逻辑层接口
 */
@Service
public interface AlarmStdManagmtService {

    /**
     * 新增报警制度管理维护数据
     *
     * <AUTHOR> 2018-02-28
     * @param alarmStdManagmtEntity 报警制度管理实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult addAlarmStdManagmt(AlarmStdManagmtEntity alarmStdManagmtEntity) throws Exception;

	/**
	 * 删除报警制度管理维护数据
	 *
	 * <AUTHOR> 2018-02-28
	 * @param data 报警制度管理实体id数组
	 * @return 处理结果信息
	 * @throws Exception
	 */
	CommonResult deleteAlarmStdManagmt(Long[] data) throws Exception;

    /**
     * 报警制度管理更新数据
     *
     * <AUTHOR> 2018-02-28
     * @param alarmStdManagmtEntity 报警制度管理实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult updateAlarmStdManagmt(AlarmStdManagmtEntity alarmStdManagmtEntity) throws Exception;

    /**
	 * 获取报警制度管理分页数据
	 *
	 * <AUTHOR> 2018-02-28
	 * @param name      名称
	 * @param catgr     分类
	 * @param startTime 上传时间范围起始
	 * @param endTime   上传时间范围结束
	 * @param page      分页对象
	 * @return 报警制度管理分页对象
	 * @throws Exception
	 */
	PaginationBean<AlarmStdManagmtEntity> getAlarmStdManagmt(String name, Integer catgr, Date startTime, Date endTime,
			Pagination page) throws Exception;

	/**
	 * 根据报警制度管理ID获取单条数据信息
	 *
	 * <AUTHOR> 2018-02-28
	 * @param alarmStdManagmtId 报警制度管理ID
	 * @return 报警制度管理实体
	 * @throws Exception
	 */
	AlarmStdManagmtEntity getSingleAlarmStdManagmt(Long alarmStdManagmtId) throws Exception;
	
}
