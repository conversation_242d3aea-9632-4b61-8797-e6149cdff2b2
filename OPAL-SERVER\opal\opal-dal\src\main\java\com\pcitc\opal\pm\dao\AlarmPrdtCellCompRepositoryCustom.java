package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.dao.imp.DataServerMonDTO;
import com.pcitc.opal.pm.dao.imp.DataServerMonVO;
import com.pcitc.opal.pm.pojo.AlarmPrdtCellComp;
import com.pcitc.opal.pm.pojo.PrdtCell;

import java.util.List;

/*
 * AlarmPrdtCellComp实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmPrdtCellCompRepositoryCustom
 * 作	者：jiangtao.xue
 * 创建时间：2018/04/04
 * 修改编号：1
 * 描	述：AlarmPrdtCellComp实体的Repository的JPA自定义接口 
 */
public interface AlarmPrdtCellCompRepositoryCustom {

	/**
	 * 校验数据
	 *
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompEntity 计量单位实体
	 * @return 返回结果信息类
	 */
	CommonResult alarmPrdtCellCompValidation(AlarmPrdtCellComp alarmPrdtCellCompEntity);

	/**
	 * 新增数据
	 *
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompEntity
	 *            计量单位实体
	 * @return 返回结果信息类
	 */
	CommonResult addAlarmPrdtCellComp(AlarmPrdtCellComp alarmPrdtCellCompEntity);

	/**
	 * 删除数据
	 *
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompIds
	 *            计量单位ID集合
	 * @return 返回结果信息类
	 */
	CommonResult deleteAlarmPrdtCellComp(Long[] alarmPrdtCellCompIds);

	/**
	 * 更新数据
	 *
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompEntity
	 * @return 返回结果信息类
	 */
	CommonResult updateAlarmPrdtCellComp(AlarmPrdtCellComp alarmPrdtCellCompEntity);

	/**
	 * 获取单条数据
	 *
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompId
	 *            计量单位ID
	 * @return 计量单位实体
	 */
	AlarmPrdtCellComp getSingleAlarmPrdtCellComp(Long alarmPrdtCellCompId);

	/**
	 * 获取多条数据
	 *
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompIds
	 *            计量单位ID集合
	 * @return 计量单位实体集合
	 */
	List<AlarmPrdtCellComp> getAlarmPrdtCellComp(Long[] alarmPrdtCellCompIds);

	/**
	 * 获取分页数据
	 *
	 * <AUTHOR> 2018-04-04
	 * @param dcsCodeId DCS名称
	 * @param opcCodeId OPC名称
	 * @param prdtCellSource 源报警生产单元
	 * @param unitCodes 装置编码集合
	 * @param prdtCellIds   本系统生产单元
	 * @param inUse   是否使用
	 * @param page 翻页实现类
	 * @return 计量单位实体集合
	 */
	PaginationBean<AlarmPrdtCellComp> getAlarmPrdtCellComp(Long dcsCodeId,Long opcCodeId, String prdtCellSource, String[] unitCodes,Long[] prdtCellIds, Integer inUse, Pagination page);
	/**
	 *根据生产单元获取源报警生产单元实体
	 *
	 * <AUTHOR> 2018-04-17
	 * @param prdtCellSource 源报警生产单元
	 * @param opcCode opc编码
	 * @param dcsCode dcs编码
	 * @throws Exception 
	 * @return AlarmPrdtCellComp  报警生产单元对照实体
	 */
	AlarmPrdtCellComp getPrdtCellInPrdtCellComp(String prdtCellSource,Long opcCode,Long dcsCode) throws Exception;

	List<DataServerMonVO> getDataServerMon(String unitCodes);

	List<PrdtCell> getPrdtCellByDcsName(Long dcsCodeId);
}
