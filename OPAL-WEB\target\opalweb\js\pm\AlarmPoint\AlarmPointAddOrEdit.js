var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var addUrl = OPAL.API.pmUrl + '/alarmPoints';
var getSingleUrl = OPAL.API.pmUrl + '/alarmPoints';
var checkPointUrl = OPAL.API.pmUrl + '/alarmPoints/getAlarmPointIsUseInAlarmEvent';
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.Refresh;
var UnitPrdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';; //装置
var MeanUnitUrl = OPAL.API.commUrl + "/getMeasUnitList"; //计量单位
var AlarmPointTypeUrl = OPAL.API.commUrl + '/getAlarmPointTypeList'; //报警点类型
var monitorTypeUrl = OPAL.API.commUrl + "/getMonitorTypeList"; //专业
var instrmtTypeUrl = OPAL.API.commUrl + "/getInstrmtTypeList"; //仪表类型
var VirtualRealityFlagUrl = OPAL.API.commUrl + "/getVirtualRealityFlagList"; //虚实标记
var getInstrmtPriUrl = OPAL.API.pmUrl + "/alarmPoints/getInstrmtPriority"; //仪表优先级

$(function() {
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var page = {
        init: function() {
            this.bindUI();
            //初始化装置树
            page.logic.initUnitTree();
            //初始化检测类型列表
            page.logic.initMonitorType();
            //初始化仪表类型列表
            page.logic.initInstrmtType();
            //初始化虚实标记列表
            page.logic.initVirtualRealityFlag();
            //初始化报警点类型
            page.logic.initAlarmPointType();
            //初始化计量单位
            page.logic.initMeanUnit();
            // 初始化仪表优先级
            page.logic.initInstrmtPriority();
        },
        bindUI: function() {
            $('#saveAddModal').click(function() {
                page.logic.save();
            });
            $('.closeBtn').click(function() {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer();
            })
            // $('#closePage').click(function() {
            //     window.pageLoadMode = PageLoadMode.None;
            //     page.logic.closeLayer();
            // })
            $(".specialValue").blur(function() {
                page.logic.setRank(this);
            })
            $(".top-and-bottom-style").click(function() {
                page.logic.include(this);
            })
        },
        logic: {
            include: function(t) {
                if (t.checked) {
                    var name = t.name;
                    var msg;
                    switch (name) {
                        case 'craftDownLimitInclude':
                            msg = '工艺卡片下限值是否包含应为空！';
                            break;
                        case 'craftUpLimitInclude':
                            msg = '工艺卡片上限值是否包含应为空！';
                            break;
                        case 'interlockDownLimitInclude':
                            msg = '联锁下限值是否包含应为空！';
                            break;
                        case 'interlockUpLimitInclude':
                            msg = '联锁上限值是否包含应为空！';
                            break;
                    }
                    if ($(t).prev().val() == '') {
                        layer.msg(msg);
                        t.checked = false;
                    }
                }
            },
            /**
             * 设置级别
             */
            setRank: function(t) {
                if ($("#" + t.id).val() == '') {
                    $("#" + t.id).next().attr('checked', false);
                }
                if ($('#craftUpLimitValue').val() != '' || $('#craftDownLimitValue').val() != '' ||
                    $('#interlockUpLimitValue').val() != '' || $('#interlockDownLimitValue').val() != '') {
                    $("#craftRankSpan").html('A');
                    $("#craftRank").val('1');
                } else {
                    $("#craftRankSpan").html('B');
                    $("#craftRank").val('2');
                }
            },
            /**
             * 工艺卡片限值,联锁限值校验
             * @return {[type]} [description]
             */
            validCraftLimitValue: function() {
                var culv = $("#craftUpLimitValue").val();
                var cdlv = $("#craftDownLimitValue").val();
                var iulv = $("#interlockUpLimitValue").val();
                var idlv = $("#interlockDownLimitValue").val();
                var culi = $("#craftUpLimitInclude").is(':checked');
                var cdli = $("#craftDownLimitInclude").is(':checked');
                var iuli = $("#interlockUpLimitInclude").is(':checked');
                var idli = $("#interlockDownLimitInclude").is(':checked');
                var level = $("#craftRankSpan").text();
                var messageC;
                var messageI;
                var message;
                if (culv != '' && cdlv != '') {
                    if (parseFloat(culv) < parseFloat(cdlv)) {
                        messageC = "工艺卡片上限值须大于等于工艺卡片下限值！";
                        layer.msg(messageC);
                        return '';
                    } else {
                        messageC = "工艺卡片值为" + cdlv + "～" + culv;
                    }
                } else if (culv != '' && cdlv == '') {
                    if (culi == true) {
                        messageC = "工艺卡片值≤" + culv;
                    } else {
                        messageC = "工艺卡片值<" + culv;
                    }
                } else if (culv == '' && cdlv != '') {
                    if (cdli == true) {
                        messageC = "工艺卡片值≥" + cdlv;
                    } else {
                        messageC = "工艺卡片值>" + cdlv;
                    }
                } else if (culv == '' && cdlv == '') {
                    messageC = "工艺卡片值为空";
                }
                if (iulv != '' && idlv != '') {
                    if (parseFloat(iulv) < parseFloat(idlv)) {
                        messageI = "联锁上限值须大于等于联锁下限值！";
                        layer.msg(messageI);
                        return '';
                    } else {
                        if(iuli == true && idli != true){
                            messageI = "联锁值为>" + iulv + ",≤" + idlv;
                        }else if(iuli != true && idli == true){
                            messageI = "联锁值为≥"+ iulv+",<"+idlv;
                        }else if(iuli != true && idli != true){
                            messageI = "联锁值为≥" + iulv+",≤" + idlv;
                        }else if(iuli == true && idli == true){
                            messageI = "联锁值为>" + iulv+",<" + idlv;
                        }
                    }
                } else if (iulv != '' && idlv == '') {
                    if (iuli == true) {
                        messageI = "联锁值>" + iulv;
                    } else {
                        messageI = "联锁值≥" + iulv;
                    }
                } else if (iulv == '' && idlv != '') {
                    if (idli == true) {
                        messageI = "联锁值<" + idlv;
                    } else {
                        messageI = "联锁值≤" + idlv;
                    }
                } else if (iulv == '' && idlv == '') {
                    messageI = "联锁值为空";
                }
                message = messageC + "；" + messageI + "；级别为" + level + "；" + "是否继续保存？";
                return message;
            },
            /**
             * 保存
             */
            save: function() {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var message = page.logic.validCraftLimitValue();
                if (message == '') {
                    return;
                }
                layer.confirm(message, {
                        btn: ['是', '否']
                    }, function(index) {
                        layer.close(index);
                        $("#prdtCellId").combo('enable');
                        $("#unitId").combo('enable');
                        var data = OPAL.form.getETCollectionData('AddOrEditModal');
                        //处理提交类型
                        var ajaxType = "POST";
                        if (pageMode == PageModelEnum.NewAdd || pageMode == PageModelEnum.View) {
                            window.pageLoadMode = PageLoadMode.Reload;
                        } else if (pageMode == PageModelEnum.Edit) {
                            ajaxType = "PUT";
                            window.pageLoadMode = PageLoadMode.Refresh;
                        }

                        $.ajax({
                            url: addUrl,
                            async: false,
                            type: ajaxType,
                            data: JSON.stringify(data),
                            processData: false,
                            contentType: "application/json;charset=utf-8",
                            dataType: "text",
                            success: function(result, XMLHttpRequest) { //保存成功
                                if (result.indexOf('collection') < 0) {
                                    window.parent.pageLoadMode = PageLoadMode.Refresh;
                                    layer.msg("保存成功！", {
                                        time: 1000
                                    }, function() {
                                        page.logic.closeLayer(true);
                                    });
                                } else {
                                    layer.msg(result.collection.error.message)
                                }
                            },
                            error: function(result, jqXHR) { //保存失败
                                var errorResult = $.parseJSON(result.responseText);
                                layer.msg(errorResult.collection.error.message);
                            }
                        })
                    },
                    function(index) {
                        layer.close(index);
                    }
                );

            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect("unitId", commonUnitTreeUrl, "id", "parentId", "sname", {
                    multiple: false,
                    onlyLeafCheck: true,
                    data: {
                        'enablePrivilege': false
                    },
                    onSelect: function(node) {
                        OPAL.ui.getComboMultipleSelect("prdtCellId", UnitPrdtCellUrl, {
                            keyField: "prdtCellId",
                            valueField: "sname",
                            data: {
                                "unitId": node.id
                            },
                            treeviewConfig: {
                                multiple: false,
                                cascadeCheck: false,
                                onlyLeafCheck: true,
                                hasDownArrow: true,
                                lines: false,
                                animate: false,
                            }
                        }, false, function() {
                            $("#prdtCellId").combotree('selectFirstRecord');
                        });
                    }
                }, false, function() {

                });
                $("#unitId").combotree("getValues");
            },
            /**
             * 初始化检测类型
             */
            initMonitorType: function() {
                OPAL.ui.getCombobox('monitorType', monitorTypeUrl, {
                    selectValue: 5,
                    async: false
                }, null);
            },
            /**
             * 初始化仪表类型
             */
            initInstrmtType: function() {
                OPAL.ui.getCombobox('instrmtType', instrmtTypeUrl, {
                    selectValue: 5,
                    async: false
                }, null);
            },
            /**
             * 初始化虚实标记
             */
            initVirtualRealityFlag: function() {
                OPAL.ui.getCombobox('virtualRealityFlag', VirtualRealityFlagUrl, {
                    selectValue: 0,
                    async: false
                }, null);
            },
            /**
             * 初始化计量单位
             */
            initMeanUnit: function() {
                OPAL.ui.getCombobox('measunitId', MeanUnitUrl, {
                    keyField: "measUnitId",
                    valueField: "name",
                    selectValue: 1,
                    async: false
                }, null);
            },
            /**
             * 初始化报警点类型
             */
            initAlarmPointType: function() {
                OPAL.ui.getCombobox('alarmPointTypeId', AlarmPointTypeUrl, {
                    keyField: "alarmPointTypeId",
                    valueField: "name",
                    selectValue: 99,
                    async: false
                }, null);
            },
            // 初始化仪表优先级
            initInstrmtPriority: function() {
                OPAL.ui.getCombobox('instrmtPriority', getInstrmtPriUrl, {
                    selectValue: 9,
                    async: false,
                    data: {
                        'isAll': false
                    }
                }, null);
            },
            /**
             * 初始化编辑数据
             */
            setData: function(data) {
                pageMode = data.pageMode;
                if (pageMode == PageModelEnum.NewAdd) {
                    $('input[name=inUse]').attr('disabled', 'disabled');
                    return;
                } else if (pageMode == PageModelEnum.View) {
                    $("#tag").val(data['tag']);
                    // $("#alarmPointHH").val(data['values'].hh);
                    // $("#alarmPointHI").val(data['values'].ph);
                    // $("#alarmPointLO").val(data['values'].pl);
                    // $("#alarmPointLL").val(data['values'].ll);
                    $("#unitId").combotree('setValue', data['unitId']);
                    page.logic.searchUnitPrdt(data["unitId"]);
                    $("#prdtCellId").combotree('setValue', data["prdtCellId"]);
                    $("#unitId").combo('disable');
                    $("#prdtCellId").combo('disable');
                    $("#tag").attr("disabled", true);
                    $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                    return;
                }
                $.ajax({
                    url: getSingleUrl + "/" + data.alarmPointId + "?now=Math.random()",
                    type: "get",
                    async: false,
                    dataType: "json",
                    success: function(data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        OPAL.form.setData('AddOrEditModal', entity);
                        if(!entity.instrmtPriority){
                            $("#instrmtPriority").val(9);
                        }
                        if (entity['craftRank'] == 1) {
                            $("#craftRankSpan").html('A');
                        } else {
                            $("#craftRankSpan").html('B');
                        }
                        page.logic.searchUnitPrdt(entity["unitId"]);
                        $("#prdtCellId").combotree('setValue', entity["prdtCellId"]);
                    },
                    complete: function(XMLHttpRequest, textStatus) {

                    },
                    error: function(XMLHttpRequest, textStatus) {

                    }
                });
                //编辑时，如果该报警点在<报警事件>表中已经使用，则装置生产单元不可修改。
                $.ajax({
                    url: checkPointUrl,
                    type: "get",
                    async: false,
                    dataType: "json",
                    data: {
                        'alarmPointId': data.alarmPointId
                    },
                    success: function(result) {
                        if (parseInt(result) > 0) {
                            $("#unitId").combo('disable');
                            $("#prdtCellId").combo('disable');
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    },
                    error: function(XMLHttpRequest, textStatus) {

                    }
                });
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect("prdtCellId", UnitPrdtCellUrl, {
                        keyField: "prdtCellId",
                        valueField: "sname",
                        async: false,
                        data: {
                            "unitId": unitId
                        },
                        treeviewConfig: {
                            multiple: false,
                            cascadeCheck: false,
                            onlyLeafCheck: true,
                            hasDownArrow: true,
                            lines: false
                        }
                    }, true,
                    function() {

                    }
                );
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function() {
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            formValidate: function() {
                //给combotree
                $('#unitId').next('.textbox').find('input').attr('name', 'unitId');
                $('#unitId').next('.textbox').addClass('form-control-tree');
                $('#prdtCellId').next('.textbox').find('input').attr('name', 'prdtCellId');
                $('#prdtCellId').next('.textbox').addClass('form-control-tree');
                jQuery.validator.addMethod("minNumber", function(value, element) {
                    var returnVal = true;
                    if (value != '') {
                        returnVal = OPAL.util.checkDecimalIsValid(value);
                    }
                    return returnVal;
                }, "小数点后最多为五位,总长度不能超过20位"); //验证错误信息
                OPAL.form.formValidate('AddOrEditModal', {
                    rules: {
                        prdtCellId: {
                            required: true
                        },
                        unitId: {
                            required: true
                        },
                        tag: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        location: {
                            rangelength: [0, 100]
                        },
                        pidCode: {
                            rangelength: [0, 100]
                        },
                        monitorType: {
                            required: true
                        },
                        measunitId: {
                            required: true
                        },
                        instrmtType: {
                            required: true
                        },
                        virtualRealityFlag: {
                            required: true
                        },
                        alarmPointHH: {
                            number: true,
                            minNumber: 5
                        },
                        alarmPointHI: {
                            number: true,
                            minNumber: 5
                        },
                        alarmPointLL: {
                            number: true,
                            minNumber: 5
                        },
                        alarmPointLO: {
                            number: true,
                            minNumber: 5
                        },
                        des: {
                            rangelength: [0, 1000]
                        },
                        craftUpLimitValue: {
                            number: true,
                            minNumber: 5
                        },
                        craftDownLimitValue: {
                            number: true,
                            minNumber: 5
                        },
                        interlockUpLimitValue: {
                            number: true,
                            minNumber: 5
                        },
                        interlockDownLimitValue: {
                            number: true,
                            minNumber: 5
                        },rtdbTag: {
                            rangelength: [0, 100]
                        },
                    },
                    messages: {
                        alarmPointHH: {
                            number: '请输入正确数值',

                        },
                        alarmPointHI: {
                            number: '请输入正确数值',

                        },
                        alarmPointLL: {
                            number: '请输入正确数值',

                        },
                        alarmPointLO: {
                            number: '请输入正确数值',
                        }
                    },
                })
            }
        }

    }
    page.init();
    window.page = page;
})