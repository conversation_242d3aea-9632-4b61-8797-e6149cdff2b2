package com.pcitc.opal.ac.bll.entity;

/*
 * 报警变更事项实体
 * 模块编号：pcitc_opal_bll_class_AlarmChangeItemValueEntity
 * 作    者：jiangtao.xue
 * 创建时间：2018-01-22
 * 修改编号：1
 * 描    述：报警变更事项实体
 */
public class AlarmChangeItemValueEntity {
    /**
     * 报警变更方案明细ID
     */
    private Long planDetailId;

    /**
     * 报警变更方案ID
     */
    private Long planId;

    /**
     * 报警点ID
     */
    private Long alarmPointId;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     * 变更前是否屏蔽（1是；0否）
     */
    private Integer beforeInSuppressed;

    /**
     * 变更前是否屏蔽描述（1是；0否）
     */
    private String beforeInSuppressedStr;

    /**
     * 变更前限值
     */
    private String beforeLimitValue;

    /**
     * 变更前优先级(1紧急；2重要；3一般)
     */
    private Integer beforePriority;

    /**
     * 变更前优先级名称
     */
    private String beforePriorityName;

    /**
     * 变更后是否屏蔽（1是；0否）
     */
    private Integer afterInSuppressed;

    /**
     * 变更后是否屏蔽描述（1是；0否）
     */
    private String afterInSuppressedStr;

    /**
     * 变更后限值
     */
    private String afterLimitValue;

    /**
     * 变更后优先级(1紧急；2重要；3一般)
     */
    private Integer afterPriority;

    /**
     * 变更后优先级名称
     */
    private String afterPriorityName;

    /**
     * 描述
     */
    private String remark;

    public Long getPlanDetailId() {
        return planDetailId;
    }

    public void setPlanDetailId(Long planDetailId) {
        this.planDetailId = planDetailId;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public Integer getBeforeInSuppressed() {
        return beforeInSuppressed;
    }

    public void setBeforeInSuppressed(Integer beforeInSuppressed) {
        this.beforeInSuppressed = beforeInSuppressed;
    }

    public String getBeforeInSuppressedStr() {
        return beforeInSuppressedStr;
    }

    public void setBeforeInSuppressedStr(String beforeInSuppressedStr) {
        this.beforeInSuppressedStr = beforeInSuppressedStr;
    }

    public String getBeforeLimitValue() {
        return beforeLimitValue;
    }

    public void setBeforeLimitValue(String beforeLimitValue) {
        this.beforeLimitValue = beforeLimitValue;
    }

    public Integer getBeforePriority() {
        return beforePriority;
    }

    public void setBeforePriority(Integer beforePriority) {
        this.beforePriority = beforePriority;
    }

    public String getBeforePriorityName() {
        return beforePriorityName;
    }

    public void setBeforePriorityName(String beforePriorityName) {
        this.beforePriorityName = beforePriorityName;
    }

    public Integer getAfterInSuppressed() {
        return afterInSuppressed;
    }

    public void setAfterInSuppressed(Integer afterInSuppressed) {
        this.afterInSuppressed = afterInSuppressed;
    }

    public String getAfterInSuppressedStr() {
        return afterInSuppressedStr;
    }

    public void setAfterInSuppressedStr(String afterInSuppressedStr) {
        this.afterInSuppressedStr = afterInSuppressedStr;
    }

    public String getAfterLimitValue() {
        return afterLimitValue;
    }

    public void setAfterLimitValue(String afterLimitValue) {
        this.afterLimitValue = afterLimitValue;
    }

    public Integer getAfterPriority() {
        return afterPriority;
    }

    public void setAfterPriority(Integer afterPriority) {
        this.afterPriority = afterPriority;
    }

    public String getAfterPriorityName() {
        return afterPriorityName;
    }

    public void setAfterPriorityName(String afterPriorityName) {
        this.afterPriorityName = afterPriorityName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
