var searchUrl = OPAL.API.afUrl + '/craftParaAlarmRate/getCraftParaAlarmRate';
var initChartsUrl = OPAL.API.afUrl + '/craftParaAlarmRate/getCraftParaAlarmRateCurve';
var getQueryTimeUrl = OPAL.API.commUrl + '/getQueryTime';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit"; //装置树
var getShowTimeUrl = OPAL.API.commUrl + '/getShowTime';
var numberTopUrl = OPAL.API.aaUrl + '/alarmNumberAssess/getAlarmNumberAssessTop20';
var alarmNumStattUrl = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumStattStatisticData';
var alarmDurationStattUrl = OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotal';
var dateTimeList = '';
var alarmTime = '';
var isLoading = true;
var mostFrequentNumberChart;
var craftParaAlarmReteCount = 0;
var dayAlarmCount = 0;
var peakAlarmRateCount = 0;
var hourAlarmCount = 0;
var totalAlarmNum = 0;
var nowDate = new Date(2020, 7, 7);
var lastWeekDate = new Date(2020, 7, 7);
lastWeekDate.setDate(nowDate.getDate() - 7);
var beforeLastWeekDate = new Date(lastWeekDate);
beforeLastWeekDate.setDate(lastWeekDate.getDate() - 7);
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            this.bindUi();
            // 初始化 报警时间的时间点
            page.logic.getQueryTime();
            //初始化日期
            page.logic.getShowTime();

            //获取装置
            page.logic.getAllUnit();

            //获取数据
            page.data.param.startTime = lastWeekDate;
            page.data.param.endTime = nowDate;

            page.logic.queryChartsData(page.data.param)

            page.logic.loadData(page.logic.drawBottom);

            //获取柱状图数据

        },
        bindUi: function () {
            //查询
            $('#btnSearch').click(function () {
                if (OPAL.util.checkDateIsValid() == true) {
                    isLoading = false;
                    page.logic.search();
                }
            })
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                mostFrequentNumberChart.resize();
            };
        },
        data: {
            param: {}
        },
        logic: {
            /**
             * 获得固定的时间点
             */
            getQueryTime: function () {
                $.ajax({
                    url: getQueryTimeUrl,
                    async: false,
                    data: '',
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        queryTimeValue = res[0].value;
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            getShowTime: function () {
                $.ajax({
                    url: getShowTimeUrl,
                    // async: true,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        getStartTime = dataArr[0].value.split(' ')[0];
                        getEndTime = dataArr[1].value.split(' ')[0];
                        //设置时间插件
                        page.logic.initTime();
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 设置日期插件
             */
            initTime: function () {
                var myDate = new Date();
                var start = laydate.render({
                    elem: '#startTime', //指定元素
                    type: 'date',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd', //日期格式
                    value: getStartTime,
                    max: getEndTime, //最大日期
                });
                var end = laydate.render({
                    elem: '#endTime',
                    type: 'date',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd',
                    value: getEndTime,
                    max: getEndTime,
                });
                $('#startTime').attr('maxDate', getEndTime)
                $('#endTime').attr('maxDate', getEndTime)
            },
            drawBottomCard: function (alarmData, title, unit, isPadding) {
                var paddingHtml = isPadding ? "padding-top: 20px;" : "";
                var itemHtml = "";

                for (var index = 0; index < 3; index++) {
                    var unitNameL = alarmData[index] ? alarmData[index].unitName : "";
                    var unitNameR = alarmData[alarmData.length - index - 1] ? alarmData[alarmData.length - index - 1].unitName : "";
                    var countL = alarmData[index] ? "<span class=\"good-color\">" + alarmData[index].count + "</span>" + unit + "" : "";
                    var countR =
                        alarmData[alarmData.length - index - 1] ? "<span class=\"warning-color\">" + alarmData[alarmData.length - index - 1].count + "</span>" + unit + "" : "";
                    var itemTemplate
                    if (index == 0) {
                        itemTemplate =
                            "<div class=\"col-md-3 align-center\" style=\"height: 40px;background-color: #EFF2F8;\">" + unitNameL + "</div><div class=\"col-md-3 align-center\" style=\"height: 40px;background-color: #EFF2F8;\">" + countL + "</div><div class=\"col-md-3 align-center\" style=\"height: 40px;background-color: #F5F5F5;\">" + unitNameR + "</div><div class=\"col-md-3 align-center\" style=\"height: 40px;background-color: #F5F5F5;\">" + countR + "</div>";
                    } else {
                        itemTemplate =
                            "<div class=\"col-md-3 align-center\" style=\"height: 40px;background-color: #EFF2F8;margin-top: 16px;\">" + unitNameL + "</div><div class=\"col-md-3 align-center\" style=\"height: 40px;background-color: #EFF2F8;margin-top: 16px;\">" + countL + "</div><div class=\"col-md-3 align-center\" style=\"height: 40px;background-color: #F5F5F5;margin-top: 16px;\">" + unitNameR + "</div><div class=\"col-md-3 align-center\" style=\"height: 40px;background-color: #F5F5F5;margin-top: 16px;\">" + countR + "</div>";
                    }
                    itemHtml += itemTemplate;
                }

                var templateHtml =
                    "<div class=\"col-md-4\" style=\"height: 50%;" + paddingHtml + "\"><div class=\"row\" style=\"height: 100%;padding-left: 10px;\"><div class=\"col-md-6\" style=\"height: 40px;\"><h3>" + title + "</h3></div><div class=\"col-md-6\" style=\"height: 40px;\"></div><div class=\"col-md-6\" style=\"height: 40px;\"><div style=\"width:7px;height:7px;border-radius:50%;background-color:#608dbd;display:inline-block\"></div><label style=\"color: #6084bd;\">&nbsp;&nbsp;前三名</label></div><div class=\"col-md-6\" style=\"height: 40px;\"><div style=\"width:7px;height:7px;border-radius:50%;background-color:#666666;display:inline-block\"></div><label style=\"color: #666666;\">&nbsp;&nbsp;后三名</label></div>" + itemHtml + "</div></div>";
                return templateHtml;
            },
            loadData: function (func) {
                // page.data.param = OPAL.form.getData("searchForm");
                // var unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                page.data.param.unitIds = null;
                page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                $.ajax({
                    url: searchUrl,
                    data: page.data.param,
                    dataType: "JSON",
                    type: 'GET',
                    success: function (result) {
                        $("#btnSearch").prop('disabled', false);
                        var dataArr = $.ET.toObjectArr(result);
                        func(dataArr)
                    },
                    error: function (result) {
                        $("#btnSearch").prop('disabled', false);
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            getAllUnit: function () {
                $.ajax({
                    url: commonUnitTreeUrl,
                    async: false,
                    data: '',
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        $("#allUnitCount").html(res ? res.length : 0);
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            calcMoM: function (data) {
                var downImgSrc = "../../../images/portal/down.png"
                var alarmCount = page.logic.searchAlarmCount()
                var lastWeekalarmCount = alarmCount[0] ? alarmCount[0].allTotalAlarmQuantity : 1;
                var alarmCountMoM = (1 - (totalAlarmNum / lastWeekalarmCount)) * 100;
                if (alarmCountMoM > 0) {
                    $("#totalAlarmNumPic").css("background-image", "url(" + downImgSrc + ")");
                }
                $("#alarmCountMoM").html(Math.abs(alarmCountMoM.toFixed(2)));

                alarmData = [];
                var lastWeekCraftParaAlarmReteCount = 0;
                var lastWeekHourAlarmCount = 0;
                var lastWeekPeakAlarmRateCount = 0;
                var lastWeekDayAlarmCount = 0;
                for (var index = 0; index < data.length; index++) {
                    lastWeekHourAlarmCount += data[index].avgAlarmRate * 6;
                    lastWeekPeakAlarmRateCount += data[index].peakAlarmRate;
                    lastWeekDayAlarmCount += data[index].alarmAmount;
                }
                lastWeekCraftParaAlarmReteCount += data[0].avgCraftParaAlarmRate;

                lastWeekCraftParaAlarmReteCount = lastWeekCraftParaAlarmReteCount == 0 ? 1 : lastWeekCraftParaAlarmReteCount;
                lastWeekHourAlarmCount = lastWeekHourAlarmCount == 0 ? 1 : lastWeekHourAlarmCount;
                lastWeekPeakAlarmRateCount = lastWeekPeakAlarmRateCount == 0 ? 1 : lastWeekPeakAlarmRateCount;
                lastWeekDayAlarmCount = lastWeekDayAlarmCount == 0 ? 1 : lastWeekDayAlarmCount;

                var craftParaAlarmReteCountMoM = (1 - (craftParaAlarmReteCount / lastWeekCraftParaAlarmReteCount)) * 100;
                if (craftParaAlarmReteCountMoM > 0) {
                    $("#craftParaAlarmReteCountPic").css("background-image", "url(" + downImgSrc + ")");
                }
                $("#craftParaAlarmReteCountMoM").html(Math.abs(craftParaAlarmReteCountMoM).toFixed(2));

                var hourAlarmCountMoM = (1 - (hourAlarmCount / lastWeekHourAlarmCount)) * 100;
                if (hourAlarmCountMoM > 0) {
                    $("#hourAlarmCountPic").css("background-image", "url(" + downImgSrc + ")");
                }
                $("#hourAlarmCountMoM").html(Math.abs(hourAlarmCountMoM).toFixed(2));

                var peakAlarmRateCountMoM = (1 - (peakAlarmRateCount / lastWeekPeakAlarmRateCount)) * 100;
                if (peakAlarmRateCountMoM > 0) {
                    $("#peakAlarmRateCountPic").css("background-image", "url(" + downImgSrc + ")");
                }
                $("#peakAlarmRateCountMoM").html(Math.abs(peakAlarmRateCountMoM).toFixed(2));

                var dayAlarmCountMoM = (1 - (dayAlarmCount / lastWeekDayAlarmCount)) * 100;
                if (dayAlarmCountMoM > 0) {
                    $("#dayAlarmCountPic").css("background-image", "url(" + downImgSrc + ")");
                }
                $("#dayAlarmCountMoM").html(Math.abs(dayAlarmCountMoM).toFixed(2));

            },
            drawBottom: function (data) {
                var alarmDataArray = [];
                //sortby false 从大到小
                var alarmDataSort = function (data, sortby) {
                    data = data.sort(
                        function (a, b) {
                            return sortby ? (a.count - b.count) : (b.count - a.count);
                        }
                    )
                    return data;
                }
                var alarmData = {};
                //报警个数
                var alarmCount = page.logic.searchAlarmCount();
                totalAlarmNum = alarmCount[0] ? alarmCount[0].allTotalAlarmQuantity : 0;
                $("#totalAlarmNum").html(totalAlarmNum);
                for (var index = 0; index < alarmCount.length; index++) {
                    alarmData = {};
                    alarmData.unitName = alarmCount[index].sname;
                    alarmData.count = alarmCount[index].totalAlarmQuantity;
                    alarmDataArray[index] = alarmData;
                }
                alarmDataArray = alarmDataSort(alarmDataArray, true);
                var alarmCountHtml = page.logic.drawBottomCard(alarmDataArray, "报警个数", "个", true);

                //报警时长
                alarmData = [];
                alarmDataArray = [];
                var aramTimeData = page.logic.searchAlarmTime()
                for (var index = 0; index < aramTimeData.length; index++) {
                    alarmData = {};
                    alarmData.unitName = aramTimeData[index].sname;
                    alarmData.count = (
                        parseFloat(aramTimeData[index].generalAlarmQuantity) +
                        parseFloat(aramTimeData[index].importantAlarmQuantity) +
                        parseFloat(aramTimeData[index].emergencyAlarmQuantity)).toFixed(2);
                    alarmDataArray[index] = alarmData;
                }
                alarmDataArray = alarmDataSort(alarmDataArray, true);
                var alarmTimeHtml = page.logic.drawBottomCard(alarmDataArray, "报警时长", "分钟", true);

                //工艺参数报警率 craftParaAlarmRate
                alarmData = [];
                alarmDataArray = [];
                for (var index = 0; index < data.length; index++) {
                    alarmData = {};
                    alarmData.unitName = data[index].unitCode;
                    alarmData.count = data[index].craftParaAlarmRate;
                    alarmDataArray[index] = alarmData;
                }
                craftParaAlarmReteCount = data[0].avgCraftParaAlarmRate;
                $("#craftParaAlarmReteCount").html(craftParaAlarmReteCount);
                alarmDataArray = alarmDataSort(alarmDataArray, false);
                var craftParaAlarmRateHtml = page.logic.drawBottomCard(alarmDataArray, "工艺参数报警率", "分", true);

                //时平均报警数 avgAlarmRate * 6
                alarmData = [];
                alarmDataArray = [];
                for (var index = 0; index < data.length; index++) {
                    alarmData = {};
                    alarmData.unitName = data[index].unitCode;
                    alarmData.count = (data[index].avgAlarmRate * 6).toFixed(2);
                    hourAlarmCount += data[index].avgAlarmRate * 6;
                    alarmDataArray[index] = alarmData;
                }
                $("#hourAlarmCount").html(hourAlarmCount.toFixed(2));
                alarmDataArray = alarmDataSort(alarmDataArray, true);
                var avgAlarmRateHtml = page.logic.drawBottomCard(alarmDataArray, "时平均报警数", "个/小时");

                //10分钟峰值报警数 peakAlarmRate
                alarmData = [];
                alarmDataArray = [];
                for (var index = 0; index < data.length; index++) {
                    alarmData = {};
                    alarmData.unitName = data[index].unitCode;
                    alarmData.count = data[index].peakAlarmRate;
                    peakAlarmRateCount += alarmData.count;
                    alarmDataArray[index] = alarmData;
                }
                $("#peakAlarmRateCount").html(peakAlarmRateCount);
                alarmDataArray = alarmDataSort(alarmDataArray, true);
                var avgHourAlarmRateHtml = page.logic.drawBottomCard(alarmDataArray, "10分钟峰值报警数", "个");

                //24小时持续报警数 alarmAmount
                alarmData = [];
                alarmDataArray = [];
                for (var index = 0; index < data.length; index++) {
                    alarmData = {};
                    alarmData.unitName = data[index].unitCode;
                    alarmData.count = data[index].alarmAmount;
                    dayAlarmCount += alarmData.count;
                    alarmDataArray[index] = alarmData;
                }
                $("#dayAlarmCount").html(dayAlarmCount);
                alarmDataArray = alarmDataSort(alarmDataArray, true);
                var alarmAmountHtml = page.logic.drawBottomCard(alarmDataArray, "24小时持续报警数", "个");
                $("#bottomAlarm").html(alarmCountHtml + alarmTimeHtml + craftParaAlarmRateHtml + avgAlarmRateHtml + avgHourAlarmRateHtml + alarmAmountHtml);


                //获取上周数据
                // debugger;
                page.data.param.startTime = beforeLastWeekDate;
                page.data.param.endTime = lastWeekDate;
                page.logic.loadData(page.logic.calcMoM);

            },
            queryChartsData: function () {
                var param = {
                    topType: 10,
                    startTime: OPAL.util.dateFormat(page.data.param.startTime, "yyyy-MM-dd"),
                    endTime: OPAL.util.dateFormat(page.data.param.endTime, "yyyy-MM-dd"),
                    timeGranularity: "day"
                }
                $.ajax({
                    url: numberTopUrl,
                    async: false,
                    data: $.param(param),
                    dataType: 'json',
                    type: 'get',
                    success: function (data) {
                        var data = $.ET.toObjectArr(data);
                        page.logic.initMostFrequentNumberChart(data);
                    },
                    error: function (data) {},
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }

                });
            },
            initMostFrequentNumberChart: function (data) {
                if (mostFrequentNumberChart && !mostFrequentNumberChart.isDisposed()) {
                    mostFrequentNumberChart.clear();
                    mostFrequentNumberChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    mostFrequentNumberChart = OPAL.ui.chart.initEmptyChart('mostFrequentNumber');
                    return;
                }
                mostFrequentNumberChart = echarts.init(document.getElementById('mostFrequentNumber'));
                var option = {
                    title: {
                        show: true, //显示策略，默认值true,可选为：true（显示） | false（隐藏）
                        text: '前10位工艺报警统计', //主标题文本，'\n'指定换行
                        link: '', //主标题文本超链接,默认值true
                        target: null, //指定窗口打开主标题超链接，支持'self' | 'blank'，不指定等同为'blank'（新窗口）
                        sublink: '', //副标题文本超链接
                        subtarget: null, //指定窗口打开副标题超链接，支持'self' | 'blank'，不指定等同为'blank'（新窗口）
                        x: 'center', //水平安放位置，默认为'left'，可选为：'center' | 'left' | 'right' | {number}（x坐标，单位px）
                        y: 'top', //垂直安放位置，默认为top，可选为：'top' | 'bottom' | 'center' | {number}（y坐标，单位px）
                        textAlign: null, //水平对齐方式，默认根据x设置自动调整，可选为： left' | 'right' | 'center
                        backgroundColor: 'rgba(0,0,0,0)', //标题背景颜色，默认'rgba(0,0,0,0)'透明
                        borderColor: '#ccc', //标题边框颜色,默认'#ccc'
                        borderWidth: 0, //标题边框线宽，单位px，默认为0（无边框）
                        padding: 10, //标题内边距，单位px，默认各方向内边距为5，接受数组分别设定上右下左边距
                        itemGap: 10, //主副标题纵向间隔，单位px，默认为10
                        textStyle: { //主标题文本样式{"fontSize": 18,"fontWeight": "bolder","color": "#333"}
                            fontFamily: 'Arial, Verdana, sans...',
                            fontSize: "20px",
                            fontWeight: "bold",
                            fontStyle: 'normal',
                            fontWeight: 'normal',
                        },
                        zlevel: 0, //一级层叠控制。默认0,每一个不同的zlevel将产生一个独立的canvas，相同zlevel的组件或图标将在同一个canvas上渲染。zlevel越高越靠顶层，canvas对象增多会消耗更多的内存和性能，并不建议设置过多的zlevel，大部分情况可以通过二级层叠控制z实现层叠控制。
                        z: 6, //二级层叠控制，默认6,同一个canvas（相同zlevel）上z越高约靠顶层。
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        bottom: '1%',
                        top: '5%',
                        height: '220px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        axisTick: {
                            alignWithLabel: true
                        },
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        flagName: []
                    }],
                    yAxis: [{
                        type: 'value',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        }

                    }],
                    tooltip: {
                        // position: ['30%', '20%'],
                        color: ['#3398DB'],
                        trigger: 'axis',
                        axisPointer: {
                            type: ''
                        },
                        formatter: function (params, index) {
                            if ($("#checkTeam").is(":checked")) {
                                return option.series[0]['tooltip'][params[0]['dataIndex']];
                            } else {
                                var dataIndex = params[0].dataIndex;
                                var flag = option.xAxis[0].flagName[dataIndex];
                                var str = "装置:" + option.xAxis[0].unitInfo[dataIndex] + "<br>报警标识:" + flag + "    报警数:" + params[0]['data'] + "<br>描述:" + option.xAxis[0].location[dataIndex];
                                return str;
                            }
                        }
                    },
                    series: [{
                        type: 'bar',
                        itemStyle: {
                            normal: {
                                color: '#4F81BD'
                            }
                        },
                        data: [],
                        label: {
                            show: true,
                            position: 'insideTop',
                            valueAnimation: true,
                        }
                    }]
                };
                var totalArray = new Array();
                var tagArray = new Array();
                var flagArray = new Array();
                var flagIdArray = new Array();
                var unitInfoArray = new Array();
                var locationArray = new Array();
                if (data != null || data != undefined) {
                    for (var i = 0; i < data.length; i++) {
                        totalArray.push(data[i]["alarmCount"]);
                        tagArray.push(data[i]['unitName'] + "\n" + data[i]['tag']);
                        unitInfoArray.push(data[i]['unitName'] + "    位号：" + data[i]['tag']);
                        flagArray.push(data[i]['alarmFlag']);
                        flagIdArray.push(data[i]['alarmFlagId']);
                        locationArray.push(data[i]['location']);
                    }
                    option.xAxis[0].flagName = flagArray;
                    option.xAxis[0].unitInfo = unitInfoArray;
                    option.xAxis[0].location = locationArray;
                }
                option.xAxis[0].data = tagArray;
                option.series[0].data = totalArray;
                mostFrequentNumberChart.setOption(option);
            },
            searchAlarmTime: function () {
                var alarmTimeData = [];
                page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                $.ajax({
                    url: alarmDurationStattUrl,
                    data: page.data.param,
                    dataType: 'json',
                    async: false,
                    success: function (data) {
                        var result = $.ET.toObjectArr(data);
                        alarmTimeData = result;
                    },
                    error: function () {},
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
                return alarmTimeData;
            },
            searchAlarmCount: function () {
                var alarmCountData = [];
                //进行时间校验
                page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                $.ajax({
                    url: alarmNumStattUrl,
                    data: page.data.param,
                    dataType: 'json',
                    async: false,
                    success: function (data) {
                        var result = $.ET.toObjectArr(data);
                        alarmCountData = result;
                    },
                    complete: function () {}
                });
                return alarmCountData;
            }
        }
    };
    page.init();
    window.page = page;
})