package com.pcitc.opal.ap.bll;

import com.pcitc.opal.ap.bll.entity.AlarmPushRuleDetailEntity;
import com.pcitc.opal.ap.bll.entity.AlarmPushRuleEntity;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleDetailEntityVO;
import com.pcitc.opal.ap.pojo.AlarmPushRuleDetail;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import org.springframework.stereotype.Service;

import java.util.List;

/*
 * 报警知识管理业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmKnowlgManagmtService
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理业务逻辑层接口
 */
@Service
public interface AlarmPushRuleDetailService {

    /**
     * 获取列表数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPushRuleId id
     * @throws Exception 
     * @return PaginationBean<AlarmPointEntity> 分页对象
     */
    List<AlarmPushRuleDetailEntityVO> getAlarmPushRuleDetail(Long alarmPushRuleId) throws  Exception;


    /**
     * 新增数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPushRuleDetailEntity 报警点实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult addAlarmPushRuleDetail(AlarmPushRuleDetailEntity alarmPushRuleDetailEntity) throws Exception;


    /**
     * 删除报警点分组数据
     *
      * <AUTHOR> 2017-10-11
     * @param ids 报警点分组主键Id集合
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult deleteAlarmPushRuleDetail(Long[] ids) throws Exception;

    /**
     * 报警点分组更新数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPushRuleDetailEntity 报警点分组实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult updateAlarmPushRuleDetail(AlarmPushRuleDetailEntity alarmPushRuleDetailEntity) throws Exception;
}
