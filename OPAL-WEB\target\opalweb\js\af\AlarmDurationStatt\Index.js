/**
 * 报警时长统计页面
 * @description 提供报警时长的统计分析，支持按车间和装置两种显示模式
 * <AUTHOR>
 * @version 2.0
 */

(function(window, $, OPAL) {
    'use strict';

    // ==================== 常量定义 ====================

    /**
     * API接口配置
     */
    const API_CONFIG = {
        // 主要数据接口
        ALARM_DURATION_STATS: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotal',
        ALARM_DURATION_DETAIL: OPAL.API.afUrl + '/alarmDurationStatt',
        ALARM_DURATION_PAGE: OPAL.API.afUrl + '/alarmDurationStatt/page',
        ALARM_DURATION_MAIN: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattMain',

        // 基础数据接口
        UNIT_TREE: OPAL.API.commUrl + '/getAllUnit',
        PRIORITY_LIST: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmPriorityList',
        SHOW_TIME: OPAL.API.commUrl + '/getShowTime',
        IN_USE: OPAL.API.commUrl + '/getInUse',
        ALARM_FLAG_LIST: OPAL.API.adUrl + '/alarmEvents/getAlarmFlagList',

        // 车间显示相关接口
        WORKSHOP_STATS: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotalWorkshop',
        UNIT_STATS: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotalUnit',
        CELL_STATS: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotalPrdtcell',
        MAIN_COMMON: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattMainCommon',

        // 导出接口
        EXPORT_MAIN: OPAL.API.afUrl + '/alarmDurationStatt/exportAlarmDurationStattMain',
        EXPORT_COMMON: OPAL.API.afUrl + '/alarmDurationStatt/exportAlarmDurationStattMainCommon'
    };

    /**
     * 图片资源配置
     */
    const IMAGE_CONFIG = {
        TAB_IMAGES: {
            imgShow: {
                active: '../../../images/one1.png',
                inactive: '../../../images/one.png'
            },
            tableShow: {
                active: '../../../images/tweos.png',
                inactive: '../../../images/tweo.png'
            },
            unitShow: {
                active: '../../../images/treese.png',
                inactive: '../../../images/trees.png'
            }
        }
    };

    /**
     * 表格配置
     */
    const TABLE_CONFIG = {
        DEFAULT_PAGE_SIZE: 5,
        PAGE_LIST: [5, 10, 20, 50, 100],
        DEFAULT_HEIGHT: 350
    };

    // ==================== 全局变量 ====================

    let floodAlarmChart = null;
    const tabCharts = {
        floodAlarmChartcj: null,
        floodAlarmChartzz: null,
        floodAlarmChartdy: null
    };

    let isLoading = true;
    let currentUnitId = null;
    let currentUnit = null;
    let currentAlarmFlagId = null;
    let currentTag = null;
    let currentPriority = null;

    // 时间相关变量
    let getStartTime = '';
    let getEndTime = '';

    // 兼容性变量（保持与旧代码的兼容）
    let unitId = null;
    let unit = null;
    let alarmFlagId = null;
    let tag = null;
    let priority = null;
    let floodChartConfig = null;
    // ==================== 工具函数 ====================

    /**
     * 工具函数集合
     */
    const Utils = {
        /**
         * 安全的图表操作
         * @param {Object} chart - 图表实例
         * @param {string} operation - 操作类型
         */
        safeChartOperation(chart, operation) {
            if (chart && !chart.isDisposed()) {
                try {
                    chart[operation]();
                } catch (error) {
                    console.warn(`图表操作失败: ${operation}`, error);
                }
            }
        },

        /**
         * 防抖函数
         * @param {Function} func - 要防抖的函数
         * @param {number} wait - 等待时间
         * @returns {Function} 防抖后的函数
         */
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func.apply(this, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        /**
         * 验证数据有效性
         * @param {*} data - 要验证的数据
         * @returns {boolean} 是否有效
         */
        isValidData(data) {
            return data !== null && data !== undefined &&
                   (Array.isArray(data) ? data.length > 0 : true);
        },

        /**
         * 格式化错误信息
         * @param {Error} error - 错误对象
         * @param {string} context - 错误上下文
         * @returns {string} 格式化的错误信息
         */
        formatError(error, context) {
            return `${context}: ${error.message || error}`;
        }
    };

    // ==================== 主页面对象 ====================

    $(function () {
        const AlarmDurationStatsPage = {
            /**
             * 页面初始化
             */
            init() {
                this.bindEvents();
                this.initializeComponents();
                this.loadInitialData();
            },

            /**
             * 绑定事件
             */
            bindEvents() {
                this.bindResizeEvent();
                this.bindTabSwitchEvent();
                this.bindSearchEvent();
                this.bindExportEvent();
            },

            /**
             * 初始化组件
             */
            initializeComponents() {
                // 日期扩展
                OPAL.util.extendDate();

                // 初始化各种选择器
                this.logic.initPriority();
                this.logic.initUnitTree();
                this.logic.initAlarmFlagList();

                // 初始化图表和表格
                this.logic.initFloodChart('');
                this.logic.initTableDisplay('');
                this.logic.initOpetateTable();
                this.logic.initTableDisplaycj('');
                this.logic.initOpetateTablecj();
            },

            /**
             * 加载初始数据
             */
            loadInitialData() {
                // 获取显示时间
                this.logic.getShowTime();

                // 装置赋值 - 从cookie恢复上次选择
                this.restoreUnitSelection();
            },

            /**
             * 恢复装置选择
             */
            restoreUnitSelection() {
                if (isLoading && !Utils.isValidData(this.data.param.unitIds)) {
                    const cookieValue = OPAL.util.getCookieByPageCode("FloodAlarmAnalysis");
                    if (Utils.isValidData(cookieValue)) {
                        $('#unitIds').combotree('setValue', cookieValue);
                    }
                }
            },
            /**
             * 绑定窗口大小改变事件
             */
            bindResizeEvent() {
                const debouncedResize = Utils.debounce(() => {
                    // 调整主图表
                    Utils.safeChartOperation(floodAlarmChart, 'resize');

                    // 调整车间模式图表
                    Object.values(tabCharts).forEach(chart => {
                        Utils.safeChartOperation(chart, 'resize');
                    });

                    // 调整表格视图
                    const tables = ['#floodTable', '#MostAlarmOperateTable', '#MostAlarmOperateTablecj'];
                    tables.forEach(tableId => {
                        try {
                            $(tableId).bootstrapTable('resetView');
                        } catch (error) {
                            console.warn(`表格重置失败: ${tableId}`, error);
                        }
                    });
                }, 250);

                window.addEventListener('resize', debouncedResize);
            },

            /**
             * 绑定标签切换事件
             */
            bindTabSwitchEvent() {
                $('.myTab li').on('click', (event) => {
                    const $target = $(event.currentTarget);
                    const flag = $target.attr('showFlag');

                    if (!flag || !IMAGE_CONFIG.TAB_IMAGES[flag]) {
                        return;
                    }

                    const imageConfig = IMAGE_CONFIG.TAB_IMAGES[flag];

                    // 设置当前标签为激活状态
                    $target.find('img').attr('src', imageConfig.active);

                    // 设置其他标签为非激活状态
                    $target.siblings().each((index, sibling) => {
                        const $sibling = $(sibling);
                        const siblingFlag = $sibling.attr('showFlag');
                        if (siblingFlag && IMAGE_CONFIG.TAB_IMAGES[siblingFlag]) {
                            $sibling.find('img').attr('src', IMAGE_CONFIG.TAB_IMAGES[siblingFlag].inactive);
                        }
                    });
                });
            },

            /**
             * 绑定查询事件
             */
            bindSearchEvent() {
                $('#btnSearch').on('click', () => {
                    if (!OPAL.util.checkDateIsValid()) {
                        return;
                    }

                    isLoading = false;
                    this.logic.search();
                });
            },

            /**
             * 绑定导出事件
             */
            bindExportEvent() {
                $('#AlarmDurationStattExport').on('click', () => {
                    layer.confirm('数据量较大，导出时间可能较长，是否继续？', {
                        btn: ['是', '否'],
                        title: '提示',
                        icon: 3
                    }, (index) => {
                        layer.close(index);
                        this.logic.exportExcel();
                    }, (index) => {
                        layer.close(index);
                    });
                });
            },
            /**
             * 页面数据管理
             */
            data: {
                param: {},
                subParam: {},
                unitCodeList: [],
                unitCodes: [],
                prdtCellId: [],
                workshopCodes: [],
                clickedChart: '',

                /**
                 * 重置参数
                 */
                resetParams() {
                    this.param = {};
                    this.subParam = {};
                    this.clickedChart = '';
                },

                /**
                 * 验证参数有效性
                 */
                validateParams() {
                    return Utils.isValidData(this.param.startTime) &&
                           Utils.isValidData(this.param.endTime);
                }
            },

            /**
             * 业务逻辑处理
             */
            logic: {
                /**
                 * 初始化查询inUse
                 */
                initInUse() {
                    OPAL.ui.getCombobox("isElimination", API_CONFIG.IN_USE, {
                        selectValue: 1,
                        data: { 'isAll': false }
                    }, null);
                },

                /**
                 * 执行查询
                 */
                search() {
                    if (!this.validateSearchParams()) {
                        return;
                    }

                    this.prepareSearchParams();
                    this.disableSearchButton(true);

                    if ($("#checkShop").is(":checked")) {
                        this.executeWorkshopSearch();
                    } else {
                        this.executeDefaultSearch();
                    }
                },

                /**
                 * 验证搜索参数
                 */
                validateSearchParams() {
                    if (!OPAL.util.checkDateIsValid()) {
                        layer.msg('请选择有效的时间范围');
                        return false;
                    }
                    return true;
                },

                /**
                 * 准备搜索参数
                 */
                prepareSearchParams() {
                    AlarmDurationStatsPage.data.clickedChart = '';
                    AlarmDurationStatsPage.data.param = OPAL.form.getData("searchForm");

                    const unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                    AlarmDurationStatsPage.data.param.unitIds = unitIds;
                    AlarmDurationStatsPage.data.param.startTime = OPAL.util.strToDate(AlarmDurationStatsPage.data.param.startTime);
                    AlarmDurationStatsPage.data.param.endTime = OPAL.util.strToDate(AlarmDurationStatsPage.data.param.endTime);
                    AlarmDurationStatsPage.data.subParam.startTime = AlarmDurationStatsPage.data.param.startTime;
                    AlarmDurationStatsPage.data.subParam.endTime = AlarmDurationStatsPage.data.param.endTime;
                },

                /**
                 * 执行车间模式搜索
                 */
                executeWorkshopSearch() {
                    this.toggleDisplayMode(true);

                    $.ajax({
                        url: API_CONFIG.WORKSHOP_STATS,
                        data: AlarmDurationStatsPage.data.param,
                        dataType: 'json',
                        success: (data) => {
                            try {
                                const result = $.ET.toObjectArr(data);
                                this.initFloodChartCom(JSON.stringify(result), 'floodAlarmChartcj');
                            } catch (error) {
                                console.error('车间数据处理失败:', error);
                                layer.msg('数据处理失败，请重试');
                            }
                        },
                        error: (xhr, status, error) => {
                            console.error('车间查询失败:', Utils.formatError(error, '车间模式查询'));
                            layer.msg('查询失败，请检查网络连接');
                        },
                        complete: () => {
                            this.disableSearchButton(false);
                        }
                    });
                },

                /**
                 * 执行默认模式搜索
                 */
                executeDefaultSearch() {
                    this.toggleDisplayMode(false);

                    $.ajax({
                        url: API_CONFIG.ALARM_DURATION_STATS,
                        data: AlarmDurationStatsPage.data.param,
                        dataType: 'json',
                        success: (data) => {
                            try {
                                const result = $.ET.toObjectArr(data);
                                this.initFloodChart(JSON.stringify(result));
                                this.initTableDisplay(JSON.stringify(result));
                            } catch (error) {
                                console.error('默认数据处理失败:', error);
                                layer.msg('数据处理失败，请重试');
                            }
                        },
                        error: (xhr, status, error) => {
                            console.error('默认查询失败:', Utils.formatError(error, '默认模式查询'));
                            layer.msg('查询失败，请检查网络连接');
                        },
                        complete: () => {
                            this.disableSearchButton(false);
                        }
                    });
                },

                /**
                 * 切换显示模式
                 * @param {boolean} isWorkshopMode - 是否为车间模式
                 */
                toggleDisplayMode(isWorkshopMode) {
                    const workshopElement = document.querySelector('.contentcj');
                    const defaultElement = document.querySelector('.contentmr');

                    if (workshopElement && defaultElement) {
                        workshopElement.style.display = isWorkshopMode ? 'block' : 'none';
                        defaultElement.style.display = isWorkshopMode ? 'none' : 'block';
                    }
                },

                /**
                 * 控制搜索按钮状态
                 * @param {boolean} disabled - 是否禁用
                 */
                disableSearchButton(disabled) {
                    $("#btnSearch").prop('disabled', disabled);
                },
                /**
                 * 导出Excel
                 */
                exportExcel() {
                    try {
                        const exportData = this.prepareExportData();
                        this.populateExportForm(exportData);
                        this.submitExportForm();
                    } catch (error) {
                        console.error('导出失败:', error);
                        layer.msg('导出失败，请重试');
                    }
                },

                /**
                 * 准备导出数据
                 */
                prepareExportData() {
                    const isWorkshopMode = $("#checkShop").is(":checked");
                    const tableId = isWorkshopMode ? '#MostAlarmOperateTablecj' : '#MostAlarmOperateTable';

                    // 获取表格标题
                    const titleArray = this.extractTableTitles(tableId);

                    // 获取表格选项
                    const tableOptions = $(tableId).bootstrapTable('getOptions');

                    // 准备基础数据
                    const baseData = {
                        titles: JSON.stringify(titleArray),
                        pageSize: tableOptions.pageSize,
                        pageNumber: tableOptions.pageNumber
                    };

                    // 合并表单数据
                    const formData = OPAL.form.getData("searchForm");

                    return {
                        ...baseData,
                        ...formData,
                        isWorkshopMode,
                        unitCodeList: AlarmDurationStatsPage.data.unitCodeList
                    };
                },

                /**
                 * 提取表格标题
                 */
                extractTableTitles(tableId) {
                    const titleArray = [];
                    const tableColumns = $(tableId).bootstrapTable('getOptions').columns[0];

                    tableColumns.forEach((column, index) => {
                        if (index >= 1 && column.field && column.title) {
                            titleArray.push({
                                key: column.field,
                                value: column.title
                            });
                        }
                    });

                    return titleArray;
                },

                /**
                 * 填充导出表单
                 */
                populateExportForm(exportData) {
                    const formFields = {
                        '#titlesExport': exportData.titles,
                        '#pageSizeExport': exportData.pageSize,
                        '#pageNumberExport': exportData.pageNumber,
                        '#unitIdsExport': exportData.unitCodeList,
                        '#priorityFrom': exportData.priority,
                        '#startTimeForm': exportData.startTime,
                        '#endTimeForm': exportData.endTime,
                        '#alarmFlagIdForm': exportData.alarmFlagId
                    };

                    // 填充表单字段
                    Object.entries(formFields).forEach(([selector, value]) => {
                        $(selector).val(value || '');
                    });

                    // 设置导出URL
                    const exportUrl = exportData.isWorkshopMode ?
                        API_CONFIG.EXPORT_COMMON :
                        API_CONFIG.EXPORT_MAIN;

                    $('#form').attr('action', exportUrl);
                },

                /**
                 * 提交导出表单
                 */
                submitExportForm() {
                    $('#form').submit();
                },
                /**
                 * 初始化装置树
                 */
                initUnitTree() {
                    OPAL.ui.getEasyUIComboTreeSelect('unitIds', API_CONFIG.UNIT_TREE, 'id', 'parentId', 'sname', {
                        onChange: (/* node, checked */) => {
                            const unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                            if (Utils.isValidData(unitIds) && unitIds.length === 1) {
                                $('.textbox,.combo').css('background-color', '');
                            } else {
                                $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                            }
                        }
                    }, false);
                },
                /**
                 * 初始化主图表
                 * @param {string} data - 图表数据JSON字符串
                 */
                initFloodChart(data) {
                    try {
                        const results = data ? JSON.parse(data) : [];

                        // 清理现有图表
                        this.disposeChart(floodAlarmChart);

                        // 如果没有数据，显示空图表
                        if (!Utils.isValidData(results)) {
                            floodAlarmChart = OPAL.ui.chart.initEmptyChart('floodAlarmChart');
                            return;
                        }

                        // 准备图表数据
                        const chartData = this.prepareChartData(results);

                        // 创建图表配置
                        const chartOption = this.createChartOption(chartData);

                        // 初始化图表
                        floodAlarmChart = echarts.init(document.getElementById('floodAlarmChart'));
                        floodAlarmChart.setOption(chartOption);

                        // 设置图表点击事件
                        this.bindChartClickEvent(floodAlarmChart, results);

                        // 更新全局变量
                        this.updateGlobalVariables(results[0]);

                        // 查询操作表格
                        this.queryMostOperate();

                    } catch (error) {
                        console.error('图表初始化失败:', error);
                        floodAlarmChart = OPAL.ui.chart.initEmptyChart('floodAlarmChart');
                    }
                },

                /**
                 * 准备图表数据
                 * @param {Array} results - 原始数据
                 * @returns {Object} 图表数据
                 */
                prepareChartData(results) {
                    const chartData = {
                        xAxis: [],
                        emergencyAlarmQuantity: [],
                        importantAlarmQuantity: [],
                        generalAlarmQuantity: [],
                        nullAlarmQuantity: []
                    };

                    results.forEach(item => {
                        // 添加X轴标签
                        chartData.xAxis.push(item.sname || item.name || '');

                        // 添加各优先级数据
                        chartData.emergencyAlarmQuantity.push(this.parseNumericValue(item.emergencyAlarmQuantity));
                        chartData.importantAlarmQuantity.push(this.parseNumericValue(item.importantAlarmQuantity));
                        chartData.generalAlarmQuantity.push(this.parseNumericValue(item.generalAlarmQuantity));
                        chartData.nullAlarmQuantity.push(this.parseNumericValue(item.nullAlarmQuantity));
                    });

                    return chartData;
                },

                /**
                 * 解析数值
                 * @param {*} value - 要解析的值
                 * @returns {number} 解析后的数值
                 */
                parseNumericValue(value) {
                    try {
                        return $.parseJSON(value) || 0;
                    } catch (error) {
                        return parseFloat(value) || 0;
                    }
                },

                /**
                 * 创建图表配置
                 * @param {Object} chartData - 图表数据
                 * @returns {Object} 图表配置
                 */
                createChartOption(chartData) {
                    return {
                        color: ['#F4D312', '#FE6732', '#CD1515', '#C9C9C9'],
                        tooltip: {
                            trigger: 'axis',
                            formatter: (params) => {
                                let result = `${params[0].axisValue}<br/>`;
                                params.forEach(param => {
                                    result += `${param.marker}${param.seriesName}: ${param.value}分钟<br/>`;
                                });
                                return result;
                            }
                        },
                        legend: {
                            itemHeight: 8,
                            itemWidth: 18,
                            data: ['一般', '重要', '紧急', '空']
                        },
                        grid: {
                            left: '1%',
                            right: '1%',
                            top: '10%',
                            height: '270px',
                            containLabel: true
                        },
                        xAxis: [{
                            type: 'category',
                            axisLabel: {
                                interval: 0,
                                show: true,
                                splitNumber: 5,
                                textStyle: {
                                    fontSize: 12,
                                    color: '#000'
                                }
                            },
                            data: chartData.xAxis
                        }],
                        dataZoom: [{
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            bottom: -5,
                            start: 0,
                            end: 100
                        }],
                        yAxis: {
                            splitLine: {
                                show: false
                            },
                            type: 'value',
                            name: "(分钟)"
                        },
                        series: [
                            {
                                name: '一般',
                                type: 'bar',
                                stack: 'sum',
                                barWidth: '20px',
                                data: chartData.generalAlarmQuantity
                            },
                            {
                                name: '重要',
                                type: 'bar',
                                barWidth: '20px',
                                stack: 'sum',
                                data: chartData.importantAlarmQuantity
                            },
                            {
                                name: '紧急',
                                type: 'bar',
                                stack: 'sum',
                                barWidth: '20px',
                                data: chartData.emergencyAlarmQuantity
                            },
                            {
                                name: '空',
                                type: 'bar',
                                stack: 'sum',
                                barWidth: '20px',
                                data: chartData.nullAlarmQuantity
                            }
                        ]
                    };
                },

                /**
                 * 绑定图表点击事件
                 * @param {Object} chart - 图表实例
                 * @param {Array} results - 数据源
                 */
                bindChartClickEvent(chart, results) {
                    chart.on('click', (param) => {
                        const index = param.dataIndex;
                        if (results[index]) {
                            AlarmDurationStatsPage.data.param.unitIds = [results[index].unitId];
                            currentUnit = param.name;
                            this.queryMostOperate();
                        }
                    });
                },

                /**
                 * 更新全局变量
                 * @param {Object} firstResult - 第一条数据
                 */
                updateGlobalVariables(firstResult) {
                    if (firstResult) {
                        currentUnitId = firstResult.unitId;
                        currentUnit = firstResult.sname;
                        AlarmDurationStatsPage.data.param.unitIds = [currentUnitId];
                        AlarmDurationStatsPage.data.unitCodeList = [currentUnitId];
                    }
                },

                /**
                 * 安全销毁图表
                 * @param {Object} chart - 图表实例
                 */
                disposeChart(chart) {
                    if (chart && !chart.isDisposed()) {
                        try {
                            chart.clear();
                            chart.dispose();
                        } catch (error) {
                            console.warn('图表销毁失败:', error);
                        }
                    }
                },

            // 展示空数据的图表
            EmptyChart: function() {
                tabChart.floodAlarmChartcj = OPAL.ui.chart.initEmptyChart('floodAlarmChartcj');
                tabChart.floodAlarmChartzz = OPAL.ui.chart.initEmptyChart('floodAlarmChartzz');
                tabChart.floodAlarmChartdy = OPAL.ui.chart.initEmptyChart('floodAlarmChartdy');
            },

            /**
             * 初始化图表
             * @param data
             */
            initFloodChartCom: function (data,Chartid,func) {              
                var results = JSON.parse(data);
                // page.logic.queryMostOperate();
                if (tabCharts[Chartid] && !tabCharts[Chartid].isDisposed()) {
                    tabCharts[Chartid].clear();
                    tabCharts[Chartid].dispose();
                }
                if (results == undefined || results.length == 0) {
                    // tabChart[Chartid] = OPAL.ui.chart.initEmptyChart(Chartid);
                    page.logic.EmptyChart()
                    return;
                }
                var xAxis = [];
                var emergencyAlarmQuantity = [];
                var importantAlarmQuantity = [];
                var generalAlarmQuantity = [];
                var nullAlarmQuantity = [];
                unitId = results[0].unitId;
                unit = results[0].sname;
                var nextparam = {
                    code: '',
                    num: 0
                }
                for (var i = 0; i < results.length; i++) {
                    if (results[i].sname) {
                        xAxis.push(results[i].sname)
                    } else if (results[i].name) {
                        xAxis.push(results[i].name)
                    }
                    emergencyAlarmQuantity.push($.parseJSON(results[i].emergencyAlarmQuantity));
                    importantAlarmQuantity.push($.parseJSON(results[i].importantAlarmQuantity));
                    generalAlarmQuantity.push($.parseJSON(results[i].generalAlarmQuantity));
                    nullAlarmQuantity.push($.parseJSON(results[i].nullAlarmQuantity))
                    if (nextparam.num < Math.abs($.parseJSON(results[i].emergencyAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].emergencyAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].importantAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].importantAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].generalAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].generalAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].nullAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].nullAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                }
                var option = {
                    color: ['#F4D312', '#FE6732', '#CD1515', '#C9C9C9'],
                    tooltip: {
                        trigger: 'axis',
                    },
                    legend: {
                        itemHeight: 8,
                        itemWidth: 18,
                        data: ['一般', '重要', '紧急', '空'],
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        top: '10%',
                        height: '270px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            show: true,
                            splitNumber: 5,
                            textStyle: {
                                fontSize: 12,
                                color: '#000'
                            },
                        },
                        data: xAxis,
                    }],
                    dataZoom: [{
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        // left: '9%',
                        bottom: -5,
                        start: 0,
                        end: 100 //初始化滚动条
                    }],
                    yAxis: {
                        splitLine: {
                            show: false
                        },
                        type: 'value',
                        name: "(分钟)"
                    },
                    series: [{
                        name: '一般',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: generalAlarmQuantity
                    },
                    {
                        name: '重要',
                        type: 'bar',
                        barWidth: '20px',
                        stack: 'sum',
                        data: importantAlarmQuantity,
                    },
                    {
                        name: '紧急',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: emergencyAlarmQuantity
                    },
                    {
                        name: '空',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: nullAlarmQuantity
                    }]
                };
                tabCharts[Chartid] = echarts.init(document.getElementById(Chartid));
                tabCharts[Chartid].setOption(option);
                page.data.param.unitIds = [unitId];
                page.data.unitCodeList=[unitId];
                floodChartConfig = option;
                tabCharts[Chartid].on('click', function (param) {
                    var indexs = param.dataIndex;
                    page.data.click = Chartid
                    if (Chartid == 'floodAlarmChartcj') {
                        page.data.param.workshopCodes = [results[indexs].code];
                        page.data.workshopCodes = [results[indexs].code]
                        page.logic.querynextChart('floodAlarmChartzz');
                    } else if(Chartid == 'floodAlarmChartzz') {
                        page.data.param.unitCodes = [results[indexs].code];
                        page.data.unitCodes = [results[indexs].code]
                        page.logic.querynextChart('floodAlarmChartdy');
                    } else if(Chartid == 'floodAlarmChartdy') {
                        page.data.param.prdtCellId = [results[indexs].code];
                        page.data.prdtCellId = [results[indexs].code]
                    }
                    // page.logic.queryMostOperate();
                });
                
                if (Chartid == 'floodAlarmChartcj') {
                    page.data.param.workshopCodes = [nextparam.code];
                    page.data.workshopCodes = [nextparam.code];
                    page.logic.querynextChart('floodAlarmChartzz');
                } else if(Chartid == 'floodAlarmChartzz') {
                    page.data.param.unitCodes = [nextparam.code];
                    page.data.unitCodes = [nextparam.code];
                    page.logic.querynextChart('floodAlarmChartdy');
                } else if (Chartid == 'floodAlarmChartdy') {
                    page.data.param.prdtCellId = [nextparam.code];
                    page.data.prdtCellId = [nextparam.code];
                    // page.logic.queryMostOperate();
                }
            },

            // 获取chart数据
            querynextChart: function(Chartid) {
                var url = '';
                if (Chartid == 'floodAlarmChartzz') {
                    url = API_CONFIG.UNIT_STATS;
                } else if(Chartid == 'floodAlarmChartdy') {
                    url = API_CONFIG.CELL_STATS;
                }
                $.ajax({
                    url: url,
                    data: page.data.param,
                    dataType: 'json',
                    success: function (data) {
                        var result = $.ET.toObjectArr(data);
                        page.logic.initFloodChartCom(JSON.stringify(result),Chartid);
                        if (Chartid == 'floodAlarmChartdy') {
                            page.logic.queryMostOperatecj();
                        }
                    },
                    error: function () {
                        console.error('获取图表数据失败:', Chartid);
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
            },



            /**
             * 初始化Table数据
             * @param data
             */
            initTableDisplay: function (data) {
                var results = JSON.parse(data);
                var options = {
                    // detailView: true, //父子表
                    striped: true, //是否显示行间隔色
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    height: 350,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'sname',
                        title: '装置',
                        align: 'left',
                    }, {
                        field: 'emergencyAlarmQuantity',
                        title: '紧急时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.emergencyAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.emergencyAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'1\')">' + row.emergencyAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'importantAlarmQuantity',
                        title: '重要时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.importantAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.importantAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'2\')">' + row.importantAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'generalAlarmQuantity',
                        title: '一般时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.generalAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.generalAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'3\')">' + row.generalAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'nullAlarmQuantity',
                        title: '优先级为空(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.nullAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.nullAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'9\')">' + row.nullAlarmQuantity + '</a>'
                            }
                        }
                    },
                    {
                        field: 'totalAlarmQuantity',
                        title: '合计(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'-1\')">' + row.totalAlarmQuantity + '</a>'
                        }
                    }
                    ]
                };
                $('#floodTable').bootstrapTable(options);
                $('#floodTable').bootstrapTable('refreshOptions', options);
                if (results == undefined) {
                    results = [];
                }
                $("#floodTable").bootstrapTable("load", results);
            },
            /**
             * 初始化Table数据
             * @param data
             */
            initTableDisplaycj: function (data) {
                var results = JSON.parse(data);
                var options = {
                    // detailView: true, //父子表
                    striped: true, //是否显示行间隔色
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    height: 350,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'sname',
                        title: '装置',
                        align: 'left',
                    }, {
                        field: 'emergencyAlarmQuantity',
                        title: '紧急时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.emergencyAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.emergencyAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'1\')">' + row.emergencyAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'importantAlarmQuantity',
                        title: '重要时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.importantAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.importantAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'2\')">' + row.importantAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'generalAlarmQuantity',
                        title: '一般时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.generalAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.generalAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'3\')">' + row.generalAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'nullAlarmQuantity',
                        title: '优先级为空(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.nullAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.nullAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'9\')">' + row.nullAlarmQuantity + '</a>'
                            }
                        }
                    },
                    {
                        field: 'totalAlarmQuantity',
                        title: '合计(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'-1\')">' + row.totalAlarmQuantity + '</a>'
                        }
                    }
                    ]
                };
                $('#floodTable').bootstrapTable(options);
                $('#floodTable').bootstrapTable('refreshOptions', options);
                if (results == undefined) {
                    results = [];
                }
                $("#floodTable").bootstrapTable("load", results);
            },
            /**
             * 报警次数详情
             */
            showOperateDetail: function (unitName, unitId, priorityName) {
                layer.open({
                    type: 2,
                    title: '报警时长详情',
                    closeBtn: 1,
                    area: ['85%', '420px'],
                    shadeClose: false,
                    content: 'AlarmDurationDtl.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        let obj = {
                            unitName: unitName,
                            unitId: unitId,
                            priorityName: priorityName,
                            startTime: moment(page.data.param.startTime).format('YYYY-MM-DD HH:mm:ss'),
                            endTime: moment(page.data.param.endTime).format('YYYY-MM-DD HH:mm:ss'),
                            alarmFlagId: page.data.param.alarmFlagId
                        };
                        iframeWin.page.logic.setData(obj);
                    }
                });
            },
            /**
             * 设置日期插件
             */
            initTime: function () {
                var myDate = new Date();
                var start = laydate.render({
                    elem: '#startTime', //指定元素
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd', //日期格式
                    value: getStartTime,
                    max: getEndTime, //最大日期
                });
                var end = laydate.render({
                    elem: '#endTime',
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd',
                    value: getEndTime,
                    max: getEndTime,
                });
                $('#startTime').attr('maxDate', getEndTime)
                $('#endTime').attr('maxDate', getEndTime)
            },
            getShowTime: function () {
                $.ajax({
                    url: API_CONFIG.SHOW_TIME,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        getStartTime = dataArr[0].value;
                        getEndTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
                        //设置时间插件
                        page.logic.initTime();
                        document.getElementById("btnSearch").click();
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            //初始化优先级
            initPriority: function () {
                OPAL.ui.getComboMultipleSelect('priority', API_CONFIG.PRIORITY_LIST, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#priority").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#priority").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化查询 报警标识
             */
            initAlarmFlagList: function () {
                OPAL.ui.getComboMultipleSelect('alarmFlagId', API_CONFIG.ALARM_FLAG_LIST, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#alarmFlagId").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#alarmFlagId").combotree("checkAllNodes");
                });
            },
            /**
             * 加载 下方表格
             */
            queryMostOperate: function () {
                $("#MostAlarmOperateTable").bootstrapTable('refresh', {
                    "url": API_CONFIG.ALARM_DURATION_PAGE,
                    "pageNumber": 1
                });
            },
            //初始化下方表格
            initOpetateTable: function () {
                page.logic.initBootstrapTable("MostAlarmOperateTable", {
                    columns: [{
                        title: "序号", formatter: function (value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1) * data.pageSize;
                        }, rowspan: 1, align: 'center', width: '80px'
                    }, {
                        field: 'unitName', title: '装置', align: 'center', width: '180px'
                    }, {
                        field: 'prdtCellName', title: '生产单元', align: 'center', width: '180px'
                    }, {
                        field: 'location', title: '参数名称', align: 'center',
                    }, {
                        field: 'tag', title: '位号', align: 'center', width: '200px'
                    }, {
                        field: 'monitorTypeStr', title: '专业', align: 'center', width: '60px'
                    }, {
                        field: 'priorityName', title: "优先级", align: 'center', width: '60px'
                    }, {
                        field: 'alarmTime', title: '报警时间', align: 'center', width: '200px'
                    }, {
                        field: 'recoveryTime', title: '结束时间', align: 'center', width: '200px'
                    }, {
                        field: 'continuousHour', title: '时长(分钟)', align: 'center', width: '80px'
                    }, {
                        field: 'alarmFlagName', title: '报警等级', align: 'center', width: '80px'
                    }]
                    // onExpandRow: function (index, row, $detail) {
                    //     page.data.subParam.unitIds = row['unitCode'];
                    //     page.data.subParam.alarmFlagId = row['alarmFlagId'];
                    //     page.data.subParam.tag = row['tag'];
                    //     page.data.subParam.priority = row['priority'];
                    //     page.logic.initCausalSubTable(index, row, $detail);
                    // }
                }, page.logic.queryParams);
                var tableOption = $('#MostAlarmOperateTable').bootstrapTable('getOptions');
                //tableOption.pageList = [20];
                $("#MostAlarmOperateTable").bootstrapTable('refreshOptions', tableOption);
            },

            /**
             * 加载 下方车间表格
             */
            queryMostOperatecj: function () {
                $("#MostAlarmOperateTablecj").bootstrapTable('refresh', {
                    "url": API_CONFIG.ALARM_DURATION_PAGE,
                    "pageNumber": 1
                });
            },
            //初始化下方车间表格
            initOpetateTablecj: function () {
                page.logic.initBootstrapTablecj("MostAlarmOperateTablecj", {
                    // detailView: true,
                    // cache: false,
                    columns: [{
                        title: "序号", formatter: function (value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1) * data.pageSize;
                        }, rowspan: 1, align: 'center', width: '80px'
                    }, {
                        field: 'unitName', title: '装置', align: 'center', width: '180px'
                    }, {
                        field: 'prdtCellName', title: '生产单元', align: 'center', width: '180px'
                    }, {
                        field: 'location', title: '参数名称', align: 'center',
                    }, {
                        field: 'tag', title: '位号', align: 'center', width: '200px'
                    }, {
                        field: 'monitorTypeStr', title: '专业', align: 'center', width: '60px'
                    }, {
                        field: 'priorityName', title: "优先级", align: 'center', width: '60px'
                    }, {
                        field: 'alarmTime', title: '报警时间', align: 'center', width: '200px'
                    }, {
                        field: 'recoveryTime', title: '结束时间', align: 'center', width: '200px'
                    }, {
                        field: 'continuousHour', title: '时长(分钟)', align: 'center', width: '80px'
                    }, {
                        field: 'alarmFlagName', title: '报警等级', align: 'center', width: '80px'
                    }]/*,
                    onExpandRow: function (index, row, $detail) {
                        page.data.subParam.unitIds = row['unitCode'];
                        page.data.subParam.alarmFlagId = row['alarmFlagId'];
                        page.data.subParam.tag = row['tag'];
                        page.data.subParam.priority = row['priority'];
                        page.logic.initCausalSubTablecj(index, row, $detail);
                    }*/
                }, page.logic.queryParams);
                var tableOption = $('#MostAlarmOperateTablecj').bootstrapTable('getOptions');
                $("#MostAlarmOperateTablecj").bootstrapTable('refreshOptions', tableOption);
            },

            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                if (page.data.click == 'floodAlarmChartcj') {
                    param.unitCodes = []
                    param.prdtCellId = []
                } else if (page.data.click == 'floodAlarmChartzz') {
                    param.prdtCellId = []
                }
                return $.extend(page.data.param, param);
            },

            /**
             * 查询子表格参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            subQueryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    alarmFlagId: alarmFlagId,
                    // isElimination: $("#isElimination").val(),
                    tag: tag,
                    priority: priority,
                    now: Math.random()
                };
                return $.extend(page.data.subParam, param);
            },

            /**
             * 初始化二级列表
             */
            initCausalSubTable: function (index, row, $detail) {
                alarmFlagId = row.alarmFlagId;
                tag = row.tag;
                priority = row.priority;
                var subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                OPAL.ui.initBootstrapTable(subId, {
                    url: API_CONFIG.ALARM_DURATION_DETAIL,
                    striped: true,
                    pagination: false,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        align: 'center',
                        width: '80'
                    }, {
                        field: 'alarmTime',
                        title: '报警时间',
                        align: 'center',
                    }, {
                        field: 'continuousHour',
                        title: '时长(分钟)',
                        align: 'right',
                    }, {
                        field: 'recoveryTime',
                        title: '恢复时间',
                        align: 'center',
                    }, {
                        field: 'eventTypeName',
                        title: '事件类型',
                        align: 'left',
                    }, {
                        field: 'monitorTypeStr',
                        title: '专业',
                        align: 'left',
                    }],
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            /*"pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],*/
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                }, page.logic.subQueryParams)
            },

            initBootstrapTable: function (tableID, config, queryParams) {
                var _config = {
                    method: 'get',
                    url: '',
                    cache: false,
                    pagination: true, //启动分页
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "server", // 表示服务端请求 后台分页
                    //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder
                    //设置为limit可以获取limit, offset, search, sort, order
                    queryParamsType: "undefined",
                    queryParams: queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#' + tableID).find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                };
                $.extend(true, _config, config);
                $('#' + tableID).bootstrapTable(_config);
            },

            initBootstrapTablecj: function (tableID, config, queryParams) {
                var _config = {
                    method: 'get',
                    url: '',
                    cache: false,
                    pagination: true, //启动分页
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "server", // 表示服务端请求 后台分页
                    //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder
                    //设置为limit可以获取limit, offset, search, sort, order
                    queryParamsType: "undefined",
                    queryParams: queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#' + tableID).find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                };
                $.extend(true, _config, config);
                $('#' + tableID).bootstrapTable(_config);
            },

            /**
             * 初始化二级列表
             */
            initCausalSubTablecj: function (index, row, $detail) {
                alarmFlagId = row.alarmFlagId;
                tag = row.tag;
                priority = row.priority;
                var subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                OPAL.ui.initBootstrapTable(subId, {
                    url: API_CONFIG.ALARM_DURATION_DETAIL,
                    striped: true,
                    pagination: false,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        align: 'center',
                        width: '80'
                    }, {
                        field: 'alarmTime',
                        title: '报警时间',
                        align: 'center',
                    }, {
                        field: 'continuousHour',
                        title: '时长(分钟)',
                        align: 'right',
                    }, {
                        field: 'recoveryTime',
                        title: '结束时间',
                        align: 'center',
                    }, {
                        field: 'eventTypeName',
                        title: '事件类型',
                        align: 'left',
                    }, {
                        field: 'monitorTypeStr',
                        title: '专业',
                        align: 'left',
                    }],
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            /*"pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],*/
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                }, AlarmDurationStatsPage.logic.subQueryParams);
            }
        }
    };

    // 创建兼容性对象，保持与旧代码的兼容
    const page = {
        data: AlarmDurationStatsPage.data,
        logic: AlarmDurationStatsPage.logic,
        init: AlarmDurationStatsPage.init.bind(AlarmDurationStatsPage),
        bindUi: AlarmDurationStatsPage.bindEvents.bind(AlarmDurationStatsPage)
    };

    // 创建兼容性的tabChart对象
    const tabChart = tabCharts;

    // 初始化页面
    AlarmDurationStatsPage.init();

    // 暴露到全局作用域（兼容性）
    window.page = page;
    window.AlarmDurationStatsPage = AlarmDurationStatsPage;
    });

})(window, jQuery, OPAL);