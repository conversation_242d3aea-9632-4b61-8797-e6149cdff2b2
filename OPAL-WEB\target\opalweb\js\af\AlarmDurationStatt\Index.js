var alarmDurationStattUrl = OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotal';
var alarmDurStattUrl = OPAL.API.afUrl + '/alarmDurationStatt';  //二级列表

var alarmDurStattPageUrl = OPAL.API.afUrl + '/alarmDurationStatt/page';  //二级列表

var alarmDurStattMainUrl = OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattMain'; //一级列表
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";   //装置树
var priorityUrl = OPAL.API.afUrl + "/alarmDurationStatt/getAlarmPriorityList";    //优先级
var exportAlarmDurationStattUrl = OPAL.API.afUrl + '/alarmDurationStatt/exportAlarmDurationStattMain';
var getShowTimeUrl = OPAL.API.commUrl + '/getShowTime';
var inUseUrl = OPAL.API.commUrl + "/getInUse";
var alarmFlagListUrl = OPAL.API.adUrl + '/alarmEvents/getAlarmFlagList';
// 按车间显示
var TotalWorkshop = OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotalWorkshop';    // 车间	
var TotalUnit = OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotalUnit';    // 装置	
var TotalPrdtcell = OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotalPrdtcell';    // 生产单元	
var getMainCommon = OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattMainCommon';    // 下方列表	     -----
var exportnextMainCommon = OPAL.API.afUrl + '/alarmDurationStatt/exportAlarmDurationStattMainCommon';    // 导出	
var floodAlarmChart;
tabChart = {
    floodAlarmChartcj: null,
    floodAlarmChartzz: null,
    floodAlarmChartdy: null,
}
var isLoading = true;
var unitId;
var unit;
var alarmFlagId;
var tag;
var priority;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            /**
             *绑定事件
             */
            this.bindUi();
            //初始化查询是否统计剔除
            // page.logic.initInUse();
            // 日期扩展
            OPAL.util.extendDate();
            // 根据日期时间计算开始时间和结束时间和一周之前的开始和结束时间
            page.logic.getShowTime();
            page.logic.initPriority();
            // 初始化装置数
            page.logic.initUnitTree();
            page.logic.initFloodChart(JSON.stringify(""));
            page.logic.initTableDisplay(JSON.stringify(""));
            page.logic.initOpetateTable();
            page.logic.initTableDisplaycj(JSON.stringify(""));
            page.logic.initOpetateTablecj();
            //初始化报警标识
            page.logic.initAlarmFlagList();
            //装置赋值
            if (isLoading && (page.data.param.unitIds == null || page.data.param.unitIds == undefined || page.data.param.unitIds.length == 0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("FloodAlarmAnalysis");
                if (cookieValue != null && cookieValue != undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
        },
        bindUi: function () {
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                floodAlarmChart.resize();
                tabChart.floodAlarmChartcj.resize()
                tabChart.floodAlarmChartzz.resize()
                tabChart.floodAlarmChartdy.resize()
                $('#floodTable').bootstrapTable('resetView');
                $('#MostAlarmOperateTable').bootstrapTable('resetView');
                $('#MostAlarmOperateTablecj').bootstrapTable('resetView');
            };
            /**
             * 导航切换
             */
            $('.myTab li').click(function () {
                var flag = $(this).attr('showFlag');
                if (flag == 'imgShow') {
                    $(this).find('img').attr('src', '../../../images/one1.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/tweo.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                } else if (flag == 'tableShow') {
                    $(this).find('img').attr('src', '../../../images/tweos.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                } else if (flag == 'unitShow') {
                    $(this).find('img').attr('src', '../../../images/treese.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/tweo.png');
                }
            })
            /**
             * 查询
             */
            $('#btnSearch').click(function () {
                if (OPAL.util.checkDateIsValid() == true) {
                    isLoading = false;
                    page.logic.search();
                }
            })
            /**
             * 导出
             */
            $('#AlarmDurationStattExport').click(function() {
                //1.提示是否继续
                layer.confirm('数据量较大，导出时间可能较长，是否继续？', {
                    btn: ['是', '否'],
                    title: '提示',
                }, function (index) {
                    layer.close(index);
                    page.logic.exportExcel();
                }, function (index) {
                    layer.close(index);
                });

            });
        },
        data: {
            param: {},
            subParam: {},
            unitCodeList: [],
            unitCodes: [],
            prdtCellId: [],
            workshopCodes: [],
            click: ''
        },

        logic: {
            /**
             * 初始化查询inUse
             */
            initInUse: function () {
                OPAL.ui.getCombobox("isElimination", inUseUrl, {
                    selectValue: 1,
                    data: {
                        'isAll': false
                    }
                }, null);
            },
            /***
             * 查询
             */
            search: function () {
                page.data.click = ''
                // $("#myModal").modal('show');
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                page.data.param = OPAL.form.getData("searchForm");
                var unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                page.data.param.unitIds = unitIds;
                page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                page.data.subParam.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.subParam.endTime = OPAL.util.strToDate(page.data.param.endTime);
                $("#btnSearch").prop('disabled', true);
                if ($("#checkShop").is(":checked")) {
                    document.querySelector('.contentcj').style.display = 'block'
                    document.querySelector('.contentmr').style.display = 'none'
                    $.ajax({
                        url: TotalWorkshop,
                        // url: alarmDurationStattUrl,
                        data: page.data.param,
                        dataType: 'json',
                        success: function (data) {
                            var result = $.ET.toObjectArr(data);
                            page.logic.initFloodChartCom(JSON.stringify(result),'floodAlarmChartcj');
                        },
                        error: function () {
                            // $("#myModal").modal('hide');
                        },
                        complete: function () {
                            $("#btnSearch").prop('disabled', false);
                        }
                    });
                } else {
                    document.querySelector('.contentcj').style.display = 'none'
                    document.querySelector('.contentmr').style.display = 'block'
                    $.ajax({
                        url: alarmDurationStattUrl,
                        data: page.data.param,
                        dataType: 'json',
                        success: function (data) {
                            // $("#myModal").modal('hide');
                            var result = $.ET.toObjectArr(data);
                            page.logic.initFloodChart(JSON.stringify(result));
                            page.logic.initTableDisplay(JSON.stringify(result));
                        },
                        error: function () {
                            // $("#myModal").modal('hide');
                        },
                        complete: function () {
                            $("#btnSearch").prop('disabled', false);
                        }
                    });
                }
                
                // page.logic.queryMostOperate();
            },
            /**
             * 导出
             */
            exportExcel: function() {
                var titleArray = new Array();
                var id = ''
                if ($("#checkShop").is(":checked")) {
                    id = '#MostAlarmOperateTablecj'
                } else {
                    id = '#MostAlarmOperateTable'
                }
                var tableTitle = $(id).bootstrapTable('getOptions').columns[0];
                $.each(tableTitle, function(i, el) {
                    if (i >= 1) {
                        titleArray.push({
                            'key': el.field,
                            'value': el.title
                        })
                    }
                })
                var data = {};
                var pageSize = $(id).bootstrapTable('getOptions').pageSize;
                var pageNumber = $(id).bootstrapTable('getOptions').pageNumber;
                data.titles = JSON.stringify(titleArray);
                data.pageSize = pageSize;
                data.pageNumber = pageNumber;
                // page.logic.setParams();
                page.data.param = OPAL.form.getData("searchForm");
                $.extend(data, page.data.param);
                $('#titlesExport').val(data.titles);
                $('#pageSizeExport').val(data.pageSize);
                $('#pageNumberExport').val(data.pageNumber);
                $('#unitIdsExport').val(page.data.unitCodeList);
                $('#priorityFrom').val(data.priority);
                $('#startTimeForm').val(data.startTime);
                $('#endTimeForm').val(data.endTime);
                $('#alarmFlagIdForm').val(data.alarmFlagId);
                if ($("#checkShop").is(":checked")) {
                    $('#form').attr('action', exportnextMainCommon);
                    // $('#unitCodesFrom').val(page.data.unitCodes);
                    // $('#workshopCodesFrom').val(page.data.workshopCodes);
                    // $('#prdtCellIdFrom').val(page.data.prdtCellId);
                    // if (page.data.click == 'floodAlarmChartcj') {
                    //     $('#workshopCodesFrom').val([]);
                    //     $('#prdtCellIdFrom').val([]);
                    // } else if (page.data.click == 'floodAlarmChartzz') {
                    //     $('#prdtCellIdFrom').val([]);
                    // }
                } else {
                    $('#form').attr('action', exportAlarmDurationStattUrl);
                }
                $('#form').submit();
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function (node, checked) {
                        var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (unitIds != undefined && unitIds.length == 1) {
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    }
                }, false);
            },
            /**
             * 初始化图表
             * @param data
             */
            initFloodChart: function (data) {
                var results = JSON.parse(data);
                // page.logic.queryMostOperate();
                if (floodAlarmChart && !floodAlarmChart.isDisposed()) {
                    floodAlarmChart.clear();
                    floodAlarmChart.dispose();
                }
                if (results == undefined || results.length == 0) {
                    floodAlarmChart = OPAL.ui.chart.initEmptyChart('floodAlarmChart');
                    return;
                }
                var xAxis = [];
                var emergencyAlarmQuantity = [];
                var importantAlarmQuantity = [];
                var generalAlarmQuantity = [];
                var nullAlarmQuantity = [];
                unitId = results[0].unitId;
                unit = results[0].sname;
                for (var i = 0; i < results.length; i++) {
                    if (results[i].sname) {
                        xAxis.push(results[i].sname)
                    } else if (results[i].name) {
                        xAxis.push(results[i].name)
                    }
                    // emergencyAlarmQuantity.push($.parseJSON(results[i].emergencyAlarmQuantityEntity));
                    // importantAlarmQuantity.push($.parseJSON(results[i].importantAlarmQuantityEntity));
                    // generalAlarmQuantity.push($.parseJSON(results[i].generalAlarmQuantityEntity));
                    // nullAlarmQuantity.push($.parseJSON(results[i].nullAlarmQuantityEntity))
                    emergencyAlarmQuantity.push($.parseJSON(results[i].emergencyAlarmQuantity));
                    importantAlarmQuantity.push($.parseJSON(results[i].importantAlarmQuantity));
                    generalAlarmQuantity.push($.parseJSON(results[i].generalAlarmQuantity));
                    nullAlarmQuantity.push($.parseJSON(results[i].nullAlarmQuantity))
                }
                var option = {
                    color: ['#F4D312', '#FE6732', '#CD1515', '#C9C9C9'],
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        itemHeight: 8,
                        itemWidth: 18,
                        data: ['一般', '重要', '紧急', '空'],
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        top: '10%',
                        height: '270px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            show: true,
                            splitNumber: 5,
                            textStyle: {
                                fontSize: 12,
                                color: '#000'
                            },
                        },
                        data: xAxis,
                    }],
                    dataZoom: [{
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        // left: '9%',
                        bottom: -5,
                        start: 0,
                        end: 100 //初始化滚动条
                    }],
                    yAxis: {
                        splitLine: {
                            show: false
                        },
                        type: 'value',
                        name: "(分钟)"
                    },
                    series: [{
                        name: '一般',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: generalAlarmQuantity
                    },
                    {
                        name: '重要',
                        type: 'bar',
                        barWidth: '20px',
                        stack: 'sum',
                        data: importantAlarmQuantity,
                    },
                    {
                        name: '紧急',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: emergencyAlarmQuantity
                    },
                    {
                        name: '空',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: nullAlarmQuantity
                    }]
                };
                floodAlarmChart = echarts.init(document.getElementById('floodAlarmChart'));
                floodAlarmChart.setOption(option);
                page.data.param.unitIds = [unitId];
                page.data.unitCodeList=[unitId];
                floodChartConfig = option;
                floodAlarmChart.on('click', function (param) {
                    var indexs = param.dataIndex;
                    page.data.param.unitIds = [results[indexs].unitId];
                    unit = param.name;

                    page.logic.queryMostOperate();

                });
                page.logic.queryMostOperate();
            },

            // 展示空数据的图表
            EmptyChart: function() {
                tabChart.floodAlarmChartcj = OPAL.ui.chart.initEmptyChart('floodAlarmChartcj');
                tabChart.floodAlarmChartzz = OPAL.ui.chart.initEmptyChart('floodAlarmChartzz');
                tabChart.floodAlarmChartdy = OPAL.ui.chart.initEmptyChart('floodAlarmChartdy');
            },

            /**
             * 初始化图表
             * @param data
             */
            initFloodChartCom: function (data,Chartid,func) {              
                var results = JSON.parse(data);
                // page.logic.queryMostOperate();
                if (tabChart[Chartid] && !tabChart[Chartid].isDisposed()) {
                    tabChart[Chartid].clear();
                    tabChart[Chartid].dispose();
                }
                if (results == undefined || results.length == 0) {
                    // tabChart[Chartid] = OPAL.ui.chart.initEmptyChart(Chartid);
                    page.logic.EmptyChart()
                    return;
                }
                var xAxis = [];
                var emergencyAlarmQuantity = [];
                var importantAlarmQuantity = [];
                var generalAlarmQuantity = [];
                var nullAlarmQuantity = [];
                unitId = results[0].unitId;
                unit = results[0].sname;
                var nextparam = {
                    code: '',
                    num: 0
                }
                for (var i = 0; i < results.length; i++) {
                    if (results[i].sname) {
                        xAxis.push(results[i].sname)
                    } else if (results[i].name) {
                        xAxis.push(results[i].name)
                    }
                    emergencyAlarmQuantity.push($.parseJSON(results[i].emergencyAlarmQuantity));
                    importantAlarmQuantity.push($.parseJSON(results[i].importantAlarmQuantity));
                    generalAlarmQuantity.push($.parseJSON(results[i].generalAlarmQuantity));
                    nullAlarmQuantity.push($.parseJSON(results[i].nullAlarmQuantity))
                    if (nextparam.num < Math.abs($.parseJSON(results[i].emergencyAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].emergencyAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].importantAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].importantAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].generalAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].generalAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].nullAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].nullAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                }
                var option = {
                    color: ['#F4D312', '#FE6732', '#CD1515', '#C9C9C9'],
                    tooltip: {
                        trigger: 'axis',
                    },
                    legend: {
                        itemHeight: 8,
                        itemWidth: 18,
                        data: ['一般', '重要', '紧急', '空'],
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        top: '10%',
                        height: '270px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            show: true,
                            splitNumber: 5,
                            textStyle: {
                                fontSize: 12,
                                color: '#000'
                            },
                        },
                        data: xAxis,
                    }],
                    dataZoom: [{
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        // left: '9%',
                        bottom: -5,
                        start: 0,
                        end: 100 //初始化滚动条
                    }],
                    yAxis: {
                        splitLine: {
                            show: false
                        },
                        type: 'value',
                        name: "(分钟)"
                    },
                    series: [{
                        name: '一般',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: generalAlarmQuantity
                    },
                    {
                        name: '重要',
                        type: 'bar',
                        barWidth: '20px',
                        stack: 'sum',
                        data: importantAlarmQuantity,
                    },
                    {
                        name: '紧急',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: emergencyAlarmQuantity
                    },
                    {
                        name: '空',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: nullAlarmQuantity
                    }]
                };
                tabChart[Chartid] = echarts.init(document.getElementById(Chartid));
                tabChart[Chartid].setOption(option);
                page.data.param.unitIds = [unitId];
                page.data.unitCodeList=[unitId];
                floodChartConfig = option;
                tabChart[Chartid].on('click', function (param) {
                    var indexs = param.dataIndex;
                    page.data.click = Chartid
                    if (Chartid == 'floodAlarmChartcj') {
                        page.data.param.workshopCodes = [results[indexs].code];
                        page.data.workshopCodes = [results[indexs].code]
                        page.logic.querynextChart('floodAlarmChartzz');
                    } else if(Chartid == 'floodAlarmChartzz') {
                        page.data.param.unitCodes = [results[indexs].code];
                        page.data.unitCodes = [results[indexs].code]
                        page.logic.querynextChart('floodAlarmChartdy');
                    } else if(Chartid == 'floodAlarmChartdy') {
                        page.data.param.prdtCellId = [results[indexs].code];
                        page.data.prdtCellId = [results[indexs].code]
                    }
                    // page.logic.queryMostOperate();
                });
                
                if (Chartid == 'floodAlarmChartcj') {
                    page.data.param.workshopCodes = [nextparam.code];
                    page.data.workshopCodes = [nextparam.code];
                    page.logic.querynextChart('floodAlarmChartzz');
                } else if(Chartid == 'floodAlarmChartzz') {
                    page.data.param.unitCodes = [nextparam.code];
                    page.data.unitCodes = [nextparam.code];
                    page.logic.querynextChart('floodAlarmChartdy');
                } else if (Chartid == 'floodAlarmChartdy') {
                    page.data.param.prdtCellId = [nextparam.code];
                    page.data.prdtCellId = [nextparam.code];
                    // page.logic.queryMostOperate();
                }
            },

            // 获取chart数据
            querynextChart: function(Chartid) {
                var url = ''
                if (Chartid == 'floodAlarmChartzz') {
                    url = TotalUnit
                } else if(Chartid == 'floodAlarmChartdy') {
                    url = TotalPrdtcell
                }
                $.ajax({
                    url: url,
                    data: page.data.param,
                    dataType: 'json',
                    success: function (data) {
                        var result = $.ET.toObjectArr(data);
                        page.logic.initFloodChartCom(JSON.stringify(result),Chartid);
                        if (Chartid == 'floodAlarmChartdy') {
                            page.logic.queryMostOperatecj();
                        }
                    },
                    error: function () {
                        // $("#myModal").modal('hide');
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
            },



            /**
             * 初始化Table数据
             * @param data
             */
            initTableDisplay: function (data) {
                var results = JSON.parse(data);
                var options = {
                    // detailView: true, //父子表
                    striped: true, //是否显示行间隔色
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    height: 350,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'sname',
                        title: '装置',
                        align: 'left',
                    }, {
                        field: 'emergencyAlarmQuantity',
                        title: '紧急时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.emergencyAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.emergencyAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'1\')">' + row.emergencyAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'importantAlarmQuantity',
                        title: '重要时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.importantAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.importantAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'2\')">' + row.importantAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'generalAlarmQuantity',
                        title: '一般时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.generalAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.generalAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'3\')">' + row.generalAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'nullAlarmQuantity',
                        title: '优先级为空(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.nullAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.nullAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'9\')">' + row.nullAlarmQuantity + '</a>'
                            }
                        }
                    },
                    {
                        field: 'totalAlarmQuantity',
                        title: '合计(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'-1\')">' + row.totalAlarmQuantity + '</a>'
                        }
                    }
                    ]
                };
                $('#floodTable').bootstrapTable(options);
                $('#floodTable').bootstrapTable('refreshOptions', options);
                if (results == undefined) {
                    results = [];
                }
                $("#floodTable").bootstrapTable("load", results);
            },
            /**
             * 初始化Table数据
             * @param data
             */
            initTableDisplaycj: function (data) {
                var results = JSON.parse(data);
                var options = {
                    // detailView: true, //父子表
                    striped: true, //是否显示行间隔色
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    height: 350,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'sname',
                        title: '装置',
                        align: 'left',
                    }, {
                        field: 'emergencyAlarmQuantity',
                        title: '紧急时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.emergencyAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.emergencyAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'1\')">' + row.emergencyAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'importantAlarmQuantity',
                        title: '重要时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.importantAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.importantAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'2\')">' + row.importantAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'generalAlarmQuantity',
                        title: '一般时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.generalAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.generalAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'3\')">' + row.generalAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'nullAlarmQuantity',
                        title: '优先级为空(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.nullAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.nullAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'9\')">' + row.nullAlarmQuantity + '</a>'
                            }
                        }
                    },
                    {
                        field: 'totalAlarmQuantity',
                        title: '合计(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'-1\')">' + row.totalAlarmQuantity + '</a>'
                        }
                    }
                    ]
                };
                $('#floodTable').bootstrapTable(options);
                $('#floodTable').bootstrapTable('refreshOptions', options);
                if (results == undefined) {
                    results = [];
                }
                $("#floodTable").bootstrapTable("load", results);
            },
            /**
             * 报警次数详情
             */
            showOperateDetail: function (unitName, unitId, priorityName) {
                layer.open({
                    type: 2,
                    title: '报警时长详情',
                    closeBtn: 1,
                    area: ['85%', '420px'],
                    shadeClose: false,
                    content: 'AlarmDurationDtl.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        let obj = {
                            unitName: unitName,
                            unitId: unitId,
                            priorityName: priorityName,
                            startTime: moment(page.data.param.startTime).format('YYYY-MM-DD HH:mm:ss'),
                            endTime: moment(page.data.param.endTime).format('YYYY-MM-DD HH:mm:ss'),
                            alarmFlagId: page.data.param.alarmFlagId
                        };
                        iframeWin.page.logic.setData(obj);
                    }
                });
            },
            /**
             * 设置日期插件
             */
            initTime: function () {
                var myDate = new Date();
                var start = laydate.render({
                    elem: '#startTime', //指定元素
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd', //日期格式
                    value: getStartTime,
                    max: getEndTime, //最大日期
                });
                var end = laydate.render({
                    elem: '#endTime',
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd',
                    value: getEndTime,
                    max: getEndTime,
                });
                $('#startTime').attr('maxDate', getEndTime)
                $('#endTime').attr('maxDate', getEndTime)
            },
            getShowTime: function () {
                $.ajax({
                    url: getShowTimeUrl,
                    // async: true,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        // getStartTime = dataArr[0].value.split(' ')[0];
                        // getEndTime = dataArr[1].value.split(' ')[0];
                        getStartTime = dataArr[0].value;
                        // getEndTime = dataArr[1].value;
                        getEndTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
                        //设置时间插件
                        page.logic.initTime();
                        document.getElementById("btnSearch").click();
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            //初始化优先级
            initPriority: function () {
                OPAL.ui.getComboMultipleSelect('priority', priorityUrl, {
                    data: {
                        'isAll': true
                    },
                    // treeviewConfig: {
                    //     onCheck: function (node) {
                    //         alert(node)
                    //     },
                    // }
                }, true, function () {
                    var treeView = $("#priority").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#priority").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化查询 报警标识
             */
            initAlarmFlagList: function () {
                OPAL.ui.getComboMultipleSelect('alarmFlagId', alarmFlagListUrl, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#alarmFlagId").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#alarmFlagId").combotree("checkAllNodes");
                });
            },
            /**
             * 加载 下方表格
             */
            queryMostOperate: function () {
                $("#MostAlarmOperateTable").bootstrapTable('refresh', {
                    //"url": alarmDurStattUrl,
                    "url": alarmDurStattPageUrl,
                    "pageNumber": 1
                });
            },
            //初始化下方表格
            initOpetateTable: function () {
                page.logic.initBootstrapTable("MostAlarmOperateTable", {
                    columns: [{
                        title: "序号", formatter: function (value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1) * data.pageSize;
                        }, rowspan: 1, align: 'center', width: '80px'
                    }, {
                        field: 'unitName', title: '装置', align: 'center', width: '180px'
                    }, {
                        field: 'prdtCellName', title: '生产单元', align: 'center', width: '180px'
                    }, {
                        field: 'location', title: '参数名称', align: 'center',
                    }, {
                        field: 'tag', title: '位号', align: 'center', width: '200px'
                    }, {
                        field: 'monitorTypeStr', title: '专业', align: 'center', width: '60px'
                    }, {
                        field: 'priorityName', title: "优先级", align: 'center', width: '60px'
                    }, {
                        field: 'alarmTime', title: '报警时间', align: 'center', width: '200px'
                    }, {
                        field: 'recoveryTime', title: '结束时间', align: 'center', width: '200px'
                    }, {
                        field: 'continuousHour', title: '时长(分钟)', align: 'center', width: '80px'
                    }, {
                        field: 'alarmFlagName', title: '报警等级', align: 'center', width: '80px'
                    }]
                    // onExpandRow: function (index, row, $detail) {
                    //     page.data.subParam.unitIds = row['unitCode'];
                    //     page.data.subParam.alarmFlagId = row['alarmFlagId'];
                    //     page.data.subParam.tag = row['tag'];
                    //     page.data.subParam.priority = row['priority'];
                    //     page.logic.initCausalSubTable(index, row, $detail);
                    // }
                }, page.logic.queryParams);
                var tableOption = $('#MostAlarmOperateTable').bootstrapTable('getOptions');
                //tableOption.pageList = [20];
                $("#MostAlarmOperateTable").bootstrapTable('refreshOptions', tableOption);
            },

            /**
             * 加载 下方车间表格
             */
            queryMostOperatecj: function () {
                $("#MostAlarmOperateTablecj").bootstrapTable('refresh', {
                    //"url": alarmDurStattUrl,
                    "url": alarmDurStattPageUrl,
                    "pageNumber": 1
                });
            },
            //初始化下方车间表格
            initOpetateTablecj: function () {
                page.logic.initBootstrapTablecj("MostAlarmOperateTablecj", {
                    // detailView: true,
                    // cache: false,
                    columns: [{
                        title: "序号", formatter: function (value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1) * data.pageSize;
                        }, rowspan: 1, align: 'center', width: '80px'
                    }, {
                        field: 'unitName', title: '装置', align: 'center', width: '180px'
                    }, {
                        field: 'prdtCellName', title: '生产单元', align: 'center', width: '180px'
                    }, {
                        field: 'location', title: '参数名称', align: 'center',
                    }, {
                        field: 'tag', title: '位号', align: 'center', width: '200px'
                    }, {
                        field: 'monitorTypeStr', title: '专业', align: 'center', width: '60px'
                    }, {
                        field: 'priorityName', title: "优先级", align: 'center', width: '60px'
                    }, {
                        field: 'alarmTime', title: '报警时间', align: 'center', width: '200px'
                    }, {
                        field: 'recoveryTime', title: '结束时间', align: 'center', width: '200px'
                    }, {
                        field: 'continuousHour', title: '时长(分钟)', align: 'center', width: '80px'
                    }, {
                        field: 'alarmFlagName', title: '报警等级', align: 'center', width: '80px'
                    }]/*,
                    onExpandRow: function (index, row, $detail) {
                        page.data.subParam.unitIds = row['unitCode'];
                        page.data.subParam.alarmFlagId = row['alarmFlagId'];
                        page.data.subParam.tag = row['tag'];
                        page.data.subParam.priority = row['priority'];
                        page.logic.initCausalSubTablecj(index, row, $detail);
                    }*/
                }, page.logic.queryParams);
                var tableOption = $('#MostAlarmOperateTablecj').bootstrapTable('getOptions');
                $("#MostAlarmOperateTablecj").bootstrapTable('refreshOptions', tableOption);
            },

            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                if (page.data.click == 'floodAlarmChartcj') {
                    param.unitCodes = []
                    param.prdtCellId = []
                } else if (page.data.click == 'floodAlarmChartzz') {
                    param.prdtCellId = []
                }
                return $.extend(page.data.param, param);
            },

            /**
             * 查询子表格参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            subQueryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    alarmFlagId: alarmFlagId,
                    // isElimination: $("#isElimination").val(),
                    tag: tag,
                    priority: priority,
                    now: Math.random()
                };
                return $.extend(page.data.subParam, param);
            },

            /**
             * 初始化二级列表
             */
            initCausalSubTable: function (index, row, $detail) {
                alarmFlagId = row.alarmFlagId;
                tag = row.tag;
                priority = row.priority;
                var subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                OPAL.ui.initBootstrapTable(subId, {
                    url: alarmDurStattUrl,
                    striped: true,
                    pagination: false,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        align: 'center',
                        width: '80'
                    }, {
                        field: 'alarmTime',
                        title: '报警时间',
                        align: 'center',
                    }, {
                        field: 'continuousHour',
                        title: '时长(分钟)',
                        align: 'right',
                    }, {
                        field: 'recoveryTime',
                        title: '恢复时间',
                        align: 'center',
                    }, {
                        field: 'eventTypeName',
                        title: '事件类型',
                        align: 'left',
                    }, {
                        field: 'monitorTypeStr',
                        title: '专业',
                        align: 'left',
                    }],
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            /*"pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],*/
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                }, page.logic.subQueryParams)
            },

            initBootstrapTable: function (tableID, config, queryParams) {
                var _config = {
                    method: 'get',
                    url: '',
                    cache: false,
                    pagination: true, //启动分页
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "server", // 表示服务端请求 后台分页
                    //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder
                    //设置为limit可以获取limit, offset, search, sort, order
                    queryParamsType: "undefined",
                    queryParams: queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#' + tableID).find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                };
                $.extend(true, _config, config);
                $('#' + tableID).bootstrapTable(_config);
            },

            initBootstrapTablecj: function (tableID, config, queryParams) {
                var _config = {
                    method: 'get',
                    url: '',
                    cache: false,
                    pagination: true, //启动分页
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "server", // 表示服务端请求 后台分页
                    //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder
                    //设置为limit可以获取limit, offset, search, sort, order
                    queryParamsType: "undefined",
                    queryParams: queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#' + tableID).find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                };
                $.extend(true, _config, config);
                $('#' + tableID).bootstrapTable(_config);
            },

            /**
             * 初始化二级列表
             */
            initCausalSubTablecj: function (index, row, $detail) {
                alarmFlagId = row.alarmFlagId;
                tag = row.tag;
                priority = row.priority;
                var subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                OPAL.ui.initBootstrapTable(subId, {
                    url: alarmDurStattUrl,
                    striped: true,
                    pagination: false,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        align: 'center',
                        width: '80'
                    }, {
                        field: 'alarmTime',
                        title: '报警时间',
                        align: 'center',
                    }, {
                        field: 'continuousHour',
                        title: '时长(分钟)',
                        align: 'right',
                    }, {
                        field: 'recoveryTime',
                        title: '结束时间',
                        align: 'center',
                    }, {
                        field: 'eventTypeName',
                        title: '事件类型',
                        align: 'left',
                    }, {
                        field: 'monitorTypeStr',
                        title: '专业',
                        align: 'left',
                    }],
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            /*"pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],*/
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                }, page.logic.subQueryParams)
            },
        }
    };
    page.init();
    window.page = page;
});