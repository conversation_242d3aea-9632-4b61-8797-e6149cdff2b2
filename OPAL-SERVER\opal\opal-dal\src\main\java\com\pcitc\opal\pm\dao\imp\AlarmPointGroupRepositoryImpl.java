package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.*;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmPointGroupRepositoryCustom;
import com.pcitc.opal.pm.dao.AlarmPointRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import com.pcitc.opal.pm.pojo.AlarmPointGroup;
import com.pcitc.opal.pm.pojo.SystRunParaConf;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.lang.reflect.Array;
import java.util.*;

/*
 * AlarmPointGroup实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_AlarmPointRepositoryImpl
 * 作    者：guoganxin
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描    述：AlarmPoint实体的Repository实现
 */
public class AlarmPointGroupRepositoryImpl extends BaseRepository<AlarmPointGroup, Long> implements AlarmPointGroupRepositoryCustom {

	@Override
	public AlarmPointGroupConfig getSingleAlarmPointGroupConfig(Long alarmPointGroupId) {
		try {
		String hql ="select new com.pcitc.opal.pm.dao.imp.AlarmPointGroupConfig(t.alarmPointGroupId,u.sname,t.groupName,t.alarmFlagId,t.des,t.unitCode,t.alarmFlagId)  " +
				"from AlarmPointGroup t "+
				"left join Unit u on t.unitCode=u.stdCode "+
				"where t.alarmPointGroupId=(:alarmPointGroupId) ";
		TypedQuery<AlarmPointGroupConfig> query  =this.getEntityManager().createQuery(hql,AlarmPointGroupConfig.class);
		query.setParameter("alarmPointGroupId",alarmPointGroupId);
		return query.getSingleResult();
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	public List<AlarmPointGroup> getSingleAlarmPointGroup(Long alarmPointGroupId) {
		try {
			String hql ="from AlarmPointGroup t "+
					"where t.alarmPointGroupId=(:alarmPointGroupId) ";
			TypedQuery<AlarmPointGroup> query  =this.getEntityManager().createQuery(hql,AlarmPointGroup.class);
			query.setParameter("alarmPointGroupId",alarmPointGroupId);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	public List<AlarmPointGroup> getAlarmPointGroupsByUnit(String unitCode) {
		try {
		String hql ="from AlarmPointGroup t "+
				"where t.unitCode=(:unitCode) ";
		TypedQuery<AlarmPointGroup> query  =this.getEntityManager().createQuery(hql,AlarmPointGroup.class);
		query.setParameter("unitCode",unitCode);
		return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	public PaginationBean<AlarmPointGroupConfig> getAlarmPointGroupsPage(String[] unitCodes, String groupName,Long companyId,Pagination page) {
			try {
		String hql ="select new com.pcitc.opal.pm.dao.imp.AlarmPointGroupConfig\n" +
				"	(t.alarmPointGroupId,u.sname,t.groupName,\n" +
//				"	group_concat(g.name),\n" +
				"	t.alarmFlagId,\n" +
				"	t.des,t.unitCode,t.alarmFlagId) " +
				"	from AlarmPointGroup t \n"+
				"	left join Unit u on t.unitCode=u.stdCode \n"+
//				"	left join AlarmFlag g on find_in_set(g.alarmFlagId,t.alarmFlagId)>0 \n"+
				"	where 1=1 \n";
			Map<String, Object> paramList = new HashMap<String, Object>();
			if (unitCodes!=null){
				hql+="	and t.unitCode in(:unitCodes) \n";
				paramList.put("unitCodes",Arrays.asList(unitCodes));
			}
			if (groupName!=null&&groupName!=""){
				hql+=(" and upper(t.groupName) like upper(:groupName) escape '/' ");
				paramList.put("groupName","%" + this.sqlLikeReplace(groupName) + "%");
			}
				//企业
				hql+=(" and t.companyId=(:companyId)\n");
				paramList.put("companyId",companyId);
				//hql+="	group by t.alarmPointGroupId \n";
				hql+="	order by t.unitCode ,t.groupName ";

			//List.count
			Query query =this.getEntityManager().createQuery(hql);
			this.setParameterList(query, paramList);
			Long count =Long.valueOf(query.getResultList().size());

			//重写分页
			BaseRepository br =new BaseRepository();
			PaginationBean<AlarmPointGroupConfig> bean = br.findCusTomAll(this.getEntityManager(),page,count, hql, paramList,AlarmPointGroupConfig.class);
			String aa =",";
			bean.getPageList().forEach(b->{
				if(b.getAlarmFlagName()!=null&&!b.getAlarmFlagName().equals("")) {
					StringBuilder res = new StringBuilder();
					if (b.getAlarmFlagName().indexOf(aa) != -1) {
						String[] bs = b.getAlarmFlagName().split(",");
						List<String> ls = Arrays.asList(bs);
						ls.forEach(l -> {
							res.append(getNameFromFlag(Long.valueOf(l)));
							res.append(",");
						});
					} else {
						Long vo = Long.valueOf(b.getAlarmFlagName());
						String fg = getNameFromFlag(vo);
						res.append(fg);
					}
					b.setAlarmFlagName(res.toString());
				}
			});
			return bean;
		} catch (Exception ex) {
			throw ex;
		}
	}

	public  String getNameFromFlag(Long id){
		if(id <=0){
			return "";
		}
		String hql ="select g.name from AlarmFlag g where g.alarmFlagId =(:id)";
		Map<String, Object> paramList = new HashMap<String, Object>();
		paramList.put("id",id);
		Query query =this.getEntityManager().createQuery(hql);
		this.setParameterList(query, paramList);
		return query.getSingleResult().toString();
	}



	/**
	 * 删除报警点分组实体
	 *
	 * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointGroupIds
	 *            报警点ID集合
	 * @return 消息结果类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteAlarmPointGroup(Long[] alarmPointGroupIds) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = " from AlarmPointGroup t " +
					"where t.companyId=:companyId " +
					"and t.alarmPointGroupId in (:alarmPointGroupIds)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			List<Long> alarmPointIdList = Arrays.asList(alarmPointGroupIds);
			paramList.put("alarmPointGroupIds", alarmPointIdList);
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",Long.valueOf(commonProperty.getCompanyId()) );
			TypedQuery<AlarmPointGroup> query = getEntityManager().createQuery(hql, AlarmPointGroup.class);
			this.setParameterList(query, paramList);
			List<AlarmPointGroup> alarmPointList = query.getResultList();
			alarmPointList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}




	/**
	 * 新增报警点分组
	 *
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointGroupEntity
	 *            报警点分组
	 * @return 消息结果类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addAlarmPointGroupDtl(AlarmPointGroup alarmPointGroupEntity) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			if(alarmPointGroupEntity.getAlarmFlagId()==null){
				alarmPointGroupEntity.setAlarmFlagId("");
			}
			this.getEntityManager().persist(alarmPointGroupEntity);
			commonResult.setResult(alarmPointGroupEntity);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		return commonResult;
	}
	/**
	 * 更新报警点
	 *
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointGroupEntity
	 *            报警点实体
	 * @return 消息结果类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult updateAlarmPointGroupDtl(AlarmPointGroup alarmPointGroupEntity) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			getEntityManager().merge(alarmPointGroupEntity);
			commonResult.setResult(alarmPointGroupEntity);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("更新成功！");
		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}


}
