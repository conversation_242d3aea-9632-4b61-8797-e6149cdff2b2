package com.pcitc.opal.ac.dao;

import com.pcitc.opal.ac.pojo.AlarmChangeItem;
import com.pcitc.opal.common.CommonResult;

import java.util.List;

/*
 * AlarmChangeItem实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_AlarmChangeItemRepositoryCustom
 * 作    者：xuelei.wang
 * 创建时间：2018/1/19
 * 修改编号：1
 * 描    述：AlarmChangeItem实体的Repository的JPA自定义接口
 */
public interface AlarmChangeItemRepositoryCustom {

    /**
     * 新增报警变更事项数据
     *
     * @param alarmChangeItem 报警变更事项数据
     * @return 返回结果信息类
     * <AUTHOR> 2018-01-23
     */
    CommonResult addAlarmChangeItem(AlarmChangeItem alarmChangeItem);

    /**
     * 删除报警变更事项实体
     *
     * @param alarmChangeItemIds 报警变更事项Id数组
     * @return 返回结果信息类
     * <AUTHOR> 2018-01-19
     */
    CommonResult deleteAlarmChangeItem(Long[] alarmChangeItemIds);

    /**
     * 删除“报警变更方案明细ID”对应的<报警变更事项>记录
     *
     * @param planDetailId 报警变更明细Id
     * @return 返回结果信息类
     * <AUTHOR> 2018-01-23
     */
    CommonResult deleteAlarmChangeItemByPDId(Long planDetailId);
    
    /**
     * 根据PlanDetailId获取报警变更事项数据
     *
     * @param planId       报警变更方案ID
     * @param planDetailId 报警变更方案明细ID
     * @return AlarmChangePlanDetail 报警变更事项
     * <AUTHOR> 2018-01-22
     */
    List<AlarmChangeItem> getAlarmChangeItemByPlanDetail(Long planId, Long planDetailId);

    /**
     * 根据报警变更方案ID和变更方案详情ID获取报警变更事项列表
     *
     * @param planId       变更方案ID
     * @param planDetailId 变更详情ID
     * @return 报警变更事项列表
     * <AUTHOR> 2018-01-22
     */
    List<AlarmChangeItem> getAlarmChangeItemByPlanDetailId(Long planId, Long planDetailId);
    /**
     * 根据报警变更方案详情ID获取报警变更事项列表
     *
     * @param planDetailIds 变更详情ID集合
     * @return 报警变更事项列表
     * <AUTHOR> 2018-01-22
     */
    List<AlarmChangeItem> getAlarmChangeItemByPlanDetailIds(Long[] planDetailIds);
    
    /**
     * 通过报警变更方案Id获取多条数据
     *
     * @param alarmChangePlanIds 报警变更方案Id数组
     * @return 报警变更事项实体集合
     * <AUTHOR> 2018-01-19
     */
    List<AlarmChangeItem> getAlarmChangeItemByPlanIds(Long[] alarmChangePlanIds);
}
