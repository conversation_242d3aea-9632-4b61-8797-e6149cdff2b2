package com.pcitc.opal.common.bll;

import com.pcitc.ssc.dps.inte.workflow.AppCallResult;
import com.pcitc.ssc.dps.inte.workflow.ExecuteTaskData;

import java.util.List;

/*
 * 流程服务接口
 * 模块编号： pcitc_opal_bll_interface_WorkFlowService
 * 作       者：xuelei.wang
 * 创建时间：2018/3/7
 * 修改编号：1
 * 描       述：流程服务接口
 */
public interface WorkFlowService {
    /**
     * 发起流程
     *
     * @param categoryCode 流程分类编码
     * @param businessId   业务ID
     * @param businessName 业务名称
     * @param businessCode 业务代码
     * @return 流程执行结果
     * <AUTHOR> 2018-03-08
     */
    AppCallResult startWorkFlow(String categoryCode,String businessId,String businessName,String businessCode) throws Exception;
    /**
     * 重新发起流程
     *
     * @param categoryCode 流程分类编码
     * @param businessId   业务ID
     * @param businessName 业务名称
     * @param businessCode 业务代码
     * @return 流程执行结果
     * <AUTHOR> 2018-03-09
     */
    AppCallResult restartWorkFlow(String categoryCode,String businessId,String businessName,String businessCode) throws Exception;
    /**
     * 推进工作流
     *
     * @param taskId  执行的待办ID
     * @return 流程执行结果
     * <AUTHOR> 2018-03-09
     */
    AppCallResult runWorkFlow(String taskId) throws Exception;

    /**
     * 流程退回
     *
     * @param taskId  执行的待办ID
     * @return 流程执行结果
     * <AUTHOR> 2018-03-09
     */
    AppCallResult revertWorkFlow(String taskId) throws Exception;

    /**
     * 获取流程待办列表
     *
     * @return
     * <AUTHOR> 2018-03-09
     */
    List<ExecuteTaskData> getWorkFlowTodoList() throws Exception;

    /**
     * 获取流程已办列表
     *
     * @return
     * <AUTHOR> 2018-03-09
     */
    List<ExecuteTaskData> getWorkFlowDoneList() throws Exception;

    /**
     * 获取流程Token
     *
     * @return token
     * @throws Exception
     * <AUTHOR> 2018-03-09
     */
    String getWorkFlowToken() throws Exception;
}
