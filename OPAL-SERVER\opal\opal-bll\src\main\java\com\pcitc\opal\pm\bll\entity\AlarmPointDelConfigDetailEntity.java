package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.pm.pojo.AlarmPointDelConfig;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.util.Date;

/**
 * @USER: chenbo
 * @DATE: 2022/12/2
 * @TIME: 14:46
 * @DESC: 报警剔除配置详细信息
 **/
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class AlarmPointDelConfigDetailEntity {
    /**
     * 装置编码
     */
    private String unitCode;

    /**
     * 报警点id（逗号拼接）
     */
    private String alarmPointId;

    /**
     *报警标识id（逗号拼接）
     */
    private String alarmFlagId;

    /**
     * 剔除开始时间
     */
    private Date delStartTime;

    /**
     * 剔除结束时间
     */
    private Date delEndTime;

    public AlarmPointDelConfigDetailEntity(AlarmPointDelConfig a) {
        this.unitCode = a.getUnitCode();
        this.alarmPointId = a.getAlarmPointId();
        this.alarmFlagId = a.getAlarmFlagId();
        this.delStartTime = a.getDelStartTime();
        this.delEndTime = a.getDelEndTime();
    }
}
