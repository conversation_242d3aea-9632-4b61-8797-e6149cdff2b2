package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.PrdtCellRepositoryCustom;
import com.pcitc.opal.pm.pojo.PrdtCell;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * PrdtCell实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_PrdtCellRepositoryImpl
 * 作       者：zheng.yang
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描       述：PrdtCell实体的Repository实现   
 */
public class PrdtCellRepositoryImpl extends BaseRepository<PrdtCell, Long> implements PrdtCellRepositoryCustom {

    /**
     * 单元唯一性校验
     *
     * @param prdtCell 生产单元实体
     * @return 查询返回信息类
     * <AUTHOR> 2017-09-25
     */
    @Override
    public CommonResult prdtCellValidation(PrdtCell prdtCell) {
        CommonResult commonResult = new CommonResult();
        //企业
        CommonProperty commonProperty = new CommonProperty();
        // 名称和装置ID联合唯一性校验
        try {
            StringBuilder jpql = new StringBuilder(
                    "from PrdtCell t where t.companyId=:companyId and t.name=:name and t.unitId=:unitId and t.prdtCellId<>:prdtCellId");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("name", prdtCell.getName());
            paramList.put("unitId", prdtCell.getUnitId());
            paramList.put("prdtCellId", prdtCell.getPrdtCellId() == null ? 0 : prdtCell.getPrdtCellId());
            paramList.put("companyId",commonProperty.getCompanyId());
            long count = this.getCount(jpql.toString(), paramList);
            if (count > 0) {
                throw new Exception("该装置下此名称已存在！");
            }
            // 简称和装置ID联合唯一性校验
            jpql = new StringBuilder(
                    "from PrdtCell t where t.companyId=:companyId and t.sname=:sname and t.unitId=:unitId and t.prdtCellId<>:prdtCellId");
            paramList = new HashMap<String, Object>();
            paramList.put("sname", prdtCell.getSname());
            paramList.put("unitId", prdtCell.getUnitId());
            paramList.put("prdtCellId", prdtCell.getPrdtCellId() == null ? 0 : prdtCell.getPrdtCellId());
            paramList.put("companyId",commonProperty.getCompanyId());
            count = this.getCount(jpql.toString(), paramList);
            if (count > 0) {
                throw new Exception("该装置下此简称已存在！");
            }
        } catch (Exception e) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(e.getMessage());
        }
        return commonResult;
    }

    /**
     * 新增单元
     *
     * @param prdtCell 添加的实体
     * <AUTHOR> 2017-09-25
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addPrdtCell(PrdtCell prdtCell) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(prdtCell);
            commonResult.setResult(prdtCell);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 删除单元
     *
     * @param prdtCellIds 单元ID集合
     * @return 消息结果类
     * <AUTHOR> 2017-09-25
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult deletePrdtCell(Long[] prdtCellIds) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            String hql = "from PrdtCell t where t.prdtCellId in (:prdtCellIds) and t.companyId=:companyId";
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> prdtCellList = Arrays.asList(prdtCellIds);
            paramList.put("prdtCellIds", prdtCellList);
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            TypedQuery<PrdtCell> query = getEntityManager().createQuery(hql, PrdtCell.class);
            this.setParameterList(query, paramList);
            List<PrdtCell> prdtCellsList = query.getResultList();
            prdtCellsList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 更新生产单元
     *
     * @param prdtCell 单元实体
     * @return 消息结果类
     * <AUTHOR> 2017-09-25
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult updatePrdtCell(PrdtCell prdtCell) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(prdtCell);
            commonResult.setResult(prdtCell);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 获取单元单个实体
     *
     * @param prdtCellId 单元ID
     * @return 单元实体
     * <AUTHOR> 2017-09-25
     */
    @Override
    public PrdtCell getSinglePrdtCell(Long prdtCellId) {
        try {
            return getEntityManager().find(PrdtCell.class, prdtCellId);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取单元实体
     *
     * @param prdtCellIds 单元ID集合
     * @return 单元实体集合
     * <AUTHOR> 2017-09-25
     */
    @Override
    public List<PrdtCell> getPrdtCell(Long[] prdtCellIds) {
        try {
            // 查询字符串
            String hql = "from PrdtCell t where  t.companyId=:companyId  ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (prdtCellIds!=null && prdtCellIds.length > 0) {
                hql += " and t.prdtCellId in (:prdtCellIds)";
                List<Long> prdtCellIdsList = Arrays.asList(prdtCellIds);
                paramList.put("prdtCellIds", prdtCellIdsList);
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            TypedQuery<PrdtCell> query = getEntityManager().createQuery(hql, PrdtCell.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取单元实体（分页）
     *
     * @param unitCodes 装置编码集合
     * @param name    单元名称
     * @param inUse   是否启用
     * @param page    分页参数
     * @return 单元实体（分页）
     * <AUTHOR> 2017-09-25
     */
    @Override
    public PaginationBean<PrdtCell> getPrdtCell(String[] unitCodes, String name, Integer inUse, Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from PrdtCell t where 1=1 and t.companyId=:companyId ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 名称/简称
            if (!StringUtils.isEmpty(name)) {
                hql.append("  and (t.name like :name)");
                paramList.put("name", "%" + this.sqlLikeReplace(name) + "%");
            }
            if (unitCodes != null && unitCodes.length > 0) {
                hql.append("  and (t.unitId in (:unitIds))");
                List<String> idsList = Arrays.asList(unitCodes);
                paramList.put("unitIds", idsList);
            }
            if (inUse != null) {
                hql.append("  and (t.inUse =:inUse)");
                paramList.put("inUse", inUse);
            }
            hql.append(" order by t.unitId,t.sortNum,t.sname");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public List<PrdtCell> getPrdtCellByUnitCode(String[] unitCodes) {
        try {
            // 查询字符串
            String hql = "from PrdtCell t where t.companyId=:companyId  ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (unitCodes!=null && unitCodes.length > 0) {
                hql += " and t.unitId in (:unitCodes)";

                paramList.put("unitCodes", Arrays.asList(unitCodes));
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
            Integer companyId = commonProperty.getCompanyId();
            if (companyId == null){
                Integer companyIdByUnit = getCompanyIdByUnit(unitCodes);
                paramList.put("companyId",companyIdByUnit);
            }else {
                paramList.put("companyId",companyId);
            }

            TypedQuery<PrdtCell> query = getEntityManager().createQuery(hql, PrdtCell.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public List<PrdtCell> getPrdtCellByCompanyId(Long companyId) {

        String hql = "from PrdtCell where companyId = :companyId";

        TypedQuery<PrdtCell> query = getEntityManager().createQuery(hql, PrdtCell.class);
        query.setParameter("companyId", (Math.toIntExact(companyId)));

        return query.getResultList();
    }
}
