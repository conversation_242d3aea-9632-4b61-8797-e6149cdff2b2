package com.pcitc.opal.common;

import java.text.Collator;
import java.util.Comparator;
import java.util.Locale;

/*
 * lambda表达式中文拼音比较器
 * 模块编号：pcitc_opal_common_class_ComparatorList
 * 作       者：dageng.sun
 * 创建时间：2017/12/05
 * 修改编号：1
 * 描       述：中文拼音比较器
 */
public class ComparatorList {
	
	@SuppressWarnings("unchecked")
	public static <T extends Comparable<? super T>> Comparator<T> orderByASC() {
        return (Comparator<T>) ComparatorList.NaturalOrderComparator.INSTANCE;
    }
	
	enum NaturalOrderComparator implements Comparator<Comparable<Object>> {
        INSTANCE;

        public int compare(Comparable<Object> value1, Comparable<Object> value2) {
            if (value1.getClass().getName().equals("java.lang.String")) {
                String s1 = value1.toString();
                String s2 = value2.toString();
                return Collator.getInstance(Locale.CHINESE).compare(s1, s2);
            }
            return 0; // 0表示相同。
        }

    }

}
