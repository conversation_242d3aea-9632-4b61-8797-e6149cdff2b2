package com.pcitc.opal.pm.dao;

import com.pcitc.opal.pm.pojo.EventType;
import com.pcitc.opal.pm.pojo.UnitPerson;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

/*
 * 装置人员信息实体的Repository的JPA标准接口
 * 模块编号：pcitc_opal_dal_interface_UnitPersonRepository
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/18
 * 修改编号：1
 * 描    述：装置人员信息实体的Repository的JPA标准接口
 */
@Repository
public interface UnitPersonRepository extends JpaRepository<UnitPerson, Long>, UnitPersonRepositoryCustom {

}
