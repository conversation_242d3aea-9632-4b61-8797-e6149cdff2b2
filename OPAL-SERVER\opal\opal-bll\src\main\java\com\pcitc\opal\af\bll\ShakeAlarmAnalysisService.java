package com.pcitc.opal.af.bll;

import java.util.Date;
import java.util.List;

import com.pcitc.opal.af.bll.entity.ShakeAlarmAnalysisChartEntity;
import org.springframework.stereotype.Service;

import com.pcitc.opal.af.bll.entity.ShakeAlarmAnalysisDataEntity;
import com.pcitc.opal.af.bll.entity.ShakeAlarmAnalysisTableEntity;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

/*
 * 震荡报警分析的逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_ShakeAlarmAnalysisService
 * 作    者：kun.zhao
 * 创建时间：2017/11/2
 * 修改编号：1
 * 描    述：震荡报警分析的逻辑层接口
 */
@Service
public interface ShakeAlarmAnalysisService {

	/**
	 * 震荡报警分析——柱状图
	 * 
	 * <AUTHOR> 2017-11-02
	 * @param startTime 查询范围起始时间
	 * @param endTime	查询范围结束时间
	 * @param unitCodes	装置编码数组
	 * @param prdtIds	生产单元ID数组
	 * @return 震荡报警事件柱状图数据
	 * @throws Exception
	 */
	List<ShakeAlarmAnalysisDataEntity> getShakeAlarmAnalysisEntity(Date startTime, Date endTime, String[] unitCodes,
			Long[] prdtIds) throws Exception;

	/**
	 * 震荡报警分析——表格
	 * 
	 * <AUTHOR> 2017-11-02
	 * @param startTime		时间范围开始
	 * @param endTime		时间范围结束
	 * @param alarmPointTag 报警点位号
	 * @param alarmFlagId   报警标识ID
	 * @param page			分页对象
	 * @return 震荡报警事件图表数据
	 * @throws Exception
	 */
	PaginationBean<ShakeAlarmAnalysisTableEntity> getShakeAlarmAnalysisTableEntity(Date startTime, Date endTime,
			String alarmPointTag, Long alarmFlagId, Pagination page) throws Exception;

	/**
	 * 震荡报警分析——下面图表
	 *
	 * <AUTHOR> 2018-04-23
	 * @param startTime		时间范围开始
	 * @param endTime		时间范围结束
	 * @param alarmPointTag 报警点位号
	 * @param alarmFlagId   报警标识ID
	 * @return 震荡报警事件图表数据
	 * @throws Exception
	 */
	 ShakeAlarmAnalysisChartEntity getShakeAlarmAnalysisChartEntity(Date startTime, Date endTime,
					String alarmPointTag, Long alarmFlagId) throws Exception;

	/**
	 * 震荡报警分析——下面图表修改值
	 *
	 * <AUTHOR> 2018-04-23
	 * @param startTime		时间范围开始
	 * @param endTime		时间范围结束
	 * @param alarmPointTag 报警点位号
	 * @param alarmFlagId   报警标识ID
	 * @param alarmValue    修改值
	 * @return 震荡报警事件图表数据
	 * @throws Exception
	 */
	ShakeAlarmAnalysisChartEntity getChangeShakeAlarmAnalysisl(Date startTime, Date endTime,
																   String alarmPointTag, Long alarmFlagId,Double alarmValue) throws Exception;
}
