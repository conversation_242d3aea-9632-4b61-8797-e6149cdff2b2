var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var alarmFlagListUrl = OPAL.API.commUrl + '/getAlarmFlagList';
var persistentAlarmUrl = OPAL.API.afUrl + '/persistentAlarmAnalysis';
var shelveAlarmUrl = OPAL.API.afUrl + '/persistentAlarmAnalysis/getShelveAlarmAnalysis';
var shieldAlarmUrl = OPAL.API.afUrl + '/persistentAlarmAnalysis/getShieldAlarmAnalysis';
var workTeamUrl = OPAL.API.commUrl + "/getWorkTeam";
var exportUrl = OPAL.API.afUrl + '/persistentAlarmAnalysis/alarmAnalysisExportExcel'; //导出1
var export2Url = OPAL.API.afUrl + '/persistentAlarmAnalysis/shelveAlarmAnalysisExportExcel'; //导出2
var export3Url = OPAL.API.afUrl + '/persistentAlarmAnalysis/shieldAlarmAnalysisExportExcel'; //导出3
var isLoading = true;
var tabIndex = 1;
$(function() {
	var page = {
		init: function() {
			this.bindUi();
			OPAL.util.extendDate();
			OPAL.ui.initDateTimePeriodPicker({
				format: 'yyyy-MM-dd HH:mm:ss',
				type: 'datetime',
			}, function() {
				page.logic.initWorkTeam();
			});
			page.logic.initUnitTree();
			page.logic.initAlarmFlag();
			page.logic.initEventType();
			page.logic.initTablePersistent();
			page.logic.initTableShelve();
			page.logic.initTableShield();
			$('#workTeamIds').html("");
			$("#workTeamIds").prop('disabled', true);

            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("PersistentAlarmAnalysis");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
            //默认查询数据
            setTimeout(function () {
                if ($("#alarmFlagId").val()!=null&&$("#isHandle").val()!=null) {
                    page.logic.search();
                }
            }, 500);
		},
		bindUi: function() {
			$('#btnSearch').click(function() {
                isLoading = false;
				page.logic.search();
			});
			$("#persistBtn").click(function() {
				tabIndex = 1;
				$("#isHandleSpan").show();
				$("#eventTypeDiv").hide();
				$("#teamDiv").hide();
			})
			$("#shelveBtn").click(function() {
				tabIndex = 2;
				$("#isHandleSpan").hide();
				$("#eventTypeDiv").hide();
				$("#teamDiv").show();
			})
			$("#shieldBtn").click(function() {
				tabIndex = 3;
				$("#isHandleSpan").hide();
				$("#eventTypeDiv").show();
				$("#teamDiv").show();
			})
			$("#startTime").unbind("change");
			$("#endTime").unbind("change");
			$("#startTime").change(function() {
				page.logic.initWorkTeam();
			})
			$("#endTime").change(function() {
				page.logic.initWorkTeam();
			})
            // 导出
            $("#AlarmExport").click(function() {
                page.logic.exportExcel();
            })
		},
		data: {
			//查询参数
			param: {}
		},
		logic: {
            // 导出
            exportExcel: function() {
				let url = '';
				let IdName = '';
				if (tabIndex == 1) {
					url = exportUrl;
					IdName = '#tblPersistent';
				} else if(tabIndex == 2) {
					url = export2Url;
					IdName = '#tblShelve';
				} else {
					url = export3Url;
					IdName = '#tblShield';
				}
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                $("#AlarmExport").attr("disabled",true);
                var titleArray = [{'key': 'index','value': '序号'}];
                var tableTitle = $(IdName).bootstrapTable('getOptions').columns[0];
                $.each(tableTitle, function(i, el) {
					if (i > 0) {
						titleArray.push({
							'key': el.field,
							'value': el.title
						})
					}
                })
                var data = OPAL.form.getData('searchForm');
                var pageSize = $(IdName).bootstrapTable('getOptions').pageSize;
                var pageNumber = $(IdName).bootstrapTable('getOptions').pageNumber;
                data.titles = JSON.stringify(titleArray);
                data.pageSize = pageSize;
                data.pageNumber = pageNumber;
                $('#formExPort').attr('action', url);
                $('#titles').val(data.titles);
                $('#pageSize').val(9999);
                $('#pageNumber').val(data.pageNumber);
                $('#unitIds1').val(data.unitIds);
                $('#startTime1').val(data.startTime);
                $('#endTime1').val(data.endTime);
                $('#prdtCellIds1').val(data.prdtCellIds);
                $('#eventTypeId1').val(data.eventTypeId);
                $('#alarmFlagId1').val(data.alarmFlagId);
                $('#isHandle1').val(data.isHandle);
                $('#workTeamIds1').val(data.workTeamIds);
                var options = {
                    success: function() {
                        $("#AlarmExport").attr("disabled",false);
                    },
                    error:function () {
                    }
                };

                $("#formExPort").ajaxSubmit(options);
                $('#formExPort').submit();
            },
			//查询
			search: function() {
				var flag = OPAL.util.checkDateIsValid();
				if (!flag) {
					return false;
				}
                $("#btnSearch").attr('disabled',true);
				page.data.param = OPAL.form.getData('searchForm');
				$("#tblPersistent").bootstrapTable('refresh', {
					"url": persistentAlarmUrl,
					"pageNumber": 1
				});
				$("#tblShelve").bootstrapTable('refresh', {
					"url": shelveAlarmUrl,
					"pageNumber": 1
				});
				$("#tblShield").bootstrapTable('refresh', {
					"url": shieldAlarmUrl,
					"pageNumber": 1
				});
			},
			//持续报警表格
			initTablePersistent: function() {
				OPAL.ui.initBootstrapTable2("tblPersistent", {
					columns: [{
						title: "序号", formatter: function (value, row, index) {
							var tableOption = $('#tblPersistent').bootstrapTable('getOptions');
							var pageNumber = tableOption.pageNumber;
							var pageSize = tableOption.pageSize;
							return (index + 1) + (pageNumber - 1) * pageSize;
						}, rowspan: 1, align: 'center', width: '80px'
					}, {
						title: "装置", field: 'unitName', rowspan: 1, align: 'left', width: '140px'
					}, {
						title: "生产单元", field: 'prdtCellName', rowspan: 1, align: 'left', width: '140px'
					}, {
						title: "参数名称", field: 'alarmPointLocation', rowspan: 1, align: 'left', width: '300px'
					}, {
						title: "位号", field: 'alarmPointTag', rowspan: 1, align: 'left', width: '140px'
					}, {
						title: "报警时间", field: 'alarmTime', rowspan: 1, align: 'center', width: '140px'
					}, {
						title: "时长（小时）", field: 'continuousHour', align: 'right', width: '80px'
					}, {
						title: "恢复时间", field: 'handleTime', align: 'center', width: '140px'
					}, {
						title: "报警等级", field: 'alarmFlagName', rowspan: 1, align: 'center', width: '80px'
					}, {
						title: "优先级", field: 'priorityName', rowspan: 1, align: 'center', width: '60px'
					}, {
						title: "专业", field: 'monitorTypeStr', rowspan: 1, align: 'center', width: '60px'
					}, {
						title: "单位", field: 'measUnitName', rowspan: 1, align: 'left', width: '80px'
					}],
					responseHandler: function(res) {
						$("#persistNum").html($.ET.getPageInfo(res)[0]["total"]);
						var item = {
							"pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
							"pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
							"total": $.ET.getPageInfo(res)[0]["total"],
							"rows": $.ET.toObjectArr(res)
						};
						return item;
					}
				}, page.logic.queryParams,"btnSearch")
			},
			queryParams: function(p) {
				var param = {
					pageSize: p.pageSize,
					pageNumber: p.pageNumber,
					sortOrder: p.sortOrder
				};
				return $.extend(page.data.param, param);
			},
			//搁置报警表格
			initTableShelve: function() {
				OPAL.ui.initBootstrapTable("tblShelve", {
					columns: [{
						title: "序号",
						formatter: function(value, row, index) {
							var tableOption = $('#tblShelve').bootstrapTable('getOptions');
							var pageNumber = tableOption.pageNumber;
							var pageSize = tableOption.pageSize;
							return (index + 1) + (pageNumber - 1) * pageSize;
						},
						rowspan: 1,
						align: 'center',
						width: '80px'
					}, {
						title: "搁置时间",
						field: 'startTime',
						rowspan: 1,
						align: 'center',
						width: '150px'
					}, {
						title: "报警时间",
						field: 'alarmTime',
						rowspan: 1,
						align: 'center',
						width: '150px'
					}, {
						title: "装置",
						field: 'unitName',
						rowspan: 1,
						align: 'left',
						width: '120px'
					}, {
						title: "生产单元",
						field: 'prdtCellName',
						rowspan: 1,
						align: 'left',
						width: '120px'
					}, {
                         title: "班组",
                         field: 'workTeamSName',
                         rowspan: 1,
                         align: 'center',
                         width: '100px'
                     }, {
						title: "位号",
						field: 'alarmPointTag',
						rowspan: 1,
						align: 'left',
						width: '100px'
					}, {
						title: "描述",
						field: 'des',
						rowspan: 1,
						align: 'left',
						width: '150px'
					}, {
						title: "报警标识",
						field: 'alarmFlagName',
						rowspan: 1,
						align: 'center',
						width: '100px'
					}, {
						title: "优先级",
						field: 'priorityName',
						rowspan: 1,
						align: 'center',
						width: '60px'
					}],
					responseHandler: function(res) {
						$("#shelveNum").html($.ET.getPageInfo(res)[0]["total"]);
						var item = {
							"pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
							"pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
							"total": $.ET.getPageInfo(res)[0]["total"],
							"rows": $.ET.toObjectArr(res)
						};
						return item;
					}
				}, page.logic.queryParams)
			},
			//屏蔽报警 表格
			initTableShield: function() {
				OPAL.ui.initBootstrapTable("tblShield", {
					columns: [{
						title: "序号",
						formatter: function(value, row, index) {
							var tableOption = $('#tblShield').bootstrapTable('getOptions');
							var pageNumber = tableOption.pageNumber;
							var pageSize = tableOption.pageSize;
							return (index + 1) + (pageNumber - 1) * pageSize;
						},
						rowspan: 1,
						align: 'center',
						width: '80px'
					}, {
						title: "屏蔽/取消屏蔽时间",
						field: 'startTime',
						rowspan: 1,
						align: 'center',
						width: '150px'
					}, {
						title: "装置",
						field: 'unitName',
						rowspan: 1,
						align: 'left',
						width: '100px'
					}, {
						title: "生产单元",
						field: 'prdtCellName',
						rowspan: 1,
						align: 'left',
						width: '100px'
					}, {
                         title: "班组",
                         field: 'workTeamSName',
                         rowspan: 1,
                         align: 'center',
                         width: '100px'
                    }, {
						title: "位号",
						field: 'alarmPointTag',
						rowspan: 1,
						align: 'left',
						width: '100px'
					}, {
						title: "描述",
						field: 'des',
						rowspan: 1,
						align: 'left',
						width: '150px'
					}, {
						title: "事件类型",
						field: 'eventTypeName',
						rowspan: 1,
						align: 'left',
						width: '100px'
					}, {
						title: "报警标识",
						field: 'alarmFlagName',
						rowspan: 1,
						align: 'center',
						width: '100px'
					}, {
						title: "优先级",
						field: 'priorityName',
						rowspan: 1,
						align: 'center',
						width: '60px'
					}],
					responseHandler: function(res) {
						$("#shieldNum").html($.ET.getPageInfo(res)[0]["total"]);
						var item = {
							"pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
							"pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
							"total": $.ET.getPageInfo(res)[0]["total"],
							"rows": $.ET.toObjectArr(res)
						};
						return item;
					}
				}, page.logic.queryParams)
			},
			//装置下拉菜单
			initUnitTree: function() {
				OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
					onChange: function(node, checked) {
						var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
						if (unitIds != undefined && unitIds.length == 1) {
							$("#prdtCellIds").combo('enable');
							$('#prdtCellIds').combotree('setValues', []);
							page.logic.initPrdtMultipleSelect(unitIds[0]);
							$("#workTeamIds").prop('disabled', false);
							page.logic.initWorkTeam();
							$('.textbox,.combo').css('background-color','');
						} else {
							$('#prdtCellIds').combotree('setValues', []);
							$("#prdtCellIds").combo('disable');
							$('#workTeamIds').html("");
							$("#workTeamIds").prop('disabled', true);
							$('.textbox-disabled').css('background-color','rgb(235, 235, 228)');
						}
					}
				}, false);
			},
			//生产单元下拉菜单
			initPrdtMultipleSelect: function(unitId) {
				OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
					keyField: "prdtCellId",
					valueField: "sname",
					data: {
						"unitId": unitId,
						'isAll': true
					}
				}, true, function() {
					var treeView = $("#prdtCellIds").combotree('tree');
					var nd = treeView.tree('find', -1);
					if (nd != null) {
						treeView.tree('update', {
							target: nd.target,
							text: '全选'
						});
					}
					$("#prdtCellIds").combotree("checkAllNodes");
				});
			},
			//报警标识下拉菜单
			initAlarmFlag: function() {
				OPAL.ui.getCombobox("alarmFlagId", alarmFlagListUrl, {
					selectFirstRecord: true,
					data: {
						'isAll': true
					}
				}, null);
			},
			initEventType: function() {
				var data = [
					{key:-1,value:"全部"},
					{key:3003,value:"屏蔽记录"},
					{key:3004,value:"取消屏蔽记录"},
				]
				OPAL.ui.getComboboxByData("eventTypeId", data, {
					selectFirstRecord: true,
				}, null);
			},
			/**
			 * 初始化班组选择
			 */
			initWorkTeam: function() {
				var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
				if (unitIds.length != 1) return;
				if ($("#startTime").val() == '' || $("#endTime").val() == '') {
					$('#workTeamIds').html("");
					$("#workTeamIds").prop('disabled', true);
					return;
				} else {
					$("#workTeamIds").prop('disabled', false);
				}
				OPAL.ui.getCombobox("workTeamIds", workTeamUrl, {
					keyField: "workTeamId",
					valueField: "workTeamSName",
					selectFirstRecord: true,
					mapManyValues: true, //是否一条记录匹配多个隐藏值
					mapManyDataFieldName: 'workTeamIdList',
					data: {
						"startTime": $("#startTime").val(),
						"endTime": $("#endTime").val(),
						"unitId": unitIds[0],
					}
				}, null);

			}
		}
	};
	page.init();
	window.page = page;
})