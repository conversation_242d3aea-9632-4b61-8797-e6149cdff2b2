package com.pcitc.opal.ac.bll.entity;

import com.pcitc.opal.common.bll.entity.DictionaryEntity;

import java.util.Date;
import java.util.List;

/*
 * 下发信息、确认信息
 * 模块编号：pcitc_opal_bll_class_AlarmChangePlanLinkInfoEntity
 * 作    者：jiangtao.xue
 * 创建时间：2018-01-30
 * 修改编号：1
 * 描    述：下发信息、确认信息
 */
public class AlarmChangePlanLinkInfoEntity {

    /**
     * 下发人
     */
    private String issueUser;

    /**
     * 下发时间
     */
    private Date issueDate;

    /**
     * 确认人
     */
    private String confirmUser;

    /**
     * 执行人下拉列表
     */
    private List<DictionaryEntity> executeUser;

    /**
     * 执行人
     */
    private String executeUserName;

    /**
     * 下发备注
     */
    private String issueRemark;

    /**
     * 确认备注
     */
    private String confirmRemark;

    public String getIssueUser() {
        return issueUser;
    }

    public void setIssueUser(String issueUser) {
        this.issueUser = issueUser;
    }

    public Date getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
    }

    public String getConfirmUser() {
        return confirmUser;
    }

    public void setConfirmUser(String confirmUser) {
        this.confirmUser = confirmUser;
    }

    public List<DictionaryEntity> getExecuteUser() {
        return executeUser;
    }

    public void setExecuteUser(List<DictionaryEntity> executeUser) {
        this.executeUser = executeUser;
    }

    public String getExecuteUserName() {
        return executeUserName;
    }

    public void setExecuteUserName(String executeUserName) {
        this.executeUserName = executeUserName;
    }

    public String getIssueRemark() {
        return issueRemark;
    }

    public void setIssueRemark(String issueRemark) {
        this.issueRemark = issueRemark;
    }

    public String getConfirmRemark() {
        return confirmRemark;
    }

    public void setConfirmRemark(String confirmRemark) {
        this.confirmRemark = confirmRemark;
    }
}
