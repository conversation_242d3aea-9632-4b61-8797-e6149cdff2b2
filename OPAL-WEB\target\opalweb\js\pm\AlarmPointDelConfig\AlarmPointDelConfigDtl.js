var addUrl = OPAL.API.pmUrl + '/alarmPointDelConfig'; //新增或编辑
var submitUrl = OPAL.API.pmUrl + '/alarmPointDelConfig/commit'; //提交
var getSingleUrl = OPAL.API.pmUrl + '/alarmPointDelConfig/getSingleAlarmPointDel?alarmPointDelConfigId='; //获取详情
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit"; //装置
var alarmGroupListUrl = OPAL.API.pmUrl + "/alarmPointGroupConfig/getGroupListByUnit"; //根据装置获取报警点分组名称下拉框
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.None;
var alarmPointDelConfigId = '';
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
            //初始化装置树
            page.logic.initUnitTree();
            // 初始化时间
            page.logic.initTime();
        },
        bindUI: function () {
            //保存
            $('#saveAddModal').click(function () {
                page.logic.save(0);
            });
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
            //提交
            $('#submitModal').click(function () {
                page.logic.submit();
            });
        },
        logic: {
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitCode', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    multiple: false,
                    onlyLeafCheck: true,
                    async: false,
                    data: {
                        'enablePrivilege':false
                    },
                    onSelect: function(node) {
                        OPAL.ui.getCombobox("alarmPointGroupId", alarmGroupListUrl, {
                            data: {
                                unitCode: node.id
                            },
                            async: false,
                            selectFirstRecord: false,
                            keyField: "alarmPointGroupId",
                            valueField: "groupName",
                        }, null);
                        $("#alarmFlagId").val('');
                    }
                }, false, function () {

                });
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getCombobox("alarmPointGroupId", alarmGroupListUrl, {
                    data: {
                        unitCode: unitId
                    },
                    async: false,
                    selectFirstRecord: false,
                    keyField: "alarmPointGroupId",
                    valueField: "groupName",
                }, null);
            },
            /**
             * 设置日期插件
             */
             initTime: function() {
                let nowDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
                start = laydate.render({
                    elem: '#delStartTime', //指定元素
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd HH:mm:ss', //日期格式
                    max: nowDate
                });
                end = laydate.render({
                    elem: '#delEndTime',
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear','confirm'],
                    format: 'yyyy-MM-dd HH:mm:ss',
                    max: nowDate
                });
            },
            /**
             * 保存 type 0保存 1提交
             */
            save: function (type) {
                $('#unitCode').next('.textbox').find('input').attr('name', 'unitCode');
                $('#unitCode').next('.textbox').addClass('form-control-tree');
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                let startDate = new Date($("#delStartTime").val()).getTime();
                let endDate = new Date($("#delEndTime").val()).getTime();
                if (startDate > endDate) {
                    layer.msg("开始时间不能大于结束时间！");
                    return false;
                }
                $("#delStatus").val(type);
                var data = OPAL.form.getETCollectionData('AddOrEditModal'); 
                //处理提交类型
                var ajaxType = "POST";
                if (pageMode == PageModelEnum.NewAdd) {
                    window.pageLoadMode = PageLoadMode.Reload;
                }
                else if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                    window.pageLoadMode = PageLoadMode.Refresh;
                }
                $.ajax({
                    url: addUrl,
                    async: false,
                    type: ajaxType,
                    data: JSON.stringify(data),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg('保存成功',{
                                time: 1000
                            },function() {
                                alarmPointDelConfigId = result;
                                $('#alarmPointDelConfigId').val(alarmPointDelConfigId);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 提交 type 0保存 1提交
             */
            submit: function () {
                $('#unitCode').next('.textbox').find('input').attr('name', 'unitCode');
                $('#unitCode').next('.textbox').addClass('form-control-tree');
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                let startDate = new Date($("#delStartTime").val()).getTime();
                let endDate = new Date($("#delEndTime").val()).getTime();
                if (startDate > endDate) {
                    layer.msg("开始时间不能大于结束时间！");
                    return false;
                                }
                $("#delStatus").val(1);
                var data = OPAL.form.getETCollectionData('AddOrEditModal'); 
                //处理提交类型
                var ajaxType = "POST";
                window.pageLoadMode = PageLoadMode.Refresh;
                $.ajax({
                    url: submitUrl,
                    async: false,
                    type: 'post',
                    data: JSON.stringify(data),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg('提交成功！',{
                                time: 1000
                            },function() {
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                alarmPointDelConfigId = data.alarmPointDelConfigId;
                if (pageMode == PageModelEnum.NewAdd) {
                    // $('input[name=inUse]').attr('disabled', 'disabled');
                    return;
                } else {
                    $.ajax({
                        url: getSingleUrl + data.alarmPointDelConfigId + "&now=" + Math.random(),
                        type: "get",
                        async: false,
                        dataType: "json",
                        success: function (data) {
                            var entity = $.ET.toObjectArr(data)[0];
                            page.logic.searchUnitPrdt(entity['unitCode']);
                            OPAL.form.setData('AddOrEditModal', $.ET.toObjectArr(data)[0]);
                        }
                    });
                }
            },
            
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal',{
                    rules: {
                        unitCode: {
                            required: true
                        },
                        alarmPointGroupId: {
                            required: true
                        },
                        delStartTime: {
                            required: true
                        },
                        delEndTime: {
                            required: true
                        },
                    }
                })
                
            }
        }

    }
    page.init();
    window.page = page;
})