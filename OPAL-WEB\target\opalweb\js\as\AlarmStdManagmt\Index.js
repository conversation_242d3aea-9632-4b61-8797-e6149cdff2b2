var addUrl = OPAL.API.asUrl + '/alarmStdManagmt';
var editUrl = OPAL.API.asUrl + '/alarmStdManagmt';
var delUrl = OPAL.API.asUrl + '/alarmStdManagmt/deleteAlarmStdManagmt';
var searchUrl = OPAL.API.asUrl + '/alarmStdManagmt';
var downloadFileUrl = OPAL.API.asUrl + '/alarmStdManagmt/downloadFile';
var currentTimeUrl = OPAL.API.commUrl + "/getSysDateTime";
var getAlarmStdManagmtCatgrListUrl = OPAL.API.commUrl + "/getAlarmStdManagmtCatgrList";
window.pageLoadMode = PageLoadMode.None;
$(function() {
    var page = {
        /**
         * 初始化
         */
        init: function() {
            //绑定事件
            this.bindUI();
            // 初始化 时间设置
            page.logic.initTime();
            // 初始化 报警制度管理分类列表
            page.logic.getAlarmStdManagmtCatgrList();
            // 初始化表格
            page.logic.initTable();
            page.logic.search();
        },
        /**
         * 绑定事件
         */
        bindUI: function() {
            // 新增
            $('#AlarmStdManagmtAdd').click(function() {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            })
            //批量删除
            $('#AlarmStdDel').click(function() {
                page.logic.delAll();
            })
            //查询
            $('#search').click(function() {
                page.logic.search();
            })
        },
        data: {
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化表格
             */
            initTable: function() {
                OPAL.ui.initBootstrapTable("table", {
                    cache: false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '90px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        field: 'name',
                        title: '名称',
                        align: 'left',
                        width: '90px'
                    }, {
                        field: 'catgrName',
                        title: '分类',
                        align: 'center',
                        width: '100px'
                    }, {
                        field: 'uplTime',
                        title: '上传时间',
                        align: 'center',
                        width: '140px'
                    }, {
                        field: 'uplUserName',
                        title: '上传人',
                        align: 'left',
                        width: '90px'
                    }, {
                        field: 'uplAttaName',
                        title: '上传附件',
                        align: 'left',
                        width: '140px',
                        /*formatter: function(value, row, index) {
                            return "<span style='color:#337ab7'>" + value + "</span>";
                        }*/
                        formatter: page.logic.onActionRenderer1
                    }, {
                        field: 'des',
                        title: '描述',
                        align: 'left',
                        width: '140px'
                    }],
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function() {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.edit(\'' + rowData.alarmStdManagmtId + '\')">编辑</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.alarmStdManagmtId + '\')" >删除</a> '
                ]
            },
            /**
             * 批量删除
             */
            delAll: function() {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections');
                $.each(rowsArray, function(i, el) {
                    idsArray.push(el.alarmStdManagmtId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function(result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function(index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function(id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function(result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function(index) {
                    layer.close(index)
                });
            },
            /**
             * 触发下载
             * @returns {string[]}
             */
            onActionRenderer1: function() {
                var rowData = arguments[1];
                return [
                    '<a  name="DownLoad"  href="javascript:window.page.logic.download(\'' + rowData.uplAttaId + '\',\'' + rowData.uplAttaName + '\')" >'+rowData.uplAttaName+'</a> '
                ]
            },
            /**
             * 新增
             */
            add: function() {
                var pageMode = PageModelEnum.NewAdd;
                var title = "报警制度新增";
                page.logic.detail(title, "", pageMode);
            },
            /**
             * 编辑
             * @param alarmStdManagmtId
             */
            edit: function(alarmStdManagmtId) {
                var pageMode = PageModelEnum.Edit;
                var title = "报警制度编辑";
                page.logic.detail(title, alarmStdManagmtId, pageMode);
            },
            /**
             * 报警制度管理新增或者编辑详细页面
             */
            detail: function(title, alarmStdManagmtId, pageMode) {
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: '',
                    area: ['930px', '400px'],
                    shadeClose: false,
                    content: 'AlarmStdManagmtAddOrEdit.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "alarmStdManagmtId": alarmStdManagmtId,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function() {
                        if(window.isRefresh){
                            $('#table').bootstrapTable('selectPage', 1);
                        }
                    }
                })
            },
            /**
             * 搜索
             */
            search: function() {
                if (!page.logic.checkDateIsValid()) {
                    return false;
                }
                page.data.param = OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 初始化查询 报警制度管理分类列表
             */
            getAlarmStdManagmtCatgrList: function() {
                OPAL.ui.getCombobox("catgr", getAlarmStdManagmtCatgrListUrl, {
                    keyField: "key",
                    valueField: "value",
                    selectValue: -1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            /**
             * 初始化 时间
             */
            initTime: function() {
                var endTimeVal;
                $.ajax({
                    url: currentTimeUrl,
                    async: false,
                    dataType: "json",
                    success: function(data) {
                        var dataArr = $.ET.toObjectArr(data);
                        endTimeVal = dataArr[0].value;
                    },
                    error: function(jqXHR, textStatus, errorThrown) {}
                });
                OPAL.ui.initDateTimePeriodPicker({
                    type: 'date',
                    format: 'yyyy-MM-dd',
                    startValue: moment().get('years') + "-01-01",
                    startMax: moment(endTimeVal).format('YYYY-MM-DD'),
                    endValue: moment(endTimeVal).format('YYYY-MM-DD'),
                    endMax:moment(endTimeVal).format('YYYY-MM-DD')
                })
            },
            /**
             * 校验时间
             */
            checkDateIsValid: function () {
                var startTime = OPAL.util.strToDate($('#startTime').val());
                var endTime = OPAL.util.strToDate($('#endTime').val());
                if ($('#startTime').val() == "" || $('#endTime').val() == "" || $('#startTime').val() == undefined || $('#endTime').val() == undefined) {
                    layer.msg("开始时间和结束时间不能为空！");
                    return false;
                }else if ((endTime - startTime) < 0) {
                    layer.msg("开始时间不能大于结束时间！");
                    return false;
                }
                return true;
            },
            download: function(uplAttaId,uplAttaName) {
                $('#formExPort').attr('action', downloadFileUrl);
                $('#uplAttaName').val(uplAttaName);
                $('#uplAttaId').val(uplAttaId);
                $('#formExPort').submit();
            }

        }
    }
    page.init();
    window.page = page;
})