package com.pcitc.opal.pm.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.pm.pojo.EventType;

/*
 * EventType实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_EventTypeRepository
 * 作       者：kun.zhao
 * 创建时间：2017/10/09
 * 修改编号：1
 * 描       述：EventType实体的Repository的JPA标准接口   
 */
public interface EventTypeRepository extends JpaRepository<EventType, Long>, EventTypeRepositoryCustom {

}
