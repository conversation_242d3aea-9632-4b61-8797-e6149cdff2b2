package com.pcitc.opal.common.bll.entity;

/*
 * 授权类实体
 * 模块编号：pcitc_opal_bll_class_AuthorizeEntity
 * 作    者：xuelei.wang
 * 创建时间：2017-12-20
 * 修改编号：1
 * 描   述：授权类实体
 */
public class AuthorizeEntity {

    /**
     * 标准编码
     */
    private String stdCode;
    /**
     * 装置名称
     */
    private String name;
    /**
     * 装置简称
     */
    private String sname;
    /**
     * 是否启用
     */
    private Integer inUse;

    public String getStdCode() {
        return stdCode;
    }

    public void setStdCode(String stdCode) {
        this.stdCode = stdCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSname() {
        return sname;
    }

    public void setSname(String sname) {
        this.sname = sname;
    }

    public Integer getInUse() {
        return inUse;
    }

    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }
}
