package com.pcitc.opal.af.bll.entity;

import com.pcitc.opal.common.bll.entity.DictionaryEntity;

import java.util.List;
import java.util.Map;

/*
 * 报警分析-报警详情数据实体
 * 模块编号：pcitc_opal_bll_class_AlarmDetailEntity
 * 作    者：jiangtao.xue
 * 创建时间：2017/11/08
 * 修改编号：1
 * 描    述：报警分析-报警详情数据实体
 */
public class AlarmDetailEntity {
    /**
     * 位号
     */
    private String tag;

    /**
     * 报警标识名称
     */
    private String alarmFlagName;

    /**
     * 报警标识
     */
    private Long alarmFlag;

    /**
     * 生产单元简称
     */
    private String prdtCellSname;

    /**
     * 位置
     */
    private String location;

    /**
     * 报警总时长
     */
    private Double alarmDuration;

    /**
     * 报警次数
     */
    private Long alarmTimes;

    /**
     * 确认次数
     */
    private Long confirmTimes;

    /**
     * 平均确认时间
     */
    private Double avgConfirmTime;

    /**
     * 报警值
     */
    private Double alarmValue;

    /**
     * 现报警数
     */
    private int nowAlarmTimes;

    /**
     * 减少比例
     */
    private Double reduceRate;

    /**
     * 最大值
     */
    private Double maxValue;

    /**
     * 最小值
     */
    private Double minValue;

    /**
     * 限值
     */
    private Object limitValue;

    /**
     * 计量单位
     */
    private String unitFlag;

    /**
     * 工艺卡片上限值
     */
    private DictionaryEntity upLimitValue;

    /**
     * 工艺卡片下限值
     */
    private DictionaryEntity downLimitValue;

    /**
     * 柱状图数据
     */
    private List<DictionaryEntity> histogramData;

    /**
     * 折线图数据
     */
    private List<DictionaryEntity> lineChartData;

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

    public String getPrdtCellSname() {
        return prdtCellSname;
    }

    public void setPrdtCellSname(String prdtCellSname) {
        this.prdtCellSname = prdtCellSname;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Double getAlarmDuration() {
        return alarmDuration;
    }

    public void setAlarmDuration(Double alarmDuration) {
        this.alarmDuration = alarmDuration;
    }

    public Long getAlarmTimes() {
        return alarmTimes;
    }

    public void setAlarmTimes(Long alarmTimes) {
        this.alarmTimes = alarmTimes;
    }

    public Long getConfirmTimes() {
        return confirmTimes;
    }

    public void setConfirmTimes(Long confirmTimes) {
        this.confirmTimes = confirmTimes;
    }

    public Double getAvgConfirmTime() {
        return avgConfirmTime;
    }

    public void setAvgConfirmTime(Double avgConfirmTime) {
        this.avgConfirmTime = avgConfirmTime;
    }

    public Double getAlarmValue() {
        return alarmValue;
    }

    public void setAlarmValue(Double alarmValue) {
        this.alarmValue = alarmValue;
    }

    public int getNowAlarmTimes() {
        return nowAlarmTimes;
    }

    public void setNowAlarmTimes(int nowAlarmTimes) {
        this.nowAlarmTimes = nowAlarmTimes;
    }

    public Double getReduceRate() {
        return reduceRate;
    }

    public void setReduceRate(Double reduceRate) {
        this.reduceRate = reduceRate;
    }

    public Double getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(Double maxValue) {
        this.maxValue = maxValue;
    }

    public Double getMinValue() {
        return minValue;
    }

    public void setMinValue(Double minValue) {
        this.minValue = minValue;
    }

    public Object getLimitValue() {
        return limitValue;
    }

    public void setLimitValue(Object limitValue) {
        this.limitValue = limitValue;
    }

    public String getUnitFlag() {
        return unitFlag;
    }

    public void setUnitFlag(String unitFlag) {
        this.unitFlag = unitFlag;
    }

    public DictionaryEntity getUpLimitValue() {
        return upLimitValue;
    }

    public void setUpLimitValue(DictionaryEntity upLimitValue) {
        this.upLimitValue = upLimitValue;
    }

    public DictionaryEntity getDownLimitValue() {
        return downLimitValue;
    }

    public void setDownLimitValue(DictionaryEntity downLimitValue) {
        this.downLimitValue = downLimitValue;
    }

    public List<DictionaryEntity> getHistogramData() {
        return histogramData;
    }

    public void setHistogramData(List<DictionaryEntity> histogramData) {
        this.histogramData = histogramData;
    }

    public List<DictionaryEntity> getLineChartData() {
        return lineChartData;
    }

    public void setLineChartData(List<DictionaryEntity> lineChartData) {
        this.lineChartData = lineChartData;
    }

    public Long getAlarmFlag() {
        return alarmFlag;
    }

    public void setAlarmFlag(Long alarmFlag) {
        this.alarmFlag = alarmFlag;
    }
}

