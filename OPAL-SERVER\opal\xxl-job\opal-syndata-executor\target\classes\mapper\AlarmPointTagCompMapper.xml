<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.mapper.AlarmPointTagCompMapper">

    <resultMap id="BaseResultMap" type="com.pcitc.opal.pojo.AlarmPointTagComp">
            <id property="alarmPointTagCompId" column="alarm_point_tag_comp_id" jdbcType="BIGINT"/>
            <id property="alarmPointId" column="alarm_point_id" jdbcType="BIGINT"/>
            <result property="companyId" column="company_id" jdbcType="BIGINT"/>
            <result property="tag" column="tag" jdbcType="VARCHAR"/>
            <result property="sysType" column="sys_type" jdbcType="BIGINT"/>
            <result property="unitcode" column="unitcode" jdbcType="VARCHAR"/>
            <result property="unitsname" column="unitsname" jdbcType="VARCHAR"/>
            <result property="crafttag" column="crafttag" jdbcType="VARCHAR"/>
            <result property="crtDate" column="crt_date" jdbcType="TIMESTAMP"/>
            <result property="mntDate" column="mnt_date" jdbcType="TIMESTAMP"/>
            <result property="crtUserId" column="crt_user_id" jdbcType="VARCHAR"/>
            <result property="mntUserId" column="mnt_user_id" jdbcType="VARCHAR"/>
            <result property="crtUserName" column="crt_user_name" jdbcType="VARCHAR"/>
            <result property="mntUserName" column="mnt_user_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        alarm_point_tag_comp_id,alarm_point_id,company_id,
        tag,sys_type,unitcode,
        unitsname,crafttag,crt_date,
        mnt_date,crt_user_id,mnt_user_id,
        crt_user_name,mnt_user_name
    </sql>
</mapper>
