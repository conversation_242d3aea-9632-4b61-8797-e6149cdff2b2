package com.pcitc.opal.aa.bll.entity;

import java.util.Date;

/*
 * 报警数量评估-报警数实体
 * 模块编号：pcitc_opal_bll_class_AlarmNumberEntity
 * 作  　者：dageng.sun
 * 创建时间：2017-10-30
 * 修改编号：1
 * 描    述：报警数量评估-报警数实体
 */
public class AlarmNumberEntity {
	
	/**
	 * 装置名称/生产单元名称的id
	 */
	private String id;
	
	/**
	 * 装置名称/生产单元名称
	 */
	private String name;

	/**
	 * 装置名称/生产单元名称
	 */
	private String UnitName;
	/**
	 * 报警数量
	 */
	private Long alarmCount;
	
	/**
     *报警时间
     */
    private String alarmTime;
    
    /**
     * 总报警数
     */
    private long sum;
    
    /**
     * 报警平均数
     */
    private float avg;
    
    /**
     *粒度周期结束时间
     */
    private String endTime;
	/**
	 *导出excel序号
	 */
    private Integer index;
	/**
	 *报警时间
	 */
    private Date alarmDate;
	/**
	 *班组名称
	 */
    private String workTeamName;
	
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUnitName() {
		return UnitName;
	}

	public void setUnitName(String unitName) {
		UnitName = unitName;
	}

	public Long getAlarmCount() {
		return alarmCount;
	}

	public void setAlarmCount(Long alarmCount) {
		this.alarmCount = alarmCount;
	}

	public String getAlarmTime() {
		return alarmTime;
	}

	public void setAlarmTime(String alarmTime) {
		this.alarmTime = alarmTime;
	}

	public long getSum() {
		return sum;
	}

	public void setSum(long sum) {
		this.sum = sum;
	}

	public float getAvg() {
		return avg;
	}

	public void setAvg(float avg) {
		this.avg = avg;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public Date getAlarmDate() {
		return alarmDate;
	}

	public void setAlarmDate(Date alarmDate) {
		this.alarmDate = alarmDate;
	}

	public String getWorkTeamName() {
		return workTeamName;
	}

	public void setWorkTeamName(String workTeamName) {
		this.workTeamName = workTeamName;
	}
}
