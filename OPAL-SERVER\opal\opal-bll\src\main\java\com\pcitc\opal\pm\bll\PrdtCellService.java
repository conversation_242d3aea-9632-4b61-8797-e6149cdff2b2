package com.pcitc.opal.pm.bll;

import org.springframework.stereotype.Service;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.PrdtCellEntity;

/*
 * 生产单元业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_PrdtCellService
 * 作       者：zheng.yang
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描       述：生产单元业务逻辑层接口 
 */
@Service
public interface PrdtCellService {

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param prdtCellEntity
	 *            生产单元实体
	 */
	CommonResult addPrdtCell(PrdtCellEntity prdtCellEntity) throws Exception;

	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param prdtCellIds
	 *            生产单元ID集合
	 */
	CommonResult deletePrdtCell(Long[] prdtCellIds) throws Exception;

	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param prdtCellEntity
	 */
	CommonResult updatePrdtCell(PrdtCellEntity prdtCellEntity) throws Exception;

	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param prdtCellId
	 *            工厂ID
	 * @return 工厂实体
	 */
	PrdtCellEntity getSinglePrdtCell(Long prdtCellId) throws Exception;

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param unitCodes
	 *            装置编码集合
	 * @param name
	 *            工厂名称
	 * @param inUse
	 *            是否启动
	 * @param page
	 *            分页参数
	 * @return 工厂实体集合
	 * @throws Exception
	 */
	PaginationBean<PrdtCellEntity> getPrdtCell(String[] unitCodes, String name, Integer inUse, Pagination page)
			throws Exception;

	/**
	 * 判断生产单元在报警点中是否使用
	 *
	 * <AUTHOR> 2017-11-30
	 * @param prdtCellId 报警点实体
	 * @return CommonResult 消息结果类
	 */
	CommonResult getPrdtCellIsUseInAlarmPoint(Long prdtCellId);
}
