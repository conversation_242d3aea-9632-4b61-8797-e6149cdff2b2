package com.pcitc.opal.ad.dao;

import com.pcitc.opal.ad.dao.imp.AlarmNumStattDtlEntityVO;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.CommonEnum.TimeFilterTypeEnum;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.AlarmPointDelConfig;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * AlarmEvent实体的Repository的JPA自定义接口
 * 模块编号：pcitc_opal_dal_interface_AlarmEventRepositoryCustom
 * 作	者：kun.zhao
 * 创建时间：2017/10/09
 * 修改编号：1
 * 描	述：AlarmEvent实体的Repository的JPA自定义接口
 */
@SuppressWarnings("all")
public interface AlarmEventRepositoryCustom {

    /**
     * 获取报警事件数据
     *
     * <AUTHOR> 2019-12-30
     * @param unitCodes       装置编码数组
     * @param prdtCellIds   生产单元ID数组
     * @param eventTypeIds  事件类型ID数组
     * @param alarmPointTag 报警点位号
     * @param alarmFlagId   报警标识ID
     * @param priority      优先级
     * @param craftRank     级别
     * @param timeType      时间筛选类型
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param dateRangeList 日期区间列表
     * @param page          分页参数
     * @return 报警事件实体集合
     * @throws Exception
     */
    PaginationBean<Object[]> getAlarmEventGroup(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds,
                                                String alarmPointTag, Long alarmFlagId, Integer priority, Integer craftRank, TimeFilterTypeEnum timeType,
                                                Date startTime, Date endTime, List dateRangeList, Pagination page) throws Exception;

    /**
     * 获取报警事件分页数据
     *
     * <AUTHOR> 2017-10-09
     * @param unitCodes       装置编码数组
     * @param prdtCellIds   生产单元ID数组
     * @param eventTypeIds  事件类型ID数组
     * @param alarmPointTag 报警点位号
     * @param alarmFlagId   报警标识ID
     * @param priority      优先级
     * @param craftRank     级别
     * @param timeType      时间筛选类型
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param dateRangeList 日期区间列表
     * @param page          分页参数
     * @return 报警事件实体集合
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    PaginationBean<AlarmEvent> getAlarmEvent(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds,
                                             String alarmPointTag, Long alarmFlagId, Integer priority, Integer craftRank, TimeFilterTypeEnum timeType,
                                             Date startTime, Date endTime, List dateRangeList, Pagination page) throws Exception;

    /**
     * 获取报警事件分页数据
     *
     * @param unitCodes     装置编码数组
     * @param prdtCellIds   生产单元ID数组
     * @param eventTypeIds  事件类型ID数组
     * @param alarmPointTag 报警点位号
     * @param alarmFlagIds  报警标识ID集合
     * @param priority      优先级
     * @param craftRank     级别
     * @param timeType      时间筛选类型
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param dateRangeList 日期区间列表
     * @param page          分页参数
     * @param isMatching
     * @return 报警事件实体集合
     * @throws Exception
     * <AUTHOR> 2018-11-05
     */
    @SuppressWarnings("rawtypes")
    PaginationBean<AlarmEvent> getAlarmEventList(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds,
                                                 String alarmPointTag, Long[] alarmFlagIds, Integer priority, Integer[] prioritys, Integer monitorType, Integer craftRank, TimeFilterTypeEnum timeType,
                                                 Date startTime, Date endTime, List dateRangeList, Pagination page, Integer isMatching) throws Exception;

    /**
     * 获取报警事件分页数据
     *
     * <AUTHOR> 2017-10-09
     * @param unitCodes       装置编码数组
     * @param prdtCellIds   生产单元ID数组
     * @param eventTypeIds  事件类型ID数组
     * @param alarmPointTag 报警点位号
     * @param alarmFlagId   报警标识ID
     * @param priority      优先级
     * @param craftRank     级别
     * @param timeType      时间筛选类型
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param dateRangeList 日期区间列表
     * @param page          分页参数
     * @return 报警事件实体集合
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    PaginationBean<AlarmEvent> getAlarmEvent(String[] unitCodes, Long[] prdtCellIds, String alarmPointTag,
                                             Long alarmFlagId, Date startTime, Date endTime, Pagination page) throws Exception;


    /**
     * 分页获取数据
     *
      * <AUTHOR> 2017-10-23
     * @param unitCodes 装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param tag 位号
     * @param priority 优先级
     * @param beginTime 报警事件的开始间
     * @param endTime 报警事件的结束时间
     * @param dateRangeList 日期区间列表
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmEvent> 返回AlarmEvent分页对象
     */
    @SuppressWarnings("rawtypes")
    @Deprecated
    PaginationBean<AlarmEvent> getAlarmBadpvDistribution(String[] unitCodes, Long[] prdtCellIds, String tag, Integer priority, Date beginTime, Date endTime, List dateRangeList, Pagination page)
            throws Exception;

    /**
     * 分页获取数据
     *
      * <AUTHOR> 2017-11-19
     * @param unitCodes 装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param tag 位号
     * @param beginTime 报警事件的开始间
     * @param endTime 报警事件的结束时间
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmEvent> 返回AlarmEvent分页对象
     */
    @SuppressWarnings("rawtypes")
    PaginationBean<Object[]> getAlarmBadpvDistribution(String[] unitCodes, Long[] prdtCellIds, String tag, Date beginTime, Date endTime, Pagination page) throws Exception;

    /**
     * 分页获取详情数据
     *
      * <AUTHOR> 2018-11-19
     * @param alarmPointId 报警点id
     * @param beginTime 报警事件的开始间
     * @param endTime 报警事件的结束时间
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmEvent> 返回AlarmEvent分页对象
     */
    PaginationBean<AlarmEvent> getAlarmBadpvDistributionDtl(Long alarmPointId, Date beginTime, Date endTime, Pagination page) throws Exception;

    /**
     * 一个时间段内最频繁的报警前20条数据
     *
     * <AUTHOR> 2017-10-27
     * @param unitCodes 装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param startTime 报警事件的开始间
     * @param endTime 报警事件的结束时间
     * @param topType Top20,Top10切换选择
     * @return
     */
    @SuppressWarnings("rawtypes")
    List getAlarmNumberTop20(String[] unitCodes, Long[] prdtCellIds, String[] wokrUnitCodes, Date startTime, Date endTime, String startFlag, String endFlag, Integer topType);

    /**
     * 一个时间段内最频繁的报警前20条数据
     *
     * <AUTHOR> 2019-09-30
     * @param unitCodes 装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param startTime 报警事件的开始间
     * @param endTime 报警事件的结束时间
     * @param topType Top20,Top10切换选择
     * @param priority 优先级
     * @return
     */
    @SuppressWarnings("rawtypes")
    List getAlarmNumberTop20(String[] unitCodes, Long[] alarmFlagId, Long[] prdtCellIds, String[] wokrUnitCodes, Date startTime, Date endTime, String startFlag, String endFlag, Integer topType, Integer[] priority, Boolean priorityFlag, Integer isElimination);

    /**
     * 一个时间段内最频繁的报警前20条数据(新)
     * @param unitCode
     * @param unitCodes
     * @param alarmFlagId
     * @param prdtCellIds
     * @param wokrUnitCodes
     * @param startTime
     * @param endTime
     * @param startFlag
     * @param endFlag
     * @param topType
     * @param priority
     * @param priorityFlag
     * @param isElimination
     * @return
     */
    @SuppressWarnings("rawtypes")
    List getAlarmNumberAll(String[] workshopCodes, String[] unitCode, Long[] prdtCellId, String[] unitCodes, Long[] alarmFlagId, Long[] prdtCellIds, String[] wokrUnitCodes, Date startTime, Date endTime, String startFlag, String endFlag, Integer topType, Integer[] priority, Boolean priorityFlag, Integer isElimination, Pagination page);

    List getAlarmNumberAllType(String[] workshopCodes, String[] unitCode, Long[] prdtCellId, String[] unitCodes, Long[] alarmFlagId, Long[] prdtCellIds, String[] wokrUnitCodes, Date startTime, Date endTime, String startFlag, String endFlag, Integer topType, Integer[] monitorType, Boolean priorityFlag, Integer isElimination, Pagination page);

    /**
     * 一个时间段内最频繁的报警前20条数据(新)
     * @param unitCode
     * @param unitCodes
     * @param alarmFlagId
     * @param prdtCellIds
     * @param wokrUnitCodes
     * @param startTime
     * @param endTime
     * @param startFlag
     * @param endFlag
     * @param topType
     * @param priority
     * @param priorityFlag
     * @param isElimination
     * @return
     */
    @SuppressWarnings("rawtypes")
    List getAlarmNumberAllTotal(String[] workshopCodes, String[] unitCode, Long[] prdtCellId, String[] unitCodes, Long[] alarmFlagId, Long[] prdtCellIds, String[] wokrUnitCodes, Date startTime, Date endTime, String startFlag, String endFlag, Integer topType, Integer[] priority, Boolean priorityFlag, Integer isElimination);

    List getAlarmNumberAllTotalType(String[] workshopCodes, String[] unitCode, Long[] prdtCellId, String[] unitCodes, Long[] alarmFlagId, Long[] prdtCellIds, String[] wokrUnitCodes, Date startTime, Date endTime, String startFlag, String endFlag, Integer topType, Integer[] priority, Boolean priorityFlag, Integer isElimination);

    /**
     * 报警数量评估——趋势图数据
     *
     * <AUTHOR> 2017-10-30
     * @param ids        编码数组（装置或单元）
     * @param queryType 查询类型（装置或单元）
     * @param dateType    时间粒度
     * @param startTime    时间范围起始
     * @param endTime    时间范围结束
     * @return 报警数量评估趋势图实体集合
     */
    @SuppressWarnings("rawtypes")
    List getAlarmNumberAssessTrendEntity(String[] ids, CommonEnum.EquipmentTypeEnum queryType, CommonEnum.DateTypeEnum dateType,
                                         Date startTime, Date endTime, Integer hours, String timeType, String name, String queryCondition);

    /**
     * 报警数量评估——趋势图数据
     *
     * <AUTHOR> 2019-12-2
     * @param ids            ID数组（装置或单元）
     * @param queryType      查询类型（装置或单元）
     * @param dateType       时间粒度
     * @param startTime      时间范围起始
     * @param endTime        时间范围结束
     * @param hours          校正时间
     * @param queList        额外过滤语句
     * @return 报警数量评估趋势图实体集合
     */
    @SuppressWarnings("rawtypes")
    List<Object[]> getAlarmNumberAssessTrendEntityByTypeQue(String[] ids, CommonEnum.EquipmentTypeEnum queryType, CommonEnum.DateTypeEnum dateType,
                                                            Date startTime, Date endTime, Integer hours, Map<String, String> queList);

    /**
     * 报警数量评估——趋势图数据
     *
     * <AUTHOR> 2019-12-2
     * @param ids            ID数组（装置或单元）
     * @param queryType      查询类型（装置或单元）
     * @param dateType       时间粒度
     * @param startTime      时间范围起始
     * @param endTime        时间范围结束
     * @param hours          校正时间
     * @param queList        额外过滤语句
     * @return 报警数量评估趋势图实体集合
     */
    @SuppressWarnings("rawtypes")
    List<Object[]> getAlarmNumberAssessTrendEntityByTypeQue(String[] ids, Long[] alarmFlagId, CommonEnum.EquipmentTypeEnum queryType, CommonEnum.DateTypeEnum dateType,
                                                            Date startTime, Date endTime, Integer hours, Map<String, String> queList, Integer[] priority, Boolean priorityFlag, Integer isElimination);

    /**
     * 根据装置编码集合和时间区间获取该装置的报警事件列表
     *
     * <AUTHOR> 2017-11-9
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCodes   装置编码集合
     * @param prdtIds   单元ID集合
     * @param page      分页信息
     * @return 屏蔽的报警事件列表
     */
    PaginationBean<AlarmEvent> getAlarmEventListByUnitId(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, Pagination page) throws Exception;

    /**
     * 根据报警点ID和报警标识ID获取报警事件列表
     *
     * <AUTHOR> 2017-10-30
     * @param startTime        开始时间
     * @param endTime            结束时间
     * @param alarmPointId    报警点ID
     * @param alarmFlagId        报警标识ID
     * @param page                分页信息
     * @return 操作详情报警事件列表
     */
    PaginationBean<AlarmEvent> getOperateDetail(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId, Pagination page);

    /**
     * 持续报警分析分页查询
     *
      * @param request
     * @param unitCodes 装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param alarmFlagId 报警标识
     * @param beginTime 报警事件开始间
     * @param endTime 报警事件结束时间
     * @param page 翻页实现类
     * @return 
     * @return PaginationBean<AlarmEvent>
     */
    PaginationBean<AlarmEvent> getPersistentAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds, Long alarmFlagId, Date beginTime, Date endTime, Pagination page);

    /**
     * 根据报警点ID和报警标识ID获取操作详情图表数据
     *
     * <AUTHOR> 2017-10-30
     * @param startTime        开始时间
     * @param endTime            结束时间
     * @param alarmPointId    报警点ID
     * @param alarmFlagId        报警标识ID
     * @param hourTime            配置时间
     * @return 操作详情图表数据
     */
    List getChartOperateDetail(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId, String hourTime);

    /**
     * 根据报警点ID和报警标识ID获取报警详情平均值，最大值，最小值
     *
     * <AUTHOR> 2017-11-08
     * @param startTime        开始时间
     * @param endTime            结束时间
     * @param alarmPointId    报警点ID
     * @param alarmFlagId        报警标识ID
     * @return 报警详情平均值，最大值，最小值
     */
    List getAlarmAvgData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId);

    /**
     * 根据报警点ID和报警标识ID获取报警详情-报警总时长
     *
     * <AUTHOR> 2017-11-08
     * @param startTime        开始时间
     * @param endTime            结束时间
     * @param alarmPointId    报警点ID
     * @param alarmFlagId        报警标识ID
     * @return 报警详情-报警总时长
     */
    List getAlarmDurationData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId);

    /**
     * 根据报警点ID和报警标识ID获取报警详情-报警次数
     *
     * <AUTHOR> 2017-11-08
     * @param startTime        开始时间
     * @param endTime            结束时间
     * @param alarmPointId    报警点ID
     * @param alarmFlagId        报警标识ID
     * @return 报警详情-报警次数
     */
    List getAlarmTimesData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId);

    /**
     * 根据报警点ID和报警标识ID获取报警详情-确认次数
     *
     * <AUTHOR> 2017-11-08
     * @param startTime        开始时间
     * @param endTime            结束时间
     * @param alarmPointId    报警点ID
     * @param alarmFlagId        报警标识ID
     * @return 报警详情-确认次数
     */
    List getConfirmTimesData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId);

    /**
     * 根据报警点ID和报警标识ID获取报警详情-现报警数
     *
     * <AUTHOR> 2017-11-08
     * @param startTime        开始时间
     * @param endTime            结束时间
     * @param alarmPointId    报警点ID
     * @param alarmFlagId        报警标识ID
     * @param alarmValue        报警值
     * @return 报警详情-现报警数
     */
    int getNowAlrmTimesData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId, Double alarmValue);

    /**
     * 根据报警点ID和报警标识ID获取报警详情-确认总时长
     *
     * <AUTHOR> 2017-11-08
     * @param startTime        开始时间
     * @param endTime            结束时间
     * @param alarmPointId    报警点ID
     * @param alarmFlagId        报警标识ID
     * @return 报警详情-确认总时长
     */
    List getConfirmDurationData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId);

    /**
     * 根据报警点ID和报警标识ID获取报警详情-柱状图数据
     *
     * <AUTHOR> 2017-11-08
     * @param startTime        开始时间
     * @param endTime            结束时间
     * @param alarmPointId    报警点ID
     * @param alarmFlagId        报警标识ID
     * @param hourTime            配置时间
     * @param alarmValue        报警值
     * @param isOriginal        是否是原数据，true原数据，false更改后数据
     * @return 报警详情柱状图数据
     */
    List getHistogramData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId, String hourTime, Double alarmValue, boolean isOriginal);

    /**
     * 根据报警点ID和报警标识ID获取报警详情-折线图数据
     *
     * <AUTHOR> 2017-11-08
     * @param startTime        开始时间
     * @param endTime            结束时间
     * @param alarmPointId    报警点ID
     * @param alarmFlagId        报警标识ID
     * @return 报警详情折线图数据
     */
    List getLineChartData(Date startTime, Date endTime, Long alarmPointId, Long alarmFlagId);

    /**
     * 搁置报警分析分页查询
     *
      * @param request
     * @param unitCodes 装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param alarmFlagId 报警标识
     * @param beginTime 报警事件开始间
     * @param endTime 报警事件结束时间
     * @param dateRangeList 日期区间对象集合
     * @param page 翻页实现类
     * @return 
     * @return PaginationBean<AlarmEvent>
     */
    PaginationBean<AlarmEvent> getShelveAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds, Long alarmFlagId, Date beginTime, Date endTime, List dateRangeList, Pagination page) throws Exception;

    /**
     * 屏蔽报警分析分页查询
     *
      * @param request
     * @param unitCodes 装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param alarmFlagId 报警标识
     * @param eventTypeIds 报警事件类型id数组
     * @param beginTime 报警事件开始间
     * @param endTime 报警事件结束时间
     * @param dateRangeList 日期区间对象集合
     * @param page 翻页实现类
     * @return 
     * @return PaginationBean<AlarmEvent>
     */
    PaginationBean<AlarmEvent> getShieldAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds, Long alarmFlagId, Long[] eventTypeIds, Date beginTime, Date endTime, List dateRangeList, Pagination page) throws Exception;

    /**
     * 查询报警数详情集合
     *
      * <AUTHOR> 2017-11-07
     * @param prdtCellId 车间/装置/生产单元的id
     * @param beginTime 报警事件的开始间
     * @param endTime 报警事件的结束时间
     * @param page 查询分页对象
     * @throws Exception 
     * @return PaginationBean<AlarmEvent> 返回AlarmEvent实体分页对象
     */
    PaginationBean<AlarmEvent> getAlarmNumberDetail(Long prdtCellId, String[] unitCodes, Date beginTime, Date endTime, Pagination page);

    /**
     * 查询变更记录集合
     *
     * <AUTHOR> 2017-11-10
     * @param unitCodes            装置编码数组
     * @param prdtCellIds   生产单元ID数组
     * @param startTime            时间范围起始
     * @param endTime            时间范围结束
     * @param dateRangeList 日期区间列表
     * @param page                分页对象
     * @return AlarmEvent分页数据
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    PaginationBean<AlarmEvent> getAlarmChangeRecord(String[] unitCodes, Long[] prdtCellIds, Date startTime, Date endTime,
                                                    List dateRangeList, Pagination page, Integer AlarmChangeRecordBusinessType) throws Exception;

    /**
     * 查询时序事件分析图形业务数据访问对象层
     *
      * <AUTHOR> 2017-11-16
     * @param unitCodes 装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param beginTime 报警事件的开始间
     * @param endTime 报警事件的结束时间
     * @return List<AlarmEvent> 返回AlarmEvent实体集合
     */
    List<AlarmEvent> getSequentialEventGraph(String[] unitCodes, Long[] prdtCellIds, Date beginTime, Date endTime);

    /**
     * 获取报警操作评估首页数据
     *
     * <AUTHOR> 2017-11-20
     * @param startTime  开始日期
     * @param endTime    结束日期
     * @param unitCodes    装置编码集合
     * @param prdtIds    生产单元ID集合
     * @param dateType   查询日期类型(日,周,月)
     * @param hour        需要减去的小时数
     * @return
     * @throws Exception
     */
    List getAlarmOperateAssess(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, String dateType, int hour) throws Exception;

    /**
     * 获取最频繁的操作Top20
     *
     * <AUTHOR> 2017-11-20
     * @param startTime  开始日期
     * @param endTime    结束日期
     * @param unitCodes    装置编码集合
     * @param prdtIds    生产单元ID集合
     * @param topType Top20,Top10切换选择
     * @return
     * @throws Exception
     */
    List getAlarmOperateTop20(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, Integer topType) throws Exception;

    /**
     * 获取报警事件优先级评估页面统计数据
     *
     * <AUTHOR> 2017-11-21
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param unitCodes      装置编码数组
     * @param prdtIds      生产单元ID数组
     * @return 优先级评估统计数据
     */
    @SuppressWarnings("rawtypes")
    List getAlarmPriorityAssessStatisticData(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds);

    /**
     * 获取报警次数统计页面统计数据
     *
     * <AUTHOR> 2017-11-21
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param unitCodes      装置编码数组
     * @param priority      优先级
     * @return 优先级评估统计数据
     */
    @SuppressWarnings("rawtypes")
    List getAlarmNumStattStatisticData(Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination);


    List getUnitMonitorAlarmNumStat(Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagIds,
                                    Integer isElimination);

    /**
     * 工艺系统调用--报警次数统计接口(车间)
     * @param unitStdCodes
     * @param priorities
     * @param alarmFlagIds
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    List getAlarmNumStattStatisticDataWorkshop(Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination);

    List getAlarmNumStattStatisticDataWorkshopType(Date startTime, Date endTime, String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination);

    /**
     * 工艺系统调用--报警次数统计接口(装置)
     * @param workshopCodes
     * @param startTime
     * @param endTime
     * @param unitCodes
     * @param priority
     * @param alarmFlagId
     * @return
     */
    List getAlarmNumStattStatisticDataUnit(String[] workshopCodes, Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination);

    /**
     * 工艺系统调用--报警次数统计接口(装置)
     * @param workshopCodes
     * @param startTime
     * @param endTime
     * @param unitCodes
     * @param priority
     * @param alarmFlagId
     * @return
     */
    List getAlarmNumStattStatisticDataUnitType(String[] workshopCodes, Date startTime, Date endTime, String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination);

    /**
     * 工艺系统调用--报警次数统计接口（生产车间）
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param unitCodes
     * @param priority
     * @param alarmFlagId
     * @return
     */
    List getAlarmNumStattStatisticDataPrdtcell(String[] unitIds, Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination);

    /**
     * 工艺系统调用--报警次数统计接口（生产车间）
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param unitCodes
     * @param priority
     * @param alarmFlagId
     * @return
     */
    List getAlarmNumStattStatisticDataPrdtcellType(String[] unitIds, Date startTime, Date endTime, String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds, Boolean priorityFlag, Integer isElimination);

    /**
     * 工艺系统调用--报警次数统计接口
     * @param unitStdCodes
     * @param priorities
     * @param alarmFlagIds
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    List getAlarmNumStattStatisticData(Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagId);

    /**
     * 工艺系统调用--报警次数统计接口(车间)
     * @param unitStdCodes
     * @param priorities
     * @param alarmFlagIds
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    List getAlarmNumStattStatisticDataWorkshop(Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagId);

    /**
     * 工艺系统调用--报警次数统计接口(装置)
     * @param workshopCodes
     * @param startTime
     * @param endTime
     * @param unitCodes
     * @param priority
     * @param alarmFlagId
     * @return
     */
    List getAlarmNumStattStatisticDataUnit(String[] workshopCodes, Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagId);

    /**
     * 工艺系统调用--报警次数统计接口（生产车间）
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param unitCodes
     * @param priority
     * @param alarmFlagId
     * @return
     */
    List getAlarmNumStattStatisticDataPrdtcell(String[] unitIds, Date startTime, Date endTime, String[] unitCodes, Integer[] priority, Long[] alarmFlagId);

    /**
     * 获取报警时长分页数据
     *
     * <AUTHOR> 2019-09-30
     * @param unitCodes       装置编码数组
     * @param eventTypeIds  事件类型ID数组
     * @param priority      优先级
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param page          分页参数
     * @return 报警事件实体集合
     * @throws Exception
     */
    public List<Object[]> getAlarmDurationStatt(String[] unitCodes, Integer[] priority, Long[] eventTypeIds, Date startTime, Date endTime, Boolean priorityFlag, Pagination page) throws Exception;

    /**
     * 判断报警点在报警事件表中是否使用
     *
     * <AUTHOR> 2017-11-29
     * @param alarmPointId
     * @return
     */
    Long getAlarmPointIsUseInAlarmEvent(Long alarmPointId);

    /**
     * 查看报警事件中是否有报警点
     *
      * <AUTHOR> 2017-12-07
     * @param alarmPointIds 报警点维护主键Id集合
     * @return 
     * @return List<AlarmEvent> AlarmEvent集合
     */
    List<AlarmEvent> getListByAlarmPointIds(Long[] alarmPointIds);

    /**
     * 获取报警事件实体
     *
     * <AUTHOR> 2018-01-22
     * @param alarmPointId 报警点Id
     * @param alarmFlagId 报警标识Id
     * @return 
     * @return AlarmEvent 获取报警事件实体
     */
    AlarmEvent getAlarmEventByPointFlag(Long alarmPointId, Long alarmFlagId);

    /**
     * 优先级评估-表格显示
     *
     * <AUTHOR> 2018-01-22
     * @param startTime 查询开始时间
     * @param endTime 查询结束时间
     * @param unitCode 装置编码
     * @param priority 优先级(1紧急；2重要；3一般)
     * @return PaginationBean<Object [ ]> 返回对象数组分页
     */
    List<Object[]> getAlarmPriorityAssessTable(Date startTime, Date endTime, String unitCode, Integer priority);

    /**
     * 查询最频繁报警详情集合
     *
      * <AUTHOR> 2018-04-13
     * @param alarmPointTag 位号
     * @param alarmFlagId 报警标识id
     * @param enentTypeIds 报警事件类型集合
     * @param alarmTime 报警时间
     * @param endTime 报结束时间时间
     * @param page 查询分页对象
     * @throws Exception 
     * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity实体分页对象
     */
    PaginationBean<AlarmEvent> getAlarmDtail(String alarmPointTag, Long alarmFlagId, Long[] eventTypeIds, Date alarmTime, Date endTime, String dateType, Pagination page) throws Exception;

    /**
     * 报警时长统计--各个优先级报警时长统计
     * @param unitIds  装置ID
     * @param priority  优先级
     * @param eventTypeIds  事件类型ID
     * @param startTime
     * @param endTime
     * @return
     */
    List<Object[]> getAlarmDurationStattTotal(String[] unitIds, Integer[] priority, Long[] eventTypeIds, Date startTime, Date endTime, Boolean priorityFlag);


    /**
     * 报警时长统计总条数
     * @param unitIds 装置ID
     * @param priority 优先级
     * @param eventTypeIds 事件类型ID
     * @param startTime
     * @param endTime
     * @return
     */
    Object getAlarmDurationStattSum(String[] unitIds, Integer[] priority, Long[] eventTypeIds, Date startTime, Date endTime, Boolean priorityFlag);

    /**
     * 时长柱状图取出1001 类型
     * @param unitIds
     * @param priority
     * @param eventTypeIds
     * @param startTime
     * @param endTime
     * @param priorityFlag
     * @return
     */
    List<Object[]> getAlarmDurationStattTotal2(String[] unitIds, Integer[] priority, Long[] eventTypeIds, Date startTime, Date endTime, Boolean priorityFlag);

    List getSendMsgInfo(Date startTime, Date endTime);

    List<Object[]> getMonitoringData(String dateStr);

    List<String> getListCodes(Date startTime, Date endTime, String strCodes);

    public List<AlarmRec> findAlarmEventInfoByAlarmTime(String startDate, String end, Integer companyId);

    public List<AlarmRec> findAlarmEventInfoByStartTime(String startDate, String end, Integer companyId);

    /**
     * 查询要剔除的数据
     * @param alarmPointDelConfigs
     * @return
     */
    List<AlarmEvent> selectDelAlarmEvent(List<AlarmPointDelConfig> alarmPointDelConfigs);

    /**
     * 批量根据id删除
     * @param alarmEvents id集合
     * @param batchSize 批量大小，默认1000s
     */
    void deleteBatch(List<Long> alarmEvents, Integer batchSize);

    /**
     * 查询装置某优先级的详情数据
     * @param alarmPointDelConfigs
     * @return
     */
    PaginationBean<AlarmNumStattDtlEntityVO> getAlarmNumStattDtl(Date startTime, Date endTime, String unitCode, Integer[] priority, Long[] alarmFlagIds, Pagination page);


    /**
     * 查询大于传入id的报警事件，并且事件类型等于1001、1002、1003、1005
     * @param currentId
     * @return
     */
    List<AlarmEvent> getAlarmEventByCurrId(Long currentId);

    /**
     * 获取报警点中个优先级所占比例
     *
     * @param unitCodes   装置
     * @param prdtCellIds 生产单元
     * @return key->优先级枚举，value->比例
     */
    HashMap<CommonEnum.AlarmPriorityEnum, String> getPriorityRatioInAp(String[] unitCodes, Long[] prdtCellIds);

    List<AlarmEvent> getAlarmEvent(Date start, Date end, String unitCode);
}

