<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.mapper.OpcCodeMapper">

    <resultMap id="BaseResultMap" type="com.pcitc.opal.pojo.OpcCode">
            <id property="opcCodeId" column="opc_code_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="inUse" column="in_use" jdbcType="BIGINT"/>
            <result property="sortNum" column="sort_num" jdbcType="BIGINT"/>
            <result property="companyId" column="company_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        opc_code_id,name,in_use,
        sort_num,company_id
    </sql>
    <select id="selectByCompanyIdToTenantDb" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_pm_opccode
        where
        company_id = #{companyId,jdbcType=NUMERIC}
    </select>
</mapper>
