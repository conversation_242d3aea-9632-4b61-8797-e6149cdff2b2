package com.pcitc.opal.ad.dao;

import com.pcitc.opal.ad.dao.imp.AlarmDurationDtlEntityVO;
import com.pcitc.opal.ad.dao.imp.AlarmTagUnitVO;
import com.pcitc.opal.ad.dao.imp.AlarmTimelyCount;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.pojo.DateRange;
import com.pcitc.opal.pm.pojo.AlarmPointDelConfig;
import com.pcitc.opal.pm.pojo.AlarmRecLog;

import java.util.Date;
import java.util.List;

public interface AlarmRecRepositoryCustom {
    List<AlarmRec> getAlarmDurationStatt(String[] unitIds, Integer[] priority, Date startTime, Date endTime,
                                         Boolean priorityFlag, String tag, Long alarmFlagId, Integer isElimination);

    PaginationBean<AlarmRec> getAlarmDurationStatt(Pagination page, String[] unitIds, Integer[] priority,
                                                   Date startTime, Date endTime, Boolean priorityFlag, String tag,
                                                   Long[] alarmFlagId, Integer isElimination);

    List<Object[]> getAlarmDurationStattMain(String[] unitIds, Long[] alarmFlagIds, Integer[] priority, Date startTime,
                                             Date endTime, Boolean priorityFlag, Pagination page,
                                             Integer isElimination);

    List<Object[]> getAlarmDurationStattMainCommon(String[] unitCodes, String[] workshopCodes, Long[] prdtCellIds,
                                                   String[] unitIds, Long[] alarmFlagIds, Integer[] priority,
                                                   Date startTime, Date endTime, Boolean priorityFlag, Pagination page,
                                                   Integer isElimination);

    List<Object[]> getAlarmDurationStattTotal(String[] unitIds, Long[] alarmFlagIds, Integer[] priority, Date startTime,
                                              Date endTime, Boolean priorityFlag, Integer isElimination);

    List<Object[]> getAlarmDurationStattTotalWorkshop(String[] unitIds, Long[] alarmFlagIds, Integer[] priority,
                                                      Date startTime, Date endTime, Boolean priorityFlag,
                                                      Integer isElimination);

    List<Object[]> getAlarmDurationStattTotalunit(String[] workshopCodes, String[] unitIds, Long[] alarmFlagIds,
                                                  Integer[] priority, Date startTime, Date endTime,
                                                  Boolean priorityFlag, Integer isElimination);

    List<Object[]> getAlarmDurationStattTotalPrdtcell(String[] unitCodes, String[] unitIds, Long[] alarmFlagIds,
                                                      Integer[] priority, Date startTime, Date endTime,
                                                      Boolean priorityFlag, Integer isElimination);

    List<Object[]> getAlarmRespond(String[] unitCodes, Long[] prdtCellIds, String tag, Long alarmFlagId,
                                   Integer priority, Date beginTime, Date endTime, Integer isTimeResponse,
                                   List<DateRange> dateList, Integer responseDuration, Pagination page);

    Object getAlarmRespondTotal(String[] unitCodes, Long[] prdtCellIds, String tag, Long alarmFlagId, Integer priority,
                                Date beginTime, Date endTime, Integer isTimeResponse, List<DateRange> dateList,
                                Integer responseDuration);

    CommonResult deleteAlarmRecByAlarmPointIds(Long[] anlyAlarmPointIdList);

    List<Object[]> getAlarmRespondOfUnRespond(String[] unitIds, Date startTime, Date endTime, Date now);

    Object getTotalAlarmDurationStatt(String[] unitIds, Long[] alarmFlagIds, Integer[] priority, Date startTime,
                                      Date endTime, Boolean priorityFlag);

    Object getTotalAlarmDurationStattCommon(String[] unitCodes, String[] workshopCodes, Long[] prdtCellIds,
                                            String[] unitIds, Long[] alarmFlagIds, Integer[] priority, Date startTime,
                                            Date endTime, Boolean priorityFlag);

    public CommonResult removeRecoveryTimeIsNullByAlarmTime(String startDate, String end, Integer companyId);

    public int findAlarmRecCountByStartTime(String unitCode, Long prdtcellId, String tag, String alarmFlag,
                                            Date alarmTime, String companyId);

    int updateAlarmRecRecoveryTime(String unitCode, Long prdtcellId, String tag, String alarmFlag, Date alarmTime,
                                   Date recoveryTime, String companyId);

    Object findAlarmRecMaxAlarmTime(String unitCode, String prdtcellId, String tag, String alarmFlag, Date startTime,
                                    String companyId);

    int updateResponseTimeByStartTime(String unitCode, String prdtcellId, String tag, String alarmFlag, Date startTime,
                                      Date recoveryTime, String companyId);

    void insertAlarmRecByInfo(List<AlarmRec> list);

    int getCountByAlarmRec(AlarmRec alarmRec, String companyId);


    public int updateAlarmRecResponseTimeByLessAlarmTime(String unitCode, Long prdtcellId, String tag, String alarmFlag,
                                                         Date alarmTime, Date recoveryTime, String companyId);

    /**
     * 根据 companyId和起止时间查询报警时间响应比例
     *
     * @param startTime
     * @param endTime
     * @param companyId
     * @return
     */
    public List<Object[]> getResponseValue(Date startTime, Date endTime, Integer companyId);

    public List<AlarmTimelyCount> getAlarmTimelyResponseCount(String[] unitCodes, Date startTime, Date endTime,
                                                              Integer companyId);

    public List<AlarmTimelyCount> getAlarmTimelyDisposalCount(String[] unitCodes, Date startTime, Date endTime,
                                                              Integer companyId);

    /**
     * 查询要剔除的数据
     *
     * @param alarmPointDelConfigs 剔除配置
     * @return rec
     */
    List<AlarmRec> selectDelAlarmRec(List<AlarmPointDelConfig> alarmPointDelConfigs);

    /**
     * 批量根据id删除
     *
     * @param alarmRecs id集合
     * @param batchSize 批量大小，默认1000
     */
    void deleteBatch(List<Long> alarmRecs, Integer batchSize);

    /**
     * 报警记录分页查询
     */
    PaginationBean<AlarmRec> getAlarmExamineRec(String[] unitIds, Long[] prdtCellIds, String tag, Integer alarmFlagId,
                                                Integer priority, Date startTime, Date endTime, Integer alarmDuration,
                                                Pagination page);

    /**
     * 更新响应时间
     */
    Integer updateResponseTimeByRec(AlarmRec alarmRec, Integer companyId);

    /**
     * 查询装置某优先级的详情数据
     *
     * @param
     * @return
     */
    PaginationBean<AlarmDurationDtlEntityVO> getAlarmNumStattDtl(Date startTime, Date endTime, String unitCode,
                                                                 Integer priority, Long[] alarmFlagIds,
                                                                 Pagination page);


    /**
     * 报警推送用于查询数据
     * 查询条件：大于《recId记录表》中最大的recId， 或者已恢复的报警记录
     */
    List<AlarmRec> getMaxRecIdOrRestored(Long maxId);

    /**
     * 查询最大recId
     */
    Long getMaxRecId();

    void insertMaxRecId(AlarmRecLog alarmRecLog);

    AlarmRec getAlarmRecByEventId(Long eventId);

    /**
     * 根据时间和装置查询报警记录数据
     */
    List<AlarmRec> getAlarmRec(Date startTime, Date endTime, String[] unitIds);

    /**
     * 查询报警记录中未恢复超过20-24小时的数据
     *
     * @param end 当前时间
     */
    List<AlarmRec> getNotRecoveryTime(Date end);

    List<AlarmRec> getAlarmRec(Date start, Date end, String unitCode);

    List<AlarmRec> getAlarmRecByMonitor(Date startTime, Date endTime, String[] unitIds, Integer monitorType);

    List<AlarmTagUnitVO> getTagUnitByMonitor(Date startTime, Date endTime, String[] unitIds, Integer monitorType);
}
