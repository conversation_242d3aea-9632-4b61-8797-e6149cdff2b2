package com.pcitc.opal.ap.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;
import com.pcitc.opal.common.pojo.BasicInfo;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

/*
 * 报警点实体
 * 模块编号：pcitc_pojo_class_Group
 * 作       者：guoganxin
 * 创建时间：2023/04/16
 * 修改编号：1
 * 描       述：群组
 */
public class AlarmPushRuleDetailEntity extends BasicEntity {
    /**
     * 报警推送规则明细ID
     */
    private Long apRuleDetailId;

    /**
     * 企业ID
     */
    private Integer companyId;

    /**
     * 报警推送规则ID
     */
    private Long alarmPushRuleId;

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 开始推送间隔时间（分钟）
     */
    private Long startPushPeriod;

    /**
     * 周期推送标志（0 非周期、1 周期）
     */
    private Long cycleFlag;

    /**
     * 周期间隔时间（分钟）
     */
    private Long cyclePeriod;

    /**
     * 报警结束推送标志(0 不推送、1 推送)
     */
    private Long alarmEndPushFlag;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     * 是否启用（1是；0否）
     */
    private Integer inUse;

    /**
     * 排序
     */
    private Integer sortNum;

    /**
     * 描述
     */
    private String des;

    public Long getApRuleDetailId() {
        return apRuleDetailId;
    }

    public void setApRuleDetailId(Long apRuleDetailId) {
        this.apRuleDetailId = apRuleDetailId;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Long getAlarmPushRuleId() {
        return alarmPushRuleId;
    }

    public void setAlarmPushRuleId(Long alarmPushRuleId) {
        this.alarmPushRuleId = alarmPushRuleId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getStartPushPeriod() {
        return startPushPeriod;
    }

    public void setStartPushPeriod(Long startPushPeriod) {
        this.startPushPeriod = startPushPeriod;
    }

    public Long getCycleFlag() {
        return cycleFlag;
    }

    public void setCycleFlag(Long cycleFlag) {
        this.cycleFlag = cycleFlag;
    }

    public Long getCyclePeriod() {
        return cyclePeriod;
    }

    public void setCyclePeriod(Long cyclePeriod) {
        this.cyclePeriod = cyclePeriod;
    }

    public Long getAlarmEndPushFlag() {
        return alarmEndPushFlag;
    }

    public void setAlarmEndPushFlag(Long alarmEndPushFlag) {
        this.alarmEndPushFlag = alarmEndPushFlag;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public Integer getInUse() {
        return inUse;
    }

    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
