package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.AlarmRecVo;
import com.pcitc.opal.pm.pojo.SendMsgAlarmConf;

import java.util.List;

public interface SendMsgAlarmConfRepositoryCustom{

    /**
     * 根据条件查询报警信息相关内容
     * @return AlarmRecVo
     */
    List<AlarmRecVo> getMsgAlarmConf(Integer companyId);


    /**
     * 更新间隔时间
     * @param timeInterval 间隔时间
     * @return 影响的行
     */
    Integer updateTimeIntervalBySendMsgAlarmConf(Long timeInterval, Long sendMsgAlarmConfId);


    /**
     * 根据报警配置id更新其他数据
     *
     * @param sendMsgAlarmConfId 警配置id数组
     * @param mobileBookId       电话本id
     * @param timeInterval       延迟事件
     * @return 受影响行数
     */
    Integer updateSendMsgAlarmConfBySendMsgAlarmConfId(Long[] sendMsgAlarmConfId, String mobileBookId, Long timeInterval);

    /**
     * 根据短信配置id查询报警点id
     *
     * @param sendMsgAlarmConfId 短信配置id数组
     * @return 报警点id数组
     */
    Long[] findAlarmPointIdBySendMsgAlarmConfId(Long[] sendMsgAlarmConfId);

    PaginationBean<SendMsgAlarmConf> findSendMsgAlarmConfByInfo(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inSendMsg, Integer timeInterval, Pagination page);

    List<Object[]> findSendMsgAlarmConfBySendMsgAlarmConfId(Long[] alarmMsgConfId);

    SendMsgAlarmConf getAlarmMsgConfigByInfo(Long ap, Long af, Long timeInterval);
}
