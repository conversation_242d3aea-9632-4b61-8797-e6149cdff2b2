package com.pcitc.opal.ap.dao.imp;


import java.util.Date;

/*
 * 报警点实体
 * 模块编号：pcitc_pojo_class_Group
 * 作       者：guoganxin
 * 创建时间：2023/04/16
 * 修改编号：1
 * 描       述：群组
 */
public class AlarmPushRuleUnitRelEntityVO {

    public AlarmPushRuleUnitRelEntityVO(Long apRuleUnitRelId, Long alarmPushRuleId, String pushRuleName, Long priority, Integer alarmSpeciality, Integer inUse, String des, Date mntDate, String mntUserName) {
        this.apRuleUnitRelId = apRuleUnitRelId;
        this.alarmPushRuleId = alarmPushRuleId;
        this.pushRuleName = pushRuleName;
        this.priority = priority;
        this.alarmSpeciality = alarmSpeciality;
        this.inUse = inUse;
        this.des = des;
        this.mntDate = mntDate;
        this.mntUserName = mntUserName;
    }

    /**
     * 报警推送规则装置关系ID
     */
    private Long apRuleUnitRelId;

    /**
     * 报警推送规则ID
     */
    private Long alarmPushRuleId;

    /**
     * 报警推送规则名称
     */
    private String pushRuleName;

    /**
     * 优先级（1 紧急；2重要；3 一般）
     */
    private Long priority;

    /**
     * 报警专业（1 工艺、2 设备、3 安全、4 环保、5 质量、6 火灾）
     */
    private Integer alarmSpeciality;

    /**
     * 是否启用（1是；0否）
     */
    private Integer inUse;

    /**
     * 描述
     */
    private String des;

    /**
     * 修改时间
     */
    private Date mntDate;

    /**
     * 修改人
     */
    private String mntUserName;

    public Long getAlarmPushRuleId() {
        return alarmPushRuleId;
    }

    public void setAlarmPushRuleId(Long alarmPushRuleId) {
        this.alarmPushRuleId = alarmPushRuleId;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public Long getApRuleUnitRelId() {
        return apRuleUnitRelId;
    }

    public void setApRuleUnitRelId(Long apRuleUnitRelId) {
        this.apRuleUnitRelId = apRuleUnitRelId;
    }

    public String getPushRuleName() {
        return pushRuleName;
    }

    public void setPushRuleName(String pushRuleName) {
        this.pushRuleName = pushRuleName;
    }

    public Long getPriority() {
        return priority;
    }

    public void setPriority(Long priority) {
        this.priority = priority;
    }

    public Integer getAlarmSpeciality() {
        return alarmSpeciality;
    }

    public void setAlarmSpeciality(Integer alarmSpeciality) {
        this.alarmSpeciality = alarmSpeciality;
    }

    public Integer getInUse() {
        return inUse;
    }

    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }

    public Date getMntDate() {
        return mntDate;
    }

    public void setMntDate(Date mntDate) {
        this.mntDate = mntDate;
    }

    public String getMntUserName() {
        return mntUserName;
    }

    public void setMntUserName(String mntUserName) {
        this.mntUserName = mntUserName;
    }
}
