<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:tns="http://tempuri.org/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="SendShortMessageByPhone">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="paramPhones" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="paramContents" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="VerificationCode" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendShortMessageByPhoneResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendShortMessageByPhoneResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendShortMessageByAccount">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="paramAccounts" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="paramContents" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="VerificationCode" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendShortMessageByAccountResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendShortMessageByAccountResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendVerifyMessageByPhone">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="account" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendVerifyMessageByPhoneResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendVerifyMessageByPhoneResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
    </s:schema>
  </wsdl:types>
  <wsdl:message name="SendShortMessageByPhoneSoapIn">
    <wsdl:part name="parameters" element="tns:SendShortMessageByPhone" />
  </wsdl:message>
  <wsdl:message name="SendShortMessageByPhoneSoapOut">
    <wsdl:part name="parameters" element="tns:SendShortMessageByPhoneResponse" />
  </wsdl:message>
  <wsdl:message name="SendShortMessageByAccountSoapIn">
    <wsdl:part name="parameters" element="tns:SendShortMessageByAccount" />
  </wsdl:message>
  <wsdl:message name="SendShortMessageByAccountSoapOut">
    <wsdl:part name="parameters" element="tns:SendShortMessageByAccountResponse" />
  </wsdl:message>
  <wsdl:message name="SendVerifyMessageByPhoneSoapIn">
    <wsdl:part name="parameters" element="tns:SendVerifyMessageByPhone" />
  </wsdl:message>
  <wsdl:message name="SendVerifyMessageByPhoneSoapOut">
    <wsdl:part name="parameters" element="tns:SendVerifyMessageByPhoneResponse" />
  </wsdl:message>
  <wsdl:message name="SendShortMessageByPhoneHttpGetIn">
    <wsdl:part name="paramPhones" type="s:string" />
    <wsdl:part name="paramContents" type="s:string" />
    <wsdl:part name="VerificationCode" type="s:string" />
  </wsdl:message>
  <wsdl:message name="SendShortMessageByPhoneHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="SendShortMessageByAccountHttpGetIn">
    <wsdl:part name="paramAccounts" type="s:string" />
    <wsdl:part name="paramContents" type="s:string" />
    <wsdl:part name="VerificationCode" type="s:string" />
  </wsdl:message>
  <wsdl:message name="SendShortMessageByAccountHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="SendVerifyMessageByPhoneHttpGetIn">
    <wsdl:part name="account" type="s:string" />
  </wsdl:message>
  <wsdl:message name="SendVerifyMessageByPhoneHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="SendShortMessageByPhoneHttpPostIn">
    <wsdl:part name="paramPhones" type="s:string" />
    <wsdl:part name="paramContents" type="s:string" />
    <wsdl:part name="VerificationCode" type="s:string" />
  </wsdl:message>
  <wsdl:message name="SendShortMessageByPhoneHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="SendShortMessageByAccountHttpPostIn">
    <wsdl:part name="paramAccounts" type="s:string" />
    <wsdl:part name="paramContents" type="s:string" />
    <wsdl:part name="VerificationCode" type="s:string" />
  </wsdl:message>
  <wsdl:message name="SendShortMessageByAccountHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="SendVerifyMessageByPhoneHttpPostIn">
    <wsdl:part name="account" type="s:string" />
  </wsdl:message>
  <wsdl:message name="SendVerifyMessageByPhoneHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:portType name="SendShortMessagesSoap">
    <wsdl:operation name="SendShortMessageByPhone">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">通过手机号码发送手机短信，手机号码支持多个，以逗号分隔，短信内容为单条。</wsdl:documentation>
      <wsdl:input message="tns:SendShortMessageByPhoneSoapIn" />
      <wsdl:output message="tns:SendShortMessageByPhoneSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SendShortMessageByAccount">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">通过账号发送手机短信，账号支持多个，以逗号分隔，短信内容为单条。</wsdl:documentation>
      <wsdl:input message="tns:SendShortMessageByAccountSoapIn" />
      <wsdl:output message="tns:SendShortMessageByAccountSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SendVerifyMessageByPhone">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">通过账号发送手机短信验证码,单个账号</wsdl:documentation>
      <wsdl:input message="tns:SendVerifyMessageByPhoneSoapIn" />
      <wsdl:output message="tns:SendVerifyMessageByPhoneSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="SendShortMessagesSoap" type="tns:SendShortMessagesSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="SendShortMessageByPhone">
      <soap:operation soapAction="http://tempuri.org/SendShortMessageByPhone" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendShortMessageByAccount">
      <soap:operation soapAction="http://tempuri.org/SendShortMessageByAccount" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendVerifyMessageByPhone">
      <soap:operation soapAction="http://tempuri.org/SendVerifyMessageByPhone" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="SendShortMessages">
    <wsdl:port name="SendShortMessagesSoap" binding="tns:SendShortMessagesSoap">
      <soap:address location="http://**************:6666/ESB.OnRamp.TwoWay.Basic/ProcessRequestResponse.svc?token=5UdUZVoZrHimSOKAOl5lkY70oEnWr7JUaVSupm%2BI29Q%3D" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>