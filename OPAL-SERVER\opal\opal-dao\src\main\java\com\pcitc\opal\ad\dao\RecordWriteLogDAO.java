package com.pcitc.opal.ad.dao;

import com.pcitc.opal.ad.entity.RecordWriteLogEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 报警记录写入日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-03
 */
public interface RecordWriteLogDAO extends BaseMapper<RecordWriteLogEntity> {

    List<RecordWriteLogEntity> getHistoryData(@Param("copmanyId") Long copmanyId);

}
