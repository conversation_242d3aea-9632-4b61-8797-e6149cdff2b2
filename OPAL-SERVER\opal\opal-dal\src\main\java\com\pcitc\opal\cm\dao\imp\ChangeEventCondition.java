package com.pcitc.opal.cm.dao.imp;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

/*
 * 变更事件查询工艺变更单条件实体
 * 模块编号：pcitc_pojo_ChangeEventCondition
 * 作       者：xuelei.wang
 * 创建时间：2017/09/30
 * 修改编号：1
 * 描       述：变更事件查询工艺变更单条件实体
 */
public class ChangeEventCondition {

    public ChangeEventCondition() {
    }
    public ChangeEventCondition(Long changeEventId,
                                Long companyId,
                                String unitCode,
                                String tag,
                                String alarmFlagName,
                                Date startTime,
                                String nowValue) {
        this.changeEventId = changeEventId;
        this.companyId = companyId;
        this.unitCode = unitCode;
        this.tag = tag;
        this.alarmFlagName = alarmFlagName;
        this.startTime = startTime;
        this.nowValue = nowValue;
    }

    /**
     * 变更事件ID
     */
    private Long changeEventId;

    /**
     * 企业ID
     */
    private Long companyId;
    /**
     *装置编码（工艺）
     */
    private String unitCode;

    /**
     *位号(工艺)
     */
    private String tag;
    /**
     * 报警标识NAME
     */
    private String alarmFlagName;

    /**
     *发生时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;

    /**
     *值
     */
    private String nowValue;



    public Long getChangeEventId() {
        return changeEventId;
    }

    public void setChangeEventId(Long changeEventId) {
        this.changeEventId = changeEventId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getNowValue() {
        return nowValue;
    }

    public void setNowValue(String nowValue) {
        this.nowValue = nowValue;
    }
}
