package com.pcitc.opal.pm.bll.imp;

import com.pcitc.imp.common.exception.BusiException;
import com.pcitc.opal.common.CommonEnum.PageModelEnum;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.CommonUtil;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.WorkshopService;
import com.pcitc.opal.pm.bll.entity.DBFactoryEntity;
import com.pcitc.opal.pm.bll.entity.DBWorkshopEntity;
import com.pcitc.opal.pm.dao.FactoryRepository;
import com.pcitc.opal.pm.dao.WorkshopRepository;
import com.pcitc.opal.pm.pojo.Factory;
import com.pcitc.opal.pm.pojo.Workshop;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.Date;
import java.util.List;

/*
 * 报警点业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmPointImpl
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点业务逻辑层实现类
 */
@Service
@Component
public class WorkshopImpl implements WorkshopService {

	@Autowired
	private WorkshopRepository workshopRepository;
	@Autowired
	private FactoryRepository factoryRepository;
	
	/**
	 * 新增实体
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopEntity 车间实体
	 * @return
	 * @throws Exception 
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addWorkshop(DBWorkshopEntity workshopEntity) throws Exception {
		// 实体转换为持久层实体
		Workshop workshopPO = ObjectConverter.entityConverter(workshopEntity, Workshop.class);
		// 数据校验
		workshopValidation(workshopPO);
		// 赋值 创建人、创建名称、创建时间
		CommonUtil.returnValue(workshopPO, PageModelEnum.NewAdd.getIndex());
		CommonResult commonResult = workshopRepository.addWorkshop(workshopPO);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false){
			throw new Exception(commonResult.getMessage());
		}
		return commonResult;
	}
	
	/**
	 * 校验
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param entity 车间实体类
	 * @throws Exception 
	 * @return void 
	 */
	private void workshopValidation(Workshop entity) throws Exception {
		CommonResult commonResult = new CommonResult();
		// 实体不能为空
		if (entity == null) {
			throw new BusiException("00", "没有车间数据！");
		}
		// 调用DAL与数据库相关的校验
		commonResult = workshopRepository.workshopValidation(entity);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
	}

	/**
	 * 删除车间维护数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopIds 车间维护主键Id集合
	 * @return
	 * @throws Exception 
	 */
	@Override
	public CommonResult deleteWorkshop(Long[] workshopIds) throws Exception {
		// 判断ID集合是否可用
		if (workshopIds == null || workshopIds.length <= 0) {
			throw new Exception("没有需要删除的车间数据！");
		}
		List<Workshop> anlyWorkshopList = workshopRepository.getWorkshop(workshopIds);
		if (anlyWorkshopList == null || anlyWorkshopList.isEmpty())
			return new CommonResult();
		Long[] anlyWorkshopIdList = anlyWorkshopList.stream().map(item -> item.getWorkshopId())
				.toArray(Long[]::new);
		// 调用DAL删除方法
		CommonResult commonResult = workshopRepository.deleteWorkshop(anlyWorkshopIdList);

		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
		return commonResult;
	}

	/**
	 * 更新车间
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopEntity 车间实体
	 * @return 
	 */
	@Override
	public CommonResult updateWorkshop(DBWorkshopEntity workshopEntity)  throws Exception {
		// 实体转换持久层实体
		Workshop workshopPO = ObjectConverter.entityConverter(workshopEntity, Workshop.class);
		// 校验
		workshopValidation(workshopPO);
		// 实体转换为持久层实体
		workshopPO = workshopRepository.getSingleWorkshop(workshopEntity.getWorkshopId());
		CommonUtil.objectExchange(workshopEntity, workshopPO);
		// 赋值 修改人、修改名称、修改时间
		CommonUtil.returnValue(workshopPO, PageModelEnum.Edit.getIndex());
		// 调用DAL更新方法
		CommonResult commonResult = workshopRepository.updateWorkshop(workshopPO);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
		return commonResult;
	}

	/**
	 * 通过车间ID获取单条数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopId 车间主键id
	 * @return
	 * @throws Exception 
	 */
	@Override
	public DBWorkshopEntity getSingleWorkshop(Long workshopId) throws Exception {
		Workshop workshop = workshopRepository.getSingleWorkshop(workshopId);
		DBWorkshopEntity wse = ObjectConverter.entityConverter(workshop, DBWorkshopEntity.class);
		wse.setFactorySname(workshop.getFactory().getSname());
		return wse;
	}

	/**
	 * 获取车间分页数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryId 工厂id
	 * @param workshopName 车间的名称或简称
	 * @param stdCode 标准编码
	 * @param inUse 是否启用
	 * @param page 分页对象
	 * @return
	 * @throws Exception 
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<DBWorkshopEntity> getWorkshop(Long factoryId, String workshopName, String stdCode,
			Integer inUse, Pagination page) throws Exception {
		PaginationBean<Workshop> listWorkshop = workshopRepository.getWorkshop(factoryId, workshopName, stdCode, inUse, page);
		PaginationBean<DBWorkshopEntity> returnWorkshop = new PaginationBean<>(page,listWorkshop.getTotal());
		returnWorkshop.setPageList(ObjectConverter.listConverter(listWorkshop.getPageList(), DBWorkshopEntity.class));
		int i = 0;
		for (DBWorkshopEntity wse : returnWorkshop.getPageList()) {
			Workshop ws = listWorkshop.getPageList().get(i);
			wse.setFactorySname(ws.getFactory().getSname());
			i++;
		}
		return returnWorkshop;
	}

	/**
	 * 获取工厂列表
	 * @param isAll  是否显示全部选项
	 * @return 工厂实体
	 */
	@Override
	public List<DBFactoryEntity> getFactoryList(boolean isAll) throws Exception {
		List<Factory> factoryList = factoryRepository.getFactory(null);
		List<DBFactoryEntity> factoryEntityList = ObjectConverter.listConverter(factoryList, DBFactoryEntity.class);
		if (isAll && factoryList.size() > 1) {
			DBFactoryEntity factoryEntity = new DBFactoryEntity();
			factoryEntity.setStdCode("-1");
			factoryEntity.setSname("全部");
			factoryEntityList.add(0, factoryEntity);
		}
		return factoryEntityList;
	}

	public Workshop getWorkShopByStdCode(String stdCode){
        Workshop workshop =workshopRepository.getWorkShopByStdCode(stdCode);
		return workshop;
	}
	
	//定时JOB专用勿动
	@Override
	public CommonResult updateWorkshopInfoById(Workshop workshopEntity) throws Exception {
		// 实体转换持久层实体
		Workshop workshopPO = ObjectConverter.entityConverter(workshopEntity, Workshop.class);
		// 校验
		workshopValidation(workshopPO);
		// 实体转换为持久层实体
		workshopPO = workshopRepository.getSingleWorkshop(workshopEntity.getWorkshopId());
		CommonUtil.objectExchange(workshopEntity, workshopPO);
		workshopPO.setMntDate(new Date());
		workshopPO.setMntUserId("admin");
		// 调用DAL更新方法
		CommonResult commonResult = workshopRepository.updateWorkshop(workshopPO);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
		return commonResult;
	}
}
