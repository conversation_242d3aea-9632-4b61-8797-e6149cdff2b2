$(function() {
    var alarmPrdtCompUrl = OPAL.API.pmUrl + "/alarmPrdtCellComp";
    var dcsUrl = OPAL.API.commUrl + "/getDcsCodeList";
    var opcUrl = OPAL.API.commUrl + "/getOpcCodeList";
    var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
    var unitPrdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';; //装置
    var pageMode = PageModelEnum.NewAdd;
    window.pageLoadMode = PageLoadMode.None;
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var page = {
        init: function() {
            this.bindUI();
        },
        bindUI: function() {
            $('#saveAddModal').click(function() {
                page.logic.save();
            });
            $('.closeBtn').click(function() {
                page.logic.closeLayer(false);
            })
            $('#closePage').click(function() {
                page.logic.closeLayer(false);
            })
        },
        logic: {
            /**
             * 保存
             */
            save: function() {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data = OPAL.form.getETCollectionData('AddOrEditModal');
                //处理提交类型
                var ajaxType = "POST";
                if (pageMode == PageModelEnum.NewAdd) {
                    window.pageLoadMode = PageLoadMode.Reload;
                } else if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                    window.pageLoadMode = PageLoadMode.Refresh;
                }
                $.ajax({
                    url: alarmPrdtCompUrl,
                    async: false,
                    type: ajaxType,
                    data: JSON.stringify(data),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function(result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！",{
                                time: 1000
                            },function() {
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function(data) {
                //初始化dcs
                page.logic.initDcs();
                //初始化opc
                page.logic.initOpc();
                //初始化装置树
                page.logic.initUnitTree();
                pageMode = data.pageMode;
                $("#pageTitle").text(data.title);
                if (pageMode == PageModelEnum.NewAdd) {
                    $('input[name=inUse]').attr('disabled', 'disabled');
                    return;
                }
                $.ajax({
                    url: alarmPrdtCompUrl + "/" + data.alarmPrdtCellCompId + "?now=" +Math.random(),
                    type: "get",
                    async: false,
                    dataType: "json",
                    success: function(data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        OPAL.form.setData('AddOrEditModal', entity);
                        page.logic.searchUnitPrdt(entity["unitId"]);
                        $("#prdtCellId").combotree('setValue', entity["prdtCellId"]);
                    },
                    complete: function(XMLHttpRequest, textStatus) {

                    },
                    error: function(XMLHttpRequest, textStatus) {

                    }
                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function(isRefresh) {
                parent.isRefresh = isRefresh;
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            formValidate: function() {
                $('#unitId').next('.textbox').find('input').attr('name', 'unitId');
                $('#unitId').next('.textbox').addClass('form-control-tree');
                $('#prdtCellId').next('.textbox').find('input').attr('name', 'prdtCellId');
                $('#prdtCellId').next('.textbox').addClass('form-control-tree');
                OPAL.form.formValidate('AddOrEditModal', {
                    rules: {
                        prdtCellSource: {
                            required: true
                        },
                        unitId: {
                            required: true
                        },
                        prdtCellId: {
                            required: true
                        }
                    }
                })
            },
            /**
             * 初始化dcs
             */
            initDcs: function() {
                OPAL.ui.getCombobox("dcsCodeId", dcsUrl, {
                    keyField: "dcsCodeId",
                    valueField: "name",
                    selectFirstRecord: true,
                    async:false
                }, null);
            },
            /**
             * 初始化opc
             */
            initOpc: function() {
                OPAL.ui.getCombobox("opcCodeId", opcUrl, {
                    keyField: "opcCodeId",
                    valueField: "name",
                    selectFirstRecord: true,
                    async:false
                }, null);
            },
             /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect("unitId", commonUnitTreeUrl, "id", "parentId", "sname", {
                    multiple: false,
                    onlyLeafCheck: true,
                    data: {
                        'enablePrivilege':false
                    },
                    onSelect: function(node) {
                        OPAL.ui.getComboMultipleSelect("prdtCellId", unitPrdtCellUrl, {
                            keyField: "prdtCellId",
                            valueField: "sname",
                            data: {
                                "unitId": node.id
                            },
                            treeviewConfig: {
                                multiple: false,
                                cascadeCheck: false,
                                onlyLeafCheck: true,
                                hasDownArrow: true,
                                lines: false,
                                animate: false,
                            }
                        }, false, function() {
                            $("#prdtCellId").combotree('selectFirstRecord');
                        });
                    }
                }, false, function() {
                });
                $("#unitId").combotree("getValues");
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect("prdtCellId", unitPrdtCellUrl, {
                        keyField: "prdtCellId",
                        valueField: "sname",
                        async: false,
                        data: {
                            "unitId": unitId
                        },
                        treeviewConfig: {
                            multiple: false,
                            cascadeCheck: false,
                            onlyLeafCheck: true,
                            hasDownArrow: true,
                            lines: false
                        }
                    }, true,
                    function() {});
            },
        }

    }
    page.init();
    window.page = page;
});