package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.common.*;
import com.pcitc.opal.pm.bll.CompanyService;
import com.pcitc.opal.pm.bll.entity.DBCompanyEntity;
import com.pcitc.opal.pm.dao.CompanyRepository;
import com.pcitc.opal.pm.pojo.Company;
import com.pcitc.opal.pm.pojo.Factory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class CompanyServiceImpl implements CompanyService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private CompanyRepository companyRepository;

    /**
     * 添加企业
     * @param companyEntity
     * @return
     */
    @Override
    public CommonResult addCompany(DBCompanyEntity companyEntity) throws Exception {
        // 实体转换为持久层实体
        Company company = ObjectConverter.entityConverter(companyEntity, Company.class);
        // 数据校验
        companyValidation(company);
        // 赋值  创建人、创建名称、创建时间
        CommonUtil.returnValue(company, CommonEnum.PageModelEnum.NewAdd.getIndex());
        CommonResult commonResult = companyRepository.addCompany(company);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 删除企业
     * @param
     * @return
     */
    @Override
    public CommonResult deleteCompany(Long[] companyIds) throws Exception {
        List<Company> anlyCompanyList = companyRepository.getCompany(companyIds);
        if (anlyCompanyList == null || anlyCompanyList.isEmpty())
            return new CommonResult();
        Long[] anlyCompanyIdList = anlyCompanyList.stream().map(item -> item.getCompanyId()).toArray(Long[]::new);
        // 调用DAL删除方法
        CommonResult commonResult = companyRepository.deleteCompany(anlyCompanyIdList);

        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 更新企业
     * @param companyEntity
     * @return
     * @throws Exception
     */
    @Override
    public CommonResult updateCompany(DBCompanyEntity companyEntity) throws Exception {
        // 实体转换持久层实体
        Company company = ObjectConverter.entityConverter(companyEntity, Company.class);
        // 校验
        companyValidation(company);
        // 实体转换为持久层实体
        company = companyRepository.getSingleCompany(companyEntity.getCompanyId());
        CommonUtil.objectExchange(companyEntity, company);
        // 赋值 修改人、修改名称、修改时间
        CommonUtil.returnValue(company, CommonEnum.PageModelEnum.Edit.getIndex());
        // 调用DAL更新方法
        CommonResult commonResult = companyRepository.updateCompany(company);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    @Override
    public DBCompanyEntity getSingleCompany(Long companyId) throws Exception {
        Company factoryPO = companyRepository.getSingleCompany(companyId);
        return ObjectConverter.entityConverter(factoryPO, DBCompanyEntity.class);
    }

    /**
     * 获取企业分页数据
     * @param name
     * @param stdCode
     * @param inUse
     * @param page
     * @return
     * @throws Exception
     */
    @Override
    public PaginationBean<DBCompanyEntity> getCompany(String name, String stdCode, Integer inUse, Pagination page) throws Exception {
        PaginationBean<Company> listFactory = companyRepository.getCompany(name, stdCode, inUse, page);
        PaginationBean<DBCompanyEntity> returnCompany = new PaginationBean<>(page,
                listFactory.getTotal());
        returnCompany.setPageList(ObjectConverter.listConverter(listFactory.getPageList(), DBCompanyEntity.class));
        return returnCompany;
    }

    private void companyValidation(Company company) throws Exception {
        CommonResult commonResult = null;
        // 实体不能为空
        if (company == null) {
            throw new Exception("没有企业数据！");
        }
        // 调用DAL与数据库相关的校验
        commonResult = companyRepository.companyValidation(company);

        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
    }

    @Override
    public List<DBCompanyEntity> getCompanyList(boolean isAll) {
        List<DBCompanyEntity> alarmPointTypeEntityList = new ArrayList<>();
        try {
            CommonProperty commonProperty = new CommonProperty();
            Integer companyId = commonProperty.getCompanyId();
            String hql = "from Company a where a.inUse=:inUse ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (companyId!=null) {
                hql += "and a.companyId=:companyId ";
                paramList.put("companyId", companyId.longValue());
            }
            hql+="order by a.sortNum asc, a.sname asc";
            paramList.put("inUse", CommonEnum.InUseEnum.Yes.getIndex());
            TypedQuery<Company> query = entityManager.createQuery(hql, Company.class);
            for(Map.Entry<String, Object> entry : paramList.entrySet())
            {
                query.setParameter(entry.getKey(),entry.getValue());
            }
            List<Company> companyList = query.getResultList();
            alarmPointTypeEntityList = ObjectConverter.listConverter(companyList, DBCompanyEntity.class);
            if (isAll && alarmPointTypeEntityList.size() > 1) {
                DBCompanyEntity entity = new DBCompanyEntity();
                entity.setCompanyId(-1L);
                entity.setSname("全部");
                alarmPointTypeEntityList.add(0, entity);
            }
        } catch (Exception ex) {
        }
        return alarmPointTypeEntityList;
    }

    @Override
    public List<Company> getAllCompanyId() {
        return companyRepository.findAll();
    }
}
