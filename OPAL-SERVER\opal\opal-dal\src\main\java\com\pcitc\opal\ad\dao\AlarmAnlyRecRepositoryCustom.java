package com.pcitc.opal.ad.dao;

import com.pcitc.opal.ad.dao.imp.AlarmAnlyRecVO;
import com.pcitc.opal.ad.dao.vo.AlarmRecAnlyVO;
import com.pcitc.opal.ad.pojo.AlarmAnlyRec;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

import java.util.Date;
import java.util.List;

public interface AlarmAnlyRecRepositoryCustom {

    PaginationBean<AlarmRecAnlyVO> getAlarmAnlyRec(String[] unitIds,
                                                   Long[] prdtCellIds,
                                                   Long alarmFlagId,
                                                   String tag,
                                                   Integer alarmStatus,
                                                   Integer anlyStatus,
                                                   Integer[] prioritys,
                                                   Date startTime,
                                                   Date endTime,
                                                   Date startTimeMonthAgo,
                                                   Integer[] monitorType,
                                                   Pagination page) throws Exception;

    List<AlarmRecAnlyVO> getAlarmAnlyRec(String[] unitIds,
                                                   Long[] prdtCellIds,
                                                   Long alarmFlagId,
                                                   String tag,
                                                   Integer alarmStatus,
                                                   Integer anlyStatus,
                                                   Integer[] prioritys,
                                                   Date startTime,
                                                   Date endTime,
                                                   Date startTimeMonthAgo,
                                                   Integer[] monitorType) throws Exception;

    List<AlarmAnlyRecVO> getAlarmAnlyByRec(Long alarmRecId) throws Exception;


    CommonResult updateAlarmAnlyRecs(List<AlarmAnlyRec> alarmAnlyRec);
    CommonResult updateAlarmAnlyRec(AlarmAnlyRec alarmAnlyRec);

    CommonResult addAlarmAnlyRec(AlarmAnlyRec alarmAnlyRec);

    List<AlarmAnlyRec> getAlarmAnlyRecsById(Long[] id);
    List<AlarmAnlyRec> getAlarmAnlyRecById(Long id);
}
