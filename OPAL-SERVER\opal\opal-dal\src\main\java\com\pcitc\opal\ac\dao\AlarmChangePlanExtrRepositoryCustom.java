package com.pcitc.opal.ac.dao;

import com.pcitc.opal.ac.pojo.AlarmChangePlanExtr;
import com.pcitc.opal.common.CommonResult;

/*
 * 报警变更方案附加信息实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_AlarmChangePlanExtrRepositoryCustom
 * 作       者：dageng.sun
 * 创建时间：2018/1/30
 * 修改编号：1
 * 描       述：报警变更方案附加信息实体的Repository的JPA自定义接口
 */
public interface AlarmChangePlanExtrRepositoryCustom {

	/**
	 * 新增报警变更方案附加信息
	 *
	 * <AUTHOR> 2018-01-30
	 * @param alarmChangePlanExtr 报警变更方案附加信息
	 * @return 返回结果信息类
	 */
	CommonResult addAlarmChangePlanExtr(AlarmChangePlanExtr alarmChangePlanExtr);

	/**
	 * 删除数据
	 *
	 * <AUTHOR> 2018-01-30
	 * @param planExtrIds 报警变更方案附加信息ID
	 */
	CommonResult deleteAlarmChangePlanExtr(Long[] planExtrIds) throws Exception;

	/**
	 * 获取报警变更方案附加信息
	 * 
	 * <AUTHOR> 2018-01-30 
	 * @param planId 报警变更方案ID
	 * @param businessType 业务类型(1下发；2确认)
	 * @return AlarmChangePlanExt 返回AlarmChangePlanExt实体
	 */
	AlarmChangePlanExtr getAlarmChangePlanExtrByPlanId (Long planId,Integer businessType);
	
}
