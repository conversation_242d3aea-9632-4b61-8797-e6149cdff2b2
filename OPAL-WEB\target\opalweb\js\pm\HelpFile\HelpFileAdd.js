$(function() {
    var addUrl = OPAL.API.pmUrl + '/helpFile/addHelpFile';
    var delUrl = OPAL.API.pmUrl + '/helpFile/deleteHelpFile';
    var getSingleUrl = OPAL.API.pmUrl + '/helpFile/';
    var getUserNameUrl = OPAL.API.commUrl + '/getSysUserInfo';
    var downloadFileUrl = OPAL.API.pmUrl + '/helpFile/downloadFile';
    var pageMode = PageModelEnum.NewAdd;
    window.pageLoadMode = PageLoadMode.Refresh;
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var uploader;

    var page = {
        init: function() {
            this.bindUI();


            //初始化上传人
            //page.logic.initUserName();
            page.logic.upload();
        },
        bindUI: function() {
            /*关闭弹窗*/
            $('#closePage').click(function() {
                page.logic.closeLayer(false);
            });
            $('.closeBtn').click(function() {
                page.logic.closeLayer(true);
            });
            //保存
            $("#saveAddModal").click(function() {
                page.logic.save();
            });

            //删除附件
            $("#delAtt").click(function() {
                page.logic.delAtt();
            });
            $("#attName").click(function() {
                page.logic.download();
            })
        },
        logic: {

            /*//初始化登录人
            initUserName: function() {
                $.ajax({
                    url: getUserNameUrl,
                    async: true,
                    dataType: "json",
                    success: function(data) {
                        var dataArr = $.ET.toObjectArr(data);
                        $("#uplUserName").val(dataArr[1].value);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {}
                });
            },*/
            //上传
            upload: function() {
                uploader = new plupload.Uploader({
                    browse_button: 'fileId2', //触发文件选择对话框的按钮，为那个元素id
                    url: addUrl, //服务器端的上传页面地址
                    flash_swf_url: '../../../js/common/plupload/js/Moxie.swf', //swf文件，当需要使用swf方式进行上传时需要配置该参数
                    multi_selection: false,
                    multipart_params: {
                        'collectionStr': ''
                    }
                });
                uploader.init();
                uploader.bind('FilesAdded', function(uploader, files) {
                    if (uploader.files.length > 1) {
                        uploader.removeFile(uploader.files[0]);
                    }
                    $("#fileName").val(files[0].name);
                    page.logic.save();
                });
                uploader.bind('FilesRemoved', function(uploader, files) {
                    $("#fileName").val("");
                });
                uploader.bind('FileUploaded', function(uploader, file, responseObject) {
                    var result = responseObject.response;
                    if (result.indexOf('collection') < 0) {
                        var sd = JSON.parse(result);
                        var helpFileId = sd.result.helpFileId;
                        page.logic.setData(helpFileId)
                        layer.msg("保存成功！");
                        /*setTimeout(function() {
                            page.logic.closeLayer(true);
                        }, 1000)*/
                    } else {
                        layer.msg(result.collection.error.message);
                    }
                });
                uploader.bind('Error', function(uploader, errObject) {
                    errObject.file.status = 2;
                    layer.msg(JSON.parse(errObject.response).collection.error.message)
                });

            },
            /**
             * 保存
             */
            save: function() {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data = OPAL.form.getETCollectionData("AddOrEditModal");
                uploader.settings.multipart_params = {
                    'collectionStr': JSON.stringify(data)
                };
                if (pageMode == PageModelEnum.NewAdd) {
                    if ($("#fileName").val() == '') {
                        layer.msg("附件不能为空！");
                        return;
                    }
                    uploader.start();
                }
            },
            //数据校验
            formValidate: function() {
                $.validator.addMethod("dateCheck", function(value, element) {
                    var returnVal = true;
                    var reg = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$/;
                    if (!reg.test(value)) returnVal = false;
                    return returnVal;
                }, "时间格式错误！"); //验证错误信息
                OPAL.form.formValidate('AddOrEditModal', {
                    rules: {
                        name: {
                            required: true,
                            rangelength: [0, 100]
                        },

                        fileName: {
                            rangelength: [0, 100]
                        }
                    },
                    messages: {
                        fileName: {
                            rangelength: "文件名最大字符长度100"
                        }
                    }
                })
            },

            /**
             * 初始化编辑数据
             */
            setData: function(helpFileId) {
                // pageMode = data.pageMode;
                //$("#pageTitle").text(data.title);

                $.ajax({
                    url: getSingleUrl + helpFileId,
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function(data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        OPAL.form.setData('AddOrEditModal', entity);
                        $("#helpFileId").val(entity.helpFileId);
                        $("#fileName").val('');
                        $("#attName").html(entity.fileName);
                        $("#AttachmentBox").show();
                    },
                    complete: function(XMLHttpRequest, textStatus) {

                    },
                    error: function(XMLHttpRequest, textStatus) {

                    }
                });
            },

            setPage:function(data){
                pageMode = data.pageMode;
                $("#pageTitle").text(data.title);
            },

            /**
             * 关闭弹出层
             */
            closeLayer: function(isRefresh) {
                parent.isRefresh = isRefresh;
                parent.layer.close(index);
            },
            //删除附件
            delAtt: function() {
                layer.confirm('确定删除此附件吗？', {
                    btn: ['确定', '取消']
                }, function(index) {
                    $("#attName").html("");
                    $("#AttachmentBox").hide();
                    var data = new Array();
                    data.push($("#helpFileId").val());
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function(result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                    layer.close(index);
                }, function(index) {});
            },
            download: function() {
                $('#formExPort').attr('action', downloadFileUrl);
                $('#fileName1').val($("#attName").html());
                $('#fileId1').val($("#fileId").val());
                $('#formExPort').submit();
            }
        }
    }
    page.init();
    window.page = page;
});
