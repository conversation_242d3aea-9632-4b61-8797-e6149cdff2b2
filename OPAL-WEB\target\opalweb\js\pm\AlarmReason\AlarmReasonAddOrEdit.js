var addUrl = OPAL.API.pmUrl + '/alarmReason/addAlarmReason';
var updateUrl = OPAL.API.pmUrl + '/alarmReason/updateAlarmReason';
var reasonTpyeDataUrl = OPAL.API.pmUrl + '/alarmReason/getReasonType';
var getSingleUrl = OPAL.API.pmUrl + '/alarmReason/getReasonType';
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.None;
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
        },
        bindUI: function () {
            $('#saveAddModal').click(function () {
                page.logic.save();
            });
            $('.closeBtn').click(function () {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer(false);
            })
        },
        logic: {
            /**
             * 初始化查询 原因分类
             */
            initReasonTypeList: function() {
                $.ajax({
                    url: reasonTpyeDataUrl + "?now=" + Math.random(),
                    type: "get",
                    data: {
                        'isAll': false
                    },
                    async: false,
                    dataType: "json",
                    success: function (data) {
                        //初始化原因分类
                        delete data[0];
                        let str = '';
                        data.forEach((item,index) => {
                            str+= '<option value="'+item.key+'">'+item.value+'</option>'
                        })
                        $('#reasonType').html(str);
                        $('#reasonType').val(1);
                    }
                });
            },
            /**
             * 保存
             */
            save: function () {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                // var data = OPAL.form.getETCollectionData('AddOrEditModal');
                var data = OPAL.form.getData('AddOrEditModal');
                //处理提交类型
                var ajaxType = "POST";
                if (pageMode == PageModelEnum.NewAdd) {
                    window.pageLoadMode = PageLoadMode.Reload;
                }
                else if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                    window.pageLoadMode = PageLoadMode.Refresh;
                    addUrl = updateUrl;
                }
                $.ajax({
                    url: addUrl,
                    async: false,
                    type: ajaxType,
                    data: data,
                    // processData: false,
                    // contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg(result,{
                                time: 1000
                            },function() {
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                //初始化原因分类
                page.logic.initReasonTypeList();
                if (pageMode == PageModelEnum.NewAdd) {
                    $('input[name=inUse]').attr('disabled', 'disabled');
                    return;
                }
                OPAL.form.setData('AddOrEditModal', JSON.parse(data.row));
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal',{
                    rules: {
                        name: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        sortNum: {
                            required: true,
                            digits: true,
                            min: 0
                        }
                    }
                })

            }
        }

    }
    page.init();
    window.page = page;
})