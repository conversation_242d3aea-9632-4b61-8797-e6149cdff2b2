package com.pcitc.opal.pm.dao.imp;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;
import javax.persistence.TypedQuery;

import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.pm.pojo.Factory;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.WorkshopRepositoryCustom;
import com.pcitc.opal.pm.pojo.Workshop;

/*
 * Workshop实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_WorkshopRepositoryImpl
 * 作    者：dageng.sun
 * 创建时间：2017/12/11
 * 修改编号：1
 * 描    述：Workshop实体的Repository实现
 */
public class WorkshopRepositoryImpl extends BaseRepository<Workshop, Long> implements WorkshopRepositoryCustom {

	/**
	 * 新增车间
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopEntity 车间实体
	 * @return 
	 */
	@Override
	public CommonResult addWorkshop(Workshop workshopEntity) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			this.getEntityManager().persist(workshopEntity);
			commonResult.setResult(workshopEntity);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		return commonResult;
	}

	/**
	 * 获取车间集合
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopIds 车间主键id集合
	 * @return 
	 */
	@Override
	public List<Workshop> getWorkshop(Long[] workshopIds) {
		try {
			// 查询字符串
			String hql = "from Workshop t ";
			Map<String, Object> paramList = new HashMap<String, Object>();
			if (workshopIds.length > 0) {
				hql += " where t.workshopId in (:workshopIds)";
				List<Long> alarmPointIdsList = Arrays.asList(workshopIds);
				paramList.put("workshopIds", alarmPointIdsList);
			}
			TypedQuery<Workshop> query = getEntityManager().createQuery(hql, Workshop.class);
			this.setParameterList(query, paramList);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 删除车间实体
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopIds 车间主键id集合
	 * @return 
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteWorkshop(Long[] workshopIds) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = " from Workshop t where t.workshopId in (:workshopIds)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			List<Long> workshopIdList = Arrays.asList(workshopIds);
			paramList.put("workshopIds", workshopIdList);

			TypedQuery<Workshop> query = getEntityManager().createQuery(hql, Workshop.class);
			this.setParameterList(query, paramList);
			List<Workshop> workshopList = query.getResultList();
			workshopList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	/**
	 * 更新车间
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopEntity 车间实体
	 * @return 消息结果类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult updateWorkshop(Workshop workshopEntity) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			getEntityManager().merge(workshopEntity);
			commonResult.setResult(workshopEntity);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("更新成功！");
		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	/**
	 * 获取车间实体
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopId 车间主键id
	 * @return 
	 */
	@Override
	public Workshop getSingleWorkshop(Long workshopId) {
		try {
			StringBuilder hql = new StringBuilder("select ws from Workshop ws ");
			hql.append("left join fetch ws.factory f ");
			hql.append("where ws.workshopId=:workshopId ");
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("workshopId", workshopId);
			TypedQuery<Workshop> query = getEntityManager().createQuery(hql.toString(), Workshop.class);
			this.setParameterList(query, paramList);
			return query.getSingleResult();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 获取车间实体（分页）
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryId 工厂id
	 * @param workshopName 车间的名称或简称
	 * @param stdCode 标准编码
	 * @param inUse 是否启用
	 * @param page 分页对象
	 * @return 
	 */
	@Override
	public PaginationBean<Workshop> getWorkshop(Long factoryId, String workshopName, String stdCode, Integer inUse,
			Pagination page) {
		try {
			CommonProperty commonProperty = new CommonProperty();
			Integer companyId =commonProperty.getCompanyId();
			// 查询字符串
			StringBuilder hql = new StringBuilder("select ws from Workshop ws ");
			StringBuilder hqlWhere = new StringBuilder("where 1=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();

			// 工厂
			hql.append("left join fetch ws.factory f ");
			if (!StringUtils.isEmpty(factoryId)) {
				hqlWhere.append("and f.factoryId =:factoryId ");
				paramList.put("factoryId", factoryId);
			}else if (!StringUtils.isEmpty(companyId)){
				hqlWhere.append("and f.companyId =:companyId ");
				paramList.put("companyId", companyId.longValue());
			}
			// 车间的名称或简称
			if (!StringUtils.isEmpty(workshopName)) {
				hqlWhere.append("and (ws.name like :name escape '/' or ws.sname like :sname escape '/') ");
				paramList.put("name", "%" + this.sqlLikeReplace(workshopName) + "%");
				paramList.put("sname", "%" + this.sqlLikeReplace(workshopName) + "%");
			}
			// 标准编码
			if (!StringUtils.isEmpty(stdCode)) {
				hqlWhere.append("and upper(ws.stdCode) like upper(:stdCode) escape '/' ");
				paramList.put("stdCode", "%" + this.sqlLikeReplace(stdCode) + "%");
			}
			// 是否启用
			if (!StringUtils.isEmpty(inUse) && inUse != -1) {
				hqlWhere.append("and ws.inUse=:inUse ");
				paramList.put("inUse", inUse);
			}
			hqlWhere.append("order by f.sortNum,f.sname,ws.sortNum,ws.name,ws.sname");

			// 调用基类方法查询返回结果
			PaginationBean<Workshop> bean = this.findAll(page, hql.toString() + hqlWhere.toString(), paramList);
			return bean;
		} catch (Exception ex) {
			throw ex;
		}
	}
	
	/**
	 * 校验
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopEntity 车间实体
	 * @return 
	 */
	@Override
	public CommonResult workshopValidation(Workshop workshopEntity) {
		CommonResult commonResult = new CommonResult();
		try {
			// “名称”唯一性校验，提示“此名称已存在！”；
			StringBuilder namehql = new StringBuilder("from Workshop t where t.workshopId<>:workshopId and t.name=:name and t.factoryId=:factoryId ");
			Map<String, Object> nameList = new HashMap<String, Object>();
			nameList.put("workshopId",workshopEntity.getWorkshopId() == null ? 0 : workshopEntity.getWorkshopId());
			nameList.put("factoryId", workshopEntity.getFactoryId());
			nameList.put("name", workshopEntity.getName());
			long nameIndex = this.getCount(namehql.toString(), nameList);
			if (nameIndex > 0) {
				commonResult.setIsSuccess(false);
				commonResult.setMessage("该工厂下此名称已存在！");
				return commonResult;
			}
			// “简称”唯一性校验，提示“此简称已存在！”；
			StringBuilder snamehql = new StringBuilder("from Workshop t where t.workshopId<>:workshopId and t.sname=:sname and t.factoryId=:factoryId ");
			Map<String, Object> snameList = new HashMap<String, Object>();
			snameList.put("workshopId",workshopEntity.getWorkshopId() == null ? 0 : workshopEntity.getWorkshopId());
			snameList.put("factoryId", workshopEntity.getFactoryId());
			snameList.put("sname", workshopEntity.getSname());
			long snameIndex = this.getCount(snamehql.toString(), snameList);
			if (snameIndex > 0) {
				commonResult.setIsSuccess(false);
				commonResult.setMessage("该工厂下此简称已存在！");
				return commonResult;
			}
			// “标准编码”唯一性校验，提示“此标准编码已存在！”；
			StringBuilder stdCodehql = new StringBuilder("from Workshop t where t.workshopId<>:workshopId and t.stdCode=:stdCode ");
			Map<String, Object> stdCodeList = new HashMap<String, Object>();
			stdCodeList.put("workshopId",workshopEntity.getWorkshopId() == null ? 0 : workshopEntity.getWorkshopId());
			stdCodeList.put("stdCode", workshopEntity.getStdCode());
			long stdCodeIndex = this.getCount(stdCodehql.toString(), stdCodeList);
			if (stdCodeIndex > 0) {
				commonResult.setIsSuccess(false);
				commonResult.setMessage("此标准编码已存在！");
				return commonResult;
			}

		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
			commonResult.setResult(null);
		}
		return commonResult;
	}

	public Workshop getWorkShopByStdCode(String stdCode){
		try {
			String sql = "from Workshop where stdCode=:stdCode";
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("stdCode", stdCode);
			Query query = getEntityManager().createQuery(sql.toString(),Workshop.class);
			this.setParameterList(query, paramList);
			if(query.getResultList().size()==0){
				return null;
			}else{
				Query q = getEntityManager().createQuery(sql.toString(),Workshop.class);
				this.setParameterList(q, paramList);
				return (Workshop) q.getSingleResult();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<Workshop> getWorkShopByFactoryId(List<Long> factoryId) {
		String hql = "from Workshop where factoryId in (:factoryId)";
		TypedQuery<Workshop> query = getEntityManager().createQuery(hql, Workshop.class);
		query.setParameter("factoryId", factoryId);
		return query.getResultList();
	}
}
