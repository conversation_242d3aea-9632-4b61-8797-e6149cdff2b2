var getChemicalAlarmDataSumUrl = OPAL.API.pmUrl + '/comunit/getChemiengAlarmData';
var getChemicalAlarmDataEveryDayUrl = OPAL.API.pmUrl+ "/comunit/getMostAlarmTop";
var getAlarmPriorityByUnitIdUrl = OPAL.API.commUrl + "/getAlarmPriorityByUnitId";
var getISOAlarmListUrl = OPAL.API.commUrl + "/getISOAlarmList";
var getAlarmAnalysisDataUrl = OPAL.API.afUrl + '/alarmAnalysis/getAlarmAnalysisData';
var getContinuousAlarmListUrl = OPAL.API.afUrl + '/alarmAnalysis/getContinuousAlarmList';
var getShelvedAlarmListUrl = OPAL.API.afUrl + '/alarmAnalysis/getShelvedAlarmList';
var getSuppressedAlarmListUrl = OPAL.API.afUrl + '/alarmAnalysis/getSuppressedAlarmList';

var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllGdUnit";   //装置树

var getShowTimeUrl = OPAL.API.commUrl + '/getShowTime';


var mostAlarmCharts, mostAlarmOperateCharts;
var ISOAlarmEmergency; //报警优先级国际标准 紧急
var ISOAlarmImportance; // 报警优先级国际标准 重要
var ISOAlarmNormal; // 报警优先级国际标准 一般
var startDate, endDate, startTime, endTime; // 开始日期 结束日期 开始时间 结束时间
var newEndTime;
var unitId, unitName; //  装置id
var AlarmEventTotalCount; // 总报警数
var operateCount; // 总操作数
var dateTimeList; //时间列表
var endFlag;
var topOperateCount=20;
var topCount=20;
$(function () {
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var page = {
        /**
         * 初始化
         */
        init: function () {
            this.bindUi();
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                mostAlarmCharts.resize();
                mostAlarmOperateCharts.resize();
            };

            page.logic.getShowTime();
            page.logic.initUnitTree();
            topOperateCount=10;
            topCount=10;
            page.logic.queryMostAlarm().then(function(data){
                    page.logic.queryMostOperate(data[0].unit_id,1)
                })
        },
        data: {
            // 设置查询参数
            param: {},
            param1:{}
        },
        bindUi: function () {
            /**
             * 导航切换
             */
            /**
            $('.myTab li').click(function () {
                var flag = $(this).attr('showFlag');
                if (flag == 'imgShow') {
                    $(this).find('img').attr('src', '../../../images/one1.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/tweo.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                } else if (flag == 'tableShow') {
                    $(this).find('img').attr('src', '../../../images/tweos.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                } else if (flag == 'unitShow') {
                    $(this).find('img').attr('src', '../../../images/treese.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/tweo.png');
                }
            });
            */
            /**
             * 查询
             */

            $('#btnSearch').click(function () {
                if (!page.logic.checkDateIsValid()) {
                    return false;
                }
                if (OPAL.util.checkDateIsValid() == true) {
                    isLoading = false;
                    page.logic.search();
                }
            })
            // 关闭
            $('#closePage').click(function () {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer(false);
            })
        },
        logic: {

            search: function () {
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                page.data.param = OPAL.form.getData("searchForm");
                var unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                page.data.param.unitIds = unitIds;
                page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                $("#btnSearch").prop('disabled', true);

                $.ajax({
                    url: getChemicalAlarmDataSumUrl,
                    async: true,
                    data: page.data.param,
                    dataType: "JSON",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        if (res != null && res != undefined && res != '') {
                            page.logic.initMostAlarmCharts(res);
                            page.logic.queryMostOperate(res[0].unit_id,2)
                    }},
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },

            /**
             * 从父页面传来值
             */
            /** 
            setData: function (data) {
                page.logic.initMostTable();
                var dataArr = JSON.parse(data.dateTimeList);
                dateTimeList = data.dateTimeList;
                endDate = data.alarmTime;
                endFlag = JSON.parse(dateTimeList)[5].value
                $.each(dataArr, function (i, el) {
                    if (el['key'] == 'startTime') {
                        startDate = OPAL.util.dateFormat(new Date(el['value']), 'yyyy-MM-dd');
                        startTime = OPAL.util.dateFormat(new Date(el['value']), 'yyyy-MM-dd HH:mm:ss');
                    }
                    if (el['key'] == 'endTime') {
                        // endDate = OPAL.util.dateFormat(new Date(el['value']), 'yyyy-MM-dd');
                        endTime = OPAL.util.dateFormat(new Date(el['value']), 'yyyy-MM-dd HH:mm:ss');
                    }
                })
                //初始化查询结束时间(不传endFlag)
                page.logic.initQueryEndTime(endTime,endFlag);
                unitId = data.unitId;
                unitName = data.unitName;
                $('#unitNameShow').html('装置：' + unitName);
                $('#timeShow').html('时间：' + startDate + ' 至 ' + endDate);
                // 设置 加载 持续报警 table
                page.logic.initAlarmTable();
                page.data.param1= {
                    startTime: startTime,
                    endTime: newEndTime,
                    unitId: unitId,
                    top: 20
                };
                $.ajax({
                    url: getAlarmAnalysisDataUrl,
                    async: true,
                    data: {
                        startTime: startTime,
                        endTime: newEndTime,
                        unitId: unitId,
                        top: 20
                    },
                    dataType: "JSON",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        if (res != null && res != undefined && res != '') {
                            // 报警总数
                            AlarmEventTotalCount = res[0].alarmCount;
                            $('#AlarmEventTotalCount').html(AlarmEventTotalCount);
                            // 操作总数
                            operateCount = res[0].operateCount;
                            $('#operateCount').html(operateCount)
                            // 最频繁报警
                            page.logic.initMostAlarmCharts(res[0].mostAlarm);
                            // 最频繁操作
                            page.logic.initMostAlarmOperateCharts(res[0].mostAlarmOperate);
                        }

                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });

            },
            */

            /**
             * 查询主图操作
             */
            queryMostAlarm:function(){
                var dtd =$.Deferred();
                $.ajax({
                    url: getChemicalAlarmDataSumUrl,
                    async: true,
                    data: page.data.param,
                    dataType: "JSON",
                    type: 'GET'})
                    .then(
                    function success (result) {
                        var res = $.ET.toObjectArr(result);
                        if (res != null && res != undefined && res != '') {
                           page.logic.initMostAlarmCharts(res);
                           if(page.data.param.startTime==null||page.data.param.endTime==null){
                           page.data.param = OPAL.form.getData("searchForm");
                           page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                           page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                           }
                          dtd.resolve(res)
                        }
                    },
                    function error (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                        dtd.reject({'msg':"失败"})
                    }
                    );
                return dtd.promise();
            },
            /**
             * 查询每天
             */
            queryMostOperate:function(unitId,type){
                page.data.param1.unitId=unitId;
                if (type ==1){
                page.data.param1.startTime=startDate;
                page.data.param1.endTime=endDate;
                }else{
                page.data.param1.startTime = OPAL.util.dateFormat(page.data.param.startTime,"yyyy-MM-dd");
                page.data.param1.endTime = OPAL.util.dateFormat(page.data.param.endTime,"yyyy-MM-dd");
                }
                $.ajax({
                    url:getChemicalAlarmDataEveryDayUrl,
                    async: true,
                    data: page.data.param1,
                    dataType: "text",
                    type: 'GET'}).then(
                     function success (result) {
                        var res = $.ET.toObjectArr(result);
                        if (res != null && res != undefined && res != '') {
                            page.logic.initMostAlarmOperateCharts(res);
                           $("#btnSearch").prop('disabled', false);
                        }
                    },
                    function error (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                    )
            },
            /**
             * 加载 最频繁报警 数据
             */
            initMostAlarmCharts: function (data) {
                var res=data;
                if(typeof(res)=="string")
                    res = JSON.parse(data);
                if (mostAlarmCharts && !mostAlarmCharts.isDisposed()) {
                    mostAlarmCharts.clear();
                    mostAlarmCharts.dispose();
                }
                if (res == undefined || res == null || res.length == 0) {
                    mostAlarmCharts = OPAL.ui.chart.initEmptyChart('shock-alarm-chart');
                    return;
                }
                mostAlarmCharts = echarts.init(document.getElementById('shock-alarm-chart'));
                option = {
                    color: ['#3398DB'],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { // 坐标轴指示器，坐标轴触发有效
                            type: '' // 默认为直线，可选为：'line' | 'shadow'
                        },
                        formatter: function (params) {
                            if (params[0] != null && params[0] != undefined && params[0] != '') {
                                var tag = params[0].name;
                                var count = params[0].value;
                                var dataIndex = params[0].dataIndex;
                                var flagName = res[dataIndex].rec_date;
                            }
                            // var str = '位号：' + tag + '&nbsp;&nbsp;&nbsp;报警标识：' + flagName + '<br>报警数：' + count
                            var str = '位号：' + tag + ' 报警数：' + count
                            return str;
                        }
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        bottom: '1%',
                        top: '5%',
                        height: '220px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        axisTick: {
                            alignWithLabel: true
                        },
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 0,
                            rotate: 15,
                            textStyle: {
                                fontSize: 10
                            }
                        },
                    },

                    ],
                    yAxis: [{
                        type: 'value',
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },

                    }],
                    series: [{
                        type: 'bar',
                        barWidth: '15',
                        data: []
                    }]
                };
                mostAlarmCharts.setOption(option);
                var totalArray = new Array();
                var tagArray = new Array();
                for (var i = 0; i < res.length; i++) {
                    totalArray.push(res[i]["alarm_num"]);
                    // 插入设备名称
                    tagArray.push(res[i]['sname']);
                }
                option.xAxis[0].data = tagArray;
                option.series[0].data = totalArray;
                mostAlarmCharts.setOption(option);
                // mostAlarmCharts.on('click', function (params) {
                // })
                mostAlarmCharts.off('click')
                mostAlarmCharts.getZr().on('click', function (params) {
                    var pointInPixel = [params.offsetX,params.offsetY];
                    if(mostAlarmCharts.containPixel('grid',pointInPixel)){
                        var pointInGrid= mostAlarmCharts.convertFromPixel({
                         seriesIndex:0
                        },pointInPixel);
                        var xIndex = pointInGrid[0];
                        var handleIndex =Number(xIndex)
                        var op = mostAlarmCharts.getOption();
                        var unit_id = op.xAxis[0].data[handleIndex]
                        page.logic.queryMostOperate(res[handleIndex]['unit_id'])
                    }
                 })

            },
            /**
             * 加载 最频繁操作 数据
             */
            initMostAlarmOperateCharts: function (data) {
                var res=data;
                if(typeof(res)=="string")
                    res = JSON.parse(data);
                if (mostAlarmOperateCharts && !mostAlarmOperateCharts.isDisposed()) {
                    mostAlarmOperateCharts.clear();
                    mostAlarmOperateCharts.dispose();
                }
                if (res == undefined || res == null || res.length == 0) {
                    mostAlarmOperateCharts = OPAL.ui.chart.initEmptyChart('shock-alarm-chart2');
                    return;
                }
                mostAlarmOperateCharts = echarts.init(document.getElementById('shock-alarm-chart2'));
                option = {
                    color: ['#00acac'],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { // 坐标轴指示器，坐标轴触发有效
                            type: '' // 默认为直线，可选为：'line' | 'shadow'
                        },
                        formatter: function (params) {
                            if (params[0] != null && params[0] != undefined && params[0] != '') {
                                var tag = params[0].name;
                                var count = params[0].value;
                                var dataIndex = params[0].dataIndex;
                                var flagName = res[dataIndex].flagName;
                            }
                            var str = '日期：' + tag +  ' 操作数：' + count
                            return str;
                        }
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        bottom: '1%',
                        top: '5%',
                        height: '220px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        axisTick: {
                            alignWithLabel: true
                        },
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 0,
                            rotate: 15,
                            textStyle: {
                                fontSize: 10
                            }
                        }
                    }],
                    yAxis: [{
                        type: 'value',
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                    }],
                    series: [{
                        // name: '',
                        type: 'bar',
                        barWidth: '15',
                        data: []
                    }]
                };
                mostAlarmOperateCharts.setOption(option);
                var totalArray = new Array();
                var tagArray = new Array();
                for (var i = 0; i < res.length; i++) {
                    totalArray.push(res[i]["alarm_num"]);
                    // tagArray.push(res[i]['unit_id']);
                    tagArray.push(res[i]['rec_date']);
                }
                option.xAxis[0].data = tagArray;
                option.series[0].data = totalArray;
                mostAlarmOperateCharts.setOption(option);
                mostAlarmOperateCharts.on('click', function (params) {
                })
            }, 
                
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    unitId: unitId,
                    startTime: startTime,
                    endTime: newEndTime,
                    now: Math.random()
                };
                page.data.param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                };
                return param;
            },

             /**
             * 校验时间
             */
                       checkDateIsValid: function () {
                        var startTime = OPAL.util.strToDate($('#startTime').val());
                        var endTime = OPAL.util.strToDate($('#endTime').val());
                        if ($('#startTime').val() == "" || $('#endTime').val() == "" || $('#startTime').val() == undefined || $('#endTime').val() == undefined) {
                            layer.msg("开始时间和结束时间不能为空！");
                            return false;
                        } else if ((endTime - startTime) < 0) {
                            layer.msg("报警开始时间不能大于结束时间！");
                            return false;
                        } else if ((endTime - startTime) > (1000*60*60*24*30)) {
                            layer.msg("查询时间范围不能超过30天！");
                            return false;
                        }
                        return true;
                    },

            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.layer.close(index);
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function (node, checked) {
                        var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (unitIds != undefined && unitIds.length == 1) {
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    }
                }, false);
            },
            /**
             * 获取查询结束时间
             */
            initQueryEndTime: function (endTime, endFlag) {
                if (endFlag == "<") {
                    newEndTime= moment(endTime).subtract(1, 'seconds').format('YYYY-MM-DD HH:mm:ss');;
                }
                else {
                    newEndTime= endTime;
                }
            },
            getShowTime: function () {
                $.ajax({
                    url: getShowTimeUrl,
                    // async: true,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        getStartTime = dataArr[0].value.split(' ')[0];
                        getEndTime = dataArr[1].value.split(' ')[0];
                        startDate = getStartTime
                        endDate =getEndTime
                        //设置时间插件
                        page.logic.initTime();
                        // document.getElementById("btnSearch").click();
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 设置日期插件
             */
            initTime: function () {
                var myDate = new Date();
                var start = laydate.render({
                    elem: '#startTime', //指定元素
                    type: 'date',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd', //日期格式
                    value: getStartTime,
                    max: getEndTime, //最大日期
                });
                var end = laydate.render({
                    elem: '#endTime',
                    type: 'date',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd',
                    value: getEndTime,
                    max: getEndTime,
                });
                $('#startTime').attr('maxDate', getEndTime)
                $('#endTime').attr('maxDate', getEndTime)
            }
        }
    }
    page.init();
    window.page = page;

})