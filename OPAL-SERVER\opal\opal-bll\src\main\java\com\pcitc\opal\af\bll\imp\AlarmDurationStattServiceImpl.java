package com.pcitc.opal.af.bll.imp;

import com.pcitc.opal.aa.bll.entity.AlarmDurationStattEntity;
import com.pcitc.opal.aa.bll.entity.AlarmDurationStattCommonEntity;
import com.pcitc.opal.ad.bll.entity.AlarmRecEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.AlarmRecRepository;
import com.pcitc.opal.ad.dao.imp.AlarmDurationDtlEntityVO;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.af.bll.AlarmDurationStattService;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.math.BigDecimal;

import java.util.*;

@Service
public class AlarmDurationStattServiceImpl implements AlarmDurationStattService {

    private final static Log logger = LogFactory.getLog(AlarmDurationStattServiceImpl.class);

    @Autowired
    private BasicDataService basicDataService;
    @Autowired
    private AlarmEventRepository alarmEventRepository;
    @Autowired
    private AlarmRecRepository alarmRecRepository;


    /**
     * 报警时长统计
     *
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param priority
     * @param endFlag
     * @return
     * @throws Exception
     */
    @Override
    public List<AlarmRecEntity> getAlarmDurationStatt(String[] unitIds, Date startTime, Date endTime,
                                                      Integer[] priority, Boolean priorityFlag, String tag,
                                                      Long alarmFlagId, String endFlag, Integer isElimination)
            throws Exception {
        List<UnitEntity> units = null;
        if (unitIds == null) {
            units = basicDataService.getUnitListByIds(unitIds, true);
            unitIds = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        List<AlarmRec> alarmRecPaginationBean = alarmRecRepository.getAlarmDurationStatt(unitIds, priority, startTime,
                endTime, priorityFlag, tag, alarmFlagId, isElimination);

        if (units == null && alarmRecPaginationBean != null) {
            // 通过公共方法获取装置
            String[] filterunitCodes = null;
            if (alarmRecPaginationBean.size() > 1) {
                List<AlarmRec> alarmRecs = alarmRecPaginationBean.subList(0, alarmRecPaginationBean.size() - 1);
                filterunitCodes = alarmRecs.stream().map(x -> x.getUnitCode()).distinct().toArray(String[]::new);
            }
            units = basicDataService.getUnitListByIds(filterunitCodes, false);
        }
        List<AlarmRecEntity> returnAlarmRec = ObjectConverter.listConverter(alarmRecPaginationBean,
                AlarmRecEntity.class);

        for (int i = 0; i < returnAlarmRec.size(); i++) {
            AlarmRecEntity alarmRecEntity = returnAlarmRec.get(i);
            AlarmRec alarmRec = alarmRecPaginationBean.get(i);
            long rtime = 0;
            if (alarmRec.getRecoveryTime() != null) {
                rtime = alarmRec.getRecoveryTime().getTime();
            } else {
                rtime = endTime.getTime();
            }
            long atime = alarmRec.getAlarmTime().getTime();
            double v = Double.valueOf(Math.abs(rtime - atime)) / (60 * 1000);

            String s = String.valueOf(v);
            alarmRecEntity.setContinuousHour((double) Math.round(v * 100) / 100);
            //填充事件类型
            alarmRecEntity.setEventTypeName(alarmRec.getEventType().getName());
            //填充装置
            UnitEntity unit = units.stream().filter(u -> alarmRec.getPrdtCell().getUnitId().equals(u.getStdCode()))
                    .findFirst().orElse(new UnitEntity());
            alarmRecEntity.setUnitName(unit.getSname());
            //填充生产单元
            alarmRecEntity.setPrdtCellName(alarmRec.getPrdtCell().getSname());
            //填充位号
            alarmRecEntity.setTag(
                    null != alarmRec.getAlarmPointId() ? alarmRec.getAlarmPoint().getTag() : alarmRec.getTag());
            //填充报警标识
            alarmRecEntity.setAlarmFlagName(
                    null != alarmRec.getAlarmFlagId() ? alarmRec.getAlarmFlag().getName() : null);
            //填充优先级
            alarmRecEntity.setPriorityName(CommonEnum.AlarmPriorityEnum.getName(alarmRec.getPriority()));

            alarmRecEntity.setMonitorTypeStr(
                    CommonEnum.MonitorTypeEnum.getName(alarmRec.getAlarmPoint().getMonitorType()));
        }


        return returnAlarmRec;
    }

    @Override
    public PaginationBean<AlarmRecEntity> getAlarmDurationStattPage(Pagination page, String[] unitIds, Date startTime,
                                                                    Date endTime, Integer[] priority,
                                                                    Boolean priorityFlag, String tag,
                                                                    Long[] alarmFlagId,
                                                                    String endFlag, Integer isElimination)
            throws Exception {
        List<UnitEntity> units = null;
        if (unitIds == null) {
            units = basicDataService.getUnitListByIds(unitIds, true);
            unitIds = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        PaginationBean<AlarmRec> paginationBean = alarmRecRepository.getAlarmDurationStatt(page,unitIds, priority, startTime,
                endTime, priorityFlag, tag, alarmFlagId, isElimination);

        List<AlarmRec> alarmRecPaginationBean = paginationBean.getPageList();

        if (units == null && alarmRecPaginationBean != null) {
            // 通过公共方法获取装置
            String[] filterunitCodes = null;
            if (alarmRecPaginationBean.size() > 1) {
                List<AlarmRec> alarmRecs = alarmRecPaginationBean.subList(0, alarmRecPaginationBean.size() - 1);
                filterunitCodes = alarmRecs.stream().map(x -> x.getUnitCode()).distinct().toArray(String[]::new);
            }
            units = basicDataService.getUnitListByIds(filterunitCodes, false);
        }
        List<AlarmRecEntity> returnAlarmRec = ObjectConverter.listConverter(alarmRecPaginationBean,
                AlarmRecEntity.class);
        PaginationBean<AlarmRecEntity> returnAlarmRecPage = new PaginationBean<AlarmRecEntity>(page, paginationBean.getTotal());

        for (int i = 0; i < returnAlarmRec.size(); i++) {
            AlarmRecEntity alarmRecEntity = returnAlarmRec.get(i);
            AlarmRec alarmRec = alarmRecPaginationBean.get(i);
            long rtime = 0;
            if (alarmRec.getRecoveryTime() != null) {
                rtime = alarmRec.getRecoveryTime().getTime();
            } else {
                rtime = endTime.getTime();
            }
            long atime = alarmRec.getAlarmTime().getTime();
            double v = Double.valueOf(Math.abs(rtime - atime)) / (60 * 1000);

            String s = String.valueOf(v);
            alarmRecEntity.setContinuousHour((double) Math.round(v * 100) / 100);
            //填充事件类型
            alarmRecEntity.setEventTypeName(alarmRec.getEventType().getName());
            //填充装置
            UnitEntity unit = units.stream().filter(u -> alarmRec.getPrdtCell().getUnitId().equals(u.getStdCode()))
                    .findFirst().orElse(new UnitEntity());
            alarmRecEntity.setUnitName(unit.getSname());
            //填充生产单元
            alarmRecEntity.setPrdtCellName(alarmRec.getPrdtCell().getSname());
            //填充位号
            alarmRecEntity.setTag(
                    null != alarmRec.getAlarmPointId() ? alarmRec.getAlarmPoint().getTag() : alarmRec.getTag());
            //填充报警标识
            alarmRecEntity.setAlarmFlagName(
                    null != alarmRec.getAlarmFlagId() ? alarmRec.getAlarmFlag().getName() : null);
            //填充优先级
            alarmRecEntity.setPriorityName(CommonEnum.AlarmPriorityEnum.getName(alarmRec.getPriority()));

            alarmRecEntity.setMonitorTypeStr(
                    CommonEnum.MonitorTypeEnum.getName(alarmRec.getAlarmPoint().getMonitorType()));
            alarmRecEntity.setLocation(alarmRec.getAlarmPoint().getLocation());
            returnAlarmRecPage.getPageList().add(alarmRecEntity);
        }
        return returnAlarmRecPage;
    }

    @Override
    public PaginationBean<AlarmRecEntity> getAlarmDurationStattMain(String[] unitIds, Long[] alarmFlagIds,
                                                                    Date startTime, Date endTime, Integer[] priority,
                                                                    Boolean priorityFlag, String endFlag,
                                                                    Pagination page, Integer isElimination)
            throws Exception {
        List<UnitEntity> units = null;
        if (unitIds == null) {
            units = basicDataService.getUnitListByIds(unitIds, true);
            unitIds = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        //获取查询开始和结束时间
//        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime, endFlag);
//        startTime = shiftDateCalculator.getQueryStartTime();
//        endTime = shiftDateCalculator.getQueryEndTime();

        List<Object[]> alarmRecPaginationBean = alarmRecRepository.getAlarmDurationStattMain(unitIds, alarmFlagIds,
                priority, startTime, endTime, priorityFlag, page, isElimination);

        if (units == null && alarmRecPaginationBean != null) {
            // 通过公共方法获取装置
            String[] filterunitCodes = null;
            if (alarmRecPaginationBean.size() > 1) {
                List<Object[]> alarmRecs = alarmRecPaginationBean.subList(0, alarmRecPaginationBean.size() - 1);
                filterunitCodes = alarmRecs.stream().map(x -> x[0].toString()).distinct().toArray(String[]::new);
            }
            units = basicDataService.getUnitListByIds(filterunitCodes, false);
        }

        Object total = alarmRecRepository.getTotalAlarmDurationStatt(unitIds, alarmFlagIds, priority, startTime,
                endTime, priorityFlag);
        Long aLong = Long.valueOf(total.toString());

        PaginationBean<AlarmRecEntity> returnAlarmRec = new PaginationBean<AlarmRecEntity>(page, aLong);


        for (int i = 0; i < alarmRecPaginationBean.size(); i++) {
            //AlarmRecEntity alarmRecEntity = returnAlarmRec.getPageList().get(i);
            Object[] objects = alarmRecPaginationBean.get(i);
            AlarmRecEntity alarmRecEntity = new AlarmRecEntity();

            String unitCode = objects[0].toString();
            UnitEntity unit = units.stream().filter(u -> unitCode.equals(u.getStdCode())).findFirst()
                    .orElse(new UnitEntity());
            alarmRecEntity.setUnitName(unit.getSname());
            Long prdtcellId = Long.valueOf(objects[1].toString());
            String prdtcellName = objects[2].toString();
            String tag = objects[3].toString();
            String des = objects[4].toString();
            Long alarmFlagId = objects[5] == null ? null : Long.valueOf(objects[5].toString());
            String alarmFlagName = objects[6] == null ? null : objects[6].toString();
            //Double continuousHour = Double.valueOf(objects[7].toString())*24*60;
            Double continuousHour = Double.valueOf(objects[7].toString());
            Integer priority1 = Integer.valueOf(objects[8].toString());
            alarmRecEntity.setUnitCode(unitCode);
            alarmRecEntity.setPrdtCellId(prdtcellId);
            alarmRecEntity.setPrdtCellName(prdtcellName);
            alarmRecEntity.setTag(tag);
            alarmRecEntity.setDes(des);
            alarmRecEntity.setAlarmFlagId(alarmFlagId);
            alarmRecEntity.setAlarmFlagName(alarmFlagName);
            alarmRecEntity.setContinuousHour((double) Math.round(continuousHour * 100) / 100);
            alarmRecEntity.setPriority(priority1);
            alarmRecEntity.setPriorityName(CommonEnum.AlarmPriorityEnum.getName(priority1));
            alarmRecEntity.setMonitorTypeStr(
                    CommonEnum.MonitorTypeEnum.getName(Integer.valueOf(objects[9].toString())));
            alarmRecEntity.setLocation(objects[10].toString());
            returnAlarmRec.getPageList().add(alarmRecEntity);


        }

        return returnAlarmRec;
    }

    @Override
    public PaginationBean<AlarmRecEntity> getAlarmDurationStattMainCommon(String[] unitCodes, String[] workshopCodes,
                                                                          Long[] prdtcellIds, String[] unitIds,
                                                                          Long[] alarmFlagIds, Date startTime,
                                                                          Date endTime, Integer[] priority,
                                                                          Boolean priorityFlag, String endFlag,
                                                                          Pagination page, Integer isElimination,String tag)
            throws Exception {
        List<UnitEntity> units = null;
        if (unitIds == null) {
            units = basicDataService.getUnitListByIds(unitIds, true);
            unitIds = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        PaginationBean<AlarmRec> paginationBean = alarmRecRepository.getAlarmDurationStatt(page,unitIds, priority, startTime,
                endTime, priorityFlag, tag, alarmFlagIds, isElimination);

        List<AlarmRec> alarmRecPaginationBean = paginationBean.getPageList();

        if (units == null && alarmRecPaginationBean != null) {
            // 通过公共方法获取装置
            String[] filterunitCodes = null;
            if (alarmRecPaginationBean.size() > 1) {
                List<AlarmRec> alarmRecs = alarmRecPaginationBean.subList(0, alarmRecPaginationBean.size() - 1);
                filterunitCodes = alarmRecs.stream().map(x -> x.getUnitCode()).distinct().toArray(String[]::new);
            }
            units = basicDataService.getUnitListByIds(filterunitCodes, false);
        }
        List<AlarmRecEntity> returnAlarmRec = ObjectConverter.listConverter(alarmRecPaginationBean,
                AlarmRecEntity.class);
        PaginationBean<AlarmRecEntity> returnAlarmRecPage = new PaginationBean<AlarmRecEntity>(page, paginationBean.getTotal());

        for (int i = 0; i < returnAlarmRec.size(); i++) {
            AlarmRecEntity alarmRecEntity = returnAlarmRec.get(i);
            AlarmRec alarmRec = alarmRecPaginationBean.get(i);
            long rtime = 0;
            if (alarmRec.getRecoveryTime() != null) {
                rtime = alarmRec.getRecoveryTime().getTime();
            } else {
                rtime = endTime.getTime();
            }
            long atime = alarmRec.getAlarmTime().getTime();
            double v = Double.valueOf(Math.abs(rtime - atime)) / (60 * 1000);

            String s = String.valueOf(v);
            alarmRecEntity.setContinuousHour((double) Math.round(v * 100) / 100);
            //填充事件类型
            alarmRecEntity.setEventTypeName(alarmRec.getEventType().getName());
            //填充装置
            UnitEntity unit = units.stream().filter(u -> alarmRec.getPrdtCell().getUnitId().equals(u.getStdCode()))
                    .findFirst().orElse(new UnitEntity());
            alarmRecEntity.setUnitName(unit.getSname());
            //填充生产单元
            alarmRecEntity.setPrdtCellName(alarmRec.getPrdtCell().getSname());
            //填充位号
            alarmRecEntity.setTag(
                    null != alarmRec.getAlarmPointId() ? alarmRec.getAlarmPoint().getTag() : alarmRec.getTag());
            //填充报警标识
            alarmRecEntity.setAlarmFlagName(
                    null != alarmRec.getAlarmFlagId() ? alarmRec.getAlarmFlag().getName() : null);
            //填充优先级
            alarmRecEntity.setPriorityName(CommonEnum.AlarmPriorityEnum.getName(alarmRec.getPriority()));

            alarmRecEntity.setMonitorTypeStr(
                    CommonEnum.MonitorTypeEnum.getName(alarmRec.getAlarmPoint().getMonitorType()));
            alarmRecEntity.setLocation(alarmRec.getAlarmPoint().getLocation());
            returnAlarmRecPage.getPageList().add(alarmRecEntity);
        }
        return returnAlarmRecPage;
    }

    private String getDuration(String s) {
        //拼接时长（分钟）
        String continuousHourMin = s.substring(0, s.indexOf("."));
        //拼接时长（秒）
        String continuousHourSec = "";

        String substring = s.substring(s.indexOf("."));
        if (StringUtils.isNotEmpty(substring)) {
            substring = "0" + substring;
            Double aDouble = Double.valueOf(substring);
            double v1 = aDouble * 60;
            double v2 = (double) Math.round(v1 * 100) / 100;
            String sec = String.valueOf(v2);
            sec = sec.substring(0, sec.indexOf("."));
            continuousHourSec = ":" + sec;
            continuousHourMin += continuousHourSec;
        }
        return continuousHourMin;
    }

    /**
     * 获取报警时长统计总条数
     *
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param priority
     * @param endFlag
     * @return
     * @throws Exception
     */
    public Long getTotal(String[] unitIds, Date startTime, Date endTime, Integer[] priority, Boolean priorityFlag,
                         String endFlag) throws Exception {
        List<UnitEntity> units = null;
        if (unitIds == null) {
            units = basicDataService.getUnitListByIds(unitIds, true);
            unitIds = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        //获取查询开始和结束时间
//        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime, endFlag);
//        startTime = shiftDateCalculator.getQueryStartTime();
//        endTime = shiftDateCalculator.getQueryEndTime();

        Long[] eventTypeIds = {1001L, 1002L, 1003L, 1005L};
        Object statisticTotal = alarmEventRepository.getAlarmDurationStattSum(unitIds, priority, eventTypeIds,
                startTime, endTime, priorityFlag);
        Long total = Long.valueOf(statisticTotal.toString());
        return total;
    }

    /**
     * 报警时长统计--各个优先级报警时长统计
     *
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param priority
     * @return
     */
    @Override
    public List<AlarmDurationStattEntity> getAlarmDurationStattTotal(String[] unitIds, Long[] alarmFlagId,
                                                                     Date startTime, Date endTime, Integer[] priority,
                                                                     Boolean priorityFlag, Integer isElimination)
            throws Exception {

        List<UnitEntity> units = null;
        if (unitIds == null) {
            units = basicDataService.getUnitListByIds(unitIds, true);
            unitIds = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        //获取查询开始和结束时间
//        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime, null);
//        startTime = shiftDateCalculator.getQueryStartTime();
//        endTime = shiftDateCalculator.getQueryEndTime();


        List<Object[]> alarmRecPaginationBean = alarmRecRepository.getAlarmDurationStattTotal(unitIds, alarmFlagId,
                priority, startTime, endTime, priorityFlag, isElimination);


        List<AlarmDurationStattEntity> alarmDurationStattEntityList = new ArrayList<>();
        for (Object[] o : alarmRecPaginationBean) {
            String unitCode = o[0].toString();
            String unitName = o[1].toString();
            Double emergency = Double.valueOf(o[2].toString());
            Double important = Double.valueOf(o[3].toString());
            Double general = Double.valueOf(o[4].toString());
            Double nullAlarmQuantity = Double.valueOf(o[5].toString());
            Double total = Double.valueOf(o[6].toString());
            AlarmDurationStattEntity alarmDurationStattEntity = new AlarmDurationStattEntity();
            alarmDurationStattEntity.setUnitId(unitCode);
            alarmDurationStattEntity.setSname(unitName);
            //紧急
            String s = new BigDecimal(emergency).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
//            String duration = getDuration(s);
//            EchartsShowEntity e = new EchartsShowEntity();
//            e.setValue(s);
//            e.setShow(duration);
//            alarmDurationStattEntity.setEmergencyAlarmQuantityEntity(e);
//            alarmDurationStattEntity.setEmergencyAlarmQuantity(duration);
            alarmDurationStattEntity.setEmergencyAlarmQuantity(s);
            //重要
            String s1 = new BigDecimal(important).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
//            String duration1 = getDuration(s1);
//            EchartsShowEntity e1 = new EchartsShowEntity();
//            e1.setValue(s1);
//            e1.setShow(duration1);
//            alarmDurationStattEntity.setImportantAlarmQuantityEntity(e1);
//            alarmDurationStattEntity.setImportantAlarmQuantity(duration1);
            alarmDurationStattEntity.setImportantAlarmQuantity(s1);
            //一般
            String s2 = new BigDecimal(general).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
//            String duration2 = getDuration(s2);
//            EchartsShowEntity e2 = new EchartsShowEntity();
//            e2.setValue(s2);
//            e2.setShow(duration2);
//            alarmDurationStattEntity.setGeneralAlarmQuantityEntity(e2);
//            alarmDurationStattEntity.setGeneralAlarmQuantity(duration2);
            alarmDurationStattEntity.setGeneralAlarmQuantity(s2);
            //空
            String s3 = new BigDecimal(nullAlarmQuantity).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
//            String duration3 = getDuration(s3);
//            EchartsShowEntity e3 = new EchartsShowEntity();
//            e3.setValue(s3);
//            e3.setShow(duration3);
//            alarmDurationStattEntity.setNullAlarmQuantityEntity(e3);
//            alarmDurationStattEntity.setNullAlarmQuantity(duration3);
            alarmDurationStattEntity.setNullAlarmQuantity(s3);
            //总数
            String s4 = new BigDecimal(total).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            String duration4 = getDuration(s4);
            alarmDurationStattEntity.setTotalAlarmQuantity(duration4);
            alarmDurationStattEntityList.add(alarmDurationStattEntity);
        }

        return alarmDurationStattEntityList;
    }

    @Override
    public List<AlarmDurationStattCommonEntity> getAlarmDurationStattTotalWorkshop(String[] unitIds, Long[] alarmFlagId,
                                                                                   Date startTime, Date endTime,
                                                                                   Integer[] priority,
                                                                                   Boolean priorityFlag,
                                                                                   Integer isElimination)
            throws Exception {

        List<UnitEntity> units = null;
        if (unitIds == null) {
            units = basicDataService.getUnitListByIds(unitIds, true);
            unitIds = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        //获取查询开始和结束时间
//        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime, null);
//        startTime = shiftDateCalculator.getQueryStartTime();
//        endTime = shiftDateCalculator.getQueryEndTime();


        List<Object[]> alarmRecPaginationBean = alarmRecRepository.getAlarmDurationStattTotalWorkshop(unitIds,
                alarmFlagId, priority, startTime, endTime, priorityFlag, isElimination);


        List<AlarmDurationStattCommonEntity> alarmDurationStattWorkshopEntityList = new ArrayList<>();
        for (Object[] o : alarmRecPaginationBean) {
            String code = o[0].toString();
            String name = o[1].toString();
            Double emergency = Double.valueOf(o[2].toString());
            Double important = Double.valueOf(o[3].toString());
            Double general = Double.valueOf(o[4].toString());
            Double nullAlarmQuantity = Double.valueOf(o[5].toString());
            Double total = Double.valueOf(o[6].toString());
            AlarmDurationStattCommonEntity alarmDurationStattWorkshopEntity = new AlarmDurationStattCommonEntity();
            alarmDurationStattWorkshopEntity.setCode(code);
            alarmDurationStattWorkshopEntity.setName(name);
            //紧急
            String s = new BigDecimal(emergency).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            alarmDurationStattWorkshopEntity.setEmergencyAlarmQuantity(s);
            //重要
            String s1 = new BigDecimal(important).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            alarmDurationStattWorkshopEntity.setImportantAlarmQuantity(s1);
            //一般
            String s2 = new BigDecimal(general).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            alarmDurationStattWorkshopEntity.setGeneralAlarmQuantity(s2);
            //空
            String s3 = new BigDecimal(nullAlarmQuantity).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            alarmDurationStattWorkshopEntity.setNullAlarmQuantity(s3);
            //总数
            String s4 = new BigDecimal(total).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            String duration4 = getDuration(s4);
            alarmDurationStattWorkshopEntity.setTotalAlarmQuantity(duration4);
            alarmDurationStattWorkshopEntityList.add(alarmDurationStattWorkshopEntity);
        }

        return alarmDurationStattWorkshopEntityList;
    }

    @Override
    public List<AlarmDurationStattCommonEntity> getAlarmDurationStattTotalUnit(String[] workshopCodes, String[] unitIds,
                                                                               Long[] alarmFlagId, Date startTime,
                                                                               Date endTime, Integer[] priority,
                                                                               Boolean priorityFlag,
                                                                               Integer isElimination) throws Exception {

        List<UnitEntity> units = null;
        if (unitIds == null) {
            units = basicDataService.getUnitListByIds(unitIds, true);
            unitIds = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        //获取查询开始和结束时间
//        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime, null);
//        startTime = shiftDateCalculator.getQueryStartTime();
//        endTime = shiftDateCalculator.getQueryEndTime();


        List<Object[]> alarmRecPaginationBean = alarmRecRepository.getAlarmDurationStattTotalunit(workshopCodes,
                unitIds, alarmFlagId, priority, startTime, endTime, priorityFlag, isElimination);


        List<AlarmDurationStattCommonEntity> alarmDurationStattWorkshopEntityList = new ArrayList<>();
        for (Object[] o : alarmRecPaginationBean) {
            String code = o[0].toString();
            String name = o[1].toString();
            Double emergency = Double.valueOf(o[2].toString());
            Double important = Double.valueOf(o[3].toString());
            Double general = Double.valueOf(o[4].toString());
            Double nullAlarmQuantity = Double.valueOf(o[5].toString());
            Double total = Double.valueOf(o[6].toString());
            AlarmDurationStattCommonEntity alarmDurationStattWorkshopEntity = new AlarmDurationStattCommonEntity();
            alarmDurationStattWorkshopEntity.setCode(code);
            alarmDurationStattWorkshopEntity.setName(name);
            //紧急
            String s = new BigDecimal(emergency).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            alarmDurationStattWorkshopEntity.setEmergencyAlarmQuantity(s);
            //重要
            String s1 = new BigDecimal(important).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            alarmDurationStattWorkshopEntity.setImportantAlarmQuantity(s1);
            //一般
            String s2 = new BigDecimal(general).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            alarmDurationStattWorkshopEntity.setGeneralAlarmQuantity(s2);
            //空
            String s3 = new BigDecimal(nullAlarmQuantity).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            alarmDurationStattWorkshopEntity.setNullAlarmQuantity(s3);
            //总数
            String s4 = new BigDecimal(total).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            String duration4 = getDuration(s4);
            alarmDurationStattWorkshopEntity.setTotalAlarmQuantity(duration4);
            alarmDurationStattWorkshopEntityList.add(alarmDurationStattWorkshopEntity);
        }

        return alarmDurationStattWorkshopEntityList;
    }

    @Override
    public List<AlarmDurationStattCommonEntity> getAlarmDurationStattTotalPrdtcell(String[] unitCodes, String[] unitIds,
                                                                                   Long[] alarmFlagId, Date startTime,
                                                                                   Date endTime, Integer[] priority,
                                                                                   Boolean priorityFlag,
                                                                                   Integer isElimination)
            throws Exception {

        List<UnitEntity> units = null;
        if (unitIds == null) {
            units = basicDataService.getUnitListByIds(unitIds, true);
            unitIds = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        //获取查询开始和结束时间
//        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime, null);
//        startTime = shiftDateCalculator.getQueryStartTime();
//        endTime = shiftDateCalculator.getQueryEndTime();


        List<Object[]> alarmRecPaginationBean = alarmRecRepository.getAlarmDurationStattTotalPrdtcell(unitCodes,
                unitIds, alarmFlagId, priority, startTime, endTime, priorityFlag, isElimination);


        List<AlarmDurationStattCommonEntity> alarmDurationStattWorkshopEntityList = new ArrayList<>();
        for (Object[] o : alarmRecPaginationBean) {
            String code = o[0].toString();
            String name = o[1].toString();
            Double emergency = Double.valueOf(o[2].toString());
            Double important = Double.valueOf(o[3].toString());
            Double general = Double.valueOf(o[4].toString());
            Double nullAlarmQuantity = Double.valueOf(o[5].toString());
            Double total = Double.valueOf(o[6].toString());
            AlarmDurationStattCommonEntity alarmDurationStattWorkshopEntity = new AlarmDurationStattCommonEntity();
            alarmDurationStattWorkshopEntity.setCode(code);
            alarmDurationStattWorkshopEntity.setName(name);
            //紧急
            String s = new BigDecimal(emergency).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            alarmDurationStattWorkshopEntity.setEmergencyAlarmQuantity(s);
            //重要
            String s1 = new BigDecimal(important).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            alarmDurationStattWorkshopEntity.setImportantAlarmQuantity(s1);
            //一般
            String s2 = new BigDecimal(general).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            alarmDurationStattWorkshopEntity.setGeneralAlarmQuantity(s2);
            //空
            String s3 = new BigDecimal(nullAlarmQuantity).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            alarmDurationStattWorkshopEntity.setNullAlarmQuantity(s3);
            //总数
            String s4 = new BigDecimal(total).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            String duration4 = getDuration(s4);
            alarmDurationStattWorkshopEntity.setTotalAlarmQuantity(duration4);
            alarmDurationStattWorkshopEntityList.add(alarmDurationStattWorkshopEntity);
        }

        return alarmDurationStattWorkshopEntityList;
    }

    @Override
    public List<DictionaryEntity> getAlarmPriorityList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            if (isAll) {
                dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全选"));
            }
            for (CommonEnum.AlarmPriorityEnum alarmPriority : CommonEnum.AlarmPriorityEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(alarmPriority.getIndex(), alarmPriority.getName()));
            }
            dictionaryEntityArrayList.add(new DictionaryEntity(9, "空"));
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    @Override
    public PaginationBean<AlarmDurationDtlEntityVO> getAlarmDurationDtl(Date startTime, Date endTime, String unitCode,
                                                                        Integer priority, Long[] alarmFlagIds,
                                                                        Pagination page) throws Exception {

        //获取查询开始和结束时间
//        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime, null);
//        startTime = shiftDateCalculator.getQueryStartTime();
//        endTime = shiftDateCalculator.getQueryEndTime();
        return alarmRecRepository.getAlarmNumStattDtl(startTime, endTime, unitCode, priority, alarmFlagIds, page);
    }
}
