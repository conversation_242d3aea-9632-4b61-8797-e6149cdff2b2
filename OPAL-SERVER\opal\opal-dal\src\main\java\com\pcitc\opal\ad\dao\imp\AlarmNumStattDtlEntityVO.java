package com.pcitc.opal.ad.dao.imp;

import java.util.Date;

/*
 * 报警次数统计统计数据实体
 * 作  　  者：shufei.sui
 * 创建时间：2019/09/26
 * 修改编号：1
 * 描       述：报警次数统计统计数据实体
 */
public class AlarmNumStattDtlEntityVO {
    public AlarmNumStattDtlEntityVO(Date alarmTime, String cellSname, String tag, String des, String alarmFlagName, String nowValue) {
        this.alarmTime = alarmTime;
        this.cellSname = cellSname;
        this.tag = tag;
        this.des = des;
        this.alarmFlagName = alarmFlagName;
        this.nowValue = nowValue;
    }

    /**
     * 报警事件
     */
    private Date alarmTime;
    /**
     * 生产单元简称
     */
    private String cellSname;
    /**
     * 位号
     */
    private String tag;
    /**
     * 描述
     */
    private String des;
    /**
     * 报警标识名称
     */
    private String alarmFlagName;
    /**
     * 当前值
     */
    private String nowValue;

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public String getCellSname() {
        return cellSname;
    }

    public void setCellSname(String cellSname) {
        this.cellSname = cellSname;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

    public String getNowValue() {
        return nowValue;
    }

    public void setNowValue(String nowValue) {
        this.nowValue = nowValue;
    }
}
