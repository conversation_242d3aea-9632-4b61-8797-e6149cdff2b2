package com.pcitc.opal.ad.bll.imp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.Version;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.pcitc.opal.ad.bll.AlarmEventCacheService;
import com.pcitc.opal.ad.dao.AlarmEventCacheRepository;
import com.pcitc.opal.ad.pojo.AlarmEventCache;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.CustomJsonDateDeserializer;
import net.sf.json.JsonConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;
import java.util.regex.Pattern;

/*
 * 报警事件缓存业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmEventCacheImpl
 * 作       者：zheng.yang
 * 创建时间：2019/03/14
 * 修改编号：1
 * 描       述：报警事件业务逻辑层实现类
 */
@Service
public class AlarmEventCacheImpl implements AlarmEventCacheService {

    private final static Log logger = LogFactory.getLog(AlarmEventCacheImpl.class);

    @Autowired
    private AlarmEventCacheRepository alarmEventCacheRepository;

    @Override
    public CommonResult addAlarmEventCache(String data) throws Exception {
//        data = "[{\"alarmTime\":\"2018-01-01 01:00:00\",\"startTime\":\"2018-01-01 00:00:00\",\"stateFlag\":\"1\",\"writeTime\":\"2018-01-01 00:00:00\",\"dcsCode\":\"11\",\"eventType\":\"北京市西城区\",\"limitValue\":25},{\"dcsCode\":\"11\",\"eventType\":\"北京市西城区\",\"abc\":25}]";

        CommonResult commonResult = new CommonResult();
        if(StringUtils.isEmpty(data)){
            commonResult.setIsSuccess(false);
            commonResult.setMessage("报警数据为空！");
            logger.error("报警数据为空！");
            return commonResult;
        }
        //需要持久化的数据集合
        List<AlarmEventCache> alarmEventCacheList = new ArrayList<>();

        if (StringUtils.isNotEmpty(data)) {
            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, true);
            CustomJsonDateDeserializer deserializer = new CustomJsonDateDeserializer();
            SimpleModule module =
                    new SimpleModule("CustomJsonDateDeserializer",
                            new Version(1, 0, 0, null, null, null));
            module.addDeserializer(Date.class, deserializer);
            mapper.registerModule(module);

            JSONArray jsonArray = null;
            int failNum = 0; //实体转换失败数
            StringJoiner failString = new StringJoiner(","); //转换失败实体
            try {
                //将json数组拆分成json对象  便于定位哪条数据不能转换成AlarmEventCache实体
                jsonArray = JSONArray.parseArray(data);
            } catch (Exception e) {
                commonResult.setIsSuccess(false);
                commonResult.setMessage("数据格式有误！");
                logger.error("数据格式有误！");
                return commonResult;
            }
            if(jsonArray != null){
                for(int i=0;i<jsonArray.size();i++){
                        JSONObject jsonObject = (JSONObject) jsonArray.get(i);
                        String objString = jsonObject.toJSONString();
                    try {
                        AlarmEventCache alarmEventCache = mapper.readValue(objString, new TypeReference<AlarmEventCache>() {});
                        boolean flag = validateEntity(alarmEventCache);
                        if(flag) {
                            alarmEventCache.setEventId(null);
                            alarmEventCacheList.add(alarmEventCache);
                        } else {
                            failNum++;
                            failString.add(objString);
                            commonResult.setIsSuccess(false);
                        }

                    } catch (IOException e) {
                        failNum++;
                        failString.add(objString);
                        commonResult.setIsSuccess(false);
                        logger.error("转换对象格式错误"+objString);
                    }

                }
            }

            if(alarmEventCacheList.size()>0){
                alarmEventCacheRepository.addAlarmEventCache(alarmEventCacheList);
            }
            if(failNum==0){
                failString.add("无");
            }
            commonResult.setMessage(String.format("传入数据%s条，数据转换失败%s条。转换失败数据：%s",jsonArray.size(),failNum,failString.toString()));
            logger.info(String.format("传入数据%s条，数据转换失败%s条。",jsonArray.size(),failNum));
        }
        return commonResult;
    }

    private boolean validateEntity(AlarmEventCache po) {
        //非空字段校验
        if(po.getWriteTime() == null || po.getAlarmTime() == null
                || po.getStartTime() == null || po.getDcsCode() == null
                || po.getStateFlag() == null){
            return false;
        } else {
            int MAX_LENGTH = 200;
            if(po.getEventType()!=null && po.getEventType().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getEventName()!=null && po.getEventName().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getPrdtCell()!=null && po.getPrdtCell().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getAlarmPoint()!=null && po.getAlarmPoint().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getAlarmFlag()!=null && po.getAlarmFlag().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getPriority()!=null && po.getPriority().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getPreviousValue()!=null && po.getPreviousValue().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getNowValue()!=null && po.getNowValue().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getMeasUnit()!=null && po.getMeasUnit().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getOperator()!=null && po.getOperator().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getParameter()!=null && po.getParameter().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getAction()!=null && po.getAction().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getActive()!=null && po.getActive().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getAcked()!=null && po.getAcked().length()>MAX_LENGTH) {
                return false;
            }
            if(po.getDes()!=null && po.getDes().length()>2000) {
                return false;
            }
        }
        return true;
    }

}
