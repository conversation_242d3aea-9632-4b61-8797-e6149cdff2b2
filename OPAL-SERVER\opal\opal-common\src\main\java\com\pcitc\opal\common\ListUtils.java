package com.pcitc.opal.common;

import java.util.ArrayList;
import java.util.List;

/**
 * @USER: chenbo
 * @DATE: 2023/3/14
 * @TIME: 16:26
 * @DESC: 集合工具类
 **/
public class ListUtils {

    /**
     * list按照指定大小分割
     * @param source 要分割的集合
     * @param splitNum 分割大小
     * @return 分割后的集合
     */
    public static <T> List<List<T>> splitList(List<T> source, int splitNum){

        List<List<T>> result = new ArrayList<List<T>>();
        if (source.isEmpty()){
            return result;
        }
        //循环下标
        int i = 0;
        //集合总数 / 分割每段数
        for (; i < source.size() / splitNum; i++) {
            //splitNum代表每段分割的子集合数据条数
            result.add(source.subList(i * splitNum, (i + 1) * splitNum));
        }
        //如果有余数，再将剩下的追加进去
        if (source.size() % splitNum != 0) {
            result.add(source.subList(i * splitNum, i * splitNum + source.size() % splitNum));
        }
        return result;
    }
}
