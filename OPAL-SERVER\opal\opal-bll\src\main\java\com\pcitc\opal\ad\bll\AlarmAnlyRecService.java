package com.pcitc.opal.ad.bll;

import com.pcitc.opal.ad.bll.bo.AlarmAnlyRecExportRequestBO;
import com.pcitc.opal.ad.bll.bo.AlarmAnlyRecExportResponseBO;
import com.pcitc.opal.ad.dao.imp.AlarmAnlyRecVO;
import com.pcitc.opal.ad.dao.vo.AlarmRecAnlyVO;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import org.springframework.stereotype.Service;

import java.rmi.RemoteException;
import java.util.Date;
import java.util.List;

@Service
public interface AlarmAnlyRecService {

    PaginationBean<AlarmRecAnlyVO> getAlarmAnlyRec(String[] unitIds,
                                                   Long[] prdtCellIds,
                                                   Long alarmFlagId,
                                                   String tag,
                                                   Integer alarmStatus,
                                                   Integer anlyStatus,
                                                   Integer[] prioritys,
                                                   Date startTime,
                                                   Date endTime,
                                                   Integer[] monitorType,
                                                   Pagination page) throws Exception;

    List<AlarmRecAnlyVO> getAlarmAnlyRec(String[] unitIds,
                                         Long[] prdtCellIds,
                                         Long alarmFlagId,
                                         String tag,
                                         Integer alarmStatus,
                                         Integer anlyStatus,
                                         Integer[] prioritys,
                                         Date startTime,
                                         Date endTime,
                                         Integer[] monitorType) throws Exception;


    List<AlarmAnlyRecVO> getAlarmAnlyByRec(Long alarmRecId) throws Exception;

    /**
     * 新增报警分析
     *
     * @param reasonType
     * @param des        描述
     */
    String addAlarmAnlyRec(Long alarmRecId, Long alarmAnlyRecId, Long reasonType, Long alarmReasonId, String des, Integer anlyStatus) throws Exception;

    String batchAddAnly(Long[] alarmRecIdList, Long reasonType, Long alarmReasonId, String des, Integer anlyStatus) throws Exception;

    /**
     * 更新报警分析记录
     *
     * @param alarmAnlyRecIds id
     * @param anlyStatus      分析类型
     */
    CommonResult updateAlarmAnlyRec(Long[] alarmAnlyRecIds, Integer anlyStatus) throws RemoteException, Exception;


    List<AlarmAnlyRecVO> getAnlyRecByEvent(List<Long> eventIds);

    /**
     * 分析记录导出

     */
    List<AlarmAnlyRecExportResponseBO> getAnalyseRecExport(List<String> unitIds,
                                                           List<Long> prdtCellIds,
                                                           Long alarmFlagId,
                                                           String tag,
                                                           Integer alarmStatus,
                                                           Integer anlyStatus,
                                                           List<Integer> prioritys,
                                                           Date startTime,
                                                           Date endTime,
                                                           List<Integer> monitorType);

}
