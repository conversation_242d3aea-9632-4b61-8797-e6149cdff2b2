$(function() {
    var getQueryTimeUrl = OPAL.API.commUrl + '/getQueryTime';
    var saveUrl = OPAL.API.acUrl + '/alarmChangePlanApply/saveAlarmChangePlanExtr';
    var confirmUrl = OPAL.API.acUrl + '/alarmChangePlanApply/addAlarmChangePlanExtr';
    var completeUrl = OPAL.API.acUrl + '/alarmChangePlanApply/completeApprove';
    var revertUrl = OPAL.API.acUrl + '/alarmChangePlanApply/revertApprove';
    var getSingleUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getSingleAlarmChangePlan';
    var planDetailUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getPlanDetail';
    var auditUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getAlarmChangePlanAproByPlanId';
    var issueUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getAlarmChangePlanLinkInfo';
    var getTimeUrl = OPAL.API.commUrl + '/getQueryStartAndEndDate';
    var pageMode;
    window.pageLoadMode = PageLoadMode.Refresh;
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var alarmChangeItemArr, rowData; //维护调整事项页面相关参数
    var page = {
        /**
         * 初始化
         */
        init: function() {
            this.bindUI();
            page.logic.getTime();
        },
        bindUI: function() {
            //表格自适应页面拉伸宽度
            $(window).resize(function() {
                $('#alarmChangeItemTable').bootstrapTable('resetView');
            });
            //关闭
            $('#closePage').click(function() {
                page.logic.closeLayer();
            })
            //保存
            $('#saveAddModal').click(function() {
                page.logic.save();
            });
            //下发 确认
            $('#submitAddModal').click(function() {
                page.logic.submitData();
            });
            //关闭
            $('.closeBtn').click(function() {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer();
            });
            //审批信息
            $('#aproBtn').click(function() {
                page.logic.AlarmChangePlanApro();
            })
            $("#passBtn").click(function() {
                page.logic.pass();
            })
            $("#rejectBtn").click(function() {
                page.logic.reject();
            })
        },
        data: {
            // 设置查询参数
            param: {}
        },
        logic: {
            initTable: function() {
                OPAL.ui.initBootstrapTable("alarmChangeItemTable", {
                    cache: false,
                    url: planDetailUrl,
                    columns: [{
                        title: "操作",
                        field: '',
                        rowspan: 1,
                        align: 'center',
                        formatter: page.logic.onActionRenderer,
                        width: '90px'
                    }, {
                        title: "序号",
                        field: 'unitSname',
                        formatter: function(value, row, index) {
                            var tableOption = $('#alarmChangeItemTable').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            return (index + 1) + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "生产单元",
                        field: 'prdtName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "计量单位",
                        field: 'measUnitName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "描述",
                        field: 'des',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }],
                }, page.logic.queryParams)
            },
            /**
             * 添加配置行按钮操作
             */
            onActionRenderer: function() {
                var rowData = arguments[1];
                return [
                    '<a name="TableDelete" href="javascript:window.page.logic.subDetail(' + rowData.planDetailId + ',' + rowData.planId + ',' + rowData.alarmPointId + ',' + rowData.alarmFlagId + ',\'' + rowData.alarmFlagName + '\')" >详情</a> '
                ]
            },
            /**
             * 查询参数
             */
            queryParams: function(p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    aproType:1
                };
                return $.extend(page.data.param, param);
            },
            setData: function(data) {
                $("#pageTitle").text(data.title);
                pageMode = data.pageMode;
                page.data.param = {
                    'planId': data.planId,
                    'businessType': data.pageMode,
                    'taskId': data.taskId
                }
                if (pageMode == 1) {
                    $("#issuedTable").hide();
                    $("#confirmTable").hide();
                    $("#saveAudit").hide();
                    $("#view2").hide();
                    $("#confirmBtn").html('下发');
                } else if (pageMode == 2) {
                    $("#issuingTable").hide();
                    $("#saveAudit").hide();
                    $("#confirmBtn").html('确认');
                    $("#view2").hide();
                } else if (pageMode == 3) {
                    $("#auditTable").hide();
                    $("#issuedTable").hide();
                    $("#confirmTable").hide();
                    $("#issuingTable").hide();
                    $("#view1").hide();
                }
                page.logic.initTable();
                page.logic.initAlarmChangeItem();
                page.logic.initAudit();
            },
            //调整事项列表
            initAlarmChangeItem: function() {
                $.ajax({
                    url: getSingleUrl + "/" + page.data.param.planId + "?now=" + Math.random(),
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function(data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        $.each(entity, function(i, val) {
                                $("#" + i).html(val);
                                $("#" + i).attr("title", val);
                        });
                        page.data.param.unitId = entity.unitId;
                        if (pageMode != 3) {
                            page.logic.initIssue();
                        }
                    },
                    error: function(XMLHttpRequest, textStatus) {}
                });
            },
            //审批信息
            initAudit: function() {
                $.ajax({
                    url: auditUrl + "?now=" + Math.random(),
                    data: page.data.param,
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function(data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        if (entity != undefined) {
                            $.each(entity, function(i, val) {
                                $("#" + i).html(val);
                                $("#" + i).attr("title", val);
                            });
                        }
                    },
                    error: function(XMLHttpRequest, textStatus) {}
                });
            },
            //下发/确认信息
            initIssue: function() {
                $.ajax({
                    url: issueUrl + "?now=" + Math.random(),
                    data: page.data.param,
                    type: "get",
                    async: false,
                    dataType: "json",
                    success: function(data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        if (entity != undefined) {
                            if (pageMode == 1) {
                                $("#opUserName2").html(entity.issueUser);
                                $("#opUserName2").attr("title", entity.issueUser);
                                var executeUserArr = JSON.parse(entity.executeUser);
                                if(executeUserArr.length != 0) {
                                    OPAL.ui.getComboboxByData("executeUserName2", executeUserArr, {
                                    selectFirstRecord: true
                                    }, null, null);
                                } 
                                $("#issueRemark2").val(entity.issueRemark);
                                $("#issueRemark2").attr("title", entity.issueRemark);
                            } else if (pageMode == 2) {
                                if (entity != undefined) {
                                    $.each(entity, function(i, val) {
                                        $("#" + i).html(val);
                                        $("#" + i).attr("title", val);
                                    });
                                    $(".removeTd").remove();
                                }
                            }
                        }
                    },
                    error: function(XMLHttpRequest, textStatus) {}
                });
            },
            //调整事项详情
            subDetail: function(planDetailId, planId, alarmPointId, alarmFlagId) {
                var pageMode = PageModelEnum.View;
                layer.open({
                    type: 2,
                    title: '维护调整事项',
                    closeBtn: 1,
                    area: ['90%', '50%'],
                    offset: '30px',
                    shadeClose: false,
                    content: 'AlarmChangeItemAddOrEdit.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        rowData = {
                            'planDetailId': planDetailId,
                            'planId': planId,
                            'alarmPointId': alarmPointId,
                            'alarmFlagId': alarmFlagId,
                            'pageMode': pageMode,
                            'unitId': $('#unitId').val()
                        }
                        iframeWin.page.logic.setData(alarmChangeItemArr, rowData);
                    },
                    end: function() {
                        // if (pageLoadMode == PageLoadMode.Refresh) {
                        //     page.logic.refreshTable();
                        //     window.pageLoadMode = PageLoadMode.None;
                        // }
                    }
                })
            },
            //保存
            save: function() {
                var obj = {};
                if (pageMode == 1) {
                    obj['remark'] = $("#issueRemark2").val();
                    obj['executeUserId'] = $("#executeUserName2").val();
                    obj['executeUserName'] = $("#executeUserName2 option:selected").text();
                } else if (pageMode == 2) {
                    obj['remark'] = $("#confirmRemark").val();
                }
                obj['businessType'] = pageMode;
                obj['planId'] = page.data.param.planId;
                var arr = new Array();
                arr.push(obj);
                var data = $.ET.toCollectionJson(arr);
                $.ajax({
                    url: saveUrl,
                    async: true,
                    type: "POST",
                    data: JSON.stringify(data),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function(result, XMLHttpRequest) {
                        layer.msg("保存成功");
                        // var res = $.parseJSON(result);
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            //下发
            submitData: function() {
                var obj = {};
                if (pageMode == 1) {
                    obj['remark'] = $("#issueRemark2").val();
                    obj['executeUserName'] = $("#executeUserName2 option:selected").text();
                    obj['executeUserId'] = $("#executeUserName2").val();
                } else if (pageMode == 2) {
                    obj['remark'] = $("#confirmRemark").val();
                }
                obj['businessType'] = pageMode;
                obj['planId'] = page.data.param.planId;
                var arr = new Array();
                arr.push(obj);
                var data = $.ET.toCollectionJson(arr);
                var tip=pageMode==1?'是否进行下发?':'是否进行确认?';
                layer.confirm(tip, {
                    btn: ['是', '否']
                }, function() {
                    $.ajax({
                        url: confirmUrl,
                        async: false,
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'POST',
                        success: function(result) {
                            if (result.indexOf('collection') < 0) {
                                parent.pageLoadMode = PageLoadMode.Refresh;
                                page.logic.closeLayer();
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function(index) {});
            },
            /**
             * 通过
             */
            pass: function() {
                $.ajax({
                    url: completeUrl,
                    async: false,
                    data: {
                        planId: page.data.param.planId,
                        taskId: page.data.param.taskId,
                        approveOpinion: $("#aproOpnion1").val()
                    },
                    dataType: "text",
                    contentType: "application/json;charset=utf-8",
                    type: 'get',
                    success: function(result) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("申请单审批通过！",{
                                time: 1000
                            },function() {
                                parent.pageLoadMode = PageLoadMode.Refresh;
                                page.logic.closeLayer(true);
                            });
                        } else {
                            result = JSON.parse(result)
                            layer.msg(result.collection.error.message)
                        }
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 驳回
             */
            reject: function() {
                $.ajax({
                    url: revertUrl,
                    async: false,
                    data: {
                        planId: page.data.param.planId,
                        taskId: page.data.param.taskId,
                        approveOpinion: $("#aproOpnion1").val()
                    },
                    dataType: "text",
                    contentType: "application/json;charset=utf-8",
                    type: 'get',
                    success: function(result) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("申请单驳回成功！",{
                                time: 1000
                            },function() {
                                parent.pageLoadMode = PageLoadMode.Refresh;
                                page.logic.closeLayer(true);
                            });
                        } else {
                            result = JSON.parse(result)
                            layer.msg(result.collection.error.message)
                        }
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function(isRefresh) {
                parent.layer.close(index);
            },
            getTime: function() {
                $.ajax({
                    url: getTimeUrl,
                    data: '',
                    dataType: 'json',
                    type: 'get',
                    success: function(result) {
                        alarmChangeItemArr = $.ET.toObjectArr(result);
                        var obj = new Object();
                        $.each(alarmChangeItemArr, function(i, el) {
                            if (i != 4 && i != 5) {
                                el.value = OPAL.util.strToDate(el.value).getTime();
                            }
                        })
                    }
                })
            },
            //审批信息页面
            AlarmChangePlanApro: function() {
                layer.open({
                    type: 2,
                    title: "",
                    closeBtn: false,
                    area: ['99%', '80%'],
                    shadeClose: false,
                    content: 'AlarmChangePlanApro.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        rowData = {
                            'planId': page.data.param.planId,
                            'taskId': page.data.param
                        }
                        iframeWin.page.logic.setData(rowData);
                    },
                    end: function(layero, index) {

                    }
                })
            }
        }
    };
    page.init();
    window.page = page;
});