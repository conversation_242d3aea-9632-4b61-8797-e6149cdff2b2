package com.pcitc.opal.common;

import lombok.Data;

import javax.swing.plaf.nimbus.State;
import java.util.Date;

/**
 * @USER: chenbo
 * @DATE: 2022/11/9
 * @TIME: 10:32
 * @DESC: 对象转换工具类
 **/
public class ObjectUtil {

    public static String objectToString(Object o){
        if (o == null){
            return "";
        }
        return o.toString();
    }

    public static Long objectToLong(Object o){
        if (o == null){
            return 0L;
        }
        return Long.parseLong(o.toString());
    }


    public static Integer objectToInteger(Object o){
        if (o == null){
            return null;
        }
        return Integer.valueOf(o.toString());
    }

    public static Date objectToDate(Object o){
        if (o == null){
            return null;
        }
        return (Date) o;
    }
}
