package com.pcitc.opal.ac.bll.entity;


import lombok.Data;

@Data
public class CraftParaAlarmRateEntity {

    //装置编码、
    private String unitCode;
    // 时平均报警数、
    private Double avgAlarmRate;
    private Integer avgAlarmRateNumerator;
    private Double avgAlarmRateDenominator;
    // 24小时持续报警数、
    private Long alarmAmount;
    // 10分钟峰值报警数
    private Double peakAlarmRate;
    //报警响应及时率
    private Double alarmTimelyResponseRate;
    private Integer alarmTimelyResponseRateNumerator;
    private Integer alarmTimelyResponseRateDenominator;
    //报警处置及时率
    private Double alarmTimelyDisposalRate;
    private Integer alarmTimelyDisposalRateNumerator;
    private Integer alarmTimelyDisposalRateDenominator;

}
