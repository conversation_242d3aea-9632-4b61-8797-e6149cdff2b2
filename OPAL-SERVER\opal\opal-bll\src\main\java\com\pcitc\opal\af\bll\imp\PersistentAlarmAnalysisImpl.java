package com.pcitc.opal.af.bll.imp;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.af.bll.PersistentAlarmAnalysisService;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.DateRangeEntity;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;

import pcitc.imp.common.ettool.utils.ObjectConverter;

/*
 * 持续报警分析业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_PersistentAlarmAnalysisImpl
 * 作       者：dageng.sun
 * 创建时间：2017/11/02
 * 修改编号：1
 * 描       述：持续报警分析业务逻辑层实现类
 */
@Service
public class PersistentAlarmAnalysisImpl implements PersistentAlarmAnalysisService {

	/**
	 * 实例化数据访问层接口
	 */
	@Autowired
	private AlarmEventRepository repo;
	/**
	 * 公共方法操作类
	 */
	@Autowired
    private BasicDataService basicDataService;
	@Autowired
    private ShiftService shiftService;
	
	/**
	 * 持续报警分析分页查询
	 * <AUTHOR> 2017-11-2
	 * @param unitCodes 装置id数组
	 * @param prdtCellIds 生产单元id数组
	 * @param alarmFlagId 报警标识
	 * @param isHandle 是否处理（0:未处理,1:已处理,-1:全部）
	 * @param beginTime 报警事件开始间
	 * @param endTime 报警事件结束时间
	 * @param page 翻页实现类
	 * @return 报警事件实体集合
	 * @throws Exception 
	 */
	@Override
	public PaginationBean<AlarmEventEntity> getPersistentAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds,
			Integer alarmFlagId, Integer isHandle, Date beginTime, Date endTime, Pagination page) throws Exception {
		try {
			if(ArrayUtils.isEmpty(unitCodes)){
				List<UnitEntity> unitEntityList = basicDataService.getUnitList( true);
				unitCodes=unitEntityList.stream().map(x->x.getStdCode()).distinct().toArray(String[]::new);
			}
			PaginationBean<AlarmEventEntity> returnAlarmEvent = basicDataService.getPersistentAlarmAnalysis(unitCodes, prdtCellIds, alarmFlagId, isHandle, beginTime, endTime, page);
			return returnAlarmEvent;
		} catch (Exception e) {
            throw e;
        }
	}

	/**
	 * 搁置报警分析分页查询
	 * 
	 * <AUTHOR> 2017-11-2
	 * @param unitCodes 装置id数组
	 * @param prdtCellIds 生产单元id数组
	 * @param alarmFlagId 报警标识
	 * @param beginTime 报警事件开始间
	 * @param endTime 报警事件结束时间
	 * @param workTeamIds 班组编号
	 * @param page 翻页实现类
	 * @return 报警事件实体集合
	 * @throws Exception 
	 */
	@Override
	public PaginationBean<AlarmEventEntity> getShelveAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds, Long alarmFlagId, Date beginTime, Date endTime,Long[] workTeamIds, Pagination page) throws Exception {
		if(ArrayUtils.isEmpty(unitCodes)){
			List<UnitEntity> unitEntityList = basicDataService.getUnitList( true);
			unitCodes=unitEntityList.stream().map(x->x.getStdCode()).distinct().toArray(String[]::new);
		}
		if (workTeamIds == null) {
            workTeamIds = new Long[]{};
        }
		List<Long> workTeamIdList = Arrays.asList(workTeamIds);
        List<DateRangeEntity> dateRangeList = new ArrayList<>();
        List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
        if (workTeamIdList.size() != 0 && unitCodes != null && unitCodes.length == 1) {
            shiftWorkList = shiftService.getShiftList(unitCodes[0], beginTime, endTime, workTeamIdList);
            dateRangeList = shiftWorkList.stream().map(item -> new DateRangeEntity(item.getStartTime(), item.getEndTime())).collect(Collectors.toList());
        }
        
		PaginationBean<AlarmEvent> listAlarmEvent = repo.getShelveAlarmAnalysis(unitCodes, prdtCellIds, alarmFlagId, beginTime, endTime,dateRangeList, page);
		List<UnitEntity> unitList = basicDataService.getUnitListByIds(listAlarmEvent.getPageList().stream().map(x->x.getAlarmPoint().getPrdtCell().getUnitId()).toArray(String[]::new), false);
		PaginationBean<AlarmEventEntity> returnAlarmEvent = mapPojoToEntity(page, listAlarmEvent, unitList,dateRangeList,workTeamIdList,unitCodes,shiftWorkList);
		return returnAlarmEvent;
	}
	
	/**
	 * 屏蔽报警分析分页查询
	 * 
	 * <AUTHOR> 2017-11-2
	 * @param unitCodes 装置id数组
	 * @param prdtCellIds 生产单元id数组
	 * @param alarmFlagId 报警标识
	 * @param eventTypeId 报警事件类型id
	 * @param beginTime 报警事件开始间
	 * @param endTime 报警事件结束时间
	 * @param workTeamIds 班组编号
	 * @param page 翻页实现类
	 * @return 报警事件实体集合
	 * @throws Exception 
	 */
	@Override
	public PaginationBean<AlarmEventEntity> getShieldAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds,
			Long alarmFlagId,Long eventTypeId, Date beginTime, Date endTime,Long[] workTeamIds, Pagination page) throws Exception {
		if(ArrayUtils.isEmpty(unitCodes)){
			List<UnitEntity> unitEntityList = basicDataService.getUnitList( true);
			unitCodes=unitEntityList.stream().map(x->x.getStdCode()).distinct().toArray(String[]::new);
		}
		if (workTeamIds == null) {
            workTeamIds = new Long[]{};
        }
		List<Long> workTeamIdList = Arrays.asList(workTeamIds);
        List<DateRangeEntity> dateRangeList = new ArrayList<>();
        List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
        if (workTeamIdList.size() != 0 && unitCodes != null && unitCodes.length == 1) {
            shiftWorkList = shiftService.getShiftList(unitCodes[0], beginTime, endTime, workTeamIdList);
            dateRangeList = shiftWorkList.stream().map(item -> new DateRangeEntity(item.getStartTime(), item.getEndTime())).collect(Collectors.toList());
        }
		Long[] eventTypeIds = null;
        if(eventTypeId == null || eventTypeId == -1){
			eventTypeIds = new Long[] {3003L,3004L};
		} else {
			eventTypeIds = new Long[] {eventTypeId};
		}
		PaginationBean<AlarmEvent> listAlarmEvent = repo.getShieldAlarmAnalysis(unitCodes, prdtCellIds, alarmFlagId,eventTypeIds, beginTime, endTime,dateRangeList, page);
		List<UnitEntity> unitList = basicDataService.getUnitListByIds(listAlarmEvent.getPageList().stream().map(x->x.getAlarmPoint().getPrdtCell().getUnitId()).toArray(String[]::new), false);
		PaginationBean<AlarmEventEntity> returnAlarmEvent = mapPojoToEntity(page, listAlarmEvent, unitList,dateRangeList,workTeamIdList,unitCodes,shiftWorkList);
		return returnAlarmEvent;
	}
	
	/**
	 * 转换POJO到Entity
	 * <AUTHOR> 2017-11-4
	 * @param page 分页信息
	 * @param listAlarmEvent 报警事件集合
	 * @return 报警事件实体集合
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	private PaginationBean<AlarmEventEntity> mapPojoToEntity(Pagination page, PaginationBean<AlarmEvent> listAlarmEvent,List<UnitEntity> units,List<DateRangeEntity> dateRangeList,List<Long> workTeamIdList,String[] unitCodes,List<ShiftWorkTeamEntity> shiftWorkList)
			throws Exception {
		PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<AlarmEventEntity>(page,listAlarmEvent.getTotal());
		returnAlarmEvent.setPageList(ObjectConverter.listConverter(listAlarmEvent.getPageList(), AlarmEventEntity.class));
		if (dateRangeList.size()==0&&workTeamIdList.size()==0) {
            Date minDate=returnAlarmEvent.getPageList().stream().reduce((item1,item2)->item1.getStartTime().getTime()<item2.getStartTime().getTime()?item1:item2).orElse(new AlarmEventEntity()).getStartTime();
            Date maxDate=returnAlarmEvent.getPageList().stream().reduce((item1,item2)->item1.getStartTime().getTime()>item2.getStartTime().getTime()?item1:item2).orElse(new AlarmEventEntity()).getStartTime();
            if(minDate!=null&&maxDate!=null) {
                shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCodes!=null?unitCodes:new String[]{}),minDate, maxDate);
            }
        }
		// 通过公共方法获取装置
		//Long[] filterUnitIds = listAlarmEvent.getPageList().stream().map(e -> e.getAlarmPoint().getPrdtCell().getUnitId()).distinct().toArray(Long[]::new);
		int i = 0;
		for (AlarmEventEntity aee : returnAlarmEvent.getPageList()) {
			AlarmEvent ae = listAlarmEvent.getPageList().get(i);
			//查找装置
			UnitEntity unit = units.stream().filter(u -> u.getStdCode().equals(ae.getAlarmPoint().getPrdtCell().getUnitId())).findFirst().orElse(new UnitEntity());
			aee.setUnitName(unit.getSname());
			aee.setPrdtCellName(ae.getAlarmPoint().getPrdtCell().getSname());
			aee.setAlarmPointTag(ae.getAlarmPoint().getTag());
			aee.setAlarmFlagName(ae.getAlarmFlag().getName());
			aee.setAlarmPointLocation(ae.getAlarmPoint().getLocation());
			aee.setMeasUnitName(ae.getAlarmPoint().getMeasUnit().getName() + "("+ ae.getAlarmPoint().getMeasUnit().getSign() + ")");
			aee.setWorkTeamSName(shiftWorkList.parallelStream().filter(item -> aee.getStartTime().getTime() >= item.getStartTime().getTime() && aee.getStartTime().getTime() < item.getEndTime().getTime()).findFirst().orElse(new ShiftWorkTeamEntity()).getWorkTeamSName());
			aee.setEventTypeName(ae.getEventType().getName());
			i++;
		}
		return returnAlarmEvent;
	}
}
