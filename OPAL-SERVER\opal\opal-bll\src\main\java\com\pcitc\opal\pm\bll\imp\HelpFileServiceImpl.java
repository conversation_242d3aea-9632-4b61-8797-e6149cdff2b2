package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.common.*;
import com.pcitc.opal.pm.bll.HelpFileService;
import com.pcitc.opal.pm.bll.entity.HelpFileEntity;
import com.pcitc.opal.pm.dao.HelpFileRepository;
import com.pcitc.opal.pm.pojo.HelpFile;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.Date;

/**
 * Author:隋
 * 2019/9/19 0019
 */
@Service
public class HelpFileServiceImpl implements HelpFileService {
    @Autowired
	private HelpFileRepository helpFileRepository;


	/**
	 * 新增
	 * @param helpFileEntity
	 * @return
	 * @throws Exception
	 */
	@Override
    public CommonResult addHelpFile(HelpFileEntity helpFileEntity) throws Exception {
		CommonProperty commonProperty = new CommonProperty();
		Date date = new Date();
        helpFileEntity.setUpLoadTime(date);
        helpFileEntity.setUpLoadUserId(commonProperty.getUserId());
        helpFileEntity.setUpLoadUserName(commonProperty.getUserName());
        // 实体转换为持久层实体
        HelpFile helpFile = ObjectConverter.entityConverter(helpFileEntity, HelpFile.class);
        // 数据校验
		//hrlpFileValidation(helpFile);
		//上传文件
		CMISClient cmisClient =new CMISClient();
		String documentId = cmisClient.uploadFile("",helpFileEntity.getFileName(),helpFileEntity.getUplAttaStream());
		if (StringUtils.isEmpty(documentId)){
			throw new Exception("上传文件失败！");
		}
        helpFile.setFileId(documentId);

		CommonResult commonResult = helpFileRepository.addHelpFile(helpFile);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false){
			//如果失败， 删除服务器的文件
			cmisClient.deleteFile(documentId);
			throw new Exception(commonResult.getMessage());
		}
		return commonResult;

    }

	/**
	 * 删除
	 * @param helpFileId
	 * @return
	 */
	@Override
	@Transactional
	public CommonResult deleteHelpFile(Long helpFileId) throws Exception {
		HelpFile helpFile = helpFileRepository.getOne(helpFileId);
		CommonResult commonResult = helpFileRepository.deleteHelpFile(helpFileId);
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
		// 删除报警制度管理相关联的附件
		CMISClient cmisClient = new CMISClient();
		boolean isDelete = cmisClient.deleteFile(helpFile.getFileId());
		if (isDelete == false)
			throw new Exception("附件删除失败！");
		return commonResult;
	}

	/**
	 * 获取所有帮助文档
	 * @param page
	 * @return
	 */
	@Override
	public PaginationBean<HelpFileEntity> getHelpFile(Pagination page) throws Exception {
		PaginationBean<HelpFile> paginationBean = helpFileRepository.getHelpFile(page);
		PaginationBean<HelpFileEntity> returnPaginationBean = new PaginationBean<>(page,paginationBean.getTotal());
		returnPaginationBean .setPageList(ObjectConverter.listConverter(paginationBean.getPageList(), HelpFileEntity.class));
		return returnPaginationBean;
	}


	/**
	 * 获取单条帮助文档
	 * @param helpFileId
	 * @return
	 * @throws Exception
	 */
	@Override
	public HelpFileEntity getSingleHelpFile(Long helpFileId) throws Exception {
		HelpFile helpFile = helpFileRepository.getSingleHelpFile(helpFileId);
		return ObjectConverter.entityConverter(helpFile,HelpFileEntity.class);
	}

	/**
	 * 校验
	 *
	 * <AUTHOR> 2018-03-01
	 * @param entity 报警制度管理实体
	 * @throws Exception
	 */
	private void hrlpFileValidation(HelpFile entity) throws Exception {
		CommonResult commonResult = null;
		/*// 实体不能为空
		if (entity == null) {
			throw new Exception("没有报警制度管理数据！");
		}*/
		// 调用DAL与数据库相关的校验
		commonResult = helpFileRepository.hrlpFileValidation(entity);

		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
	}
}
