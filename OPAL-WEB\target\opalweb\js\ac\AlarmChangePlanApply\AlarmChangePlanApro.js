$(function() {
	window.pageLoadMode = PageLoadMode.Refresh;
	var aproUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getAlarmChangePlanApro';
	var previewUrl=OPAL.API.commUrl + '/getWorkFlowPreviewURL';
	var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
	var page = {
		/**
		 * 初始化
		 */
		init: function() {
			this.bindUI();
			page.logic.initPreview();
		},
		bindUI: function() {
            //表格自适应页面拉伸宽度
            $(window).resize(function () {
                $('#infoTable').bootstrapTable('resetView');
            });
            $('#closePage').click(function () {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer();
            })
		},
		data: {
			// 设置查询参数
			param: {}
		},
		logic: {
			initTable: function(data) {
				OPAL.ui.initBootstrapTable("infoTable", {
					url: aproUrl,
					columns: [{
						title: "序号",
						field: '',
						formatter: function(value, row, index) {
							var tableOption = $('#infoTable').bootstrapTable('getOptions');
							var pageNumber = tableOption.pageNumber;
							var pageSize = tableOption.pageSize;
							return (index + 1) + (pageNumber - 1) * pageSize;
						},
						rowspan: 1,
						align: 'center',
						width: '50px'
					}, {
						title: "审批时间",
						field: 'aproTime',
						rowspan: 1,
						align: 'center',
						width: '100px'
					}, {
						title: "审批人",
						field: 'aproUserName',
						rowspan: 1,
						align: 'left',
						width: '100px'
					}, {
						title: "审批状态",
						field: 'aproStatus',
						rowspan: 1,
						align: 'center',
						width: '75px',
						formatter: function (value, row, index) {
							return row.aproStatus == 1 ? '驳回' : '通过';
						}
					}, {
						title: "审批意见",
						field: 'aproOpnion',
						rowspan: 1,
						align: 'left',
						width: '100px'
					}],
					responseHandler: function(res) {
						return $.ET.toObjectArr(res);
					},
					sidePagination: "client",
					cache: false,
					pagination: true, //是否分页
					pageNumber: 1,
					pageSize: 10,
					paginationPreText: '<',
					paginationNextText: '>',
					showColumns: false,
					pageList: [10, 20, 50, 100],
					striped: false
				}, data)
			},
            /**
             * 关闭弹出层
             */
            closeLayer: function() {
                parent.layer.close(index);
            },
			/**
			 * 查询参数
			 */
			queryParams: function(p) { // 设置查询参数
				var param = {
					'planId': data.planId,
				};
				return $.extend(param);
			},
			setData: function(data) {
				page.data.param = data;
				page.data.param.pageNumber = 1;
				page.logic.initTable(page.data.param);
			},
			closeLayer: function() {
				parent.layer.close(index);
			},
			/**
			 * 初始化预览
			 */
			initPreview:function(){
				$.ajax({
					url: previewUrl,
					dataType: "text",
					type: 'get',
					success: function (result) {
						var url = result + "&instanceId="+page.data.param.flowCaseId+"&flowOrganiseId=#templateorgId#";
						$("#previewTarget").attr("src",url);
					},
					error: function (result) {
						var errorResult = $.parseJSON(result.responseText);
						layer.msg(errorResult.collection.error.message);
					}
				})
			}
		}
	};
	page.init();
	window.page = page;
});
