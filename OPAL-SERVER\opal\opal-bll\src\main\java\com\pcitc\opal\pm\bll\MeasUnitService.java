package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import org.springframework.stereotype.Service;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.MeasUnitEntity;

/*
 * 计量单位业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_MeasUnitService
 * 作       者：jiangtao.xue
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描       述：计量单位业务逻辑层接口 
 */
@Service
public interface MeasUnitService {

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param measUnitEntity
	 *            计量单位实体
	 */
	CommonResult addMeasUnit(MeasUnitEntity measUnitEntity) throws Exception;

	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param measUnitIds  计量单位ID集合
	 */
	CommonResult deleteMeasUnit(Long[] measUnitIds) throws Exception;

	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param measUnitEntity 计量单位实体
	 */
	CommonResult updateMeasUnit(MeasUnitEntity measUnitEntity) throws Exception;

	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param measUnitId  计量单位ID
	 * @return 计量单位实体
	 */
	MeasUnitEntity getSingleMeasUnit(Long measUnitId) throws Exception;

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param name  计量单位名称
	 * @param sign  符号
	 * @param page  分页参数
	 * @return 计量单位实体集合
	 * @throws Exception
	 */
	PaginationBean<MeasUnitEntity> getMeasUnit(String name,String sign, Integer inUse, Pagination page)
			throws  Exception;
}
