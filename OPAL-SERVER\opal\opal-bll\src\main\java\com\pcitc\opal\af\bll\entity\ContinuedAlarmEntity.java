package com.pcitc.opal.af.bll.entity;

import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.ad.vo.AlarmRecInfoVO;
import com.pcitc.opal.common.CommonEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;
import java.util.List;

/**
 * @USER: chenbo
 * @DATE: 2022/11/29
 * @TIME: 14:08
 * @DESC:
 **/
@Data
@NoArgsConstructor
public class ContinuedAlarmEntity extends BaseResRep implements Serializable {

    public ContinuedAlarmEntity(AlarmRecInfoVO alarmRec) {
        this.prdtCellName = alarmRec.getPrdtcellName();
        this.alarmPointTag = alarmRec.getTag();
        this.measUnitName = alarmRec.getMeasunitName() + "( " + alarmRec.getMeasunitSign() + ")";
        this.alarmFlagName = alarmRec.getAlarmflagName();
        this.des = alarmRec.getLocation();
        setPriority(alarmRec.getPriority());
        setMonitorTypeShow(alarmRec.getMonitorType());
    }

    /**
     *装置名称
     */
    private String unitName;

    /**
     * 生产单元名称
     */
    private String prdtCellName;

    /**
     * 位号
     */
    private String alarmPointTag;

    /**
     * 描述
     */
    private String des;

    /**
     * 优先级（1紧急；2重要；3一般）
     */
    private String priority;


    /**
     * 报警标识
     */
    private String alarmFlagName;

    /**
     * 计量单位
     */
    private String measUnitName;

    /**
     * 持续报警次数
     */
    private Integer alarmNumber;

    /**
     * 详细信息
     */
    List<ContinuedAlarmDetail> details;

    private String monitorTypeShow;

    /**
     * 根据拼接好的字符串进行赋值
     * @param str 分割后的数组
     */
    public void setSplicing(String[] str){
        unitName = str[0];
        prdtCellName = str[1];
        alarmPointTag = str[2];
        des = str[3];
        setPriority(str[4] == null ? null : Integer.parseInt(str[4]));
        alarmFlagName = str[5];
        measUnitName = str[6];
    }

    public void setPriority(Integer priority) {
        if (priority == null) {
            this.priority = "";
        } else {
            this.priority = CommonEnum.AlarmPriorityEnum.getName(priority);
        }
    }

    public void setMonitorTypeShow(Integer monitorType) {
        if (monitorType == null) {
            this.monitorTypeShow = "";
        } else {
            this.monitorTypeShow = CommonEnum.MonitorTypeEnum.getName(monitorType);
        }
    }
}