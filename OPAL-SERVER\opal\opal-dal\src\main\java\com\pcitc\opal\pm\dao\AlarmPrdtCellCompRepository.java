package com.pcitc.opal.pm.dao;

import com.pcitc.opal.pm.pojo.AlarmPrdtCellComp;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * AlarmPrdtCellComp实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmPrdtCellCompRepository
 * 作    者：jiangtao.xue
 * 创建时间：2018/04/04
 * 修改编号：1
 * 描    述：AlarmPrdtCellComp实体的Repository实现   
 */
public interface AlarmPrdtCellCompRepository extends JpaRepository<AlarmPrdtCellComp, Long>, AlarmPrdtCellCompRepositoryCustom {

}
