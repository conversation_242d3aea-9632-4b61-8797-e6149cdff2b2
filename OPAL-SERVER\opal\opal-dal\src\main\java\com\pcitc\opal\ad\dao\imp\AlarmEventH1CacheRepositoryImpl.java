package com.pcitc.opal.ad.dao.imp;

import com.pcitc.opal.ad.dao.AlarmEventH1CacheRepositoryCustom;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.pojo.AlarmEventH1Cache;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class AlarmEventH1CacheRepositoryImpl extends BaseRepository<AlarmEventH1Cache,Long> implements AlarmEventH1CacheRepositoryCustom {


    /**
     * 获取未匹配计量单位列表
     *
     * @return 未匹配计量单位列表
     * <AUTHOR> 2018-04-19
     */
    @Override
    public List<AlarmEventH1Cache> getCacheMeasUnitList(Long[] dcsCodeIds,String measUnit, Long reason, Date startTime, Date endTime) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> paramList = new HashMap<>();
        sql.append("SELECT DISTINCT * FROM (");
        String unExistSql = "SELECT DISTINCT C1.UNITS MEAS_UNIT" +
                " FROM T_AD_ALARMEVENT_H1_CACHE C1 WHERE UPPER(C1.UNITS) NOT IN " +
                " (SELECT DISTINCT UPPER(MU.SIGN) FROM T_PM_MEASUNIT MU WHERE MU.IN_USE=1) AND C1.FTTIME>=:STARTTIME AND C1.FTTIME<:ENDTIME";
        String dcsSql =" AND C1.DCS_CODE IN (:DCSCODEIDS)";
        String measUnitSql = " AND UPPER(C1.UNITS) =UPPER(:MEASUNIT) ";
        String union =" UNION ALL";
        String differenceSql=" SELECT DISTINCT C1.UNITS MEAS_UNIT" +
                " FROM T_AD_ALARMEVENT_H1_CACHE C1 " +
                " LEFT JOIN T_AD_ALARMPRDTCELLCOMP COMP ON C1.LOCATION_TAG_NAME=COMP.PRDTCELL_SOURCE AND C1.OPC_CODE=COMP.OPC_CODE_ID AND C1.DCS_CODE=COMP.DCS_CODE_ID" +
                " LEFT JOIN T_PM_ALARMPOINT AP ON COMP.PRDTCELL_ID=AP.PRDTCELL_ID " +
                " LEFT JOIN T_PM_MEASUNIT M ON AP.MEASUNIT_ID=M.MEASUNIT_ID" +
                " WHERE M.IN_USE=1 and COMP.company_id=:companyId and AP.company_id=:companyId" +
                " AND UPPER(C1.UNITS)<>UPPER(M.SIGN) " +
                " AND UPPER(C1.UNITS) IN (SELECT DISTINCT UPPER(MU.SIGN) FROM T_PM_MEASUNIT MU WHERE MU.IN_USE=1)" +
                " AND C1.FTTIME>=:STARTTIME AND C1.FTTIME<:ENDTIME";
        if(reason == -1) {
            if(ArrayUtils.isNotEmpty(dcsCodeIds)) {
                if(!measUnit.equals("-1")){
                    sql.append(unExistSql + dcsSql +measUnitSql + union + differenceSql + dcsSql +measUnitSql);
                    paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
                    paramList.put("MEASUNIT", measUnit);
                } else {
                    sql.append(unExistSql +dcsSql +union + differenceSql + dcsSql);
                    paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
                }
            } else {
                if(!measUnit.equals("-1")){
                    sql.append(unExistSql +measUnitSql + union + differenceSql +measUnitSql);
                    paramList.put("MEASUNIT", measUnit);
                } else {
                    sql.append(unExistSql +union + differenceSql);
                }
            }
        } else if(reason == 1) { //不存在
            if(ArrayUtils.isNotEmpty(dcsCodeIds)) {
                if(!measUnit.equals("-1")){
                    sql.append(unExistSql +dcsSql + measUnitSql);
                    paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
                    paramList.put("MEASUNIT", measUnit);
                } else {
                    sql.append(unExistSql + dcsSql);
                    paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
                }
            } else {
                if(!measUnit.equals("-1")){
                    sql.append(unExistSql +measUnitSql);
                    paramList.put("MEASUNIT", measUnit);
                } else {
                    sql.append(unExistSql);
                }
            }
        } else if(reason == 2) { //不一致
            if(ArrayUtils.isNotEmpty(dcsCodeIds)) {
                if(!measUnit.equals("-1")){
                    sql.append(differenceSql + dcsSql + measUnitSql);
                    paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
                    paramList.put("MEASUNIT", measUnit);
                } else {
                    sql.append(differenceSql +dcsSql);
                    paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
                }
            } else {
                if(!measUnit.equals("-1")){
                    sql.append(differenceSql + measUnitSql);
                    paramList.put("MEASUNIT", measUnit);
                } else {
                    sql.append(differenceSql);
                }
            }
        }
        sql.append(" ) T ORDER BY T.MEAS_UNIT ASC");
        paramList.put("STARTTIME", startTime);
        paramList.put("ENDTIME", endTime);
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId",commonProperty.getCompanyId());
        Query query = getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, paramList);
        List<String> list = query.getResultList();
        List<AlarmEventH1Cache> returnList = new ArrayList<>();
        for (String item : list) {
            AlarmEventH1Cache cache = new AlarmEventH1Cache();
            cache.setMeasUnit(item);
            cache.setParameter(item);
            returnList.add(cache);
        }
        return returnList;
    }

    /**
     * 获取未匹配优先级列表
     *
     * @param priority  优先级
     * @param dcsIds    DCSID集合
     * @param startTime 开始事件
     * @param endTime   结束事件
     * @return 未匹配优先级列表
     * <AUTHOR> 2018-04-17
     */
    @Override
    public List<AlarmEventH1Cache> getCachePriorityList(String priority, Long[] dcsIds, Date startTime, Date endTime) {
        Map<String, Object> paramList = new HashMap<>();
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        paramList.put("dcsCode", Arrays.asList(dcsIds));
        StringBuilder hql = new StringBuilder(" select distinct cache.priority from AlarmEventH1Cache cache where 1=1 ");

        //1.如果""或者-1,则默认选择全部或者是第一次进入页面查询
        //2.如果是"-",表示查询的是下拉为空的,匹配IS NULL
        //3.如果不是"",-1,"-",则查询匹配值

        if ("".equals(priority) || "-1".equals(priority)) {
            hql.append(" and (cache.priority not in(select distinct  pc.prioritySource from AlarmPriorityComp  pc where pc.inUse=1) or cache.priority IS NULL) ");
        } else if ("-".equals(priority)) {
            hql.append(" and cache.priority IS NULL ");
        } else {
            hql.append(" and cache.priority not in(select distinct  pc.prioritySource from AlarmPriorityComp  pc where pc.inUse=1) and cache.priority=:priority ");
            paramList.put("priority", priority);
        }

        hql.append(" and cache.startTime>=:startTime and cache.startTime<:endTime " +
                " and cache.dcsCode in:dcsCode " +
                " order by cache.priority asc  ");

        Query query = getEntityManager().createQuery(hql.toString());
        setParameterList(query, paramList);
        List<String> list = query.getResultList();

        List<AlarmEventH1Cache> returnList = new ArrayList<>();
        for (String item : list) {
            AlarmEventH1Cache cache = new AlarmEventH1Cache();
            if (item == null) {
                cache.setDes("");
                cache.setParameter("-");
            } else {
                cache.setDes(item);
                cache.setParameter(item);
            }
            returnList.add(cache);
        }
        return returnList;
    }

    /**
     * 获取未匹配报警标识列表
     *
     * @param alarmFlag  报警标识
     * @param dcsIds    DCSID集合
     * @param startTime 开始事件
     * @param endTime   结束事件
     * @return 匹配报警标识列表
     * <AUTHOR> 2018-04-17
     */
    @Override
    public List<AlarmEventH1Cache> getCacheAlarmFlagList(String alarmFlag, Long[] dcsIds, Date startTime, Date endTime) {
        Map<String, Object> paramList = new HashMap<>();
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        paramList.put("dcsCode", Arrays.asList(dcsIds));
        StringBuilder hql = new StringBuilder(" select distinct cache.alarmFlag from AlarmEventH1Cache cache where 1=1 ");

        //1.如果""或者-1,则默认选择全部或者是第一次进入页面查询
        //2.如果是"-",表示查询的是下拉为空的,匹配IS NULL
        //3.如果不是"",-1,"-",则查询匹配值

        if ("".equals(alarmFlag) || "-1".equals(alarmFlag)) {
            hql.append(" and (cache.alarmFlag not in(select distinct  pc.alarmFlagSource from AlarmFlagComp pc where pc.inUse=1) or cache.alarmFlag IS NULL) ");
        } else if ("-".equals(alarmFlag)) {
            hql.append(" and cache.alarmFlag IS NULL ");
        } else {
            hql.append(" and cache.alarmFlag not in(select distinct  pc.alarmFlagSource from AlarmFlagComp  pc where pc.inUse=1) and cache.alarmFlag=:alarmFlag ");
            paramList.put("alarmFlag", alarmFlag);
        }

        hql.append(" and cache.startTime>=:startTime and cache.startTime<:endTime " +
                " and cache.dcsCode in:dcsCode " +
                " order by cache.alarmFlag asc  ");

        Query query = getEntityManager().createQuery(hql.toString());
        setParameterList(query, paramList);
        List<String> list = query.getResultList();

        List<AlarmEventH1Cache> returnList = new ArrayList<>();
        for (String item : list) {
            AlarmEventH1Cache cache = new AlarmEventH1Cache();
            if (item == null) {
                cache.setDes("");
                cache.setParameter("-");
            } else {
                cache.setDes(item);
                cache.setParameter(item);
            }
            returnList.add(cache);
        }
        return returnList;
    }


    /**
     * 获取未匹配报警点列表
     *
     *
     * @param alarmPoint
     * @param dcsIds    DCSID集合
     * @param startTime 开始事件
     * @param endTime   结束事件
     * @return 匹配报警点列表
     * <AUTHOR> 2018-04-17
     */
    @Override
    public List<AlarmEventH1Cache> getCacheAlarmPointList(String alarmPoint, Long[] dcsIds, Date startTime, Date endTime) {
        Map<String, Object> paramList = new HashMap<>();
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        paramList.put("dcsCode", Arrays.asList(dcsIds));
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId",commonProperty.getCompanyId());
        StringBuilder hql = new StringBuilder(" select distinct cache.alarmPoint from AlarmEventH1Cache cache where 1=1 ");

        //1.如果""或者-1,则默认选择全部或者是第一次进入页面查询
        //2.如果是"-",表示查询的是下拉为空的,匹配IS NULL
        //3.如果不是"",-1,"-",则查询匹配值

        if ("".equals(alarmPoint) || "-1".equals(alarmPoint)) {
            hql.append(" and (cache.alarmPoint not in(select distinct  pc.tag from AlarmPoint pc where pc.inUse=1 and pc.companyId=:companyId ) or cache.alarmPoint IS NULL) ");
        } else if ("-".equals(alarmPoint)) {
            hql.append(" and cache.alarmPoint IS NULL ");
        } else {
            hql.append(" and cache.alarmPoint not in(select distinct  pc.tag from AlarmPoint pc where pc.inUse=1 and pc.companyId=:companyId ) and cache.alarmPoint=:alarmPoint ");
            paramList.put("alarmPoint", alarmPoint);
        }

        hql.append(" and cache.startTime>=:startTime and cache.startTime<:endTime " +
                " and cache.dcsCode in:dcsCode " +
                " order by cache.alarmPoint asc  ");

        Query query = getEntityManager().createQuery(hql.toString());
        setParameterList(query, paramList);
        List<String> list = query.getResultList();

        List<AlarmEventH1Cache> returnList = new ArrayList<>();
        for (String item : list) {
            AlarmEventH1Cache cache = new AlarmEventH1Cache();
            if (item == null) {
                cache.setDes("");
                cache.setParameter("-");
            } else {
                cache.setDes(item);
                cache.setParameter(item);
            }
            returnList.add(cache);
        }
        return returnList;
    }

    /**
     * 获取未匹配报警点列表
     *
     *
     * @param alarmPoint 报警点
     * @param dcsCodeIds         DCSID集合
     * @param reason         不一致原因
     * @param startTime      开始事件
     * @param endTime        结束事件
     * @return      匹配报警点列表
     * <AUTHOR> 2018-04-26
     */
    public List<AlarmEventH1Cache> getCacheAlarmPointList(String alarmPoint, Long[] dcsCodeIds, Long reason,Date startTime, Date endTime){
        Map<String, Object> paramList = new HashMap<>();
        StringBuilder sql = new StringBuilder("SELECT DISTINCT * FROM (");
        String unExistSql = "SELECT DISTINCT C1.SOURCETAGNAME ALARM_POINT " +
                " FROM T_AD_ALARMEVENT_H1_CACHE C1 WHERE ( C1.SOURCETAGNAME NOT IN " +
                " (SELECT DISTINCT AP.TAG FROM T_PM_ALARMPOINT AP WHERE AP.IN_USE=1 and AP.company_id=:companyId)" +
                "AND C1.FTTIME>=:STARTTIME AND C1.FTTIME<:ENDTIME";
        String nullsql =" )OR C1.SOURCETAGNAME IS NULL " ;
        String dcsSql =" AND C1.DCS_CODE IN (:DCSCODEIDS)";
        String alarmPointSql = " AND C1.SOURCETAGNAME =:ALARMPOINT ";
        String union =" UNION ALL";
        String differenceSql=" SELECT DISTINCT C1.SOURCETAGNAME ALARM_POINT " +
                " FROM T_AD_ALARMEVENT_H1_CACHE C1 " +
                " INNER JOIN T_AD_ALARMPRDTCELLCOMP COMP ON C1.LOCATION_TAG_NAME=COMP.PRDTCELL_SOURCE AND C1.OPC_CODE=COMP.OPC_CODE_ID " +
                " INNER JOIN T_PM_ALARMPOINT AP ON C1.SOURCETAGNAME=AP.TAG " +
                " AND COMP.PRDTCELL_ID<>AP.PRDTCELL_ID and COMP.company_id=:companyId and AP.company_id=:companyId " +
                " AND C1.FTTIME>=:STARTTIME AND C1.FTTIME<:ENDTIME";

        if(reason == -1) {
            if(ArrayUtils.isNotEmpty(dcsCodeIds)) {
                if(!alarmPoint.equals("-1") && !alarmPoint.equals("") && !alarmPoint.equals("-")){
                    sql.append(unExistSql + dcsSql +alarmPointSql + nullsql + union + differenceSql + dcsSql +alarmPointSql);
                    paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
                    paramList.put("ALARMPOINT", alarmPoint);
                } else {
                    sql.append(unExistSql +dcsSql + nullsql +union + differenceSql + dcsSql);
                    paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
                }

            } else {
                if(!alarmPoint.equals("-1") && !alarmPoint.equals("") && !alarmPoint.equals("-")){
                    sql.append(unExistSql +alarmPointSql + nullsql + union + differenceSql +alarmPointSql);
                    paramList.put("ALARMPOINT", alarmPoint);
                } else {
                    sql.append(unExistSql +nullsql +union + differenceSql);
                }
            }
        } else if(reason == 1) { //不存在
            if(ArrayUtils.isNotEmpty(dcsCodeIds)) {
                if(!alarmPoint.equals("-1") && !alarmPoint.equals("") && !alarmPoint.equals("-")){
                    sql.append(unExistSql +dcsSql + alarmPointSql +nullsql);
                    paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
                    paramList.put("ALARMPOINT", alarmPoint);
                } else {
                    sql.append(unExistSql + dcsSql + nullsql);
                    paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
                }
            } else {
                if(!alarmPoint.equals("-1") && !alarmPoint.equals("") && !alarmPoint.equals("-")){
                    sql.append(unExistSql +alarmPointSql + nullsql);
                    paramList.put("ALARMPOINT", alarmPoint);
                } else {
                    sql.append(unExistSql + nullsql);
                }
            }
        } else if(reason == 2) { //不一致
            if(ArrayUtils.isNotEmpty(dcsCodeIds)) {
                if(!alarmPoint.equals("-1") && !alarmPoint.equals("") && !alarmPoint.equals("-")){
                    sql.append(differenceSql + dcsSql + alarmPointSql);
                    paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
                    paramList.put("ALARMPOINT", alarmPoint);
                } else {
                    sql.append(differenceSql +dcsSql);
                    paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
                }
            } else {
                if(!alarmPoint.equals("-1") && !alarmPoint.equals("") && !alarmPoint.equals("-")){
                    sql.append(differenceSql + alarmPointSql);
                    paramList.put("ALARMPOINT", alarmPoint);
                } else {
                    sql.append(differenceSql);
                }
            }
        }
        sql.append(" ) T WHERE 1=1");
        if(alarmPoint.equals("-")){
            sql.append(" AND T.ALARM_POINT IS NULL");
        }
        sql.append(" ORDER BY T.ALARM_POINT ASC");
        paramList.put("STARTTIME", startTime);
        paramList.put("ENDTIME", endTime);
//企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId",commonProperty.getCompanyId());
        Query query = getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, paramList);
        List<String> list = query.getResultList();
        List<AlarmEventH1Cache> returnList = new ArrayList<>();
        for (String item : list) {
            AlarmEventH1Cache cache = new AlarmEventH1Cache();
            if(item == null) {
                cache.setAlarmPoint("");
                cache.setParameter("-");
            }else{
                cache.setAlarmPoint(item);
                cache.setParameter(item);
            }
            returnList.add(cache);
        }
        return returnList;
    }

    /**
     * 获取未匹配生产单元列表
     *
     * @param prdtCell  生产单元
     * @param dcsIds    DCSID集合
     * @param startTime 开始事件
     * @param endTime   结束事件
     * @return 未匹配生产单元列表
     * <AUTHOR> 2018-04-17
     */
    @Override
    public List<AlarmEventH1Cache> getCachePrdtCellList(String prdtCell, Long[] dcsIds, Date startTime, Date endTime) {

        Map<String, Object> paramList = new HashMap<>();
        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        paramList.put("dcsCode", Arrays.asList(dcsIds));
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId",commonProperty.getCompanyId());
        StringBuilder hql = new StringBuilder(" select distinct cache.prdtCell from AlarmEventH1Cache cache where 1=1 ");

        //1.如果""或者-1,则默认选择全部或者是第一次进入页面查询
        //2.如果是"-",表示查询的是下拉为空的,匹配IS NULL
        //3.如果不是"",-1,"-",则查询匹配值

        if ("".equals(prdtCell) || "-1".equals(prdtCell)) {
            hql.append(" and (cache.prdtCell not in(select distinct  pc.prdtCellSource from AlarmPrdtCellComp pc where pc.inUse=1 and pc.companyId=:companyId ) or cache.prdtCell IS NULL) ");
        } else if ("-".equals(prdtCell)) {
            hql.append(" and cache.prdtCell IS NULL ");
        } else {
            hql.append(" and cache.prdtCell not in(select distinct  pc.prdtCellSource from AlarmPrdtCellComp pc where pc.inUse=1 and pc.companyId=:companyId ) and cache.prdtCell=:prdtCell ");
            paramList.put("prdtCell", prdtCell);
        }

        hql.append(" and cache.startTime>=:startTime and cache.startTime<:endTime " +
                " and cache.dcsCode in:dcsCode " +
                " order by cache.prdtCell asc  ");

        Query query = getEntityManager().createQuery(hql.toString());
        setParameterList(query, paramList);

        List<String> list = query.getResultList();
        List<AlarmEventH1Cache> returnList = new ArrayList<>();
        for (String item : list) {
            AlarmEventH1Cache cache = new AlarmEventH1Cache();
            if (item == null) {
                cache.setDes("");
                cache.setParameter("-");
            } else {
                cache.setDes(item);
                cache.setParameter(item);
            }
            returnList.add(cache);
        }
        return returnList;
    }

//    /**
//     * 获取未匹配事件名称列表
//     *
//     *
//     * @param eventName 事件名称
//     * @param eventType 事件名称
//     * @param dcsIds    DCSID集合
//     * @param startTime 开始事件
//     * @param endTime   结束事件
//     * @return 未匹配事件名称列表
//     * <AUTHOR> 2018-04-17
//     */
//    @Override
//    public List<AlarmEventH1Cache> getCacheEventNameList(String eventName, String eventType, Long[] dcsIds, Date startTime, Date endTime) {
//        StringBuilder hql = new StringBuilder();
//        Map<String, Object> paramList = new HashMap<>();
//        paramList.put("startTime", startTime);
//        paramList.put("endTime", endTime);
//        paramList.put("dcsCode", Arrays.asList(dcsIds));
//
//        hql.append("SELECT DISTINCT CACHE.EVENT_NAME FROM T_AD_ALARMEVENT_H1_CACHE CACHE " +
//                " WHERE ((CACHE.EVENTCATEGORY,CACHE.EVENT_NAME) NOT IN(SELECT DISTINCT ET.EVENT_TYPE_SOURCE,ET.EVENT_NAME_SOURCE FROM T_AD_ALARMEVENTTYPECOMP ET WHERE ET.IN_USE=1) OR CACHE.EVENT_NAME IS NULL) " +
//                " AND CACHE.FTTIME>=:startTime AND CACHE.FTTIME<:endTime " +
//                " AND CACHE.DCS_CODE IN :dcsCode ");
//
//        //1.如果""或者-1,则默认选择全部或者是第一次进入页面查询
//        //2.如果是"-",表示查询的是下拉为空的,匹配IS NULL
//        //3.如果不是"",-1,"-",则查询匹配值
//        if ("".equals(eventName) || "-1".equals(eventName)) {
//
//        } else if ("-".equals(eventName)) {
//            hql.append(" AND CACHE.EVENT_NAME IS NULL ");
//        } else {
//            hql.append(" AND CACHE.EVENT_NAME=:EVENTNAME ");
//            paramList.put("EVENTNAME", eventName);
//        }
//        //处理事件类型
//        if ("".equals(eventType) || "-1".equals(eventType)) {
//            List<String> eventTypeList = getCacheEventTypeList(eventType, dcsIds, startTime, endTime).stream().map(AlarmEventH1Cache::getDes).collect(Collectors.toList());
//            if (eventTypeList.size() != 0) {
//                if (eventTypeList.contains("")) {
//                    hql.append("  AND (CACHE.EVENT_TYPE IN (:EVENTTYPE) OR CACHE.EVENT_TYPE IS NULL) ");
//                } else {
//                    hql.append("  AND CACHE.EVENT_TYPE IN :EVENTTYPE ");
//                }
//                paramList.put("EVENTTYPE", eventTypeList);
//            }
//        } else {
//            paramList.put("EVENTTYPE", eventType);
//            hql.append(" AND CACHE.EVENTCATEGORY =:EVENTTYPE ");
//        }
//        hql.append(" ORDER BY CACHE.EVENT_NAME ASC ");
//        Query query = getEntityManager().createNativeQuery(hql.toString());
//
//        setParameterList(query, paramList);
//        List<String> list = query.getResultList();
//        List<AlarmEventH1Cache> returnList = new ArrayList<>();
//        for (String item : list) {
//            AlarmEventH1Cache cache = new AlarmEventH1Cache();
//            if (item == null) {
//                cache.setDes("");
//                cache.setParameter("-");
//            } else {
//                cache.setDes(item);
//                cache.setParameter(item);
//            }
//            returnList.add(cache);
//        }
//        return returnList;
//    }

    /**
     * 获取未匹配事件类型列表
     *
     * @param eventType 事件类型
     * @param dcsIds    DCSID集合
     * @param startTime 开始事件
     * @param endTime   结束事件
     * @return 未匹配事件类型列表
     * <AUTHOR> 2018-04-17
     */
    @Override
    public List<AlarmEventH1Cache> getCacheEventTypeList(String eventType, Long[] dcsIds, Date startTime, Date endTime) {
        Map<String, Object> paramList = new HashMap<>();
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT DISTINCT CACHE.EVENTCATEGORY FROM T_AD_ALARMEVENT_H1_CACHE CACHE " +
                " WHERE (CACHE.EVENTCATEGORY) NOT IN(SELECT DISTINCT ET.EVENT_TYPE_SOURCE FROM T_AD_ALARMEVENTTYPECOMP ET WHERE ET.IN_USE=1) " +
                " AND CACHE.FTTIME>=:startTime AND CACHE.FTTIME<:endTime " +
                " AND CACHE.DCS_CODE IN :dcsCode ");

        paramList.put("startTime", startTime);
        paramList.put("endTime", endTime);
        paramList.put("dcsCode", Arrays.asList(dcsIds));

        //1.如果""或者-1,则默认选择全部或者是第一次进入页面查询
        //2.如果是"-",表示查询的是下拉为空的,匹配IS NULL
        //3.如果不是"",-1,"-",则查询匹配值
        if ("".equals(eventType) || "-1".equals(eventType)) {

        } else if ("-".equals(eventType)) {
            hql.append(" AND CACHE.EVENTCATEGORY IS NULL");
        } else {
            paramList.put("EVENTTYPE", eventType);
            hql.append(" AND CACHE.EVENTCATEGORY =:EVENTTYPE ");
        }

        hql.append(" ORDER BY CACHE.EVENTCATEGORY ASC ");
        Query query = getEntityManager().createNativeQuery(hql.toString());
        setParameterList(query, paramList);

        List<String> list = query.getResultList();
        List<AlarmEventH1Cache> returnList = new ArrayList<>();
        for (String item : list) {
            AlarmEventH1Cache cache = new AlarmEventH1Cache();
            if (item == null) {
                cache.setDes("");
                cache.setParameter("-");
            } else {
                cache.setDes(item);
                cache.setParameter(item);
            }
            returnList.add(cache);
        }
        return returnList;
    }

    /**
     * 获取未配置数据分页
     *
     * @param dcsCodeIds      DcsCodeID
     * @param prdtCell        生产单元
     * @param alarmPoint      报警点
     * @param alarmFlag       报警标识
     * @param eventTypeSource 源事件类型
     * @param eventNameSource 源事件名称
     * @param priority        优先级
     * @param startTime       查询开始时间
     * @param endTime         查询结束时间
     * @param page
     * @return 未匹配数据
     * @throws Exception
     * <AUTHOR>  2018-04-16
     */
    @Override
    public PaginationBean<AlarmEventH1Cache> getUnconfiguredDataList(Long[] dcsCodeIds,
                                                                   String prdtCell,
                                                                   String alarmPoint,
                                                                   String alarmFlag,
                                                                   String eventTypeSource,
                                                                   String eventNameSource,
                                                                   String priority,
                                                                   Date startTime,
                                                                   Date endTime,
                                                                   Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from AlarmEventH1Cache t " +
                    "left join fetch t.dcs " +
                    "where t.dcs.inUse=1 and t.startTime>=:startTime and t.startTime <:endTime ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<>();
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);

            // DcsCodeID
            hql.append("  and t.dcsCode in :dcsCode ");
            paramList.put("dcsCode", Arrays.asList(dcsCodeIds));

            List<String> prdtCellList = new ArrayList<>();
            if ("-1".equals(prdtCell) || "".equals(prdtCell)) {
                prdtCellList = getCachePrdtCellList(prdtCell, dcsCodeIds, startTime, endTime).stream().map(AlarmEventH1Cache::getDes).collect(Collectors.toList());
            } else if (StringUtils.isNoneBlank(prdtCell) && !"-".equals(prdtCell)) {
                prdtCellList.add(prdtCell);
            } else if ("-".equals(prdtCell)) {
                prdtCellList.add("");
            }

            List<String> alarmPointList = new ArrayList<>();
            if ("-1".equals(alarmPoint) || "".equals(alarmPoint)) {
                alarmPointList = getCacheAlarmPointList(alarmPoint, dcsCodeIds, startTime, endTime).stream().map(AlarmEventH1Cache::getDes).collect(Collectors.toList());
            } else if (StringUtils.isNoneBlank(alarmPoint) && !"-".equals(alarmPoint)) {
                alarmPointList.add(alarmPoint);
            } else if ("-".equals(alarmPoint)) {
                alarmPointList.add("");
            }

            List<String> alarmFlagList = new ArrayList<>();
            if ("-1".equals(alarmFlag) || "".equals(alarmFlag)) {
                alarmFlagList = getCacheAlarmFlagList(alarmFlag, dcsCodeIds, startTime, endTime).stream().map(AlarmEventH1Cache::getDes).collect(Collectors.toList());
            } else if (StringUtils.isNoneBlank(alarmFlag) && !"-".equals(alarmFlag)) {
                alarmFlagList.add(alarmFlag);
            } else if ("-".equals(alarmFlag)) {
                alarmFlagList.add("");
            }

            List<String> eventTypeList = new ArrayList<>();
            if ("-1".equals(eventTypeSource) || "".equals(eventTypeSource)) {
                eventTypeList = getCacheEventTypeList("", dcsCodeIds, startTime, endTime).stream().map(AlarmEventH1Cache::getDes).collect(Collectors.toList());
            } else if (StringUtils.isNoneBlank(eventTypeSource) && !"-".equals(eventTypeSource)) {
                eventTypeList.add(eventTypeSource);
            } else if ("-".equals(eventTypeSource)) {
                eventTypeList.add("");
            }

//            List<String> eventNameList = new ArrayList<>();
//            if ("-1".equals(eventNameSource) || "".equals(eventNameSource)) {
//                eventNameList = getCacheEventNameList(eventNameSource, eventTypeSource, dcsCodeIds, startTime, endTime).stream().map(AlarmEventH1Cache::getDes).filter(item -> item != null).collect(Collectors.toList());
//            } else if (StringUtils.isNoneBlank(eventNameSource) && !"-".equals(eventNameSource)) {
//                eventNameList = getCacheEventNameList(eventNameSource, eventTypeSource, dcsCodeIds, startTime, endTime).stream().map(AlarmEventH1Cache::getDes).filter(item -> item != null).collect(Collectors.toList());
//            } else if ("-".equals(eventNameSource)) {
//                eventNameList.add("");
//            }

            List<String> priorityList = new ArrayList<>();
            if ("-1".equals(priority) || "".equals(priority)) {
                priorityList = getCachePriorityList(priority, dcsCodeIds, startTime, endTime).stream().map(AlarmEventH1Cache::getDes).collect(Collectors.toList());
            } else if (StringUtils.isNoneBlank(priority) && !"-".equals(priority)) {
                priorityList.add(priority);
            } else if ("-".equals(priority)) {
                priorityList.add("");
            }

            // 生产单元
            if (prdtCellList.size() != 0) {
                if (prdtCellList.contains("")) {
                    hql.append("  and (t.prdtCell in :prdtCell or t.prdtCell IS NULL) ");
                } else {
                    hql.append("  and t.prdtCell in :prdtCell ");
                }
                paramList.put("prdtCell", prdtCellList);
            } else if (prdtCell != null && prdtCellList.size() == 0) {
                return new PaginationBean<AlarmEventH1Cache>();
            }
            // 报警点
            if (alarmPointList.size() != 0) {
                if (alarmPointList.contains("")) {
                    hql.append("  and (t.alarmPoint in :alarmPoint or t.alarmPoint IS NULL) ");
                } else {
                    hql.append("  and t.alarmPoint in :alarmPoint ");
                }
                paramList.put("alarmPoint", alarmPointList);
            } else if (alarmPoint != null && alarmPointList.size() == 0) {
                return new PaginationBean<AlarmEventH1Cache>();
            }
            // 报警标识
            if (alarmFlagList.size() != 0) {
                if (alarmFlagList.contains("")) {
                    hql.append("  and (t.alarmFlag in :alarmFlag or t.alarmFlag IS NULL) ");
                } else {
                    hql.append("  and t.alarmFlag in :alarmFlag ");
                }
                paramList.put("alarmFlag", alarmFlagList);
            } else if (alarmFlag != null && alarmFlagList.size() == 0) {
                return new PaginationBean<AlarmEventH1Cache>();
            }
            // 源事件类型
            if (eventTypeList.size() != 0) {
                if (eventTypeList.contains("")) {
                    hql.append("  and (t.eventType in (:eventType) or t.eventType IS NULL) ");
                } else {
                    hql.append("  and t.eventType in :eventType ");
                }
                paramList.put("eventType", eventTypeList);
            } else if (eventTypeSource != null && eventTypeList.size() == 0) {
                return new PaginationBean<AlarmEventH1Cache>();
            }
//            // 源事件名称
//            if (eventNameList.size() != 0) {
//                if (eventNameList.contains("")) {
//                    hql.append("  and (t.eventName in :eventName or t.eventName IS NULL) ");
//                } else {
//                    hql.append("  and t.eventName in :eventName ");
//                }
//                paramList.put("eventName", eventNameList);
//            }
            // 优先级
            if (priorityList.size() != 0) {
                if (priorityList.contains("")) {
                    hql.append("  and (t.priority in :priority or t.priority IS NULL) ");
                } else {
                    hql.append("  and t.priority in :priority ");
                }
                paramList.put("priority", priorityList);
            } else if (priority != null && priorityList.size() == 0) {
                return new PaginationBean<AlarmEventH1Cache>();
            }
            hql.append(" order by t.writeTime desc,t.dcs.name, t.alarmPoint asc ");
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取计量单位未配置数据分页
     *
     * @param dcsCodeIds        DcsCodeID
     * @param measUnit         计量单位
     * @param reson            不一致原因
     * @param startTime       查询开始时间
     * @param endTime         查询结束时间
     * @param page
     * @return                未匹配数据
     * @throws Exception
     * <AUTHOR>  2018-04-19
     */
    @Override
    public PaginationBean getUnconfiguredMeasUnitList(Long[] dcsCodeIds, String measUnit, Long reson, Date startTime, Date endTime, Pagination page) {
        StringBuilder sql = new StringBuilder("SELECT DISTINCT T.*,DCS.NAME DCSNAME FROM (");
        String unExistSql = "SELECT C1.EVENT_ID,C1.WRITE_TIME,C1.DCS_CODE,C1.EVENTCATEGORY,'' EVENT_NAME,C1.LOCATION_TAG_NAME,C1.SOURCETAGNAME,C1.MESSAGE," +
                " C1.CONDITIONNAME,C1.SEVERITY,C1.UNITS,C1.FTTIME,C1.ACTIVETIME,1 RESON " +
                " FROM T_AD_ALARMEVENT_H1_CACHE C1 WHERE UPPER(C1.UNITS) NOT IN " +
                " (SELECT DISTINCT UPPER(MU.SIGN) FROM T_PM_MEASUNIT MU WHERE MU.IN_USE=1)";
        String union = " UNION ";
        String differenceSql = "SELECT C1.EVENT_ID,C1.WRITE_TIME,C1.DCS_CODE,C1.EVENTCATEGORY,'' EVENT_NAME,C1.LOCATION_TAG_NAME,C1.SOURCETAGNAME,C1.MESSAGE," +
                " C1.CONDITIONNAME,C1.SEVERITY,C1.UNITS,C1.FTTIME,C1.ACTIVETIME,2 RESON " +
                " FROM T_AD_ALARMEVENT_H1_CACHE C1, T_PM_ALARMPOINT AP, T_PM_MEASUNIT M,T_AD_ALARMPRDTCELLCOMP COMP " +
                " WHERE 1=1 and AP.company_id=:companyId and COMP.company_id=:companyId" +
                " AND C1.OPC_CODE=COMP.OPC_CODE_ID"+
                " AND C1.LOCATION_TAG_NAME=COMP.PRDTCELL_SOURCE"+
                " AND C1.DCS_CODE=COMP.DCS_CODE_ID"+
                " AND COMP.PRDTCELL_ID=AP.PRDTCELL_ID"+
                " AND AP.MEASUNIT_ID = M.MEASUNIT_ID" +
                " AND M.IN_USE=1" +
                " AND UPPER(C1.UNITS) <> UPPER(M.SIGN) " +
                " AND UPPER(C1.UNITS) IN " +
                " (SELECT DISTINCT UPPER(MU.SIGN) FROM T_PM_MEASUNIT MU WHERE MU.IN_USE=1) " +
                " AND C1.SOURCETAGNAME IS NOT NULL ";
        Map<String, Object> paramList = new HashMap<>();

        if(reson == -1) {
            sql.append(unExistSql +union +differenceSql);
        } else if(reson == 1) { //不存在
            sql.append(unExistSql);
        } else if(reson == 2) { //不一致
            sql.append(differenceSql);
        } else {
            sql.append(unExistSql +union +differenceSql);
        }
        sql.append(" ) T LEFT JOIN T_PM_DCSCODE DCS ON T.DCS_CODE=DCS.DCS_CODE_ID WHERE 1=1 ");
        if(!measUnit .equals("-1")) {
            sql.append(" AND UPPER(T.UNITS)=UPPER(:MEASUNIT) ");
            paramList.put("MEASUNIT", measUnit);
        }
        if(ArrayUtils.isNotEmpty(dcsCodeIds)) {
            sql.append(" AND T.DCS_CODE IN (:DCSCODEIDS)");
            paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
        }
        sql.append(" AND T.FTTIME>=:STARTTIME AND T.FTTIME<:ENDTIME ");
        sql.append(" ORDER BY T.WRITE_TIME DESC,DCS.NAME,T.SOURCETAGNAME ");
        paramList.put("STARTTIME", startTime);
        paramList.put("ENDTIME", endTime);

        String countSql = "SELECT COUNT(*) FROM (" +sql.toString() +") T1";
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId",commonProperty.getCompanyId());
        Query countQuery = this.getEntityManager().createNativeQuery(countSql);
        this.setParameterList(countQuery, paramList);
        Long count=new BigDecimal(countQuery.getResultList().get(0)+"").longValue();

        PaginationBean resultList = new PaginationBean(page, count);
        Query query = this.getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, paramList);
        query.setFirstResult(resultList.getBeginIndex()).setMaxResults(resultList.getPageSize());
        ArrayList resultList1 = (ArrayList) query.getResultList();
        resultList.setPageList(resultList1);
        return resultList;
    }

    /**
     * 获取报警点未分类数据分页
     *
     * @param dcsCodeIds        DcsCodeID
     * @param alarmPoint         报警点
     * @param reson            不一致原因
     * @param startTime       查询开始时间
     * @param endTime         查询结束时间
     * @param page
     * @return                未匹配数据
     * @throws Exception
     * <AUTHOR>  2018-04-26
     */
    public PaginationBean getUnconfiguredAlarmPointList(Long[] dcsCodeIds,String alarmPoint,Long reson, Date startTime,Date endTime,Pagination page) {
        Map<String, Object> paramList = new HashMap<>();
        StringBuilder sql = new StringBuilder("SELECT T.*,DCS.NAME DCSNAME FROM (");
        String unExistSql = "SELECT C.EVENT_ID,C.WRITE_TIME,C.DCS_CODE,C.EVENTCATEGORY,'' EVENT_NAME,C.LOCATION_TAG_NAME,C.SOURCETAGNAME,C.MESSAGE,C.FTTIME,1 RESON,C.OPC_CODE " +
                " FROM T_AD_ALARMEVENT_H1_CACHE C " +
                " WHERE C.SOURCETAGNAME NOT IN " +
                " (SELECT DISTINCT AP.TAG FROM T_PM_ALARMPOINT AP WHERE AP.IN_USE=1 and AP.company_id=:companyId) OR C.SOURCETAGNAME IS NULL";
        String union = " UNION ";
        String differenceSql = " SELECT C1.EVENT_ID,C1.WRITE_TIME,C1.DCS_CODE,C1.EVENTCATEGORY,'' EVENT_NAME,C1.LOCATION_TAG_NAME,C1.SOURCETAGNAME,C1.MESSAGE,C1.FTTIME,2 RESON,C1.OPC_CODE " +
                " FROM T_AD_ALARMEVENT_H1_CACHE C1 INNER JOIN T_AD_ALARMPRDTCELLCOMP COMP ON C1.LOCATION_TAG_NAME=COMP.PRDTCELL_SOURCE AND C1.OPC_CODE=COMP.OPC_CODE_ID" +
                " INNER JOIN T_PM_ALARMPOINT AP ON C1.SOURCETAGNAME=AP.TAG " +
                " AND COMP.PRDTCELL_ID<>AP.PRDTCELL_ID and AP.company_id=:companyId AND COMP.company_id=:companyId";
        if(reson == -1) {
            sql.append(unExistSql +union +differenceSql);
        } else if(reson == 1) { //不存在
            sql.append(unExistSql);
        } else if(reson == 2) { //不一致
            sql.append(differenceSql);
        } else {
            sql.append(unExistSql +union +differenceSql);
        }
        sql.append(" ) T LEFT JOIN T_PM_DCSCODE DCS ON T.DCS_CODE=DCS.DCS_CODE_ID WHERE 1=1 ");
        if(ArrayUtils.isNotEmpty(dcsCodeIds)) {
            sql.append(" AND T.DCS_CODE IN (:DCSCODEIDS)");
            paramList.put("DCSCODEIDS", Arrays.asList(dcsCodeIds));
        }
        if(!alarmPoint.equals("-1") && !alarmPoint.equals("") && !alarmPoint.equals("-")){
            sql.append(" AND T.SOURCETAGNAME=:ALARMPOINT");
            paramList.put("ALARMPOINT", alarmPoint);
        }
        if(alarmPoint.equals("-")){
            sql.append(" AND T.SOURCETAGNAME IS NULL");
        }
        sql.append(" AND T.FTTIME>=:STARTTIME AND T.FTTIME<:ENDTIME ");
        sql.append(" ORDER BY T.WRITE_TIME DESC,DCS.NAME,T.SOURCETAGNAME ");
        paramList.put("STARTTIME", startTime);
        paramList.put("ENDTIME", endTime);
//企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId",commonProperty.getCompanyId());
        String countSql = "SELECT COUNT(*) FROM ( " +sql.toString() +" ) T1";
        Query countQuery = this.getEntityManager().createNativeQuery(countSql);
        this.setParameterList(countQuery, paramList);
        Long count=new BigDecimal(countQuery.getResultList().get(0)+"").longValue();

        PaginationBean resultList = new PaginationBean(page, count);
        Query query = this.getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, paramList);
        query.setFirstResult(resultList.getBeginIndex()).setMaxResults(resultList.getPageSize());
        ArrayList resultList1 = (ArrayList) query.getResultList();
        resultList.setPageList(resultList1);
        return resultList;
    }
}
