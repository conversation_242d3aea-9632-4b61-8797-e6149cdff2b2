var alarmGroupCurrentUrl = OPAL.API.pmUrl + '/alarmPointGroupConfig/getCurrentGroupList';  //二级列表
var alarmGroupUrl = OPAL.API.pmUrl + '/alarmPointGroupConfig/getAlarmPointGroups'; //一级列表
// var alarmGroupCurrentUrl = OPAL.API.afUrl + '/alarmDurationStatt';  //二级列表
// var alarmGroupUrl = OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattMain'; //一级列表
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";   //装置树
var getShowTimeUrl = OPAL.API.commUrl + '/getShowTime';
var alarmFlagListUrl = OPAL.API.adUrl + '/alarmEvents/getAlarmFlagList';
var delUrl = OPAL.API.pmUrl + '/alarmPointGroupConfig/delete';
var delSonUrl = OPAL.API.pmUrl + '/alarmPointGroupConfig/delete-dtl';
var isLoading = true;
var unitId;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            /**
             *绑定事件
             */
            this.bindUi();
            // //初始化查询装置树
            page.logic.initUnitTree();
            //初始化表格
            page.logic.initOpetateTable();
            // 查询
            page.logic.search();

        },
        bindUi: function () {
            // 新增
            $('#AlarmGroupAdd').click(function () {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            });
            //批量删除
            $('#AlarmGroupDel').click(function () {
                page.logic.delAll();
            });
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                $('#MostAlarmOperateTable').bootstrapTable('resetView');
            };
            /**
             * 查询
             */
            $('#btnSearch').click(function () {
                isLoading = false;
                page.logic.search();
            })
        },
        data: {
            param: {},
            subParam: {}
        },

        logic: {
            /***
             * 查询
             */
            search: function () {
                page.data.param = OPAL.form.getData("searchForm");
                page.logic.queryMostOperate();
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                }, false, function () {
                });
            },
            /**
             * 加载 下方表格
             */
            queryMostOperate: function () {
                $("#MostAlarmOperateTable").bootstrapTable('refresh', {
                    "url": alarmGroupUrl,
                    "pageNumber": 1
                });
            },
            //初始化下方表格
            initOpetateTable: function () {
                page.logic.initBootstrapTable("MostAlarmOperateTable", {
                    detailView: true,
                    cache: false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '90px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'unitName',
                        title: '装置',
                        align: 'left',
                    }, {
                        field: 'groupName',
                        title: '分组名称',
                        align: 'left',
                    }, {
                        field: 'alarmFlagName',
                        title: '报警标识',
                        align: 'left',
                    }, {
                        field: 'des',
                        title: "描述",
                        align: 'left',
                    }],
                    onExpandRow: function (index, row, $detail) {
                        page.data.subParam.alarmPointGroupId = row['alarmPointGroupId'];
                        page.logic.initCausalSubTable(index, row, $detail);
                    }
                }, page.logic.queryParams);
                var tableOption = $('#MostAlarmOperateTable').bootstrapTable('getOptions');
                $("#MostAlarmOperateTable").bootstrapTable('refreshOptions', tableOption);

                $('#MostAlarmOperateTable').colResizable({
                    liveDrag: true
                });
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.edit(\'' + rowData.alarmPointGroupId + '\',\'' + rowData.unitName + '\')">编辑</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.alarmPointGroupId + '\',\'' + arguments[2] + '\')" >删除</a> '
                ]
            },
            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },

            /**
             * 初始化二级列表
             */
            initCausalSubTable: function (index, row, $detail) {
                let subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                OPAL.ui.initBootstrapTable(subId, {
                    url: alarmGroupCurrentUrl,
                    striped: true,
                    pagination: false,
                    columns: [{
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '90px',
                        formatter: function (value, row, index) {
                            return '<a name="TableDelete" href="javascript:window.page.logic.delSingleSon(\'' + row.dtlId + '\',\'' + subId + '\'\,\'' + index + '\'\)">删除</a>'
                        }
                    }, {
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        align: 'center',
                        width: '80'
                    }, {
                        field: 'cellName',
                        title: '生产单元',
                        align: 'left',
                    }, {
                        field: 'tag',
                        title: '位号',
                        align: 'left',
                    }],
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                }, page.data.subParam)
            },

            initBootstrapTable: function (tableID, config, queryParams) {
                var _config = {
                    method: 'get',
                    url: '',
                    cache: false,
                    pagination: true, //启动分页
                    pageSize: 10,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "server", // 表示服务端请求 后台分页
                    //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder
                    //设置为limit可以获取limit, offset, search, sort, order
                    queryParamsType: "undefined",
                    queryParams: queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#' + tableID).find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                };
                $.extend(true, _config, config);
                $('#' + tableID).bootstrapTable(_config);
                $("#" + tableID).colResizable({
                    liveDrag: true
                });
            },

            /**
             * 批量删除
             */
            delAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#MostAlarmOperateTable").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmPointGroupId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 单条主删除
             */
            delSingle: function (id, index) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    // $("#MostAlarmOperateTable tbody tr").eq(index).remove();
                    $.ajax({
                        url: delUrl,
                        async: false, //
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    $('#MostAlarmOperateTable').bootstrapTable('selectPage', 1);
                                    page.logic.queryMostOperate();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 单条子表删除
             */
            delSingleSon: function (id, idName, index) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {

                    $.ajax({
                        url: delSonUrl,
                        async: false, //
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    // $('#'+idName + ' tbody tr').eq(index).remove() ;
                                    $("#" + idName).bootstrapTable('refresh');
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 新增
             */
            add: function () {
                var pageMode = PageModelEnum.NewAdd;
                var title = "报警点分组配置新增";
                page.logic.detail(title, "", pageMode);
            },
            /**
             * 编辑
             * @param alarmPointGroupId
             */
            edit: function (id) {
                var pageMode = PageModelEnum.Edit;
                var title = "报警点分组配置编辑";
                page.logic.detail(title, id, pageMode);
            },
            /**
             * 装置新增或者编辑详细页面
             */
            detail: function (title, ID, pageMode) {
                layer.open({
                    type: 2,
                    title: title,
                    closeBtn: 1,
                    area: ['1200px', '600px'],
                    shadeClose: false,
                    content: 'AlarmPointGroupConfigDtl.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "alarmPointGroupId": ID,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
        }
    };
    page.init();
    window.page = page;
});