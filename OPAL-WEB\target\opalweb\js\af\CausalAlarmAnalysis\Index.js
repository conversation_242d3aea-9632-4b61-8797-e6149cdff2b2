var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var searchUrl = OPAL.API.commUrl+'/getCausalAlarmAnalysis';
var getCausalAlarmAnalysisTableUrl = OPAL.API.commUrl+'/getCausalAlarmAnalysisTable';
var alarmFlagId;
var alarmPointId;
var isLoading = true;
$(function() {
    var page = {
        init: function() {
            this.bindUi();
            //扩展日期插件
            OPAL.util.extendDate();
            // 初始化时间
            page.logic.initTime();
            //初始化查询装置树
            page.logic.initUnitTree();
            page.logic.initTable();

            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("CausalAlarmAnalysis");
                if (cookieValue!=null&&cookieValue!=undefined&&cookieValue.length>0) {
                    $('#unitIds').combotree('setValue', cookieValue[0]);
                    page.logic.search();
                }
            }
        },
        bindUi: function() {
            $('#btnSearch').click(function() {
                isLoading = false;
                page.logic.search();
            })
        },
        data: {
            // 设置查询参数
            param: {},
            subParam: {}
        },
        logic: {
            initTable: function() {
            	OPAL.ui.initBootstrapTable2('tb_agentService',{
            		detailView: true, //父子表
            		columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1 ) * data.pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'tag',
                        title: '位号',
                        align: 'left',
                        width: '200px'
                    }, {
                        field: 'location',
                        title: '位置',
                        align: 'left',
                        width: '200px'
                    }, {
                        field: 'prdtCellSname',
                        title: '生产单元',
                        align: 'left',
                        width: '200px'
                    }, {
                        field: 'alarmFlagName',
                        title: '报警等级',
                        align: 'center',
                        width: '200px'
                    }, {
                        field: 'count',
                        title: '数量',
                        align: 'right',
                        width: '100px'
                    }],
                    onExpandRow: function(index, row, $detail) {
                        page.logic.initSubTable(index, row, $detail);
                    }
            	},page.logic.queryParams,"btnSearch")
            },
            initSubTable: function(index, row, $detail) {
                alarmPointId = row.alarmPointId;
                alarmFlagId = row.alarmFlagId;
                var subId = 'sub_table'+index;
                $detail.html('<table></table>').find('table').attr('id',subId);
				OPAL.ui.initBootstrapTable(subId,{
					url: getCausalAlarmAnalysisTableUrl,
					striped: true, 
                    pagination: false,
					columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            return index + 1 ;
                        },
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'tagName',
                        title: '子位号',
                        align: 'left',
                        width: '200px'
                    }, {
                        field: 'location',
                        title: '位置',
                        align: 'left',
                        width: '200px'
                    }, {
                        field: 'prdtCellName',
                        title: '生产单元',
                        align: 'left',
                        width: '150px'
                    }, {
                        field: 'alarmFlagName',
                        title: '报警标识',
                        align: 'center',
                        width: '100px'
                    }, {
                        field: 'alarmTimes',
                        title: '报警数',
                        align: 'right',
                        width: '100px'
                    }, {
                        field: 'forecast',
                        title: '可预测性(%)',
                        align: 'right',
                        width: '120px'
                    }, {
                        field: 'important',
                        title: '重要的(%)',
                        align: 'right',
                        width: '120px'
                    }],
                    formatNoMatches: function () {
			            return "该位号和报警标识下没有查询到数据！";
			        },
                    responseHandler: function (res) {
			            var item = {
			                "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
			                "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
			                "total": $.ET.getPageInfo(res)[0]["total"],
			                "rows": $.ET.toObjectArr(res)
			            };
			            return item;
			        },
				},page.logic.subQueryParams)
            },
            /**
             * 查询父表格参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) {
                var param = {
                    isCausalAlarmAnalysis:true,
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    now: Math.random()
                };
                return $.extend(page.data.param,param);
            },
            /**
             * 查询子表格参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            subQueryParams: function(p) {
                var param = {
                    isCausalAlarmAnalysis:true,
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    alarmPointId: alarmPointId,
                    alarmFlagId: alarmFlagId,
                    now: Math.random()
                };
                return $.extend(page.data.subParam,param);
            },
            search: function() {
            	var data = $('#formSearch').serialize();
            	var searchUnitId = $('#unitIds').val();
                var searchPrdtCell = OPAL.ui.getComboMultipleSelect.getValues('prdtCellIds');
                var startTime = $('#startTime').val();
                var endTime = $('#endTime').val();
                if (!page.logic.checkUnit($('#unitIds').val())) return;
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                page.data.param = {
                    unitIds: searchUnitId,
                    prdtIds: searchPrdtCell,
                    startTime:startTime,
                    endTime:endTime,
                    now: Math.random(),
                };
                page.data.subParam = {
                    unitId: searchUnitId,
                    startTime:startTime,
                    endTime:endTime,
                    now: Math.random()
                };
                $("#btnSearch").attr('disabled', true);
                $('#tb_agentService').bootstrapTable('refresh',{
                    "url": searchUrl,
                    "pageNumber": 1
                })
            },
            checkUnit: function(unitVal) {
                if (unitVal == '' || unitVal == undefined || unitVal == null) {
                    layer.msg('请选择装置！');
                    return false
                } else {
                    return true
                }
            },
            /**
             * 初始化 时间
             */
            initTime: function() {
                OPAL.ui.initDateTimePeriodPicker({
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm:ss'
                })
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    multiple: false,
                    onlyLeafCheck: true,
                    onChange: function(node) {
                        page.logic.searchUnitPrdt(node);
                    }

                }, false);
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function() {
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            }


        }
    }
    page.init();
    window.page = page;

});