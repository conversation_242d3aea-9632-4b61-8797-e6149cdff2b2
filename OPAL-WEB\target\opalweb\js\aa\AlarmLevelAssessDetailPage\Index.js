var searchUrl = OPAL.API.aaUrl + '/alarmAssessFirstPages/getAlarmLevelAssessDetail';
var alarmLevelUrl = OPAL.API.aaUrl + '/alarmAssessFirstPages/getAssessLevel';
var getVariationTrendUrl = OPAL.API.aaUrl + '/alarmAssessFirstPages/getVariationTrend';
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.Refresh;
var alarmTime;
var avgAlarmChart,peakAlarmChart,excitationChart;
$(function() {
	var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
	var page = {
		/**
		 * 初始化
		 */
		init: function() {
			this.bindUi();
			page.logic.pageChange();
			page.logic.initAlarmLevelList();

		},
		bindUi: function() {
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                avgAlarmChart.resize();
                peakAlarmChart.resize();
                excitationChart.resize();
            };
			// 关闭
			$('#closePage').click(function() {
				window.pageLoadMode = PageLoadMode.None;
				page.logic.closeLayer(false);
			});
			//表格自适应页面拉伸宽度
			$(window).resize(function () {
                $('#table').bootstrapTable('resetView');
                $('#avgAlarmTable').bootstrapTable('resetView');
                $('#peakAlarmTable').bootstrapTable('resetView');
                $('#excitationTable').bootstrapTable('resetView');
            });
		},
		logic: {
			/**
			 * 图形和列表切换的样式
			 */
			pageChange: function() {
				$('.myTab li').click(function() {
					var flag = $(this).attr('showFlag');
					if (flag == 'imgShow') {
						$(this).find('img').attr('src', '../../../images/one1.png');
						$(this).siblings().find('img').attr('src', '../../../images/tweo.png');

					} else if (flag == 'tableShow') {
						$(this).find('img').attr('src', '../../../images/tweos.png');
						$(this).siblings().find('img').attr('src', '../../../images/one.png');

					}
				})
			},
			/**
			 * 获得固定的时间点
			 */
			getQueryTime: function(dateTimeList, unitIdList, id) {
				OPAL.util.getQueryTime(function(queryTime) {
					queryTimeArray = queryTime;
					page.logic.initTable();
					page.logic.setTable();
					//初始化页面
					page.logic.initPage(queryTimeArray, dateTimeList, unitIdList, id);
				});
			},
			setData: function(data) {
				var dateTimeList = data.dateTimeList;
				var unitIdList = data.unitIdList;
				var id = data.assessLevel;
				alarmTime = data.alarmTime;
				$('#alarmLevel').val(id);
				// 初始化 报警时间的时间点
				page.logic.getQueryTime(dateTimeList, unitIdList, id);
				//查询
				$('#search').click(function() {
					flag = false;
					$('#search').attr('disabled', true);
					page.logic.initPage(queryTimeArray, dateTimeList, unitIdList)
				})
			},

			/**
			 * 初始化页面
			 */
			initPage: function(queryTimeArray, dateJason, assessLevelJason, id) {
				if (dateJason == ""){
					$('#search').attr('disabled', false);
					return;
				}
				if (id != '' && id != null && id != undefined) {
					var assessLevel = id;
				} else {
					var assessLevel = $('#alarmLevel').val();
				}
				$.ajax({
					url: searchUrl,
					async: false,
					data: {
						dateJason: dateJason,
						assessLevelJason: assessLevelJason,
						assessLevel: assessLevel
					},
					dataType: "JSON",
					contentType: "X-WWW-FORM-URLENCODED",
					type: 'GET',
					success: function(result) {
						flag = true;
						$('#search').attr('disabled', false);
						if (avgAlarmChart && !avgAlarmChart.isDisposed()) {
		                    avgAlarmChart.clear();
		                    avgAlarmChart.dispose();
		                }
		                if (peakAlarmChart && !peakAlarmChart.isDisposed()) {
		                    peakAlarmChart.clear();
		                    peakAlarmChart.dispose();
		                }
		                if (excitationChart && !excitationChart.isDisposed()) {
		                    excitationChart.clear();
		                    excitationChart.dispose();
		                }
						var res = $.ET.toObjectArr(result);
						if (res[0].gridViewEntityList == '') {
							 $('#unitName').html('');
							avgAlarmChart = OPAL.ui.chart.initEmptyChart('avgAlarmChart');
							peakAlarmChart = OPAL.ui.chart.initEmptyChart('peakAlarmChart');
							excitationChart = OPAL.ui.chart.initEmptyChart('excitationChart');
							page.logic.initTable([], dateJason, queryTimeArray);
							page.logic.initUnitTable([], [], queryTimeArray);
							$('#avgAlarmTable tbody').html('<tr class="no-records-found"> <td colspan="3"></td></tr>')
							$('#peakAlarmTable tbody').html('<tr class="no-records-found"> <td colspan="3"></td></tr>')
							$('#excitationTable tbody').html('<tr class="no-records-found"> <td colspan="3"></td></tr>')
							return false;
						} 
						var gridViewDataList = JSON.parse(res[0].gridViewEntityList);
						var variationTrendDataListArr = JSON.parse(res[0].variationTrendList);
						var variationTrendDateArr = JSON.parse(res[0].variationTrendDate);
						page.logic.initTime(variationTrendDateArr);
						page.logic.initTable(gridViewDataList, dateJason, queryTimeArray);
						page.logic.initCharts(variationTrendDataListArr, variationTrendDateArr, queryTimeArray);
						page.logic.initUnitTable(variationTrendDataListArr, variationTrendDateArr, queryTimeArray);
						// 跳转报警分析页面
						page.logic.alarmAnalysisJump(dateJason, assessLevelJason);
					},
					error: function(result) {
						var errorResult = $.parseJSON(result.responseText);
						layer.msg(errorResult.collection.error.message);
					}
				})
			},
			/**
			 * 初始化时间
			 */
			initTime: function(dataArr) {
				startDate = dataArr[0].replace(/\//g, '-');
				endDate = dataArr[(dataArr.length - 1)].replace(/\//g, '-');
				var str = '时间：' + startDate + ' 至 ' + endDate;
				$('#showTime').html(str);
			},
			/**
			 * 初始化表格
			 */
			initTable: function(dataArr, dateJason, queryTimeArray) {
				$('#table').bootstrapTable({
					height: '200',
					columns: [{
						title: "序号",
						formatter: function(value, row, index) {
							return index + 1;
						},
						rowspan: 1,
						align: 'center',
						width: '80'
					}, {
						field: 'unitName',
						title: '装置',
						align: 'left',
						formatter: function (value, row, index) {
							var str = '<a href="javascript:;" style="text-decoration: underline;color: #348fe2" class="alarmAnalysisJump2" dataId="'+row.unitId+'">'+value+'</a>'
							return str;
						}
					}, {
						field: 'avgAlarmRate',
						title: '平均报警率',
						align: 'right',
						formatter: function(value,row,index) {
							var str = value+'<img src="../../../images/' + row.avgAlarmContrastImg + '.png" class="alarm-level-image-box">';
							return str;
						}
					},{
						field: 'peakAlarmRate',
						title: '峰值报警率',
						align: 'right',
						formatter: function(value,row,index) {
							var str = value+'<img src="../../../images/' + row.peakAlarmContrastImg + '.png" class="alarm-level-image-box">';
							return str;
						}
					},{
						field: 'excitationRate',
						title: '扰动率(%)',
						align: 'right',
						formatter: function(value,row,index) {
							var str = value+'<img src="../../../images/' + row.excitationContrastImg + '.png" class="alarm-level-image-box">';
							return str;
						}
					},{
						field: 'assessLevelName',
						title: '评估等级',
						align: 'center',
						formatter: function(value,row,index) {
							var str = '<a class="alarm-level-style assessLevelColor' + row.assessLevel + '">' + row.assessLevelName + '</a>';
							return str;
						}
					}],
					pagination: false,
					formatNoMatches: function() {
						return "";
					},
					formatLoadingMessage: function() {
						return "";
					}
				})
				var data = new Array();
				
				
				if(dataArr != unitName && dataArr != null){
					$.each(dataArr,function(i,el) {
						var avgAlarmContrastImg = page.logic.getStateComparisonEnum(el['avgAlarmContrast']);
						var peakAlarmContrastImg = page.logic.getStateComparisonEnum(el['peakAlarmContrast']);
						var excitationContrastImg = page.logic.getStateComparisonEnum(el['excitationContrast']);
						var s = {};
						s.unitId = el.unitId;
						s.unitName = el.unitName;
						s.avgAlarmRate = el.avgAlarmRate;
						s.peakAlarmRate = el.peakAlarmRate;
						s.excitationRate = el.excitationRate;
						s.assessLevelName = el.assessLevelName;
						s.avgAlarmContrastImg = avgAlarmContrastImg;
						s.peakAlarmContrastImg = peakAlarmContrastImg;
						s.excitationContrastImg = excitationContrastImg;
						s.assessLevel = el.assessLevel;
						data.push(s)
					})
					$("#table").bootstrapTable("load", data);
					$("#table").bootstrapTable("refresh");
					if(data.length != 0){
						$("#table tbody tr:eq(0)").addClass("selectedd");
					}
				}
				// 点击表格 更改趋势图
				// page.logic.getVariationTrend(dateJason, queryTimeArray,data);
			},
			/**
			 * 判断 上升下降率
			 */
			getStateComparisonEnum: function(contrast) {
				switch (contrast) {
					case 1:
						return 'uprat'
						break;
					case 2:
						return 'down'
						break;
					case 3:
						return 'rope'
						break;
				};
			},
			/**
			 * 初始化 图表
			 */
			initCharts: function(data, xAxisArray, queryTimeArray) {
				var unitName = '';
				if (data[1].length != 0) {
					unitName = data[1][0].unitName;
					 $('#unitName').html(unitName);
				}
				var avgAlarmRateArray = data[1][0].alarmRate;
				var peakAlarmRateArray = data[2][0].alarmRate;
				var excitationRateArray = data[3][0].alarmRate;
				avgAlarmChart = echarts.init(document.getElementById('avgAlarmChart'));
				peakAlarmChart = echarts.init(document.getElementById('peakAlarmChart'));
				excitationChart = echarts.init(document.getElementById('excitationChart'));
				var option = page.logic.setOption(xAxisArray, unitName);
				avgAlarmChart.setOption(option);
				peakAlarmChart.setOption(option);
				excitationChart.setOption(option);
				page.logic.changeOption(avgAlarmChart, avgAlarmRateArray, queryTimeArray, '平均报警率', unitName, '46baef', 'eefaff', '');
				page.logic.changeOption(peakAlarmChart, peakAlarmRateArray, queryTimeArray, '峰值报警率', unitName, 'ffafa7', 'ffafa7', '');
				page.logic.changeOption(excitationChart, excitationRateArray, queryTimeArray, '扰动率', unitName, '82e2e5', '82e2e5', '%');
			},
			/**
			 * 设置图表的配置
			 *
			 */
			setOption: function(xAxisArray, dataArr, unitName) {
				option = {
					tooltip: {
						trigger: 'item'
					},
					grid: {
						left: '6%',
						right: '2%',
						bottom: '1%',
						top: '2%',
						height: '250px',
						containLabel: true
					},
					calculable: true,
					xAxis: [{
						type: 'category',
						boundaryGap: false,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						},
						axisLabel:  {     
							interval: 0,
							rotate: 60,
							textStyle: {
                                fontSize: 11
                            }   
						},
						data: xAxisArray
					}],
					yAxis: [{
						type: 'value',
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						},
					}],
					series: [{
						name: unitName,
						type: 'line',
						smooth: true,
						itemStyle: {
							normal: {
								color: '#46baef',
								areaStyle: {
									color: '#eefaff'
								}
							}
						},

					}]
				};
				return option;
			},
			changeOption: function(charts, dataArr, queryTimeArray, promptData, unitName, color1, color2, percentage) {
				charts.setOption({
					series: [{
						name: '',
						type: 'line',
						smooth: true,
						itemStyle: {
							normal: {
								color: '#' + color1,
								areaStyle: {
									color: '#' + color2
								}
							}
						},
						areaStyle: {
							normal: {
								color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
									offset: 0,
									color: '#' + color2 // 0% 处的颜色
								}, {
									offset: 1,
									color: '#fff' // 100% 处的颜色
								}], false)
							}
						},
						data: dataArr,
						tooltip: {
							position: ['30%', '20%'],
							formatter: function(params) {
								var name = params.name.replace(/\//g, '-');
								var d1 = Date.parse(new Date(params.name));
								var d2 = d1 + (60 * 60 * 24 * 1000);
								var d3 = moment(d2).format("YYYY-MM-DD");
								var timeData = '从：' + name + ' ' + queryTimeArray + '<br>至：' + d3 + ' ' + queryTimeArray + '<br>' + unitName + '：' + params.value + percentage
								return timeData;
							}
						}
					}]
				})
			},
			/**
			 * 初始化 装置的列表
			 *
			 */
			initUnitTable: function(variationTrendDataListArr, variationTrendDateArr, queryTimeArray) {
				var data1 = [];
				var data2 = [];
				var data3 = [];
				if(variationTrendDataListArr.length != 0){
					var avgAlarmData = variationTrendDataListArr[1][0].alarmRate;
					var peakAlarmData = variationTrendDataListArr[2][0].alarmRate;
					var excitationAlarmData = variationTrendDataListArr[3][0].alarmRate;
					$.each(avgAlarmData, function(i, el) {
						var s = {};
						s.time = variationTrendDateArr[i];
						s.total = el;
						data1.push(s)
					});
					$.each(peakAlarmData, function(i, el) {
						var s = {};
						s.time = variationTrendDateArr[i];
						s.total = el;
						data2.push(s)
					});
					$.each(excitationAlarmData, function(i, el) {
						var s = {};
						s.time = variationTrendDateArr[i];
						s.total = el;
						data3.push(s)
					});
				}
				$("#avgAlarmTable").bootstrapTable("load", data1);
				$("#peakAlarmTable").bootstrapTable("load", data2);
				$("#excitationTable").bootstrapTable("load", data3);
				$("#avgAlarmTable").bootstrapTable("refresh");
				$("#peakAlarmTable").bootstrapTable("refresh");
				$("#excitationTable").bootstrapTable("refresh");
			},
			setTable: function() {
				var tableData1 = {
					height: '310',
					columns: [{
						title: "序号",
						formatter: function(value, row, index) {
							return index + 1;
						},
						rowspan: 1,
						align: 'center',
						width: '50px'
					}, {
						field: 'time',
						title: '时间',
						align: 'center',
						formatter: function(params) {
							var p = params + ' '+queryTimeArray;
							return p
						}
					}, {
						field: 'total',
						title: '合计',
						align: 'right',
						width: '120px'
					}],
					sidePagination: "client",
					pagination: false,
					formatNoMatches: function() {
						return "";
					},
					formatLoadingMessage: function() {
						return "";
					}
				}
				var tableData2 = {
					height: '310',
					columns: [{
						title: "序号",
						formatter: function(value, row, index) {
							return index + 1;
						},
						rowspan: 1,
						align: 'center',
						width: '50px'
					}, {
						field: 'time',
						title: '时间',
						align: 'center',
						formatter: function(params) {
							var p = params + ' '+queryTimeArray;
							return p
						}
					}, {
						field: 'total',
						title: '合计(%)',
						align: 'right',
						width: '120px',
						formatter:function(value, row, index) {
							if(value == "0.00") return 0;
							return value;
						}
					}],
					sidePagination: "client",
					pagination: false,
					formatNoMatches: function() {
						return "";
					},
					formatLoadingMessage: function() {
						return "";
					}
				}
				$('#avgAlarmTable').bootstrapTable(tableData1);
				$('#peakAlarmTable').bootstrapTable(tableData1);
				$('#excitationTable').bootstrapTable(tableData2);
			},
			/**
			 * 初始化查询 等级评估
			 */
			initAlarmLevelList: function() {
				OPAL.ui.getCombobox("alarmLevel", alarmLevelUrl, {
					async: false,
					selectValue: -1,
					data: {
						'isAll': true
					}
				}, null);
			},
			/**
			 * 点击table 更改趋势图
			 */
			getVariationTrend: function(dateJason, queryTimeArray,data) {
				$("#table tbody tr").click(function() {
					$("#table tbody tr").removeClass("selectedd");
					$(this).addClass("selectedd");
				});
				$('#table tbody tr').click(function() {
					var i =  $(this).attr('data-index');
					if(data.length != 0){
						var unitId = data[i].unitId;
							if (unitId == null || unitId == undefined || unitId == '') {
							unitId = -1
						}
						$.ajax({
							url: getVariationTrendUrl,
							async: false,
							data: {
								dateJason: dateJason,
								unitId: unitId
							},
							dataType: "JSON",
							contentType: "X-WWW-FORM-URLENCODED",
							type: 'GET',
							success: function(result) {
								var res = $.ET.toObjectArr(result);
								var variationTrendDataListArr = JSON.parse(res[0].variationTrendList);
								var variationTrendDateArr = JSON.parse(res[0].variationTrendDate);
							    page.logic.initCharts(variationTrendDataListArr, variationTrendDateArr, queryTimeArray);
								page.logic.initUnitTable(variationTrendDataListArr, variationTrendDateArr, queryTimeArray);
							},
							error: function(result) {
								var errorResult = $.parseJSON(result.responseText);
								layer.msg(errorResult.collection.error.message);
							}
						})
					}
					
				})
			},
			/**
			 * 关闭弹出层
			 */
			closeLayer: function(isRefresh) {
				parent.isRefresh = isRefresh;
				parent.layer.close(index);
			},
			/**
			 * 点击跳转 报警分析页面 时 传递数据
			 */
			alarmAnalysisJump: function(dateTimeList, unitIdList) {
				$('.alarmAnalysisJump2').click(function() {
					var unitId = $(this).attr('dataId');
					var unitName = $(this).text();
					layer.open({
						type: 2,
						title: '',
						closeBtn: false,
						area: ['100%', '100%'],
						shadeClose: false,
						scrollbar: false,
						content: '../../af/AlarmAnalysis/Index.html?'+ Math.random(),
						success: function(layero, index) {
							var body = layer.getChildFrame('body', index);
							var iframeWin = window[layero.find('iframe')[0]['name']];
							var data = {
								dateTimeList: dateTimeList,
								unitIdList: unitIdList,
								unitId: unitId,
								unitName: unitName,
								alarmTime: alarmTime
							};
							iframeWin.page.logic.setData(data);
						}

					})
				})
			}
		}
	}
	page.init();
	window.page = page;
})