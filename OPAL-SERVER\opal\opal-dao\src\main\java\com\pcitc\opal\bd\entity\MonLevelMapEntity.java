package com.pcitc.opal.bd.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigInteger;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_bd_monlevelmap")
public class MonLevelMapEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 报警标识ID
     */
    @TableField("alarm_flag_id")
    private Long alarmFlagId;

    /**
     * 报警标识
     */
    @TableField("alarm_flag_name")
    private String alarmFlagName;

    /**
     * 工艺参数级别ID
     */
    @TableField("mon_level_id")
    private Long monLevelId;


}
