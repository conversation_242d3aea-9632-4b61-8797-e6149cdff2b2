package com.pcitc.opal.aa.bll.imp;

import com.pcitc.opal.aa.bll.AlarmPriorityAssessService;
import com.pcitc.opal.aa.bll.entity.AlarmPriorityAssessEntity;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.CommonEnum.EventTypeEnum;
import com.pcitc.opal.common.CommonEnum.TimeFilterTypeEnum;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.ShiftDateCalculator;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.pojo.MeasUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/*
 * 优先级评估业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmPriorityAssessImpl
 * 作       者：kun.zhao
 * 创建时间：2017/10/17
 * 修改编号：1
 * 描       述：优先级评估业务逻辑层实现类
 */
@Service
public class AlarmPriorityAssessImpl implements AlarmPriorityAssessService {

    @Autowired
    private AlarmEventRepository alarmEventRepository;

    @Autowired
    private BasicDataService basicDataService;

    /**
     * 获取主列表数据
     *
     * @param unitCodes     装置ID数组
     * @param prdtCellIds 生产单元ID数组
     * @param priority    优先级
     * @param startTime   报警时间范围起始
     * @param endTime     报警时间范围结束
     * @param endFlag     时间校正标识
     * @param page        分页参数
     * @return 报警事件实体集合
     * @throws Exception
     * <AUTHOR> 2019-12-30
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<AlarmEventEntity> getAllAlarmEvent(String[] unitCodes, Long[] prdtCellIds,
                                                   Integer priority, Date startTime, Date endTime, String endFlag, Pagination page) throws Exception {
        List<UnitEntity> units = null;
        if (unitCodes == null) {
            units = basicDataService.getUnitListByIds(unitCodes, true);
            unitCodes = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        //获取查询开始和结束时间
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime, endFlag);
        startTime = shiftDateCalculator.getQueryStartTime();
        endTime = shiftDateCalculator.getQueryEndTime();

        PaginationBean<Object[]> listAlarmEvent = alarmEventRepository.getAlarmEventGroup(unitCodes, prdtCellIds, new Long[]{EventTypeEnum.ProcessEvent.getIndex()}
                , null, null, priority, null, TimeFilterTypeEnum.ALarmTime, startTime, endTime, null, page);
        if (units == null && listAlarmEvent != null) {
            // 通过公共方法获取装置
            String[] filterunitCodes = null;
            if (listAlarmEvent.getPageList().size() > 1) {
                List<Object[]> differList= listAlarmEvent.getPageList().subList(0,listAlarmEvent.getPageList().size()-1);
                filterunitCodes = differList.stream().map(x -> x[0].toString()).distinct().toArray(String[]::new);
            }
            units = basicDataService.getUnitListByIds(filterunitCodes, false);
        }
        PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<>(page, listAlarmEvent.getTotal());

        Long totalAlarmCount = 0l;
        List<AlarmEventEntity> alarmEventEntityList = new ArrayList<>();
        for(int i=0;i<listAlarmEvent.getPageList().size();i++){
//            if(i==listAlarmEvent.getPageList().size()-1) {
//                Object count = listAlarmEvent.getPageList().get(i);
//                totalAlarmCount = Long.parseLong(count.toString());
//                break;
//            }
            Object[] o = listAlarmEvent.getPageList().get(i);
            AlarmEventEntity alarmEventEntity = new AlarmEventEntity();
            // ae.unitCode,pc.prdtCellId,pc.sname,ap.alarmPointId,ap.tag,ap.des,af.alarmFlagId,af.name,count(1)
            UnitEntity ue = units.stream().filter(x -> o[0].toString().equals(x.getStdCode())).findFirst().orElse(new UnitEntity());
            // 填充装置简称
            alarmEventEntity.setUnitName(ue.getSname());
            alarmEventEntity.setUnitCode(ue.getStdCode());
            // 填充生产单元简称
            alarmEventEntity.setPrdtCellId(Long.parseLong(o[1].toString()));
            alarmEventEntity.setPrdtCellName(o[2] + "");
            // 填充报警点位号
            alarmEventEntity.setAlarmPointId(Long.parseLong(o[3].toString()));
            alarmEventEntity.setAlarmPointTag(o[4].toString());
            alarmEventEntity.setAlarmPointExplain(o[5] != null ? o[5].toString() : "");
            // 填充报警标识名称
            alarmEventEntity.setAlarmFlagId(Long.parseLong(o[6].toString()));
            alarmEventEntity.setAlarmFlagName(o[7].toString());
            // 填充总数
            alarmEventEntity.setCount(Long.parseLong(o[8].toString()));
            //事件总数
            totalAlarmCount += Long.parseLong(o[8].toString());
            alarmEventEntityList.add(alarmEventEntity);
        }
        if (totalAlarmCount != null && totalAlarmCount != 0) {
            //报警平均数为报警总数/总的天数（保留两位小数）
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            long startDateTime = dateFormat.parse(dateFormat.format(startTime)).getTime();
            long endDateTime = dateFormat.parse(dateFormat.format(endTime)).getTime();
            int day = (int) ((endDateTime - startDateTime) / (1000 * 3600 * 24));
            alarmEventEntityList.get(0).setTotalAlarmNum(totalAlarmCount);
            Double d = totalAlarmCount.doubleValue() / day;
            alarmEventEntityList.get(0).setAvgAlarmNum(String.valueOf((double) Math.round(d * 100) / 100));
        }
        returnAlarmEvent.setPageList(alarmEventEntityList);
        return returnAlarmEvent;
    }

    /**
     * 获取分页数据
     *
     * @param unitCodes     装置ID数组
     * @param prdtCellIds 生产单元ID数组
     * @param priority    优先级
     * @param startTime   报警时间范围起始
     * @param endTime     报警时间范围结束
     * @param endFlag     时间校正标识
     * @param page        分页参数
     * @return 报警事件实体集合
     * @throws Exception
     * <AUTHOR> 2017-10-17
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<AlarmEventEntity> getAlarmEvent(String[] unitCodes, Long[] prdtCellIds,
                                                          Integer priority, Date startTime, Date endTime, String endFlag,String alarmPointTag, Long alarmFlagId, Pagination page) throws Exception {
        List<UnitEntity> units = null;
        if(unitCodes == null) {
            units = basicDataService.getUnitListByIds(unitCodes,true);
            unitCodes = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        //获取查询开始和结束时间
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime, endFlag);
        startTime = shiftDateCalculator.getQueryStartTime();
        endTime = shiftDateCalculator.getQueryEndTime();

        PaginationBean<AlarmEvent> listAlarmEvent = alarmEventRepository.getAlarmEvent(unitCodes, prdtCellIds, new Long[]{EventTypeEnum.ProcessEvent.getIndex()}
                , alarmPointTag, alarmFlagId, priority, null, TimeFilterTypeEnum.ALarmTime, startTime, endTime, null, page);
        PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<AlarmEventEntity>(page, listAlarmEvent.getTotal());
        returnAlarmEvent.setPageList(ObjectConverter.listConverter(listAlarmEvent.getPageList(), AlarmEventEntity.class));
        if(units == null) {
            // 通过公共方法获取装置
            String[] filterunitCodes = listAlarmEvent.getPageList().stream().map(e -> e.getAlarmPoint().getPrdtCell().getUnitId()).distinct().toArray(String[]::new);
            units = basicDataService.getUnitListByIds(filterunitCodes, false);
        }
        // 映射字段
        for (int i = 0; i < returnAlarmEvent.getPageList().size(); i++) {
            AlarmEventEntity alarmEventEntity = returnAlarmEvent.getPageList().get(i);
            AlarmEvent alarmEvent = listAlarmEvent.getPageList().get(i);
            // 填充装置简称
            UnitEntity unit = units.stream().filter(u -> alarmEvent.getAlarmPoint().getPrdtCell().getUnitId().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
            alarmEventEntity.setUnitName(unit.getSname());
            // 填充生产单元简称
            alarmEventEntity.setPrdtCellName(alarmEvent.getAlarmPoint().getPrdtCell().getSname());
            // 填充报警点位号
            alarmEventEntity.setAlarmPointTag(alarmEvent.getAlarmPoint().getTag());
            // 填充报警标识名称
            alarmEventEntity.setAlarmFlagName(alarmEvent.getAlarmFlag().getName());
            // 填充事件类型
            alarmEventEntity.setEventTypeName(alarmEvent.getEventType().getName());
            // 填充计量单位
            MeasUnit measUnit = alarmEvent.getAlarmPoint().getMeasUnit();
            alarmEventEntity.setMeasUnitName(measUnit.getName() + "(" + measUnit.getSign() + ")");
            // 填充级别
            alarmEventEntity.setCraftRank(alarmEvent.getAlarmPoint().getCraftRank());
            alarmEventEntity.setCraftRankName(alarmEventEntity.getCraftRankName());
            //处理限值
            if(alarmEventEntity.getEventTypeId().toString().startsWith("10")){
				Long flag = alarmEventEntity.getAlarmFlagId();
				if (flag == CommonEnum.AlarmFlagEnum.PVHH.getIndex()
						&& alarmEvent.getAlarmPoint().getAlarmPointHH() != null) {
					alarmEventEntity.setLimitValue(alarmEvent.getAlarmPoint().getAlarmPointHH());
				} else if (flag == CommonEnum.AlarmFlagEnum.PVHI.getIndex()
						&& alarmEvent.getAlarmPoint().getAlarmPointHI() != null) {
					alarmEventEntity.setLimitValue(alarmEvent.getAlarmPoint().getAlarmPointHI());
				} else if (flag == CommonEnum.AlarmFlagEnum.PVLL.getIndex()
						&& alarmEvent.getAlarmPoint().getAlarmPointLL() != null) {
					alarmEventEntity.setLimitValue(alarmEvent.getAlarmPoint().getAlarmPointLL());
				} else if (flag == CommonEnum.AlarmFlagEnum.PVLO.getIndex()
						&& alarmEvent.getAlarmPoint().getAlarmPointLO() != null) {
					alarmEventEntity.setLimitValue(alarmEvent.getAlarmPoint().getAlarmPointLO());
				} else {
					alarmEventEntity.setLimitValue(null);
				}
			} else {
				alarmEventEntity.setLimitValue(null);
			}
        }
        return returnAlarmEvent;
    }

    /**
     * 获取报警事件优先级评估统计数据
     *
     * @param startTime   报警时间范围起始
     * @param endTime     报警时间范围结束
     * @param unitCodes     装置ID数组
     * @param prdtCellIds 生产单元ID数组
     * @param endFlag     时间校正标识
     * @return 报警事件优先级评估统计数据
     * @throws Exception
     * <AUTHOR> 2017-11-21
     */
    @SuppressWarnings("rawtypes")
    @Override
    public List<AlarmPriorityAssessEntity> getAlarmPriorityAssessStatisticData(Date startTime, Date endTime,
                                                                               String[] unitCodes, Long[] prdtCellIds, String endFlag) throws Exception {
        if (unitCodes == null)
            unitCodes = basicDataService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        //获取查询开始和结束时间
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime, endFlag);
        startTime = shiftDateCalculator.getQueryStartTime();
        endTime = shiftDateCalculator.getQueryEndTime();

        List statisticData = alarmEventRepository.getAlarmPriorityAssessStatisticData(startTime, endTime, unitCodes, prdtCellIds);

        //查询报警点中优先级比例
        HashMap<CommonEnum.AlarmPriorityEnum, String> priorityRatioInAp = alarmEventRepository.getPriorityRatioInAp(unitCodes, prdtCellIds);



        AlarmPriorityAssessEntity entity = new AlarmPriorityAssessEntity();
        List<AlarmPriorityAssessEntity> returnList = new ArrayList<AlarmPriorityAssessEntity>();
        returnList.add(entity);
        double tNum = Double.parseDouble(((Object[]) statisticData.get(0))[0] + "");
        if (tNum == 0) {
            entity.setTotalAlarmEvents(Integer.valueOf(((Object[]) statisticData.get(0))[0] + ""));
            entity.setSysEmergencyAlarmRate("0.00%");
            entity.setSysImportantAlarmRate("0.00%");
            entity.setSysGeneralAlarmRate("0.00%");
        } else {
            double eNum = Double.parseDouble(((Object[]) statisticData.get(0))[1] + "");
            double iNum = Double.parseDouble(((Object[]) statisticData.get(0))[2] + "");
            double gNum = Double.parseDouble(((Object[]) statisticData.get(0))[3] + "");
            entity.setTotalAlarmEvents(Integer.valueOf(((Object[]) statisticData.get(0))[0] + ""));
            entity.setSysEmergencyAlarmRate(String.format("%.2f", eNum / tNum * 100.00) + "%");
            entity.setSysImportantAlarmRate(String.format("%.2f", iNum / tNum * 100.00) + "%");
            entity.setSysGeneralAlarmRate(String.format("%.2f", gNum / tNum * 100.00) + "%");
        }

        entity.setEmergencyRatio(priorityRatioInAp.get(CommonEnum.AlarmPriorityEnum.Emergency));
        entity.setNormalRatio(priorityRatioInAp.get(CommonEnum.AlarmPriorityEnum.Normal));
        entity.setImportanceRatio(priorityRatioInAp.get(CommonEnum.AlarmPriorityEnum.Importance));
        return returnList;
    }
}
