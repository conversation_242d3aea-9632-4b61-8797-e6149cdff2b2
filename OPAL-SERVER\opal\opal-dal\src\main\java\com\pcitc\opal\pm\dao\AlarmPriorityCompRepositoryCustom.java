package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.AlarmPriorityComp;

import java.util.List;

/*
 * AlarmPriorityComp实体的Repository的JPA自定义接口
 * 模块编号：pcitc_opal_dal_interface_AlarmPriorityCompRepositoryCustom
 * 作       者：zheng.yang
 * 创建时间：2018/03/30
 * 修改编号：1
 * 描       述：AlarmPriorityComp实体的Repository的JPA自定义接口
 */
public interface AlarmPriorityCompRepositoryCustom {
    /**
     * 新增数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityComp 报警标识对照实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult addPriorityComp(AlarmPriorityComp alarmPriorityComp) throws Exception;

    /**
     * 删除数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompIds 报警标识对照主键Id集合
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult deleteAlarmPriorityComp(Long[] alarmPriorityCompIds) throws Exception;

    /**
     * 更新数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityComp 报警标识对照实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult updateAlarmPriorityComp(AlarmPriorityComp alarmPriorityComp) throws Exception;

    /**
     * 获取单条数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompId 报警标识对照ID
     * @return AlarmPriorityCompEntity 报警标识对照实体类
     * @throws Exception
     */
    AlarmPriorityComp getSingleAlarmPriorityComp(Long alarmPriorityCompId) throws Exception;

    /**
     * 获取多条数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompIds 报警标识对照ID集合
     * @return List<AlarmPriorityComp> 报警标识对照实体类集合
     * @throws Exception
     */
    List<AlarmPriorityComp> getAlarmPriorityCompList(Long[] alarmPriorityCompIds) throws Exception;
    /**
     * 获取分页数据
     *
      * <AUTHOR> 2018-03-30
     * @param dCSCodeId DCS编码ID
     * @param prioritySource 源报警优先级
     * @param priority    报警优先级ID
     * @param inUse 是否启用
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmPriorityCompEntity> 翻页对象
     */
    PaginationBean<AlarmPriorityComp> getAlarmPriorityComp(Long dCSCodeId, String prioritySource, Integer priority, Integer inUse, Pagination page) throws  Exception;

    /**
     * 校验数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityComp 报警优先级对照实体
     * @return CommonResult 消息结果类
     */
    CommonResult alarmPriorityCompValidation(AlarmPriorityComp alarmPriorityComp);
}
