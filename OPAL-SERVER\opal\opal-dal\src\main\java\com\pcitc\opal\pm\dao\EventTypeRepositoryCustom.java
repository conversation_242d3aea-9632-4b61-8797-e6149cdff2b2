package com.pcitc.opal.pm.dao;

import java.util.List;

import com.pcitc.opal.pm.pojo.EventType;

/*
 * EventType实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_EventTypeRepositoryCustom
 * 作       者：kun.zhao
 * 创建时间：2017/10/09
 * 修改编号：1
 * 描       述：EventType实体的Repository的JPA自定义接口  
 */
public interface EventTypeRepositoryCustom {

	/**
	 * 获取所有已经启用的事件类型集合
	 *
	 * <AUTHOR> 2017-10-10
	 * @return 已经启用的事件类型集合
	 */
	List<EventType> getEventType();
}
