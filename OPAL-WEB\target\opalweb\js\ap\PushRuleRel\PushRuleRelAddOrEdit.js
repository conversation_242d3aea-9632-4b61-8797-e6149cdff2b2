var addUrl = OPAL.API.apUrl + '/PushRuleRel/addAlarmPushRuleUnitRel'; //新增
var getSingleUrl = OPAL.API.apUrl + '/PushRuleRelAddOrEdit/getRelAndDetailList?apRuleUnitRelId=';  //获取详情
var allNameRuleUrl = OPAL.API.apUrl + '/PushRuleRel/findAllNameRule'; //规则名称下拉框
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";  //装置树
var alarmPriorityListUrl = OPAL.API.afUrl + "/alarmDurationStatt/getAlarmPriorityList";//优先级
var getUnitsUsedUrl = OPAL.API.apUrl + "/PushRuleRelAddOrEdit/getAllUnitsUsed"; //通过优先级和专业获取装置 
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.None;
var apRuleUnitRelId = '';
var alarmPointId = '';
var isRefresh = false;
var detailData = {
};
var allValue = [];
var BusinessType = ''; //业务类型
//专业
var majorList = [
    { value: '1', text: '工艺' },
];
// var unitByMajor = ["Z4SL0001", "Z4SL0013", "Z4SL0008","Z4SL0022","Z4SL0021"];
var unitByMajor = [];
var unitArr = [];
var num = 0; //优先级下拉框改变几次
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
            //初始化装置树
            page.logic.initUnitTree();
            //初始化专业
            page.logic.initMajorList();
            //初始化优先级
            page.logic.initPriorityList();
        },
        bindUI: function () {
            $('#saveAddModal').click(function () {
                page.logic.save();
            });
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
        },
        logic: {
            /**
             * 保存
             */
            save: function () {
                $('#unitIds').next('.textbox').find('input').attr('name', 'unitIds');
                $('#unitIds').next('.textbox').addClass('form-control-tree');
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data = page.logic.getETCollectionData('AddOrEditModal');
                //处理提交类型
                let url = '';
                if (pageMode == PageModelEnum.NewAdd) {
                    window.pageLoadMode = PageLoadMode.Reload;
                }
                else if (pageMode == PageModelEnum.Edit) {
                    window.pageLoadMode = PageLoadMode.Refresh;
                }
                $.ajax({
                    url: addUrl,
                    async: false,
                    type: 'POST',
                    data: JSON.stringify(data),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            apRuleUnitRelId = result;
                            $('#apRuleUnitRelId').val(apRuleUnitRelId);
                            $('#alarmChangeItemAdd').removeAttr('disabled');
                            layer.msg(result);
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                        pageMode = PageModelEnum.Edit;
                        window.parent.pageLoadMode = PageLoadMode.Refresh;
                        page.logic.closeLayer(true);
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },

            getETCollectionData: function (formId) {
                var formData = OPAL.form.getData(formId);
                formData.unitIds = formData.unitIds.toString();
                formData.BusinessType = BusinessType;
                var arr = new Array();
                arr.push(formData);
                return $.ET.toCollectionJson(arr);
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                detailData = JSON.parse(data.row);
                pageMode = data.pageMode;
                apRuleUnitRelId = data.apRuleUnitRelId;
                BusinessType = data.BusinessType;
                if (BusinessType == 1) {
                    $('.priorityDiv').show();
                } else {
                    $('.priorityDiv').hide();
                }
                let detailUnit = '';
                //初始化规则名称
                page.logic.initRuleNameList();
                if (pageMode == PageModelEnum.NewAdd) {
                    //根据专业优先级获取装置
                    page.logic.getUnitByMajor();
                } else {
                    $.ajax({
                        url: getSingleUrl + data.apRuleUnitRelId,
                        type: "get",
                        async: false,
                        dataType: "json",
                        success: function (data) {
                            var entity = $.ET.toObjectArr(data)[0];
                            // punits 这个是pass掉的装置
                            // cunits 这个是已关联的装置
                            unitByMajor = JSON.parse(entity.punits);
                            detailUnit = JSON.parse(entity.cunits);
                            page.logic.selectUnitFun();
                            OPAL.form.setData('AddOrEditModal', detailData);
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    });
                }
                if(pageMode == PageModelEnum.Edit){
                    $('#alarmSpeciality').attr('disabled', 'disabled');
                    $('#priority').attr('disabled', 'disabled');
                    $('#apRuleUnitRelId').val(detailData.apRuleUnitRelId);
                    $('#unitIds').combotree('setValues', detailUnit);
                }else if (pageMode == PageModelEnum.View) {
                    $("select").attr('disabled', 'disabled');
                    $("input").attr('disabled', 'disabled');
                    $('#saveAddModal').hide();
                    $('#unitIds').combobox({ disabled: true });
                    $('#unitIds').combotree('setValues', detailUnit);
                }
            },
            /**
             * 初始化查询 规则名称
             */
            initRuleNameList: function () {
                // OPAL.ui.getCombobox("alarmPushRuleId", allNameRuleUrl, {
                //     // selectValue: '-1',
                //     data: {
                //         'isAll': false,
                //         businessType: BusinessType
                //     }
                // }, function (e) {
                // });
                $.ajax({
                    url: allNameRuleUrl,
                    type: "get",
                    async: false,
                    data: {
                        'isAll': false,
                        businessType: BusinessType
                    },
                    dataType: "json",
                    success: function (data) {
                        var str = '';
                        $.each(data, function (i, el) {
                            str += '<option value="' + el.alarmPushRuleId + '">' + el.name + '</option>';
                        });
                        $('#alarmPushRuleId').html(str);
                        //设置默认值
                        $('#alarmPushRuleId').val('1');
                    }
                });
            },
            /**
             * 初始化查询 优先级
             */
            initPriorityList: function () {
                OPAL.ui.getCombobox("priority", alarmPriorityListUrl, {
                    // selectValue: '-1',
                    data: {
                        'isAll': false
                    }
                }, function (e) {
                }, function (e) {
                    num++;
                    if (num > 1 && pageMode == PageModelEnum.NewAdd) {
                        page.logic.getUnitByMajor();
                    }
                });
            },
            /**
             * 初始化专业
             */
            initMajorList: function () {
                var str = '';
                $.each(majorList, function (i, el) {
                    str += '<option value="' + el.value + '">' + el.text + '</option>';
                });
                $('#alarmSpeciality').html(str);
                //设置默认值
                $('#alarmSpeciality').val('1');
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal', {
                    rules: {
                        unitIds: {
                            required: true
                        },
                        alarmPushRuleId: {
                            required: true,
                        },
                        alarmSpeciality: {
                            required: true,
                        },
                        priority: {
                            required: true
                        }
                    }
                })

            },
            //根据专业和优先级返回装置
            getUnitByMajor: function () {
                let obj = {
                    speciality: $('#alarmSpeciality').val(),
                    priority: BusinessType == 1 ? $('#priority').val() : ''
                }
                $.ajax({
                    url: getUnitsUsedUrl,
                    async: false,
                    type: 'get',
                    data: obj,
                    success: function (result, XMLHttpRequest) {
                        let arr = $.ET.toObjectArr(result);
                        unitByMajor = [];
                        arr.forEach(item => {
                            unitByMajor.push(item.stdCode);
                        })
                        page.logic.selectUnitFun();
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            // 选中、禁用装置
            selectUnitFun: function() {
                $('#unitIds').combotree({
                    onCheck: function(node, checked) {
                        page.logic.recursionStyle([node],'remove');
                    },
                    onLoadSuccess: function (node, data) {
                        page.logic.recursionStyle(data,'add');
                    }
                })
            },
            //递归加减样式
            recursionStyle: function(data,type){
                function hasUnit(data) {
                    data.forEach((item,index) => {
                        if(item.children) {
                            hasUnit(item.children);
                        } else {
                            unitByMajor.forEach((list) => {
                                if (list == item.id) {
                                    if (type == 'add') { //默认选中项置灰
                                        $('#' + item.domId).css({ background: '#ddd', opacity: 0.6 })
                                    } else if (type == 'remove') { //取消选择置灰项
                                        $('#unitIds').combotree('tree').tree("uncheck", item.target); //取消所有的选中
                                        $('#' + item.domId +' .tree-checkbox').removeClass('tree-checkbox1')
                                    }
                                }
                            })
                        }
                    })
                }
                hasUnit(data);
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                }, false, function () {
                    $('#unitIds').combotree('tree').tree('expandAll');
                    if (pageMode == PageModelEnum.View) {
                        // $('#unitIds').combotree('setValues', unitByMajor);
                    }
                });
            },
        }

    }
    page.init();
    window.page = page;
})