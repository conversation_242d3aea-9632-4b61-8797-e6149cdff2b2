package com.pcitc.opal.ad.bll.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class AlarmRespondEntity {

    /**
     *报警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date alarmTime;

    /**
     *优先级名称
     */
    @SuppressWarnings("unused")
    private String priorityName;

    /**
     * 响应时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date responseTime;

    /**
     * 装置名称简称
     */
    private String unitSname;

    /**
     * 生产单元简称
     */
    private String prdtCellSname;

    /**
     * 班组
     */
    private String team;

    /**
     * 位号
     */
    private String tag;

    /**
     * 报警标识名称
     */
    private String alarmFlagName;

    /**
     * 描述
     */
    private String des;

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public String getPriorityName() {
        return priorityName;
    }

    public void setPriorityName(String priorityName) {
        this.priorityName = priorityName;
    }

    public Date getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Date responseTime) {
        this.responseTime = responseTime;
    }

    public String getUnitSname() {
        return unitSname;
    }

    public void setUnitSname(String unitSname) {
        this.unitSname = unitSname;
    }

    public String getPrdtCellSname() {
        return prdtCellSname;
    }

    public void setPrdtCellSname(String prdtCellSname) {
        this.prdtCellSname = prdtCellSname;
    }

    public String getTeam() {
        return team;
    }

    public void setTeam(String team) {
        this.team = team;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
