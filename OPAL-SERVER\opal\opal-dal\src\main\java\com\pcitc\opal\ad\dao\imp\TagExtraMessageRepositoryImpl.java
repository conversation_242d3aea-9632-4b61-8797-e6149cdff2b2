package com.pcitc.opal.ad.dao.imp;

import com.pcitc.opal.ad.dao.TagExtraMessageRepositoryCustom;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.pojo.TagExtraMessage;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * TagExtraMessage实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_TagExtraMessageRepositoryImpl
 * 作       者：dageng.sun
 * 创建时间：2017/12/07
 * 修改编号：1
 * 描       述：TagExtraMessage实体的Repository实现
 */
public class TagExtraMessageRepositoryImpl extends BaseRepository<TagExtraMessage, Long> implements TagExtraMessageRepositoryCustom {

	@PersistenceContext
	EntityManager em;

	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-12-07
	 * @param alarmPointIds 报警点ID集合
	 * @return 
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteTagExtraMessage(Long[] alarmPointIds) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = " from TagExtraMessage tem where tem.alarmPointId in (:alarmPointIds)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			List<Long> alarmPointIdList = Arrays.asList(alarmPointIds);
			paramList.put("alarmPointIds", alarmPointIdList);

			TypedQuery<TagExtraMessage> query = getEntityManager().createQuery(hql, TagExtraMessage.class);
			this.setParameterList(query, paramList);
			List<TagExtraMessage> temList = query.getResultList();
			temList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}
	
	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-12-07
	 * @param temEntity 附加信息实体
	 * @return 
	 */
	@Override
	public CommonResult addTagExtraMessage(TagExtraMessage temEntity) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			this.getEntityManager().persist(temEntity);
			commonResult.setResult(temEntity);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		return commonResult;
	}

	/**
	 * 批量插入报警点附加信息
	 *
	 * <AUTHOR> 2017-12-07
	 * @param list 报警点附加信息集合
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public void batchInsert(List<TagExtraMessage> list) {
		try {
			//一次500条插入
			int batchSize = 500;
			for (int i = 0; i < list.size(); i++) {
				em.persist(list.get(i));
				if (i % batchSize == 0 && i > 0) {
					em.flush();
					em.clear();
				}
			}
		}catch (Exception e){
			throw e;
		}
	}
}

