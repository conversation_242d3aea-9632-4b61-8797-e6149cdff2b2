package com.pcitc.opal.common.bll.vo;

import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;

/*
 *  属性值实体
 * 模块编号：pcitc_wm_common_class_PropertyValueVO
 * 作    者：xuelei.wang
 * 创建时间：2018/06/26
 * 修改编号：1
 * 描    述：属性值实体
 */
public class PropertyValueVO extends BaseResRep implements Serializable {
    /**
     * 属性值ID
     */
    private String propertyValueId;
    /**
     * 属性ID
     */
    private String propertyId;
    /**
     * 属性值名称
     */
    private String propertyValueName;
    /**
     * 属性值
     */
    private String value;
    /**
     * 源ID
     */
    private String sourceId;

    public String getPropertyValueId() {
        return propertyValueId;
    }

    public void setPropertyValueId(String propertyValueId) {
        this.propertyValueId = propertyValueId;
    }

    public String getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(String propertyId) {
        this.propertyId = propertyId;
    }

    public String getPropertyValueName() {
        return propertyValueName;
    }

    public void setPropertyValueName(String propertyValueName) {
        this.propertyValueName = propertyValueName;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }
}
