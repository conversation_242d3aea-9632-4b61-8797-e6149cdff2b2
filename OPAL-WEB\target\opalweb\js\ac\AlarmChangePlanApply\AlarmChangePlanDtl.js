$(function() {
    var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
    var currentTimeUrl = OPAL.API.commUrl + "/getSysDateTime";
    var getQueryTimeUrl = OPAL.API.commUrl + '/getQueryTime';
    var delUrl = OPAL.API.acUrl + '/alarmChangePlanApply/delete';
    var saveUrl = OPAL.API.acUrl + '/alarmChangePlanApply/add';
    var getSingleUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getSingleAlarmChangePlan';
    var planDetailUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getPlanDetail';
    var auditUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getAlarmChangePlanAproByPlanId';
    var issueUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getAlarmChangePlanExtrByPlanId';
    var getTimeUrl = OPAL.API.commUrl + '/getQueryStartAndEndDate';
    var pageMode = PageModelEnum.NewAdd;
    window.pageLoadMode = PageLoadMode.Refresh;
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var alarmChangeItemArr, rowData; //维护调整事项页面相关参数
    var page = {
        /**
         * 初始化
         */
        init: function() {
            this.bindUI();
            page.logic.getTime();
        },
        bindUI: function() {
            //表格自适应页面拉伸宽度
            $(window).resize(function() {
                $('#alarmChangeItemTable').bootstrapTable('resetView');
            });
            $('#closePage').click(function() {
                page.logic.closeLayer();
            });
            //审批信息
            $('#aproBtn').click(function() {
                page.logic.AlarmChangePlanApro();
            })
        },
        data: {
            // 设置查询参数
            param: {}
        },
        logic: {
            initTable: function() {
                OPAL.ui.initBootstrapTable("alarmChangeItemTable", {
                    cache: false,
                    url: planDetailUrl,
                    columns: [{
                        title: "操作",
                        field: '',
                        rowspan: 1,
                        align: 'center',
                        formatter: page.logic.onActionRenderer,
                        width: '50px'
                    }, {
                        title: "序号",
                        field: 'unitSname',
                        formatter: function(value, row, index) {
                            var tableOption = $('#alarmChangeItemTable').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            return (index + 1) + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "生产单元",
                        field: 'prdtName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "计量单位",
                        field: 'measUnitName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "描述",
                        field: 'des',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }],
                }, page.logic.queryParams)
            },
            /**
             * 添加配置行按钮操作
             */
            onActionRenderer: function() {
                var rowData = arguments[1];
                return [
                    '<a name="TableDelete"  href="javascript:window.page.logic.subDetail(' + rowData.planDetailId + ',' + rowData.planId + ',' + rowData.alarmPointId + ',' + rowData.alarmFlagId + ',\'' + rowData.alarmFlagName + '\')" >详情</a> '
                ]
            },
            /**
             * 查询参数
             */
            queryParams: function(p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    aproType:1
                };
                return $.extend(page.data.param, param);
            },
            
            setData: function(data) {
                $("#pageTitle").text(data.title);
                pageMode = data.pageMode;
                page.data.param = {
                    'planId': data.planId,
                    'businessType': 1,
                    'aproType':data.aproType
                }
                page.logic.initTable();
                page.logic.initAlarmChangeItem();
                page.logic.initAudit();

            },
            //调整事项列表
            initAlarmChangeItem: function() {
                $.ajax({
                    url: getSingleUrl + "/" + page.data.param.planId + "?now=" + Math.random(),
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function(data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        if(entity["flowCaseId"] == "") {
                            $("#aproBtn").attr('disabled', 'disabled');
                        }
                        $.each(entity, function(i, val) {
                                $("#"+i).html(val);
                                $("#"+i).attr("title",val);
                        });
                        page.data.param.flowCaseId = entity["flowCaseId"];
                    },
                    error: function(XMLHttpRequest, textStatus) {}
                });
            },
            //审批信息
            initAudit: function(auditData) {
                $.ajax({
                    url: auditUrl + "?now=" + Math.random(),
                    data: page.data.param,
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function(data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        if (entity == undefined) {
                            $("#linkInfo").hide();
                        } else {
                            $.each(entity, function(i, val) {
                                $("#"+i).html(val);
                                $("#"+i).attr("title",val);
                            });
                            page.logic.initIssue();
                        }
                    },
                    error: function(XMLHttpRequest, textStatus) {}
                });
            },
            //下发信息
            initIssue: function() {
                $.ajax({
                    url: issueUrl + "?now=" + Math.random(),
                    data: page.data.param,
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function(data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        if (entity == undefined) {
                            $("#issuedTable").hide();
                            $("#confirmTable").hide();
                        } else {
                            $.each(entity, function(i, val) {
                                $("#"+i).html(val);
                                $("#"+i).attr("title",val);
                            });
                            page.logic.initConfirm();
                        }
                    },
                    error: function(XMLHttpRequest, textStatus) {}
                });
            },
            initConfirm: function() {
                page.data.param.businessType = 2;
                $.ajax({
                    url: issueUrl + "?now=" + Math.random(),
                    data: page.data.param,
                    type: "get",
                    async: false,
                    dataType: "json",
                    success: function(data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        if (entity == undefined) {
                            $("#confirmTable").hide();
                        } else {
                            $("#confirmUserName").html(entity.opUserName);
                            $("#confirmTime").html(entity.opTime);
                            $("#remark2").html(entity.remark);
                            $("#confirmUserName").attr("title", entity.opUserName);
                            $("#confirmTime").attr("title", entity.opTime);
                            $("#remark2").attr("title", entity.remark);
                        }
                    },
                    error: function(XMLHttpRequest, textStatus) {}
                });
            },
            //详情
            subDetail: function(planDetailId, planId, alarmPointId, alarmFlagId) {
                var pageMode = PageModelEnum.View;
                layer.open({
                    type: 2,
                    title: '维护调整事项',
                    closeBtn: 1,
                    area: ['99%', '50%'],
                    offset: '30px',
                    shadeClose: false,
                    fixed: true,
                    content: 'AlarmChangeItemAddOrEdit.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        rowData = {
                            'planDetailId': planDetailId,
                            'planId': planId,
                            'alarmPointId': alarmPointId,
                            'alarmFlagId': alarmFlagId,
                            'pageMode': pageMode,
                            'unitId': $('#unitId').val()
                        }
                        iframeWin.page.logic.setData(alarmChangeItemArr, rowData);
                    },
                    end: function() {
                        if (pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.refreshTable();
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function(isRefresh) {
                parent.layer.close(index);
            },
            getTime: function() {
                $.ajax({
                    url: getTimeUrl,
                    data: '',
                    dataType: 'json',
                    type: 'get',
                    success: function(result) {
                        alarmChangeItemArr = $.ET.toObjectArr(result);
                        var obj = new Object();
                        $.each(alarmChangeItemArr, function(i, el) {
                            if (i != 4 && i != 5) {
                                el.value = OPAL.util.strToDate(el.value).getTime();
                            }
                        })
                    }
                })
            },
            //审批信息页面
            AlarmChangePlanApro: function() {
                layer.open({
                    type: 2,
                    title: "",
                    closeBtn: false,
                    area: ['99%', '80%'],
                    shadeClose: false,
                    content: 'AlarmChangePlanApro.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        rowData = {
                            'planId': page.data.param.planId,
                            "flowCaseId": page.data.param.flowCaseId
                        }
                        iframeWin.page.logic.setData(rowData);
                    },
                    end: function(layero, index) {

                    }
                })
            }
        }
    };
    page.init();
    window.page = page;
});