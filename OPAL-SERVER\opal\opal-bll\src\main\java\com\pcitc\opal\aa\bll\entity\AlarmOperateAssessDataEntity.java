package com.pcitc.opal.aa.bll.entity;

/*
 * 报警操作评估实体
 * 模块编号：pcitc_opal_bll_class_AlarmOperateAssessDataEntity
 * 作  　者：xuelei.wang
 * 创建时间：2017-10-24
 * 修改编号：1
 * 描    述：报警操作评估实体
 */
public class AlarmOperateAssessDataEntity {
    /**
     * ID
     */
    private String id;
    /**
     * 标识ID
     */
    private Long flagId;
    /**
     * 位号
     */
    private String tag;
    /**
     * 总数
     */
    private Long total;
    /**
     * 标识名称
     */
    private String flagName;

    public String getFlagName() {
        return flagName;
    }

    public void setFlagName(String flagName) {
        this.flagName = flagName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getFlagId() {
        return flagId;
    }

    public void setFlagId(Long flagId) {
        this.flagId = flagId;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }
}
