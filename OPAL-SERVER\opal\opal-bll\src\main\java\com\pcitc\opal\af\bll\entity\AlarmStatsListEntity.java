package com.pcitc.opal.af.bll.entity;


import com.pcitc.opal.ad.dao.imp.AlarmTagUnitVO;
import com.pcitc.opal.ad.vo.AlarmRecInfoParamVO;
import com.pcitc.opal.ad.vo.AlarmRecInfoVO;
import lombok.Data;

import java.util.List;

/**
 * 工艺参数报警率数据实体
 * 作  　  者：shufei.sui
 * 创建时间：2019/12/11
 * 修改编号：1
 * 描述：工艺参数报警率数据实体
 */
@Data
public class AlarmStatsListEntity {
    /**
     * 按优先级分布
     */
    private List<CodeNameValue> priorityPreList;
    /**
     * 按报警时长分布
     */
    private List<CodeNameValue> durTime;
    /**
     * 报警总数
     */
    private List<CodeNameValue> splitDateList;
    /**
     * 限制报警总数
     */
    private List<CodeNameValue> splitDateHLList;
    /**
     * 时平均报警数
     */
    private List<PortalAlarmRateEntity> avgAlarmRateList;
    /**
     * 24小时持续报警数
     */
    private List<PortalAlarmRateEntity> avgAlarmAmountList;
    /**
     * 报警响应及时率
     */
    private List<PortalAlarmRateEntity> avgAlarmTimelyResponseList;
    /**
     * 报警处置及时率
     */
    private List<PortalAlarmRateEntity> avgAlarmTimelyDisposalList;
    /**
     * 工艺参数报警率
     */
    private List<PortalAlarmRateEntity> craftParaAlarmRateList;
    /**
     * 最频繁的报警位号
     */
    private List<AlarmTagUnitVO> tagUnitVOList;
    /**
     * 所有装置响应及时率
     */
    private Double avgAlarmTimelyResponseRate;
    /**
     * 所有装置处置及时率
     */
    private Double avgAlarmTimelyDisposalRate;
    /**
     * 时平均报警数
     */
    private Double avgAvgAlarmRate;
    /**
     * 平均工艺参数报警率
     */
    private Double avgCraftParaAlarmRate;
    /**
     * 报警记录总数据
     */
    private List<AlarmRecInfoVO> alarmRecs;

}


