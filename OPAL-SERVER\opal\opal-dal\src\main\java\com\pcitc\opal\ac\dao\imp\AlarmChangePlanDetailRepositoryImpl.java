package com.pcitc.opal.ac.dao.imp;

import com.pcitc.opal.ac.dao.AlarmChangePlanDetailRepositoryCustom;
import com.pcitc.opal.ac.pojo.AlarmChangePlanDetail;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * 报警变更方案明细实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_AlarmChangePlanDetailRepositoryImpl
 * 作    者：kun.zhao
 * 创建时间：2017/01/19
 * 修改编号：1
 * 描    述：报警变更方案明细实体的Repository实现
 */

public class AlarmChangePlanDetailRepositoryImpl extends BaseRepository<AlarmChangePlanDetail, Long>
        implements AlarmChangePlanDetailRepositoryCustom {

    /**
     * 新增报警变更方案明细
     *
     * <AUTHOR> 2018-01-22
     * @param alarmChangePlanDetail 报警变更方案明细
     * @return 返回结果信息类
     */
    @Override
    public CommonResult addAlarmChangePlanDetail(AlarmChangePlanDetail alarmChangePlanDetail){
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(alarmChangePlanDetail);
            commonResult.setResult(alarmChangePlanDetail);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

	/**
	 * 删除报警变更方案明细实体 
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangePlanDetailIds 报警变更方案明细Id数组
	 * @return 返回结果信息类
	 */
	@Override
	@Transactional
	public CommonResult deleteAlarmChangePlanDetail(Long[] alarmChangePlanDetailIds) {
		// 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            StringBuilder hql =new StringBuilder(" from AlarmChangePlanDetail t where t.planDetailId in (:alarmChangePlanDetailIds)");
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> alarmChangePlanDetailIdsList = Arrays.asList(alarmChangePlanDetailIds);
            paramList.put("alarmChangePlanDetailIds", alarmChangePlanDetailIdsList);

            TypedQuery<AlarmChangePlanDetail> query = getEntityManager().createQuery(hql.toString(), AlarmChangePlanDetail.class);
            this.setParameterList(query, paramList);
            List<AlarmChangePlanDetail> alarmChangePlanDetailList = query.getResultList();
            alarmChangePlanDetailList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 删除出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 修改报警变更方案明细
     *
     * <AUTHOR> 2018-01-22
     * @param alarmChangePlanDetail 报警变更方案明细
     * @return 返回结果信息类
     */
    public CommonResult updateAlarmChangePlanDetail(AlarmChangePlanDetail alarmChangePlanDetail){
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(alarmChangePlanDetail);
            commonResult.setResult(alarmChangePlanDetail);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

	/**
	 * 通过报警变更方案Id获取多条数据
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangePlanIds 报警变更方案Id数组
	 * @return 报警变更方案明细实体集合
	 */
	@Override
	public List<AlarmChangePlanDetail> getalarmChangePlanDetailByPlanIds(Long[] alarmChangePlanIds) {
		try {
            // 查询字符串
            String hql = "from AlarmChangePlanDetail t left join fetch t.alarmPoint ap left join fetch ap.prdtCell left join fetch t.alarmChangePlan ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (alarmChangePlanIds.length > 0) {
                hql += " where t.planId in (:alarmChangePlanIds) and case when ap.alarmPointId is not null then ap.inUse else 1  end =1";
                List<Long> alarmChangePlanIdsList = Arrays.asList(alarmChangePlanIds);
                paramList.put("alarmChangePlanIds", alarmChangePlanIdsList);
            }
            TypedQuery<AlarmChangePlanDetail> query = getEntityManager().createQuery(hql, AlarmChangePlanDetail.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 根据报警变更方案Id、报警点Id、报警标识Id获取报警变更方案明细数据
     *
     * <AUTHOR> 2018-01-22
     * @param planId       报警变更方案ID
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @return AlarmChangePlanDetail 报警变更方案明细
     */
    @Override
    public List<AlarmChangePlanDetail> getChangeDetailByPointFlag(Long planId, Long alarmPointId, Long alarmFlagId) {
        StringBuilder hql = new StringBuilder(" from AlarmChangePlanDetail t where t.planId =:planId and t.alarmPointId = :alarmPointId and t.alarmFlagId = :alarmFlagId");
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("planId", planId);
        paramList.put("alarmPointId", alarmPointId);
        paramList.put("alarmFlagId", alarmFlagId);
        TypedQuery<AlarmChangePlanDetail> query = getEntityManager().createQuery(hql.toString(), AlarmChangePlanDetail.class);
        this.setParameterList(query, paramList);
        return query.getResultList();
    }

    /**
     * 根据变更方案ID获取变更方案详情分页信息
     *
     * <AUTHOR> 2018-01-22
     * @param planId 变更方案ID
     * @param page   分页信息
     * @return 方案详情列表
     */
    @Override
    public PaginationBean<AlarmChangePlanDetail> getAlarmChangePlanDetail(Long planId, Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from AlarmChangePlanDetail t inner join fetch t.alarmFlag af inner join fetch t.alarmPoint ap inner join fetch ap.prdtCell pc inner join fetch ap.measUnit mu where 1=1 ");
            hql.append(" and ap.inUse =1 ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 过滤装置
            if (planId != null) {
                hql.append(" and t.planId=:planId ");
                paramList.put("planId", planId);
            }
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 根据planDetailId集合获取变更方案详情列表
     *
     * <AUTHOR> 2018-01-22
     * @param planDetailIds
     * @return
     */
    @Override
    public List<AlarmChangePlanDetail> getByIds(Long[] planDetailIds) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from AlarmChangePlanDetail t where 1=1 ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 过滤装置
            if (planDetailIds != null && planDetailIds.length != 0) {
                hql.append(" and t.planDetailId in(:planDetailIds) ");
                paramList.put("planDetailIds", Arrays.asList(planDetailIds));
            }
            TypedQuery<AlarmChangePlanDetail> query = this.getEntityManager().createQuery(hql.toString(), AlarmChangePlanDetail.class);
            setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

}
