<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.mapper.AlarmFlagCompMapper">

    <resultMap id="BaseResultMap" type="com.pcitc.opal.pojo.AlarmFlagComp">
            <id property="alarmflagcompId" column="alarmflagcomp_id" jdbcType="BIGINT"/>
            <result property="dcsCodeId" column="dcs_code_id" jdbcType="BIGINT"/>
            <result property="alarmFlagSource" column="alarm_flag_source" jdbcType="VARCHAR"/>
            <result property="alarmFlagId" column="alarm_flag_id" jdbcType="BIGINT"/>
            <result property="inUse" column="in_use" jdbcType="BIGINT"/>
            <result property="crtDate" column="crt_date" jdbcType="TIMESTAMP"/>
            <result property="mntDate" column="mnt_date" jdbcType="TIMESTAMP"/>
            <result property="crtUserId" column="crt_user_id" jdbcType="VARCHAR"/>
            <result property="mntUserId" column="mnt_user_id" jdbcType="VARCHAR"/>
            <result property="crtUserName" column="crt_user_name" jdbcType="VARCHAR"/>
            <result property="mntUserName" column="mnt_user_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        alarmflagcomp_id,dcs_code_id,alarm_flag_source,
        alarm_flag_id,in_use,crt_date,
        mnt_date,crt_user_id,mnt_user_id,
        crt_user_name,mnt_user_name
    </sql>
    <select id="selectAllByTenantDb" resultType="com.pcitc.opal.pojo.AlarmFlagComp">
        select * from t_ad_alarmflagcomp;
    </select>
   
<!--    <delete id="delByAlarmFlagcomp" parameterType="AlarmFlagComp">-->
<!--        delete-->
<!--        from t_ad_alarmflagcomp-->
<!--        <where>-->
<!--            <foreach collection="alarmflagcompList" item="a" open="(" close=")" separator=",">-->
<!--                <if test="a.get">-->
<!--                    -->
<!--                </if>-->
<!--            </foreach>-->
<!--        </where>-->
<!--    </delete>-->
</mapper>
