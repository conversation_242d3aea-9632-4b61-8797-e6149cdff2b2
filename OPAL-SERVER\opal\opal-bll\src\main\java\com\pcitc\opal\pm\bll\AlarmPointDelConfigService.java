package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.*;
import com.pcitc.opal.pm.pojo.AlarmPointDelConfig;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/*
 * 报警点业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_AlarmPointService
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点业务逻辑层接口 
 */
@Service
public interface AlarmPointDelConfigService {

    /**
     * 查询报警分组剔除配置
     *
     * @param unitCodes
     * @param startTime
     * @param endTime
     * @param delStatus
     */
    PaginationBean<AlarmPointDelConfigDTOEntity> getAlarmPointDelConfigPage(String[] unitCodes, String groupName, Date startTime, Date endTime, Integer inUse, Pagination page, Integer delStatus) throws Exception;

    /**
     * 获取单条数据
     *
     * @param alarmPointDelConfigId 报警剔除配置ID
     * @return AlarmPointDelConfigDTOEntity 剔除实体类
     * @throws Exception
     */
    AlarmPointDelConfigDTOEntity getSingleAlarmPointDel(Long alarmPointDelConfigId) throws Exception;

    /**
     * 删除报警点剔除数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPointDelConfigIds 报警点分组主键Id集合
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult deleteAlarmPointDel(Long[]  alarmPointDelConfigIds) throws Exception;

    /**
     * 新增报警点剔除数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPointDelConfigEntity 报警点分组明细对象
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult addAlarmPointDel(AlarmPointDelConfigEntity alarmPointDelConfigEntity) throws Exception;


    /**
     * 更新报警点剔除数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPointDelConfigEntity 报警点分组实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult updateAlarmPointDel(AlarmPointDelConfigEntity alarmPointDelConfigEntity) throws Exception;

    /**
     * 批量审批通过
     * @param alarmPointDelConfigId
     * @return
     */
    CommonResult approvePass(Integer[] alarmPointDelConfigId);

    CommonResult reject(Integer[] alarmPointDelConfigId);

    /**
     * 根据id进行数据剔除
     * @param alarmPointDelConfigId id
     * @param alarmPointDelConfigs 剔除配置实体
     */
    void delAlarmRecAndAlarmEvent(Integer[] alarmPointDelConfigId, Integer companyId, List<AlarmPointDelConfig> alarmPointDelConfigs);
}
