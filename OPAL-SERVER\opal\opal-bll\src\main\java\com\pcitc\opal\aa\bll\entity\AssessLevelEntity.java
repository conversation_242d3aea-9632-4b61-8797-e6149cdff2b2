package com.pcitc.opal.aa.bll.entity;

/*
 * 评估等级数据实体
 * 模块编号：pcitc_opal_bll_class_AssessLevelData
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/17
 * 修改编号：1
 * 描    述：评估等级数据实体
 */
public class AssessLevelEntity {
    /**
     * 等级1，超负荷的
     */
    private Long level1;
    /**
     * 等级2，反应性的
     */
    private Long level2;
    /**
     * 等级3，稳定的
     */
    private Long level3;
    /**
     * 等级4，鲁棒的
     */
    private Long level4;
    /**
     * 等级5，可预测的
     */
    private Long level5;

    /**
     * 总报警数
     */
    private Long totalAlarmNum;

    /**
     * 报警平均数
     */
    private String avgAlarmNum;

    public Long getLevel1() {
        return level1;
    }

    public void setLevel1(Long level1) {
        this.level1 = level1;
    }

    public Long getLevel2() {
        return level2;
    }

    public void setLevel2(Long level2) {
        this.level2 = level2;
    }

    public Long getLevel3() {
        return level3;
    }

    public void setLevel3(Long level3) {
        this.level3 = level3;
    }

    public Long getLevel4() {
        return level4;
    }

    public void setLevel4(Long level4) {
        this.level4 = level4;
    }

    public Long getLevel5() {
        return level5;
    }

    public void setLevel5(Long level5) {
        this.level5 = level5;
    }

    public String getAvgAlarmNum() {
        return avgAlarmNum;
    }

    public void setAvgAlarmNum(String avgAlarmNum) {
        this.avgAlarmNum = avgAlarmNum;
    }

    public Long getTotalAlarmNum() {
        return totalAlarmNum;
    }

    public void setTotalAlarmNum(Long totalAlarmNum) {
        this.totalAlarmNum = totalAlarmNum;
    }

    /*public Long getTotalAlarmNum() {
        Long total=0L;
        if(this.level1!=null)
            total+=this.level1;
        if(this.level2!=null)
            total+=this.level2;
        if(this.level3!=null)
            total+=this.level3;
        if(this.level4!=null)
            total+=this.level4;
        if(this.level5!=null)
            total+=this.level5;
        return total;
    }*/
}
