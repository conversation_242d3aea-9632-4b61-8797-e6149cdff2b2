package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.bll.entity.BasicEntity;


/*
 * 报警点实体
 * 模块编号：pcitc_opal_bll_class_AlarmPointEntity
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点实体
 */
public class AlarmPointEntity extends BasicEntity {

	/**
     * 报警点ID
     */
    private Long alarmPointId;
    /**
     * 生产单元ID
     */
    private Long prdtCellId;
    /**
     * 位号
     */
    private String tag;
    /**
     * 位置
     */
    private String location;
    /**
     * PID图号
     */
    private String pidCode;
    /**
     * 报警点类型ID
     */
    private Long  alarmPointTypeId;
    /**
     * 监测类型（1物料；2能源；3质量）
     */
    private Integer monitorType;
    /**
     * 计量单位ID
     */
    private Long measunitId;
    /**
     * 仪表类型（1监测表；2控制表）
     */
    private Integer instrmtType;
    /**
	 *  是否虚表（0否；1是）
     */
    private Integer virtualRealityFlag;
	/**
	 *  虚拟标识（0否；1是）
	 */
	private Integer virtualFlag;
    /**
     * 报警点高高报
     */
    private Double alarmPointHH;
    /**
     * 报警点高报
     */
    private Double alarmPointHI;
    /**
     * 报警点低报
     */
    private Double alarmPointLO;
    /**
     * 报警点低低报
     */
    private Double alarmPointLL;
    /**
     * 排序
     */
    private Integer sortNum;

    /**
     * 描述
     */
    private String des;
	/**
	 * 工厂名称
	 */
	private String factoryName;
	/**
	 * 车间名称
	 */
	private String workshopName;
    /**
	 * 装置名称简称
	 */
	private String unitSname;
    /**
     * 生产单元简称
     */
    private String prdtCellSname;
    /**
     * 报警点类型名称
     */
    private String alarmPointTypeName;
    /**
     * 监测类型（1物料；2能源；3质量）
     */
    @SuppressWarnings("unused")
    private String monitorTypeStr;
    /**
     * 仪表类型（1监测表；2控制表）
     */
    @SuppressWarnings("unused")
    private String instrmtTypeStr;
    /**
     *  是否虚表（0否；1是）
     */
    @SuppressWarnings("unused")
    private String virtualRealityFlagStr;
    /**
	 * 装置编码
	 */
	private String unitId;
	/**
     * 计量单位名称
     */
    private String measunitName;
	/**
	 * 符号
	 */
	private String signName;
    /**
     * 级别(1A；2B)
     */
    private Integer craftRank;
    /**
     * 级别(1A；2B)
     */
    @SuppressWarnings("unused")
	private String craftRankName;
    /**
     * 工艺卡片上限值是否包含(1是；0否)
     */
    private Integer craftUpLimitInclude;

	/**
	 * 工艺卡片上限值是否包含(1是；0否)
	 */
	private String craftUpLimitIncludeStr;

    /**
     * 工艺卡片下限值是否包含(1是；0否)
     */
    private Integer craftDownLimitInclude;

	/**
	 * 工艺卡片下限值是否包含(1是；0否)
	 */
	private String craftDownLimitIncludeStr;

    /**
     * 工艺卡片上限值
     */
    private Double craftUpLimitValue;

    /**
     * 工艺卡片下限值
     */
    private Double craftDownLimitValue;

    /**
     * 联锁上限值是否包含(1是；0否)
     */
    private Integer interlockUpLimitInclude;

	/**
	 * 联锁上限值是否包含(1是；0否)
	 */
	private Integer interlockUpLimitIncludeStr;

    /**
     * 联锁下限值是否包含(1是；0否)
     */
    private Integer interlockDownLimitInclude;

	/**
	 * 联锁下限值是否包含(1是；0否)
	 */
	private Integer interlockDownLimitIncludeStr;

    /**
     * 联锁上限值
     */
    private Double interlockUpLimitValue;

    /**
     * 联锁下限值
     */
    private Double interlockDownLimitValue;

    /**
     * 工艺卡片值
     */
    private String craftLimitValue;

    /**
     * 联锁值
     */
    private String interlockLimitValue;

	/**
	 * 数据是否标红
	 */
	private int isRed;
    /**
     * 仪表优先级（1紧急，2重要，3一般)
     */
	private Integer instrmtPriority;
	private String instrmtPriorityShow;

	/**
	 * 是否发送报警短信（1是；0否）
	 */
	private Integer inSendMsg;
	private String inSendMsgShow;

	/**
	 * 手机号（多个手机号用英文逗号隔开）
	 */
	private String mobilePhone;

	/**
	 * 报警标识名称
	 */
	private String alarmflagName;
	private String alarmflagId;

	/**
	 * 电话本
	 */
	private String mobileBook;
	private String mobileBooks;

	/**
	 * 电话本Id
	 */
	private String mobileBookId;

	private String rtdbTag;

	private Integer companyId;

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

    public Integer getInstrmtPriority() {
        return instrmtPriority;
    }

    public void setInstrmtPriority(Integer instrmtPriority) {
        this.instrmtPriority = instrmtPriority;
    }

    public String getInstrmtPriorityShow() {
        return instrmtPriorityShow;
    }

    public void setInstrmtPriorityShow(String instrmtPriorityShow) {
        this.instrmtPriorityShow = instrmtPriorityShow;
    }

    public String getSignName() {
		return signName;
	}

	public void setSignName(String signName) {
		this.signName = signName;
	}

	public String getFactoryName() {
		return factoryName;
	}

	public void setFactoryName(String factoryName) {
		this.factoryName = factoryName;
	}

	public Long getAlarmPointId() {
		return alarmPointId;
	}
	public void setAlarmPointId(Long alarmPointId) {
		this.alarmPointId = alarmPointId;
	}
	public Long getPrdtCellId() {
		return prdtCellId;
	}
	public void setPrdtCellId(Long prdtCellId) {
		this.prdtCellId = prdtCellId;
	}
	public String getTag() {
		return tag;
	}
	public void setTag(String tag) {
		this.tag = tag;
	}
	public String getLocation() {
		return location;
	}
	public void setLocation(String location) {
		this.location = location;
	}
	public String getPidCode() {
		return pidCode;
	}
	public void setPidCode(String pidCode) {
		this.pidCode = pidCode;
	}
	public Long getAlarmPointTypeId() {
		return alarmPointTypeId;
	}
	public void setAlarmPointTypeId(Long alarmPointTypeId) {
		this.alarmPointTypeId = alarmPointTypeId;
	}
	public Integer getMonitorType() {
		return monitorType;
	}
	public void setMonitorType(Integer monitorType) {
		this.monitorType = monitorType;
	}
	public Long getMeasunitId() {
		return measunitId;
	}
	public void setMeasunitId(Long measunitId) {
		this.measunitId = measunitId;
	}
	public Integer getInstrmtType() {
		return instrmtType;
	}
	public void setInstrmtType(Integer instrmtType) {
		this.instrmtType = instrmtType;
	}



    public Integer getVirtualRealityFlag() {
		return virtualRealityFlag;
	}
	public void setVirtualRealityFlag(Integer virtualRealityFlag) {
		this.virtualRealityFlag = virtualRealityFlag;
	}
	public Double getAlarmPointHH() {
		return alarmPointHH;
	}
	public void setAlarmPointHH(Double alarmPointHH) {
		this.alarmPointHH = alarmPointHH;
	}
	public Double getAlarmPointHI() {
		return alarmPointHI;
	}
	public void setAlarmPointHI(Double alarmPointHI) {
		this.alarmPointHI = alarmPointHI;
	}
	public Double getAlarmPointLO() {
		return alarmPointLO;
	}
	public void setAlarmPointLO(Double alarmPointLO) {
		this.alarmPointLO = alarmPointLO;
	}
	public Double getAlarmPointLL() {
		return alarmPointLL;
	}
	public void setAlarmPointLL(Double alarmPointLL) {
		this.alarmPointLL = alarmPointLL;
	}
	public Integer getSortNum() {
		return sortNum;
	}
	public void setSortNum(Integer sortNum) {
		this.sortNum = sortNum;
	}
	public String getDes() {
		return des;
	}
	public void setDes(String des) {
		this.des = des;
	}

	public String getWorkshopName() {
		return workshopName;
	}

	public void setWorkshopName(String workshopName) {
		this.workshopName = workshopName;
	}

	public String getUnitSname() {
		return unitSname;
	}
	public void setUnitSname(String unitSname) {
		this.unitSname = unitSname;
	}
	public String getPrdtCellSname() {
		return prdtCellSname;
	}
	public void setPrdtCellSname(String prdtCellSname) {
		this.prdtCellSname = prdtCellSname;
	}
	public String getAlarmPointTypeName() {
		return alarmPointTypeName;
	}
	public void setAlarmPointTypeName(String alarmPointTypeName) {
		this.alarmPointTypeName = alarmPointTypeName;
	}
	public String getMonitorTypeStr() {
		return CommonEnum.MonitorTypeEnum.getName(monitorType);
	}
	public void setMonitorTypeStr(String monitorTypeStr) {
		this.monitorTypeStr = monitorTypeStr;
	}
	public String getInstrmtTypeStr() {
		return CommonEnum.InstrmtTypeEnum.getName(instrmtType);
	}
	public void setInstrmtTypeStr(String instrmtTypeStr) {
		this.instrmtTypeStr = instrmtTypeStr;
	}
	public String getVirtualRealityFlagStr() {
		return CommonEnum.VirtualRealityFlagEnum.getName(virtualRealityFlag);
	}
	public void setVirtualRealityFlagStr(String virtualRealityFlagStr) {
		this.virtualRealityFlagStr = virtualRealityFlagStr;
	}
	public String getUnitId() {
		return unitId;
	}
	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}
	public String getMeasunitName() {
		return measunitName;
	}
	public void setMeasunitName(String measunitName) {
		this.measunitName = measunitName;
	}
	public Integer getCraftRank() {
		return craftRank;
	}
	public void setCraftRank(Integer craftRank) {
		this.craftRank = craftRank;
	}
	public String getCraftRankName() {
		return CommonEnum.CraftRankEnum.getName(craftRank);
	}
	public void setCraftRankName(String craftRankName) {
		this.craftRankName = craftRankName;
	}
	public Integer getCraftUpLimitInclude() {
		return craftUpLimitInclude;
	}
	public void setCraftUpLimitInclude(Integer craftUpLimitInclude) {
		this.craftUpLimitInclude = craftUpLimitInclude;
	}

	public String getCraftUpLimitIncludeStr() {
		return craftUpLimitIncludeStr;
	}

	public void setCraftUpLimitIncludeStr(String craftUpLimitIncludeStr) {
		this.craftUpLimitIncludeStr = craftUpLimitIncludeStr;
	}

	public Integer getCraftDownLimitInclude() {
		return craftDownLimitInclude;
	}
	public void setCraftDownLimitInclude(Integer craftDownLimitInclude) {
		this.craftDownLimitInclude = craftDownLimitInclude;
	}

	public String getCraftDownLimitIncludeStr() {
		return craftDownLimitIncludeStr;
	}

	public void setCraftDownLimitIncludeStr(String craftDownLimitIncludeStr) {
		this.craftDownLimitIncludeStr = craftDownLimitIncludeStr;
	}

	public Double getCraftUpLimitValue() {
		return craftUpLimitValue;
	}
	public void setCraftUpLimitValue(Double craftUpLimitValue) {
		this.craftUpLimitValue = craftUpLimitValue;
	}
	public Double getCraftDownLimitValue() {
		return craftDownLimitValue;
	}
	public void setCraftDownLimitValue(Double craftDownLimitValue) {
		this.craftDownLimitValue = craftDownLimitValue;
	}
	public Integer getInterlockUpLimitInclude() {
		return interlockUpLimitInclude;
	}
	public void setInterlockUpLimitInclude(Integer interlockUpLimitInclude) {
		this.interlockUpLimitInclude = interlockUpLimitInclude;
	}

	public Integer getInterlockUpLimitIncludeStr() {
		return interlockUpLimitIncludeStr;
	}

	public void setInterlockUpLimitIncludeStr(Integer interlockUpLimitIncludeStr) {
		this.interlockUpLimitIncludeStr = interlockUpLimitIncludeStr;
	}

	public Integer getInterlockDownLimitIncludeStr() {
		return interlockDownLimitIncludeStr;
	}

	public void setInterlockDownLimitIncludeStr(Integer interlockDownLimitIncludeStr) {
		this.interlockDownLimitIncludeStr = interlockDownLimitIncludeStr;
	}

	public Integer getInterlockDownLimitInclude() {
		return interlockDownLimitInclude;
	}
	public void setInterlockDownLimitInclude(Integer interlockDownLimitInclude) {
		this.interlockDownLimitInclude = interlockDownLimitInclude;
	}
	public Double getInterlockUpLimitValue() {
		return interlockUpLimitValue;
	}
	public void setInterlockUpLimitValue(Double interlockUpLimitValue) {
		this.interlockUpLimitValue = interlockUpLimitValue;
	}
	public Double getInterlockDownLimitValue() {
		return interlockDownLimitValue;
	}
	public void setInterlockDownLimitValue(Double interlockDownLimitValue) {
		this.interlockDownLimitValue = interlockDownLimitValue;
	}
	public String getCraftLimitValue() {
		return craftLimitValue;
	}
	public void setCraftLimitValue(String craftLimitValue) {
		this.craftLimitValue = craftLimitValue;
	}
	public String getInterlockLimitValue() {
		return interlockLimitValue;
	}
	public void setInterlockLimitValue(String interlockLimitValue) {
		this.interlockLimitValue = interlockLimitValue;
	}

	public int getIsRed() {
		return isRed;
	}

	public void setIsRed(int isRed) {
		this.isRed = isRed;
	}

	public Integer getVirtualFlag() {
		return virtualFlag;
	}

	public void setVirtualFlag(Integer virtualFlag) {
		this.virtualFlag = virtualFlag;
	}

	public Integer getInSendMsg() {
		return inSendMsg;
	}

	public void setInSendMsg(Integer inSendMsg) {
		this.inSendMsg = inSendMsg;
	}

	public String getInSendMsgShow() {
		return inSendMsgShow;
	}

	public void setInSendMsgShow(String inSendMsgShow) {
		this.inSendMsgShow = inSendMsgShow;
	}

	public String getMobilePhone() {
		return mobilePhone;
	}

	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	public String getAlarmflagName() {
		return alarmflagName;
	}

	public void setAlarmflagName(String alarmflagName) {
		this.alarmflagName = alarmflagName;
	}

	public String getMobileBook() {
		return mobileBook;
	}

	public void setMobileBook(String mobileBook) {
		this.mobileBook = mobileBook;
	}

	public String getMobileBookId() {
		return mobileBookId;
	}

	public void setMobileBookId(String mobileBookId) {
		this.mobileBookId = mobileBookId;
	}

	public String getAlarmflagId() {
		return alarmflagId;
	}

	public void setAlarmflagId(String alarmflagId) {
		this.alarmflagId = alarmflagId;
	}

	public String getMobileBooks() {
		return mobileBooks;
	}

	public void setMobileBooks(String mobileBooks) {
		this.mobileBooks = mobileBooks;
	}

	public String getRtdbTag() {
		return rtdbTag;
	}

	public void setRtdbTag(String rtdbTag) {
		this.rtdbTag = rtdbTag;
	}
}
