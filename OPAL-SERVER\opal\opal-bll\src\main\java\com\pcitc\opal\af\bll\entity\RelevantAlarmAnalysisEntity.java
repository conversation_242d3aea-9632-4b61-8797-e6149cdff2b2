package com.pcitc.opal.af.bll.entity;

import java.util.List;

/*
 * 相关性报警分析实体
 * 模块编号：pcitc_opal_bll_class_RelevantAlarmAnalysisEntity
 * 作       者：dageng.sun
 * 创建时间：2018/08/03
 * 修改编号：1
 * 描       述：相关性报警分析实体
 */
public class RelevantAlarmAnalysisEntity {
	
	/**
	 * 位号
	 */
	private String tag;
	
	/**
	 * 横坐标集合
	 */
	private List<String> xaxis;
	
	/**
	 * 报警数提示语集合
	 */
	private List<String> processTips;
	
	/**
	 * 报警数集合
	 */
	private List<Long> processCounts;
	
	/**
	 * 操作数提示语集合
	 */
	private List<String> operateTips;
	
	/**
	 * 操作数集合
	 */
	private List<Long> operateCounts;
	
	/**
	 * 生产单元简称
	 */
	private String prdtCellSname;
	
	/**
	 * 装置简称
	 */
	private String unitSname;
	
	/**
	 * 报警点id
	 */
	private Long alarmPointId;

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public List<String> getXaxis() {
		return xaxis;
	}

	public void setXaxis(List<String> xaxis) {
		this.xaxis = xaxis;
	}

	public List<String> getProcessTips() {
		return processTips;
	}

	public void setProcessTips(List<String> processTips) {
		this.processTips = processTips;
	}

	public List<Long> getProcessCounts() {
		return processCounts;
	}

	public void setProcessCounts(List<Long> processCounts) {
		this.processCounts = processCounts;
	}

	public List<String> getOperateTips() {
		return operateTips;
	}

	public void setOperateTips(List<String> operateTips) {
		this.operateTips = operateTips;
	}

	public List<Long> getOperateCounts() {
		return operateCounts;
	}

	public void setOperateCounts(List<Long> operateCounts) {
		this.operateCounts = operateCounts;
	}

	public String getPrdtCellSname() {
		return prdtCellSname;
	}

	public void setPrdtCellSname(String prdtCellSname) {
		this.prdtCellSname = prdtCellSname;
	}

	public String getUnitSname() {
		return unitSname;
	}

	public void setUnitSname(String unitSname) {
		this.unitSname = unitSname;
	}

	public Long getAlarmPointId() {
		return alarmPointId;
	}

	public void setAlarmPointId(Long alarmPointId) {
		this.alarmPointId = alarmPointId;
	}
}
