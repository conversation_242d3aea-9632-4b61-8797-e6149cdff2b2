package com.pcitc.opal.common.bll;

import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/*
 * AAA班组服务接口
 * 模块编号：pcitc_opal_bll_interface_ShiftService
 * 作       者：xuelei.wang
 * 创建时间：2017-12-12
 * 修改编号：1
 * 描       述：AAA班组服务接口
 */
public interface ShiftService {
    /**
     * 获取班组列表
     *
     * @param unitCode    装置编码
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 班组信息列表
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    List<ShiftWorkTeamEntity> getShiftWorkTeamList(String unitCode, Date startTime, Date endTime) throws Exception;
    /**
     * 获取班组列表
     *
     * @param unitCodeList 装置编码集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 班组列表信息
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    List<ShiftWorkTeamEntity> getShiftWorkTeamList(List<String> unitCodeList, Date startTime, Date endTime) throws Exception;
    /**
     * 根据班组名称集合及开始结束时间获取排班时间段列表
     *
     * @param unitCode         装置编码
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param workTeamIdList 班组Id集合
     * @return 排班时间段列表
     * @throws Exception
     * <AUTHOR> 2017-11-26
     */
    List<ShiftWorkTeamEntity> getShiftList(String unitCode, Date startTime, Date endTime, List<Long> workTeamIdList) throws Exception;

    /**
     * 获取指定班组的生产排班信息
     *
     * @param unitCode     装置ID
     * @param workTeamId 班组ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 生产排班信息集合
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    List<ShiftWorkTeamEntity> getShiftList(String unitCode, Long workTeamId, Date startTime, Date endTime) throws Exception;

    /**
     * 根据轮班域ID或编码集合获取轮班域列表
     *
     * @param shiftAreaIds 轮班域ID或编码集合
     * @return 轮班域集合
     * <AUTHOR> 2017-12-12
     */
    List<ShiftWorkTeamEntity> getShiftAreaList(List<String> shiftAreaIds) throws Exception;
}
