package com.pcitc.opal.ac.bll;

import java.util.Date;
import java.util.List;

import com.pcitc.opal.ac.bll.entity.*;
import org.springframework.stereotype.Service;

import com.pcitc.opal.aa.bll.entity.AlarmPriorityAssessEntity;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

/*
 * 报警变更方案申请逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_AlarmChangePlanApplyService
 * 作	者：kun.zhao
 * 创建时间：2018/01/19
 * 修改编号：1
 * 描	述：报警变更方案申请逻辑层接口
 */
@Service
public interface AlarmChangePlanApplyService {

	//region 报警变更方案申请首页接口

	/**
	 * 删除报警变更方案维护数据
	 * 
	 * <AUTHOR> 2018-1-19
	 * @param data 报警变更方案主键Id集合
	 * @return 消息结果类
	 * @throws Exception
	 */
	CommonResult deleteAlarmChangePlan(Long[] data,Integer aproType) throws Exception;

	/**
	 * 根据报警变更方案维护ID获取单条数据信息
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangePlanId 报警变更方案Id
	 * @return 报警变更方案实体数据
	 * @throws Exception
	 */
	AlarmChangePlanEntity getSingleAlarmChangePlan(Long alarmChangePlanId) throws  Exception;

	/**
	 * 加载报警变更方案维护主数据
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param unitCodes      装置编码数组
	 * @param startTime    申请时间范围开始
	 * @param endTime	      申请时间范围结束
	 * @param status	      状态
	 * @param businessType 变更方案业务类型
	 * @param page		      分页实体
	 * @return	报警变更实体数据
	 * @throws Exception
	 */
	PaginationBean<AlarmChangePlanEntity> getAlarmChangePlan(String[] unitCodes, Date startTime, Date endTime,
			Integer status, Integer businessType, Pagination page) throws Exception;

	//endregion

	//region 变更事项维护业务逻辑层接口

	/**
	 * 新增变更事项
	 *
	 * <AUTHOR> 2018-01-22
	 * @param alarmChangeItemValueEntity 报警变更事项实体
	 * @throws Exception
	 */
	CommonResult addAlarmChangeItem(AlarmChangeItemValueEntity alarmChangeItemValueEntity) throws Exception;

	/**
	 * 更新变更事项
	 *
	 * <AUTHOR> 2018-01-22
	 * @param alarmChangeItemValueEntity 报警变更事项实体
	 * @throws Exception
	 */
	CommonResult updateAlarmChangeItem(AlarmChangeItemValueEntity alarmChangeItemValueEntity) throws Exception;

	/**
	 * 获取报警变更事项实体数据
	 *
	 * <AUTHOR> 2018-01-22
	 * @param planId       报警变更方案ID
	 * @param planDetailId 报警变更方案明细ID
	 * @param alarmPointId 报警点ID
	 * @param alarmFlagId  报警标识ID
	 * @return AlarmChangeItemValueEntity 报警变更事项实体
	 */
	AlarmChangeItemValueEntity getAlarmChangeItemByPlanDetail(Long planId, Long planDetailId, Long alarmPointId, Long alarmFlagId) throws Exception;

	/**
	 * 根据planId、planDetailId获取报警变更事项数据列表
	 *
	 * <AUTHOR>   2018-01-22
	 * @param planId         报警变更方案ID
	 * @param planDetailId  报警变更方案明细ID
	 * @return  报警变更事项列表
	 */
	List<AlarmChangeItemEntity> getAlarmChangeItemListByPlanDetail(Long planId, Long planDetailId) throws Exception;

	//endregion

	//region 报警变更方案详情服务层

	/**
	 * 根据变更方案ID获取变更方案详情分页信息
	 *
	 * @param planId 变更方案ID
	 * @param page   分页信息
	 * @return 方案详情列表
	 * <AUTHOR> 2018-01-22
	 */
	PaginationBean<AlarmChangePlanDetailEntity> getAlarmChangePlanDetail(Long planId,Integer aproType, Pagination page) throws Exception;

	/**
	 * 删除数据
	 *
	 * @param planDetailIds 变更方案详情ID集合
	 * @param aproType
	 * <AUTHOR> 2018-01-22
	 */
	CommonResult deleteAlarmChangePlanDetailByIds(Long[] planDetailIds, Integer aproType) throws Exception;
	//endregion

	//region 报警变更方案新增、编辑、详情服务层
	/**
	 * 新增数据
	 *
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangePlanEntity 报警变更方案实体
	 */
	CommonResult addAlarmChangePlan(AlarmChangePlanEntity alarmChangePlanEntity)throws Exception;
	/**
	 * 更新
	 *
	 * <AUTHOR> 2018-01-19
	 * @param entity 报警变更方案实体
	 */
	CommonResult updateAlarmChangePlan(AlarmChangePlanEntity entity) throws Exception;
	//endregion

	//region 审批信息服务层

	/**
	 * 审批信息-服务层
	 *
	 * <AUTHOR> 2018-01-23
	 * @param planId 报警变更方案ID
	 * @throws Exception 抛出异常
	 * @return List<AlarmChangePlanAproEntity> 返回AlarmChangePlanAproEntity实体集合
	 */
	List<AlarmChangePlanAproEntity> getAlarmChangePlanApro (Long planId) throws Exception;

	//endregion

	//region 位号清单服务层

	/**
	 * 最频繁的报警-Top20服务层
	 *
	 * <AUTHOR> 2018-01-22
	 * @param endTime 查询结束时间
	 * @param unitCode 装置编码
	 * @param topType Top20,Top10切换选择
	 * @throws Exception 抛出异常
	 * @return List<MostFrequentAlarmEntity> 返回MostFrequentAlarmEntity实体集合
	 */
	List<MostFrequentAlarmEntity> getMostFrequentAlarmTop20(Date endTime,String unitCode, Integer topType) throws Exception;

	/**
	 * 最频繁的操作-Top20服务层
	 *
	 * <AUTHOR> 2018-01-22
	 * @param endTime 查询结束时间
	 * @param unitCode 装置编码
	 * @param topType Top20,Top10切换选择
	 * @throws Exception 抛出异常
	 * @return List<MostFrequentAlarmEntity> 返回MostFrequentAlarmEntity实体集合
	 */
	List<MostFrequentAlarmEntity> getMostFrequentOperateTop20(Date endTime,String unitCode, Integer topType) throws Exception;

	/**
	 * 持续报警-Top20服务层
	 *
	 * <AUTHOR> 2018-01-22
	 * @param endTime 查询结束时间
	 * @param unitCode 装置编码
	 * @throws Exception 抛出异常
	 * @return List<AlarmEventEntity> 返回AlarmEventEntity实体集合
	 */
	List<AlarmEventEntity> getPersistentAlarmAnalysisTop20(Date endTime, String unitCode) throws Exception;

	/**
	 * 震荡报警-Top20服务层
	 *
	 * <AUTHOR> 2018-01-22
	 * @param endTime 查询结束时间
	 * @param unitCode 装置编码
	 * @throws Exception 抛出异常
	 * @return List<MostFrequentAlarmEntity> 返回MostFrequentAlarmEntity实体集合
	 */
	List<MostFrequentAlarmEntity> getShakeAlarmAnalysisTop20(Date endTime,String unitCode) throws Exception;

	/**
	 * 优先级评估-图形显示服务层
	 *
	 * <AUTHOR> 2018-01-22
	 * @param endTime 查询结束时间
	 * @param unitCode 装置编码
	 * @throws Exception 抛出异常
	 * @return List<AlarmPriorityAssessEntity> 返回AlarmPriorityAssessEntity实体集合
	 */
	List<AlarmPriorityAssessEntity> getAlarmPriorityAssessGraphical(Date endTime, String unitCode) throws Exception;

	/**
	 * 优先级评估-表格显示服务层
	 *
	 * <AUTHOR> 2018-01-22
	 * @param endTime 查询结束时间
	 * @param unitCode 装置编码
	 * @param priority 优先级(1紧急；2重要；3一般)
	 * @throws Exception 抛出异常
	 * @return List<MostFrequentAlarmEntity> 返回MostFrequentAlarmEntity实体集合
	 */
	List<PriorityAssessEntity> getAlarmPriorityAssessTable(Date endTime,String unitCode,Integer priority) throws Exception;

	/**
	 * 位号查询-服务层
	 *
	 * <AUTHOR> 2018-01-23
	 * @param unitCode 装置编码
	 * @param tag 位号
	 * @param page 分页对象
	 * @throws Exception 抛出异常
	 * @return PaginationBean<AlarmPointViewEntity> 返回AlarmPointViewEntity实体分页对象
	 */
	PaginationBean<AlarmPointViewEntity> getAlarmPointView(String unitCode,String tag,Pagination page) throws Exception;
	
	//region 变更方案详情
	/**
	 * 审核信息-服务层
	 * 
	 * <AUTHOR> 2018-01-30 
	 * @param planId 报警变更方案ID
	 * @throws Exception 抛出异常
	 * @return AlarmChangePlanAproEntity 返回AlarmChangePlanAproEntity实体
	 */
	AlarmChangePlanAproEntity getAlarmChangePlanAproByPlanId (Long planId) throws Exception;
	
	/**
	 * 下发信息，确认信息-服务层
	 * 
	 * <AUTHOR> 2018-01-30 
	 * @param planId 报警变更方案ID
	 * @param businessType 业务类型(1下发；2确认)
	 * @throws Exception 抛出异常
	 * @return AlarmChangePlanExtrEntity 返回AlarmChangePlanExtrEntity实体
	 */
	AlarmChangePlanExtrEntity getAlarmChangePlanExtrByPlanId (Long planId,Integer businessType) throws Exception;
	
	//endregion

	//region 下发信息、确认信息

	/**
	 * 下发信息-保存、确认信息-保存
	 *
	 * <AUTHOR> 2018-01-30
	 * @param alarmChangePlanExtrEntity 报警变更方案附加信息实体
	 * @return
	 */
	CommonResult saveAlarmChangePlanExtr(AlarmChangePlanExtrEntity alarmChangePlanExtrEntity) throws Exception;

	/**
	 * 下发信息-下发、确认信息-确认
	 *
	 * <AUTHOR> 2018-01-30
	 * @param alarmChangePlanExtrEntity 报警变更方案附加信息实体
	 * @return
	 */
	CommonResult addAlarmChangePlanExtr(AlarmChangePlanExtrEntity alarmChangePlanExtrEntity) throws Exception;

	/**
	 * 获取下发信息
	 *
	 * <AUTHOR> 2018-01-30
	 * @param planId 报警变更方案id
	 * @param businessType 业务类型(1下发；2确认)
	 * @param unitCode 装置编码
	 * @return
	 */
	AlarmChangePlanLinkInfoEntity getAlarmChangePlanLinkInfo(Long planId,Integer businessType,String unitCode) throws Exception;

	/**
	 * 变更方案审核
	 *
	 * @param unitCodes   装置编码集合
	 * @param startTime 开始日期
	 * @param endTime   结束日期
	 * @param status    状态
	 * @param page		      分页实体
	 * @return	报警变更实体数据
	 * <AUTHOR> 2018-03-13
	 */
	PaginationBean<AlarmChangePlanEntity> getAlarmChangePlanAproList(String[] unitCodes,Date startTime, Date endTime, int status,Pagination page) throws Exception;

	/**
	 * 提交报警变更方案
	 *
	 * <AUTHOR> 2018-3-13
	 * @param planId       报警变更方案ID
	 * @param businessId   业务ID
	 * @param businessName 业务名称
	 * @param aproType
	 * @return
	 * @throws Exception
	 */
	CommonResult submitAlarmChangePlan(Long planId, String businessId, String businessName, Integer aproType) throws Exception;

	/**
	 * 审批通过
	 *
	 *
	 * @param planId          报警变更方案ID
	 * @param taskId          待办ID
	 * @param approveOpinion  审批意见
	 * @return
	 * @throws Exception
	 * <AUTHOR> 2018-3-13
	 */
	CommonResult completeApprove(Long planId, String taskId, String approveOpinion) throws Exception;

	/**
	 * 审批驳回
	 *
	 *
	 * @param planId          报警变更方案ID
	 * @param taskId          待办ID
	 * @param approveOpinion  审批意见
	 * @return
	 * @throws Exception
	 * <AUTHOR> 2018-3-13
	 */
	CommonResult revertApprove(Long planId, String taskId, String approveOpinion) throws Exception;

	//endregion
	/**
	 * 工艺审批结果回调
	 *
	 * @param data json数组
	 * @return CommonResult 返回消息
	 * @throws Exception
	 * <AUTHOR> 2019-04-17
	 */
	CommonResult auditFeedback(String data) throws Exception;

	List<CraftParaAlarmRateEntity> getCraftParaAlarmRate(String[] unitStdCodes, String stattType, Date startDate, Date endDate) throws Exception;

}
