package com.pcitc.opal.af.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 相关性位号配置实体
 * 模块编号：pcitc_opal_bll_class_RelevantTagConfigEntity
 * 作    者：dageng.sun
 * 创建时间：2018-08-01 15:12:20
 * 修改编号：1
 * 描    述：相关性位号配置实体
 */
public class RelevantTagConfigEntity extends BasicEntity {

    /**
     * 相关性位号配置ID
     */
    private Long relevantTagConfigId;

    /**
     * 报警点ID
     */
    private Long alarmPointId;
    
    /**
     * 位号
     */
    private String tag;
    
    /**
	 * 装置编码
	 */
	private String unitId;
    
    /**
	 * 装置名称简称
	 */
	private String unitSname;
	
	/**
     * 生产单元ID
     */
    private Long prdtCellId;
	
	/**
     * 生产单元简称
     */
    private String prdtCellSname;

	public Long getRelevantTagConfigId() {
		return relevantTagConfigId;
	}

	public void setRelevantTagConfigId(Long relevantTagConfigId) {
		this.relevantTagConfigId = relevantTagConfigId;
	}

	public Long getAlarmPointId() {
		return alarmPointId;
	}

	public void setAlarmPointId(Long alarmPointId) {
		this.alarmPointId = alarmPointId;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public String getUnitSname() {
		return unitSname;
	}

	public void setUnitSname(String unitSname) {
		this.unitSname = unitSname;
	}

	public Long getPrdtCellId() {
		return prdtCellId;
	}

	public void setPrdtCellId(Long prdtCellId) {
		this.prdtCellId = prdtCellId;
	}

	public String getPrdtCellSname() {
		return prdtCellSname;
	}

	public void setPrdtCellSname(String prdtCellSname) {
		this.prdtCellSname = prdtCellSname;
	}

}

