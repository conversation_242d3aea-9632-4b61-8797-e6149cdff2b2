<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.mapper.CraftChangeInfoMapper">

    <resultMap id="BaseResultMap" type="com.pcitc.opal.pojo.CraftChangeInfo">
            <id property="craftChangeInfoId" column="craft_change_info_id" jdbcType="BIGINT"/>
            <result property="companyId" column="company_id" jdbcType="BIGINT"/>
            <result property="unitCode" column="unit_code" jdbcType="VARCHAR"/>
            <result property="unitSname" column="unit_sname" jdbcType="VARCHAR"/>
            <result property="sn" column="sn" jdbcType="VARCHAR"/>
            <result property="tagCode" column="tag_code" jdbcType="VARCHAR"/>
            <result property="mtrl" column="mtrl" jdbcType="VARCHAR"/>
            <result property="item" column="item" jdbcType="VARCHAR"/>
            <result property="measunit" column="measunit" jdbcType="VARCHAR"/>
            <result property="submitUserName" column="submit_user_name" jdbcType="VARCHAR"/>
            <result property="beforeValue" column="before_value" jdbcType="VARCHAR"/>
            <result property="afterValue" column="after_value" jdbcType="VARCHAR"/>
            <result property="submitTime" column="submit_time" jdbcType="TIMESTAMP"/>
            <result property="rlsTime" column="rls_time" jdbcType="TIMESTAMP"/>
            <result property="writeTime" column="write_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        craft_change_info_id,company_id,unit_code,
        unit_sname,sn,tag_code,
        mtrl,item,measunit,
        submit_user_name,before_value,after_value,
        submit_time,rls_time,write_time
    </sql>
</mapper>
