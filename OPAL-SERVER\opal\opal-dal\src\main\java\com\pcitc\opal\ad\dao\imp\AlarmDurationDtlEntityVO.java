package com.pcitc.opal.ad.dao.imp;

import javax.persistence.Column;
import java.util.Date;

/*
 * 报警次数统计统计数据实体
 * 作  　  者：shufei.sui
 * 创建时间：2019/09/26
 * 修改编号：1
 * 描       述：报警次数统计统计数据实体
 */
public class AlarmDurationDtlEntityVO {
    public AlarmDurationDtlEntityVO(Date alarmTime, Integer durationM, Date recoveryTime, String cellSname, String tag, String des, String alarmFlagName) {
        this.alarmTime = alarmTime;
        this.durationM = durationM;
        this.recoveryTime = recoveryTime;
        this.cellSname = cellSname;
        this.tag = tag;
        this.des = des;
        this.alarmFlagName = alarmFlagName;
    }

    /**
     * 报警时间
     */
    private Date alarmTime;
    /**
     * 时长（分钟）
     */
    private Integer durationM;
    /**
     * 恢复时间
     */
    private Date recoveryTime;
    /**
     * 生产单元简称
     */
    private String cellSname;
    /**
     * 位号
     */
    private String tag;
    /**
     * 描述
     */
    private String des;
    /**
     * 报警标识名称
     */
    private String alarmFlagName;

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public Integer getDurationM() {
        return durationM;
    }

    public void setDurationM(Integer durationM) {
        this.durationM = durationM;
    }

    public Date getRecoveryTime() {
        return recoveryTime;
    }

    public void setRecoveryTime(Date recoveryTime) {
        this.recoveryTime = recoveryTime;
    }

    public String getCellSname() {
        return cellSname;
    }

    public void setCellSname(String cellSname) {
        this.cellSname = cellSname;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }
}
