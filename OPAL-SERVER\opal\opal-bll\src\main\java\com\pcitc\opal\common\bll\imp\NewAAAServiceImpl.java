package com.pcitc.opal.common.bll.imp;

import com.alibaba.fastjson.JSONObject;
import com.pcitc.opal.common.CommonPropertiesReader;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.bll.AAAService;
import com.pcitc.opal.common.bll.entity.AAAPropertyValueEntity;
import com.pcitc.opal.common.bll.entity.AAAOrgUnitEntity;
import com.pcitc.opal.common.bll.entity.AAAUserEntity;
import com.pcitc.opal.common.bll.entity.UserEntity;
import com.pcitc.opal.pm.dao.CompanyRepository;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.authenticateservice.current.IAuthenticateServiceProxy;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.authenticateservice.current.entity.OrgUnit;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.authenticateservice.current.entity.User;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.authorizeservice.current.IAuthorizeServiceProxy;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.authorizeservice.current.entity.AuthPropertyValue;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * .Net版AAA平台提供的面向应用的服务接口（开发测试环境使用这个版本)
 * 模块编号：pcitc_opal_common_interface_NewAAAServiceImpl
 * 作    者：jiangtao.xue
 * 创建时间：2017-12-14
 * 修改编号：1
 * 描    述：AAA平台提供的面向应用的服务接口
 */
@Component
@ConditionalOnProperty(name ="aaa_version",havingValue = "new")
public class NewAAAServiceImpl implements AAAService {

    @Autowired
    private CompanyRepository companyRepository;

    private final static Log logger = LogFactory.getLog(NewAAAServiceImpl.class);

    /**
     * 根据角色获取用户集合
     *
     * @param role 用户角色
     * @return
     * <AUTHOR> 2018-02-05
     */
    @Override
    public List<AAAUserEntity> listUsersByRole(String role) throws RemoteException {
        IAuthenticateServiceProxy proxy = new IAuthenticateServiceProxy();
        List<User> userList = Arrays.asList(proxy.listUsersByRole(role));
        List<AAAUserEntity> aaaUserEntityList = new ArrayList<>();
        for (User user : userList) {
            AAAUserEntity aaaUserEntity = new AAAUserEntity();
            BeanUtils.copyProperties(user, aaaUserEntity);
            aaaUserEntityList.add(aaaUserEntity);
        }
        return aaaUserEntityList;
    }

    /**
     * 根据UserId 获取所属的组织单元
     *
     * @param userId 用户ID或者LoginName
     * @return 组织单元
     * @throws Exception
     * <AUTHOR> 2018-03-09
     */
    @Override
    public AAAOrgUnitEntity getOrgByUserId(String userId) throws Exception {
        IAuthenticateServiceProxy proxy = new IAuthenticateServiceProxy();
        OrgUnit orgUnit = Arrays.asList(proxy.listOrganizationUnitByUser(userId)).stream().findFirst().orElse(null);
        AAAOrgUnitEntity orgEntity = new AAAOrgUnitEntity();
        if (orgUnit != null) {
            BeanUtils.copyProperties(orgUnit, orgEntity);
        } else {
            return null;
        }
        return orgEntity;
    }

    /**
     * 获取用户授权属性
     *
     * @param userId 用户ID
     * @return
     * @throws Exception
     * <AUTHOR> 2017-12-20
     */
    @Override
    public List<AAAPropertyValueEntity> getAuthPropertyValueList(String userId) throws Exception {
        List<AAAPropertyValueEntity> resultList = new ArrayList<>();
        try {
            IAuthorizeServiceProxy proxy = new IAuthorizeServiceProxy();
            if (StringUtils.isEmpty(userId)) {
                userId = new CommonProperty().getUserId();
            }
            String propertyCode = CommonPropertiesReader.getValue("aaa.authorize.unitPropertyCode");
            logger.error("当前用户艾迪："+userId);
            logger.error("装置属性："+propertyCode );
            AuthPropertyValue[] resArray = proxy.listPropertyValue(userId, propertyCode);
            if (resArray != null) {
                for (int i = 0; i < resArray.length; i++) {
                    logger.error(resArray[i].getPropertyValueId());
                    AAAPropertyValueEntity entity = new AAAPropertyValueEntity();
                    BeanUtils.copyProperties(resArray[i], entity);
                    resultList.add(entity);
                }
            }
        } catch (Exception ex) {
            System.out.println("调用AAA服务认证接口出现了异常!:" + ex.getMessage());
            ex.printStackTrace();
        }
        return resultList;
    }

    @Override
    public UserEntity getUserInfoByUserCode(String userCode) throws Exception {
        return null;
    }
    @Override
    public Integer getCompanyIdByUserId(String userId){
        logger.error("通过NewAAAServiceImpl的getCompanyIdByUserId的方法获取企业编码");
//        Integer company_id=companyRepository.getCompanyIdByStdCode("ZGSH01");
//        return company_id;
//        List<AAAPropertyValueEntity> resultList = new ArrayList<>();
//        try {
//            IAuthorizeServiceProxy proxy = new IAuthorizeServiceProxy();
//            if (StringUtils.isEmpty(userId)) {
//                userId = new CommonProperty().getUserId();
//            }
//            String propertyCode = CommonPropertiesReader.getValue("aaa.authorize.companyPropertyCode");
//            logger.error("当前用户艾迪："+userId);
//            logger.error("装置属性："+propertyCode );
//            AuthPropertyValue[] resArray = proxy.listPropertyValue(userId, propertyCode);
//            if (resArray != null) {
//                for (int i = 0; i < resArray.length; i++) {
//                    logger.error("通过新3A获取企业编码----------------------------------------------------------------");
//                    logger.error(resArray[i].getValue());
//                    logger.error(resArray[i].getPropertyValueId());
//                    AAAPropertyValueEntity entity = new AAAPropertyValueEntity();
//                    BeanUtils.copyProperties(resArray[i], entity);
//                    resultList.add(entity);
//                }
//            }
//        } catch (Exception ex) {
//            System.out.println("调用AAA服务认证接口出现了异常!:" + ex.getMessage());
//            ex.printStackTrace();
//        }
        return 24;
    }
}
