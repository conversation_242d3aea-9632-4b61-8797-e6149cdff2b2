package com.pcitc.opal.af.bll.entity;


import lombok.Data;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/*
 * 工艺参数报警率数据实体
 * 作  　  者：shufei.sui
 * 创建时间：2019/12/11
 * 修改编号：1
 * 描       述：工艺参数报警率数据实体
 */
@Data
public class CraftParaAlarmRateEntity {

    //装置编码
    private String unitid;

    //装置名称
    private String unitCode;

    //班组详细信息
    private List<CraftParaAlarmRateEntity> teamDetail;

    // 时平均报警数、
    private Double avgAlarmRate;
    private LinkedHashMap<String, Double> avgAlarmRateDetail;
    private Double avgAvgAlarmRate;

    // 24小时持续报警数、
    private Double alarmAmount;
    private List<ContinuedAlarmEntity> alarmAmountDetail;
    private Double avgAlarmAmount;

    // 10分钟峰值报警数
    private Double peakAlarmRate;
    private LinkedHashMap<String, Integer> peakAlarmRateDetail;

    //报警响应及时率
    private Double alarmTimelyResponseRate;
    private List<ContinuedAlarmEntity> alarmTimelyResponseRateDetail;
    private Double avgAlarmTimelyResponseRate;

    //报警处置及时率
    private Double alarmTimelyDisposalRate;
    private List<ContinuedAlarmEntity> alarmTimelyDisposalRateDetail;
    private Double avgAlarmTimelyDisposalRate;

    //平均工艺参数报警率
    private Double avgCraftParaAlarmRate;

    //工艺参数报警率
    private Double craftParaAlarmRate;

    //结束时间
    private String endTime;

    public void setAvgAvgAlarmRate(Double avgAvgAlarmRate) {
        this.avgAvgAlarmRate = formatToTwoDecimalPlaces(avgAvgAlarmRate);
    }

    public void setAvgAlarmAmount(Double avgAlarmAmount) {
        this.avgAlarmAmount = formatToTwoDecimalPlaces(avgAlarmAmount);
    }

    public void setAvgAlarmTimelyResponseRate(Double avgAlarmTimelyResponseRate) {
        this.avgAlarmTimelyResponseRate = formatToTwoDecimalPlaces(avgAlarmTimelyResponseRate);
    }

    public void setAvgAlarmTimelyDisposalRate(Double avgAlarmTimelyDisposalRate) {
        this.avgAlarmTimelyDisposalRate = formatToTwoDecimalPlaces(avgAlarmTimelyDisposalRate);
    }

    public void setAvgCraftParaAlarmRate(Double avgCraftParaAlarmRate) {
        this.avgCraftParaAlarmRate = formatToTwoDecimalPlaces(avgCraftParaAlarmRate);
    }
    public void setCraftParaAlarmRate(Double craftParaAlarmRate) {
        this.craftParaAlarmRate = formatToTwoDecimalPlaces(craftParaAlarmRate);;
    }

    private double formatToTwoDecimalPlaces(double value) {
        // 使用 DecimalFormat 将值格式化为两位小数
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        return Double.parseDouble(decimalFormat.format(value));
    }


}

