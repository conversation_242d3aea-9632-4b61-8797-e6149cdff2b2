package com.pcitc.opal.ad.bll.imp;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pcitc.opal.ad.bll.AlarmEventService;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.bll.entity.AlarmEventShowEntity;
import com.pcitc.opal.ad.bll.entity.MobileMsgListEntity;
import com.pcitc.opal.ad.bll.entity.MonitoringDataEntity;
import com.pcitc.opal.ad.dao.*;
import com.pcitc.opal.ad.entity.DaterangeEntity;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.ad.pojo.AlarmEventDelApprove;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.ad.pojo.MobileMsgList;
import com.pcitc.opal.ad.vo.AlarmEventTableParamVO;
import com.pcitc.opal.ad.vo.AlarmEventTableVO;
import com.pcitc.opal.af.bll.AlarmDurationStattService;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.CommonEnum.TimeFilterTypeEnum;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.DateRangeEntity;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.PrdtCellService;
import com.pcitc.opal.pm.bll.UnitService;
import com.pcitc.opal.pm.bll.entity.*;
import com.pcitc.opal.pm.dao.AlarmMobileConfRepository;
import com.pcitc.opal.pm.dao.EventTypeRepository;
import com.pcitc.opal.pm.dao.MobileListRepository;
import com.pcitc.opal.pm.pojo.*;
import com.pcitc.opal.webservice.messageService.SendShortMessages;
import com.pcitc.opal.webservice.messageService.SendShortMessagesSoap;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import javax.annotation.Resource;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/*
 * 报警事件业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmEventImpl
 * 作       者：kun.zhao
 * 创建时间：2017/10/09
 * 修改编号：1
 * 描       述：报警事件业务逻辑层实现类
 */
@Service
@Component
public class AlarmEventImpl implements AlarmEventService {
    //private final static Log logger = LogFactory.getLog(AlarmEventImpl.class);
    private static final Logger logger = LoggerFactory.getLogger(AlarmEventImpl.class);

    @Autowired
    private AlarmEventRepository alarmEventRepository;

    @Autowired
    private EventTypeRepository eventTypeRepository;

    @Autowired
    private BasicDataService basicDataService;

    @Autowired
    private ShiftService shiftService;

    @Autowired
    private UnitService unitService;

    @Autowired
    private PrdtCellService prdtCellService;

    @Autowired
    private AlarmDurationStattService alarmDurationStattService;
    @Autowired
    private AlarmMobileConfRepository alarmMobileConfRepository;
    @Autowired
    private MobileListRepository mobileListRepository;
    @Autowired
    private MobileMsgListRepository mobileMsgListRepository;

    @Resource
    private AlarmEventDelApproveRepository alarmEventDelApproveRepository;

    @Resource
    private AlarmEventDelRepository alarmEventDelRepository;

    @Autowired
    private AlarmEventDAO alarmEventDAO;

    @Autowired
    private DaterangeDAO daterangeDAO;


    /**
     * 获取分页数据
     *
     * @param unitCodes     装置ID数组
     * @param prdtCellIds   生产单元ID数组
     * @param eventTypeIds  事件类型ID数组
     * @param workTeamIds   班组ID数组
     * @param alarmPointTag 报警点位号
     * @param alarmFlagIds  报警标识ID集合
     * @param priority      优先级
     * @param craftRank     级别
     * @param startTime     发生时间范围起始
     * @param endTime       发生时间范围结束
     * @param page          分页参数
     * @param isMatching
     * @return 报警事件实体集合
     * <AUTHOR> 2017-10-09
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<AlarmEventEntity> getAlarmEvent(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds, Long[] workTeamIds, String alarmPointTag, Long[] alarmFlagIds,
                                                          Integer priority, Integer craftRank, Date startTime, Date endTime, Pagination page, Integer isMatching)
            throws Exception {
        List<UnitEntity> units = null;
        if (unitCodes == null) {
            units = basicDataService.getUnitList(true);
            unitCodes = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        // 班组
        if (workTeamIds == null) {
            workTeamIds = new Long[]{};
        }
        List<Long> workTeamIdList = Arrays.asList(workTeamIds);
        List<DateRangeEntity> dateRangeList = new ArrayList<>();
        List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
        if (workTeamIdList.size() != 0 && unitCodes != null && unitCodes.length == 1) {
            shiftWorkList = shiftService.getShiftList(unitCodes[0], startTime, endTime, workTeamIdList);
            dateRangeList = shiftWorkList.stream().map(item -> new DateRangeEntity(item.getStartTime(), item.getEndTime())).collect(Collectors.toList());
        }

        PaginationBean<AlarmEvent> listAlarmEvent = alarmEventRepository.getAlarmEventList(unitCodes, prdtCellIds, eventTypeIds
                , alarmPointTag, alarmFlagIds, priority, null, null, craftRank, TimeFilterTypeEnum.StartTime, startTime, endTime, dateRangeList, page, isMatching);
        PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<AlarmEventEntity>(page,
                listAlarmEvent.getTotal());
        returnAlarmEvent.setPageList(ObjectConverter.listConverter(listAlarmEvent.getPageList(), AlarmEventEntity.class));
        // 装置不选或班组全选时补全班组信息
        if (dateRangeList.size() == 0 && workTeamIdList.size() == 0) {
            Date minDate = returnAlarmEvent.getPageList().stream().reduce((item1, item2) -> item1.getStartTime().getTime() < item2.getStartTime().getTime() ? item1 : item2).orElse(new AlarmEventEntity()).getStartTime();
            Date maxDate = returnAlarmEvent.getPageList().stream().reduce((item1, item2) -> item1.getStartTime().getTime() > item2.getStartTime().getTime() ? item1 : item2).orElse(new AlarmEventEntity()).getStartTime();
            if (minDate != null && maxDate != null) {
                shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCodes), minDate, maxDate);
            }
        }
        if (units == null) {
            // 通过公共方法获取装置
            String[] filterunitCodes = listAlarmEvent.getPageList().stream().map(e -> e.getPrdtCell().getUnitId()).distinct().toArray(String[]::new);
            units = basicDataService.getUnitListByIds(filterunitCodes, false);
        }
        // 映射字段
        for (int i = 0; i < returnAlarmEvent.getPageList().size(); i++) {
            AlarmEventEntity alarmEventEntity = returnAlarmEvent.getPageList().get(i);
            AlarmEvent alarmEvent = listAlarmEvent.getPageList().get(i);
            // 填充是否匹配
            alarmEventEntity.setAlarmPointExplain(null == alarmEvent.getAlarmPointId() ? "未匹配" : null);
            // 填充装置简称
            UnitEntity unit = units.stream().filter(u -> alarmEvent.getPrdtCell().getUnitId().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
            alarmEventEntity.setUnitName(unit.getSname());
            // 填充生产单元简称
            alarmEventEntity.setPrdtCellName(alarmEvent.getPrdtCell().getSname());
            // 填充报警点位号
            alarmEventEntity.setAlarmPointTag(null != alarmEvent.getAlarmPoint() ? alarmEvent.getAlarmPoint().getTag() : alarmEvent.getTag());
            //报警点位置
            alarmEventEntity.setLocation(null != alarmEvent.getAlarmPoint() ? alarmEvent.getAlarmPoint().getLocation() : "");
            // 填充报警标识名称
            alarmEventEntity.setAlarmFlagName(null != alarmEvent.getAlarmFlag() ? alarmEvent.getAlarmFlag().getName() : null);
            // 填充事件类型
            alarmEventEntity.setEventTypeName(alarmEvent.getEventType().getName());
            // 填充计量单位
            MeasUnit measUnit = null != alarmEvent.getAlarmPoint() ? alarmEvent.getAlarmPoint().getMeasUnit() : null;
            alarmEventEntity.setMeasUnitName(null != measUnit ? measUnit.getName() + "(" + measUnit.getSign() + ")" : null);
            // 填充级别
            alarmEventEntity.setCraftRank(null != alarmEvent.getAlarmPoint() ? alarmEvent.getAlarmPoint().getCraftRank() : null);
            alarmEventEntity.setCraftRankName(alarmEventEntity.getCraftRankName());
            // 填充班组名称
            alarmEventEntity.setWorkTeamSName(shiftWorkList.parallelStream().filter(item -> alarmEventEntity.getStartTime().getTime() >= item.getStartTime().getTime() && alarmEventEntity.getStartTime().getTime() < item.getEndTime().getTime()).findFirst().orElse(new ShiftWorkTeamEntity()).getWorkTeamSName());
            //工艺卡片值 联锁值赋值
            if (null != alarmEvent.getAlarmPoint()) {
                Double culv = alarmEvent.getAlarmPoint().getCraftUpLimitValue();//工艺卡片上限值
                Double cdlv = alarmEvent.getAlarmPoint().getCraftDownLimitValue();//工艺卡片下限值
                Integer culi = alarmEvent.getAlarmPoint().getCraftUpLimitInclude();//工艺卡片上限值是否包含(1是；0否)
                Integer cdli = alarmEvent.getAlarmPoint().getCraftDownLimitInclude();//工艺卡片下限值是否包含(1是；0否)
                Double iulv = alarmEvent.getAlarmPoint().getInterlockUpLimitValue();//联锁上限值
                Double idlv = alarmEvent.getAlarmPoint().getInterlockDownLimitValue();//联锁下限值
                Integer iuli = alarmEvent.getAlarmPoint().getInterlockUpLimitInclude();//联锁上限值是否包含(1是；0否)
                Integer idli = alarmEvent.getAlarmPoint().getInterlockDownLimitInclude();//联锁下限值是否包含(1是；0否)
                if (culv != null && cdlv != null) {
                    String culvStr = changeDouble(culv);
                    String cdlvStr = changeDouble(cdlv);
                    alarmEventEntity.setCraftLimitValue(cdlvStr + "~" + culvStr);
                } else if (culv != null && cdlv == null) {
                    if (culi != null && culi.intValue() == 1) {
                        String culvStr = changeDouble(culv);
                        alarmEventEntity.setCraftLimitValue("≤" + culvStr);
                    } else if (culi != null && culi.intValue() == 0) {
                        String culvStr = changeDouble(culv);
                        alarmEventEntity.setCraftLimitValue("<" + culvStr);
                    }
                } else if (culv == null && cdlv != null) {
                    if (cdli != null && cdli.intValue() == 1) {
                        String cdlvStr = changeDouble(cdlv);
                        alarmEventEntity.setCraftLimitValue("≥" + cdlvStr);
                    } else if (cdli != null && cdli.intValue() == 0) {
                        String cdlvStr = changeDouble(cdlv);
                        alarmEventEntity.setCraftLimitValue(">" + cdlvStr);
                    }
                } else if (culv == null && cdlv == null) {
                    alarmEventEntity.setCraftLimitValue("");
                }
                if (iulv != null && idlv != null) {
                    String iulvStr = changeDouble(iulv);
                    String idlvStr = changeDouble(idlv);
                    alarmEventEntity.setInterlockLimitValue(idlvStr + "~" + iulvStr);
                } else if (iulv != null && idlv == null) {
                    if (iuli.intValue() == 1) {
                        String iulvStr = changeDouble(iulv);
                        alarmEventEntity.setInterlockLimitValue("≤" + iulvStr);
                    } else if (iuli.intValue() == 0) {
                        String iulvStr = changeDouble(iulv);
                        alarmEventEntity.setInterlockLimitValue("<" + iulvStr);
                    }
                } else if (iulv == null && idlv != null) {
                    if (idli.intValue() == 1) {
                        String idlvStr = changeDouble(idlv);
                        alarmEventEntity.setInterlockLimitValue("≥" + idlvStr);
                    } else if (idli.intValue() == 0) {
                        String idlvStr = changeDouble(idlv);
                        alarmEventEntity.setInterlockLimitValue(">" + idlvStr);
                    }
                } else if (iulv == null && idlv == null) {
                    alarmEventEntity.setInterlockLimitValue("");
                }
                if (alarmEventEntity.getEventTypeId().toString().startsWith("10")) {
                    //限值处理
                    Long flag = alarmEventEntity.getAlarmFlagId();
                    if (null != flag && flag == CommonEnum.AlarmFlagEnum.PVHH.getIndex() && alarmEvent.getAlarmPoint().getAlarmPointHH() != null) {
                        alarmEventEntity.setLimitValue(alarmEvent.getAlarmPoint().getAlarmPointHH());
                    } else if (null != flag && flag == CommonEnum.AlarmFlagEnum.PVHI.getIndex() && alarmEvent.getAlarmPoint().getAlarmPointHI() != null) {
                        alarmEventEntity.setLimitValue(alarmEvent.getAlarmPoint().getAlarmPointHI());
                    } else if (null != flag && flag == CommonEnum.AlarmFlagEnum.PVLL.getIndex() && alarmEvent.getAlarmPoint().getAlarmPointLL() != null) {
                        alarmEventEntity.setLimitValue(alarmEvent.getAlarmPoint().getAlarmPointLL());
                    } else if (null != flag && flag == CommonEnum.AlarmFlagEnum.PVLO.getIndex() && alarmEvent.getAlarmPoint().getAlarmPointLO() != null) {
                        alarmEventEntity.setLimitValue(alarmEvent.getAlarmPoint().getAlarmPointLO());
                    } else {
                        alarmEventEntity.setLimitValue(null);
                    }

                } else {
                    alarmEventEntity.setLimitValue(null);
                }
            }
        }
        return returnAlarmEvent;
    }


    /**
     * 获取分页数据
     *
     * @param unitCodes     装置ID数组
     * @param prdtCellIds   生产单元ID数组
     * @param eventTypeIds  事件类型ID数组
     * @param workTeamIds   班组ID数组
     * @param alarmPointTag 报警点位号
     * @param alarmFlagIds  报警标识ID集合
     * @param prioritys     优先级
     * @param craftRank     级别
     * @param startTime     发生时间范围起始
     * @param endTime       发生时间范围结束
     * @param page          分页参数
     * @param isMatching
     * @return 报警事件实体集合
     * <AUTHOR> 2017-10-09
     */
    @SuppressWarnings("unchecked")
//    @Override
//    public PaginationBean<AlarmEventEntity> getAlarmEventP(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds, Long[] workTeamIds, String alarmPointTag, Long[] alarmFlagIds,
//                                                           Integer[] prioritys, Integer monitorType, Integer craftRank, Date startTime, Date endTime, Pagination page, Integer isMatching)
//            throws Exception {
//        List<UnitEntity> units = null;
//        if (unitCodes == null) {
//            units = basicDataService.getUnitList(true);
//            unitCodes = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
//        }
//        // 班组
//        if (workTeamIds == null) {
//            workTeamIds = new Long[]{};
//        }
//        List<Long> workTeamIdList = Arrays.asList(workTeamIds);
//        List<DateRangeEntity> dateRangeList = new ArrayList<>();
//        List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
//        if (workTeamIdList.size() != 0 && unitCodes != null && unitCodes.length == 1) {
//            shiftWorkList = shiftService.getShiftList(unitCodes[0], startTime, endTime, workTeamIdList);
//            dateRangeList = shiftWorkList.stream().map(item -> new DateRangeEntity(item.getStartTime(), item.getEndTime())).collect(Collectors.toList());
//        }
//
//        PaginationBean<AlarmEvent> listAlarmEvent = alarmEventRepository.getAlarmEventList(unitCodes, prdtCellIds, eventTypeIds
//                , alarmPointTag, alarmFlagIds, null, prioritys, monitorType, craftRank, TimeFilterTypeEnum.StartTime, startTime, endTime, dateRangeList, page, isMatching);
//        PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<AlarmEventEntity>(page,
//                listAlarmEvent.getTotal());
//        returnAlarmEvent.setPageList(ObjectConverter.listConverter(listAlarmEvent.getPageList(), AlarmEventEntity.class));
//        // 装置不选或班组全选时补全班组信息
//        if (dateRangeList.size() == 0 && workTeamIdList.size() == 0) {
//            Date minDate = returnAlarmEvent.getPageList().stream().reduce((item1, item2) -> item1.getStartTime().getTime() < item2.getStartTime().getTime() ? item1 : item2).orElse(new AlarmEventEntity()).getStartTime();
//            Date maxDate = returnAlarmEvent.getPageList().stream().reduce((item1, item2) -> item1.getStartTime().getTime() > item2.getStartTime().getTime() ? item1 : item2).orElse(new AlarmEventEntity()).getStartTime();
//            if (minDate != null && maxDate != null) {
//                shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCodes), minDate, maxDate);
//            }
//        }
//        if (units == null) {
//            // 通过公共方法获取装置
//            String[] filterunitCodes = listAlarmEvent.getPageList().stream().map(e -> e.getPrdtCell().getUnitId()).distinct().toArray(String[]::new);
//            units = basicDataService.getUnitListByIds(filterunitCodes, false);
//        }
//        // 映射字段
//        for (int i = 0; i < returnAlarmEvent.getPageList().size(); i++) {
//            AlarmEventEntity alarmEventEntity = returnAlarmEvent.getPageList().get(i);
//            AlarmEvent alarmEvent = listAlarmEvent.getPageList().get(i);
//            // 填充是否匹配
//            alarmEventEntity.setAlarmPointExplain(null == alarmEvent.getAlarmPointId() ? "未匹配" : null);
//            //填充检测类型
//            AlarmPoint alarmPoint = alarmEvent.getAlarmPoint();
//            if (null != alarmPoint) {
//                alarmEventEntity.setMonitorType(alarmPoint.getMonitorType());
//            }
//            // 填充装置简称
//            UnitEntity unit = units.stream().filter(u -> alarmEvent.getPrdtCell().getUnitId().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
//            alarmEventEntity.setUnitName(unit.getSname());
//            // 填充生产单元简称
//            alarmEventEntity.setPrdtCellName(alarmEvent.getPrdtCell().getSname());
//            // 填充报警点位号
//            alarmEventEntity.setAlarmPointTag(null != alarmPoint ? alarmPoint.getTag() : alarmEvent.getTag());
//            //报警点位置
//            alarmEventEntity.setLocation(null != alarmPoint ? alarmPoint.getLocation() : "");
//            // 填充报警标识名称
//            alarmEventEntity.setAlarmFlagName(null != alarmEvent.getAlarmFlag() ? alarmEvent.getAlarmFlag().getName() : null);
//            // 填充事件类型
//            alarmEventEntity.setEventTypeName(alarmEvent.getEventType().getName());
//            // 填充计量单位
//            MeasUnit measUnit = null != alarmPoint ? alarmPoint.getMeasUnit() : null;
//            alarmEventEntity.setMeasUnitName(null != measUnit ? measUnit.getName() + "(" + measUnit.getSign() + ")" : null);
//            // 填充级别
//            alarmEventEntity.setCraftRank(null != alarmPoint ? alarmPoint.getCraftRank() : null);
//            alarmEventEntity.setCraftRankName(alarmEventEntity.getCraftRankName());
//            // 填充班组名称
//            alarmEventEntity.setWorkTeamSName(shiftWorkList.parallelStream().filter(item -> alarmEventEntity.getStartTime().getTime() >= item.getStartTime().getTime() && alarmEventEntity.getStartTime().getTime() < item.getEndTime().getTime()).findFirst().orElse(new ShiftWorkTeamEntity()).getWorkTeamSName());
//            //工艺卡片值 联锁值赋值
//            if (null != alarmPoint) {
//                Double culv = alarmPoint.getCraftUpLimitValue();//工艺卡片上限值
//                Double cdlv = alarmPoint.getCraftDownLimitValue();//工艺卡片下限值
//                Integer culi = alarmPoint.getCraftUpLimitInclude();//工艺卡片上限值是否包含(1是；0否)
//                Integer cdli = alarmPoint.getCraftDownLimitInclude();//工艺卡片下限值是否包含(1是；0否)
//                Double iulv = alarmPoint.getInterlockUpLimitValue();//联锁上限值
//                Double idlv = alarmPoint.getInterlockDownLimitValue();//联锁下限值
//                Integer iuli = alarmPoint.getInterlockUpLimitInclude();//联锁上限值是否包含(1是；0否)
//                Integer idli = alarmPoint.getInterlockDownLimitInclude();//联锁下限值是否包含(1是；0否)
//                if (culv != null && cdlv != null) {
//                    String culvStr = changeDouble(culv);
//                    String cdlvStr = changeDouble(cdlv);
//                    alarmEventEntity.setCraftLimitValue(cdlvStr + "~" + culvStr);
//                } else if (culv != null && cdlv == null) {
//                    if (culi != null && culi.intValue() == 1) {
//                        String culvStr = changeDouble(culv);
//                        alarmEventEntity.setCraftLimitValue("≤" + culvStr);
//                    } else if (culi != null && culi.intValue() == 0) {
//                        String culvStr = changeDouble(culv);
//                        alarmEventEntity.setCraftLimitValue("<" + culvStr);
//                    }
//                } else if (culv == null && cdlv != null) {
//                    if (cdli != null && cdli.intValue() == 1) {
//                        String cdlvStr = changeDouble(cdlv);
//                        alarmEventEntity.setCraftLimitValue("≥" + cdlvStr);
//                    } else if (cdli != null && cdli.intValue() == 0) {
//                        String cdlvStr = changeDouble(cdlv);
//                        alarmEventEntity.setCraftLimitValue(">" + cdlvStr);
//                    }
//                } else if (culv == null && cdlv == null) {
//                    alarmEventEntity.setCraftLimitValue("");
//                }
//                if (iulv != null && idlv != null) {
//                    String iulvStr = changeDouble(iulv);
//                    String idlvStr = changeDouble(idlv);
//                    alarmEventEntity.setInterlockLimitValue(idlvStr + "~" + iulvStr);
//                } else if (iulv != null && idlv == null) {
//                    if (iuli.intValue() == 1) {
//                        String iulvStr = changeDouble(iulv);
//                        alarmEventEntity.setInterlockLimitValue("≤" + iulvStr);
//                    } else if (iuli.intValue() == 0) {
//                        String iulvStr = changeDouble(iulv);
//                        alarmEventEntity.setInterlockLimitValue("<" + iulvStr);
//                    }
//                } else if (iulv == null && idlv != null) {
//                    if (idli.intValue() == 1) {
//                        String idlvStr = changeDouble(idlv);
//                        alarmEventEntity.setInterlockLimitValue("≥" + idlvStr);
//                    } else if (idli.intValue() == 0) {
//                        String idlvStr = changeDouble(idlv);
//                        alarmEventEntity.setInterlockLimitValue(">" + idlvStr);
//                    }
//                } else if (iulv == null && idlv == null) {
//                    alarmEventEntity.setInterlockLimitValue("");
//                }
//                if (alarmEventEntity.getEventTypeId().toString().startsWith("10")) {
//                    //限值处理
//                    Long flag = alarmEventEntity.getAlarmFlagId();
//                    if (null != flag && flag == CommonEnum.AlarmFlagEnum.PVHH.getIndex() && alarmPoint.getAlarmPointHH() != null) {
//                        alarmEventEntity.setLimitValue(alarmPoint.getAlarmPointHH());
//                    } else if (null != flag && flag == CommonEnum.AlarmFlagEnum.PVHI.getIndex() && alarmPoint.getAlarmPointHI() != null) {
//                        alarmEventEntity.setLimitValue(alarmPoint.getAlarmPointHI());
//                    } else if (null != flag && flag == CommonEnum.AlarmFlagEnum.PVLL.getIndex() && alarmPoint.getAlarmPointLL() != null) {
//                        alarmEventEntity.setLimitValue(alarmPoint.getAlarmPointLL());
//                    } else if (null != flag && flag == CommonEnum.AlarmFlagEnum.PVLO.getIndex() && alarmPoint.getAlarmPointLO() != null) {
//                        alarmEventEntity.setLimitValue(alarmPoint.getAlarmPointLO());
//                    } else {
//                        alarmEventEntity.setLimitValue(null);
//                    }
//
//                } else {
//                    alarmEventEntity.setLimitValue(null);
//                }
//                alarmEventEntity.setAlarmPointHH(alarmPoint.getAlarmPointHH());
//                alarmEventEntity.setAlarmPointLL(alarmPoint.getAlarmPointLL());
//                alarmEventEntity.setAlarmPointLO(alarmPoint.getAlarmPointLO());
//                alarmEventEntity.setAlarmPointHI(alarmPoint.getAlarmPointHI());
//                alarmEventEntity.setMonitorTypeStr(CommonEnum.MonitorTypeEnum.getName(alarmPoint.getMonitorType()));
//            }
//        }
//        return returnAlarmEvent;
//    }

    @Override
    public PaginationBean<AlarmEventTableVO> getAlarmEventP(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds, Long[] workTeamIds, String alarmPointTag, Long[] alarmFlagIds,
                                                            Integer[] prioritys, Integer monitorType, Integer craftRank, Date startTime, Date endTime, Pagination page, Integer isMatching)
            throws Exception {
        PaginationBean<AlarmEventTableVO> returnAlarmEvent = null;
        try {
            List<UnitEntity> units = null;
            if (unitCodes == null) {
                units = basicDataService.getUnitList(true);
                unitCodes = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
            }
            // 班组
            if (workTeamIds == null) {
                workTeamIds = new Long[]{};
            }
            List<Long> workTeamIdList = Arrays.asList(workTeamIds);
            List<DaterangeEntity> dateRangeList = new ArrayList<>();
            List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
            if (workTeamIdList.size() != 0 && unitCodes != null && unitCodes.length == 1) {
                shiftWorkList = shiftService.getShiftList(unitCodes[0], startTime, endTime, workTeamIdList);
                for (ShiftWorkTeamEntity res : shiftWorkList) {
                    DaterangeEntity dateRange = new DaterangeEntity();
                    dateRange.setStartTime(res.getStartTime());
                    dateRange.setEndTime(res.getEndTime());
                    dateRangeList.add(dateRange);
                }
                logger.info("班组事件");
            }
            Page<AlarmEventTableVO> pager = new Page<AlarmEventTableVO>(page.getPageNumber(), page.getPageSize());
            AlarmEventTableParamVO paramVO = new AlarmEventTableParamVO();
            if (unitCodes != null && unitCodes.length > 0) {
                paramVO.setUnitIds(Arrays.asList(unitCodes));
            }

            if (prdtCellIds != null && prdtCellIds.length > 0) {
                paramVO.setPrdtCellIds(Arrays.asList(prdtCellIds));
            }
            if (eventTypeIds != null && eventTypeIds.length > 0) {
                paramVO.setEventTypeIds(Arrays.asList(eventTypeIds));
            }
            if (StringUtils.isNotBlank(alarmPointTag)) {
                paramVO.setTag(alarmPointTag);
            }
            List<Long> alarmFlagIdList = new ArrayList<>();
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                alarmFlagIdList = Arrays.asList(alarmFlagIds);
            }

            if (!alarmFlagIdList.contains(-9L)) {
                paramVO.setAlarmFlagIds(alarmFlagIdList);
            }
            if (prioritys != null && prioritys.length > 0) {
                List<Integer> priorityList = Arrays.asList(prioritys);
                if (!priorityList.contains(9)) {
                    paramVO.setPriority(priorityList);
                }
            }
            if (monitorType != null && !monitorType.equals(9)) {
                paramVO.setMonitorType(monitorType);
            }
            paramVO.setCraftRank(craftRank);
            if (CollectionUtils.isEmpty(dateRangeList)) {
                paramVO.setBeginTime(startTime);
                paramVO.setEndTime(endTime);
            } else {
                for (DaterangeEntity entity : dateRangeList) {
                    daterangeDAO.insert(entity);
                }

                paramVO.setIsWorkTeam(1);
            }
            Page<AlarmEventTableVO> tablePage = alarmEventDAO.getAlarmEventTable(pager, paramVO);

            List<AlarmEventTableVO> voList = tablePage.getRecords();
            // 装置不选或班组全选时补全班组信息
            if (dateRangeList.size() == 0 && workTeamIdList.size() == 0) {
                Date minDate = voList.stream().reduce((item1, item2) -> item1.getStartTime().getTime() < item2.getStartTime().getTime() ? item1 : item2).orElse(new AlarmEventTableVO()).getStartTime();
                Date maxDate = voList.stream().reduce((item1, item2) -> item1.getStartTime().getTime() > item2.getStartTime().getTime() ? item1 : item2).orElse(new AlarmEventTableVO()).getStartTime();
                if (minDate != null && maxDate != null) {
                    shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCodes), minDate, maxDate);
                }
            }
            getAlarmEventList(voList, shiftWorkList, monitorType);
            returnAlarmEvent = new PaginationBean<AlarmEventTableVO>(page,
                    tablePage.getTotal());
            returnAlarmEvent.setPageList(voList);
        } catch (Exception e) {
            logger.error("报警事件剔除----获取分页数据异常", e);
            throw e;
        }
        return returnAlarmEvent;
    }
    @Transactional
    @Override
    public List<AlarmEventTableVO> getAlarmEventExport(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds, Long[] workTeamIds,
                                                       String alarmPointTag, Long[] alarmFlagIds, Integer[] prioritys, Integer monitorType,
                                                       Integer craftRank, Date startTime, Date endTime, Integer isMatching) throws Exception {
        List<AlarmEventTableVO> voList = null;
        try {
            List<UnitEntity> units = null;
            if (unitCodes == null) {
                units = basicDataService.getUnitList(true);
                unitCodes = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
            }
            // 班组
            if (workTeamIds == null) {
                workTeamIds = new Long[]{};
            }
            List<Long> workTeamIdList = Arrays.asList(workTeamIds);
            List<DaterangeEntity> dateRangeList = new ArrayList<>();
            List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
            if (workTeamIdList.size() != 0 && unitCodes != null && unitCodes.length == 1) {
                shiftWorkList = shiftService.getShiftList(unitCodes[0], startTime, endTime, workTeamIdList);
                for (ShiftWorkTeamEntity res : shiftWorkList) {
                    DaterangeEntity dateRange = new DaterangeEntity();
                    dateRange.setStartTime(res.getStartTime());
                    dateRange.setEndTime(res.getEndTime());
                    dateRangeList.add(dateRange);
                }
                logger.info("班组事件");
            }
            AlarmEventTableParamVO paramVO = new AlarmEventTableParamVO();
            if (unitCodes != null && unitCodes.length > 0) {
                paramVO.setUnitIds(Arrays.asList(unitCodes));
            }

            if (prdtCellIds != null && prdtCellIds.length > 0) {
                paramVO.setPrdtCellIds(Arrays.asList(prdtCellIds));
            }
            if (eventTypeIds != null && eventTypeIds.length > 0) {
                paramVO.setEventTypeIds(Arrays.asList(eventTypeIds));
            }
            if (StringUtils.isNotBlank(alarmPointTag)) {
                paramVO.setTag(alarmPointTag);
            }
            List<Long> alarmFlagIdList = new ArrayList<>();
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                alarmFlagIdList = Arrays.asList(alarmFlagIds);
            }

            if (!alarmFlagIdList.contains(-9L)) {
                paramVO.setAlarmFlagIds(alarmFlagIdList);
            }
            if (prioritys != null && prioritys.length > 0) {
                List<Integer> priorityList = Arrays.asList(prioritys);
                if (!priorityList.contains(9)) {
                    paramVO.setPriority(priorityList);
                }
            }
            if (monitorType != null && !monitorType.equals(9)) {
                paramVO.setMonitorType(monitorType);
            }
            paramVO.setCraftRank(craftRank);
            if (CollectionUtils.isEmpty(dateRangeList)) {
                paramVO.setBeginTime(startTime);
                paramVO.setEndTime(endTime);
            } else {
//                for (DaterangeEntity entity : dateRangeList) {
//                    daterangeDAO.insert(entity);
//                }
                paramVO.setDateRangeList(dateRangeList);
                paramVO.setIsWorkTeam(1);
            }
            voList = alarmEventDAO.getAlarmEventTable(paramVO);

            // 装置不选或班组全选时补全班组信息
            if (dateRangeList.size() == 0 && workTeamIdList.size() == 0) {
                Date minDate = voList.stream().reduce((item1, item2) -> item1.getStartTime().getTime() < item2.getStartTime().getTime() ? item1 : item2).orElse(new AlarmEventTableVO()).getStartTime();
                Date maxDate = voList.stream().reduce((item1, item2) -> item1.getStartTime().getTime() > item2.getStartTime().getTime() ? item1 : item2).orElse(new AlarmEventTableVO()).getStartTime();
                if (minDate != null && maxDate != null) {
                    shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCodes), minDate, maxDate);
                }
            }
            getAlarmEventList(voList, shiftWorkList, monitorType);
        } catch (Exception e) {
            logger.error("报警事件剔除----获取分页数据异常", e);
            throw e;
        }
        return voList;
    }

    @SuppressWarnings("unchecked")
    @Transactional
    @Override
    public PaginationBean<AlarmEventTableVO> getAlarmEventRemoveP(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds, Long[] workTeamIds, String alarmPointTag, Long[] alarmFlagIds,
                                                                  Integer[] prioritys, Integer monitorType, Integer craftRank, Date startTime,
                                                                  Date endTime, Pagination page, Integer isMatching, Integer delstatus)
            throws Exception {
        PaginationBean<AlarmEventTableVO> returnAlarmEvent = null;
        try {
            List<UnitEntity> units = null;
            if (unitCodes == null) {
                units = basicDataService.getUnitList(true);
                unitCodes = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
            }
            // 班组
            if (workTeamIds == null) {
                workTeamIds = new Long[]{};
            }
            List<Long> workTeamIdList = Arrays.asList(workTeamIds);
            List<DaterangeEntity> dateRangeList = new ArrayList<>();
            List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
            if (workTeamIdList.size() != 0 && unitCodes != null && unitCodes.length == 1) {
                shiftWorkList = shiftService.getShiftList(unitCodes[0], startTime, endTime, workTeamIdList);
                for (ShiftWorkTeamEntity res : shiftWorkList) {
                    DaterangeEntity dateRange = new DaterangeEntity();
                    dateRange.setStartTime(res.getStartTime());
                    dateRange.setEndTime(res.getEndTime());
                    dateRangeList.add(dateRange);
                }
                logger.info("班组事件");
            }
            Page<AlarmEventTableVO> pager = new Page<AlarmEventTableVO>(page.getPageNumber(), page.getPageSize());
            AlarmEventTableParamVO paramVO = new AlarmEventTableParamVO();
            if (unitCodes != null && unitCodes.length > 0) {
                paramVO.setUnitIds(Arrays.asList(unitCodes));
            }

            if (prdtCellIds != null && prdtCellIds.length > 0) {
                paramVO.setPrdtCellIds(Arrays.asList(prdtCellIds));
            }
            if (eventTypeIds != null && eventTypeIds.length > 0) {
                paramVO.setEventTypeIds(Arrays.asList(eventTypeIds));
            }
            if (StringUtils.isNotBlank(alarmPointTag)) {
                paramVO.setTag(alarmPointTag);
            }
            List<Long> alarmFlagIdList = new ArrayList<>();
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                alarmFlagIdList = Arrays.asList(alarmFlagIds);
            }

            if (!alarmFlagIdList.contains(-9L)) {
                paramVO.setAlarmFlagIds(alarmFlagIdList);
            }
            if (prioritys != null && prioritys.length > 0) {
                List<Integer> priorityList = Arrays.asList(prioritys);
                if (!priorityList.contains(9)) {
                    paramVO.setPriority(priorityList);
                }
            }
            if (monitorType != null && !monitorType.equals(9)) {
                paramVO.setMonitorType(monitorType);
            }
            paramVO.setCraftRank(craftRank);
            if (CollectionUtils.isEmpty(dateRangeList)) {
                paramVO.setBeginTime(startTime);
                paramVO.setEndTime(endTime);
            } else {
                for (DaterangeEntity entity : dateRangeList) {
                    daterangeDAO.insert(entity);
                }

                paramVO.setIsWorkTeam(1);
            }
            paramVO.setDelstatus(delstatus);
            Page<AlarmEventTableVO> tablePage = alarmEventDAO.getAlarmEventTable(pager, paramVO);

            List<AlarmEventTableVO> voList = tablePage.getRecords();
            // 装置不选或班组全选时补全班组信息
            if (dateRangeList.size() == 0 && workTeamIdList.size() == 0) {
                Date minDate = voList.stream().reduce((item1, item2) -> item1.getStartTime().getTime() < item2.getStartTime().getTime() ? item1 : item2).orElse(new AlarmEventTableVO()).getStartTime();
                Date maxDate = voList.stream().reduce((item1, item2) -> item1.getStartTime().getTime() > item2.getStartTime().getTime() ? item1 : item2).orElse(new AlarmEventTableVO()).getStartTime();
                if (minDate != null && maxDate != null) {
                    shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCodes), minDate, maxDate);
                }
            }
            getAlarmEventList(voList, shiftWorkList, monitorType);
            returnAlarmEvent = new PaginationBean<AlarmEventTableVO>(page,
                    tablePage.getTotal());
            returnAlarmEvent.setPageList(voList);
        } catch (Exception e) {
            logger.error("报警事件剔除----获取分页数据异常", e);
            throw e;
        }
        return returnAlarmEvent;
    }

    private void getAlarmEventList(List<AlarmEventTableVO> voList, List<ShiftWorkTeamEntity> shiftWorkList, Integer monitorType) {
        // 映射字段
        for (AlarmEventTableVO alarmEventEntity : voList) {
            Integer priority = alarmEventEntity.getPriority();
            if (priority != null) {
                alarmEventEntity.setPriorityName(CommonEnum.AlarmPriorityEnum.getName(priority));
            }

            Integer monitorType1 = alarmEventEntity.getMonitorType();
            if (monitorType1 != null) {
                alarmEventEntity.setMonitorTypeStr(CommonEnum.MonitorTypeEnum.getName(monitorType1));
            }
            // 填充是否匹配
            alarmEventEntity.setAlarmPointExplain(null == alarmEventEntity.getAlarmPointId() ? "未匹配" : null);
            // 填充班组名称
            alarmEventEntity.setWorkTeamSName(shiftWorkList.parallelStream().filter(item ->
                            alarmEventEntity.getStartTime().getTime() >= item.getStartTime().getTime()
                                    && alarmEventEntity.getStartTime().getTime() < item.getEndTime().getTime())
                    .findFirst().orElse(new ShiftWorkTeamEntity()).getWorkTeamSName());
            //工艺卡片值 联锁值赋值
            Double culv = alarmEventEntity.getCraftUpLimitValue();//工艺卡片上限值
            Double cdlv = alarmEventEntity.getCraftDownLimitValue();//工艺卡片下限值
            Integer culi = alarmEventEntity.getCraftUpLimitInclude();//工艺卡片上限值是否包含(1是；0否)
            Integer cdli = alarmEventEntity.getCraftDownLimitInclude();//工艺卡片下限值是否包含(1是；0否)
            Double iulv = alarmEventEntity.getInterlockUpLimitValue();//联锁上限值
            Double idlv = alarmEventEntity.getInterlockDownLimitValue();//联锁下限值
            Integer iuli = alarmEventEntity.getInterlockUpLimitInclude();//联锁上限值是否包含(1是；0否)
            Integer idli = alarmEventEntity.getInterlockDownLimitInclude();//联锁下限值是否包含(1是；0否)
            if (culv != null && cdlv != null) {
                String culvStr = changeDouble(culv);
                String cdlvStr = changeDouble(cdlv);
                alarmEventEntity.setCraftLimitValue(cdlvStr + "~" + culvStr);
            } else if (culv != null && cdlv == null) {
                if (culi != null && culi.intValue() == 1) {
                    String culvStr = changeDouble(culv);
                    alarmEventEntity.setCraftLimitValue("≤" + culvStr);
                } else if (culi != null && culi.intValue() == 0) {
                    String culvStr = changeDouble(culv);
                    alarmEventEntity.setCraftLimitValue("<" + culvStr);
                }
            } else if (culv == null && cdlv != null) {
                if (cdli != null && cdli.intValue() == 1) {
                    String cdlvStr = changeDouble(cdlv);
                    alarmEventEntity.setCraftLimitValue("≥" + cdlvStr);
                } else if (cdli != null && cdli.intValue() == 0) {
                    String cdlvStr = changeDouble(cdlv);
                    alarmEventEntity.setCraftLimitValue(">" + cdlvStr);
                }
            } else if (culv == null && cdlv == null) {
                alarmEventEntity.setCraftLimitValue("");
            }
            if (iulv != null && idlv != null) {
                String iulvStr = changeDouble(iulv);
                String idlvStr = changeDouble(idlv);
                alarmEventEntity.setInterlockLimitValue(idlvStr + "~" + iulvStr);
            } else if (iulv != null && idlv == null) {
                if (iuli.intValue() == 1) {
                    String iulvStr = changeDouble(iulv);
                    alarmEventEntity.setInterlockLimitValue("≤" + iulvStr);
                } else if (iuli.intValue() == 0) {
                    String iulvStr = changeDouble(iulv);
                    alarmEventEntity.setInterlockLimitValue("<" + iulvStr);
                }
            } else if (iulv == null && idlv != null) {
                if (idli.intValue() == 1) {
                    String idlvStr = changeDouble(idlv);
                    alarmEventEntity.setInterlockLimitValue("≥" + idlvStr);
                } else if (idli.intValue() == 0) {
                    String idlvStr = changeDouble(idlv);
                    alarmEventEntity.setInterlockLimitValue(">" + idlvStr);
                }
            } else if (iulv == null && idlv == null) {
                alarmEventEntity.setInterlockLimitValue("");
            }
            if (alarmEventEntity.getEventTypeId().toString().startsWith("10")) {
                //限值处理
                Long flag = alarmEventEntity.getAlarmFlagId();
                if (null != flag && flag == CommonEnum.AlarmFlagEnum.PVHH.getIndex() && alarmEventEntity.getAlarmPointHH() != null) {
                    alarmEventEntity.setLimitValue(alarmEventEntity.getAlarmPointHH());
                } else if (null != flag && flag == CommonEnum.AlarmFlagEnum.PVHI.getIndex() && alarmEventEntity.getAlarmPointHI() != null) {
                    alarmEventEntity.setLimitValue(alarmEventEntity.getAlarmPointHI());
                } else if (null != flag && flag == CommonEnum.AlarmFlagEnum.PVLL.getIndex() && alarmEventEntity.getAlarmPointLL() != null) {
                    alarmEventEntity.setLimitValue(alarmEventEntity.getAlarmPointLL());
                } else if (null != flag && flag == CommonEnum.AlarmFlagEnum.PVLO.getIndex() && alarmEventEntity.getAlarmPointLO() != null) {
                    alarmEventEntity.setLimitValue(alarmEventEntity.getAlarmPointLO());
                } else {
                    alarmEventEntity.setLimitValue(null);
                }

            } else {
                alarmEventEntity.setLimitValue(null);
            }
            alarmEventEntity.setDelStatusShow(AlarmPointDelConfigDTOEntity.getDelStatusShowDetail(alarmEventEntity.getDelStatus()));
        }
    }

    //Double类型转String
    private String changeDouble(Double num) {
        if ((num + "").endsWith(".0")) {
            return num.intValue() + "";
        }
        return num + "";
    }

    /**
     * 根据事件类型ID获取当前节点及子节点数据
     *
     * @param id 事件类型ID
     * @return 事件类型树形实体集合
     * <AUTHOR> 2017-10-10
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<EventTypeEntity> getAllEventType(Long id) throws Exception {
        List<EventTypeEntity> entityList = new ArrayList<>();
        List<EventTypeEntity> eventList;
        //1.获取所有的事件类型列表
        List<EventType> allEventTypeList = eventTypeRepository.getEventType();

        List<EventTypeEntity> eventTypeEntityList = ObjectConverter.listConverter(allEventTypeList, EventTypeEntity.class);
        return eventTypeEntityList;
    }

    /**
     * 递归获取子节点数据
     *
     * @param allEventTypeList 全部事件类型集合
     * @param eventTypeId      事件类型ID
     * @param entityList       事件类型集合
     * <AUTHOR> 2017-10-10
     */
    private void getChildNode(List<EventTypeEntity> allEventTypeList, Long eventTypeId, List<EventTypeEntity> entityList) {
        List<EventTypeEntity> eventList = allEventTypeList.stream().filter(x -> x.getEventTypeId().equals(eventTypeId)).collect(Collectors.toList());
        entityList.addAll(eventList);

        List<EventTypeEntity> childList = allEventTypeList.stream().filter(x -> eventTypeId.equals(x.getParentId())).collect(Collectors.toList());

        if (childList != null && childList.stream().count() > 0) {
            entityList.addAll(childList);
            childList.stream().forEach(x -> {
                long childCount = allEventTypeList.stream().filter(y -> x.getEventTypeId().equals(y.getParentId())).count();
                if (childCount > 0) {
                    getChildNode(allEventTypeList, x.getEventTypeId(), entityList);
                }
            });
        }
    }

    /**
     * 获取报警标识列表
     *
     * @param isAll
     * @return
     */
    @Override
    public List<DictionaryEntity> getAlarmFlagList(boolean isAll) {
        List<DictionaryEntity> alarmFlagList = basicDataService.getAlarmFlagList(isAll);
        DictionaryEntity allEntity = new DictionaryEntity(-9, "空");
//        alarmFlagList.add(0, allEntity);
        alarmFlagList.add(allEntity);
        return alarmFlagList;
    }

    @Override
    public void getSendMsgInfo(URL msgUrl) throws Exception {
        try {
            SystRunParaConfEntity alarmEventMsgDelayedTimeEntity = basicDataService.getSystRunParamByCode("AlarmEventMsgDelayedTime");
            if (alarmEventMsgDelayedTimeEntity == null || StringUtils.isBlank(alarmEventMsgDelayedTimeEntity.getCode())) {
                throw new Exception("报警事件短信延迟时间(分钟)参数");
            }
            SystRunParaConfEntity alarmEventMsgIntervalTimeEntity = basicDataService.getSystRunParamByCode("AlarmEventMsgIntervalTime");
            if (alarmEventMsgIntervalTimeEntity == null || StringUtils.isBlank(alarmEventMsgIntervalTimeEntity.getCode())) {
                throw new Exception("报警事件短信推送间隔时间(分钟)参数");
            }
            Integer alarmEventMsgDelayedTime = Integer.valueOf(alarmEventMsgDelayedTimeEntity.getParaValue());
            Integer alarmEventMsgIntervalTime = Integer.valueOf(alarmEventMsgIntervalTimeEntity.getParaValue());
            long start = DateHelper.now().getTime() - alarmEventMsgDelayedTime * 60 * 1000 - alarmEventMsgIntervalTime * 60 * 1000;
            long end = DateHelper.now().getTime() - alarmEventMsgDelayedTime * 60 * 1000;
            Date startTime = new Date(start);
            Date endTime = new Date(end);
            //测试用数据
//            Date startTime = new Date(1540876030000L);
//            Date endTime = new Date(1540879630000L);

            ThreadPoolUtils.execute(() -> {
                try {
                    sendMsgInfo(startTime, endTime, msgUrl);
                } catch (Exception e) {
                    logger.info(e.getMessage());
                }
            });

        } catch (Exception e) {
            logger.error("短信发送失败：" + e.getMessage());
        }

    }

    private void sendMsgInfo(Date startTime, Date endTime, URL msgUrl) throws Exception {
        List alarmEventEntityList = alarmEventRepository.getSendMsgInfo(startTime, endTime);
        if (null != alarmEventEntityList && alarmEventEntityList.size() > 0) {
            for (int i = 0; i < alarmEventEntityList.size(); i++) {
                // 报警时间+“，”+位号+描述信息+报警标识(缓存表)+“报警，请注意”
                //ap.prdtcell_id, ae.tag, min(ap.mobile_phone),min(ae.alarm_time),min(ae.des),min(ae.alarm_flag)
                StringBuilder msgInfo = new StringBuilder();
                Object[] o = (Object[]) alarmEventEntityList.get(i);
                String des = "";
                if (null != o[4]) {
                    des = o[4].toString();
                }
                String flag = "";
                if (null != o[5]) {
                    flag = o[5].toString();
                }
                msgInfo.append("操作报警系统：" + o[3].toString().substring(0, 19) + ", " + o[1].toString() + " " + des + " " + flag + " 报警，请注意");
                String phoneStr = "";
                if (null != o[2])
                    phoneStr = o[2].toString();

                Long alarmPoint = Long.valueOf(o[6].toString());

                List<AlarmMobileConf> alarmMobileConfPointId = alarmMobileConfRepository.getAlarmMobileConfPointId(new Long[]{alarmPoint});

                List<Long> mobileIds = alarmMobileConfPointId.stream().map(AlarmMobileConf::getMobileListId).collect(Collectors.toList());
                List<MobileList> mobileLists = mobileListRepository.getMobileListByIds(mobileIds);
                Map<String, List<MobileList>> mobileMap = mobileLists.stream().collect(Collectors.groupingBy(MobileList::getMobile));
                if (null != mobileLists && mobileLists.size() > 0) {
                    /*if (mobile.endsWith(",")){
                        mobile = mobile.substring(0,mobile.length()-1);
                    }*/
                    for (MobileList mobileList : mobileLists) {
                        if (StringUtils.isNotBlank(phoneStr)) {
                            phoneStr += "," + mobileList.getMobile();
                        } else {
                            phoneStr += mobileList.getMobile();
                        }
                    }
                }
                logger.info("发送短信内容：" + phoneStr + " " + msgInfo.toString());
                try {
                    logger.info("实例化前：new SendShortMessages()");
                    SendShortMessages sendShortMessages = new SendShortMessages(msgUrl);
                    logger.info("实例化：new SendShortMessages()");
                    SendShortMessagesSoap sendShortMessagesSoap = sendShortMessages.getSendShortMessagesSoap();
                    logger.info("实例化：sendShortMessages.getSendShortMessagesSoap();");
                    String message = sendShortMessagesSoap.sendShortMessageByPhone(phoneStr, msgInfo.toString(), "D22D8664-A99C-4393-A19D-5B85B2011F45");
                    logger.info(message);
                    //String message = "发送成功";
                    Date now = DateHelper.now();
                    String[] phoneNums = phoneStr.split(",");
                    for (String s : phoneNums) {
                        //ap.prdtcell_id, ae.tag, min(ap.mobile_phone), min(ae.alarm_time),  min(ae.des),min(ae.alarm_flag),min(ap.alarm_point_id)
                        boolean b = mobileMap.containsKey(s);
                        MobileMsgList mobileMsgList = new MobileMsgList();
                        //工厂ID
                        mobileMsgList.setFactoryId(b ? mobileMap.get(s).get(0).getFactoryId() : null);
                        //车间ID
                        mobileMsgList.setWorkshopId(b ? mobileMap.get(s).get(0).getWorkshopId() : null);
                        //装置编码
                        mobileMsgList.setUnitCode(b ? mobileMap.get(s).get(0).getUnitCode() : null);
                        //生产单元ID
                        mobileMsgList.setPrdtCellId(Long.valueOf(o[0].toString()));
                        //位号
                        mobileMsgList.setTag(o[1].toString());
                        //报警标识ID
                        mobileMsgList.setAlarmFlagId(Long.valueOf(o[7].toString()));
                        //报警时间
                        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date alarmTime = format.parse(o[3].toString());
                        mobileMsgList.setAlarmTime(alarmTime);
                        //接收人
                        mobileMsgList.setName(b ? mobileMap.get(s).get(0).getName() : null);
                        //手机号
                        mobileMsgList.setMobile(s);
                        //短信内容
                        mobileMsgList.setContent(msgInfo.toString());
                        //状态（1已发送；2发送失败；）
                        if (message.equals("发送成功")) {
                            mobileMsgList.setStatus(CommonEnum.SendMsgStatusEnum.Success.getIndex());
                        } else {
                            mobileMsgList.setStatus(CommonEnum.SendMsgStatusEnum.Fail.getIndex());
                        }
                        //返回结果
                        mobileMsgList.setResult(message);
                        //发送时间
                        mobileMsgList.setSendTime(now);
                        CommonResult commonResult = mobileMsgListRepository.add(mobileMsgList);
                    }
                } catch (Exception e) {
                    logger.error("短信发送失败信息：" + phoneStr + " " + msgInfo.toString() + " 短信发送失败：" + e.getMessage());
                }
            }
        } else {
            logger.error("从" + startTime + "到" + endTime + "期间。没有可发送短信目标对象数据信息");
        }
    }

    @Override
    public CommonResult getAlarmEventForInterface(String unitName, String prdtCellName, Long[] eventTypeIds, /*Long[] workTeamIds*/String workTeamName, String alarmPointTag, Long[] alarmFlagId, Integer priority, Integer craftRank, Date startTime, Date endTime, String[] workUnitIds, Pagination page) throws Exception {

        CommonResult commonResult = new CommonResult();
        Pagination page1 = new Pagination();
        page1.setPageSize(Integer.MAX_VALUE);
        page1.setPageNumber(1);

        if (null != eventTypeIds) {
            List<EventTypeEntity> eventTypeList = basicDataService.getEventTypeList(false);
            List<Long> eventTypeIdList = eventTypeList.stream().map(x -> x.getEventTypeId()).collect(Collectors.toList());
            List<Long> longs = Arrays.asList(eventTypeIds);
            List<Long> collect = longs.stream().filter(item -> eventTypeIdList.contains(item)).collect(Collectors.toList());
            if (null != collect && collect.size() > 0) {
                eventTypeIds = collect.toArray(new Long[collect.size()]);
            } else {
                commonResult.setMessage("事件类型输入错误！！");
                commonResult.setIsSuccess(false);
                return commonResult;
            }
        }
        Long[] workTeamIds = null;
        /*if (null != workTeamName){
            List<UnitEntity> unitList = basicDataService.getUnitList(false);
            List<String> collect1 = unitList.stream().map(x -> x.getStdCode()).collect(Collectors.toList());

            List<ShiftWorkTeamEntity> shiftWorkTeamList = shiftService.getShiftWorkTeamList(collect1, startTime, endTime);
            List<ShiftWorkTeamEntity> collect = shiftWorkTeamList.stream().filter(item -> item.getWorkTeamSName().equals(workTeamName)).collect(Collectors.toList());
            List<Long> collect2 = collect.stream().map(x -> x.getWorkTeamId()).collect(Collectors.toList());
            if (collect.size()>0){
                workTeamIds =collect2.toArray(new Long[collect2.size()]);
            }else {
                commonResult.setMessage("班组输入错误！！");
                commonResult.setIsSuccess(false);
                return commonResult;
            }
        }*/
        if (null != alarmFlagId) {
            List<DictionaryEntity> alarmFlagList = getAlarmFlagList(false);
            List<Long> alarmFlagIdList = alarmFlagList.stream().map(x -> Long.valueOf(x.getKey().toString())).collect(Collectors.toList());
            List<Long> longs = Arrays.asList(alarmFlagId);
            List<Long> collect = longs.stream().filter(item -> alarmFlagIdList.contains(item)).collect(Collectors.toList());
            if (null != collect && collect.size() > 0) {
                alarmFlagId = collect.toArray(new Long[collect.size()]);
            } else {
                commonResult.setMessage("报警标识输入错误！！");
                commonResult.setIsSuccess(false);
                return commonResult;
            }

        }
        if (null != priority) {
            List<DictionaryEntity> alarmPriorityList = alarmDurationStattService.getAlarmPriorityList(false);
            List<Integer> priorityIdList = alarmPriorityList.stream().map(x -> (Integer) x.getKey()).collect(Collectors.toList());
            if (!priorityIdList.contains(priority)) {

                commonResult.setMessage("优先级输入错误！！");
                commonResult.setIsSuccess(false);
                return commonResult;
            }
        }
        if (null != craftRank) {
            List<DictionaryEntity> craftRankList = basicDataService.getCraftRankList(false);
            List<Integer> craftRankIdList = craftRankList.stream().map(x -> (Integer) x.getKey()).collect(Collectors.toList());
            if (!craftRankIdList.contains(craftRank)) {

                commonResult.setMessage("级别输入错误！！");
                commonResult.setIsSuccess(false);
                return commonResult;
            }
        }

        List<String> unitCodeList = new ArrayList<>();
        //模糊查询装置名称

        if (StringUtils.isNotBlank(unitName)) {
            PaginationBean<DBUnitEntity> unitList = unitService.getUnitList(null, null, unitName, null, 1, page1);
            unitCodeList = unitList.getPageList().stream().map(p -> p.getStdCode()).collect(Collectors.toList());
            if (null == unitCodeList || unitCodeList.size() == 0) {
                commonResult.setMessage("本装置无效");
                commonResult.setIsSuccess(false);
                return commonResult;
            }
        }
        //通过车间编码获取装置
        List<String> unitCodeList2 = new ArrayList<>();
        if (null != workUnitIds && workUnitIds.length > 0) {
            List<UnitEntity> unitListByWorkshopIds = basicDataService.getUnitListByWorkshopIds(workUnitIds, false);
            if (null == unitListByWorkshopIds || unitListByWorkshopIds.size() == 0) {
                commonResult.setMessage("本车间编码无效");
                commonResult.setIsSuccess(false);
                return commonResult;
            }
            unitCodeList2 = unitListByWorkshopIds.stream().map(p -> p.getStdCode()).collect(Collectors.toList());
        }
        if (null != unitCodeList && unitCodeList.size() > 0 && null != unitCodeList2 && unitCodeList2.size() > 0) {
            //unitCodeList.addAll(unitCodeList2);
            List<String> finalUnitCodeList = unitCodeList2;
            unitCodeList = unitCodeList.stream().filter(item -> finalUnitCodeList.contains(item)).collect(Collectors.toList());
            unitCodeList = unitCodeList.stream().distinct().collect(Collectors.toList());
            if (null == unitCodeList || unitCodeList.size() == 0) {
                commonResult.setMessage("本车间编码中装置与传入装置不匹配");
                commonResult.setIsSuccess(false);
                return commonResult;
            }
        } else if ((null == unitCodeList || unitCodeList.size() == 0) && null != unitCodeList2 && unitCodeList2.size() > 0) {
            unitCodeList.addAll(unitCodeList2);
            unitCodeList = unitCodeList.stream().distinct().collect(Collectors.toList());
        }
        //模糊查询生产单元
        Long[] prdtCellIds = null;
        if (StringUtils.isNotBlank(prdtCellName)) {
            PaginationBean<PrdtCellEntity> prdtCell = prdtCellService.getPrdtCell(null, prdtCellName, 1, page1);
            if (null != prdtCell && prdtCell.getPageList().size() > 0) {
                List<Long> prdtCellList = prdtCell.getPageList().stream().map(p -> p.getPrdtCellId()).collect(Collectors.toList());
                List<String> u = prdtCell.getPageList().stream().map(p -> p.getUnitId()).collect(Collectors.toList());
                prdtCellIds = prdtCellList.toArray(new Long[prdtCellList.size()]);
                if (unitCodeList.size() > 0) {
                    unitCodeList = unitCodeList.stream().filter(item -> u.contains(item)).collect(Collectors.toList());
                    if (unitCodeList.size() == 0) {
                        prdtCellIds = null;
                        unitCodeList.clear();
                    }
                } else {
                    unitCodeList = u;
                }
            } else {
                commonResult.setMessage("本生产单元无效");
                commonResult.setIsSuccess(false);
                return commonResult;
            }
        }
        if (StringUtils.isNotBlank(unitName) || StringUtils.isNotBlank(prdtCellName) || (null != workUnitIds && workUnitIds.length > 0)) {
            if (unitCodeList.size() == 0 && (null == prdtCellIds || prdtCellIds.length == 0)) {
                commonResult.setMessage("本装置中生产单元与传入生产单元不匹配");
                commonResult.setIsSuccess(false);
                return commonResult;
            }
            if (StringUtils.isNotBlank(workTeamName)) {
                /*List<UnitEntity> unitList = basicDataService.getUnitList(false);
                List<String> collect1 = unitList.stream().map(x -> x.getStdCode()).collect(Collectors.toList());*/
                if (unitCodeList.size() > 1) {
                    commonResult.setMessage("请选择单个装置");
                    commonResult.setIsSuccess(false);
                    return commonResult;
                } else {
                    List<ShiftWorkTeamEntity> shiftWorkTeamList = shiftService.getShiftWorkTeamList(unitCodeList, startTime, endTime);
                    List<ShiftWorkTeamEntity> collect = shiftWorkTeamList.stream().filter(item -> item.getWorkTeamSName().contains(workTeamName)).collect(Collectors.toList());
                    List<Long> collect2 = collect.stream().map(x -> x.getWorkTeamId()).collect(Collectors.toList());
                    if (collect.size() > 0) {
                        workTeamIds = collect2.toArray(new Long[collect2.size()]);
                    } else {
                        commonResult.setMessage("班组输入错误！！");
                        commonResult.setIsSuccess(false);
                        return commonResult;
                    }
                }
            }
        } else {
            if (null != workTeamName) {
                commonResult.setMessage("请输入一个装置！！");
                commonResult.setIsSuccess(false);
                return commonResult;
            }
        }
        String[] UnitCodes = unitCodeList.toArray(new String[unitCodeList.size()]);
        PaginationBean<AlarmEventEntity> alarmEvent = getAlarmEvent(UnitCodes, prdtCellIds, eventTypeIds, workTeamIds, alarmPointTag, alarmFlagId, priority, craftRank, startTime, endTime, page, -1);

        PaginationBean<AlarmEventShowEntity> returnBeans = new PaginationBean<>(page, alarmEvent.getTotal());
        List list = ObjectConverter.listConverter(alarmEvent.getPageList(), AlarmEventShowEntity.class);
        returnBeans.setPageList(list);
        commonResult.setResult(returnBeans);
        return commonResult;
    }

    @Override
    public List<MonitoringDataEntity> getMonitoringData() throws Exception {

        List<MonitoringDataEntity> returnList = new ArrayList<>();

        Date now = DateHelper.now();
        String dateStr = DateHelper.format(now);
        List<UnitEntity> unitEntityList = basicDataService.getUnitList(false);
        List<Object[]> alarmEventList = alarmEventRepository.getMonitoringData(dateStr);
        for (int i = 0; i < alarmEventList.size(); i++) {
            Object[] o = alarmEventList.get(i);
            MonitoringDataEntity monitoringDataEntity = new MonitoringDataEntity();
            //dcs名称
            monitoringDataEntity.setDcsCodeName(o[0].toString());

            UnitEntity unit = unitEntityList.stream().filter(u -> o[1].toString().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
            String unitName = unit.getSname();
            monitoringDataEntity.setUnitCodeName(unitName);
            //生产单元名称
            monitoringDataEntity.setPrdtcellName(o[2].toString());
            monitoringDataEntity.setOpcName(o[3].toString());
            Date date = DateHelper.parseDate(o[4].toString().substring(0, 19));
            monitoringDataEntity.setStartTime(date);
            //monitoringDataEntity.setDuration("> 120");
            returnList.add(monitoringDataEntity);
        }
        return returnList;
    }

    @Override
    public PaginationBean<MobileMsgListEntity> getAlarmMsgConfig(String[] unitCodes, Long[] prdtCellIds, String tag, Integer status, Date startSendTime, Date endSendTime, Pagination page) throws Exception {

        //根据权限获取装置
        List<UnitEntity> units = basicDataService.getUnitListByIds(unitCodes, true);
        unitCodes = units.stream().map(UnitEntity::getStdCode).toArray(String[]::new);

        PaginationBean<MobileMsgList> mobileMsgListPaginationBean = mobileMsgListRepository.getAlarmMsgConfig(unitCodes, prdtCellIds, tag, status, startSendTime, endSendTime, page);
        PaginationBean<MobileMsgListEntity> returnMobileMsgListEntity = new PaginationBean<MobileMsgListEntity>(page,
                mobileMsgListPaginationBean.getTotal());
        returnMobileMsgListEntity.setPageList(ObjectConverter.listConverter(mobileMsgListPaginationBean.getPageList(), MobileMsgListEntity.class));

        for (int i = 0; i < returnMobileMsgListEntity.getPageList().size(); i++) {
            MobileMsgListEntity mobileMsgListEntity = returnMobileMsgListEntity.getPageList().get(i);
            MobileMsgList mobileMsgList = mobileMsgListPaginationBean.getPageList().get(i);
            //装置
            if (null != mobileMsgList.getUnitCode()) {
                UnitEntity unit = units.stream().filter(u -> mobileMsgList.getUnitCode().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
                mobileMsgListEntity.setUnitName(unit.getSname());
            }
            //报警标识
            mobileMsgListEntity.setAlarmFlagName(mobileMsgList.getAlarmFlag().getName());
            //生产单元
            mobileMsgListEntity.setPrdtcellName(mobileMsgList.getPrdtCell().getSname());
        }
        return returnMobileMsgListEntity;
    }

    @Override
    public List<DictionaryEntity> getAlarmMsgConfigStatus() {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));

            for (CommonEnum.SendMsgStatusEnum sendMsgStatusEnum : CommonEnum.SendMsgStatusEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(sendMsgStatusEnum.getIndex(), sendMsgStatusEnum.getName()));
            }
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    @Override
    public List<AlarmRec> getAlarmEventInfoByAlarmTime(String startDate, String end, Integer companyId) {
        List<AlarmRec> list = alarmEventRepository.findAlarmEventInfoByAlarmTime(startDate, end, companyId);
        return list;
    }

    @Override
    public List<AlarmRec> getAlarmEventInfoByStartTime(String startDate, String end, Integer companyId) {
        List<AlarmRec> obj = alarmEventRepository.findAlarmEventInfoByStartTime(startDate, end, companyId);

        return obj;
    }

    @Override
    public String commitAlarmEvent(Long[] eventIds, String reason) {

        // 判断选中的事件是否存在于剔除审批表中
        boolean exists = alarmEventDelApproveRepository.existsAlarmEvent(eventIds, new Integer[]{1, 2});

        if (exists) {
            throw new RuntimeException("选中的数据已经存在审批表中，请检查");
        }

        List<AlarmEvent> eventList = this.alarmEventRepository.findAllById(Arrays.asList(eventIds));
        ArrayList<AlarmEventDelApprove> alarmEventDelApproves = new ArrayList<>();

        for (AlarmEvent alarmEvent : eventList) {

            // 判断该条数据是否存在审批表，如果存在则删除
            AlarmEventDelApprove alarmEventDelApprove = new AlarmEventDelApprove();
            alarmEventDelApprove.setDelStatus(3);
            alarmEventDelApprove.setEventId(alarmEvent.getEventId());
            List<AlarmEventDelApprove> all = alarmEventDelApproveRepository.findAll(Example.of(alarmEventDelApprove));
            if (!all.isEmpty()) {
                alarmEventDelApproveRepository.deleteAll(all);
            }
            // -----------


            AlarmEventDelApprove a = new AlarmEventDelApprove();
            a.setUnitCode(alarmEvent.getUnitCode());
            a.setEventId(alarmEvent.getEventId());
            a.setCompanyId(alarmEvent.getCompanyId());
            a.setDelStatus(1);
            a.setDelDataStatus(0);
            a.setReason(reason);

            try {
                CommonUtil.returnValue(a, 1);
            } catch (Exception e) {
                logger.error("创建人赋值失败-{}", e.getMessage());
            }
            alarmEventDelApproves.add(a);
        }

        try {
            this.alarmEventDelApproveRepository.saveAll(alarmEventDelApproves);
            return "提交成功";
        } catch (Exception e) {
            logger.error("事件表剔除提交失败-{}", e.getMessage());
            return "提交失败";
        }
    }

    @Override
    public String saveAlarmEvent(Long[] eventIds, String reason) {
        return null;
    }

}
