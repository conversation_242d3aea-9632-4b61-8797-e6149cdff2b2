package com.pcitc.opal.common.annotation;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Set;

/**
 * <AUTHOR>
 * @USER: chenbo
 * @DATE: 2023/3/27
 * @DESC: CacheRemove功能实现
 **/
@Aspect
@Component
@Slf4j
public class CacheRemoveAspect {


    @Resource
    private RedisTemplate redisTemplate;


    @AfterReturning("@annotation(com.pcitc.opal.common.annotation.CacheRemove)")
    public void remove(JoinPoint point) {
        Method method = ((MethodSignature) point.getSignature()).getMethod();
        CacheRemove cacheRemove = method.getAnnotation(CacheRemove.class);
        String[] keys = cacheRemove.value();
        for (String key : keys) {
            //防止全部删除
            if ("*".equals(key)){
                continue;
            }
            Set deleteKeys = redisTemplate.keys(key);
            if (CollectionUtils.isNotEmpty(deleteKeys)){
                log.info("删除缓存key-[{}], 结果[{}] ", key, redisTemplate.delete(deleteKeys));
            }
        }
    }

}
