package com.pcitc.opal.ad.bll.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pcitc.opal.common.bll.entity.BasicEntity;

import java.util.Date;

/*
 * 报警事件视图实体
 * 模块编号：pcitc_opal_bll_class_AlarmEventViewEntity
 * 作    者：xuelei.wang
 * 创建时间：2017/11/15
 * 修改编号：1
 * 描    述：报警事件视图实体
 */
public class AlarmEventViewEntity extends BasicEntity {

    /**
     *报警事件ID
     */
    private Long eventId;

    /**
     * 生产单元ID
     */
    private Long prdtCellId;
    /**
     *报警点ID
     */
    private Long alarmPointId;

    /**
     *报警次数
     */
    private Long alarmTimes;

    /**
     *装置编码
     */
    private String unitId;

    /**
     *装置简称
     */
    private String unitSname;

    /**
     *操作人员数
     */
    private Long operNum;

    /**
     *报警时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date alarmTime;

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    public Long getAlarmTimes() {
        return alarmTimes;
    }

    public void setAlarmTimes(Long alarmTimes) {
        this.alarmTimes = alarmTimes;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getUnitSname() {
        return unitSname;
    }

    public void setUnitSname(String unitSname) {
        this.unitSname = unitSname;
    }

    public Long getOperNum() {
        return operNum;
    }

    public void setOperNum(Long operNum) {
        this.operNum = operNum;
    }

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public Long getPrdtCellId() {
        return prdtCellId;
    }

    public void setPrdtCellId(Long prdtCellId) {
        this.prdtCellId = prdtCellId;
    }
}
