package com.pcitc.opal.ad.bll.entity;

import com.pcitc.opal.common.CommonEnum;

import java.util.Date;

/*
 * 报警事件视图实体
 * 模块编号：pcitc_opal_bll_class_AlarmEventViewExEntity
 * 作       者：dageng.sun
 * 创建时间：2017/10/25
 * 修改编号：1
 * 描       述：报警事件视图实体
 */
public class AlarmEventViewExEntity {
	
	/**
     * 报警事件ID
     */
    private Long eventId;

    /**
     * 事件类型ID
     */
    private Long eventTypeId;

    /**
     *报警点ID
     */
    private Long alarmPointId;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     *发生时间
     */
    private Date startTime;

    /**
     *报警时间
     */
    private Date alarmTime;

    /**
     *优先级(1紧急；2重要；3一般)
     */
    private Integer priority;
    
    /**
	 *优先级名称
	 */
	@SuppressWarnings("unused")
	private String priorityName;

    /**
     *先前值
     */
    private String previousValue;

    /**
     *值
     */
    private String nowValue;

    /**
     *限值
     */
    private Double limitValue;

    /**
     *是否搁置(1是；0否)
     */
    private Integer inShelved;
    
    /**
	 *是否搁置名称
	 */
	@SuppressWarnings("unused")
	private String inShelvedName;

    /**
     *是否屏蔽（1是；0否）
     */
    private Integer inSuppressed;
    
    /**
	 *是否屏蔽名称
	 */
	@SuppressWarnings("unused")
	private String inSuppressedName;

    /**
     *操作人
     */
    private String operator;

    /**
     * 响应时间
     */
    private Date responseTime;
    /**
     * 确认时间
     */
    private Date ackTime;
    /**
     * 恢复时间
     */
    private Date recoveryTime;

    /**
     *事件类型
     */
    //private EventType eventType;

    /**
     *报警点
     */
    //private AlarmPoint alarmPoint;

    /**
     *报警标识
     */
    //private AlarmFlag alarmFlag;
    
    /**
     *报警点附加信息
     */
    //private TagExtraMessage tagExtraMessage;
    
    /**
	 * 装置名称简称
	 */
	private String unitSname;
	
    /**
     * 生产单元简称
     */
    private String prdtCellSname;
    
    /**
     * 班组
     */
    private String team;
    
    /**
     * 位号
     */
    private String tag;
    
    /**
     * 位置
     */
    private String location;
    
    /**
	 * 报警标识名称
	 */
	private String alarmFlagName;
	
	/**
     * 实际响应时长
     */
    private Long actualResponseDuration;
	
	/**
     * 规定响应时长
     */
    private Long prescribedResponseDuration;
    
    /**
     * 响应及时率
     */
    private String responseTimeRate;

	/**
	 * 描述
	 */
	private String des;

	public Long getEventId() {
		return eventId;
	}

	public void setEventId(Long eventId) {
		this.eventId = eventId;
	}

	public Long getEventTypeId() {
		return eventTypeId;
	}

	public void setEventTypeId(Long eventTypeId) {
		this.eventTypeId = eventTypeId;
	}

	public Long getAlarmPointId() {
		return alarmPointId;
	}

	public void setAlarmPointId(Long alarmPointId) {
		this.alarmPointId = alarmPointId;
	}

	public Long getAlarmFlagId() {
		return alarmFlagId;
	}

	public void setAlarmFlagId(Long alarmFlagId) {
		this.alarmFlagId = alarmFlagId;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getAlarmTime() {
		return alarmTime;
	}

	public void setAlarmTime(Date alarmTime) {
		this.alarmTime = alarmTime;
	}

	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public String getPriorityName() {
		return CommonEnum.AlarmPriorityEnum.getName(priority);
	}

	public void setPriorityName(String priorityName) {
		this.priorityName = priorityName;
	}

	public String getPreviousValue() {
		return previousValue;
	}

	public void setPreviousValue(String previousValue) {
		this.previousValue = previousValue;
	}

	public String getNowValue() {
		return nowValue;
	}

	public void setNowValue(String nowValue) {
		this.nowValue = nowValue;
	}

	public Double getLimitValue() {
		return limitValue;
	}

	public void setLimitValue(Double limitValue) {
		this.limitValue = limitValue;
	}

	public Integer getInShelved() {
		return inShelved;
	}

	public void setInShelved(Integer inShelved) {
		this.inShelved = inShelved;
	}

	public String getInShelvedName() {
		return CommonEnum.InShelvedEnum.getName(inShelved);
	}

	public void setInShelvedName(String inShelvedName) {
		this.inShelvedName = inShelvedName;
	}

	public Integer getInSuppressed() {
		return inSuppressed;
	}

	public void setInSuppressed(Integer inSuppressed) {
		this.inSuppressed = inSuppressed;
	}

	public String getInSuppressedName() {
		return CommonEnum.InSuppressedEnum.getName(inSuppressed);
	}

	public void setInSuppressedName(String inSuppressedName) {
		this.inSuppressedName = inSuppressedName;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public Date getResponseTime() {
		return responseTime;
	}

	public void setResponseTime(Date responseTime) {
		this.responseTime = responseTime;
	}

	public Date getAckTime() {
		return ackTime;
	}

	public void setAckTime(Date ackTime) {
		this.ackTime = ackTime;
	}

	public Date getRecoveryTime() {
		return recoveryTime;
	}

	public void setRecoveryTime(Date recoveryTime) {
		this.recoveryTime = recoveryTime;
	}

	/*public EventType getEventType() {
		return eventType;
	}

	public void setEventType(EventType eventType) {
		this.eventType = eventType;
	}

	public AlarmPoint getAlarmPoint() {
		return alarmPoint;
	}

	public void setAlarmPoint(AlarmPoint alarmPoint) {
		this.alarmPoint = alarmPoint;
	}

	public AlarmFlag getAlarmFlag() {
		return alarmFlag;
	}

	public void setAlarmFlag(AlarmFlag alarmFlag) {
		this.alarmFlag = alarmFlag;
	}

	public TagExtraMessage getTagExtraMessage() {
		return tagExtraMessage;
	}

	public void setTagExtraMessage(TagExtraMessage tagExtraMessage) {
		this.tagExtraMessage = tagExtraMessage;
	}*/

	public String getUnitSname() {
		return unitSname;
	}

	public void setUnitSname(String unitSname) {
		this.unitSname = unitSname;
	}

	public String getPrdtCellSname() {
		return prdtCellSname;
	}

	public void setPrdtCellSname(String prdtCellSname) {
		this.prdtCellSname = prdtCellSname;
	}

	public String getTeam() {
		return team;
	}

	public void setTeam(String team) {
		this.team = team;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getAlarmFlagName() {
		return alarmFlagName;
	}

	public void setAlarmFlagName(String alarmFlagName) {
		this.alarmFlagName = alarmFlagName;
	}

	public Long getActualResponseDuration() {
		return actualResponseDuration;
	}

	public void setActualResponseDuration(Long actualResponseDuration) {
		this.actualResponseDuration = actualResponseDuration;
	}

	public Long getPrescribedResponseDuration() {
		return prescribedResponseDuration;
	}

	public void setPrescribedResponseDuration(Long prescribedResponseDuration) {
		this.prescribedResponseDuration = prescribedResponseDuration;
	}

	public String getResponseTimeRate() {
		return responseTimeRate;
	}

	public void setResponseTimeRate(String responseTimeRate) {
		this.responseTimeRate = responseTimeRate;
	}


	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}
}
