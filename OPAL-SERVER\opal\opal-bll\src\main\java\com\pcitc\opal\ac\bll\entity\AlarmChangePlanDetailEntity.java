package com.pcitc.opal.ac.bll.entity;

import java.util.Date;

/*
 * 报警变更方案明细实体
 * 模块编号：pcitc_opal_bll_class_AlarmChangePlanDetail
 * 作    者：xuelei.wang
 * 创建时间：2018-01-19
 * 修改编号：1
 * 描    述：报警变更方案明细实体
 */
public class AlarmChangePlanDetailEntity{

    /**
     * 报警变更方案明细ID
     */
    private Long planDetailId;

    /**
     * 报警变更方案ID
     */
    private Long planId;

    /**
     * 报警点ID
     */
    private Long alarmPointId;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 位号
     */
    private String tag;
    /**
     * 生产单元名称
     */
    private String prdtName;
    /**
     * 报警标识名称
     */
    private String alarmFlagName;
    /**
     * 计量单位名称
     */
    private String measUnitName;
    /**
     * 描述
     */
    private String des;

    /**
     * 审批状态（0未提交；1已提交；2通过；3驳回）
     */
    private Integer aproStatus;

    /**
     * 审批意见
     */
    private String aproOpnion;

    /**
     * 审批时间
     */
    private Date aproTime;

    /**
     * 审批人ID
     */
    private String aproUserId;

    /**
     * 审批人名称
     */
    private String aproUserName;

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getPrdtName() {
        return prdtName;
    }

    public void setPrdtName(String prdtName) {
        this.prdtName = prdtName;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

    public String getMeasUnitName() {
        return measUnitName;
    }

    public void setMeasUnitName(String measUnitName) {
        this.measUnitName = measUnitName;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public Long getPlanDetailId() {
        return planDetailId;
    }

    public void setPlanDetailId(Long planDetailId) {
        this.planDetailId = planDetailId;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getAproStatus() {
        return aproStatus;
    }

    public void setAproStatus(Integer aproStatus) {
        this.aproStatus = aproStatus;
    }

    public String getAproOpnion() {
        return aproOpnion;
    }

    public void setAproOpnion(String aproOpnion) {
        this.aproOpnion = aproOpnion;
    }

    public Date getAproTime() {
        return aproTime;
    }

    public void setAproTime(Date aproTime) {
        this.aproTime = aproTime;
    }

    public String getAproUserId() {
        return aproUserId;
    }

    public void setAproUserId(String aproUserId) {
        this.aproUserId = aproUserId;
    }

    public String getAproUserName() {
        return aproUserName;
    }

    public void setAproUserName(String aproUserName) {
        this.aproUserName = aproUserName;
    }
}

