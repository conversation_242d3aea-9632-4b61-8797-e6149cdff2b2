package com.pcitc.opal.af.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

import java.util.Date;

public class AlarmRecTimeEntity extends BasicEntity {

    /**
     * 持续时间
     */
    private Long durTime;
    /**
     * 报警记录ID
     */
    private Long alarmRecId;

    /**
     *装置编码
     */
    private String unitCode;

    /**
     * 装置名称
     */
    private String unitName;

    /**
     *生产单元ID
     */
    private Long prdtCellId;
    private String prdtCellName;

    /**
     *DCS编码(缓存表)
     */
    private String dcsCode;

    /**
     * 事件类型ID
     */
    private Long eventTypeId;

    /**
     * 事件类型名称
     */
    private String eventTypeName;

    /**
     *报警点ID
     */
    private Long alarmPointId;

    /**
     *位号
     */
    private String tag;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     * 报警标识名称
     */
    private String alarmFlagName;

    /**
     *报警标识(缓存表)
     */
    private String alarmFlagType;

    /**
     *报警时间
     */
    private Date alarmTime;

    /**
     *恢复时间
     */
    private Date recoveryTime;

    /**
     *响应时间
     */
    private Date responseTime;

    /**
     *优先级(1紧急；2重要；3一般)
     */
    private Integer priority;

    /**
     * 优先级名称
     */
    @SuppressWarnings("unused")
    private String priorityName;

    /**
     *优先级(缓存表)
     */
    private String priorityCache;

    /**
     *先前值
     */
    private String previousValue;

    /**
     *值
     */
    private String nowValue;

    /**
     *限值
     */
    private Double limitValue;

    /**
     *是否搁置(1是；0否)
     */
    private Integer inShelved;

    /**
     *是否屏蔽（1是；0否）
     */
    private Integer inSuppressed;

    /**
     *操作人
     */
    private String operator;

    /**
     *描述
     */
    private String des;

    /**
     *参数
     */
    private String parameter;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 报警是否恢复
     */
    private Integer isAck;

    /**
     * 时长
     */
    private Double continuousHour;

    /**
     * 班组
     */
    private String team;

    /**
     * 实际响应时长
     */
    private Long actualResponseDuration;

    /**
     * 规定响应时长
     */
    private Long prescribedResponseDuration;

    /**
     * 响应及时率
     */
    private String responseTimeRate;
    private Integer companyId;

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Long getAlarmRecId() {
        return alarmRecId;
    }

    public void setAlarmRecId(Long alarmRecId) {
        this.alarmRecId = alarmRecId;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Long getPrdtCellId() {
        return prdtCellId;
    }

    public void setPrdtCellId(Long prdtCellId) {
        this.prdtCellId = prdtCellId;
    }

    public String getPrdtCellName() {
        return prdtCellName;
    }

    public void setPrdtCellName(String prdtCellName) {
        this.prdtCellName = prdtCellName;
    }

    public String getDcsCode() {
        return dcsCode;
    }

    public void setDcsCode(String dcsCode) {
        this.dcsCode = dcsCode;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public String getEventTypeName() {
        return eventTypeName;
    }

    public void setEventTypeName(String eventTypeName) {
        this.eventTypeName = eventTypeName;
    }

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

    public String getAlarmFlagType() {
        return alarmFlagType;
    }

    public void setAlarmFlagType(String alarmFlagType) {
        this.alarmFlagType = alarmFlagType;
    }

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public Date getRecoveryTime() {
        return recoveryTime;
    }

    public void setRecoveryTime(Date recoveryTime) {
        this.recoveryTime = recoveryTime;
    }

    public Date getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Date responseTime) {
        this.responseTime = responseTime;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getPriorityName() {
        return priorityName;
    }

    public void setPriorityName(String priorityName) {
        this.priorityName = priorityName;
    }

    public String getPriorityCache() {
        return priorityCache;
    }

    public void setPriorityCache(String priorityCache) {
        this.priorityCache = priorityCache;
    }

    public String getPreviousValue() {
        return previousValue;
    }

    public void setPreviousValue(String previousValue) {
        this.previousValue = previousValue;
    }

    public String getNowValue() {
        return nowValue;
    }

    public void setNowValue(String nowValue) {
        this.nowValue = nowValue;
    }

    public Double getLimitValue() {
        return limitValue;
    }

    public void setLimitValue(Double limitValue) {
        this.limitValue = limitValue;
    }

    public Integer getInShelved() {
        return inShelved;
    }

    public void setInShelved(Integer inShelved) {
        this.inShelved = inShelved;
    }

    public Integer getInSuppressed() {
        return inSuppressed;
    }

    public void setInSuppressed(Integer inSuppressed) {
        this.inSuppressed = inSuppressed;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getParameter() {
        return parameter;
    }

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsAck() {
        return isAck;
    }

    public void setIsAck(Integer isAck) {
        this.isAck = isAck;
    }

    public Double getContinuousHour() {
        return continuousHour;
    }

    public void setContinuousHour(Double continuousHour) {
        this.continuousHour = continuousHour;
    }

    public String getTeam() {
        return team;
    }

    public void setTeam(String team) {
        this.team = team;
    }

    public Long getActualResponseDuration() {
        return actualResponseDuration;
    }

    public void setActualResponseDuration(Long actualResponseDuration) {
        this.actualResponseDuration = actualResponseDuration;
    }

    public Long getPrescribedResponseDuration() {
        return prescribedResponseDuration;
    }

    public void setPrescribedResponseDuration(Long prescribedResponseDuration) {
        this.prescribedResponseDuration = prescribedResponseDuration;
    }

    public String getResponseTimeRate() {
        return responseTimeRate;
    }

    public void setResponseTimeRate(String responseTimeRate) {
        this.responseTimeRate = responseTimeRate;
    }

    public Long getDurTime() {
        return durTime;
    }

    public void setDurTime(Long durTime) {
        this.durTime = durTime;
    }
}

