package com.pcitc.opal.aa.bll.entity;
/*
 * 报警统计实体
 * 模块编号：pcitc_opal_bll_class_AlarmStatistic
 * 作  　者：xuelei.wang
 * 创建时间：2017-10-21
 * 修改编号：1
 * 描    述：报警统计实体
 */

import java.io.Serializable;

@SuppressWarnings("serial")
public class AlarmStatisticEntity implements Serializable{
    /**
     * 装置编码
     */
    private String unitId;
    /**
     * 装置名称
     */
    private String name;
    /**
     * 报警总数
     */
    private Double alarmTotalCount;
    /**
     * 滋扰报警
     */
    private Double disturbance;
    /**
     * 报警确认
     */
    private Double configure;
    /**
     * 报警搁置
     */
    private Double hold;
    /**
     * 报警恢复
     */
    private Double recovery;

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getAlarmTotalCount() {
        return alarmTotalCount;
    }

    public void setAlarmTotalCount(Double alarmTotalCount) {
        this.alarmTotalCount = alarmTotalCount;
    }

    public Double getDisturbance() {
        return disturbance;
    }

    public void setDisturbance(Double disturbance) {
        this.disturbance = disturbance;
    }

    public Double getConfigure() {
        return configure;
    }

    public void setConfigure(Double configure) {
        this.configure = configure;
    }

    public Double getHold() {
        return hold;
    }

    public void setHold(Double hold) {
        this.hold = hold;
    }

    public Double getRecovery() {
        return recovery;
    }

    public void setRecovery(Double recovery) {
        this.recovery = recovery;
    }
}