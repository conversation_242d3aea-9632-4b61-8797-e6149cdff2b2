package com.pcitc.opal.aa.bll.entity;

import com.pcitc.opal.common.bll.entity.DictionaryEntity;

import java.util.List;

/*
 * 评估首页实体
 * 模块编号：pcitc_opal_bll_class_AlarmAssessFirstPageEntity
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/17
 * 修改编号：1
 * 描    述：评估首页实体
 */
public class AlarmAssessFirstPageEntity {
    /**
     * 评估等级数据
     */
    private AssessLevelEntity assessLevelEntity;
    /**
     * 网格列数据
     */
    private List<GridViewEntity> gridViewEntityList;
    /**
     * 变化趋势图数据
     */
    private List<List<VariationTrendEntity>> variationTrendEntityList;

    /**
     * 变化趋势图中x轴的时间
     */
    private List<String> variationTrendDate;

    /**
     * 变化趋势图中展示的装置名称
     */
    private List<String> variationTrendUnit;

    /**
     * 统计值数据
     */
    private StatisticDataEntity statisticDataEntity;

    /**
     * 时间集合
     */
    private List<DictionaryEntity> dateTimeList;

    /**
     * 评估等级装置编码集合
     */
    private List<DictionaryEntity> unitIdList;

    public AssessLevelEntity getAssessLevelEntity() {
        return assessLevelEntity;
    }

    public void setAssessLevelEntity(AssessLevelEntity assessLevelEntity) {
        this.assessLevelEntity = assessLevelEntity;
    }

    public List<GridViewEntity> getGridViewEntityList() {
        return gridViewEntityList;
    }

    public void setGridViewEntityList(List<GridViewEntity> gridViewEntityList) {
        this.gridViewEntityList = gridViewEntityList;
    }

    public List<List<VariationTrendEntity>> getVariationTrendEntityList() {
        return variationTrendEntityList;
    }

    public void setVariationTrendEntityList(List<List<VariationTrendEntity>> variationTrendEntityList) {
        this.variationTrendEntityList = variationTrendEntityList;
    }

    public List<String> getVariationTrendDate() {
        return variationTrendDate;
    }

    public void setVariationTrendDate(List<String> variationTrendDate) {
        this.variationTrendDate = variationTrendDate;
    }

    public List<String> getVariationTrendUnit() {
        return variationTrendUnit;
    }

    public void setVariationTrendUnit(List<String> variationTrendUnit) {
        this.variationTrendUnit = variationTrendUnit;
    }

    public StatisticDataEntity getStatisticDataEntity() {
        return statisticDataEntity;
    }

    public void setStatisticDataEntity(StatisticDataEntity statisticDataEntity) {
        this.statisticDataEntity = statisticDataEntity;
    }

    public List<DictionaryEntity> getDateTimeList() {
        return dateTimeList;
    }

    public void setDateTimeList(List<DictionaryEntity> dateTimeList) {
        this.dateTimeList = dateTimeList;
    }

    public List<DictionaryEntity> getUnitIdList() {
        return unitIdList;
    }

    public void setUnitIdList(List<DictionaryEntity> unitIdList) {
        this.unitIdList = unitIdList;
    }
}

