<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.ad.dao.AlarmRecDAO">

    <select id="getUnitRecNum" resultType="com.pcitc.opal.ad.vo.UnitRecNumVO">
        select u.name                 as unitName,
               u.std_code             as unitCode,
               count(ar.alarm_rec_id) as number
        from t_pm_unit u
                 left join t_ad_alarmrec ar on u.std_code = ar.unit_code
            and ar.alarm_time &gt;= #{startTime}
            and ar.alarm_time &lt; #{endTime}
        group by u.std_code
        order by number desc, std_code
    </select>
    <select id="getPriorityRecNum" resultType="com.pcitc.opal.ad.vo.PriorityRecNumVO">
        select priority            as priority,
               count(alarm_rec_id) as number
        from t_ad_alarmrec
        where recovery_time is null and alarm_time &gt;= #{startTime}
        group by priority
    </select>
    <select id="getUnitRecCurrent" resultType="com.pcitc.opal.ad.vo.UnitRecCurrentVO">
        select max(alarm_time) as alarmTime,
               min(priority)   as priority,
               ar.unit_code    as unitCode,
               u.name          as unitName
        from t_ad_alarmrec ar
                 left join t_pm_unit u on ar.unit_code = u.std_code
        where ar.recovery_time is null and ar.alarm_time &gt;= #{startTime}
        group by ar.unit_code
        having max(alarm_time)
           and min(priority)
    </select>
    <select id="getPointRecNum" resultType="com.pcitc.opal.ad.vo.PointRecNumVO">
        select ap.alarm_point_id      as alarmPointId,
               ap.location            as location,
               ap.des                 as des,
               count(ar.alarm_rec_id) as number
        from t_pm_alarmpoint ap
                 left join t_ad_alarmrec ar on ap.alarm_point_id = ar.alarm_point_id
            and ar.alarm_time &gt;= #{startTime}
            and ar.alarm_time &lt; #{endTime}
        group by ap.alarm_point_id
        order by number desc, alarmPointId
    </select>
    <select id="getMonitorRecNum" resultType="com.pcitc.opal.ad.vo.MonitorRecNumVO">
        select monitor_type           as monitorType,
               count(ar.alarm_rec_id) as number
        from t_ad_alarmrec ar
                 inner join t_pm_alarmpoint ap on ap.alarm_point_id = ar.alarm_point_id
        where ar.alarm_time &gt;= #{startTime}
        group by monitor_type
    </select>

    <select id="selectAlarmAnlyByEvent" resultType="com.pcitc.opal.ad.vo.AlarmAnlyInfoVO">
        select
        ar.alarm_rec_id as alarmRecId,
        aar.reason_des as reasonDes
        from t_ad_alarmrec ar
        inner join t_ad_alarmanlyrec aar on ar.alarm_rec_id = aar.ALARM_REC_ID
        where ar.event_id in
        <foreach collection="eventIds" item="eventId" index="index" open="(" close=")" separator=",">
            #{eventId}
        </foreach>
    </select>

    <select id="selectAnalyseRecExport" resultType="com.pcitc.opal.ad.vo.AlarmAnlyRecExportVO">
        select aar.anly_status      as anlyStatus,
               ar.alarm_time        as alarmTime,
               workshop.sname       as workshopName,
               tpu.sname            as unitName,
               prdt.sname           as prdtCellName,
               ap.des               as tagDes,
               ap.tag               as tag,
               tpm.name             as measUnitName,
               ar.priority          as priority,
               af.name              as alarmFlagName,
               ap.alarm_point_hh    as alarmPointHH,
               ap.alarm_point_hi    as alarmPointHI,
               ap.alarm_point_lo    as alarmPointLO,
               ap.alarm_point_ll    as alarmPointLL,
               ap.monitor_type      as monitorType,
               aar.reason_type      as reasonType,
               areason.NAME         as alarmReasonName,
               aar.REASON_DES       as reasonDes,
               aar.CRT_TIME         as crtTime,
               aar.CRT_USER_NAME    as crtUserName,
               aar.SUBMIT_TIME      as submitTime,
               aar.SUBMIT_USER_NAME as submitUserName
        from t_ad_alarmrec ar
                 left join t_ad_alarmanlyrec aar on ar.alarm_rec_id = aar.alarm_rec_id
                 left join t_pm_alarmreason areason on aar.alarm_reason_id = areason.alarm_reason_id
                 left join t_ad_alarmflag af on ar.alarm_flag_id = af.alarm_flag_id
                 left join t_pm_unit tpu on ar.unit_code = tpu.std_code
                 left join t_pm_workshop workshop on tpu.workshop_id = workshop.workshop_id
                 left join t_pm_prdtcell prdt on ar.prdtcell_id = prdt.prdtcell_id
                 left join t_pm_alarmpoint ap
                           on ar.alarm_point_id = ap.alarm_point_id and ap.prdtcell_id = ar.prdtcell_id
                 left join t_pm_measunit tpm on ap.measunit_id = tpm.measunit_id
        where tpu.in_use = 1
          and prdt.in_use = 1 and ap.in_use = 1
        <if test="vo.anlyStatus != null" >
            <if test="vo.anlyStatus == 0" >
                and aar.alarm_anly_rec_id is null
            </if>
            <if test="vo.anlyStatus != 0">
                and aar.anly_status = #{vo.anlyStatus}
            </if>
        </if>
        <if test="vo.unitIds != null and vo.unitIds.size() > 0">
            and ar.unit_code in
            <foreach collection="vo.unitIds" item="unitCod" index="index" open="(" close=")" separator=",">
                #{unitCod}
            </foreach>
        </if>
        <if test="vo.prdtCellIds != null and vo.prdtCellIds.size() > 0">
            and ar.prdtcell_id in
            <foreach collection="vo.prdtCellIds" item="prdtCellId" index="index" open="(" close=")" separator=",">
                #{prdtCellId}
            </foreach>
        </if>
        <if test="vo.alarmFlagId != null and vo.alarmFlagId != -1 ">
            and ar.alarm_flag_id = #{vo.alarmFlagId}
        </if>
        <if test="vo.tag != null">
            <bind name="tagLike" value="'%' + vo.tag + '%'"/>
            and ar.tag like #{tagLike}
        </if>
        <if test="vo.prioritys != null and vo.prioritys.size() > 0">
            and ar.priority in
            <foreach collection="vo.prioritys" item="priority" index="index" open="(" close=")" separator=",">
                #{priority}
            </foreach>
        </if>
        <if test="vo.monitorType != null and vo.monitorType.size() > 0">
            and ap.monitor_type in
            <foreach collection="vo.monitorType" item="m" index="index" open="(" close=")" separator=",">
                #{m}
            </foreach>
        </if>
        <if test="vo.alarmStatus != null">
            <if test="vo.alarmStatus == 0">
                and ar.recoveryTime is null
            </if>
            <if test="vo.alarmStatus == 1">
                and ar.recoveryTime is not null
            </if>
        </if>
        <if test="vo.startTime != null">
            and ar.alarm_time &gt;= #{vo.startTime}
        </if>
        <if test="vo.endTime != null">
            and ar.alarm_time &lt;= #{vo.endTime}
        </if>
        order by ar.alarm_time desc
    </select>

    <select id="selectUnitResponseNumber" resultType="com.pcitc.opal.ad.vo.UnitResponseNumberVO">
        SELECT
        ar.unit_code as unitCode,
        un.name as unitName,
        COUNT(ar.response_time) as responseNumber,
        COUNT(ar.alarm_rec_id) as alarmNumber,
        count(if((ar.response_time-ar.alarm_time) &lt;= 30*1000, 1, null)) as timelyResponseNumber
        FROM t_ad_alarmrec ar
        INNER JOIN t_pm_unit un ON ar.unit_code = un.std_code
        <where>
            <if test="startTime != null">
                and ar.alarm_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and ar.alarm_time &lt;= #{endTime}
            </if>
        </where>
        group by ar.unit_code , un.name
        order by unitName
    </select>

    <select id="selectWorkshopResponseNumber" resultType="com.pcitc.opal.ad.vo.WorkshopResponseNumberVO">
        SELECT
        w.workshop_id as workshopId,
        w.name as workshopName,
        COUNT(ar.response_time) as responseNumber,
        COUNT(ar.alarm_rec_id) as alarmNumber,
        count(if((ar.response_time-ar.alarm_time) &lt;= 30*1000, 1,null)) as timelyResponseNumber
        FROM t_ad_alarmrec ar
        INNER JOIN t_pm_unit un ON ar.unit_code = un.std_code
        inner join t_pm_workshop w on un.workshop_id = w.workshop_id
        <where>
            <if test="startTime != null">
                and ar.alarm_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and ar.alarm_time &lt;= #{endTime}
            </if>
        </where>
        group by w.workshop_id , w.name
        order by workshopName
    </select>


    <select id="selectInfoList" resultType="com.pcitc.opal.ad.vo.AlarmRecInfoVO">
        select
        ar.alarm_point_id as alarmPointId,
        ar.prdtcell_id as prdtCellId,
        ar.tag as tag,
        ap.location as location,
        ar.unit_code as unitCode,
        unit.sname as unitName,
        prdtcell.sname as prdtcellName,
        ar.priority as priority,
        ar.alarm_flag_id as alarmFlagId,
        af.name as alarmflagName,
        ar.recovery_time as recoveryTime,
        ar.response_time as responseTime,
        ar.alarm_time as alarmTime,
        ar.des as des,
        measunit.name as measunitName,
        measunit.sign as measunitSign,
        ap.monitor_type as monitorType
        from t_ad_alarmrec ar
        inner join t_pm_alarmpoint ap on ar.alarm_point_id = ap.alarm_point_id and ap.in_use = 1
        inner join t_ad_alarmflag af on ar.alarm_flag_id = af.alarm_flag_id
        inner join t_pm_unit unit on ar.unit_code = unit.std_code
        inner join t_pm_prdtcell prdtcell on ar.prdtcell_id = prdtcell.prdtcell_id
        left join t_pm_measunit measunit on ap.measunit_id = measunit.measunit_id
        <where>
            <if test="vo.unitCodes != null and vo.unitCodes.size() > 0">
                and ar.unit_code in
                <foreach collection="vo.unitCodes" item="unitCode" index="index" open="(" close=")" separator=",">
                    #{unitCode}
                </foreach>
            </if>
            <if test="vo.monitorTypeList != null and vo.monitorTypeList.size() > 0">
                and ap.monitor_type in
                <foreach collection="vo.monitorTypeList" item="monitorType" index="index" open="(" close=")" separator=",">
                    #{monitorType}
                </foreach>
            </if>
            <if test="vo.prioritys != null and vo.prioritys.size() > 0">
                and ar.priority in
                <foreach collection="vo.prioritys" item="priority" index="index" open="(" close=")" separator=",">
                    #{priority}
                </foreach>
            </if>
            <if test="vo.startTime != null">
                and ar.alarm_time &gt;= #{vo.startTime}
            </if>
            <if test="vo.endTime != null">
                and ar.alarm_time &lt;= #{vo.endTime}
            </if>
            <if test="vo.monitorType != null ">
                and ap.monitor_type = #{vo.monitorType}
            </if>
        </where>
    </select>


    <select id="getAlarmRecInfo" resultType="com.pcitc.opal.ad.vo.AlarmRecInfoRespModel">
        select alarmrec0_.recovery_time as recoveryTime,
        alarmrec0_.response_time as responseTime,
        alarmrec0_.unit_code as unitCode,
        alarmrec0_.alarm_time as alarmTime
        from t_ad_alarmrec alarmrec0_
        inner join t_pm_alarmpoint alarmpoint1_ on alarmrec0_.alarm_point_id = alarmpoint1_.alarm_point_id
        where alarmrec0_.alarm_time between #{reqModel.startTime} AND #{reqModel.endTime}
        and alarmrec0_.unit_code in
        <foreach collection="reqModel.unitCodeList" item="unitCode" index="index" open="(" close=")" separator=",">
            #{unitCode}
        </foreach>
        and alarmrec0_.priority is not null
        and alarmrec0_.alarm_flag_id is not null
        and alarmpoint1_.in_use = 1
    </select>
</mapper>
