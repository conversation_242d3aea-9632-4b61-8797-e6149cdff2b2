var addUrl = OPAL.API.pmUrl + '/alarmPointGroupConfig/group'; //新增
var editUrl = OPAL.API.pmUrl + '/alarmPointGroupConfig'; //编辑
var getSingleUrl = OPAL.API.pmUrl + '/alarmPointGroupConfig/getSingleAlarmPointGroup?alarmPointGroupId=';  //获取详情
var alarmFlagListUrl = OPAL.API.adUrl + '/alarmEvents/getAlarmFlagList'; //报警标识下拉框
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";  //装置树
var delSonUrl = OPAL.API.pmUrl + '/alarmPointGroupConfig/delete-dtl'; //子表删除
var alarmGroupCurrentUrl = OPAL.API.pmUrl + '/alarmPointGroupConfig/getCurrentGroupList';  //二级列表
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.None;
var alarmPointGroupId = '';
var alarmPointId = '';
var isRefresh = false;
var detailData = {
    alarmFlagId: ''
};
var allValue = [];
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
            //初始化装置树
            page.logic.initUnitTree();
            //初始化报警标识
            page.logic.initAlarmFlagList();
            //初始化子表格
            page.logic.initOpetateTable([]);
        },
        bindUI: function () {
            $('#saveAddModal').click(function () {
                page.logic.save();
            });
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
            // 新增
            $('#alarmChangeItemAdd').click(function() {
                page.logic.add();
            });
        },
        logic: {
            
            /**
             * 跳转报警点选择页面
             */
            add: function () {
                layer.open({
                    type: 2,
                    title: "报警点选择",
                    closeBtn: 1,
                    area: ['800px', '500px'],
                    shadeClose: false,
                    content: 'AlarmPointListSel.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "unitId": $("#unitCode").val(),
                            "alarmPointGroupId": alarmPointGroupId
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (isRefresh) {
                            page.logic.getTableCurrentData();
                        }
                    }
                })
            },
            /**
             * 保存
             */
            save: function () {
                $('#unitCode').next('.textbox').find('input').attr('name', 'unitCode');
                $('#unitCode').next('.textbox').addClass('form-control-tree');
                $('#alarmFlagId').next('.textbox').find('input').attr('name', 'alarmFlagId');
                $('#alarmFlagId').next('.textbox').addClass('form-control-tree');
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data = page.logic.getETCollectionData('AddOrEditModal'); 
                //处理提交类型
                var ajaxType = "POST";
                let url = '';
                if (pageMode == PageModelEnum.NewAdd) {
                    window.pageLoadMode = PageLoadMode.Reload;
                    url = addUrl;
                }
                else if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                    window.pageLoadMode = PageLoadMode.Refresh;
                    url = editUrl;
                }
                $.ajax({
                    url: url,
                    async: false,
                    type: ajaxType,
                    data: JSON.stringify(data),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            pageMode = PageModelEnum.Edit;
                            window.parent.pageLoadMode = PageLoadMode.Refresh;
                            alarmPointGroupId = result;
                            $('#alarmPointGroupId').val(alarmPointGroupId);
                            $('#alarmChangeItemAdd').removeAttr('disabled');
                            layer.msg('保存成功！')
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            
            getETCollectionData: function (formId) {
                var formData = OPAL.form.getData(formId);
                formData.alarmFlagId = formData.alarmFlagId.toString();
                var arr = new Array();
                arr.push(formData);
                return $.ET.toCollectionJson(arr);
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                alarmPointGroupId = data.alarmPointGroupId;
                if (pageMode == 1) {
                    $('#alarmChangeItemAdd').attr('disabled','disabled');
                } else {
                    $('#alarmChangeItemAdd').removeAttr('disabled');
                    $.ajax({
                        url: getSingleUrl + data.alarmPointGroupId,
                        type: "get",
                        async: false,
                        dataType: "json",
                        success: function (data) {
                            detailData = $.ET.toObjectArr(data)[0];
                            // OPAL.form.setData('AddOrEditModal', $.ET.toObjectArr(data)[0]);
                            //加载子表格
                            page.logic.getTableCurrentData();
                            var alarmFlagIdsArr;
                            if (detailData.alarmFlagId != '' || detailData.alarmFlagId != null || detailData.alarmFlagId != undefined) {
                                alarmFlagIdsArr = detailData.alarmFlagId.split(',');
                            }
                            //回显‘全选’
                            if (alarmFlagIdsArr.length == allValue.length && allValue.toString() === detailData.alarmFlagId) {
                                alarmFlagIdsArr.push(-1);
                            }
                            $('#unitCode').combotree('setValue', detailData.unitCode);
                            $('#alarmFlagId').combotree('setValues', alarmFlagIdsArr);
                            $('#groupName').val(detailData.groupName);
                            $('#des').val(detailData.des);
                            $('#alarmPointGroupId').val(detailData.alarmPointGroupId);
                        }
                    });
                }
                if (pageMode == PageModelEnum.View) {
                    $('input').attr('disabled','disabled');
                    $('select').attr('disabled','disabled');
                    $("#saveAddModal").hide();
                    $("#alarmChangeItemAdd").hide();
                }
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
             onActionRenderer: function () {
                var rowData = arguments[1];
                if (pageMode == PageModelEnum.View) {
                    return [
                        '<a name="TableDelete" style="color: #999;">删除</a> '
                    ]
                } else {
                    return [
                        '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.dtlId + '\')" >删除</a> '
                    ]
                }
            },
            /**
             * 单条主删除
             */
            delSingle: function (id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delSonUrl,
                        async: false, //
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    page.logic.getTableCurrentData();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            // 获取子表格数据
            getTableCurrentData: function() {
                $.ajax({
                    url: alarmGroupCurrentUrl + '?alarmPointGroupId=' + alarmPointGroupId,
                    // data: param,
                    dataType: "JSON",
                    type: 'GET',
                    success: function(result) {
                        dataArr = $.ET.toObjectArr(result);
                        //初始化子表格
                        page.logic.initOpetateTable(dataArr);
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            //初始化下方表格
            initOpetateTable: function (dataArr) {
                var options = {
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    // pageNumber: 1, //初始化加载第一页，默认第一页
                    // height: 350,
                    columns: [ {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '90px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'cellName',
                        title: '生产单元',
                        align: 'left',
                    }, {
                        field: 'tag',
                        title: '位号',
                        align: 'left',
                    }]
                };
                $('#detailGroupTable').bootstrapTable(options);
                $('#detailGroupTable').bootstrapTable('refreshOptions', options);
                $("#detailGroupTable").bootstrapTable("load", dataArr);
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal',{
                    rules: {
                        unitCode: {
                            required: true
                        },
                        groupName: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        alarmFlagId: {
                            required: true
                        }
                    }
                })
                
            },
            /**
             * 初始化报警标识
             */
             initAlarmFlagList: function () {
                OPAL.ui.getComboMultipleSelect('alarmFlagId', alarmFlagListUrl, {
                    data: {
                        'isAll': true
                    },
                    async: false,
                }, true, function() {
                    var treeView = $("#alarmFlagId").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    allValue = [];
                    //获取所有节点value集合
                    let allData = treeView.tree('getChildren');
                    allData.forEach(item => {
                        if (item.id != -1) {
                            allValue.push(item.id);
                        }
                    })
                    // $("#alarmFlagId").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitCode', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    multiple: false,
                    onlyLeafCheck: true,
                    async: false,
                    data: {
                        'enablePrivilege':false
                    },
                }, false, function () {

                });
            },
        }

    }
    page.init();
    window.page = page;
})