package com.pcitc.opal.as.bll.imp;

import com.pcitc.opal.as.bll.AlarmStdManagmtService;
import com.pcitc.opal.as.bll.entity.AlarmStdManagmtEntity;
import com.pcitc.opal.as.dao.AlarmStdManagmtRepository;
import com.pcitc.opal.as.pojo.AlarmStdManagmt;
import com.pcitc.opal.common.CMISClient;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.Date;
import java.util.List;

/*
 * 报警制度管理业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmStdManagmtImpl
 * 作	者：kun.zhao
 * 创建时间：2018/02/28
 * 修改编号：1
 * 描    述：报警制度管理业务逻辑层实现类
 */
@Service
public class AlarmStdManagmtImpl implements AlarmStdManagmtService {
	
	@Autowired
	private AlarmStdManagmtRepository alarmStdManagmtRepository;
	
	/**
	 * 新增报警制度管理维护数据
	 *
	 * <AUTHOR> 2018-02-28
	 * @param alarmStdManagmtEntity 报警制度管理实体
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	@SuppressWarnings("unused")
	@Override
    public CommonResult addAlarmStdManagmt(AlarmStdManagmtEntity alarmStdManagmtEntity) throws Exception{
		// 实体转换为持久层实体
		AlarmStdManagmt alarmStdManagmt = ObjectConverter.entityConverter(alarmStdManagmtEntity, AlarmStdManagmt.class);
		// 数据校验
		alarmStdManagmtValidation(alarmStdManagmt);
		//上传文件
		CMISClient cmisClient =new CMISClient();
		String documentId = cmisClient.uploadFile("",alarmStdManagmtEntity.getUplAttaName(),alarmStdManagmtEntity.getUplAttaStream());
		if (StringUtils.isEmpty(documentId)){
			throw new Exception("上传文件失败！");
		}
		alarmStdManagmt.setUplAttaId(documentId);

		CommonResult commonResult = alarmStdManagmtRepository.addAlarmStdManagmt(alarmStdManagmt);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false){
			//如果失败， 删除服务器的文件
			cmisClient.deleteFile(documentId);
			throw new Exception(commonResult.getMessage());
		}
		return commonResult;
	}

	/**
	 * 删除报警制度管理维护数据
	 * 
	 * <AUTHOR> 2018-02-28
	 * @param alarmStdManagmtIds 报警制度管理实体id数组
	 * @return 处理结果信息
	 * @throws Exception
	 */
	@Override
	@Transactional
	public CommonResult deleteAlarmStdManagmt(Long[] alarmStdManagmtIds) throws Exception {
		List<AlarmStdManagmt> anlyAlarmStdManagmtList = alarmStdManagmtRepository.getAlarmStdManagmt(alarmStdManagmtIds);
        if (anlyAlarmStdManagmtList == null || anlyAlarmStdManagmtList.isEmpty())
            return new CommonResult();
        // 获取报警制度管理Id数组
        Long[] anlyAlarmStdManagmtIdList = anlyAlarmStdManagmtList.stream().map(item -> item.getAlarmStdManagmtId()).toArray(Long[]::new);
        // 获取与报警制度管理相关的附件Id数组
        String[] uplAttaIdList = anlyAlarmStdManagmtList.stream().map(item -> item.getUplAttaId()).toArray(String[]::new);
        // 调用DAL删除方法
        CommonResult commonResult = alarmStdManagmtRepository.deleteAlarmStdManagmt(anlyAlarmStdManagmtIdList);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        // 删除报警制度管理相关联的附件
        CMISClient cmisClient = new CMISClient();
        for (String uplAttaId : uplAttaIdList) {
    		boolean isDelete = cmisClient.deleteFile(uplAttaId);
    		if (isDelete == false)
    				throw new Exception("附件删除失败！");
		}
        return commonResult;
	}
	
	/**
	 * 报警报警制度管理维护数据
	 *
	 * <AUTHOR> 2018-02-28
	 * @param alarmStdManagmtEntity 报警制度管理维护实体
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	@Override
	public CommonResult updateAlarmStdManagmt(AlarmStdManagmtEntity alarmStdManagmtEntity) throws Exception{
		// 实体转换持久层实体
		AlarmStdManagmt alarmStdManagmt = ObjectConverter.entityConverter(alarmStdManagmtEntity, AlarmStdManagmt.class);
		// 校验
		alarmStdManagmtValidation(alarmStdManagmt);
		if (alarmStdManagmtEntity.getUplAttaStream()!=null && alarmStdManagmtEntity.getUplAttaStream().available()!=0){
			//上传文件
			CMISClient cmisClient =new CMISClient();
			String documentId = cmisClient.uploadFile("",alarmStdManagmtEntity.getUplAttaName(),alarmStdManagmtEntity.getUplAttaStream());
			if (StringUtils.isEmpty(documentId)){
				throw new Exception("上传文件失败！");
			}
			//删除文件
			cmisClient.deleteFile(alarmStdManagmt.getUplAttaId());
			alarmStdManagmt.setUplAttaId(documentId);
		}

		// 调用DAL更新方法
		CommonResult commonResult = alarmStdManagmtRepository.updateAlarmStdManagmt(alarmStdManagmt);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
		return commonResult;
	}

	/**
	 * 根据报警制度管理ID获取单条数据信息
	 * <AUTHOR> 2018-02-28
	 * @param alarmStdManagmtId 报警制度管理ID
	 * @return 报警制度管理实体
	 * @throws Exception
	 */
	@Override
	public AlarmStdManagmtEntity getSingleAlarmStdManagmt(Long alarmStdManagmtId) throws Exception {
		AlarmStdManagmt alarmStdManagmtPO = alarmStdManagmtRepository.getSingleAlarmStdManagmt(alarmStdManagmtId);
        return ObjectConverter.entityConverter(alarmStdManagmtPO, AlarmStdManagmtEntity.class);
	}
	
	/**
	 * 获取报警制度管理分页数据
	 * 
	 * <AUTHOR> 2018-02-28
	 * @param name      名称
	 * @param catgr     分类
	 * @param startTime 上传时间范围起始
	 * @param endTime   上传时间范围结束
	 * @param page      分页对象
	 * @return 报警制度管理分页对象
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<AlarmStdManagmtEntity> getAlarmStdManagmt(String name, Integer catgr, Date startTime,
			Date endTime, Pagination page) throws Exception {
		PaginationBean<AlarmStdManagmt> listMeasUnit = alarmStdManagmtRepository.getAlarmStdManagmt(name, catgr, startTime, endTime, page);
        PaginationBean<AlarmStdManagmtEntity> returnMeasUnit = new PaginationBean<AlarmStdManagmtEntity>(page, listMeasUnit.getTotal());
        returnMeasUnit .setPageList(ObjectConverter.listConverter(listMeasUnit.getPageList(), AlarmStdManagmtEntity.class));
        return returnMeasUnit;
	}


	/**
	 * 校验
	 *
	 * <AUTHOR> 2018-03-01
	 * @param entity 报警制度管理实体
	 * @throws Exception
	 */
	private void alarmStdManagmtValidation(AlarmStdManagmt entity) throws Exception {
		CommonResult commonResult = null;
		// 实体不能为空
		if (entity == null) {
			throw new Exception("没有报警制度管理数据！");
		}
		// 调用DAL与数据库相关的校验
		commonResult = alarmStdManagmtRepository.alarmStdManagmtValidation(entity);

		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
	}
}
