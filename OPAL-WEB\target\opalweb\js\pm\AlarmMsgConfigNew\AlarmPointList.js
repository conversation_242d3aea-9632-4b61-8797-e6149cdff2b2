$(function () {
    var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";   //装置
    // var unitPrdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';   //生产单元
    var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
    var initSendMsgUrl = OPAL.API.pmUrl + "/alarmMsgConfig/getInSendMsgList";     //是否发送报警短信
    // var searchUrl = OPAL.API.pmUrl + "/alarmMsgConfig/getAlarmMsgConfig";   //查询
    var searchUrl = OPAL.API.pmUrl + "/alarmMsgConfigNew/getAlarmPonitList";//查询
    // var isRefresh = false;
    window.pageLoadMode = PageLoadMode.None;
    var isQueryFlag;
    var pageIndex = 1;
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            //绑定事件
            this.bindUI();
            //初始化查询是否发送报警短信
            page.logic.initSendMsg();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化表格
            page.logic.initTable();
            //默认查询数据
            // page.logic.search();
        },
        bindUI: function () {
            //查询
            $('#btnSearch').click(function () {
                page.logic.search();
            });
            //关弹层
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            });
            //批量配置
            $('#batchAdd').click(function () {
                page.logic.batchAdd();
            });

        },
        data: {
            param: {}
        },
        logic: {
            /**
             * 初始化表格
             */
            initTable: function () {
                OPAL.ui.initBootstrapTable("table", {
                    pageSize:1000,
                    pageList: [1000],
                    cache: false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "装置",
                        field: 'unitSname',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }, {
                        title: "生产单元",
                        field: 'prdtCellSname',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }, {
                        title: "是否发送报警短信",
                        field: 'inSendMsg',
                        rowspan: 1,
                        width: '100px',
                        align: 'center',
                        formatter:function(value,row,index){
                            if(value==1){
                                return "是";
                            }else{
                                return "否";
                            }
                        }
                    }]
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: 1,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            // formatterNumber: function (value, row, index) {
            //     if (value != '')
            //         return value.indexOf('～') > -1 ? new Number(value.split("～")[0]) + "～" + new Number(value.split("～")[1]) : value.substring(0, 1) + new Number(value.substring(1));
            //     return '';
            // },
            // /**
            //  * 查询参数
            //  * @param params
            //  * @returns {{pageSize: *, pageNumber: *}}
            //  */
            // queryParams: function (p) { // 设置查询参数
            //     var param = {
            //         isQueryFlag:isQueryFlag,
            //         pageSize: p.pageSize,
            //         pageNumber: p.pageNumber,
            //         sortOrder: p.sortOrder
            //     };
            //     return $.extend(page.data.param, param);
            // },
            /**
             * 搜索
             */
            search: function () {
                // var u = $("#unitIds").val();
                // if(u==null || u==""){
                //     layer.msg('请选择装置！');
                //     return;
                // }
                page.data.param = OPAL.form.getData("searchForm");
                page.data.param.unitIds = $("#unitIds").val().split(",");
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl
                    // "pageNumber": pageIndex
                });
            },

            /**
             * 初始化是否发送报警短信
             */
            initSendMsg: function () {
                OPAL.ui.getCombobox("inSendMsg", initSendMsgUrl, {
                    selectValue: 1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            // /**
            //  * 初始化装置树
            //  */
            // initUnitTree: function () {
            //     OPAL.ui.getEasyUIComboTreeSelect("unitIds", commonUnitTreeUrl, "id", "parentId", "sname", {
            //         data: {
            //             'enablePrivilege': false
            //         },
            //         onChange: function (node) {
            //             var nodeIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds");
            //             if (nodeIds.length == 1) {
            //                 $("#prdtCellIds").combo('enable');
            //                 $("#prdtCellIds").combotree('setValues', []);
            //                 page.logic.searchUnitPrdt(nodeIds[0]);
            //                 $('.textbox,.combo').css('background-color', '');
            //             } else {
            //                 $("#prdtCellIds").combotree('setValues', []);
            //                 $("#prdtCellIds").combo('disable');
            //                 $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
            //             }
            //
            //         }
            //     }, false, function () {
            //
            //     });
            // },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect("unitIds", commonUnitTreeUrl, "id", "parentId", "sname", {
                    multiple: false,
                    onlyLeafCheck: true,
                    onChange: function(node) {
                        OPAL.ui.getComboMultipleSelect("prdtCellIds", prdtCellUrl, {
                            keyField: "prdtCellId",
                            valueField: "sname",
                            data: {
                                "unitId": node,
                                'isAll': true
                            },
                        }, false, function() {
                            var treeView = $("#prdtCellIds").combotree('tree');
                            var nd = treeView.tree('find', -1);
                            if (nd != null) {
                                treeView.tree('update', {
                                    target: nd.target,
                                    text: '全选'
                                });
                            }
                            $("#prdtCellIds").combotree("checkAllNodes");
                        });
                    }
                }, false, function() {});
                $("#unitIds").combotree("getValues");
            },
            // /**
            //  * 生产单元
            //  */
            // searchUnitPrdt: function (unitId) {
            //     OPAL.ui.getComboMultipleSelect('prdtCellIds', unitPrdtCellUrl, {
            //         keyField: "prdtCellId",
            //         valueField: "sname",
            //         data: {
            //             "unitId": unitId,
            //             'isAll': true
            //         }
            //     }, true, function () {
            //         var treeView = $("#prdtCellIds").combotree('tree');
            //         var nd = treeView.tree('find', -1);
            //         if (nd != null) {
            //             treeView.tree('update', {
            //                 target: nd.target,
            //                 text: '全选'
            //             });
            //         }
            //         $("#prdtCellIds").combotree("checkAllNodes");
            //     });
            // },
            /**
             * 批量新增
             */
            batchAdd: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections');
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmPointId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要配置的报警点！");
                    return;
                }
                var pageMode = PageModelEnum.Edit;
                var title = "报警短信新增";
                var alarmPointIds = idsArray.join(",");
                page.logic.detail(title, alarmPointIds, pageMode);
            },
            detail: function(title, alarmPointId, pageMode){
                layer.open({
                    type: 2,
                    title: false,
                    closeBtn: 0,
                    area: ['1000px', '560px'],
                    shadeClose: false,
                    // offset: '30px',
                    content: 'AlarmMsgConfigAdd.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "alarmPointIds": alarmPointId,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                        // page.logic.closeLayer(false);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            closethis:function(){
                window.pageLoadMode = PageLoadMode.Refresh;
                page.logic.closeLayer(true);
            }
        }
    }
    page.init();
    window.page = page;

})