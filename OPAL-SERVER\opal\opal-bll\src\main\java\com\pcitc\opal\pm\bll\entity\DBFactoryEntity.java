package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;


/*
 * 工厂实体
 * 模块编号：pcitc_opal_bll_class_FactoryEntity
 * 作       者：xuelei.wang
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描       述：工厂实体
 */
public class DBFactoryEntity extends BasicEntity {
	/**
	 * 工厂ID
	 */
	private Long factoryId;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 简称
	 */
	private String sname;

	/**
	 * 标准编码
	 */
	private String stdCode;
	/**
	 * 企业ID
	 */
	private Long companyId;
	/**
	 * 企业名称
	 */
	private String companyName;

	/**
	 * 排序
	 */
	private Integer sortNum;
	
	/**
	 * 描述
	 */
	private String des;

	public Long getFactoryId() {
		return factoryId;
	}

	public void setFactoryId(Long factoryId) {
		this.factoryId = factoryId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSname() {
		return sname;
	}

	public void setSname(String sname) {
		this.sname = sname;
	}

	public String getStdCode() {
		return stdCode;
	}

	public void setStdCode(String stdCode) {
		this.stdCode = stdCode;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public Integer getSortNum() {
		return sortNum;
	}

	public void setSortNum(Integer sortNum) {
		this.sortNum = sortNum;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}
}