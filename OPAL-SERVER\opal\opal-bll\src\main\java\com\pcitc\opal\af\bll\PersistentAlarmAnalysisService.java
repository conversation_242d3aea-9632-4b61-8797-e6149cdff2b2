package com.pcitc.opal.af.bll;

import java.util.Date;

import org.springframework.stereotype.Service;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

/*
 * 持续报警分析业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_PersistentAlarmAnalysis
 * 作       者：dageng.sun
 * 创建时间：2017/11/02
 * 修改编号：1
 * 描       述：持续报警分析业务逻辑层接口 
 */
@Service
public interface PersistentAlarmAnalysisService {

	/**
	 * 持续报警分析分页查询
	 * 
	 * <AUTHOR> 2017-11-02
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param alarmFlagId 报警标识
	 * @param isHandle 是否处理（0:未处理,1:已处理,-1:全部）
	 * @param beginTime 报警事件开始间
	 * @param endTime 报警事件结束时间
	 * @param page 翻页实现类
	 * @return
	 * @throws Exception 
	 * @return PaginationBean<AlarmEventEntity> 
	 */
	PaginationBean<AlarmEventEntity> getPersistentAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds, Integer alarmFlagId, Integer isHandle, Date beginTime, Date endTime, Pagination page)
			throws Exception;
	
	/**
	 * 搁置报警分析分页查询
	 * 
	 * <AUTHOR> 2017-11-02
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param alarmFlagId 报警标识
	 * @param beginTime 报警事件开始间
	 * @param endTime 报警事件结束时间
	 * @param workTeamIds 班组编号
	 * @param page 翻页实现类
	 * @return
	 * @throws Exception 
	 * @return PaginationBean<AlarmEventEntity> 
	 */
	PaginationBean<AlarmEventEntity> getShelveAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds, Long alarmFlagId,
															Date beginTime, Date endTime, Long[] workTeamIds, Pagination page)
            throws Exception;
	
	/**
	 * 屏蔽报警分析分页查询
	 * 
	 * <AUTHOR> 2017-11-02
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param alarmFlagId 报警标识
	 * @param eventTypeId 报警事件类型id
	 * @param beginTime 报警事件开始间
	 * @param endTime 报警事件结束时间
	 * @param workTeamIds 班组编号
	 * @param page 翻页实现类
	 * @return
	 * @throws Exception 
	 * @return PaginationBean<AlarmEventEntity> 
	 */
	PaginationBean<AlarmEventEntity> getShieldAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds, Long alarmFlagId,Long eventTypeId,
															Date beginTime, Date endTime, Long[] workTeamIds, Pagination page)
            throws Exception;
	
}
