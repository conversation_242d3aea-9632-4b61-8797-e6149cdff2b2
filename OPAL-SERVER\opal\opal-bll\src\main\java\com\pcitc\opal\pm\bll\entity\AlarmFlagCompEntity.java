package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;
import com.pcitc.opal.pm.pojo.DcsCode;

/*
 * 报警标识对照表实体
 * 模块编号：pcitc_opal_bll_class_AlarmFlagCompEntity
 * 作	者：jiangtao.xue
 * 创建时间：2018/3/30
 * 修改编号：1
 * 描	述：报警标识对照表实体
 */
public class AlarmFlagCompEntity extends BasicEntity {

    /**
     * 报警标识对照ID
     */
    private Long alarmFlagCompId;

    /**
     * DCS编码ID
     */
    private Long dcsCodeId;

    /**
     * DCS名称
     */
    private String dcsName;

    /**
     * 源报警标识
     */
    private String alarmFlagSource;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     * 本系统事件标识
     */
    private String alarmFlagName;

    /**
     * dcs编码表
     */
    private DcsCode dcsCode;


    public Long getDcsCodeId() {
        return dcsCodeId;
    }

    public void setDcsCodeId(Long dcsCodeId) {
        this.dcsCodeId = dcsCodeId;
    }

    public String getAlarmFlagSource() {
        return alarmFlagSource;
    }

    public void setAlarmFlagSource(String alarmFlagSource) {
        this.alarmFlagSource = alarmFlagSource;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public Long getAlarmFlagCompId() {
        return alarmFlagCompId;
    }

    public void setAlarmFlagCompId(Long alarmFlagCompId) {
        this.alarmFlagCompId = alarmFlagCompId;
    }

    public DcsCode getDcsCode() {
        return dcsCode;
    }

    public void setDcsCode(DcsCode dcsCode) {
        this.dcsCode = dcsCode;
    }

    public String getDcsName() {
        return dcsName;
    }

    public void setDcsName(String dcsName) {
        this.dcsName = dcsName;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

}
