<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pcitc.opal</groupId>
		<artifactId>opal</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<packaging>jar</packaging>
	<artifactId>opal-common</artifactId>
	<name>opal-common</name>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<!--<dependency>-->
			<!--<groupId>org.springframework.boot</groupId>-->
			<!--<artifactId>spring-boot-configuration-processor</artifactId>-->
			<!--<optional>true</optional>-->
		<!--</dependency>-->
		<dependency>
			<groupId>com.pcitc.opal</groupId>
			<artifactId>opal-pojo</artifactId>
		</dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.2</version>
        </dependency>
		<!-- cmis -->
		<dependency>
			<groupId>org.apache.chemistry.opencmis</groupId>
			<artifactId>chemistry-opencmis-client-api</artifactId>
			<version>0.13.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.chemistry.opencmis</groupId>
			<artifactId>chemistry-opencmis-client-bindings</artifactId>
			<version>0.13.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.chemistry.opencmis</groupId>
			<artifactId>chemistry-opencmis-client-impl</artifactId>
			<version>0.13.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.chemistry.opencmis</groupId>
			<artifactId>chemistry-opencmis-commons-api</artifactId>
			<version>0.13.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.chemistry.opencmis</groupId>
			<artifactId>chemistry-opencmis-commons-impl</artifactId>
			<version>0.13.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.chemistry.opencmis</groupId>
			<artifactId>chemistry-opencmis-test-tck</artifactId>
			<version>0.13.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.chemistry.opencmis</groupId>
			<artifactId>chemistry-opencmis-workbench</artifactId>
			<version>0.13.0</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.pcitc.opal</groupId>
			<artifactId>opal-webservice</artifactId>
		</dependency>
		<dependency>
			<groupId>net.sf.ehcache</groupId>
			<artifactId>ehcache</artifactId>
			<version>2.10.2</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.83</version>
		</dependency>
    </dependencies>
</project>
