package com.pcitc.opal.ap.dao.imp;

import com.pcitc.opal.ap.dao.AlarmPushRuleDetailRepositoryCustom;
import com.pcitc.opal.ap.dao.AlarmPushRuleUnitRelDetailRepositoryCustom;
import com.pcitc.opal.ap.pojo.AlarmPushRuleDetail;
import com.pcitc.opal.ap.pojo.AlarmPushRuleUnitRelDetail;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.pojo.Unit;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/*
 * 报警知识管理实体的Repository的JPA接口实现
 * 模块编号： pcitc_opal_dal_class_AlarmKnowlgManagmtRepositoryImpl
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理实体的Repository的JPA接口实现
 */
public class AlarmPushRuleUnitRelDetailRepositoryImpl extends BaseRepository<AlarmPushRuleUnitRelDetail, Long>
		implements AlarmPushRuleUnitRelDetailRepositoryCustom {


	@Override
	public PaginationBean<AlarmPushRuleUnitRelDetailEntityVO> getAlarmPushRuleUnitRelDetails(Long apRuleUnitRelId, Pagination page) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("select new com.pcitc.opal.ap.dao.imp.AlarmPushRuleUnitRelDetailEntityVO( " +
					"t.apRuleUnitRelDetailId,t.apRuleUnitRelId,t.unitCode,u.sname,w.sname,f.sname) " +
					"from AlarmPushRuleUnitRelDetail t " +
					"left join t.alarmPushRuleUnitRel a " +
					"left join Unit u on t.unitCode=u.stdCode " +
					"left join u.workshop w " +
					"left join w.factory f " +
					"where 1=1 and t.inUse=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();

			if (null != apRuleUnitRelId){
				hql.append(" and t.apRuleUnitRelId = :apRuleUnitRelId ");
				paramList.put("apRuleUnitRelId",apRuleUnitRelId);
			}
			hql.append(" order by t.sortNum asc");

			Long count =Long.valueOf(this.findCusCount(hql.toString(),paramList));
			BaseRepository<AlarmPushRuleUnitRelDetailEntityVO, Long> br =new BaseRepository();
			return br.findCusTomAll(this.getEntityManager(),page,count, hql.toString(), paramList,AlarmPushRuleUnitRelDetailEntityVO.class);

		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addAlarmPushRuleUnitRelDetail(AlarmPushRuleUnitRelDetail alarmPushRuleUnitRelDetail) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			this.getEntityManager().persist(alarmPushRuleUnitRelDetail);
			commonResult.setResult(alarmPushRuleUnitRelDetail);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		return commonResult;
	}

	@Override
	public List<AlarmPushRuleUnitRelDetail> getRelDetailByUnit(Long apRuleUnitRelId,String unitCode) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("from AlarmPushRuleUnitRelDetail t \n" +
					"where 1=1 and t.inUse=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();

			if (null != apRuleUnitRelId){
				hql.append(" and t.apRuleUnitRelId = :apRuleUnitRelId ");
				paramList.put("apRuleUnitRelId",apRuleUnitRelId);
			}if (null != unitCode){
				hql.append(" and t.unitCode = :unitCode ");
				paramList.put("unitCode",unitCode);
			}
			// 调用基类方法查询返回结果
			Query query = getEntityManager().createQuery(hql.toString());
			this.setParameterList(query, paramList);
			List<AlarmPushRuleUnitRelDetail> list = query.getResultList();
			return list;
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	public List<String> getUnitByApRule(Long apRuleUnitRelId) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("select t.unitCode from AlarmPushRuleUnitRelDetail t \n" +
					"where 1=1 and t.inUse=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();

			if (null != apRuleUnitRelId){
				hql.append(" and t.apRuleUnitRelId = :apRuleUnitRelId ");
				paramList.put("apRuleUnitRelId",apRuleUnitRelId);
			}

			// 调用基类方法查询返回结果
			Query query = getEntityManager().createQuery(hql.toString());
			this.setParameterList(query, paramList);
			List<String> list = query.getResultList();
			return list;
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteAlarmPushRuleUnitRelDetail(List<Long> ids) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = " from AlarmPushRuleUnitRelDetail t " +
					"where t.apRuleUnitRelDetailId in (:ids)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("ids", ids);
			TypedQuery<AlarmPushRuleUnitRelDetail> query = getEntityManager().createQuery(hql, AlarmPushRuleUnitRelDetail.class);
			this.setParameterList(query, paramList);
			List<AlarmPushRuleUnitRelDetail> alarmPointList = query.getResultList();
			alarmPointList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteAlarmPushRuleUnitRelDetailOne(Long ids) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = " from AlarmPushRuleUnitRelDetail t " +
					"where t.apRuleUnitRelDetailId = :ids ";
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("ids", ids);
			TypedQuery<AlarmPushRuleUnitRelDetail> query = getEntityManager().createQuery(hql, AlarmPushRuleUnitRelDetail.class);
			this.setParameterList(query, paramList);
			List<AlarmPushRuleUnitRelDetail> alarmPointList = query.getResultList();
			alarmPointList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	@Override
	public List<Unit> getAllUnitsUsed(Integer speciality,Long priority) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			StringBuilder hql = new StringBuilder("select u from AlarmPushRuleUnitRelDetail t " +
					"left join Unit u on t.unitCode=u.stdCode\n" +
					"left join  t.alarmPushRuleUnitRel r " +
					"where 1=1 and t.inUse =1 ");
			Map<String, Object> paramList = new HashMap<String, Object>();
			if (priority!=null){
				hql.append(" and r.priority = :priority ");
				paramList.put("priority",priority);
			}
			else{
				hql.append(" and r.priority is null ");
			}
			if (speciality!=null){
				hql.append(" and r.alarmSpeciality = :alarmSpeciality ");
				paramList.put("alarmSpeciality",speciality);
			}
			Query query = getEntityManager().createQuery(hql.toString());
			this.setParameterList(query, paramList);
			List<Unit> alarmPointList = query.getResultList();
			return alarmPointList;
		} catch (Exception ex) {
		throw ex;
	}
		// 返回消息结果对象
	}


	@Override
	public CommonResult updateAlarmPushRuleUnitRelDetail(AlarmPushRuleUnitRelDetail alarmPushRuleUnitRelDetail) {
		return null;
	}

	@Override
	public List<Unit> getAllCUnits(Long apRuleUnitRelId) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder(
					"select u from AlarmPushRuleUnitRelDetail t " +
					"left join Unit u on t.unitCode=u.stdCode " +
					"where 1=1 and t.inUse=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			if (null != apRuleUnitRelId){
				hql.append(" and t.apRuleUnitRelId = :apRuleUnitRelId ");
				paramList.put("apRuleUnitRelId",apRuleUnitRelId);
			}

			Query query = getEntityManager().createQuery(hql.toString());
			this.setParameterList(query, paramList);
			List<Unit> list = query.getResultList();
			return list;
		} catch (Exception ex) {
			throw ex;
		}
	}
}
