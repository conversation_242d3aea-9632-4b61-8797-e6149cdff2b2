package com.pcitc.opal.pm.bll.entity;

import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.InputStream;
import java.io.Serializable;
import java.util.Date;

/**
 * Author:隋
 * 2019/9/19 0019
 */
public class HelpFileEntity {


    /**
     * 帮助文档ID
     */
    private Long helpFileId;

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 上传时间
     */
    private Date upLoadTime;

    /**
     * 上传人ID
     */
    private String upLoadUserId;

    /**
     * 上传人名称
     */
    private String upLoadUserName;

    /**
     * 上传附件流
     */
    private InputStream uplAttaStream;



    public Long getHelpFileId() {
        return helpFileId;
    }

    public void setHelpFileId(Long helpFileId) {
        this.helpFileId = helpFileId;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Date getUpLoadTime() {
        return upLoadTime;
    }

    public void setUpLoadTime(Date upLoadTime) {
        this.upLoadTime = upLoadTime;
    }

    public String getUpLoadUserId() {
        return upLoadUserId;
    }

    public void setUpLoadUserId(String upLoadUserId) {
        this.upLoadUserId = upLoadUserId;
    }

    public String getUpLoadUserName() {
        return upLoadUserName;
    }

    public void setUpLoadUserName(String upLoadUserName) {
        this.upLoadUserName = upLoadUserName;
    }

    public InputStream getUplAttaStream() {
        return uplAttaStream;
    }

    public void setUplAttaStream(InputStream uplAttaStream) {
        this.uplAttaStream = uplAttaStream;
    }
}
