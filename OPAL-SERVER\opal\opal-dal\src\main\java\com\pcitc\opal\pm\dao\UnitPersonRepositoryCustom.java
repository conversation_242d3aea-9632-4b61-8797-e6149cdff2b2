package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.pm.pojo.UnitPerson;

import java.util.List;

/*
 * 装置人员信息实体的Repository的JPA自定义接口
 * 模块编号：pcitc_opal_dal_interface_UnitPersonRepositoryCustom
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/18
 * 修改编号：1
 * 描    述：装置人员信息实体的Repository的JPA自定义接口
 */
public interface UnitPersonRepositoryCustom {
	/**
	 * 校验数据
	 *
	 * <AUTHOR> 2018-12-19
	 * @param unitPerson 装置人员实体
	 * @return     返回结果信息类
	 */
	CommonResult unitValidation(UnitPerson unitPerson);

	/**
	 * 获取所有已经启用的装置人员信息集合
	 *
	 * <AUTHOR> 2017-10-18
	 * @return 已经启用的装置人员信息集合
	 */
	List<UnitPerson> getUnitPerson();

	/**
	 * 保存装置人员信息表
	 *
	 * @param unitPerson
	 * <AUTHOR> 2017-12-11
	 */
	CommonResult saveUnitPerson(UnitPerson unitPerson);

	/**
	 * 获取单个装置人员信息
	 *
	 * @param unitPersonId
	 * @return
	 * <AUTHOR> 2017-12-11
	 */
	UnitPerson getSingleUnitPerson(Long unitPersonId);

	/**
	 * 根据装置编码获取装置人员信息
	 *
	 * @param unitCode 装置编码
	 * @return
	 * <AUTHOR> 2017-12-11
	 */
	UnitPerson getUnitPersonByUnitId(String unitCode);
	/**
	 * 更新装置人员信息
	 *
	 * @param unitPerson 装置人员信息
	 * @return
	 * <AUTHOR> 2017-12-11
	 */
	CommonResult updateUnitPerson(UnitPerson unitPerson);
	/**
	 * 根据装置编码删除装置人员信息
	 *
	 * @param unitCodes 装置编码集合
	 * @return
	 * <AUTHOR> 2017-12-11
	 */
	CommonResult deleteByUnitId(String[] unitCodes);
}
