package com.pcitc.opal.pm.bll.imp;

import com.pcitc.imp.common.exception.BusiException;
import com.pcitc.opal.common.*;
import com.pcitc.opal.pm.bll.AlarmPriorityCompService;
import com.pcitc.opal.pm.bll.entity.AlarmPriorityCompEntity;
import com.pcitc.opal.pm.dao.AlarmPriorityCompRepository;
import com.pcitc.opal.pm.pojo.AlarmPriorityComp;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.List;

/*
 * 报警优先级对照业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmPriorityCompImpl
 * 作       者：zheng.yang
 * 创建时间：2018/03/30
 * 修改编号：1
 * 描       述：报警优先级业务逻辑层实现类
 */
@Service
@Component
public class AlarmPriorityCompImpl implements AlarmPriorityCompService {

    @Autowired
    private AlarmPriorityCompRepository repo;

    /**
     * 新增数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompEntity 报警标识对照实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    @Override
    public CommonResult addPriorityComp(AlarmPriorityCompEntity alarmPriorityCompEntity) throws Exception {
        AlarmPriorityComp alarmPriorityCompPO = ObjectConverter.entityConverter(alarmPriorityCompEntity, AlarmPriorityComp.class);
        alarmPointValidation(alarmPriorityCompPO);
        CommonUtil.returnValue(alarmPriorityCompPO, CommonEnum.PageModelEnum.NewAdd.getIndex());
        CommonResult commonResult = repo.addPriorityComp(alarmPriorityCompPO);
        if(commonResult.getIsSuccess() == false){
            throw new Exception(commonResult.getMessage());
        }
        return commonResult;
    }

    /**
     * 删除数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompIds 报警标识对照主键Id集合
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    @Override
    public CommonResult deleteAlarmPriorityComp(Long[] alarmPriorityCompIds) throws Exception {
        if(ArrayUtils.isEmpty(alarmPriorityCompIds)) {
            throw new Exception("没有需要删除的数据！");
        }
        List<AlarmPriorityComp> alarmPriorityCompList = repo.getAlarmPriorityCompList(alarmPriorityCompIds);
        if (alarmPriorityCompList == null || alarmPriorityCompList.isEmpty()) {
            return new CommonResult();
        }
        CommonResult commonResult = repo.deleteAlarmPriorityComp(alarmPriorityCompIds);
        if(commonResult.getIsSuccess() == false) {
            throw new Exception(commonResult.getMessage());
        }
        return commonResult;
    }

    /**
     * 更新数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompEntity 报警标识对照实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    @Override
    public CommonResult updateAlarmPriorityComp(AlarmPriorityCompEntity alarmPriorityCompEntity) throws Exception {
        AlarmPriorityComp alarmPriorityCompPO = ObjectConverter.entityConverter(alarmPriorityCompEntity, AlarmPriorityComp.class);
        alarmPointValidation(alarmPriorityCompPO);
        alarmPriorityCompPO = repo.getSingleAlarmPriorityComp(alarmPriorityCompEntity.getAlarmPriorityCompId());
        CommonUtil.objectExchange(alarmPriorityCompEntity, alarmPriorityCompPO);
        CommonUtil.returnValue(alarmPriorityCompPO, CommonEnum.PageModelEnum.Edit.getIndex());
        CommonResult commonResult = repo.updateAlarmPriorityComp(alarmPriorityCompPO);
        if(commonResult.getIsSuccess() == false){
            throw new Exception(commonResult.getMessage());
        }
        return commonResult;
    }

    /**
     * 获取单条数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompId 报警标识对照ID
     * @return AlarmPriorityCompEntity 报警标识对照实体类
     * @throws Exception
     */
    @Override
    public AlarmPriorityCompEntity getSingleAlarmPriorityComp(Long alarmPriorityCompId) throws Exception {
        AlarmPriorityComp alarmPriorityComp = repo.getSingleAlarmPriorityComp(alarmPriorityCompId);
        AlarmPriorityCompEntity alarmPriorityCompEntity = ObjectConverter.entityConverter(alarmPriorityComp, AlarmPriorityCompEntity.class);
        alarmPriorityCompEntity.setDcsName(alarmPriorityComp.getDcsCode().getName());
        return alarmPriorityCompEntity;
    }

    /**
     * 获取分页数据
     *
      * <AUTHOR> 2018-03-30
     * @param dcsCodeId DCS编码ID
     * @param prioritySource 源报警优先级
     * @param priority    报警优先级ID
     * @param inUse 是否启用
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmPriorityCompEntity> 翻页对象
     */
    @Override
    public PaginationBean<AlarmPriorityCompEntity> getAlarmPriorityComp(Long dcsCodeId, String prioritySource, Integer priority, Integer inUse, Pagination page) throws Exception {
        PaginationBean<AlarmPriorityComp> listAlarmPriorityCopm = repo.getAlarmPriorityComp(dcsCodeId, prioritySource, priority, inUse, page);
        PaginationBean<AlarmPriorityCompEntity> pagebean = new PaginationBean<AlarmPriorityCompEntity>(page, listAlarmPriorityCopm.getTotal());
        pagebean.setPageList(ObjectConverter.listConverter(listAlarmPriorityCopm.getPageList(),AlarmPriorityCompEntity.class));
        int i = 0;
        for(AlarmPriorityCompEntity entity: pagebean.getPageList()) {
            AlarmPriorityComp alarmPriorityComp = listAlarmPriorityCopm.getPageList().get(i);
            entity.setDcsName(alarmPriorityComp.getDcsCode().getName());
            entity.setPriorityName(CommonEnum.AlarmPriorityEnum.getName(alarmPriorityComp.getPriority()));
            i++;
        }
        return pagebean;
    }

    /**
     * “源报警优先级”唯一性校验
     *
     * <AUTHOR> 2018-03-30
     * @param alarmPriorityComp 源报警优先级
     * @return CommonResult 消息结果类
     */

    private void alarmPointValidation(AlarmPriorityComp alarmPriorityComp) throws Exception{
        CommonResult commonResult = new CommonResult();
        if (alarmPriorityComp == null) {
            throw new BusiException("00", "没有数据！");
        }
        commonResult = repo.alarmPriorityCompValidation(alarmPriorityComp);
        if(commonResult.getIsSuccess() == false) {
            throw new Exception(commonResult.getMessage());
        }
    }
}
