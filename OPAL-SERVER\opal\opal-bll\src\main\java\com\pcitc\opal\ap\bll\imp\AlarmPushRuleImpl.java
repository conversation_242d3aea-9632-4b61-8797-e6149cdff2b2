package com.pcitc.opal.ap.bll.imp;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.AlarmFlagRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.ad.pojo.AlarmFlag;
import com.pcitc.opal.ak.dao.AlarmKnowlgManagmtRepository;
import com.pcitc.opal.ak.pojo.AlarmKnowlgManagmt;
import com.pcitc.opal.ap.bll.AlarmPushRuleService;
import com.pcitc.opal.ap.bll.entity.AlarmPushRuleEntity;
import com.pcitc.opal.ap.dao.AlarmPushRuleDetailRepository;
import com.pcitc.opal.ap.dao.AlarmPushRuleRepository;
import com.pcitc.opal.ap.dao.GroupRepository;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleDetailEntityVO;
import com.pcitc.opal.ap.pojo.AlarmPushRule;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointGroupConfigEntity;
import com.pcitc.opal.pm.dao.AlarmPointRepository;
import com.pcitc.opal.pm.dao.imp.AlarmPointGroupConfig;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import com.pcitc.opal.pm.pojo.AlarmPointGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/*
 * 报警制度管理业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmStdManagmtImpl
 * 作	者：kun.zhao
 * 创建时间：2018/02/28
 * 修改编号：1
 * 描    述：报警制度管理业务逻辑层实现类
 */
@Service
public class AlarmPushRuleImpl implements AlarmPushRuleService {


    @Autowired
    private AlarmPushRuleDetailRepository alarmPushRuleDetailRepository;
    @Autowired
    private AlarmPushRuleRepository alarmPushRuleRepository;
    @Autowired
    private GroupRepository groupRepository;



    @Override
    public PaginationBean<AlarmPushRuleEntity> getAlarmPushRule(String name,Integer companyId, Integer pushType, Pagination page) throws Exception {

        PaginationBean<AlarmPushRule> alarmPointGroupConfigList = alarmPushRuleRepository.getAlarmPushRulePage(name,companyId, pushType, page);
        PaginationBean<AlarmPushRuleEntity> returnAlarmPoint = new PaginationBean<AlarmPushRuleEntity>(page,(alarmPointGroupConfigList.getTotal()));
        returnAlarmPoint.setPageList(ObjectConverter.listConverter(alarmPointGroupConfigList.getPageList(), AlarmPushRuleEntity.class));
        return returnAlarmPoint;
    }

    @Override
    public CommonResult addAlarmPushRule(AlarmPushRuleEntity alarmPushRuleEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmPushRule alarmPushRule = ObjectConverter.entityConverter(alarmPushRuleEntity, AlarmPushRule.class);
        CommonUtil.returnValue(alarmPushRule, CommonEnum.PageModelEnum.NewAdd.getIndex());
        return alarmPushRuleRepository.addAlarmPushRule(alarmPushRule);
    }

    @Override
    public CommonResult deleteAlarmPushRule(Long[] ids) throws Exception {
        List<Long> idl = Arrays.asList(ids);
        List<Long> apIdl=new ArrayList<>();
        idl.forEach(id->{
            List<AlarmPushRuleDetailEntityVO> vos= alarmPushRuleDetailRepository.getAlarmPushRuleDetails(id);
            vos.forEach(v->{
                apIdl.add(v.getApRuleDetailId());
            });
        });
        alarmPushRuleDetailRepository.deleteAlarmPushRuleDetail(apIdl);
        return alarmPushRuleRepository.deleteAlarmPushRule(ids);
    }

    @Override
    public CommonResult updateAlarmPushRule(AlarmPushRuleEntity alarmPushRuleEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmPushRule alarmPushRule = ObjectConverter.entityConverter(alarmPushRuleEntity, AlarmPushRule.class);
        CommonUtil.returnValue(alarmPushRule, CommonEnum.PageModelEnum.NewAdd.getIndex());
        return alarmPushRuleRepository.updateAlarmPushRule(alarmPushRule);
    }


}
