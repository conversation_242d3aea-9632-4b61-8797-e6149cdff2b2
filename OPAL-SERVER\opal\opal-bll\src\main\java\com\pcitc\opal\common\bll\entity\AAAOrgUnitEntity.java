package com.pcitc.opal.common.bll.entity;

import java.io.Serializable;

/*
 * AAA OrgUnit
 * 模块编号： pcitc_opal_bll_class_AAAOrgUnitEntity
 * 作       者：xuelei.wang
 * 创建时间：2018/3/9
 * 修改编号：1
 * 描       述：AAA OrgUnit
 */
public class AAAOrgUnitEntity implements Serializable{
    /**
     * Code
     */
    private String code;
    /**
     * 英文名称
     */
    private String EName;
    /**
     * memo
     */
    private String memo;
    /**
     * 名称
     */
    private String name;
    /**
     * 排序
     */
    private Long orderId;
    /**
     * ID
     */
    private Long orgUnitId;
    /**
     * 父节点ID
     */
    private Long parentId;
    /**
     * 简称
     */
    private String SName;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEName() {
        return EName;
    }

    public void setEName(String EName) {
        this.EName = EName;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrgUnitId() {
        return orgUnitId;
    }

    public void setOrgUnitId(Long orgUnitId) {
        this.orgUnitId = orgUnitId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getSName() {
        return SName;
    }

    public void setSName(String SName) {
        this.SName = SName;
    }
}
