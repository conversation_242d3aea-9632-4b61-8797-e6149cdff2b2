var pmUrl = OPAL.API.pmUrl;
var delUrl = pmUrl + '/alarmEventTypeComp';
var searchUrl = pmUrl + '/alarmEventTypeComp';
var dcsCodeUrl = OPAL.API.commUrl + "/getDcsCodeList";
var eventTypeUrl = OPAL.API.commUrl + '/getEventTypeList';
var isRefresh = false;
var inUseUrl = OPAL.API.commUrl + "/getInUse";
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            this.bindUI();
            page.logic.initInUse();
            page.logic.initDcsCode();
            page.logic.initEventType();
            page.logic.initTable();
            //默认查询数据
            page.logic.search();
        },
        /**
         * 绑定事件
         */
        bindUI: function () {
            $('#txtAdd').click(function () {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            })
            $('#txtDel').click(function () {
                page.logic.delAll();
            })
            $('#btnSearch').click(function () {
                page.logic.search();
            })
        },
        data: {
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化表格
             */
            initTable: function () {
                OPAL.ui.initBootstrapTable("table", {
                    cache: false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '110px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "DCS名称",
                        field: 'dcsName',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "源事件类型",
                        field: 'eventTypeSource',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "源事件名称",
                        field: 'eventNameSource',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "本系统事件类型",
                        field: 'eventTypeName',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "是否启用",
                        field: 'inUseShow',
                        rowspan: 1,
                        align: 'center',
                    }]
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.edit(\'' + rowData.alarmEventTypeCompId + '\')">编辑</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.alarmEventTypeCompId + '\')" >删除</a> '
                ]
            },
            /**
             * 批量删除
             */
            delAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmEventTypeCompId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function (id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 新增
             */
            add: function () {
                var pageMode = PageModelEnum.NewAdd;
                var title = "事件类型对照配置新增";
                page.logic.detail(title, "", pageMode);
            },
            /**
             * 编辑
             * @param alarmEventTypeCompId
             */
            edit: function (alarmEventTypeCompId) {
                var pageMode = PageModelEnum.Edit;
                var title = "事件类型对照配置编辑";
                page.logic.detail(title, alarmEventTypeCompId, pageMode);
            },
            /**
             * 新增或者编辑详细页面
             *
             * @param title
             * @param alarmEventTypeCompId
             * @param pageMode
             */
            detail: function (title, alarmEventTypeCompId, pageMode) {
                layer.open({
                    type: 2,
                    title: title,
                    closeBtn: 1,
                    area: ['600px', '430px'],
                    shadeClose: false,
                    content: 'AlarmEventTypeCompAddOrEdit.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "alarmEventTypeCompId": alarmEventTypeCompId,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (isRefresh == true) {
                            if (pageMode == PageModelEnum.Edit)
                                page.logic.search();
                            else if (pageMode == PageModelEnum.NewAdd)
                                $('#table').bootstrapTable('selectPage', 1);
                        }
                    }
                })
            },
            /**
             * 搜索
             */
            search: function () {
                page.data.param = OPAL.form.getData("searchForm", true);
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 初始化查询DcsCode
             */
            initDcsCode: function () {
                OPAL.ui.getCombobox("dcsCodeId", dcsCodeUrl, {
                    keyField: "dcsCodeId",
                    valueField: "name",
                    data: {
                        isAll: true
                    },
                    selectFirstRecord: true
                }, null);
            },
            /**
             * 初始化事件类型
             */
            initEventType: function () {
                OPAL.ui.getEasyUIComboTreeSelect('eventTypeIds', eventTypeUrl, 'eventTypeId', 'parentId', 'name', {
                    showParentNodeText: true
                }, false);
            },
            /**
             * 初始化查询inUse
             */
            initInUse: function () {
                OPAL.ui.getCombobox("inUse", inUseUrl, {
                    data: {
                        isAll: true
                    }
                }, null);
            }
        }

    }
    page.init();
    window.page = page;
})