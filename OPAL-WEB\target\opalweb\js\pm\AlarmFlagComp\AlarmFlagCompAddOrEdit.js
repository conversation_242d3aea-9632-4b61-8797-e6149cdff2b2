$(function() {
    var alarmFlagCompUrl = OPAL.API.pmUrl + "/alarmFlagComp";
    var dcsUrl = OPAL.API.commUrl + "/getDcsCodeList";
    var alarmFlagUrl = OPAL.API.commUrl + "/getAllAlarmFlagList";
    var pageMode = PageModelEnum.NewAdd;
    window.pageLoadMode = PageLoadMode.None;
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var page = {
        init: function() {
            this.bindUI();

        },
        bindUI: function() {
            $('#saveAddModal').click(function() {
                page.logic.save();
            });
            $('.closeBtn').click(function() {
                page.logic.closeLayer(false);
            })
            $('#closePage').click(function() {
                page.logic.closeLayer(false);
            })
        },
        logic: {
            /**
             * 保存
             */
            save: function() {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data = OPAL.form.getETCollectionData('AddOrEditModal');

                //处理提交类型
                var ajaxType = "POST";
                if (pageMode == PageModelEnum.NewAdd) {
                    window.pageLoadMode = PageLoadMode.Reload;
                } else if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                    window.pageLoadMode = PageLoadMode.Refresh;
                }
                $.ajax({
                    url: alarmFlagCompUrl,
                    async: false,
                    type: ajaxType,
                    data: JSON.stringify(data),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function(result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！",{
                                time: 1000
                            },function() {
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function(data) {
                //初始化dcs
                page.logic.initDcs();
                //初始化优先级
                page.logic.initAlarmFlag();
                pageMode = data.pageMode;
                $("#pageTitle").text(data.title);
                if (pageMode == PageModelEnum.NewAdd) {
                    $('input[name=inUse]').attr('disabled', 'disabled');
                    return;
                }
                $.ajax({
                    url: alarmFlagCompUrl + "/" + data.alarmFlagCompId + "?now=" + Math.random(),
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function(data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        OPAL.form.setData('AddOrEditModal', entity);
                    },
                    complete: function(XMLHttpRequest, textStatus) {

                    },
                    error: function(XMLHttpRequest, textStatus) {

                    }
                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function(isRefresh) {
                parent.isRefresh = isRefresh;
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            formValidate: function() {
                OPAL.form.formValidate('AddOrEditModal', {
                    rules: {
                        alarmFlagSource: {
                            required: true
                        }
                    }
                })

            },
            /**
             * 初始化dcs
             */
            initDcs: function() {
                OPAL.ui.getCombobox("dcsCodeId", dcsUrl, {
                    keyField: "dcsCodeId",
                    valueField: "name",
                    selectFirstRecord: true,
                    async: false
                }, null);
            },
            initAlarmFlag: function() {
                OPAL.ui.getCombobox("alarmFlagId", alarmFlagUrl, {
                    selectFirstRecord: true,
                    async: false
                }, null);
            }
        }

    }
    page.init();
    window.page = page;
});