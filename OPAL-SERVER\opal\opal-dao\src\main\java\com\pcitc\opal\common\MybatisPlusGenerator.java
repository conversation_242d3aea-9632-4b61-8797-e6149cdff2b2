package com.pcitc.opal.common;


import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.converts.DmTypeConvert;
import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.config.rules.IColumnType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

public class MybatisPlusGenerator {

    public static void main(String[] args) {

        // 表名数组
        String[] tableName = {"t_bd_craftparamcompare"};
        // 获取项目路径
        String projectPath = System.getProperty("user.dir");
        //数据源配置
        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setDbType(DbType.MYSQL);// 设置数据库类型为MySQL
        dataSourceConfig.setDriverName("com.mysql.cj.jdbc.Driver");// 设置驱动名称
        dataSourceConfig.setUrl("************************************************");// 设置数据库URL
        dataSourceConfig.setUsername("root");// 设置用户名
        dataSourceConfig.setPassword("Mes_opal123");// 设置密码
        dataSourceConfig.setTypeConvert(new EasyDmTypeConvert());// 设置类型转换

        // 全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setAuthor("liumengnan");// 设置作者
        globalConfig.setFileOverride(true);// 覆盖文件
        globalConfig.setDateType(DateType.ONLY_DATE);// 只使用日期类型
        globalConfig.setIdType(IdType.AUTO);// ID类型自动增长
        globalConfig.setSwagger2(false);// 不生成Swagger2注解
        globalConfig.setOpen(false);// 不打开输出目录
        globalConfig.setOutputDir(projectPath + "/OPAL-SERVER/opal/opal-dao/src/main/java");// 设置输出目录
        // 包配置
        PackageConfig packageConfig = new PackageConfig();
        packageConfig.setParent("com.pcitc.opal.ad");// 设置父包名
        packageConfig.setMapper("dao");// 设置mapper包名
        packageConfig.setXml("mapper");// 设置xml文件包名
        // 配置模板
        TemplateConfig templateConfig = new TemplateConfig();
        templateConfig.setController(null);// 不生成controller模板
        templateConfig.setService(null);// 不生成service模板
        templateConfig.setServiceImpl(null);// 不生成service实现模板
        //策略配置
        StrategyConfig strategyConfig = new StrategyConfig();
        strategyConfig.setTablePrefix("t_", "t_ad_", "t_bd_", "t_pm_");// 设置表前缀
        strategyConfig.setNaming(NamingStrategy.underline_to_camel);// 下划线转驼峰命名
        strategyConfig.setColumnNaming(NamingStrategy.underline_to_camel);// 列名也使用下划线转驼峰命名
        strategyConfig.setInclude(tableName);// 包含的表名
        strategyConfig.setEntityLombokModel(true);// 启用Lombok模型
        strategyConfig.setEntityTableFieldAnnotationEnable(true);// 启用实体表字段注解

        FreemarkerTemplateEngine templateEngine = new FreemarkerTemplateEngine();

        AutoGenerator autoGenerator = new AutoGenerator();
        autoGenerator.setDataSource(dataSourceConfig);// 设置数据源配置
        autoGenerator.setGlobalConfig(globalConfig);// 设置全局配置
        autoGenerator.setPackageInfo(packageConfig);// 设置包配置
        autoGenerator.setTemplate(templateConfig);// 设置模板配置
        autoGenerator.setStrategy(strategyConfig);// 设置策略配置
        autoGenerator.setTemplateEngine(templateEngine);// 设置模板引擎
        autoGenerator.execute();// 执行代码生成

    }


}

class EasyDmTypeConvert extends MySqlTypeConvert {
    @Override
    public IColumnType processTypeConvert(GlobalConfig config, String fieldType) {
        IColumnType iColumnType = super.processTypeConvert(config, fieldType);
        if (fieldType.toLowerCase().equals("biginteger")) {
            iColumnType = DbColumnType.LONG;
        }
        if (fieldType.equals("SMALLINT")) {
            iColumnType = DbColumnType.INTEGER;
        }
        return iColumnType;
    }
}
