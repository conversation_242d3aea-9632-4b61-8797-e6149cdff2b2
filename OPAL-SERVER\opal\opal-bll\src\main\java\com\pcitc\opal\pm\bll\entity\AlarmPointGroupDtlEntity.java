package com.pcitc.opal.pm.bll.entity;
import com.pcitc.opal.common.bll.entity.BasicEntity;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * <p>
 * 报警点分组明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-01
 */
public class AlarmPointGroupDtlEntity extends BasicEntity {

    /**
     * 报警点分组明细ID
     */
    private Long alarmPointGroupDtlId;

    /**
     * 报警点分组ID
     */
    private Long alarmPointGroupId;

    /**
     * 报警点ID
     */
    private Long alarmPointId;

    /**
     * 创建时间
     */
    private Date crtDate;

    /**
     * 创建人ID
     */
    private String crtUserId;

    /**
     * 创建人名称
     */
    private String crtUserName;

    public Long getAlarmPointGroupDtlId() {
        return alarmPointGroupDtlId;
    }

    public void setAlarmPointGroupDtlId(Long alarmPointGroupDtlId) {
        this.alarmPointGroupDtlId = alarmPointGroupDtlId;
    }

    public Long getAlarmPointGroupId() {
        return alarmPointGroupId;
    }

    public void setAlarmPointGroupId(Long alarmPointGroupId) {
        this.alarmPointGroupId = alarmPointGroupId;
    }

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    @Override
    public Date getCrtDate() {
        return crtDate;
    }

    @Override
    public void setCrtDate(Date crtDate) {
        this.crtDate = crtDate;
    }

    @Override
    public String getCrtUserId() {
        return crtUserId;
    }

    @Override
    public void setCrtUserId(String crtUserId) {
        this.crtUserId = crtUserId;
    }

    @Override
    public String getCrtUserName() {
        return crtUserName;
    }

    @Override
    public void setCrtUserName(String crtUserName) {
        this.crtUserName = crtUserName;
    }
}
