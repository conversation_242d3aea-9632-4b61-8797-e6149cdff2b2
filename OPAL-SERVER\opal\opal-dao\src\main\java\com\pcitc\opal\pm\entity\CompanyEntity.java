package com.pcitc.opal.pm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 企业
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_pm_company")
public class CompanyEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "company_id", type = IdType.AUTO)
    private Long companyId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 简称
     */
    @TableField("sname")
    private String sname;

    /**
     * 标准编码
     */
    @TableField("std_code")
    private String stdCode;

    @TableField("in_use")
    private Long inUse;

    /**
     * 创建时间
     */
    @TableField("crt_date")
    private Date crtDate;

    /**
     * 维护时间
     */
    @TableField("mnt_date")
    private Date mntDate;

    /**
     * 创建人ID
     */
    @TableField("crt_user_id")
    private String crtUserId;

    /**
     * 最后维护人ID
     */
    @TableField("mnt_user_id")
    private String mntUserId;

    /**
     * 创建人名称
     */
    @TableField("crt_user_name")
    private String crtUserName;

    /**
     * 最后维护人名称
     */
    @TableField("mnt_user_name")
    private String mntUserName;

    @TableField("sort_num")
    private Long sortNum;

    /**
     * 描述
     */
    @TableField("des")
    private String des;


}
