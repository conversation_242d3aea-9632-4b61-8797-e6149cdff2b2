package com.pcitc.opal.ad.vo;

import com.pcitc.opal.ad.entity.DaterangeEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AlarmEventTableParamVO implements Serializable {

    private List<Long> eventTypeIds;

    private List<Long> prdtCellIds;

    private List<Long> alarmFlagIds;

    private String tag;

    private List<Integer> priority;

    private Integer monitorType;

    private Date beginTime;

    private Date endTime;

    private  Integer craftRank;

    private List<String> unitIds;

    private Integer isWorkTeam;

    private Integer delstatus;

    private List<DaterangeEntity> dateRangeList;

}
