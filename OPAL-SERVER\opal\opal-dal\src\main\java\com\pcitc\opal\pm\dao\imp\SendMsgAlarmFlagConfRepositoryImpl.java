package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.SendMsgAlarmFlagConfRepositoryCustom;
import com.pcitc.opal.pm.pojo.SendMsgAlarmFlagConf;

import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SendMsgAlarmFlagConfRepositoryImpl extends BaseRepository<SendMsgAlarmFlagConf, Long> implements SendMsgAlarmFlagConfRepositoryCustom {
    @Override
    public List<SendMsgAlarmFlagConf> getSendMsgAlarmFlagConfByAlarmPointId(Long[] alarmPointId) {
        try {
            StringBuilder hql = new StringBuilder("from SendMsgAlarmFlagConf smafc where smafc.alarmPointId in(:alarmPointId)");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("alarmPointId", Arrays.asList(alarmPointId));
            TypedQuery<SendMsgAlarmFlagConf> query = getEntityManager().createQuery(hql.toString(), SendMsgAlarmFlagConf.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        }catch (Exception e){
            throw e;
        }
    }
}
