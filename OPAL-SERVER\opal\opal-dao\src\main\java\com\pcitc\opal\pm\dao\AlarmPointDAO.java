package com.pcitc.opal.pm.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pcitc.opal.ad.vo.AlarmEventTableVO;
import com.pcitc.opal.pm.dto.ModelDTO;
import com.pcitc.opal.pm.entity.AlarmPointEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pcitc.opal.pm.vo.AlarmPointTableParamVO;
import com.pcitc.opal.pm.vo.AlarmPointTableVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 报警点 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
public interface AlarmPointDAO extends BaseMapper<AlarmPointEntity> {

    List<ModelDTO> selectDdzhModel(@Param("dto") ModelDTO modelDTO);

    Page<AlarmPointTableVO> selectTable(@Param("vo") AlarmPointTableParamVO vo, @Param("pager") Page<AlarmPointTableVO> pager);

    List<AlarmPointTableVO> selectTable(@Param("vo") AlarmPointTableParamVO vo);

}
