package com.pcitc.opal.pm.dao;

import java.util.List;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.RelevantTagConfig;

/*
 * RelevantTagConfig实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_RelevantTagConfigCustom
 * 作       者：dageng.sun
 * 创建时间：2018/8/1
 * 修改编号：1
 * 描       述：RelevantTagConfig实体的Repository的JPA自定义接口 
 */
public interface RelevantTagConfigRepositoryCustom {
	
	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfig 相关性位号配置实体
	 * @return CommonResult 返回消息结果类
	 */
	CommonResult addRelevantTagConfig(RelevantTagConfig relevantTagConfig);
	
	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigIds 相关性位号配置主键id集合
	 * @return CommonResult 返回消息结果类
	 */
	CommonResult deleteRelevantTagConfig(Long[] relevantTagConfigIds);
	
	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigEntity 相关性位号配置实体
	 * @return CommonResult 返回消息结果类
	 */
	CommonResult updateRelevantTagConfig(RelevantTagConfig relevantTagConfigEntity);
	
	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigId 相关性位号配置主键id
	 * @return RelevantTagConfig 返回RelevantTagConfig实体
	 */
	RelevantTagConfig getSingleRelevantTagConfig(Long relevantTagConfigId);
	
	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigIds 相关性位号配置主键id集合
	 * @return List<RelevantTagConfig> 返回RelevantTagConfig实体集合
	 */
	List<RelevantTagConfig> getRelevantTagConfig(Long[] relevantTagConfigIds);
	
	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 主位号
	 * @param page 翻页对象
	 * @return PaginationBean<RelevantTagConfig> 返回RelevantTagConfig分页对象
	 */
	PaginationBean<RelevantTagConfig> getRelevantTagConfig(String[] unitCodes, Long[] prdtCellIds, String tag, Pagination page);
	
}
