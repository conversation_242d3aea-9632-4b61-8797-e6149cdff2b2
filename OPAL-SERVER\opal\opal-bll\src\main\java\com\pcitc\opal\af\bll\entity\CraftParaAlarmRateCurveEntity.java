package com.pcitc.opal.af.bll.entity;

import com.pcitc.opal.aa.bll.entity.StatisticDataEntity;
import com.pcitc.opal.aa.bll.entity.VariationTrendEntity;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;

import java.util.List;

public class CraftParaAlarmRateCurveEntity {
    /**
     * 变化趋势图数据
     */
    private List<List<VariationTrendEntity>> variationTrendEntityList;
    /**
     * 变化趋势图中x轴的时间
     */
    private List<String> variationTrendDate;

    /**
     * 变化趋势图中展示的装置名称
     */
    private List<String> variationTrendUnit;

    /**
     * 统计值数据
     */
    private StatisticDataEntity statisticDataEntity;

    /**
     * 时间集合
     */
    private List<DictionaryEntity> dateTimeList;

    public List<List<VariationTrendEntity>> getVariationTrendEntityList() {
        return variationTrendEntityList;
    }

    public void setVariationTrendEntityList(List<List<VariationTrendEntity>> variationTrendEntityList) {
        this.variationTrendEntityList = variationTrendEntityList;
    }

    public List<String> getVariationTrendDate() {
        return variationTrendDate;
    }

    public void setVariationTrendDate(List<String> variationTrendDate) {
        this.variationTrendDate = variationTrendDate;
    }

    public List<String> getVariationTrendUnit() {
        return variationTrendUnit;
    }

    public void setVariationTrendUnit(List<String> variationTrendUnit) {
        this.variationTrendUnit = variationTrendUnit;
    }

    public StatisticDataEntity getStatisticDataEntity() {
        return statisticDataEntity;
    }

    public void setStatisticDataEntity(StatisticDataEntity statisticDataEntity) {
        this.statisticDataEntity = statisticDataEntity;
    }

    public List<DictionaryEntity> getDateTimeList() {
        return dateTimeList;
    }

    public void setDateTimeList(List<DictionaryEntity> dateTimeList) {
        this.dateTimeList = dateTimeList;
    }
}
