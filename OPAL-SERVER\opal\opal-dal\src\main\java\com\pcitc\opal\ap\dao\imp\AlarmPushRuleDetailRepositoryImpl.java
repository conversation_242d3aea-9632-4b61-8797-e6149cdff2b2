package com.pcitc.opal.ap.dao.imp;

import com.pcitc.opal.ap.dao.AlarmPushRuleDetailRepositoryCustom;
import com.pcitc.opal.ap.pojo.AlarmPushRuleDetail;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.pojo.AlarmPointGroup;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/*
 * 报警知识管理实体的Repository的JPA接口实现
 * 模块编号： pcitc_opal_dal_class_AlarmKnowlgManagmtRepositoryImpl
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理实体的Repository的JPA接口实现
 */
public class AlarmPushRuleDetailRepositoryImpl extends BaseRepository<AlarmPushRuleDetail, Long>
		implements AlarmPushRuleDetailRepositoryCustom {


	@Override
	public List<AlarmPushRuleDetailEntityVO> getAlarmPushRuleDetails(Long alarmPushRuleId) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("select new com.pcitc.opal.ap.dao.imp.AlarmPushRuleDetailEntityVO( \n" +
					"t.apRuleDetailId,\n" +
					"t.companyId,\n" +
					"t.alarmPushRuleId,\n" +
					"t.groupId,\n" +
					"t.startPushPeriod,\n" +
					"t.cycleFlag,\n" +
					"t.cyclePeriod,\n" +
					"t.alarmEndPushFlag,\n" +
					"t.alarmFlagId,\n" +
					"t.inUse,\n" +
					"t.sortNum,\n" +
					"t.des,\n" +
					"r.name,f.name,g.name) from AlarmPushRuleDetail t \n" +
					"left join t.alarmPushRule r \n" +
					"left join t.alarmFlag f \n" +
					"left join t.group g \n" +
					"where 1=1 and t.inUse=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			//
			CommonProperty commonProperty = new CommonProperty();
				hql.append(" and t.companyId = :companyId ");
				paramList.put("companyId",commonProperty.getCompanyId());

			if (null != alarmPushRuleId){
				hql.append(" and t.alarmPushRuleId = :alarmPushRuleId ");
				paramList.put("alarmPushRuleId",alarmPushRuleId);
			}
			hql.append(" order by t.sortNum");
			// 调用基类方法查询返回结果
			Query query = getEntityManager().createQuery(hql.toString());
			this.setParameterList(query, paramList);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addAlarmPushRuleDetail(AlarmPushRuleDetail alarmPushRuleDetail) {
		// 初始化消息结果类
		//企业
		CommonProperty commonProperty = new CommonProperty();
		alarmPushRuleDetail.setCompanyId(commonProperty.getCompanyId());
		CommonResult commonResult = new CommonResult();
		try {
			this.getEntityManager().persist(alarmPushRuleDetail);
			commonResult.setResult(alarmPushRuleDetail);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		return commonResult;
	}

	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteAlarmPushRuleDetail(List<Long> ids) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = " from AlarmPushRuleDetail t " +
					"where t.companyId=:companyId " +
					"and t.apRuleDetailId in (:ids)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("ids", ids);
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			TypedQuery<AlarmPushRuleDetail> query = getEntityManager().createQuery(hql, AlarmPushRuleDetail.class);
			this.setParameterList(query, paramList);
			List<AlarmPushRuleDetail> alarmPointList = query.getResultList();
			alarmPointList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult updateAlarmPushRuleDetail(AlarmPushRuleDetail alarmPushRuleDetail) {
		return null;
	}
}
