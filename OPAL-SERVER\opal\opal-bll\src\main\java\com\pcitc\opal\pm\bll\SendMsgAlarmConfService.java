package com.pcitc.opal.pm.bll;

import com.pcitc.opal.pm.bll.entity.SendMsgEntity;
import com.pcitc.opal.pm.pojo.AlarmRecVo;
import org.springframework.stereotype.Service;

@Service
public interface SendMsgAlarmConfService {

    /**
     * 发送短信并且将基本信息回写到<报警短信记录>
     *
     * @param content    短信内容
     * @param alarmRecVo 基本信息
     */
    String saveMobileMsg(String content, AlarmRecVo alarmRecVo, Integer companyId);


    /**
     * 发送短信并且将基本信息回写到<报警短信记录>
     *
     * @param content    短信内容
     * @param sendMsgEntity 基本信息
     */
    String saveMobileMsg(String content, SendMsgEntity sendMsgEntity, Integer companyId);

}
