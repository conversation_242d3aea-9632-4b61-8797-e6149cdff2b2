// var searchUrl = OPAL.API.afUrl + '/craftParaAlarmRate/getCraftParaAlarmRate';
var newsearchUrl = OPAL.API.portalUrl + '/alarmStat';
var initChartsUrl = OPAL.API.afUrl + '/craftParaAlarmRate/getCraftParaAlarmRateCurve';
var getQueryTimeUrl = OPAL.API.commUrl + '/getQueryTime';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit"; //装置树
var getShowTimeUrl = OPAL.API.commUrl + '/getShowTime';
var numberTopUrl = OPAL.API.aaUrl + '/alarmNumberAssess/getAlarmNumberAssessTop20';
var alarmNumStattUrl = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumStattStatisticData';
var alarmDurationStattUrl = OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotal';
var exportUrl = OPAL.API.portalUrl + '/export';
var dateTimeList = '';
var alarmTime = '';
var isLoading = true;
var mostFrequentNumberChart;
var craftParaAlarmReteCount = 0;
var dayAlarmCount = 0;
var peakAlarmRateCount = 0;
var hourAlarmCount = 0;
var totalAlarmNum = 0;
var nowDate = new Date();
var lastWeekDate = new Date();
lastWeekDate.setDate(nowDate.getDate() - 7);
var beforeLastWeekDate = new Date(lastWeekDate);
beforeLastWeekDate.setDate(lastWeekDate.getDate() - 7);
var craftParaAlarmRateChart;
var priorityChart;
var alarmDurationChart;
var craftStaticChart;
// 图4x,y
var xData = [];
var serveData = [];
var chartData41 = [];
var chartData42 = [];
var chartData43 = [];
var chartData44 = [];
// 图5x,y
var xData1 = [];
var serveData1 = [];
var chartData51 = [];
var chartData52 = [];
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            this.bindUi();
            // 初始化 报警时间的时间点
            // page.logic.getQueryTime();
            //初始化日期
            //page.logic.getShowTime();

            //获取装置
            //page.logic.getAllUnit();

            //获取数据
            page.data.param.startTime = lastWeekDate;
            page.data.param.endTime = nowDate;

            getStartTime = OPAL.util.dateFormat(new Date(page.data.param.startTime), "yyyy-MM-dd");
            getEndTime = OPAL.util.dateFormat(new Date(page.data.param.endTime), "yyyy-MM-dd");

            //设置时间插件
            page.logic.initTime();

            //  page.logic.queryChartsData(page.data.param)

            page.logic.loadData();
            // var data = [
            //     { sname: 'S Zorb1', totalAlarmQuantity: 89, count: 89 },
            //     { sname: '连续重整', totalAlarmQuantity: 69, count: 69 },
            //     { sname: '1#常减压', totalAlarmQuantity: 100, count: 100 },
            //     { sname: '1#柴加', totalAlarmQuantity: 28, count: 28 },
            //     { sname: '2#硫磺', totalAlarmQuantity: 50, count: 50 },
            //     { sname: '1#焦化', totalAlarmQuantity: 132, count: 132 },
            // ]
            // page.logic.drawBottom()
            $('#goindexExport').click(function () {
                page.logic.exportExcel();
            });


        },
        bindUi: function () {
            //查询
            $('#btnSearch').click(function () {
                if (OPAL.util.checkDateIsValid() == true) {
                    isLoading = false;
                    page.logic.search();
                }
            })
            $('.map4but').click(function () {
                $(this).addClass('btn-primary').siblings('.map4but').removeClass('btn-primary')
                xData = []
                serveData = []
                xData1 = []
                serveData1 = []
                if ($(this).attr('tabdata') == 1) {
                    $.each(chartData41, function (i, item) {
                        xData.push(item.unitCode)
                        serveData.push({value: item.craftParaAlarmRate, name: item.unitCode})
                    })
                    page.logic.initCraftParaAlarmRateStaticChart(serveData);
                }
                if ($(this).attr('tabdata') == 2) {
                    $.each(chartData42, function (i, item) {
                        xData.push(item.unitCode)
                        serveData.push({value: item.avgAlarmRate, name: item.unitCode})

                    })
                    page.logic.initCraftParaAlarmRateStaticChart(serveData);
                }
                if ($(this).attr('tabdata') == 3) {
                    $.each(chartData43, function (i, item) {
                        xData.push(item.unitCode)
                        serveData.push({value: item.alarmTimelyResponseRate, name: item.unitCode})

                    })
                    page.logic.initCraftParaAlarmRateStaticChart(serveData);
                }
                if ($(this).attr('tabdata') == 4) {
                    $.each(chartData44, function (i, item) {
                        xData.push(item.unitCode)
                        serveData.push({value: item.alarmTimelyDisposalRate, name: item.unitCode})

                    })
                    page.logic.initCraftParaAlarmRateStaticChart(serveData);
                }
                if ($(this).attr('tabdata') == 5) {
                    $.each(chartData51, function (i, item) {
                        xData1.push(item.name)
                        serveData1.push({value: item.value, name: item.name})
                    })
                    page.logic.RinitCraftParaAlarmRateStaticChart(serveData1);
                }
                if ($(this).attr('tabdata') == 6) {
                    $.each(chartData52, function (i, item) {
                        xData1.push(item.name)
                        serveData1.push({value: item.value, name: item.name})
                    })
                    page.logic.RinitCraftParaAlarmRateStaticChart(serveData1);
                }
            })
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                mostFrequentNumberChart.resize();
                craftParaAlarmRateChart.resize();
                priorityChart.resize();
                alarmDurationChart.resize();
                craftStaticChart.resize();
            };
        },
        data: {
            param: {}
        },
        logic: {
            //查询
            search: function () {
                page.logic.loadData(page.logic.drawBottom);
            },
            /**
             * 获得固定的时间点
             */
            getQueryTime: function () {
                $.ajax({
                    url: getQueryTimeUrl,
                    async: false,
                    data: '',
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        queryTimeValue = res[0].value;
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            getShowTime: function () {
                $.ajax({
                    url: getShowTimeUrl,
                    // async: true,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        getStartTime = dataArr[0].value.split(' ')[0];
                        getEndTime = dataArr[1].value.split(' ')[0];
                        //设置时间插件
                        page.logic.initTime();
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 设置日期插件
             */
            initTime: function () {
                var myDate = new Date();
                var start = laydate.render({
                    elem: '#startTime', //指定元素
                    type: 'date',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd', //日期格式
                    value: getStartTime,
                    max: getEndTime, //最大日期
                });
                var end = laydate.render({
                    elem: '#endTime',
                    type: 'date',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd',
                    value: getEndTime,
                    max: getEndTime,
                });
                $('#startTime').attr('maxDate', getEndTime)
                $('#endTime').attr('maxDate', getEndTime)
            },
            drawBottomCard: function (alarmData, title, unit, isPadding) {
                var paddingHtml = isPadding ? "padding-top: 6px;" : "";
                var itemHtml = "";

                for (var index = 0; index < 3; index++) {
                    var unitNameL = alarmData[index] ? alarmData[index].unitName : "";
                    var unitNameR = alarmData[alarmData.length - index - 1] ? alarmData[alarmData.length - index - 1].unitName : "";
                    var countL = alarmData[index] ? "<span class=\"good-color\">" + alarmData[index].count + "</span>" + unit + "" : "";
                    var countR =
                        alarmData[alarmData.length - index - 1] ? "<span class=\"warning-color\">" + alarmData[alarmData.length - index - 1].count + "</span>" + unit + "" : "";
                    var itemTemplate
                    if (index == 0) {
                        itemTemplate =
                            "<div class=\"col-md-3 align-center\" style=\"height: 30px;background-color: #EFF2F8;\" title=" + unitNameL + ">" + unitNameL + "</div><div class=\"col-md-3 align-center\" style=\"height: 30px;background-color: #EFF2F8;\">" + countL + "</div><div class=\"col-md-3 align-center\" style=\"height: 30px;background-color: #F5F5F5;\" title=" + unitNameR + ">" + unitNameR + "</div><div class=\"col-md-3 align-center\" style=\"height: 30px;background-color: #F5F5F5;\">" + countR + "</div>";
                    } else {
                        itemTemplate =
                            "<div class=\"col-md-3 align-center\" style=\"height: 30px;background-color: #EFF2F8;margin-top: 8px;\" title=" + unitNameL + ">" + unitNameL + "</div><div class=\"col-md-3 align-center\" style=\"height: 30px;background-color: #EFF2F8;margin-top: 8px;\">" + countL + "</div><div class=\"col-md-3 align-center\" style=\"height: 30px;background-color: #F5F5F5;margin-top: 8px;\" title=" + unitNameR + ">" + unitNameR + "</div><div class=\"col-md-3 align-center\" style=\"height: 30px;background-color: #F5F5F5;margin-top: 8px;\">" + countR + "</div>";
                    }
                    itemHtml += itemTemplate;
                }

                var templateHtml =
                    "<div class=\"col-md-4\" style=\"font-size: 14px;" + paddingHtml + "\"><div class=\"row\" style=\"height: 100%;padding-left: 10px;\"><div class=\"col-md-8\" style=\"height: 30px;\"><h3>" + title + "</h3></div><div class=\"col-md-4\" style=\"height: 30px;\"></div><div class=\"col-md-6\" style=\"height: 25px;\"><div style=\"width:7px;height:7px;border-radius:50%;background-color:#608dbd;display:inline-block\"></div><label style=\"color: #6084bd;\">&nbsp;&nbsp;前三名</label></div><div class=\"col-md-6\" style=\"height: 25px;\"><div style=\"width:7px;height:7px;border-radius:50%;background-color:#666666;display:inline-block\"></div><label style=\"color: #666666;\">&nbsp;&nbsp;后三名</label></div>" + itemHtml + "</div></div>";
                return templateHtml;
            },
            loadData: function (func) {
                // page.data.param = OPAL.form.getData("searchForm");
                // var unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                page.data.param.monitorType = $('#monitorType').val();
                if ($("#startTime").val() == "" && $("#endTime").val() == "") {
                    page.data.param.startTime = OPAL.util.dateFormat(new Date(page.data.param.startTime), "yyyy-MM-dd") + " 08:00:00";
                    page.data.param.endTime = OPAL.util.dateFormat(new Date(page.data.param.endTime), "yyyy-MM-dd") + " 08:00:00"
                } else {
                    page.data.param.startTime = $("#startTime").val() + " 08:00:00"
                    page.data.param.endTime = $("#endTime").val() + " 08:00:00"
                }

                $.ajax({
                    url: newsearchUrl,
                    data: page.data.param,
                    dataType: "JSON",
                    type: 'GET',
                    success: function (result) {

                        $("#btnSearch").prop('disabled', false);
                        var dataArr = $.ET.toObjectArr(result);
                        $('.hpjnum').text(dataArr[0].avgAvgAlarmRate)
                        $('.cznum').text(dataArr[0].avgAlarmTimelyDisposalRate + "%")
                        $('.qrnum').text(dataArr[0].avgAlarmTimelyResponseRate + "%")
                        //获取图1
                        var piedata1 = [
                            {

                                name: '工艺参数报警率',
                                "value": dataArr[0].avgCraftParaAlarmRate,
                                label: {
                                    show: true, //单独显示该数据项
                                    formatter: '{d}%'
                                }
                            },
                            {

                                "value": 100 - dataArr[0].avgCraftParaAlarmRate,
                                itemStyle: {
                                    color: '#ccc',
                                },
                                label: {
                                    emphasis: {
                                        show: false
                                    }
                                }
                            }
                        ]
                        page.logic.initCraftParaAlarmRateChart(piedata1);
                        //获取图2    
                        var piedata2 = JSON.parse(dataArr[0].priorityPreList)
                        page.logic.initPriorityChart(piedata2);
                        //获取图3
                        var piedata3 = JSON.parse(dataArr[0].durTime)
                        page.logic.initAlarmDurationChart(piedata3);
                        xData = []
                        serveData = []
                        xData1 = []
                        serveData1 = []
                        //图4
                        $('.gycansu').addClass('btn-primary').siblings('.map4but').removeClass('btn-primary')
                        chartData41 = JSON.parse(dataArr[0].craftParaAlarmRateList);
                        chartData42 = JSON.parse(dataArr[0].avgAlarmRateList);
                        chartData43 = JSON.parse(dataArr[0].avgAlarmTimelyResponseList);
                        chartData44 = JSON.parse(dataArr[0].avgAlarmTimelyDisposalList);
                        $.each(chartData41, function (i, item) {
                            xData.push(item.unitCode)
                            serveData.push({value: item.craftParaAlarmRate, name: item.unitCode})
                        })
                        page.logic.initCraftParaAlarmRateStaticChart(serveData);

                        //图5
                        $('.zbj').addClass('btn-primary').siblings('.map4but').removeClass('btn-primary')
                        chartData51 = JSON.parse(dataArr[0].splitDateList);
                        chartData52 = JSON.parse(dataArr[0].splitDateHLList);
                        $.each(chartData51, function (i, item) {
                            xData1.push(item.name)
                            serveData1.push({value: item.value, name: item.name})
                        })
                        page.logic.RinitCraftParaAlarmRateStaticChart(serveData1);
                        //工艺参数报警率
                        var alarmCount = JSON.parse(dataArr[0].craftParaAlarmRateList)
                        var alarmCountnum = alarmCount[0]
                        var alarmCountnum1 = alarmCount[1]
                        var alarmCountnum2 = alarmCount[2]

                        var alarmCountnum6 = alarmCount[alarmCount.length - 1];
                        var alarmCountnum5 = alarmCount[alarmCount.length - 2];
                        var alarmCountnum4 = alarmCount[alarmCount.length - 3];
                        var alarmCountnew = []
                        alarmCountnew.push(alarmCountnum4, alarmCountnum5, alarmCountnum6, alarmCountnum, alarmCountnum1, alarmCountnum2)

                        let alarmdata = [];    //新数组
                        alarmCountnew.map(item => {
                            let _item = JSON.parse(JSON.stringify(item).replace('unitCode', 'unitName').replace('craftParaAlarmRate', 'count'));
                            alarmdata.push(_item)
                        });
                        var alarmCountHtml = page.logic.drawBottomCard(alarmdata, "工艺参数报警率", "%", true);
                        //报警响应及时率
                        var avgAlarmTime = JSON.parse(dataArr[0].avgAlarmTimelyResponseList)
                        var avgAlarmTimenum = avgAlarmTime[0]
                        var avgAlarmTimenum1 = avgAlarmTime[1]
                        var avgAlarmTimenum2 = avgAlarmTime[2]

                        var avgAlarmTimenum6 = avgAlarmTime[avgAlarmTime.length - 1];
                        var avgAlarmTimenum5 = avgAlarmTime[avgAlarmTime.length - 2];
                        var avgAlarmTimenum4 = avgAlarmTime[avgAlarmTime.length - 3];
                        var avgAlarmTimenew = []
                        avgAlarmTimenew.push(avgAlarmTimenum, avgAlarmTimenum1, avgAlarmTimenum2, avgAlarmTimenum4, avgAlarmTimenum5, avgAlarmTimenum6)

                        let avgAlarmTimedata = [];    //新数组
                        avgAlarmTimenew.map(item => {
                            let _item = JSON.parse(JSON.stringify(item).replace('unitCode', 'unitName').replace('alarmTimelyResponseRate', 'count'));
                            avgAlarmTimedata.push(_item)
                        });
                        var avgAlarmTimeHtml = page.logic.drawBottomCard(avgAlarmTimedata, "报警响应及时率", "%", true);
                        //24小时持续报警装置排名
                        var avgAlarmAmount = JSON.parse(dataArr[0].avgAlarmAmountList)
                        var avgAlarmAmountnum = avgAlarmAmount[0]
                        var avgAlarmAmountnum1 = avgAlarmAmount[1]
                        var avgAlarmAmountnum2 = avgAlarmAmount[2]

                        var avgAlarmAmountnum6 = avgAlarmAmount[avgAlarmAmount.length - 1];
                        var avgAlarmAmountnum5 = avgAlarmAmount[avgAlarmAmount.length - 2];
                        var avgAlarmAmountnum4 = avgAlarmAmount[avgAlarmAmount.length - 3];
                        var avgAlarmAmountnew = []
                        avgAlarmAmountnew.push(avgAlarmAmountnum, avgAlarmAmountnum1, avgAlarmAmountnum2, avgAlarmAmountnum4, avgAlarmAmountnum5, avgAlarmAmountnum6)

                        let avgAlarmAmountdata = [];    //新数组
                        avgAlarmAmountnew.map(item => {
                            let _item = JSON.parse(JSON.stringify(item).replace('unitCode', 'unitName').replace('alarmAmount', 'count'));
                            avgAlarmAmountdata.push(_item)
                        });
                        var avgAlarmAmountHtml = page.logic.drawBottomCard(avgAlarmAmountdata, "24小时持续报警装置排名", "个", true);
                        //时平均报警数
                        var avgAlarmRate = JSON.parse(dataArr[0].avgAlarmRateList)
                        var avgAlarmRatenum = avgAlarmRate[0]
                        var avgAlarmRatenum1 = avgAlarmRate[1]
                        var avgAlarmRatenum2 = avgAlarmRate[2]

                        var avgAlarmRatenum6 = avgAlarmRate[avgAlarmRate.length - 1];
                        var avgAlarmRatenum5 = avgAlarmRate[avgAlarmRate.length - 2];
                        var avgAlarmRatenum4 = avgAlarmRate[avgAlarmRate.length - 3];
                        var avgAlarmRatenew = []
                        avgAlarmRatenew.push(avgAlarmRatenum, avgAlarmRatenum1, avgAlarmRatenum2, avgAlarmRatenum4, avgAlarmRatenum5, avgAlarmRatenum6)

                        let avgAlarmRatedata = [];    //新数组
                        avgAlarmRatenew.map(item => {
                            let _item = JSON.parse(JSON.stringify(item).replace('unitCode', 'unitName').replace('avgAlarmRate', 'count'));
                            avgAlarmRatedata.push(_item)
                        });
                        var avgAlarmRateHtml = page.logic.drawBottomCard(avgAlarmRatedata, "时平均报警数", "个", true);
                        //报警处置及时率
                        var avgAlarmTimelyDisposal = JSON.parse(dataArr[0].avgAlarmTimelyDisposalList)
                        var avgAlarmTimelyDisposalnum = avgAlarmTimelyDisposal[0]
                        var avgAlarmTimelyDisposalnum1 = avgAlarmTimelyDisposal[1]
                        var avgAlarmTimelyDisposalnum2 = avgAlarmTimelyDisposal[2]

                        var avgAlarmTimelyDisposalnum6 = avgAlarmTimelyDisposal[avgAlarmTimelyDisposal.length - 1];
                        var avgAlarmTimelyDisposalnum5 = avgAlarmTimelyDisposal[avgAlarmTimelyDisposal.length - 2];
                        var avgAlarmTimelyDisposalnum4 = avgAlarmTimelyDisposal[avgAlarmTimelyDisposal.length - 3];
                        var avgAlarmTimelyDisposalnew = []
                        avgAlarmTimelyDisposalnew.push(avgAlarmTimelyDisposalnum, avgAlarmTimelyDisposalnum1, avgAlarmTimelyDisposalnum2, avgAlarmTimelyDisposalnum4, avgAlarmTimelyDisposalnum5, avgAlarmTimelyDisposalnum6)

                        let avgAlarmTimelyDisposaldata = [];    //新数组
                        avgAlarmTimelyDisposalnew.map(item => {
                            let _item = JSON.parse(JSON.stringify(item).replace('unitCode', 'unitName').replace('alarmTimelyDisposalRate', 'count'));
                            avgAlarmTimelyDisposaldata.push(_item)
                        });
                        var avgAlarmTimelyDisposalHtml = page.logic.drawBottomCard(avgAlarmTimelyDisposaldata, "报警处置及时率", "%", true);
                        $("#bottomAlarm").html(alarmCountHtml + avgAlarmTimeHtml + avgAlarmAmountHtml + avgAlarmRateHtml + avgAlarmTimelyDisposalHtml);
                        //最频繁的报警位号
                        var tagUnitVOListnew = []
                        var tagUnitVOList = JSON.parse(dataArr[0].tagUnitVOList)
                        var itemHtml = "";
                        var itemTemplate
                        if (tagUnitVOList && tagUnitVOList.length >= 3) {
                            var tagUnitVOListnum = tagUnitVOList[0]
                            var tagUnitVOListnum1 = tagUnitVOList[1]
                            var tagUnitVOListnum2 = tagUnitVOList[2]

                            tagUnitVOListnew.push(tagUnitVOListnum, tagUnitVOListnum1, tagUnitVOListnum2)
                            $.each(tagUnitVOListnew, function (i, item) {
                                itemTemplate =
                                    "<div class=\"col-md-4 align-center\" style=\"height: 30px;margin-top: 8px;background-color: #EFF2F8;\"  title=" + item.unitName + ">" + item.unitName + "</div><div class=\"col-md-4 align-center\" style=\"height: 30px;margin-top: 8px;background-color: #EFF2F8;\">" + item.tag + "</div><div class=\"col-md-4 align-center\" style=\"height: 30px;margin-top: 8px;background-color: #EFF2F8;\">" + item.count + "次</div>";
                                itemHtml += itemTemplate;
                            })

                            var paddingHtml = "";
                            var title = "最频繁的报警位号";
                            var tagUnitVOListHtml =
                                "<div class=\"col-md-4\" style=\"font-size: 14px;padding-top: 6px;" + paddingHtml + "\"><div class=\"row\" style=\"height: 100%;padding-left: 10px;\"><div class=\"col-md-8\" style=\"height: 30px;\"><h3>" + title + "</h3></div><div class=\"col-md-4\" style=\"height: 30px;\"></div><div class=\"col-md-6\" style=\"height: 25px;\"><div style=\"width:7px;height:7px;border-radius:50%;background-color:#608dbd;display:inline-block\"></div></div><div class=\"col-md-6\" style=\"height: 25px;\"></div>" + itemHtml + "</div></div>";
                        } else {
                            var paddingHtml = "";
                            var title = "最频繁的报警位号";
                            var tagUnitVOListHtml =
                                "<div class=\"col-md-4\" style=\"font-size: 14px;padding-top: 6px;" + paddingHtml + "\"><div class=\"row\" style=\"height: 100%;padding-left: 10px;\"><div class=\"col-md-6\" style=\"height: 30px;\"><h3>" + title + "</h3></div><div class=\"col-md-6\" style=\"height: 30px;\"></div><div class=\"col-md-6\" style=\"height: 25px;\"><div style=\"width:7px;height:7px;border-radius:50%;background-color:#608dbd;display:inline-block\"></div></div><div class=\"col-md-6\" style=\"height: 25px;\"></div>" + "无数据" + "</div></div>";
                        }


                        $("#bottomAlarm").html(alarmCountHtml + avgAlarmTimeHtml + avgAlarmAmountHtml + avgAlarmRateHtml + avgAlarmTimelyDisposalHtml + tagUnitVOListHtml);


                        // func(dataArr)
                    },
                    error: function (result) {
                        $("#btnSearch").prop('disabled', false);
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            getAllUnit: function () {
                $.ajax({
                    url: commonUnitTreeUrl,
                    async: false,
                    data: '',
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        $("#allUnitCount").html(res ? res.length : 0);
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            calcMoM: function (data) {
                var downImgSrc = "../../../images/portal/down.png"
                var alarmCount = page.logic.searchAlarmCount()
                var lastWeekalarmCount = alarmCount[0] ? alarmCount[0].allTotalAlarmQuantity : 1;
                var alarmCountMoM = (1 - (totalAlarmNum / lastWeekalarmCount)) * 100;
                if (alarmCountMoM > 0) {
                    $("#totalAlarmNumPic").css("background-image", "url(" + downImgSrc + ")");
                }
                $("#alarmCountMoM").html(Math.abs(alarmCountMoM.toFixed(2)));

                alarmData = [];
                var lastWeekCraftParaAlarmReteCount = 0;
                var lastWeekHourAlarmCount = 0;
                var lastWeekPeakAlarmRateCount = 0;
                var lastWeekDayAlarmCount = 0;
                for (var index = 0; index < data.length; index++) {
                    lastWeekHourAlarmCount += data[index].avgAlarmRate * 6;
                    lastWeekPeakAlarmRateCount += data[index].peakAlarmRate;
                    lastWeekDayAlarmCount += data[index].alarmAmount;
                }
                lastWeekCraftParaAlarmReteCount += data[0].avgCraftParaAlarmRate;

                lastWeekCraftParaAlarmReteCount = lastWeekCraftParaAlarmReteCount == 0 ? 1 : lastWeekCraftParaAlarmReteCount;
                lastWeekHourAlarmCount = lastWeekHourAlarmCount == 0 ? 1 : lastWeekHourAlarmCount;
                lastWeekPeakAlarmRateCount = lastWeekPeakAlarmRateCount == 0 ? 1 : lastWeekPeakAlarmRateCount;
                lastWeekDayAlarmCount = lastWeekDayAlarmCount == 0 ? 1 : lastWeekDayAlarmCount;

                var craftParaAlarmReteCountMoM = (1 - (craftParaAlarmReteCount / lastWeekCraftParaAlarmReteCount)) * 100;
                if (craftParaAlarmReteCountMoM > 0) {
                    $("#craftParaAlarmReteCountPic").css("background-image", "url(" + downImgSrc + ")");
                }
                $("#craftParaAlarmReteCountMoM").html(Math.abs(craftParaAlarmReteCountMoM).toFixed(2));

                var hourAlarmCountMoM = (1 - (hourAlarmCount / lastWeekHourAlarmCount)) * 100;
                if (hourAlarmCountMoM > 0) {
                    $("#hourAlarmCountPic").css("background-image", "url(" + downImgSrc + ")");
                }
                $("#hourAlarmCountMoM").html(Math.abs(hourAlarmCountMoM).toFixed(2));

                var peakAlarmRateCountMoM = (1 - (peakAlarmRateCount / lastWeekPeakAlarmRateCount)) * 100;
                if (peakAlarmRateCountMoM > 0) {
                    $("#peakAlarmRateCountPic").css("background-image", "url(" + downImgSrc + ")");
                }
                $("#peakAlarmRateCountMoM").html(Math.abs(peakAlarmRateCountMoM).toFixed(2));

                var dayAlarmCountMoM = (1 - (dayAlarmCount / lastWeekDayAlarmCount)) * 100;
                if (dayAlarmCountMoM > 0) {
                    $("#dayAlarmCountPic").css("background-image", "url(" + downImgSrc + ")");
                }
                $("#dayAlarmCountMoM").html(Math.abs(dayAlarmCountMoM).toFixed(2));

            },
            drawBottom: function () {
                var alarmDataArray = [];
                //sortby false 从大到小
                // var alarmDataSort = function (data, sortby) {
                //     data = data.sort(
                //         function (a, b) {
                //             return sortby ? (a.count - b.count) : (b.count - a.count);
                //         }
                //     )
                //     return data;
                // }
                var alarmData = {};
                //报警个数
                // var alarmCount = page.logic.searchAlarmCount();
                var alarmCount = [
                    {sname: 'S Zorb', totalAlarmQuantity: 89, count: 89},
                    {sname: '连续重整', totalAlarmQuantity: 69, count: 69},
                    {sname: '1#常减压', totalAlarmQuantity: 100, count: 100},
                    {sname: '1#柴加', totalAlarmQuantity: 28, count: 28},
                    {sname: '2#硫磺', totalAlarmQuantity: 50, count: 50},
                    {sname: '1#焦化', totalAlarmQuantity: 132, count: 132},
                ]
                // totalAlarmNum = alarmCount[ 0 ] ? alarmCount[ 0 ].allTotalAlarmQuantity : 0;
                // $("#totalAlarmNum").html(totalAlarmNum);
                for (var index = 0; index < alarmCount.length; index++) {
                    alarmData = {};
                    alarmData.unitName = alarmCount[index].sname;
                    alarmData.count = alarmCount[index].totalAlarmQuantity;
                    alarmDataArray[index] = alarmData;
                }
                // alarmDataArray = alarmDataSort(alarmDataArray, true);
                var alarmCountHtml = page.logic.drawBottomCard(alarmDataArray, "工艺参数报警率", "个", true);

                //报警时长
                alarmData = [];
                alarmDataArray = [];
                //var aramTimeData = page.logic.searchAlarmTime()
                var aramTimeData = [
                    {
                        sname: 'S Zorb',
                        generalAlarmQuantity: 89,
                        importantAlarmQuantity: 89,
                        emergencyAlarmQuantity: 12.34
                    },
                    {
                        sname: '连续重整2',
                        generalAlarmQuantity: 69,
                        importantAlarmQuantity: 69,
                        emergencyAlarmQuantity: 112.34
                    },
                    {
                        sname: '1#常减压',
                        generalAlarmQuantity: 100,
                        importantAlarmQuantity: 108,
                        emergencyAlarmQuantity: 52.34
                    },
                    {
                        sname: '1#柴加',
                        generalAlarmQuantity: 28,
                        importantAlarmQuantity: 58,
                        emergencyAlarmQuantity: 82.34
                    },
                    {
                        sname: '2#硫磺',
                        generalAlarmQuantity: 50,
                        importantAlarmQuantity: 111,
                        emergencyAlarmQuantity: 42.34
                    },
                    {
                        sname: '1#焦化',
                        generalAlarmQuantity: 132,
                        importantAlarmQuantity: 79,
                        emergencyAlarmQuantity: 72.34
                    },
                ]
                for (var index = 0; index < aramTimeData.length; index++) {
                    alarmData = {};
                    alarmData.unitName = aramTimeData[index].sname;
                    alarmData.count = (
                        parseFloat(aramTimeData[index].generalAlarmQuantity) +
                        parseFloat(aramTimeData[index].importantAlarmQuantity) +
                        parseFloat(aramTimeData[index].emergencyAlarmQuantity)).toFixed(2);
                    alarmDataArray[index] = alarmData;
                }
                // alarmDataArray = alarmDataSort(alarmDataArray, true);
                var alarmTimeHtml = page.logic.drawBottomCard(alarmDataArray, "报警处置及时率", "%", true);

                //工艺参数报警率 craftParaAlarmRate
                // alarmData = [];
                // alarmDataArray = [];
                // for (var index = 0; index < data.length; index++) {
                //     alarmData = {};
                //     alarmData.unitName = data[ index ].unitCode;
                //     alarmData.count = data[ index ].craftParaAlarmRate;
                //     alarmDataArray[ index ] = alarmData;
                // }
                // craftParaAlarmReteCount = data[ 0 ].avgCraftParaAlarmRate;
                // $("#craftParaAlarmReteCount").html(craftParaAlarmReteCount);
                // // alarmDataArray = alarmDataSort(alarmDataArray, false);
                // var craftParaAlarmRateHtml = page.logic.drawBottomCard(alarmDataArray, "24小时持续报警装置", "", true);

                //时平均报警数 avgAlarmRate * 6
                // alarmData = [];
                // alarmDataArray = [];
                // for (var index = 0; index < data.length; index++) {
                //     alarmData = {};
                //     alarmData.unitName = data[ index ].unitCode;
                //     alarmData.count = (data[ index ].avgAlarmRate * 6).toFixed(2);
                //     hourAlarmCount += data[ index ].avgAlarmRate * 6;
                //     alarmDataArray[ index ] = alarmData;
                // }
                // $("#hourAlarmCount").html(hourAlarmCount.toFixed(2));
                // // alarmDataArray = alarmDataSort(alarmDataArray, true);
                // var avgAlarmRateHtml = page.logic.drawBottomCard(alarmDataArray, "时平均报警数", "个");

                //10分钟峰值报警数 peakAlarmRate
                // alarmData = [];
                // alarmDataArray = [];
                // for (var index = 0; index < data.length; index++) {
                //     alarmData = {};
                //     alarmData.unitName = data[ index ].unitCode;
                //     alarmData.count = data[ index ].peakAlarmRate;
                //     peakAlarmRateCount += alarmData.count;
                //     alarmDataArray[ index ] = alarmData;
                // }
                // $("#peakAlarmRateCount").html(peakAlarmRateCount);
                // // alarmDataArray = alarmDataSort(alarmDataArray, true);
                // var avgHourAlarmRateHtml = page.logic.drawBottomCard(alarmDataArray, "报警处置及时率", "%");

                //24小时持续报警数 alarmAmount
                // alarmData = [];
                // alarmDataArray = [];
                // for (var index = 0; index < data.length; index++) {
                //     alarmData = {};
                //     alarmData.unitName = data[ index ].unitCode;
                //     alarmData.count = data[ index ].alarmAmount;
                //     dayAlarmCount += alarmData.count;
                //     alarmDataArray[ index ] = alarmData;
                // }
                // $("#dayAlarmCount").html(dayAlarmCount);
                // alarmDataArray = alarmDataSort(alarmDataArray, true);
                // var alarmAmountHtml = page.logic.drawBottomCard(alarmDataArray, "最频繁的报警位号", "");
                $("#bottomAlarm").html(alarmCountHtml + alarmTimeHtml);

                // + craftParaAlarmRateHtml + avgAlarmRateHtml + avgHourAlarmRateHtml + alarmAmountHtml
                //获取上周数据
                // debugger;
                page.data.param.startTime = beforeLastWeekDate;
                page.data.param.endTime = lastWeekDate;
                page.logic.loadData(page.logic.calcMoM);

            },
            queryChartsData: function () {
                var param = {
                    topType: 10,
                    startTime: OPAL.util.dateFormat(page.data.param.startTime, "yyyy-MM-dd"),
                    endTime: OPAL.util.dateFormat(page.data.param.endTime, "yyyy-MM-dd"),
                    timeGranularity: "day"
                }
                $.ajax({
                    url: numberTopUrl,
                    async: false,
                    data: $.param(param),
                    dataType: 'json',
                    type: 'get',
                    success: function (data) {
                        var data = $.ET.toObjectArr(data);

                    },
                    error: function (data) {
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }

                });
            },
            //工艺参数报警率饼图
            initCraftParaAlarmRateChart: function (data) {
                if (craftParaAlarmRateChart && !craftParaAlarmRateChart.isDisposed()) {
                    craftParaAlarmRateChart.clear();
                    craftParaAlarmRateChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    craftParaAlarmRateChart = OPAL.ui.chart.initEmptyChart('craftParaAlarmRateChart');
                    return;
                }
                craftParaAlarmRateChart = echarts.init(document.getElementById('craftParaAlarmRateChart'));
                var option = {
                    title: [
                        {
                            // text: '工艺参数报警率',
                            left: 'center',
                            textStyle: {
                                fontSize: 15
                            }
                        }
                    ],
                    tooltip: {
                        trigger: 'item',
                        show: false,
                    },
                    color: ['#37976a'],
                    // grid: {
                    //   bottom: '0%',
                    //   top: 20,
                    //   orient: 'vertical'
                    // },
                    animation: true,
                    series: [
                        {
                            type: 'pie',
                            radius: ['60%', '80%'],
                            center: ['50%', '40%'],
                            // top: 25,
                            data: data,
                            label: {
                                normal: {//默认不显示数据
                                    show: false,
                                    position: 'center',
                                },
                                color: '#fff',
                            },

                        }
                    ]
                }
                craftParaAlarmRateChart.setOption(option);
            },
            //优先级分布饼图
            initPriorityChart: function (data) {
                if (priorityChart && !priorityChart.isDisposed()) {
                    priorityChart.clear();
                    priorityChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    priorityChart = OPAL.ui.chart.initEmptyChart('priorityChart');
                    return;
                }
                priorityChart = echarts.init(document.getElementById('priorityChart'));
                var option = {
                    title: [
                        {
                            text: '优先级分布',
                            left: 'center',
                            // top: 10,
                            textStyle: {
                                fontSize: 15
                            }
                        }
                    ],
                    color: ['#cd1515', '#fe6732', '#f4d312', '#57a2a9'],
                    tooltip: {
                        trigger: 'item'
                    },

                    legend: {
                        bottom: '1%',
                        left: 'left',
                        top: 'top',
                        orient: 'vertical',
                        itemHeight: 10,
                        itemGap: 4
                    },
                    animation: true,
                    series: [
                        {
                            name: '',
                            type: 'pie',
                            radius: ['0', '70%'],
                            center: ['50%', '55%'],
                            label: {
                                formatter: '{name|{b}}\n{value|{d}%}',
                                rich: {
                                    name: {
                                        fontSize: 13,
                                        color: '#666'
                                    },
                                    value: {
                                        fontSize: 13,
                                        color: '#666'
                                    }
                                }
                            },
                            data: data
                        }
                    ]
                }
                priorityChart.setOption(option);
            },
            //报警时长分布饼图
            initAlarmDurationChart: function (data) {
                if (alarmDurationChart && !alarmDurationChart.isDisposed()) {
                    alarmDurationChart.clear();
                    alarmDurationChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    alarmDurationChart = OPAL.ui.chart.initEmptyChart('alarmDurationChart');
                    return;
                }
                alarmDurationChart = echarts.init(document.getElementById('alarmDurationChart'));
                var option = {
                    title: [
                        {
                            text: '报警时长分布',
                            left: 'center',
                            textStyle: {
                                fontSize: 15
                            }
                        }
                    ],
                    color: ['#91cc75', '#5470c6', '#fac858', '#ee6666'],
                    tooltip: {
                        trigger: 'item'
                    },
                    legend: {
                        bottom: '1%',
                        left: 'left',
                        top: 'top',
                        orient: 'vertical',
                        itemHeight: 10,
                        itemGap: 4
                    },
                    animation: true,
                    series: [
                        {
                            name: '',
                            type: 'pie',
                            radius: ['0', '70%'],
                            center: ['50%', '55%'],
                            label: {
                                formatter: '{name|{b}}\n{value|{d}%}',
                                rich: {
                                    name: {
                                        fontSize: 13,
                                        color: '#666'
                                    },
                                    value: {
                                        fontSize: 13,
                                        color: '#666'
                                    }
                                }
                            },
                            data: data
                        }
                    ]
                }
                alarmDurationChart.setOption(option);
            },
            //柱状图-左工艺参数报警率统计
            initCraftParaAlarmRateStaticChart: function (data) {
                if (craftStaticChart && !craftStaticChart.isDisposed()) {
                    craftStaticChart.clear();
                    craftStaticChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    craftStaticChart = OPAL.ui.chart.initEmptyChart('craftStaticChart');
                    return;
                }
                craftStaticChart = echarts.init(document.getElementById('craftStaticChart'));
                var option = {
                    title: [
                        {
                            //text: '工艺参数报警率统计',
                            left: 'center',
                            top: 5,
                            textStyle: {
                                fontSize: 15
                            }
                        }
                    ],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    grid: {
                        left: '2%',
                        right: '1%',
                        bottom: '8%',
                        top: '22%',
                        containLabel: true
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: xData,
                            axisTick: {
                                alignWithLabel: true
                            },
                            nameGap: -50,
                            axisLabel: {
                                show: true,
                                // showMaxLabel: true,
                                // showMinLabel: true,
                                textStyle: {
                                    color: '#000',
                                    // padding: [ 0, 0, 0, 0],
                                    fontSize: 14,
                                },
                                align: 'left',
                                verticalAlign: 'top',
                                rotate: -20,
                                formatter: function (value) {
                                    var str = value.split('');
                                    return str.join('\n')
                                }
                                // interval: this.mdata
                            }
                        }
                    ],
                    yAxis: {
                        //name: this.yoptionthreename,
                        nameTextStyle: {
                            color: '#000',
                            fontSize: 14,
                            padding: [0, 0, 0, -25] // 上右下左与原位置距离
                        },
                        type: 'value',
                        splitLine: {
                            show: false //去掉网格线
                        },
                        // min: 0,
                        // max: 16,
                        // splitNumber: 8,
                        axisLine: {
                            // 坐标轴 轴线
                            show: true, // 是否显示
                            lineStyle: {
                                color: '#D2DDFD', // 坐标轴线线的颜⾊
                                type: 'dashed' // 坐标轴线线的类型（'solid'，实线类型；'dashed'，虚线类型；'dotted',点状类型）
                            }
                        },
                        axisLabel: {
                            color: '#000',
                            fontSize: 14,

                        }
                    },
                    series: [
                        {
                            name: '工艺参数报警率统计',
                            type: 'bar',
                            barWidth: '15px',
                            // 实现数字展示在柱状图
                            label: {
                                show: true,
                                position: 'top',
                                color: 'black',
                                fontSize: 14,
                            },
                            data: data,
                            color: ['#84C8FE']
                        }
                    ]
                }
                craftStaticChart.setOption(option);
            },
            //柱状图-右报警总数
            RinitCraftParaAlarmRateStaticChart: function (data) {
                if (mostFrequentNumberChart && !mostFrequentNumberChart.isDisposed()) {
                    mostFrequentNumberChart.clear();
                    mostFrequentNumberChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    mostFrequentNumberChart = OPAL.ui.chart.initEmptyChart('mostFrequentNumber');
                    return;
                }
                mostFrequentNumberChart = echarts.init(document.getElementById('mostFrequentNumber'));
                var option = {
                    title: [
                        {
                            //text: '工艺参数报警率统计',
                            left: 'center',
                            top: 5,
                            textStyle: {
                                fontSize: 15
                            }
                        }
                    ],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    grid: {
                        left: '2%',
                        right: '1%',
                        bottom: '15%',
                        top: '20%',
                        containLabel: true
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: xData1,
                            axisTick: {
                                alignWithLabel: true
                            },
                            axisLabel: {
                                show: true,
                                // showMaxLabel: true,
                                // showMinLabel: true,
                                textStyle: {
                                    color: '#000',
                                    // padding: [ 0, 0, 0, 0],
                                    fontSize: 14,
                                },
                                align: 'left',
                                verticalAlign: 'top',
                                //  rotate: -20,
                                // formatter: function (value) {
                                //     var str = value.split('');
                                //     return str.join('\n')
                                // }
                                // interval: this.mdata
                            }
                        }
                    ],
                    yAxis: {
                        //name: this.yoptionthreename,
                        nameTextStyle: {
                            color: '#000',
                            fontSize: 14,
                            padding: [0, 0, 0, -25] // 上右下左与原位置距离
                        },
                        type: 'value',
                        splitLine: {
                            show: false //去掉网格线
                        },
                        // min: 0,
                        // max: 16,
                        // splitNumber: 8,
                        axisLine: {
                            // 坐标轴 轴线
                            show: true, // 是否显示
                            lineStyle: {
                                color: '#D2DDFD', // 坐标轴线线的颜⾊
                                type: 'dashed' // 坐标轴线线的类型（'solid'，实线类型；'dashed'，虚线类型；'dotted',点状类型）
                            }
                        },
                        axisLabel: {
                            color: '#000',
                            fontSize: 14,

                        }
                    },
                    dataZoom: [
                        {
                            height: 20,
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            // left: '20%',
                            bottom: '25px',
                            start: 0,
                            end: 100
                        }
                    ],
                    series: [
                        {
                            name: '报警总数',
                            type: 'bar',
                            barWidth: '15px',
                            // 实现数字展示在柱状图
                            label: {
                                show: true,
                                position: 'top',
                                color: 'black',
                                fontSize: 14,
                            },
                            data: data,
                            color: ['#84C8FE']
                        }
                    ]
                }
                mostFrequentNumberChart.setOption(option);
            },
            // initMostFrequentNumberChart: function (data) {
            //     if (mostFrequentNumberChart && !mostFrequentNumberChart.isDisposed()) {
            //         mostFrequentNumberChart.clear();
            //         mostFrequentNumberChart.dispose();
            //     }
            //     if (data == null || data == undefined || data.length == 0) {
            //         mostFrequentNumberChart = OPAL.ui.chart.initEmptyChart('mostFrequentNumber');
            //         return;
            //     }
            //     mostFrequentNumberChart = echarts.init(document.getElementById('mostFrequentNumber'));
            //     var option = {
            //         title: {
            //             show: true, //显示策略，默认值true,可选为：true（显示） | false（隐藏）

            //             link: '', //主标题文本超链接,默认值true
            //             target: null, //指定窗口打开主标题超链接，支持'self' | 'blank'，不指定等同为'blank'（新窗口）
            //             sublink: '', //副标题文本超链接
            //             subtarget: null, //指定窗口打开副标题超链接，支持'self' | 'blank'，不指定等同为'blank'（新窗口）
            //             x: 'center', //水平安放位置，默认为'left'，可选为：'center' | 'left' | 'right' | {number}（x坐标，单位px）
            //             y: 'top', //垂直安放位置，默认为top，可选为：'top' | 'bottom' | 'center' | {number}（y坐标，单位px）
            //             textAlign: null, //水平对齐方式，默认根据x设置自动调整，可选为： left' | 'right' | 'center
            //             backgroundColor: 'rgba(0,0,0,0)', //标题背景颜色，默认'rgba(0,0,0,0)'透明
            //             borderColor: '#ccc', //标题边框颜色,默认'#ccc'
            //             borderWidth: 0, //标题边框线宽，单位px，默认为0（无边框）
            //             padding: 10, //标题内边距，单位px，默认各方向内边距为5，接受数组分别设定上右下左边距
            //             itemGap: 10, //主副标题纵向间隔，单位px，默认为10
            //             textStyle: { //主标题文本样式{"fontSize": 18,"fontWeight": "bolder","color": "#333"}
            //                 fontFamily: 'Arial, Verdana, sans...',
            //                 fontSize: "20px",
            //                 fontWeight: "bold",
            //                 fontStyle: 'normal',
            //                 fontWeight: 'normal',
            //             },
            //             zlevel: 0, //一级层叠控制。默认0,每一个不同的zlevel将产生一个独立的canvas，相同zlevel的组件或图标将在同一个canvas上渲染。zlevel越高越靠顶层，canvas对象增多会消耗更多的内存和性能，并不建议设置过多的zlevel，大部分情况可以通过二级层叠控制z实现层叠控制。
            //             z: 6, //二级层叠控制，默认6,同一个canvas（相同zlevel）上z越高约靠顶层。
            //         },
            //         grid: {
            //             left: '1%',
            //             right: '1%',
            //             bottom: '1%',
            //             top: '5%',
            //             height: '220px',
            //             containLabel: true
            //         },
            //         xAxis: [ {
            //             type: 'category',
            //             data: [],
            //             axisTick: {
            //                 alignWithLabel: true
            //             },
            //             splitLine: { //网格线
            //                 show: true,
            //                 lineStyle: {
            //                     color: [ '#e5e5e5' ],
            //                     type: 'solid'
            //                 }
            //             },
            //             flagName: []
            //         } ],
            //         yAxis: [ {
            //             type: 'value',
            //             splitLine: {
            //                 show: true,
            //                 lineStyle: {
            //                     color: [ '#e5e5e5' ],
            //                     type: 'solid'
            //                 }
            //             }

            //         } ],
            //         tooltip: {
            //             // position: ['30%', '20%'],
            //             color: [ '#3398DB' ],
            //             trigger: 'axis',
            //             axisPointer: {
            //                 type: ''
            //             },

            //         },
            //         series: [ {
            //             type: 'bar',
            //             itemStyle: {
            //                 normal: {
            //                     color: '#4F81BD'
            //                 }
            //             },
            //             data: [],
            //             label: {
            //                 show: true,
            //                 position: 'insideTop',
            //                 valueAnimation: true,
            //             }
            //         } ]
            //     };
            //     var totalArray = new Array();
            //     var tagArray = new Array();
            //     var flagArray = new Array();
            //     var flagIdArray = new Array();
            //     var unitInfoArray = new Array();
            //     var locationArray = new Array();
            //     if (data != null || data != undefined) {
            //         for (var i = 0; i < data.length; i++) {
            //             totalArray.push(data[ i ][ "alarmCount" ]);
            //             tagArray.push(data[ i ][ 'unitName' ] + "\n" + data[ i ][ 'tag' ]);
            //             unitInfoArray.push(data[ i ][ 'unitName' ] + "    位号：" + data[ i ][ 'tag' ]);
            //             flagArray.push(data[ i ][ 'alarmFlag' ]);
            //             flagIdArray.push(data[ i ][ 'alarmFlagId' ]);
            //             locationArray.push(data[ i ][ 'location' ]);
            //         }
            //         option.xAxis[ 0 ].flagName = flagArray;
            //         option.xAxis[ 0 ].unitInfo = unitInfoArray;
            //         option.xAxis[ 0 ].location = locationArray;
            //     }
            //     option.xAxis[ 0 ].data = tagArray;
            //     option.series[ 0 ].data = totalArray;
            //     mostFrequentNumberChart.setOption(option);
            // },
            searchAlarmTime: function () {
                var alarmTimeData = [];
                page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                $.ajax({
                    url: alarmDurationStattUrl,
                    data: page.data.param,
                    dataType: 'json',
                    async: false,
                    success: function (data) {
                        var result = $.ET.toObjectArr(data);
                        alarmTimeData = result;
                    },
                    error: function () {
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
                return alarmTimeData;
            },
            searchAlarmCount: function () {
                var alarmCountData = [];
                //进行时间校验
                page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                $.ajax({
                    url: alarmNumStattUrl,
                    data: page.data.param,
                    dataType: 'json',
                    async: false,
                    success: function (data) {
                        var result = $.ET.toObjectArr(data);
                        alarmCountData = result;
                    },
                    complete: function () {
                    }
                });
                return alarmCountData;
            },
            exportExcel: function () {
                var data = {};
                data.monitorType = $('#monitorType').val()
                data.startTime = JSON.parse(JSON.stringify($("#startTime").val() + " 08:00:00"))
                data.endTime = JSON.parse(JSON.stringify($("#endTime").val() + " 08:00:00"))
                OPAL.form.setExportExcelData('formExportExcel', data);
                $('#formExportExcel').attr('action', exportUrl);
                $('#formExportExcel').submit();
            },
        }
    };
    page.init();
    window.page = page;
})