package com.pcitc.opal.ak.dao;

import com.pcitc.opal.ak.pojo.AlarmKnowlgManagmt;
import com.pcitc.opal.as.pojo.AlarmStdManagmt;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

import java.util.Date;
import java.util.List;

/*
 * 报警知识管理实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_AlarmKnowlgManagmtRepositoryCustom
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理实体的Repository的JPA自定义接口
 */
public interface AlarmKnowlgManagmtRepositoryCustom {

	/**
	 * 新增报警知识维护数据
	 *
	 * <AUTHOR> 2018-03-09
	 * @param alarmKnowlgManagmt 报警知识实体
	 * @throws Exception
	 * @return CommonResult 消息结果类
	 */
	 CommonResult addAlarmKnowlgManagmt(AlarmKnowlgManagmt alarmKnowlgManagmt);

	/**
	 * 加载报警知识维护主数据
	 *
	 * @param eventId       报警事件id
	 * @param alarmPointId  报警点id
	 * @param alarmFlagId   报警标识id
	 * @param page   分页信息
	 * @return 报警知识维护数据集合
	 * @throws Exception
	 * <AUTHOR> 2018-03-09
	 */
	PaginationBean<AlarmKnowlgManagmt> getAlarmKnowlgManagmt(Long eventId,Long alarmPointId,Long alarmFlagId, Pagination page);

}
