package com.pcitc.opal.common.bll.vo;

import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;
import java.util.Date;

/*
 * 组织机构
 * 模块编号：pcitc_iol_vo_class_OrgVO
 * 作    者：dongsheng.zhao
 * 创建时间：2018-07-30
 * 修改编号：1
 * 描    述：组织机构
 */
public class OrgVO extends BaseResRep implements Serializable {

    private Long orgId;

    private String orgCode;

    private String orgName;

    private String orgAlia;

    private Long orgTyeId;

    private String englishName;

    private String legar;

    private String address;

    private Long zipCode;

    private String contract;

    private String contractTel;

    private String fax;

    private String email;

    private String bankId;

    private String accountNo;

    private String taxNo;

    private String invoiceTitle;

    private String des;

    private Integer orderId;

    private Integer enabled;

    private String crtUserId;

    private String crtUserName;

    private Date crtUserDate;

    private String mntUserId;

    private String mntUserName;

    private Date mntUserDate;

    private Integer version;

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgAlia() {
        return orgAlia;
    }

    public void setOrgAlia(String orgAlia) {
        this.orgAlia = orgAlia;
    }

    public Long getOrgTyeId() {
        return orgTyeId;
    }

    public void setOrgTyeId(Long orgTyeId) {
        this.orgTyeId = orgTyeId;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getLegar() {
        return legar;
    }

    public void setLegar(String legar) {
        this.legar = legar;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Long getZipCode() {
        return zipCode;
    }

    public void setZipCode(Long zipCode) {
        this.zipCode = zipCode;
    }

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract;
    }

    public String getContractTel() {
        return contractTel;
    }

    public void setContractTel(String contractTel) {
        this.contractTel = contractTel;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getTaxNo() {
        return taxNo;
    }

    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public String getCrtUserId() {
        return crtUserId;
    }

    public void setCrtUserId(String crtUserId) {
        this.crtUserId = crtUserId;
    }

    public String getCrtUserName() {
        return crtUserName;
    }

    public void setCrtUserName(String crtUserName) {
        this.crtUserName = crtUserName;
    }

    public Date getCrtUserDate() {
        return crtUserDate;
    }

    public void setCrtUserDate(Date crtUserDate) {
        this.crtUserDate = crtUserDate;
    }

    public String getMntUserId() {
        return mntUserId;
    }

    public void setMntUserId(String mntUserId) {
        this.mntUserId = mntUserId;
    }

    public String getMntUserName() {
        return mntUserName;
    }

    public void setMntUserName(String mntUserName) {
        this.mntUserName = mntUserName;
    }

    public Date getMntUserDate() {
        return mntUserDate;
    }

    public void setMntUserDate(Date mntUserDate) {
        this.mntUserDate = mntUserDate;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}

