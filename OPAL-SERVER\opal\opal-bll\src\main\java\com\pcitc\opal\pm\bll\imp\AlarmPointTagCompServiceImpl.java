package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.pm.bll.AlarmPointTagCompService;
import com.pcitc.opal.pm.bll.entity.AlarmPointTagCompEntity;
import com.pcitc.opal.pm.dao.AlarmPointTagCompRepository;
import com.pcitc.opal.pm.pojo.AlarmPointTagComp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/*
 * 事件类型对照配置业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmEventTypeCompServiceImpl
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：事件类型对照配置业务逻辑层实现类
 */
@Service
@Component
public class AlarmPointTagCompServiceImpl implements AlarmPointTagCompService {



    @Autowired
    AlarmPointTagCompRepository alarmPointTagCompRepository;
    @Override
    public List<AlarmPointTagCompEntity> getAlarmPointTagCompEntitys(Long comId) throws Exception {
        List<AlarmPointTagCompEntity> alarmPointTagCompEntityList =new ArrayList<AlarmPointTagCompEntity>();
        List<AlarmPointTagComp> alarmPointTagCompList = alarmPointTagCompRepository.getAlarmPointTagCompEntitys(comId);
        alarmPointTagCompList.forEach(l->{
            AlarmPointTagCompEntity alarmPointTagCompEntity =new AlarmPointTagCompEntity();
            BeanUtils.copyProperties(l, alarmPointTagCompEntity);
            alarmPointTagCompEntityList.add(alarmPointTagCompEntity);
        });
        return alarmPointTagCompEntityList;
    }

    @Override
    public List<Long> distinctByComId() {
        return alarmPointTagCompRepository.distinctByComId();
    }
}
