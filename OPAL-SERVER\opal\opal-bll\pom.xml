<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pcitc.opal</groupId>
		<artifactId>opal</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<artifactId>opal-bll</artifactId>
	<name>opal-bll</name>
	<packaging>jar</packaging>
    <repositories>
        <repository>
            <id>workFlowMavenCenter</id>
            <name>MavenMirror</name>
            <url>http://nexus.paas.sinopec.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
	<dependencies>
	    <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pcitc.opal</groupId>
			<artifactId>opal-dal</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pcitc.opal</groupId>
			<artifactId>opal-dao</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pcitc.opal</groupId>
			<artifactId>opal-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pcitc.opal</groupId>
			<artifactId>opal-webservice</artifactId>
		</dependency>
		<!--工作流开始-->
		<dependency>
			<groupId>dps</groupId>
			<artifactId>dps-api</artifactId>
			<version>0.0.2</version>
		</dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <!--工作流结束-->
	</dependencies>
</project>