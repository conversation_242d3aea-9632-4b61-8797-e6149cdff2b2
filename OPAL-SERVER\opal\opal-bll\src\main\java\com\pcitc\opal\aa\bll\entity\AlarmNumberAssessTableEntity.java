package com.pcitc.opal.aa.bll.entity;

/*
 * 报警数量评估表格实体
 * 模块编号：pcitc_opal_bll_class_AlarmNumberAssessTableEntity
 * 作  　者：kun.zhao
 * 创建时间：2017-10-30
 * 修改编号：1
 * 描    述：报警数量评估表格实体
 */
public class AlarmNumberAssessTableEntity {
	 /**
     * 报警名称
     */
    private String name;
    /**
     * 操作名称
     */
    private String operationName;
    /**
     * 时间字符串
     */
    private String timeStr;
    /**
     * 报警数量
     */
    private Long count = 0l;
    /**
     * 操作数量
     */
    private Long operationCount = 0l;
    
	public AlarmNumberAssessTableEntity() {}
	public AlarmNumberAssessTableEntity(String operationName, String timeStr, Long operationCount) {
		this.operationName = operationName;
		this.timeStr = timeStr;
		this.operationCount = operationCount;
	}

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getTimeStr() {
		return timeStr;
	}
	public void setTimeStr(String timeStr) {
		this.timeStr = timeStr;
	}
	public Long getCount() {
		return count;
	}
	public void setCount(Long count) {
		this.count = count;
	}
	public String getOperationName() {
		return operationName;
	}
	public void setOperationName(String operationName) {
		this.operationName = operationName;
	}
	public Long getOperationCount() {
		return operationCount;
	}
	public void setOperationCount(Long operationCount) {
		this.operationCount = operationCount;
	}
}
