package com.pcitc.opal.ap.dao.imp;


/*
 * 报警点实体
 * 模块编号：pcitc_pojo_class_Group
 * 作       者：guoganxin
 * 创建时间：2023/04/16
 * 修改编号：1
 * 描       述：群组
 */
public class AlarmPushRuleUnitRelDetailEntityVO {
    public AlarmPushRuleUnitRelDetailEntityVO(Long apRuleUnitRelDetailId, Long apRuleUnitRelId, String unitCode, String unitName, String workshopName, String factoryName) {
        this.apRuleUnitRelDetailId = apRuleUnitRelDetailId;
        this.apRuleUnitRelId = apRuleUnitRelId;
        this.unitCode = unitCode;
        this.unitName = unitName;
        this.workshopName = workshopName;
        this.factoryName = factoryName;
    }

    /**
     * 报警推送规则装置关系明细ID
     */
    private Long apRuleUnitRelDetailId;

    /**
     * 报警推送规则装置关系ID
     */
    private Long apRuleUnitRelId;

    /**
     * 装置编码
     */
    private String unitCode;

    /**
     * 装置名称
     */
    private String unitName;

    /**
     * 车间名称
     */
    private String workshopName;

    /**
     * 工厂名称
     */
    private String factoryName;

    public Long getApRuleUnitRelDetailId() {
        return apRuleUnitRelDetailId;
    }

    public void setApRuleUnitRelDetailId(Long apRuleUnitRelDetailId) {
        this.apRuleUnitRelDetailId = apRuleUnitRelDetailId;
    }

    public Long getApRuleUnitRelId() {
        return apRuleUnitRelId;
    }

    public void setApRuleUnitRelId(Long apRuleUnitRelId) {
        this.apRuleUnitRelId = apRuleUnitRelId;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getWorkshopName() {
        return workshopName;
    }

    public void setWorkshopName(String workshopName) {
        this.workshopName = workshopName;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }
}
