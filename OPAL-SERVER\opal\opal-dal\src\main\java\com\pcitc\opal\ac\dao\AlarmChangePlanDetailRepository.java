package com.pcitc.opal.ac.dao;
import com.pcitc.opal.ac.pojo.AlarmChangePlanDetail;

import org.springframework.data.jpa.repository.JpaRepository;

/*
 * AlarmChangePlanDetail实体的Repository的JPA标准接口
 * 模块编号： pcitc_opal_dal_interface_AlarmChangePlanRepository
 * 作       者：xuelei.wang
 * 创建时间：2018/1/19
 * 修改编号：1
 * 描       述：AlarmChangePlanDetail实体的Repository的JPA标准接口
 */
public interface AlarmChangePlanDetailRepository extends JpaRepository<AlarmChangePlanDetail, Long>, AlarmChangePlanDetailRepositoryCustom {

}
