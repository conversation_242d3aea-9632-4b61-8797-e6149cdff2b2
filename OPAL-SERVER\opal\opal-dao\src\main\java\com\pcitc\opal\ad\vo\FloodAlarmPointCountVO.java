package com.pcitc.opal.ad.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class FloodAlarmPointCountVO implements Serializable {

    private String tag;

    private String location;

    private String unitName;

    private String prdtCellName;
    /**
     * 报警标识
     */
    private String alarmFlag;

    private Long alarmCount;

    private Date alarmTime;

    private Integer monitorType;

    private String monitorTypeStr;

}
