package com.pcitc.opal.af.bll;

import com.pcitc.opal.af.bll.entity.AlarmStatsListEntity;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.util.Date;
import java.util.List;

public interface AlarmStatService {

    /**
     * 报警统计首页
     *
     * @param startTime
     * @param endTime
     * @param companyId
     * @param monitorType
     * @return
     * @throws Exception
     */
    List<AlarmStatsListEntity> getAlarmStatsPortal(Date startTime, Date endTime, Integer companyId, Integer monitorType);



    XWPFDocument exportAlarmStatsPortal(Date startTime, Date endTime, Integer monitorType) throws Exception;


}
