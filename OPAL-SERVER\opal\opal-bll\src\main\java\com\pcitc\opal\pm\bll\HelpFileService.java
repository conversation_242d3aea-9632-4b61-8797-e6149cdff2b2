package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.HelpFileEntity;
import org.springframework.stereotype.Service;

/**
 * Author:隋
 * 2019/9/19 0019
 */
@Service
public interface HelpFileService {

    /**
     * 添加帮助文档
     * @param helpFileEntity
     * @return
     */
    CommonResult addHelpFile(HelpFileEntity helpFileEntity) throws Exception;

    /**
     * 删除
     * @param helpFileId
     * @return
     */
    CommonResult deleteHelpFile(Long helpFileId) throws Exception;

    /**
     * 获取所有帮助文档（上传时间倒序）
     * @param page
     * @return
     */
    PaginationBean<HelpFileEntity> getHelpFile(Pagination page) throws Exception;

    /**
     * 获取单条帮助文档
     * @param helpFileId
     * @return
     * @throws Exception
     */
    HelpFileEntity getSingleHelpFile(Long helpFileId) throws Exception;
}
