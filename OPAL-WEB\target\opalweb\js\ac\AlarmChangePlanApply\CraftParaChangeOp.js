$(function() {
    var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
    // var currentTimeUrl = OPAL.API.commUrl + "/getSysDateTime";
    // var getQueryTimeUrl = OPAL.API.commUrl + '/getQueryTime';
    var delUrl = OPAL.API.acUrl + '/alarmChangePlanApply/deletePlanDetail';
    var saveUrl = OPAL.API.acUrl + '/alarmChangePlanApply/addAlarmChangePlan';
    var updateUrl = OPAL.API.acUrl + '/alarmChangePlanApply/updateAlarmChangePlan';
    var getSingleUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getSingleAlarmChangePlan';
    var planDetailUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getPlanDetail';
    var planDetailItemListUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getPlanDetailItemList';
    var getTimeUrl = OPAL.API.commUrl + '/getQueryStartAndEndDate';
    var submitUrl = OPAL.API.acUrl + "/alarmChangePlanApply/submitAlarmChangePlan";
    var pageMode = PageModelEnum.NewAdd;
    window.pageLoadMode = PageLoadMode.None;
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var alarmChangeItemArr, rowData; //维护调整事项页面相关参数
    var page = {
        /**
         * 初始化
         */
        init: function() {

            this.bindUI();
            //初始化装置
            page.logic.initUnitTree();
            page.logic.getTime();
        },
        bindUI: function() {
            //表格自适应页面拉伸宽度
            $(window).resize(function() {
                $('#alarmChangeItemTable').bootstrapTable('resetView');
            });
            //关闭
            $('#closePage').click(function() {
                page.logic.closeLayer();
            });
            //保存
            $('#saveAddModal').click(function() {
                page.logic.save();
            });
            //提交
            $('#submitAddModal').click(function() {
                page.logic.submitData();
            });
            //关闭
            $('.closeBtn').click(function() {
                page.logic.closeLayer();
            });
            //新增
            $('#alarmChangeItemAdd').click(function() {
                page.logic.changeTagList();
            });
            //删除
            $('#alarmChangeItemDel').click(function() {
                page.logic.delAll();
            })
            //审批信息
            $('#aproBtn').click(function() {
                page.logic.AlarmChangePlanApro();
            })
        },
        data: {
            // 设置查询参数
            param: {},
            unitParam: {}
        },
        logic: {
            //刷新列表
            refreshTable: function() {
                $("#alarmChangeItemTable").bootstrapTable('refresh', {
                    "url": planDetailUrl,
                    "pageNumber": 1,

                });
            },
            //初始化调整事项列表
            initTable: function() {
                OPAL.ui.initBootstrapTable("alarmChangeItemTable", {
                    cache: false,
                    url: '',
                    detailView: true,
                    columns: [{
                        field: '',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center'
                    }, {
                        title: "操作",
                        field: '',
                        rowspan: 1,
                        align: 'center',
                        formatter: page.logic.onActionRenderer,
                        width: '90px'
                    }, {
                        title: "序号",
                        field: '',
                        formatter: function(value, row, index) {
                            var tableOption = $('#alarmChangeItemTable').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            return (index + 1) + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '85px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "生产单元",
                        field: 'prdtName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "计量单位",
                        field: 'measUnitName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "描述",
                        field: 'des',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "审批结果",
                        field: 'aproStatusName',
                        rowspan: 1,
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "审批时间",
                        field: 'aproTime',
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "审批人",
                        field: 'aproUserName',
                        rowspan: 1,
                        align: 'center',
                        width: '60px'
                    }, {
                        title: "审批意见",
                        field: 'aproOpnion',
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }],
                    onExpandRow: function(index, row, $detail) {
                        page.logic.initPlanDetailItemList(index, row, $detail);
                    },
                    responseHandler: function(res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        if ($.ET.toObjectArr(res).length > 0) {
                            $("#unitId").combo('disable');
                            $('.textbox-disabled').css('background', 'rgb(235, 235, 228)')
                        } else {
                            $("#unitId").combo('enable');
                            $('.textbox-disabled').css('background', '#fff')
                        }
                        return item;
                    },
                }, page.logic.queryParams)
            },
            /**
             * 添加配置行按钮操作
             */
            onActionRenderer: function() {
                var rowData = arguments[1];
                if(rowData.aproStatus == 0 || rowData.aproStatus == 3){
                    return [
                        '<a name="TableEditor"  href="javascript:window.page.logic.edit(' + rowData.planDetailId + ',' + rowData.planId + ',' + rowData.alarmPointId + ',' + rowData.alarmFlagId + ',\'' + rowData.alarmFlagName + '\')">编辑</a> &nbsp;&nbsp;' +
                        '<a name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.planDetailId + '\',\'' + rowData.aproStatus + '\')" >删除</a> '
                    ]
                }else {
                    return [
                        '<a name="TableEditor"  href="javascript:window.page.logic.edit(' + rowData.planDetailId + ',' + rowData.planId + ',' + rowData.alarmPointId + ',' + rowData.alarmFlagId + ',\'' + rowData.alarmFlagName + '\')">编辑</a> &nbsp;&nbsp;' +
                        '<span style="color: #999">删除</span> '
                    ]
                }
                
            },
            /**
             * 查询参数
             */
            queryParams: function(p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    aproType:2
                };
                return $.extend(page.data.param, param);
            },
            //二级列表
            initPlanDetailItemList: function(index, row, $detail) {
                var param = {
                    planId: row.planId,
                    planDetailId: row.planDetailId,
                    pageSize: 10,
                    pageNumber: 1,
                    sortOrder: 'asc'
                };
                var subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                OPAL.ui.initBootstrapTable(subId, {
                    cache: false,
                    url: planDetailItemListUrl + "?now=" + Math.random(),
                    striped: true,
                    pagination: false,
                    sidePagination: 'client',
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'changeItemTypeName',
                        title: '调整事项',
                        align: 'center',
                    }, {
                        field: 'beforeContent',
                        title: '调整前',
                        align: 'center',
                    }, {
                        field: 'afterContent',
                        title: '调整后',
                        align: 'center',
                    }],
                    formatNoMatches: function() {
                        return "没有查询到数据！";
                    },
                    responseHandler: function(res) {
                        return $.ET.toObjectArr(res);
                    },
                }, param)
            },

            //位号清单
            changeTagList: function() {
                layer.open({
                    type: 2,
                    title: '位号清单',
                    closeBtn: 1,
                    area: ['95%', '100%'],
                    shadeClose: false,
                    fixed: true,
                    content: 'ChangeTagList.html?' + Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            'unitId': page.data.unitParam.unitId,
                            'unitName': page.data.unitParam.unitName,
                            'planCode': $('#planCode').val(),
                            'planId': $("#planId").val()
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function() {
                        if (pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.refreshTable();
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            //编辑
            edit: function(planDetailId, planId, alarmPointId, alarmFlagId) {
                var pageMode = PageModelEnum.Edit;
                layer.open({
                    type: 2,
                    title: '维护调整事项',
                    closeBtn: 1,
                    area: ['90%', '100%'],
                    offset: '30px',
                    shadeClose: false,
                    fixed: true,
                    content: 'AlarmChangeItemAddOrEdit.html?' + Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        rowData = {
                            'planDetailId': planDetailId,
                            'planId': planId,
                            'alarmPointId': alarmPointId,
                            'alarmFlagId': alarmFlagId,
                            'pageMode': pageMode,
                            'unitId': $('#unitId').val()
                        }
                        iframeWin.page.logic.setData(alarmChangeItemArr, rowData);
                    },
                    end: function() {
                        if (pageLoadMode == PageLoadMode.Refresh) {
                            page.data.param = {
                                'planId': planId,
                                'pageNumber': 1,
                            }
                            page.logic.refreshTable();
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 批量删除
             */
            delAll: function() {
                var idsArray = new Array();
                var flag = 0; //非 0未提交状态的数据 计数
                var rowsArray = $("#alarmChangeItemTable").bootstrapTable('getSelections');
                $.each(rowsArray, function(i, el) {
                    idsArray.push(el.planDetailId);
                    if(el.aproStatus !=0){
                        flag++;
                    }
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                if(flag>0) {
                    layer.msg("只能删除状态为未提交的数据！");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: {
                            data:idsArray,
                            aproType:2
                        },
                        dataType: "text",
                        // contentType: "application/json;charset=utf-8",
                        type: 'POST', //PUT DELETE POST
                        success: function(result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#alarmChangeItemTable').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function(index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function(id,status) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    $.ajax({
                        url: delUrl,
                        async: false, //
                        data:{
                            data:data,
                            aproType:2
                        },
                        dataType: "text",
                        // contentType: "application/json;charset=utf-8",
                        type: 'POST', //PUT DELETE POST
                        success: function(result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#alarmChangeItemTable').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function(index) {
                    layer.close(index)
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect("unitId", commonUnitTreeUrl, "id", "parentId", "sname", {
                    multiple: false,
                    onlyLeafCheck: true
                }, false, function() {});
                $("#unitId").combotree("getValues");
            },
            //页面初始化
            setData: function(data) {
                page.data.param = {
                    'planId': data.planId,
                    'pageNumber': 1
                }
                pageMode = data.pageMode;
                page.logic.initTable();
                $("#pageTitle").text(data.title);
                if (pageMode == PageModelEnum.NewAdd) {
                    $('#submitAddModal').attr('disabled', 'disabled');
                    $('#aproBtn').attr('disabled', 'disabled');
                    $('#alarmChangeItemAdd').attr('disabled', 'disabled');
                    $('#alarmChangeItemDel').attr('disabled', 'disabled');
                    return;
                } else if (pageMode == PageModelEnum.Edit) {
                    $('#startTime').attr('disabled', 'disabled');
                    $('#endTime').attr('disabled', 'disabled');
                }
                $.ajax({
                    url: getSingleUrl + "/" + data.planId + "?now=" + Math.random(),
                    type: "get",
                    async: false,
                    dataType: "json",
                    success: function(data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        OPAL.form.setData('AddOrEditModal', entity);
                        $('#submitAddModal').val('提交');
                        //树形结构处理
                        $("#unitId").combotree('setValue', entity["unitId"]);
                        $("#planId").val(entity["planId"]);
                        page.data.unitParam.unitId = $("#unitId").val();
                        page.data.unitParam.unitName = $("#unitId").combo('getText');
                        //审批信息
                        if (entity["flowCaseId"] == "") {
                            $("#aproBtn").attr('disabled', 'disabled');
                        }
                        page.data.param.flowCaseId = entity["flowCaseId"];
                        page.logic.refreshTable();
                    },
                    error: function(XMLHttpRequest, textStatus) {}
                });
            },
            //保存
            save: function() {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data = OPAL.form.getETCollectionData('AddOrEditModal');
                //处理提交类型
                var ajaxType = "POST";
                if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                    saveUrl = updateUrl;
                }
                $.ajax({
                    url: saveUrl,
                    async: false,
                    type: ajaxType,
                    data: JSON.stringify(data),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function(result, XMLHttpRequest) {
                        layer.msg("保存成功!");
                        if (pageMode == PageModelEnum.NewAdd) {
                            var res = $.parseJSON(result);
                            $('#applyTime').val(res.date);
                            $('#planCode').val(res.planCode);
                            $("#planId").val(res.planId);
                            $('#alarmChangeItemAdd').attr('disabled', false);
                            $('#alarmChangeItemDel').attr('disabled', false);
                            $('#submitAddModal').attr('disabled', false);
                            pageMode = PageModelEnum.Edit;
                            page.data.param.planId = res.planId;
                        } else {
                            page.data.param.planId = $("#planId").val();
                        }
                        parent.pageLoadMode = PageLoadMode.Refresh;
                        page.data.unitParam.unitId = $("#unitId").val();
                        page.data.unitParam.unitName = $("#unitId").combo('getText');
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            //提交
            submitData: function() {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                if ($("#alarmChangeItemTable").bootstrapTable("getData").length == 0) {
                    layer.msg("请新增调整事项数据！");
                    return;
                }
                layer.confirm('确定提交吗？', {
                    btn: ['是', '否']
                }, function() {
                    var businessId = "AlarmChangePlanApro-" + page.data.param.planId;
                    var businessName = $("#unitId").textbox().val() + "-报警调整方案审批";
                    $.ajax({
                        url: submitUrl,
                        async: false,
                        dataType: "text",
                        data: {
                            planId: page.data.param.planId,
                            businessId: businessId,
                            businessName: businessName,
                            aproType:2
                        },
                        contentType: "application/json;charset=utf-8",
                        type: 'get',
                        success: function(result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("提交成功!", {
                                    time: 1000
                                }, function() {
                                    parent.pageLoadMode = PageLoadMode.Refresh;
                                    page.logic.closeLayer(true);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function(index) {

                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function() {
                parent.layer.close(index);
            },
            getTime: function() {
                $.ajax({
                    url: getTimeUrl,
                    data: '',
                    dataType: 'json',
                    type: 'get',
                    success: function(result) {
                        alarmChangeItemArr = $.ET.toObjectArr(result);
                        var obj = new Object();
                        $.each(alarmChangeItemArr, function(i, el) {
                            if (i != 4 && i != 5) {
                                el.value = OPAL.util.strToDate(el.value).getTime();
                            }
                        })
                    }
                })
            },
            //审批信息页面
            AlarmChangePlanApro: function() {
                layer.open({
                    type: 2,
                    title: "",
                    closeBtn: false,
                    area: ['99%', '80%'],
                    shadeClose: false,
                    content: 'AlarmChangePlanApro.html?' + Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        rowData = {
                            'planId': $("#planId").val(),
                            "flowCaseId": page.data.param.flowCaseId
                        }
                        iframeWin.page.logic.setData(rowData);
                    },
                    end: function(layero, index) {

                    }
                })
            },
            /**
             * 表单验证
             */
            formValidate: function() {
                $("#AddOrEditModal").validate({
                    //验证commbotree
                    ignore: "",
                    //失去焦点时不验证
                    onfocusout: false,
                    rules: {
                        'unitId': {
                            required: true
                        },
                        'applyReason': {
                            required: true
                        }
                    },
                    messages: {
                        'unitId': {
                            required: "请选择装置！",
                        },
                        'applyReason': {
                            required: "请输入申请原因！",
                        }
                    },
                    errorPlacement: function(error, element) {},
                    highlight: function(element) {},
                    success: function(label) {},
                    showErrors: function(errorMap, errorList) {
                        if (errorList.length > 0) {
                            layer.msg(errorList[0].message);
                        }
                    }
                });
            }
        }
    };
    page.init();
    window.page = page;
});