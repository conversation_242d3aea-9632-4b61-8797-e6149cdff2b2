package com.pcitc.opal.common.bll.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import pcitc.imp.common.ettool.baseresrep.BaseResRep;
import java.io.Serializable;
import java.util.Date;
/*
 * 车间实体
 * 模块编号：pcitc_opal_common_class_WorkShopVO
 * 作    者：xuelei.wang
 * 创建时间：2018/06/26
 * 修改编号：1
 * 描    述：车间实体
 */
public class WorkShopVO extends BaseResRep implements Serializable {
    /**
     * 组织机构编码
     */
    private String orgCode;
    /**
     * 组织机构名称
     */
    private String orgName;
    /**
     * 组织机构简称
     */
    private String orgAlias;
    /**
     * 组织机构类型编码
     */
    private String orgTypeCode;
    /**
     * 组织机构类型名称
     */
    private String orgTypeName;
    /**
     * 父级组织机构编码
     */
    private String parentOrgCode;
    /**
     * 父级组织机构名称
     */
    private String parentOrgName;
    /**
     * 父级组织机构别名
     */
    private String parentOrgAlias;
    /**
     * 业务域编码
     */
    private String bizCode;
    /**
     * 业务域名称
     */
    private String bizName;
    /**
     * 业务域别名
     */
    private String bizAlias;
    /**
     * 扩展标识
     */
    private Integer expendFlag;
    /**
     * 是否启用
     */
    private Integer inUse;
    /**
     * 排序
     */
    private Integer sortNum;
    /**
     * 描述
     */
    private String des;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgAlias() {
        return orgAlias;
    }

    public void setOrgAlias(String orgAlias) {
        this.orgAlias = orgAlias;
    }

    public String getOrgTypeCode() {
        return orgTypeCode;
    }

    public void setOrgTypeCode(String orgTypeCode) {
        this.orgTypeCode = orgTypeCode;
    }

    public String getOrgTypeName() {
        return orgTypeName;
    }

    public void setOrgTypeName(String orgTypeName) {
        this.orgTypeName = orgTypeName;
    }

    public String getParentOrgCode() {
        return parentOrgCode;
    }

    public void setParentOrgCode(String parentOrgCode) {
        this.parentOrgCode = parentOrgCode;
    }

    public String getParentOrgName() {
        return parentOrgName;
    }

    public void setParentOrgName(String parentOrgName) {
        this.parentOrgName = parentOrgName;
    }

    public String getParentOrgAlias() {
        return parentOrgAlias;
    }

    public void setParentOrgAlias(String parentOrgAlias) {
        this.parentOrgAlias = parentOrgAlias;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public String getBizAlias() {
        return bizAlias;
    }

    public void setBizAlias(String bizAlias) {
        this.bizAlias = bizAlias;
    }

    public Integer getExpendFlag() {
        return expendFlag;
    }

    public void setExpendFlag(Integer expendFlag) {
        this.expendFlag = expendFlag;
    }

    public Integer getInUse() {
        return inUse;
    }

    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
