package com.pcitc.opal.common.bll.entity;

import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;

/*
 * 工厂模型关系实体
 * 模块编号：pcitc_opal_bll_class_ShiftAreaRelationEntity
 * 作       者：xuelei.wang
 * 创建时间：2018-09-06
 * 修改编号：1
 * 描       述：工厂模型关系实体
 */
@SuppressWarnings({"serial"})
public class ShiftAreaRelationEntity extends BaseResRep implements Serializable {
    /**
     * 关系编码
     */
    private String relationId;
    /**
     * 轮班域编码
     */
    private String shiftAreaCode;

    /**
     * 工厂模型编码
     */
    private String factoryCode;

    /**
     * 工厂模型类型
     */
    private String factoryType;

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public String getShiftAreaCode() {
        return shiftAreaCode;
    }

    public void setShiftAreaCode(String shiftAreaCode) {
        this.shiftAreaCode = shiftAreaCode;
    }

    public String getFactoryCode() {
        return factoryCode;
    }

    public void setFactoryCode(String factoryCode) {
        this.factoryCode = factoryCode;
    }

    public String getFactoryType() {
        return factoryType;
    }

    public void setFactoryType(String factoryType) {
        this.factoryType = factoryType;
    }
}
