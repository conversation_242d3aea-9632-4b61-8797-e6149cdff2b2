package com.pcitc.opal.pm.dao;

import com.pcitc.opal.pm.pojo.AlarmFlagComp;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * AlarmFlagComp实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmFlagCompRepository
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/30
 * 修改编号：1
 * 描    述：AlarmFlagComp实体的Repository实现   
 */
public interface AlarmFlagCompRepository extends JpaRepository<AlarmFlagComp, Long>, AlarmFlagCompRepositoryCustom {

}
