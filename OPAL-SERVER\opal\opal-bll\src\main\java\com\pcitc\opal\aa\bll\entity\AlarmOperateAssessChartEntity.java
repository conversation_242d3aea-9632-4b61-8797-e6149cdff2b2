package com.pcitc.opal.aa.bll.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/*
 * 报警操作评估图表实体
 * 模块编号：pcitc_opal_bll_class_AlarmOperateAssessChartEntity
 * 作  　者：xuelei.wang
 * 创建时间：2017-10-26
 * 修改编号：1
 * 描    述：报警操作评估图表实体
 */
@SuppressWarnings("serial")
public class AlarmOperateAssessChartEntity implements Serializable{
    /**
     * 名称
     */
    private String name;
    /**
     * 图表X轴信息
     */
    private List<String> xaxis;
    /**
     * 数量信息
     */
    private  List<Long> counts;
    /**
     * 颜色信息
     */
    private  String color;
    /**
     * 图表Tip信息
     */
    private List<String> tip;
    /**
     * 数据信息
     */
    private List<AlarmOperateAssessEntity> list=new ArrayList<>();
    /**
     * 单元[装置]总的数量
     */
    private Long totalCounts;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 装置或者单元的ID
     */
    private String id;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public List<String> getXaxis() {
        return xaxis;
    }

    public void setXaxis(List<String> xaxis) {
        this.xaxis = xaxis;
    }

    public List<Long> getCounts() {
        return counts;
    }

    public void setCounts(List<Long> counts) {
        this.counts = counts;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public List<String> getTip() {
        return tip;
    }

    public void setTip(List<String> tip) {
        this.tip = tip;
    }


    public List<AlarmOperateAssessEntity> getList() {
        return list;
    }

    public void setList(List<AlarmOperateAssessEntity> list) {
        this.list = list;
    }

    public Long getTotalCounts() {
        return totalCounts;
    }

    public void setTotalCounts(Long totalCounts) {
        this.totalCounts = totalCounts;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
