package com.pcitc.opal.pm.bll;

import java.util.List;

import org.springframework.stereotype.Service;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.pm.bll.entity.SystRunParaConfEntity;

/*
 * 系统运行参数配置业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_SystRunParaConfService
 * 作       者：kun.zhao
 * 创建时间：2018/01/22
 * 修改编号：1
 * 描       述：系统运行参数配置业务逻辑层接口 
 */
@Service
public interface SystRunParaConfService {

	/**
	 * 批量更新系统运行参数数据
	 * 
	 * <AUTHOR> 2018-01-22
	 * @param systRunParaConfEntityList 系统运行参数配置实体集合
	 * @return	更新结果提示信息
	 * @throws Exception
	 */
	CommonResult updateSystRunParaConfInBatch(List<SystRunParaConfEntity> systRunParaConfEntityList) throws Exception;

	/**
	 * 加载系统运行参数维护主数据
	 * 
	 * <AUTHOR> 2018-01-22
	 * @param name 名称
	 * @param code 编码
	 * @return	系统运行参数维护实体集合
	 * @throws Exception
	 */
	List<SystRunParaConfEntity> getSystRunParaConf(String name, String code) throws Exception;

	String getParaValueByCode(String code,Integer companyId);
	
}
