package com.pcitc.opal.pm.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.pm.pojo.Factory;

/*
 * Factory实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_FactoryRepository
 * 作       者：kun.zhao
 * 创建时间：2017/12/11
 * 修改编号：1
 * 描       述：Factory实体的Repository实现   
 */
public interface FactoryRepository extends JpaRepository<Factory, Long>, FactoryRepositoryCustom {

}
