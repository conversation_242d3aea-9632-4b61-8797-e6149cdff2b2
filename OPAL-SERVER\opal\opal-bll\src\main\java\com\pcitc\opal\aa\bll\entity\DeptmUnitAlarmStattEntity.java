package com.pcitc.opal.aa.bll.entity;

public class DeptmUnitAlarmStattEntity {
    //装置名称+装置简称+装置标准编码+报警次数+最后报警时间(新加属性)
    /**
     * 装置名称
     */
    private String unitName;
    /**
     * 装置简称
     */
    private String unitSName;
    /**
     * 装置编码
     */
    private String unitCode;
    /**
     * 最后报警时间
     */
    private String alarmTime;
    /**
     * 报警数量
     */
    private Long alarmCount;

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getUnitSName() {
        return unitSName;
    }

    public void setUnitSName(String unitSName) {
        this.unitSName = unitSName;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(String alarmTime) {
        this.alarmTime = alarmTime;
    }

    public Long getAlarmCount() {
        return alarmCount;
    }

    public void setAlarmCount(Long alarmCount) {
        this.alarmCount = alarmCount;
    }
}
