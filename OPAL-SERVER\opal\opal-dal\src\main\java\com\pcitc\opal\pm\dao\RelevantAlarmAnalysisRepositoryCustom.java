package com.pcitc.opal.pm.dao;

import java.util.Date;
import java.util.List;

import com.pcitc.opal.common.CommonEnum.DateTypeEnum;

/*
 * RelevantAlarmAnalysis实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_RelevantAlarmAnalysisRepositoryCustom
 * 作       者：dageng.sun
 * 创建时间：2018/08/03 
 * 修改编号：1
 * 描       述：RelevantAlarmAnalysis实体的Repository的JPA自定义接口 
 */
public interface RelevantAlarmAnalysisRepositoryCustom {

	/**
	 * 相关性报警分析
	 * 
	 * <AUTHOR> 2018-08-03 
	 * @param alarmPointIds 报警点主键id数组
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param dateType 间粒度枚举
	 * @return List<Object[]> 返回Object数组集合
	 */
	List<Object[]> getRelevantProcessAnalysis(Long[] alarmPointIds, Date startTime, Date endTime, DateTypeEnum dateType);
	
	/**
	 * 相关性操作分析
	 * 
	 * <AUTHOR> 2018-08-03 
	 * @param alarmPointIds 报警点主键id数组
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param dateType 间粒度枚举
	 * @return List<Object[]> 返回Object数组集合
	 */
	List<Object[]> getRelevantOperateAnalysis(Long[] alarmPointIds, Date startTime, Date endTime, DateTypeEnum dateType);
		
}
