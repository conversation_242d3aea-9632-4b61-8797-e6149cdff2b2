var searchUrl = OPAL.API.adUrl + '/alarmAnly'; //一级表格
var searchChildUrl = OPAL.API.adUrl + '/alarmAnly/getAlarmAnlyByRec'; //二级表格
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var alarmFlagListUrl = OPAL.API.commUrl + '/getAlarmFlagList';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var getQueryStartAndEndDateUrl = OPAL.API.commUrl + '/getShowTime';
var confirmUrl = OPAL.API.adUrl + '/alarmAnly/updateAlarmAnlyRec'; //确认
var submitUrl = OPAL.API.adUrl + '/alarmAnly/addAlarmAnlyRec'; //提交
var alarmPriorityListUrl = OPAL.API.afUrl + '/alarmDurationStatt/getAlarmPriorityList'; //优先级
var monitorTypeUrl = OPAL.API.commUrl + "/getMonitorTypeList"; //专业
var exportUrl = OPAL.API.adUrl + '/alarmAnly/export'; //导出
var queryTimeArray;
var setStartTime, setNowTime;
var pageMode;
var BusinessType = 1;
var BusinessTypeTitle = '';
//分析状态
var anlyStatusList = [
    {value: '0', text: '未分析'},
    {value: '1', text: '已分析'},
    {value: '2', text: '已提交'},
    {value: '3', text: '已确认'},
    {value: '4', text: '已驳回'},
];
//报警状态
var alarmStatusList = [
    {value: '-9', text: '全部'},
    {value: '0', text: '未关闭'},
    {value: '1', text: '已关闭'},
];
var tableData = [];
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            BusinessType = OPAL.util.getQueryParam('BusinessType');
            //1，分析；2，确认
            if (BusinessType == 1) {
                BusinessTypeTitle = '分析';
                $('#confirmBtn').hide();
                $('#overruleBtn').hide();
                $('#exportBtn').hide();
            } else {
                BusinessTypeTitle = '确认';
                $('#analyseBtn').hide();
            }
            $(".alarm-public-title-dis").html('报警记录' + BusinessTypeTitle);
            this.bindUi();
            //扩展日期插件
            OPAL.util.extendDate();
            //初始化 报警时间的时间点
            page.logic.getQueryTime();
            //初始化查询装置树
            page.logic.initUnitTree();

            page.logic.initPrdtCellComboTree();
            //初始化表格
            page.logic.initTable();
            //初始化报警标识
            page.logic.initAlarmFlagList();
            //分析状态
            page.logic.initAnlyStatusList();

            //初始化优先级
            page.logic.initAlarmPriorityList();
            //初始化 专业
            page.logic.initMonitorType();
            //默认查询数据
            setTimeout(function () {
                page.logic.search();
            }, 500);
        },
        bindUi: function () {
            //查询
            $('#search').click(function () {
                page.logic.search();
            })
            //确认
            $('#confirmBtn').click(function () {
                page.logic.confirmAll();
            })
            //驳回
            $('#overruleBtn').click(function () {
                page.logic.overrule();
            })
            //分析
            $('#analyseBtn').click(function () {
                page.logic.analyseList();
            })
            //分析
            $('#exportBtn').click(function () {
                page.logic.exportExcel();
            })
        },
        data: {
            param: {},
            subParam: {}
        },
        logic: {
            overrule: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections');
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmAnlyRecId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要驳回的数据");
                    return;
                }
                let arr = rowsArray.filter(item => {
                    return item.anlyStatus != 2
                })
                if (arr.length > 0) {
                    layer.msg("只能驳回状态为已提交的数据！");
                    return;
                }
                var obj = {
                    anlyStatus: 4,
                    alarmAnlyRecIds: idsArray
                };
                layer.confirm('是否驳回？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: confirmUrl,
                        async: false,
                        data: obj,
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'get', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            analyseList: function () {
                var idsArray = new Array();
                var monitorType = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections');
                var anlyRows = new Array();
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmRecId);
                    monitorType.push(el.monitorType);
                    anlyRows.push({
                        'alarmRecId' : el.alarmRecId,
                        'unitName': el.unitName,
                        'prdtCellName': el.prdtCellName,
                        'tagDes': el.tagDes,
                        'tag': el.tag,
                        'measUnitName': el.measUnitName,
                        'priorityName': el.priorityName,
                        'monitorTypeName': el.monitorTypeName
                    });
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要分析的数据");
                    return;
                }
                let arr = rowsArray.filter(item => {
                    return item.anlyStatus != 0
                })
                if (arr.length > 0) {
                    layer.msg("只能对未分析数据批量操作！");
                    return;
                }
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: '',
                    area: ['1000px', '400px'],
                    shadeClose: false,
                    content: 'AlarmAnlyList.html?' + Math.random(),
                    success: function (layero, index) {
                        // var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            alarmRecIdList: idsArray,
                            anlyRows: anlyRows,
                            monitorType: monitorType
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.isRefresh) {
                            $('#table').bootstrapTable('selectPage', 1);
                        }
                    }
                })
            },
            exportExcel: function () {
                page.data.param.titles = JSON.stringify(new Array(
                    {'value': "分析状态", 'key': "anlyStatusName"},
                    {'value': "装置", 'key': "unitName"},
                    {'value': "生产单元", 'key': "prdtCellName"},
                    {'value': "报警时间", 'key': "alarmTime"},
                    {'value': "参数名称", 'key': "tagDes"},
                    {'value': "位号", 'key': "tag"},
                    {'value': "报警等级", 'key': "alarmFlagName"},
                    {'value': "优先级", 'key': "priorityName"},
                    {'value': "专业", 'key': "monitorTypeName"},
                    {'value': "单位", 'key': "measUnitName"},
                    {'value': "高高报", 'key': "alarmPointHH"},
                    {'value': "高报", 'key': "alarmPointHI"},
                    {'value': "低报", 'key': "alarmPointLO"},
                    {'value': "低低报", 'key': "alarmPointLL"},
                    {'value': "原因类型", 'key': "reasonTypeName"},
                    {'value': "原因", 'key': "alarmReasonName"},
                    {'value': "原因分析及处置措施", 'key': "reasonDes"},
                    {'value': "创建时间", 'key': "crtTime"},
                    {'value': "创建人", 'key': "crtUserName"},
                    {'value': "维护时间", 'key': "submitTime"},
                    {'value': "维护人", 'key': "submitUserName"}
                ));
                var url = exportUrl + "?" + $.param(page.data.param);
                window.location = url;
            },
            /**
             * 报警制度管理新增或者编辑详细页面
             */
            crendChart: function (index) {
                var row = tableData[index];
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: '',
                    area: ['1000px', '500px'],
                    shadeClose: false,
                    content: 'OpeMonTrendChart.html?' + Math.random(),
                    success: function (layero, index) {
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "alarmRecId": row.alarmRecId
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.isRefresh) {
                            $('#table').bootstrapTable('selectPage', 1);
                        }
                    }
                })
            },

            /**
             * 报警制度管理新增或者编辑详细页面
             */
            detail: function (index, type) {
                var row = tableData[index];
                if (row.alarmAnlyRecId == '') {
                    pageMode = PageModelEnum.NewAdd
                } else {
                    pageMode = PageModelEnum.Edit
                }
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: '',
                    area: ['1000px', '400px'],
                    shadeClose: false,
                    content: 'AlarmAnlyDtl.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "alarmRecId": row.alarmRecId,
                            "monitorType": row.monitorType,
                            'row': JSON.stringify(row),
                            'type': type
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.isRefresh) {
                            $('#table').bootstrapTable('selectPage', 1);
                        }
                    }
                })
            },
            initTable: function () {
                OPAL.ui.initBootstrapTable("table", {
                    uniqueId: 'alarmAnlyRecId',
                    columns: [         {
                        field: 'state', checkbox: true, rowspan: 1, align: 'center', width: '50px'
                    },{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '140px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "分析状态", field: 'anlyStatusName', rowspan: 1, align: 'center', width: '80px'
                    }, {
                        title: "装置", field: 'unitName', rowspan: 1, align: 'left', width: '130px'
                    }, {
                        title: "生产单元", field: 'prdtCellName', rowspan: 1, align: 'left', width: '130px'
                    }, {
                        title: "报警时间", field: 'alarmTime', rowspan: 1, align: 'center', width: '140px'
                    }, {
                        title: "参数名称", field: 'tagDes', rowspan: 1, align: 'center', width: '230px'
                    }, {
                        title: "位号", field: 'tag', rowspan: 1, align: 'center', width: '130px'
                    }, {
                        title: "报警等级", field: 'alarmFlagName', rowspan: 1, align: 'center', width: '80px'
                    }, {
                        title: "优先级", field: 'priorityName', rowspan: 1, align: 'center', width: '60px'
                    }, {
                        title: "专业", field: 'monitorTypeName', rowspan: 1, align: 'center', width: '60px'
                    }, {
                        title: "单位", field: 'measUnitName', rowspan: 1, align: 'center', width: '80px'
                    }, {
                        title: "高高报", field: 'alarmPointHH', rowspan: 1, align: 'center', width: '60px'
                    }, {
                        title: "高报", field: 'alarmPointHI', rowspan: 1, align: 'center', width: '60px'
                    }, {
                        title: "低报", field: 'alarmPointLO', rowspan: 1, align: 'center', width: '60px'
                    }, {
                        title: "低低报", field: 'alarmPointLL', rowspan: 1, align: 'center', width: '60px'
                    }, {
                        field: 'reasonTypeName', title: '原因类型', align: 'left', width: '200px'
                    }, {
                        field: 'name', title: '原因', align: 'left', width: '200px',
                    }, {
                        field: 'reasonDes', title: '原因分析及处置措施', align: 'center', width: '380px',
                    }, {
                        field: 'crtTime', title: '分析时间', align: 'center', width: '130px',
                    }, {
                        field: 'crtUserName', title: '分析人', align: 'center', width: '80px',
                    }, {
                        field: 'confirmTime', title: '确认时间', align: 'center', width: '130px',
                    }, {
                        field: 'confirmUserName', title: '确认人', align: 'center', width: '80px',
                    }],
                    onExpandRow: function (index, row, $detail) {
                         page.data.subParam.alarmRecId = row['alarmRecId'];
                        // page.logic.initCausalSubTable(index, row, $detail);
                    },
                    onLoadSuccess: function (res) {
                        tableData = res.rows;
                        $("#search").attr('disabled', false);
                        //设置鼠标浮动提示
                        var tds = $('#table').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                }, page.logic.queryParams, "search")
            },
            /**
             * 初始化二级列表
             */
            initCausalSubTable: function (index, row, $detail) {
                let subId = 'sub_table' + index;
                $detail.html('<table class="sub_table"></table>').find('table').attr('id', subId);
                OPAL.ui.initBootstrapTable(subId, {
                    url: searchChildUrl,
                    striped: true,
                    pagination: false,
                    columns: [{
                        field: 'reasonTypeName',
                        title: '原因类型',
                        align: 'left',
                        width: '80px'
                    }, {
                        field: 'name',
                        title: '原因',
                        align: 'left',
                        width: '80px',
                    }, {
                        field: 'reasonDes',
                        title: '原因分析及处置措施',
                        align: 'center',
                        width: '280px',
                    }, {
                        field: 'crtTime',
                        title: '分析时间',
                        align: 'center',
                        width: '130px',
                    }, {
                        field: 'crtUserName',
                        title: '分析人',
                        align: 'center',
                        width: '80px',
                    }, {
                        field: 'confirmTime',
                        title: '确认时间',
                        align: 'center',
                        width: '130px',
                    }, {
                        field: 'confirmUserName',
                        title: '确认人',
                        align: 'center',
                        width: '80px',
                    }],
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                }, page.data.subParam)
            },

            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                if (BusinessType == 1) {
                    if (rowData.anlyStatus == 0) {
                        return [
                            '<a  name="TableEditor"  href="javascript:window.page.logic.crendChart(\'' + arguments[2] + '\')">趋势&nbsp;&nbsp;&nbsp;&nbsp;</a>' +
                            '<a  name="TableEditor"  href="javascript:window.page.logic.detail(\'' + arguments[2] + '\',0)">分析&nbsp;&nbsp;&nbsp;&nbsp;</a>' +
                            '<a  name="TableEditor" style="color: #aaa">提交</a>'
                        ]
                    } else if (rowData.anlyStatus == 1) {
                        return [
                            '<a  name="TableEditor"  href="javascript:window.page.logic.crendChart(\'' + arguments[2] + '\')">趋势&nbsp;&nbsp;&nbsp;&nbsp;</a>' +
                            '<a  name="TableEditor"  href="javascript:window.page.logic.detail(\'' + arguments[2] + '\',0)">分析&nbsp;&nbsp;&nbsp;&nbsp;</a>' +
                            '<a  name="TableEditor"  href="javascript:window.page.logic.submitSingle(\'' + rowData.alarmAnlyRecId + '\')">提交</a>'
                        ]
                    } else if (rowData.anlyStatus == 4) {
                        return [
                            '<a  name="TableEditor"  href="javascript:window.page.logic.crendChart(\'' + arguments[2] + '\')">趋势&nbsp;&nbsp;&nbsp;&nbsp;</a>' +
                            '<a  name="TableEditor"  href="javascript:window.page.logic.detail(\'' + arguments[2] + '\',0)">分析&nbsp;&nbsp;&nbsp;&nbsp;</a>' +
                            '<a  name="TableEditor"  href="javascript:window.page.logic.submitSingle(\'' + rowData.alarmAnlyRecId + '\')">提交</a>'
                        ]
                    } else {
                        return [
                            '<a  name="TableEditor"  href="javascript:window.page.logic.crendChart(\'' + arguments[2] + '\')">趋势&nbsp;&nbsp;&nbsp;&nbsp;</a>' +
                            '<a  name="TableEditor" style="color: #aaa">分析&nbsp;&nbsp;&nbsp;&nbsp;</a>' +
                            '<a  name="TableEditor" style="color: #aaa">提交</a>'
                        ]
                    }
                } else if (BusinessType == 2) {
                    if (rowData.anlyStatus == 2) {
                        return [
                            '<a  name="TableEditor"  href="javascript:window.page.logic.crendChart  (\'' + arguments[2] + '\')">趋势&nbsp;&nbsp;&nbsp;&nbsp;</a>' +
                            '<a  name="TableEditor"  href="javascript:window.page.logic.confirmSingle(\'' + rowData.alarmAnlyRecId + '\')">确认&nbsp;&nbsp;&nbsp;&nbsp;</a>'+
                            '<a  name="TableEditor"  href="javascript:window.page.logic.overruleSingle(\'' + rowData.alarmAnlyRecId + '\')">驳回&nbsp;&nbsp;&nbsp;&nbsp;</a>'+
                            '<a  name="TableEditor"  href="javascript:window.page.logic.detail(\'' + arguments[2] + '\',1)">修改</a>'
                        ]
                    } else {
                        return [
                            '<a  name="TableEditor"  href="javascript:window.page.logic.crendChart(\'' + arguments[2] + '\')">趋势&nbsp;&nbsp;&nbsp;&nbsp;</a>' +
                            '<a  name="TableEditor" style="color: #aaa">确认&nbsp;&nbsp;&nbsp;&nbsp;</a>' +
                            '<a  name="TableEditor" style="color: #aaa">驳回&nbsp;&nbsp;&nbsp;&nbsp;</a>' +
                            '<a  name="TableEditor" style="color: #aaa">修改</a>'
                        ]
                    }
                }
            },
            // 多选确认
            confirmAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections');
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmAnlyRecId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要确认的数据");
                    return;
                }
                let arr = rowsArray.filter(item => {
                    return item.anlyStatus != 2
                })
                if (arr.length > 0) {
                    layer.msg("只能确认状态为已提交的数据！");
                    return;
                }
                var obj = {
                    anlyStatus: 3,
                    alarmAnlyRecIds: idsArray
                };
                layer.confirm('是否确认？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: confirmUrl,
                        async: false,
                        data: obj,
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'get', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            overruleSingle:function (id) {
                var obj = {
                    anlyStatus: 4,
                    alarmAnlyRecIds: [id]
                };
                layer.confirm('确定驳回吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: confirmUrl,
                        async: false,
                        data: obj,
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'get', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            // 单选确认
            confirmSingle: function (id) {
                var obj = {
                    anlyStatus: 3,
                    alarmAnlyRecIds: [id]
                };
                layer.confirm('确定确认吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: confirmUrl,
                        async: false,
                        data: obj,
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'get', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            // 提交
            submitSingle: function (id) {
                var obj = {
                    anlyStatus: 2,
                    alarmAnlyRecId: id
                };
                layer.confirm('确定提交吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: submitUrl,
                        async: false,
                        data: obj,
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'get', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },

            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 获得固定的时间点
             */
            getQueryTime: function () {
                OPAL.util.getQueryTime(function (queryTime) {
                    queryTimeArray = queryTime.split(':');
                    // 初始化 开始时间和结束时间
                    page.logic.getQueryStartAndEndDate();
                    // 初始化 时间设置
                    page.logic.initTime();
                });
            },
            /**
             * 初始化 开始时间和结束时间
             */

            getQueryStartAndEndDate: function () {
                $.ajax({
                    url: getQueryStartAndEndDateUrl,
                    async: false,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        setStartTime = dataArr[0].value;
                        setNowTime = dataArr[2].value;
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 设置日期插件
             */
            initTime: function () {
                OPAL.ui.initDateTimePeriodPicker({
                    format: 'yyyy-MM-dd HH:mm:ss',
                    type: 'datetime',
                }, function () {
                });
            },

            /**
             * 搜索
             */
            search: function () {
                if (!OPAL.util.checkDateIsValid()) {
                    return false;
                }
                $("#search").attr('disabled', true);
                page.data.param = OPAL.form.getData("searchForm", true);
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },

            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function (nodes) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    }
                }, false);
            },


            initPrdtCellComboTree: function () {
                OPAL.ui.defaultComboTree('prdtCellIds');
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function (unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化查询 报警标识
             */
            initAlarmFlagList: function () {
                OPAL.ui.getCombobox("alarmFlagId", alarmFlagListUrl, {
                    selectValue: '-1',
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            /**
             * 初始化查询 分析状态
             */
            initAnlyStatusList: function () {
                var str = '';
                var selectValue = '';
                if (BusinessType == 1) {
                    //'分析'默认显示“未分析”
                    selectValue = '0';
                } else {
                    //'确认'默认显示“已提交”
                    selectValue = '2';
                }
                $.each(anlyStatusList, function (i, el) {
                    str += '<option value="' + el.value + '">' + el.text + '</option>';
                });
                $('#anlyStatus').html(str);
                //设置默认值
                $('#anlyStatus').val(selectValue);
            },
            /**
             * 初始化查询 优先级
             */
            initAlarmPriorityList: function () {
                OPAL.ui.getComboMultipleSelect('prioritys', alarmPriorityListUrl, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#prioritys").combotree('tree');
                    $("#prioritys").combotree("setValues", []);
                    var roots = treeView.tree('getRoots');
                    for (var i = 0; i < roots.length; i++) {
                        treeView.tree('check', roots[i].target);
                    }
                });
            },
            initMonitorType: function () {
                OPAL.ui.getComboMultipleSelect('monitorType', monitorTypeUrl, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#monitorType").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    var roots = treeView.tree('getRoots');
                    for (var i = 0; i < roots.length; i++) {
                        treeView.tree('check', roots[i].target);
                    }
                });
            }

            /**
             * 设置参数
             */
            // setData: function () {
            //     page.data.param = OPAL.form.getData('searchForm');
            // },

        }
    };
    page.init();
    window.page = page;
});


