package com.pcitc.opal.oracle.dialect;

import org.hibernate.Hibernate;
import org.hibernate.dialect.MySQL57Dialect;
import org.hibernate.dialect.MySQL5Dialect;
import org.hibernate.dialect.MySQL8Dialect;
import org.hibernate.dialect.function.SQLFunctionTemplate;
import org.hibernate.type.StandardBasicTypes;

import java.sql.Types;

public class CustomMysqlDialect extends MySQL8Dialect {
    public CustomMysqlDialect(){
        super();
        this.registerFunction("F_OPAL_GETTIMESEGMENT",
                new SQLFunctionTemplate(StandardBasicTypes.STRING, "F_OPAL_GETTIMESEGMENT(?1,?2,?3,?4)"));
        this.registerFunction("F_OPAL_INCRAFTRANGE",
                new SQLFunctionTemplate(StandardBasicTypes.STRING, "F_OPAL_INCRAFTRANGE(?1,?2,?3,?4,?5)"));
        this.registerFunction("F_OPAL_INTIMERANGE",
                new SQLFunctionTemplate(StandardBasicTypes.NUMERIC_BOOLEAN, "F_OPAL_INTIMERANGE(?1,?2)"));
        this.registerFunction("F_OPAL_ISNUMERIC",
                new SQLFunctionTemplate(StandardBasicTypes.NUMERIC_BOOLEAN, "F_OPAL_ISNUMERIC(?1)"));
        this.registerFunction("F_OPAL_COMPARE_NUMERIC",
                new SQLFunctionTemplate(StandardBasicTypes.NUMERIC_BOOLEAN, "F_OPAL_COMPARE_NUMERIC(?1,?2,?3)"));
        this.registerFunction("F_OPAL_DATESUB",
                new SQLFunctionTemplate(StandardBasicTypes.DATE,"date_sub(?1, INTERVAL ?2 ?3)"));
        this.registerFunction("F_OPAL_NEXTDAY",
                new SQLFunctionTemplate(StandardBasicTypes.DATE,"F_OPAL_NEXTDAY(?1,?2)"));
        // 注册JPA未识别类型
        registerHibernateType(Types.NCHAR, StandardBasicTypes.CHARACTER.getName());
        registerHibernateType(Types.NCHAR, 1, StandardBasicTypes.CHARACTER.getName());
        registerHibernateType(Types.NCHAR, 255, StandardBasicTypes.STRING.getName());
        registerHibernateType(Types.NVARCHAR, StandardBasicTypes.STRING.getName());
        registerHibernateType(Types.LONGNVARCHAR, StandardBasicTypes.TEXT.getName());
        registerHibernateType(Types.NCLOB, StandardBasicTypes.CLOB.getName());
        registerHibernateType(Types.JAVA_OBJECT, "T_OPAL_DATE_OBJECT");
    }
}
