package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.AlarmEventTypeComp;
import com.pcitc.opal.pm.pojo.AlarmPointTagComp;

import java.util.List;

/*
 * AlarmEventTypeComp实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmEventTypeCompRepositoryCustom
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：AlarmEventTypeComp实体的Repository的JPA自定义接口 
 */
public interface AlarmPointTagCompRepositoryCustom {

    public List<AlarmPointTagComp> getAlarmPointTagCompEntitys(Long comId);

    public List<Long> distinctByComId();
}
