var alarmPointDelUrl = OPAL.API.pmUrl + '/alarmPointDelConfig/getAlarmPointDels'; //一级列表
var getShowTimeUrl = OPAL.API.commUrl + '/getShowTime';
var delUrl = OPAL.API.pmUrl + '/alarmPointDelConfig/delete';
var inUseUrl = OPAL.API.commUrl + "/getInUse";
var getQueryStartAndEndDateUrl = OPAL.API.commUrl + '/getShowTime';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";   //装置树
var passUrl = OPAL.API.pmUrl + '/alarmPointDelConfig/approve'; //审批通过
var rejectUrl = OPAL.API.pmUrl + '/alarmPointDelConfig/reject'; //驳回
var isLoading = true;
var unitId;
var unit;
var alarmFlagId;
var tag;
var priority;
var setStartTime;
var setNowTime;
var queryTimeArray = new Array();
var urlParam = '';
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            // //初始化查询装置树
            page.logic.initUnitTree();
			//初始化查询是否启用
            page.logic.initInUse();
            // 初始化 开始时间和结束时间
            page.logic.getQueryStartAndEndDate();
            // 初始化时间
            page.logic.initTime();
            /**
             *绑定事件
             */
            this.bindUi();
            //初始化表格
            page.logic.initOpetateTable();
            urlParam = OPAL.util.getQueryParam('businessType');
            // 1剔除 2审批
            if(urlParam == 1) {
                $(".page-title").html('报警剔除配置');
                $("#delStatus").val(-1);
                $("#AlarmGroupAdd").show();
                $("#AlarmGroupDel").show();
                $("#AlarmGroupPass").hide();
                $("#AlarmGroupReject").hide();
            } else {
                $(".page-title").html('报警剔除审批');
                $("#delStatus").val(1);
                $("#AlarmGroupAdd").hide();
                $("#AlarmGroupDel").hide();
                $("#AlarmGroupPass").show();
                $("#AlarmGroupReject").show();
            }
            // 查询
            page.logic.search();
        },
        bindUi: function () {
            // 新增
            $('#AlarmGroupAdd').click(function() {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            });
            //批量删除
            $('#AlarmGroupDel').click(function() {
                page.logic.delAll();
            });
            // 通过
            $('#AlarmGroupPass').click(function() {
                page.logic.pass();
            });
            //驳回
            $('#AlarmGroupReject').click(function() {
                page.logic.reject();
            });
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                $('#MostAlarmOperateTable').bootstrapTable('resetView');
            };
            /**
             * 查询
             */
            $('#btnSearch').click(function () {
                isLoading = false;
                page.logic.search();
            })
        },
        data: {
            param: {},
        },

        logic: {
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                }, false, function() {
                });
            },
            /**
             * 设置日期插件
             */
             initTime: function() {
                start = laydate.render({
                    elem: '#startTime', //指定元素
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd HH:mm:ss', //日期格式
                    value: setStartTime,
                    // max: setNowTime //最大日期
                    
                });
                end = laydate.render({
                    elem: '#endTime',
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear','confirm'],
                    format: 'yyyy-MM-dd HH:mm:ss',
                    value: setNowTime,
                    // max: setNowTime
                    
                });
            },
            // 初始化 开始时间和结束时间
            getQueryStartAndEndDate: function() {
                $.ajax({
                    url: getQueryStartAndEndDateUrl,
                    async: false,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function(result) {
                        var dataArr = $.ET.toObjectArr(result);
                        let nowYear = dataArr[2].value.split('-')[0];
                        let nowMonth = Number(new Date(dataArr[2].value).getMonth()) + 1
                        setStartTime = nowYear + '-' + nowMonth + '-01 00:00:00';
                        setNowTime = dataArr[2].value;
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /***
             * 查询
             */
            search: function () {
                page.data.param = OPAL.form.getData("searchForm");
                // page.data.param.startTime = OPAL.util.strToDate('2022-01-01');
                // $("#btnSearch").prop('disabled', true);
                page.logic.queryMostOperate();
            },
            /**
             * 加载 下方表格
             */
            queryMostOperate: function () {
                $("#MostAlarmOperateTable").bootstrapTable('refresh', {
                    "url": alarmPointDelUrl,
                    "pageNumber":1
                });
            },
            //初始化下方表格
            initOpetateTable: function () {
                page.logic.initBootstrapTable("MostAlarmOperateTable",{
                    // detailView: true,
                    cache:false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '90px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        field: 'unitName',
                        title: '装置',
                        align: 'left',
                    }, {
                        field: 'groupName',
                        title: '报警点分组名称',
                        align: 'left',
                        width: '130px',
                        formatter: page.logic.onActionOpen
                    }, {
                        field: 'delStartTime',
                        title: '剔除开始时间',
                        align: 'left',
                    },  {
                        field: 'delEndTime',
                        title: '剔除结束时间',
                        align: 'left',
                    }, {
                        field: 'delStatusShow',
                        title: '剔除状态',
                        align: 'center',
                    }, {
                        field: 'delDataStatusShow',
                        title: '数据剔除状态',
                        align: 'center',
                    }, {
                        title: "是否启用",
                        field: 'inUse',
                        rowspan: 1,
                        align: 'center',
                        width: '70px',
                        formatter: function(value, row, index) {
                            let str = row.inUse == 1 ? '是' : '否';
                            return '<div>'+ str +'</div>'
                        }
                    }, {
                        title: "创建时间",
                        field: 'crtDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "创建人",
                        field: 'crtUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "维护时间",
                        field: 'mntDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "维护人",
                        field: 'mntUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "描述",
                        field: 'des',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }],
                },page.logic.queryParams);
                var tableOption = $('#MostAlarmOperateTable').bootstrapTable('getOptions');
                $("#MostAlarmOperateTable").bootstrapTable('refreshOptions', tableOption);
                
                $('#MostAlarmOperateTable').colResizable({
                    liveDrag: true
                });
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
             onActionRenderer: function () {
                var rowData = arguments[1];
                if (urlParam == 1) {
                    if (rowData.delStatus == 0 || rowData.delStatus == 3) {
                        return [
                            '<a name="TableEditor" href="javascript:window.page.logic.edit(\'' + rowData.alarmPointDelConfigId + '\')">编辑</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            '<a name="TableDelete" href="javascript:window.page.logic.delSingle(\'' + rowData.alarmPointDelConfigId + '\')" >删除</a> '
                        ]
                    } else {
                        return [
                            '<a name="TableEditor" style="color: #999;">编辑</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            '<a name="TableDelete" style="color: #999;">删除</a> '
                        ]
                    }
                } else if(urlParam == 2) {
                    if (rowData.delStatus == 1) {
                        return [
                            '<a name="TableEditor" href="javascript:window.page.logic.passSingle(\'' + rowData.alarmPointDelConfigId + '\')">通过</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            '<a name="TableDelete" href="javascript:window.page.logic.rejectSingle(\'' + rowData.alarmPointDelConfigId + '\')" >驳回</a> '
                        ]
                    } else {
                        return [
                            '<a name="TableEditor" style="color: #999;">通过</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            '<a name="TableDelete" style="color: #999;">驳回</a> '
                        ]
                    }
                }
            },
            /**
             * 添加配置报警点分组
             * @returns {*[]}
             */
            onActionOpen: function () {
                var rowData = arguments[1];
                return [
                    '<a name="TableEditor" href="javascript:window.page.logic.alarmPointDetail(\'' + rowData.alarmPointGroupId + '\')">'+rowData.groupName+'</a>'
                ]
            },
            //打开报警点分组子页面
            alarmPointDetail: function(ID) {
                layer.open({
                    type: 2,
                    title: '报警点配置',
                    closeBtn: 1,
                    area: ['1000px', '90%'],
                    shadeClose: false,
                    offset: '30px',
                    content: '../AlarmPointGroupConfig/AlarmPointGroupConfigDtl.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var setData = {
                            "pageMode": PageModelEnum.View,
                            "alarmPointGroupId": ID,
                            'title': '报警点分组配置详情'
                        };
                        iframeWin.page.logic.setData(setData);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            $("#tbEventType").bootstrapTable('refresh', {
                                "url": searchUrl,
                                "pageNumber": 1
                            });
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#tbEventType').bootstrapTable('selectPage', 1);
                        }
                    }
                })
            },
            queryParams:function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param,param);
            },

            initBootstrapTable:function (tableID, config, queryParams) {
                var _config = {
                    method: 'get',
                    url: '',
                    cache: false,
                    pagination: true, //启动分页
                    pageSize: 10,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "server", // 表示服务端请求 后台分页
                    //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder
                    //设置为limit可以获取limit, offset, search, sort, order
                    queryParamsType: "undefined",
                    queryParams: queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#' + tableID).find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                };
                $.extend(true, _config, config);
                $('#' + tableID).bootstrapTable(_config);
            },
            // 批量通过
            pass: function() {
                var idsArray = new Array();
                var rowsArray = $("#MostAlarmOperateTable").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmPointDelConfigId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要审批通过的数据！");
                    return;
                }
                let arr = rowsArray.filter(item => {return item.delStatus != 1})
                if (arr.length > 0) {
                    layer.msg("只能审批状态为已提交的数据！");
                    return;
                }
                layer.confirm('确定审批通过吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: passUrl,
                        async: false,
                        data: {alarmPointDelConfigId: idsArray},
                        dataType: "text",
                        // contentType: "application/json;charset=utf-8",
                        type: 'POST', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("审批通过成功！", {
                                    time: 1000
                                }, function() {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            // 通过单条
            passSingle: function(id) {
                let arr = [id];
                layer.confirm('确定审批通过吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: passUrl,
                        async: false,
                        data: {alarmPointDelConfigId: arr},
                        dataType: "text",
                        type: 'POST', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("审批通过成功！", {
                                    time: 1000
                                }, function() {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            // 批量驳回
            reject: function() {
                var idsArray = new Array();
                var rowsArray = $("#MostAlarmOperateTable").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmPointDelConfigId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要驳回的数据！");
                    return;
                }
                let arr = rowsArray.filter(item => {return item.delStatus != 1})
                if (arr.length > 0) {
                    layer.msg("只能驳回状态为已提交的数据！");
                    return;
                }
                layer.confirm('确定驳回吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: rejectUrl,
                        async: false,
                        data: {alarmPointDelConfigId: idsArray},
                        dataType: "text",
                        // contentType: "application/json;charset=utf-8",
                        type: 'POST', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("驳回成功！", {
                                    time: 1000
                                }, function() {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            // 驳回单条
            rejectSingle: function(id) {
                let arr = [id];
                layer.confirm('确定驳回吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: rejectUrl,
                        async: false,
                        data: {alarmPointDelConfigId: arr},
                        dataType: "text",
                        // contentType: "application/json;charset=utf-8",
                        type: 'POST', //PUT DELETE POST
                        success: function (result) {
                            debugger
                            if (result.indexOf('collection') < 0) {
                                layer.msg("驳回成功！", {
                                    time: 1000
                                }, function() {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            debugger
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 批量删除
             */
             delAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#MostAlarmOperateTable").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmPointDelConfigId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据！");
                    return;
                }
                let arr = rowsArray.filter(item => {return item.delStatus != 0 && item.delStatus != 3})
                if (arr.length > 0) {
                    layer.msg("只能删除状态为未提交和已驳回的数据！");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function() {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function (id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false, //
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function() {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 新增
             */
            add: function () {
                var pageMode = PageModelEnum.NewAdd;
                var title = "报警点剔除配置新增";
                page.logic.detail(title, "", pageMode);
            },
            /**
             * 编辑
             * @param alarmPointDelConfigId
             */
            edit: function (alarmPointDelConfigId) {
                var pageMode = PageModelEnum.Edit;
                var title = "报警点剔除配置编辑";
                page.logic.detail(title, alarmPointDelConfigId, pageMode);
            },
            /**
             * 装置新增或者编辑详细页面
             */
            detail: function (title, alarmPointDelConfigId, pageMode) {
                layer.open({
                    type: 2,
                    title: title,
                    closeBtn: 1,
                    area: ['1100px', '500px'],
                    shadeClose: false,
                    content: 'AlarmPointDelConfigDtl.html?'+ Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "alarmPointDelConfigId": alarmPointDelConfigId,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                    	if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            
			/**
             * 初始化查询inUse
             */
             initInUse: function() {
                OPAL.ui.getCombobox("inUse", inUseUrl, {
                    selectValue: 1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
        }
    };
    page.init();
    window.page = page;
});