package com.pcitc.opal.af.bll.entity;

/*
 * 工艺接口 --报警次数统计统计数据实体
 * 作  　  者：shufei.sui
 * 创建时间：2019/11/07
 * 修改编号：1
 * 描       述：报警次数统计统计数据实体
 */
public class AlarmNumStattDataEntity {

    /**
     * 装置标准编码
     */
    private String unitCode;
    /**
     * 装置简称
     */
    private String name;
    /**
     * 优先级key
     */
    private Integer priority;
    /**
     * 优先级Value
     */
    private String priorityName;
    /**
     * 报警次数
     */
    private Long AlarmQuantity;

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getPriorityName() {
        return priorityName;
    }

    public void setPriorityName(String priorityName) {
        this.priorityName = priorityName;
    }

    public Long getAlarmQuantity() {
        return AlarmQuantity;
    }

    public void setAlarmQuantity(Long alarmQuantity) {
        AlarmQuantity = alarmQuantity;
    }
}
