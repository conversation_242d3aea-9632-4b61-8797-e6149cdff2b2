package com.pcitc.opal.ac.dao;

import java.util.Date;
import java.util.List;

import com.pcitc.opal.ac.pojo.AlarmChangePlan;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

/*
 * 报警变更方案实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_AlarmChangePlanRepositoryCustom
 * 作       者：xuelei.wang
 * 创建时间：2018/1/19
 * 修改编号：1
 * 描       述：报警变更方案实体的Repository的JPA自定义接口
 */
public interface AlarmChangePlanRepositoryCustom {
    /**
     * 获取当天最大的编号
     * @return
     * <AUTHOR> 2018-1-22
     */
    String getMaxPlanCode();
	/**
	 * 校验数据
	 *
	 * <AUTHOR> 2018-01-26
	 * @param po  实体
	 * @return    返回结果信息类
	 */
    CommonResult validate(AlarmChangePlan po);

    /**
     * 新增
     * @return
     * <AUTHOR> 2018-1-22
     */
    CommonResult add(AlarmChangePlan po);

	
    /**
	 * 删除报警变更方案实体
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param anlyAlarmChangePlanIds 报警变更方案Id数组
	 * @return 返回结果信息类
	 */
	CommonResult deleteAlarmChangePlan(Long[] anlyAlarmChangePlanIds);

	/**
	 * 根据报警变更方案维护ID获取单条数据信息
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangePlanId 报警变更方案Id
	 * @return 报警变更方案实体数据
	 * @throws Exception
	 */
	AlarmChangePlan getSingleAlarmChangePlan(Long alarmChangePlanId);
	
	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangePlanIds 报警变更方案Id数组
	 * @return 报警变更方案实体集合
	 */
	List<AlarmChangePlan> getAlarmChangePlan(Long[] alarmChangePlanIds);
	
	/**
	 * 加载报警变更方案维护主数据
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param unitCodes   装置编码数组
	 * @param startTime 申请时间范围开始
	 * @param endTime   申请时间范围结束
	 * @param statuses  状态数组
	 * @param page	        分页实体
	 * @return 报警变更实体数据
	 */
	PaginationBean<AlarmChangePlan> getAlarmChangePlan(String[] unitCodes, Date startTime, Date endTime, Integer[] statuses,Integer businessType, Pagination page);

	/**
	 * 加载报警变更方案数据
	 *
	 * <AUTHOR> 2018-03-14
	 * @param unitCodes   装置编码数组
	 * @param startTime 申请时间范围开始
	 * @param endTime   申请时间范围结束
	 * @param instanceIds   流程实例id集合
	 * @param page	        分页实体
	 * @return 报警变更实体数据
	 */
	PaginationBean<AlarmChangePlan> getAlarmChangePlan(String[] unitCodes, Date startTime, Date endTime,List<String> instanceIds, Pagination page);

	/**
	 * 更新
	 * @return
	 * <AUTHOR> 2018-1-22
	 */
	CommonResult update(AlarmChangePlan po);
}
