package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.AlarmPointGroupService;
import com.pcitc.opal.pm.bll.entity.AlarmPointGroupConfigEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointGroupEntity;
import com.pcitc.opal.pm.dao.AlarmPointGroupDtlRepository;
import com.pcitc.opal.pm.dao.AlarmPointGroupRepository;
import com.pcitc.opal.pm.dao.imp.AlarmPointGroupConfig;
import com.pcitc.opal.pm.pojo.AlarmPointGroup;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/*
 * 报警点分组逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmPointGroupImpl
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点分组逻辑层实现类
 */
@Service
@Component
public class AlarmPointGroupImpl implements AlarmPointGroupService {

    @Resource
    AlarmPointGroupRepository alarmPointGroupRepository;
    @Resource
    AlarmPointGroupDtlRepository alarmPointGroupDtlRepository;
    @Resource
    BasicDataService basicDataService;


    @Override
    public CommonResult addAlarmPointGroup(AlarmPointGroupEntity alarmPointGroupEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmPointGroup alarmPointGroup = ObjectConverter.entityConverter(alarmPointGroupEntity, AlarmPointGroup.class);
        return alarmPointGroupRepository.addAlarmPointGroupDtl(alarmPointGroup);
    }

    @Override
    public CommonResult deleteAlarmPointGroup(Long[] alarmPointGroupIds) throws Exception {
        List<Long> ids = Arrays.asList(alarmPointGroupIds);
        CommonResult commonResult = new CommonResult();
        try {
            ids.forEach(i->{
                List<Long> dIdList = alarmPointGroupDtlRepository.getAlarmPointGroupDtlList(i);
                if(dIdList.size()>0) {
                    alarmPointGroupDtlRepository.deleteAlarmPointGroupDtl(dIdList);
                }
            });
            commonResult = alarmPointGroupRepository.deleteAlarmPointGroup(alarmPointGroupIds);

        }catch (Exception e){
            commonResult.setMessage("要删除的分组已经配置剔除，无法删除");
        }

        return commonResult;
    }

    @Override
    public CommonResult updateAlarmPointGroup(AlarmPointGroupEntity alarmPointGroupEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmPointGroup alarmPointGroup = ObjectConverter.entityConverter(alarmPointGroupEntity, AlarmPointGroup.class);
        //企业
        CommonProperty commonProperty = new CommonProperty();
        alarmPointGroup.setCompanyId(Long.valueOf(commonProperty.getCompanyId()));
         return alarmPointGroupRepository.updateAlarmPointGroupDtl(alarmPointGroup);
    }

    @Override
    public AlarmPointGroupConfigEntity getSingleAlarmPointGroup(Long alarmPointGroupId) throws Exception {
        AlarmPointGroupConfig alarmPointGroupConfig = alarmPointGroupRepository.getSingleAlarmPointGroupConfig(alarmPointGroupId);
        return ObjectConverter.entityConverter(alarmPointGroupConfig, AlarmPointGroupConfigEntity.class);
    }

    @Override
    public PaginationBean<AlarmPointGroupConfigEntity> getAlarmPointGroups(String unitCodes[], String groupName,Long companyId, Pagination page) throws Exception {

        //根据权限获取装置
        unitCodes = basicDataService.getUnitListByIds(unitCodes, true).stream().map(UnitEntity::getStdCode).toArray(String[]::new);


        PaginationBean<AlarmPointGroupConfig> alarmPointGroupConfigList = alarmPointGroupRepository.getAlarmPointGroupsPage(unitCodes,groupName,companyId,page);
        PaginationBean<AlarmPointGroupConfigEntity> returnAlarmPoint = new PaginationBean<AlarmPointGroupConfigEntity>(page,(alarmPointGroupConfigList.getTotal()));
        returnAlarmPoint.setPageList(ObjectConverter.listConverter(alarmPointGroupConfigList.getPageList(), AlarmPointGroupConfigEntity.class));
        return returnAlarmPoint;
    }

    @Override
    public List<AlarmPointGroup> getAlarmPointGroupsByUnit(String unitCode) throws Exception {
        return alarmPointGroupRepository.getAlarmPointGroupsByUnit(unitCode);
    }


}
