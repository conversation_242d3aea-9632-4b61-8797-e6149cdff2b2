package com.pcitc.opal.af.bll.entity;

import java.util.Date;

/*
 * 报警分析-操作详情网格列数据实体
 * 模块编号：pcitc_opal_bll_class_OperateDetailGridEntity
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/30
 * 修改编号：1
 * 描    述：报警分析-操作详情网格列数据实体
 */
public class OperateDetailGridEntity {

    /**
     *报警时间
     */
    private Date alarmTime;

    /**
     *优先级(1紧急；2重要；3一般)
     */
    private Integer priority;

    /**
     *优先级名称
     */
    private String priorityName;

    /**
     *先前值
     */
    private Double previousValue;

    /**
     *值
     */
    private Double nowValue;

    /**
     * 事件类型ID
     */
    private Long eventTypeId;

    /**
     * 事件类型名称
     */
    private String eventTypeName;

    /**
     *操作人
     */
    private String operator;

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getPriorityName() {
        return priorityName;
    }

    public void setPriorityName(String priorityName) {
        this.priorityName = priorityName;
    }

    public Double getPreviousValue() {
        return previousValue;
    }

    public void setPreviousValue(Double previousValue) {
        this.previousValue = previousValue;
    }

    public Double getNowValue() {
        return nowValue;
    }

    public void setNowValue(Double nowValue) {
        this.nowValue = nowValue;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public String getEventTypeName() {
        return eventTypeName;
    }

    public void setEventTypeName(String eventTypeName) {
        this.eventTypeName = eventTypeName;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
