var pmUrl = OPAL.API.pmUrl;
var addUrl = pmUrl + '/alarmEventTypeComp';
var getSingleUrl = pmUrl + '/alarmEventTypeComp';
var dcsCodeUrl = OPAL.API.commUrl + "/getDcsCodeList";
var eventTypeUrl = OPAL.API.commUrl + '/getEventTypeList';

var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.Refresh;
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
            page.logic.initDcsCode();
            page.logic.initEventType();
        },
        bindUI: function () {
            $('#saveAddModal').click(function () {
                page.logic.save();
            });
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
            $('#closePage').click(function () {
                page.logic.closeLayer(false);
            })
        },
        logic: {
            /**
             * 保存
             */
            save: function () {
                $('#dcsCodeId').next('.textbox').find('input').attr('name', 'dcsCodeId');
                $('#dcsCodeId').next('.textbox').addClass('form-control-tree');
                $('#eventTypeId').next('.textbox').find('input').attr('name', 'eventTypeId');
                $('#eventTypeId').next('.textbox').addClass('form-control-tree');
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data=OPAL.form.getETCollectionData("AddOrEditModal");
                //处理提交类型
                var ajaxType = "POST";
                if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                }

                $.ajax({
                    url: addUrl,
                    async: false,
                    type: ajaxType,
                    data: JSON.stringify(data),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！",{
                                time: 1000
                            },function() {
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                $("#pageTitle").text(data.title);
                if (pageMode == PageModelEnum.NewAdd) {
                    $('#inUse').attr('disabled', 'disabled');
                    return;
                }
                $.ajax({
                    url: getSingleUrl + "/" + data.alarmEventTypeCompId + "?now=" + Math.random(),
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function (data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        OPAL.form.setData('AddOrEditModal', entity);
                    },
                    complete: function (XMLHttpRequest, textStatus) {

                    },
                    error: function (XMLHttpRequest, textStatus) {

                    }
                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                parent.layer.close(index);
            },
            /**
             * 表单校验
             */
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal',{
                    rules: {
                        dcsCodeId: {
                            required: true
                        },
                        eventTypeSource:{
                            required: true,
                            rangelength: [0, 100]
                        },
                        eventNameSource: {
                            required: false,
                            rangelength: [0, 100]
                        },
                        eventTypeId: {
                            required: true
                        }
                    }
                });
            },
            /**
             * 初始化查询DcsCode
             */
            initDcsCode: function () {
                OPAL.ui.getCombobox("dcsCodeId", dcsCodeUrl, {
                    keyField: "dcsCodeId",
                    valueField: "name",
                    data: {
                        isAll: false
                    },
                    selectFirstRecord:true
                }, null);
            },
            /**
             * 初始化事件类型
             */
            initEventType: function () {
                OPAL.ui.getEasyUIComboTreeSelect('eventTypeId', eventTypeUrl, 'eventTypeId', 'parentId', 'name', {
                    multiple: false,
                    onlyLeafCheck: false,
                    showParentNodeText: true,
                    clickParentClosePanel: true,
                }, false);
            },
        }

    }
    page.init();
    window.page = page;
})