package com.pcitc.opal.pm.dao.imp;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.TypedQuery;

import com.pcitc.opal.common.CommonProperty;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.MeasUnitRepositoryCustom;
import com.pcitc.opal.pm.pojo.MeasUnit;

/*
 * MeasUnit实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_MeasUnitRepositoryImpl
 * 作    者：jiangtao.xue
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描    述：MeasUnit实体的Repository实现
 */
public class MeasUnitRepositoryImpl extends BaseRepository<MeasUnit, Long> implements MeasUnitRepositoryCustom {

    /**
     * 唯一性校验
     *
     * @param measUnitEntity 计量单位实体
     * @return 查询返回信息类
     * <AUTHOR> 2017-09-25
     */
    @Override
    public CommonResult measUnitValidation(MeasUnit measUnitEntity) {
        CommonResult commonResult = new CommonResult();
        //企业
        CommonProperty commonProperty = new CommonProperty();

        try {
            // “名称”唯一性校验，提示“此名称已存在！”；
            StringBuilder hql = new StringBuilder(
                    "from MeasUnit t where t.name =:name and t.measUnitId<>:measUnitId");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("name", measUnitEntity.getName());
            paramList.put("measUnitId", measUnitEntity.getMeasUnitId() == null ? 0 : measUnitEntity.getMeasUnitId());

            long index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("此名称已存在！");
            }

            // “符号”唯一性校验，提示“此符号已存在！”。
            hql = new StringBuilder(
                    "from MeasUnit t where t.sign =:sign and t.measUnitId<>:measUnitId");
            paramList = new HashMap<String, Object>();
            paramList.put("sign", measUnitEntity.getSign());
            paramList.put("measUnitId", measUnitEntity.getMeasUnitId() == null ? 0 : measUnitEntity.getMeasUnitId());
            index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("此符号已存在！");
            }

            //查询该计量单位ID在<报警点>“计量单位ID”中是否存在，如果存在并且“是否启用”为“0”，则提示“该计量单位在报警点表中已使用，须为已启用！”
            hql = new StringBuilder(
                    "from AlarmPoint t where t.measunitId =:measUnitId and t.companyId=:companyId");
            paramList = new HashMap<String, Object>();
            paramList.put("measUnitId", measUnitEntity.getMeasUnitId() == null ? 0 : measUnitEntity.getMeasUnitId());
            paramList.put("companyId",commonProperty.getCompanyId());
            index = this.getCount(hql.toString(), paramList);
            if (index > 0 && measUnitEntity.getInUse() ==0) {
                throw new Exception("该计量单位在报警点表中已使用，须为已启用！");
            }
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    /**
     * 新增计量单位
     *
     * @param measUnitEntity 添加的实体
     * <AUTHOR> 2017-09-25
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addMeasUnit(MeasUnit measUnitEntity) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(measUnitEntity);
            commonResult.setResult(measUnitEntity);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 删除计量单位
     *
     * @param measUnitIds 计量单位ID集合
     * @return 消息结果类
     * <AUTHOR> 2017-09-25
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult deleteMeasUnit(Long[] measUnitIds) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            String hql = " from MeasUnit t where t.measUnitId in (:measUnitIds)";
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> measUnitIdsList = Arrays.asList(measUnitIds);
            paramList.put("measUnitIds", measUnitIdsList);

            TypedQuery<MeasUnit> query = getEntityManager().createQuery(hql, MeasUnit.class);
            this.setParameterList(query, paramList);
            List<MeasUnit> measUnitList = query.getResultList();
            measUnitList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 更新计量单位
     *
     * @param measUnitEntity 计量单位实体
     * @return 消息结果类
     * <AUTHOR> 2017-09-25
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult updateMeasUnit(MeasUnit measUnitEntity) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(measUnitEntity);
            commonResult.setResult(measUnitEntity);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 获取计量单位实体
     *
     * @param measUnitId 计量单位ID
     * @return 计量单位实体
     * <AUTHOR>  2017-09-25
     */
    @Override
    public MeasUnit getSingleMeasUnit(Long measUnitId) {
        try {
            return getEntityManager().find(MeasUnit.class, measUnitId);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取计量单位实体
     *
     * @param measUnitIds 计量单位ID集合
     * @return 计量单位实体集合
     * <AUTHOR> 2017-09-25
     */
    @Override
    public List<MeasUnit> getMeasUnit(Long[] measUnitIds) {
        try {
            // 查询字符串
            String hql = "from MeasUnit t ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (measUnitIds.length > 0) {
                hql += " where t.measUnitId in (:measUnitIds)";
                List<Long> measUnitIdsList = Arrays.asList(measUnitIds);
                paramList.put("measUnitIds", measUnitIdsList);
            }
            TypedQuery<MeasUnit> query = getEntityManager().createQuery(hql, MeasUnit.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取计量单位实体（分页）
     *
     * @param name  名称
     * @param sign  符号
     * @param inUse 是否启用
     * @param page  分页参数
     * @return 计量单位实体（分页）
     * <AUTHOR> 2017-09-25
     */
    @Override
    public PaginationBean<MeasUnit> getMeasUnit(String name,String sign, Integer inUse, Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from MeasUnit t where 1=1 ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();

            // 名称/简称
            if (!StringUtils.isEmpty(name)) {
                hql.append("  and (upper(t.name) like upper(:name))");
                paramList.put("name", "%" + this.sqlLikeReplace(name) + "%");
            }
            if (!StringUtils.isEmpty(sign)) {
                hql.append("  and upper(t.sign) like upper(:sign) escape '/' ");
                paramList.put("sign", "%" + this.sqlLikeReplace(sign) + "%");
            }
            if (inUse != null) {
                hql.append("  and inUse = :inUse ");
                paramList.put("inUse", inUse);
            }
            hql.append(" order by t.sortNum, name");
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
    }

}
