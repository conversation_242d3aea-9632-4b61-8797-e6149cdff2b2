package com.pcitc.opal.pm.bll.imp;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import pcitc.imp.common.ettool.utils.ObjectConverter;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.CommonUtil;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.FactoryDemoService;
import com.pcitc.opal.pm.bll.entity.FactoryDemoEntity;
import com.pcitc.opal.pm.pojo.FactoryDemo;

/*
 * 工厂业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_FactoryImpl
 * 作       者：dongsheng.zhao
 * 创建时间：2017/09/17
 * 修改编号：1
 * 描       述：工厂业务逻辑层实现类
 */
@Service
@Component
public class FactoryDemoImpl implements FactoryDemoService {

	/**
	 * 实例化数据访问层接口
	 */
	@Autowired
	private com.pcitc.opal.pm.dao.FactoryDemoRepository repo;

	/**
	 * 新增工厂
	 * 
	 * <AUTHOR> 2017-09-17
	 * @param factoryEntity
	 *            工厂实体
	 * @throws Exception
	 */
	@Override
	public void addFactory(FactoryDemoEntity factoryEntity) throws Exception {
		// 实体转换为持久层实体
		FactoryDemo factoryPO = ObjectConverter.entityConverter(factoryEntity, FactoryDemo.class);
		// 数据校验
		factoryValidation(factoryPO);
		CommonResult commonResult = repo.addFactory(factoryPO);

		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
	}

	/**
	 * 删除工厂
	 * 
	 * <AUTHOR> 2017-09-17
	 * @param factoryIds
	 *            工厂ID集合
	 * @throws Exception
	 */
	@Override
	public void deleteFactory(Long[] factoryIds) throws Exception {
		// 判断ID集合是否可用
		if (factoryIds == null || factoryIds.length <= 0) {
			throw new Exception("没有需要删除的工厂数据~！");
		}
		List<FactoryDemo> anlyFactoryList = repo.getFactory(factoryIds);
		if (anlyFactoryList == null || anlyFactoryList.isEmpty())
			return;
		Long[] anlyFactoryIdList = anlyFactoryList.stream().map(item -> item.getFactoryId()).toArray(Long[]::new);
		// 调用DAL删除方法
		CommonResult commonResult = repo.deleteFactory(anlyFactoryIdList);

		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
	}

	/**
	 * 修改工厂
	 * 
	 * <AUTHOR> 2017-09-17
	 * @param factoryEntity
	 *            工厂实体
	 * @throws Exception
	 */
	@Override
	public void updateFactory(FactoryDemoEntity factoryEntity) throws Exception {
		// 实体转换持久层实体
		FactoryDemo factoryPO = ObjectConverter.entityConverter(factoryEntity, FactoryDemo.class);
		// 校验
		factoryValidation(factoryPO);
		// 实体转换为持久层实体
		factoryPO = repo.getSingleFactory(factoryEntity.getFactoryId());
		CommonUtil.objectExchange(factoryEntity, factoryPO);
		// 赋值 修改人、修改名称、修改时间
		// :todo
		// 调用DAL更新方法
		CommonResult commonResult = repo.updateFactory(factoryPO);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
	}

	/**
	 * 通过工厂ID获取单条数据
	 * 
	 * <AUTHOR> 2017-09-17
	 * @param factoryId
	 *            工厂ID
	 */
	@Override
	public FactoryDemoEntity getSingleFactory(Long factoryId) {
		try {
			FactoryDemo factory = repo.getSingleFactory(factoryId);
			return ObjectConverter.entityConverter(factory, FactoryDemoEntity.class);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 工厂查询
	 * 
	 * <AUTHOR> 2017-09-17
	 * @param page
	 *            分页参数
	 * @param name
	 *            工厂名称
	 * @param stdCode
	 *            标准码
	 * @param inUse
	 *            是否启用
	 * @return 工厂实体（分页）
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<FactoryDemoEntity> getFactory(String name, String stdCode, Integer inUse, Pagination page)
			throws Exception {
		try {
			PaginationBean<FactoryDemo> listFactory = repo.getFactory(name, stdCode, inUse, page);
			PaginationBean<FactoryDemoEntity> returnFactory = new PaginationBean<FactoryDemoEntity>(page,
					listFactory.getTotal());
			returnFactory
					.setPageList(ObjectConverter.listConverter(listFactory.getPageList(), FactoryDemoEntity.class));
			return returnFactory;
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 校验
	 * 
	 * @param entity
	 *            工厂实体
	 * @throws Exception
	 */
	private void factoryValidation(FactoryDemo entity) throws Exception {
		// 实体不能为空
		if (entity == null) {
			throw new Exception("没有工厂数据！");
		}
		// 调用DAL与数据库相关的校验
		CommonResult commonResult = repo.factoryValidation(entity);

		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
	}
}
