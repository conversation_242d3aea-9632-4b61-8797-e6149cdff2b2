package com.pcitc.opal.ad.dao.imp;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;
import javax.persistence.TypedQuery;

import com.pcitc.opal.common.*;
import org.apache.commons.beanutils.converters.DateConverter;

import com.pcitc.opal.ad.dao.AlarmNumberRepositoryCustom;
import com.pcitc.opal.common.CommonEnum.EventTypeEnum;
import com.pcitc.opal.common.dao.BaseRepository;
import org.springframework.beans.factory.annotation.Autowired;

/*
 * AlarmNumber实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_AlarmNumberRepositoryImpl
 * 作       者：kun.zhao
 * 创建时间：2017/10/09
 * 修改编号：1
 * 描       述：AlarmNumber实体的Repository实现
 */
public class AlarmNumberRepositoryImpl extends BaseRepository<Object[], Long> implements AlarmNumberRepositoryCustom {
	@Autowired
	private DbConfig dbConfig;

	/**
	 * 报警数量评估-报警数-图形显示
	 *
	  * <AUTHOR> 2017-10-30
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param dateType 日期枚举类型
	 * @return List<Object[]> 返回对象数组集合
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getAlarmNumber(String[] unitCodes, Long[] prdtCellIds, Date beginTime, Date endTime,
										 CommonEnum.DateTypeEnum dateType) {
		try {
			Map<String, Object> map=getHqlAndParams(unitCodes, prdtCellIds, beginTime, endTime, dateType);

			// 调用基类方法查询返回结果
			TypedQuery<Object[]> query = getEntityManager().createQuery((String)map.get("hql"), Object[].class);
            this.setParameterList(query, (Map<String, Object>)map.get("paramList"));
            return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 抽取公共方法，获得hql语句和params参数
	 *
	 * <AUTHOR> 2017-10-30
	 * @param startTime 开始日期
     * @param endTime   结束日期
     * @param unitCodes   装置编码集合
     * @param prdtCellIds   生产单元ID集合
     * @param dateType 时间粒度
	 * @return 
	 * @return Map<String,Object>
	 */
	public Map<String, Object> getHqlAndParams(String[] unitCodes, Long[] prdtCellIds, Date beginTime, Date endTime, CommonEnum.DateTypeEnum dateType){
		// 查询字符串
		StringBuilder hql = new StringBuilder("");
		StringBuilder hqlWhere = new StringBuilder("where "+
				" ae.companyId=:companyId "
//						"and ap.companyId=:companyId and pc.companyId=:companyId and ae.alarmPointId is not null "
//				+ " and ae.alarmFlagId is not null "
//				+ " and ae.priority is not null "
				+ " and ap.inUse = 1 "
				+ " and ae.eventTypeId=:eventTypeId ");
		//3.计算数据分组中的小时
		String queryTime = CommonPropertiesReader.getValue("query.time");
        DateConverter dateConverter = new DateConverter();
        dateConverter.setPattern("HH:mm:ss");
        Date dateTime = dateConverter.convert(Date.class, queryTime);
		Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateTime);
        int hours = calendar.get(Calendar.HOUR_OF_DAY);
        float h=(float)hours/(float)24;
		// 参数集合
		Map<String, Object> paramList = new HashMap<String, Object>();
		paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
		String selectTimeStr="";
		switch (dateType) {
			case Hour:
//					selectTimeStr="concat(to_char(ae.alarmTime,'yyyy-MM-dd HH24'),':00:00')";
				selectTimeStr="concat("+ DbConversion.DbDateTransformYmdhToStr("ae.alarmTime") +",':00:00')";
				break;
			case Day:
				//selectTimeStr="to_char(ae.alarmTime-"+h+",'yyyy-MM-dd')";
				selectTimeStr=DbConversion.DbDateSubTransformYmdToStr("ae.alarmTime",h,hours);
				break;
			case Week:
//				Calendar cal = Calendar.getInstance();
//				cal.setTime(beginTime);
//				int w = cal.get(Calendar.DAY_OF_WEEK);
//				selectTimeStr="to_char(next_day(ae.alarmTime-7-"+h+","+w+"),'YYYY-MM-DD')";
				selectTimeStr = DbConversion.numtodsintervalYmdWeekHql("ae.alarmTime",hours,beginTime);
				break;
			case Month:
//					selectTimeStr="to_char(ae.alarmTime-"+h+",'yyyy-MM')";
				selectTimeStr=DbConversion.DbDateSubTransformYmToStr("ae.alarmTime",h,hours);
				break;
			default:
				break;
		}
		// 装置
		hql.append("select ");
		String groupNameStr="";
		if (unitCodes != null && unitCodes.length > 0) {
			hqlWhere.append("and ae.unitCode in (:unitIds) ");
			List<String> unitIdsList = Arrays.asList(unitCodes);
			paramList.put("unitIds", unitIdsList);
			// 生产单元
			if (unitCodes.length == 1 && prdtCellIds != null && prdtCellIds.length > 0) {
				hql.append("pc.prdtCellId as id,max(pc.sname) as name,count(*) as alarmCount,"+selectTimeStr+" as alarmTime");
				groupNameStr=",pc.prdtCellId";
				hqlWhere.append("and pc.prdtCellId in (:prdtCellIds) ");
				List<Long> prdtCellIdsList = Arrays.asList(prdtCellIds);
				paramList.put("prdtCellIds", prdtCellIdsList);
			}else{
				hql.append("pc.unitId as id,max(pc.sname) as name,count(*) as alarmCount,"+selectTimeStr+" as alarmTime");
				groupNameStr=",pc.unitId";
			}
		}else{
			hql.append("pc.unitId as id,max(pc.sname) as name,count(*) as alarmCount,"+selectTimeStr+" as alarmTime");
			groupNameStr=",pc.unitId";
		}
		hql.append(" from AlarmEvent ae left join ae.alarmPoint ap ");
		hql.append("inner join ae.prdtCell pc ");
//		hql.append("left join ae.alarmFlag af ");
		// 日期
		hqlWhere.append("and ae.alarmTime between :beginTime and :endTime ");
		paramList.put("beginTime", beginTime);
		paramList.put("endTime", endTime);
		hqlWhere.append("group by ").append(selectTimeStr).append(groupNameStr);

		hqlWhere.append(" order by ").append(selectTimeStr).append(groupNameStr);
		//企业
		CommonProperty commonProperty = new CommonProperty();
		paramList.put("companyId",commonProperty.getCompanyId());

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("hql", hql.toString() + hqlWhere.toString());
		map.put("paramList", paramList);
		return map;
	}

	/**
	 * 报警数量评估-报警数-单元显示
	 *
	  * <AUTHOR> 2017-10-30
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @return List<Object[]> 返回对象数组集合
	 * @throws Exception 
	 */
	@Override
	public List<Object[]> getAlarmNumberUnit(String[] unitCodes, Long[] prdtCellIds, Date beginTime,
											 Date endTime) throws Exception {

		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("");
			StringBuilder hqlWhere = new StringBuilder("where " +
//					"ae.companyId=:companyId and ap.companyId=:companyId and pc.companyId=:companyId " +
//					"and ae.alarmPointId is not null "
//					+ " and ae.alarmFlagId is not null "
//					+ " and ae.priority is not null "
					"   ap.inUse = 1 "
					+ " and ae.eventTypeId=:eventTypeId ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
			// 装置
			hql.append("select ");
			String groupNameStr="";
			if (unitCodes != null && unitCodes.length > 0) {
				hqlWhere.append("and ae.unitCode in (:unitIds) ");
				List<String> unitIdsList = Arrays.asList(unitCodes);
				paramList.put("unitIds", unitIdsList);
				// 生产单元
				if (unitCodes.length == 1 && prdtCellIds != null && prdtCellIds.length > 0) {
					hql.append("ae.prdtCellId as id,max(pc.sname) as name,count(*) as alarmCount");
					groupNameStr="ae.prdtCellId";
					hqlWhere.append("and ae.prdtCellId in (:prdtCellIds) ");
					List<Long> prdtCellIdsList = Arrays.asList(prdtCellIds);
					paramList.put("prdtCellIds", prdtCellIdsList);
				}else{
					hql.append("ae.unitCode  as id,max(pc.sname) as name,count(*) as alarmCount");
					groupNameStr="ae.unitCode ";
				}
			}else{
				hql.append("pc.unitId as id,max(pc.sname) as name,count(*) as alarmCount");
				groupNameStr="pc.unitId";
			}
			hql.append(" from AlarmEvent ae left join ae.alarmPoint ap ");
			hql.append("inner join ap.prdtCell pc ");
			// 关联报警标识
//			hql.append("left join ae.alarmFlag af ");
			// 日期
			hqlWhere.append("and ae.alarmTime between :beginTime and :endTime ");
			paramList.put("beginTime", beginTime);
			paramList.put("endTime", endTime);
			hqlWhere.append("group by "+groupNameStr+" order by "+groupNameStr);
//			CommonProperty commonProperty = new CommonProperty();
//			paramList.put("companyId",commonProperty.getCompanyId());

			// 调用基类方法查询返回结果
			TypedQuery<Object[]> query = getEntityManager().createQuery(hql.toString()+hqlWhere.toString(), Object[].class);
            this.setParameterList(query, paramList);
            return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 查询车间报警数量评估-报警数-图形显示
	 *
	  * <AUTHOR> 2017-11-08
	 * <AUTHOR> 2017-11-08
	 * @param unitCodes 车间id数组
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param dateType 日期枚举类型
	 * @return List<Object[]> 返回对象数组集合
	 * @throws Exception 
	 */
	@Override
	public List<Object[]> getWorkShopAlarmNumber(String[] unitCodes, Date beginTime, Date endTime,
                                                 CommonEnum.DateTypeEnum dateType) throws Exception {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("");
			StringBuilder hqlWhere = new StringBuilder("where ae.companyId=:companyId "
//					"and ap.companyId=:companyId and pc.companyId=:companyId and ae.alarmPointId is not null "
//					+ " and ae.alarmFlagId is not null "
//					+ " and ae.priority is not null "
					+ " and ap.inUse = 1 "
					+ " and ae.eventTypeId=:eventTypeId ");
			//3.计算数据分组中的小时
			String queryTime = CommonPropertiesReader.getValue("query.time");
	        DateConverter dateConverter = new DateConverter();
	        dateConverter.setPattern("HH:mm:ss");
	        Date dateTime = dateConverter.convert(Date.class, queryTime);
			Calendar calendar = Calendar.getInstance();
	        calendar.setTime(dateTime);
	        int hours = calendar.get(Calendar.HOUR_OF_DAY);
	        float h=(float)hours/(float)24;
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
			String selectTimeStr="";
			switch (dateType) {
				case Hour:
//						selectTimeStr="concat(to_char(ae.alarmTime,'yyyy-MM-dd HH24'),':00:00')";
					selectTimeStr="concat("+ DbConversion.DbDateTransformYmdhToStr("ae.alarmTime") +",':00:00')";
					break;
				case Day:
//						selectTimeStr="to_char(ae.alarmTime-"+h+",'yyyy-MM-dd')";
					selectTimeStr=DbConversion.DbDateSubTransformYmdToStr("ae.alarmTime",h,hours);
					break;
				case Week:
//					Calendar cal = Calendar.getInstance();
//					cal.setTime(beginTime);
//					int w = cal.get(Calendar.DAY_OF_WEEK);
////						selectTimeStr="to_char(next_day(ae.alarmTime-7-"+h+","+w+"),'YYYY-MM-DD')";
//					selectTimeStr="to_char(next_day(ae.alarmTime-7-"+h+","+w+"),'YYYY-MM-DD')";
					DbConversion.numtodsintervalYmdWeekHql("ae.alarmTime",hours,beginTime);
					break;
				case Month:
//						selectTimeStr="to_char(ae.alarmTime-"+h+",'yyyy-MM')";
					selectTimeStr=DbConversion.DbDateSubTransformYmToStr("ae.alarmTime",h,hours);
					break;
				default:
					break;
			}
			// 装置
			hql.append("select ");
			if (unitCodes != null && unitCodes.length > 0) {
				hql.append("ae.unitCode  as id,max(pc.sname) as name,count(*) as alarmCount,"+selectTimeStr+" as alarmTime");
				hqlWhere.append("and ae.unitCode in (:unitIds) ");
				List<String> unitIdsList = Arrays.asList(unitCodes);
				paramList.put("unitIds", unitIdsList);
			}
			hql.append(" from AlarmEvent ae left join ae.alarmPoint ap ");
			hql.append("inner join ae.prdtCell pc ");
//			hql.append("left join ae.alarmFlag af ");
			// 日期
			hqlWhere.append("and ae.alarmTime between :beginTime and :endTime ");
			paramList.put("beginTime", beginTime);
			paramList.put("endTime", endTime);
			hqlWhere.append("group by "+selectTimeStr+",ae.unitCode order by "+selectTimeStr+",ae.unitCode ");
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			// 调用基类方法查询返回结果
			TypedQuery<Object[]> query = getEntityManager().createQuery(hql.toString()+hqlWhere.toString(), Object[].class);
            this.setParameterList(query, paramList);
            return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 查询车间报警数量评估-报警数-单元显示
	 *
	  * <AUTHOR> 2017-11-08
	 * @param unitCodes 车间id数组
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @return List<Object[]> 返回对象数组集合
	 * @throws Exception 
	 */
	@Override
	public List<Object[]> getWorkShopAlarmNumberUnit(String[] unitCodes, Date beginTime, Date endTime) throws Exception {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("");
			StringBuilder hqlWhere = new StringBuilder("where ae.companyId=:companyId "
//					"and ap.companyId=:companyId and pc.companyId=:companyId and ae.alarmPointId is not null "
//					+ " and ae.alarmFlagId is not null "
//					+ " and ae.priority is not null "
					+ " and ap.inUse = 1 "
					+ " and ae.eventTypeId=:eventTypeId ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
			// 装置
			hql.append("select ");
			hql.append("ae.unitCode as id,max(pc.sname) as name,count(*) as alarmCount");
			if (unitCodes != null && unitCodes.length > 0) {
				hqlWhere.append("and ae.unitCode in (:unitIds) ");
				List<String> unitIdsList = Arrays.asList(unitCodes);
				paramList.put("unitIds", unitIdsList);
			}
			hql.append(" from AlarmEvent ae left join ae.alarmPoint ap ");
			hql.append("inner join ae.prdtCell pc ");
			// 关联报警标识
//	        hql.append("left join ae.alarmFlag af ");
			// 日期
			hqlWhere.append("and ae.alarmTime between :beginTime and :endTime ");
			paramList.put("beginTime", beginTime);
			paramList.put("endTime", endTime);
			hqlWhere.append("group by ae.unitCode order by ae.unitCode ");
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			// 调用基类方法查询返回结果
			TypedQuery<Object[]> query = getEntityManager().createQuery(hql.toString()+hqlWhere.toString(), Object[].class);
            this.setParameterList(query, paramList);
            return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 查询车间报警数量评估-报警数-单元显示
	 *
	  * <AUTHOR> 2017-11-08
	 * @param unitCodes 车间id数组
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @return List<Object[]> 返回对象数组集合
	 * @throws Exception 
	 */
	@Override
	public List<Object[]> deptmUnitAlarmStatt(String[] unitCodes, Date beginTime, Date endTime) throws Exception {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("");
			StringBuilder hqlWhere = new StringBuilder("where ae.companyId=:companyId and ap.companyId=:companyId and pc.companyId=:companyId and ae.alarmPointId is not null "
					+ " and ae.alarmFlagId is not null "
					+ " and ae.priority is not null "
					+ " and ae.eventTypeId=:eventTypeId "
					+ " and ap.inUse = 1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("eventTypeId", EventTypeEnum.ProcessEvent.getIndex());
			// 装置
			hql.append("select ");
			hql.append("pc.unitId as id,max(pc.sname) as name,count(*) as alarmCount,max(pc.name),max(ae.alarmTime)");
			if (unitCodes != null && unitCodes.length > 0) {
				hqlWhere.append("and pc.unitId in (:unitIds) ");
				List<String> unitIdsList = Arrays.asList(unitCodes);
				paramList.put("unitIds", unitIdsList);
			}
			hql.append(" from AlarmEvent ae left join ae.alarmPoint ap ");
			hql.append("inner join ap.prdtCell pc ");
			// 关联报警标识
			hql.append("left join ae.alarmFlag af ");
			// 日期
			hqlWhere.append("and ae.alarmTime between :beginTime and :endTime ");
			paramList.put("beginTime", beginTime);
			paramList.put("endTime", endTime);
			hqlWhere.append("group by pc.unitId order by pc.unitId ");
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			// 调用基类方法查询返回结果
			TypedQuery<Object[]> query = getEntityManager().createQuery(hql.toString()+hqlWhere.toString(), Object[].class);
			this.setParameterList(query, paramList);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}
}
