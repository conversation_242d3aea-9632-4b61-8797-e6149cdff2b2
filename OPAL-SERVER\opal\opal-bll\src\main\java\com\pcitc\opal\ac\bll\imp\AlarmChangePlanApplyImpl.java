package com.pcitc.opal.ac.bll.imp;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.Version;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.pcitc.imp.common.exception.BusiException;
import com.pcitc.opal.aa.bll.entity.AlarmPriorityAssessEntity;
import com.pcitc.opal.ac.bll.AlarmChangePlanApplyService;
import com.pcitc.opal.ac.bll.entity.*;
import com.pcitc.opal.ac.dao.*;
import com.pcitc.opal.ac.pojo.*;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.AlarmRecDAO;
import com.pcitc.opal.ad.dao.AlarmRecRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.ad.vo.AlarmRecInfoReqModel;
import com.pcitc.opal.ad.vo.AlarmRecInfoRespModel;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.CommonEnum.*;
import com.pcitc.opal.common.bll.AAAService;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.WorkFlowService;
import com.pcitc.opal.common.bll.entity.AAAUserEntity;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.common.bll.entity.WorkshopEntity;
import com.pcitc.opal.pm.bll.entity.SystRunParaConfEntity;
import com.pcitc.opal.pm.dao.AlarmPointRepository;
import com.pcitc.opal.pm.dao.AlarmPointViewRepository;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import com.pcitc.opal.pm.pojo.AlarmPointView;
import com.pcitc.opal.webservice.operationalAlarmCtmIndexService.CraftAproEntity;
import com.pcitc.opal.webservice.operationalAlarmCtmIndexService.CtmContrIndexResultExEntity;
import com.pcitc.opal.webservice.operationalAlarmCtmIndexService.OperationalAlarmCtmIndexServiceSoapProxy;
import com.pcitc.ssc.dps.inte.workflow.AppCallResult;
import com.pcitc.ssc.dps.inte.workflow.ExecuteTaskData;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import javax.persistence.EntityManager;
import javax.persistence.ParameterMode;
import javax.persistence.PersistenceContext;
import javax.persistence.StoredProcedureQuery;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/*
 * 报警变更方案申请业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmChangePlanApplyImpl
 * 作	者：kun.zhao
 * 创建时间：2018/01/19
 * 修改编号：1
 * 描	述：报警变更方案申请业务逻辑层实现类
 */
@Service
public class AlarmChangePlanApplyImpl implements AlarmChangePlanApplyService {

    private final static Log logger = LogFactory.getLog(AlarmChangePlanApplyImpl.class);

    /**
     * 实例化数据访问层接口
     */
    @Autowired
    private AlarmPointViewRepository alarmPointViewRepository;
    @Autowired
    private AlarmChangePlanRepository alarmChangePlanRepository;
    @Autowired
    private AlarmChangePlanAproRepository alarmChangePlanAproRepository;
    @Autowired
    private AlarmChangeItemRepository alarmChangeItemRepository;
    @Autowired
    private AlarmChangePlanDetailRepository alarmChangePlanDetailRepository;
    @Autowired
    private AlarmEventRepository alarmEventRepository;
    @Autowired
    private CraftParaChangeAproRepository craftParaChangeAproRepository;
    @Autowired
    private AAAService AAAService;
    @Autowired
    private AlarmRecRepository alarmRecRepository;
    /**
     * 公共方法操作类
     */
    @Autowired
    private BasicDataService basicDataService;
    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private AlarmChangePlanExtrRepository alarmChangePlanExtrRepository;
    @Autowired
    private AlarmPointRepository alarmPointRepository;
    @Autowired
    private WorkFlowService workFlowService;
    @Autowired
    private DbConfig dbConfig;
    @Autowired
    private AlarmRecDAO alarmRecDAO;

    //region 报警变更方案申请首页实现

    /**
     * 删除相关表格实体数据
     *
     * @param alarmChangePlanIds 报警变更事项Id数组
     * @return 返回结果信息类
     * <AUTHOR> 2018-01-19
     */
    @Override
    @Transactional
    public CommonResult deleteAlarmChangePlan(Long[] alarmChangePlanIds, Integer aproType) throws Exception {
        CommonResult commonResult = new CommonResult();
        //根据报警变更方案ID,删除<报警变更事项>数据
        List<AlarmChangeItem> alarmChangeItemList = alarmChangeItemRepository.getAlarmChangeItemByPlanIds(alarmChangePlanIds);
        if (alarmChangeItemList != null && !alarmChangeItemList.isEmpty()) {
            Long[] alarmChangeItemIds = alarmChangeItemList.stream().map(item -> item.getItemId()).toArray(Long[]::new);
            commonResult = alarmChangeItemRepository.deleteAlarmChangeItem(alarmChangeItemIds);
            if (commonResult.getIsSuccess() == false)
                throw new Exception(commonResult.getMessage());
        }
        //根据“报警变更方案ID”查询<报警变更方案明细>获得“报警变更方案明细ID”数组；根据“报警变更方案明细ID数组”删除<工艺参数变更审批记录>；
        //根据报警变更方案ID,删除<报警变更方案明细>数据
        List<AlarmChangePlanDetail> alarmChangePlanDetailList = alarmChangePlanDetailRepository.getalarmChangePlanDetailByPlanIds(alarmChangePlanIds);
        if (alarmChangePlanDetailList != null && !alarmChangePlanDetailList.isEmpty()) {
            Long[] alarmChangePlanDetailIds = alarmChangePlanDetailList.stream().map(item -> item.getPlanDetailId()).toArray(Long[]::new);
            commonResult = craftParaChangeAproRepository.deleteCraftParaChangeAproByPDId(alarmChangePlanDetailIds);
            commonResult = alarmChangePlanDetailRepository.deleteAlarmChangePlanDetail(alarmChangePlanDetailIds);
            if (commonResult.getIsSuccess() == false)
                throw new Exception(commonResult.getMessage());
        }
        List<AlarmChangePlan> alarmChangePlanList = alarmChangePlanRepository.getAlarmChangePlan(alarmChangePlanIds);
        if (alarmChangePlanList != null && !alarmChangePlanList.isEmpty()) {
            //根据报警变更方案ID,删除<报警变更方案审批记录>数据
            Long[] filterPlanIds = alarmChangePlanList.stream().filter(item -> StringUtils.isNotEmpty(item.getFlowCaseId())).map(item -> item.getPlanId()).toArray(Long[]::new);
            List<AlarmChangePlanApro> alarmChangePlanAproList = alarmChangePlanAproRepository.getalarmChangePlanAproByPlanIds(filterPlanIds);
            if (alarmChangePlanAproList != null && !alarmChangePlanAproList.isEmpty()) {
                Long[] alarmChangePlanAproIds = alarmChangePlanAproList.stream().map(item -> item.getAproId()).toArray(Long[]::new);
                commonResult = alarmChangePlanAproRepository.deleteAlarmChangePlanApro(alarmChangePlanAproIds);
                if (commonResult.getIsSuccess() == false)
                    throw new Exception(commonResult.getMessage());
            }
            //删除<报警变更方案>数据
            Long[] anlyAlarmChangePlanIds = alarmChangePlanList.stream().map(item -> item.getPlanId()).toArray(Long[]::new);
            commonResult = alarmChangePlanRepository.deleteAlarmChangePlan(anlyAlarmChangePlanIds);
            if (commonResult.getIsSuccess() == false)
                throw new Exception(commonResult.getMessage());
        }
        return commonResult;
    }

    /**
     * 根据报警变更方案维护ID获取单条数据信息
     *
     * @param alarmChangePlanId 报警变更方案Id
     * @return 报警变更方案实体数据
     * @throws Exception
     * <AUTHOR> 2018-01-19
     */
    @Override
    public AlarmChangePlanEntity getSingleAlarmChangePlan(Long alarmChangePlanId) throws Exception {
        AlarmChangePlan alarmChangePlanPO = alarmChangePlanRepository.getSingleAlarmChangePlan(alarmChangePlanId);
        AlarmChangePlanEntity alarmChangePlanEntity = ObjectConverter.entityConverter(alarmChangePlanPO, AlarmChangePlanEntity.class);
        alarmChangePlanEntity.setUnitName(basicDataService.getUnitListByIds(new String[]{alarmChangePlanEntity.getUnitId()}, true).get(0).getSname());
        return alarmChangePlanEntity;
    }

    /**
     * 加载报警变更方案维护主数据
     *
     * @param unitCodes    装置编码数组
     * @param startTime    申请时间范围开始
     * @param endTime      申请时间范围结束
     * @param status       状态
     * @param businessType 变更方案业务类型
     * @param page         分页实体
     * @return 报警变更实体数据
     * @throws Exception
     * <AUTHOR> 2018-01-19
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<AlarmChangePlanEntity> getAlarmChangePlan(String[] unitCodes, Date startTime, Date endTime, Integer status,
                                                                    Integer businessType, Pagination page) throws Exception {
        List<UnitEntity> units = null;
        if (unitCodes == null) {
            units = basicDataService.getUnitList(true);
            unitCodes = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        // 获取查询时间
        ShiftDateCalculator shiftDateCalculator = null;
        if (startTime != null && endTime != null) {
            shiftDateCalculator = new ShiftDateCalculator(startTime, endTime);
            startTime = shiftDateCalculator.getQueryStartTime();
            endTime = shiftDateCalculator.getQueryEndTime();
        } else if (startTime != null) {
            shiftDateCalculator = new ShiftDateCalculator(startTime, new Date());
            startTime = shiftDateCalculator.getQueryStartTime();
        } else if (endTime != null) {
            shiftDateCalculator = new ShiftDateCalculator(endTime);
            endTime = shiftDateCalculator.getQueryEndTime();
        }
        // 获取查询状态
        Integer[] statuses = getSearchStatuses(status, businessType);

        PaginationBean<AlarmChangePlan> listAlarmChangePlan = alarmChangePlanRepository.getAlarmChangePlan(unitCodes, startTime, endTime, statuses, businessType, page);
        PaginationBean<AlarmChangePlanEntity> returnAlarmChangePlan = new PaginationBean<AlarmChangePlanEntity>(page,
                listAlarmChangePlan.getTotal());
        returnAlarmChangePlan.setPageList(ObjectConverter.listConverter(listAlarmChangePlan.getPageList(), AlarmChangePlanEntity.class));
        if (listAlarmChangePlan.getPageList().size() == 0) {
            return returnAlarmChangePlan;
        }

        if (units == null) {
            // 通过公共方法获取装置
            String[] filterunitCodes = returnAlarmChangePlan.getPageList().stream().map(e -> e.getUnitId()).distinct().toArray(String[]::new);
            units = basicDataService.getUnitListByIds(filterunitCodes, false);
        }
        // 通过公共方法获取车间
        String[] filterWorkshopIds = units.stream().map(e -> e.getWorkshopCode()).distinct().toArray(String[]::new);
        List<WorkshopEntity> workshops = basicDataService.getWorkshopListByWorkshopIds(filterWorkshopIds);
        // 映射字段
        for (int i = 0; i < returnAlarmChangePlan.getPageList().size(); i++) {
            AlarmChangePlanEntity alarmChangePlanEntity = returnAlarmChangePlan.getPageList().get(i);
            AlarmChangePlan alarmChangePlan = listAlarmChangePlan.getPageList().get(i);
            // 填充装置简称
            UnitEntity unit = units.stream().filter(u -> alarmChangePlan.getUnitId().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
            alarmChangePlanEntity.setUnitName(unit.getSname());
            // 填充车间简称
            WorkshopEntity workshop = workshops.stream().filter(w -> unit.getWorkshopCode().equals(w.getStdCode())).findFirst().orElse(new WorkshopEntity());
            alarmChangePlanEntity.setWorkshopId(workshop.getStdCode());
            alarmChangePlanEntity.setWorkshopName(workshop.getSname());
            // 映射显示状态
            alarmChangePlanEntity.setShowStatus(findShowStatus(alarmChangePlanEntity.getStatus(), businessType));
            alarmChangePlanEntity.setBusinessType(businessType);
        }
        return returnAlarmChangePlan;
    }

    //endregion

    //region 变更事项维护业务逻辑层实现

    /**
     * 新增报警变更方案明细、变更事项
     *
     * @param alarmChangeItemValueEntity 报警变更事项实体
     * @throws Exception
     * <AUTHOR> 2018-01-22
     */
    @Override
    @Transactional
    public CommonResult addAlarmChangeItem(AlarmChangeItemValueEntity alarmChangeItemValueEntity) throws Exception {

        //region 新增报警变更方案明细
        AlarmChangePlanDetail alarmChangePlanDetail = new AlarmChangePlanDetail();
        alarmChangePlanDetail.setPlanId(alarmChangeItemValueEntity.getPlanId());
        alarmChangePlanDetail.setAlarmPointId(alarmChangeItemValueEntity.getAlarmPointId());
        alarmChangePlanDetail.setAlarmFlagId(alarmChangeItemValueEntity.getAlarmFlagId());
        alarmChangePlanDetail.setRemark(alarmChangeItemValueEntity.getRemark());

        // 赋值 创建人、创建名称、创建时间
        CommonUtil.returnValue(alarmChangePlanDetail, CommonEnum.PageModelEnum.NewAdd.getIndex());

        //新增报警变更方案明细
        CommonResult commonResult = alarmChangePlanDetailRepository.addAlarmChangePlanDetail(alarmChangePlanDetail);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        //endregion

        //region新增报警变更事项
        alarmChangeItemValueEntity.setPlanDetailId(alarmChangePlanDetail.getPlanDetailId());
        addAlarmChangeItemExt(alarmChangeItemValueEntity);
        //endregion

        return commonResult;
    }

    /**
     * 更新报警变更方案明细、变更事项
     *
     * @param alarmChangeItemValueEntity 报警变更事项实体
     * @throws Exception
     * <AUTHOR> 2018-01-22
     */
    @Override
    @Transactional
    public CommonResult updateAlarmChangeItem(AlarmChangeItemValueEntity alarmChangeItemValueEntity) throws Exception {

        //region 更新报警变更方案明细
        AlarmChangePlanDetail alarmChangePlanDetail = alarmChangePlanDetailRepository.getByIds(new Long[]{alarmChangeItemValueEntity.getPlanDetailId()}).get(0);
        alarmChangePlanDetail.setRemark(alarmChangeItemValueEntity.getRemark());

        // 赋值 创建人、创建名称、创建时间
        CommonUtil.returnValue(alarmChangePlanDetail, CommonEnum.PageModelEnum.Edit.getIndex());
        //新增报警变更方案明细
        CommonResult commonResult = alarmChangePlanDetailRepository.updateAlarmChangePlanDetail(alarmChangePlanDetail);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        //endregion

        //region删除“报警变更方案明细ID”对应的<报警变更事项>记录
        alarmChangeItemRepository.deleteAlarmChangeItemByPDId(alarmChangeItemValueEntity.getPlanDetailId());

        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        //endregion

        //region新增报警变更事项
        addAlarmChangeItemExt(alarmChangeItemValueEntity);
        //endregion

        return commonResult;
    }

    /**
     * 获取报警变更事项实体数据
     *
     * @param planId       报警变更方案ID
     * @param planDetailId 报警变更方案明细ID
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @return AlarmChangeItemValueEntity 报警变更事项实体
     * <AUTHOR> 2018-01-22
     */
    @Override
    public AlarmChangeItemValueEntity getAlarmChangeItemByPlanDetail(Long planId, Long planDetailId, Long alarmPointId, Long alarmFlagId) throws Exception {

        //变更事项页面实体
        AlarmChangeItemValueEntity alarmChangeItemValueEntity = new AlarmChangeItemValueEntity();

        //报警变更方案明细
        AlarmChangePlanDetail alarmChangePlanDetail = null;
        if (planDetailId == null) {
            List<AlarmChangePlanDetail> acpdList = alarmChangePlanDetailRepository.getChangeDetailByPointFlag(planId, alarmPointId, alarmFlagId);
            if (acpdList != null && acpdList.size() > 0) {
                alarmChangePlanDetail = acpdList.get(0);
            }
        } else {
            alarmChangePlanDetail = alarmChangePlanDetailRepository.getByIds(new Long[]{planDetailId}).get(0);
        }
        //报警事件实体
        AlarmEvent alarmEvent = alarmEventRepository.getAlarmEventByPointFlag(alarmPointId, alarmFlagId);
        //报警点数据
        AlarmPoint alarmPoint = alarmPointRepository.getSingleAlarmPoint(alarmPointId);
        //变更事项
        List<AlarmChangeItem> alarmChangeItemList = alarmChangeItemRepository.getAlarmChangeItemByPlanDetail(planId, planDetailId);

        // region 是否屏蔽
        AlarmChangeItem alarmChangeItem = alarmChangeItemList.stream().filter(x -> x.getChangeItemTypeId().equals(Long.valueOf(CommonEnum.ChangeItemTypeEnum.InSuppressed.getIndex()))).findFirst().orElse(null);
        if (alarmChangeItem == null) {
            if (alarmEvent == null) {
                alarmChangeItemValueEntity.setBeforeInSuppressed(null);
            } else {
                alarmChangeItemValueEntity.setBeforeInSuppressed(alarmEvent.getInSuppressed());
            }
            alarmChangeItemValueEntity.setAfterInSuppressed(alarmChangeItemValueEntity.getBeforeInSuppressed());
        } else {
            AlarmChangeItemEntity alarmChangeItemEntity = ObjectConverter.entityConverter(alarmChangeItem, AlarmChangeItemEntity.class);
            alarmChangeItemValueEntity.setBeforeInSuppressed(alarmChangeItemEntity.getBeforeContent() == null ? null : Integer.valueOf(alarmChangeItemEntity.getBeforeContent()));
            alarmChangeItemValueEntity.setAfterInSuppressed(alarmChangeItemEntity.getAfterContent() == null ? null : Integer.valueOf(alarmChangeItemEntity.getAfterContent()));
        }
        alarmChangeItemValueEntity.setBeforeInSuppressedStr(alarmChangeItemValueEntity.getBeforeInSuppressed() == null ? null : CommonEnum.InSuppressedEnum.getName(alarmChangeItemValueEntity.getBeforeInSuppressed()));
        alarmChangeItemValueEntity.setAfterInSuppressedStr(alarmChangeItemValueEntity.getAfterInSuppressed() == null ? null : CommonEnum.InSuppressedEnum.getName(alarmChangeItemValueEntity.getAfterInSuppressed()));
        //endregion

        // region 限值
        alarmChangeItem = alarmChangeItemList.stream().filter(x -> x.getChangeItemTypeId().equals(Long.valueOf(CommonEnum.ChangeItemTypeEnum.LimitValue.getIndex()))).findFirst().orElse(null);
        if (alarmChangeItem == null) {
            if (alarmPoint == null) {
                alarmChangeItemValueEntity.setBeforeLimitValue(null);
            } else {
                Double limitValue = null;
                if (alarmFlagId.equals(Long.valueOf(CommonEnum.AlarmFlagEnum.PVHH.getIndex()))) {
                    limitValue = alarmPoint.getAlarmPointHH();
                } else if (alarmFlagId.equals(Long.valueOf(CommonEnum.AlarmFlagEnum.PVHI.getIndex()))) {
                    limitValue = alarmPoint.getAlarmPointHI();
                } else if (alarmFlagId.equals(Long.valueOf(CommonEnum.AlarmFlagEnum.PVLO.getIndex()))) {
                    limitValue = alarmPoint.getAlarmPointLO();
                } else if (alarmFlagId.equals(Long.valueOf(CommonEnum.AlarmFlagEnum.PVLL.getIndex()))) {
                    limitValue = alarmPoint.getAlarmPointLL();
                }
                alarmChangeItemValueEntity.setBeforeLimitValue(limitValue == null ? null : new DecimalFormat("###################.###########").format(limitValue));
            }
            alarmChangeItemValueEntity.setAfterLimitValue(alarmChangeItemValueEntity.getBeforeLimitValue());
        } else {
            AlarmChangeItemEntity alarmChangeItemEntity = ObjectConverter.entityConverter(alarmChangeItem, AlarmChangeItemEntity.class);
            alarmChangeItemValueEntity.setBeforeLimitValue(alarmChangeItemEntity.getBeforeContent() == null ? null : alarmChangeItemEntity.getBeforeContent());
            alarmChangeItemValueEntity.setAfterLimitValue(alarmChangeItemEntity.getAfterContent() == null ? null : alarmChangeItemEntity.getAfterContent());
        }
        //endregion

        //region 优先级
        alarmChangeItem = alarmChangeItemList.stream().filter(x -> x.getChangeItemTypeId().equals(Long.valueOf(CommonEnum.ChangeItemTypeEnum.Priority.getIndex()))).findFirst().orElse(null);
        if (alarmChangeItem == null) {
            if (alarmEvent == null) {
                alarmChangeItemValueEntity.setBeforePriority(null);
            } else {
                alarmChangeItemValueEntity.setBeforePriority(alarmEvent.getPriority());
            }
            alarmChangeItemValueEntity.setAfterPriority(alarmChangeItemValueEntity.getBeforePriority());
        } else {
            AlarmChangeItemEntity alarmChangeItemEntity = ObjectConverter.entityConverter(alarmChangeItem, AlarmChangeItemEntity.class);
            alarmChangeItemValueEntity.setBeforePriority(alarmChangeItemEntity.getBeforeContent() == null ? null : Integer.valueOf(alarmChangeItemEntity.getBeforeContent()));
            alarmChangeItemValueEntity.setAfterPriority(alarmChangeItemEntity.getAfterContent() == null ? null : Integer.valueOf(alarmChangeItemEntity.getAfterContent()));
        }
        alarmChangeItemValueEntity.setBeforePriorityName(alarmChangeItemValueEntity.getBeforePriority() == null ? null : CommonEnum.AlarmPriorityEnum.getName(alarmChangeItemValueEntity.getBeforePriority()));
        alarmChangeItemValueEntity.setAfterPriorityName(alarmChangeItemValueEntity.getAfterPriority() == null ? null : CommonEnum.AlarmPriorityEnum.getName(alarmChangeItemValueEntity.getAfterPriority()));
        //endregion

        //region 备注
        alarmChangeItemValueEntity.setRemark(alarmChangePlanDetail == null ? null : alarmChangePlanDetail.getRemark());
        //endregion

        return alarmChangeItemValueEntity;
    }

    /**
     * 根据planId、planDetailId获取报警变更事项数据列表
     *
     * @param planId       报警变更方案ID
     * @param planDetailId 报警变更方案明细ID
     * @return 报警变更事项列表
     * <AUTHOR>   2018-01-22
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<AlarmChangeItemEntity> getAlarmChangeItemListByPlanDetail(Long planId, Long planDetailId) throws Exception {
        List<AlarmChangeItem> list = alarmChangeItemRepository.getAlarmChangeItemByPlanDetailId(planId, planDetailId);
        return ObjectConverter.listConverter(list, AlarmChangeItemEntity.class);
    }

    //region 私有方法

    /**
     * 新增报警变更事项
     *
     * @param alarmChangeItemValueEntity 报警变更事项
     * @return 报警变更事项列表
     * <AUTHOR>   2018-01-23
     */
    private void addAlarmChangeItemExt(AlarmChangeItemValueEntity alarmChangeItemValueEntity) throws Exception {
        List<AlarmChangeItem> changeItemList = new ArrayList<>();
        boolean isTrue = true;
        //2）若“是否屏蔽”变更前与变更后不相同且变更后非空，则变更事项插入“是否屏蔽（3）”记录；
        if (!((alarmChangeItemValueEntity.getBeforeInSuppressed() == null ? "" : alarmChangeItemValueEntity.getBeforeInSuppressed()).equals(alarmChangeItemValueEntity.getAfterInSuppressed()))
                && alarmChangeItemValueEntity.getAfterInSuppressed() != null) {
            isTrue = false;
            AlarmChangeItem alarmChangeItem = new AlarmChangeItem();
            alarmChangeItem.setPlanId(alarmChangeItemValueEntity.getPlanId());
            alarmChangeItem.setPlanDetailId(alarmChangeItemValueEntity.getPlanDetailId());
            alarmChangeItem.setChangeItemTypeId(Long.valueOf(CommonEnum.ChangeItemTypeEnum.InSuppressed.getIndex()));
            alarmChangeItem.setBeforeContent(alarmChangeItemValueEntity.getBeforeInSuppressed() == null ? "" : alarmChangeItemValueEntity.getBeforeInSuppressed().toString());
            alarmChangeItem.setAfterContent(alarmChangeItemValueEntity.getAfterInSuppressed() == null ? "" : alarmChangeItemValueEntity.getAfterInSuppressed().toString());
            changeItemList.add(alarmChangeItem);
        }
        //3）若限值变更前与变更后不相同且变更后非空，则变更事项插入“限值（1）”记录；
        if (!((alarmChangeItemValueEntity.getBeforeLimitValue() == null ? "" : alarmChangeItemValueEntity.getBeforeLimitValue()).equals(alarmChangeItemValueEntity.getAfterLimitValue()))
                && alarmChangeItemValueEntity.getAfterLimitValue() != null) {
            isTrue = false;
            AlarmChangeItem alarmChangeItem = new AlarmChangeItem();
            alarmChangeItem.setPlanId(alarmChangeItemValueEntity.getPlanId());
            alarmChangeItem.setPlanDetailId(alarmChangeItemValueEntity.getPlanDetailId());
            alarmChangeItem.setChangeItemTypeId(Long.valueOf(CommonEnum.ChangeItemTypeEnum.LimitValue.getIndex()));
            alarmChangeItem.setBeforeContent(alarmChangeItemValueEntity.getBeforeLimitValue() == null ? "" : alarmChangeItemValueEntity.getBeforeLimitValue());
            alarmChangeItem.setAfterContent(alarmChangeItemValueEntity.getAfterLimitValue() == null ? "" : alarmChangeItemValueEntity.getAfterLimitValue());
            changeItemList.add(alarmChangeItem);
        }
        //4）若优先级变更前与变更后不相同且变更后非空，则变更事项插入“优先级（2）”记录；
        if (!((alarmChangeItemValueEntity.getBeforePriority() == null ? "" : alarmChangeItemValueEntity.getBeforePriority()).equals(alarmChangeItemValueEntity.getAfterPriority()))
                && alarmChangeItemValueEntity.getAfterPriority() != null) {
            isTrue = false;
            AlarmChangeItem alarmChangeItem = new AlarmChangeItem();
            alarmChangeItem.setPlanId(alarmChangeItemValueEntity.getPlanId());
            alarmChangeItem.setPlanDetailId(alarmChangeItemValueEntity.getPlanDetailId());
            alarmChangeItem.setChangeItemTypeId(Long.valueOf(CommonEnum.ChangeItemTypeEnum.Priority.getIndex()));
            alarmChangeItem.setBeforeContent(alarmChangeItemValueEntity.getBeforePriority() == null ? "" : alarmChangeItemValueEntity.getBeforePriority().toString());
            alarmChangeItem.setAfterContent(alarmChangeItemValueEntity.getAfterPriority() == null ? "" : alarmChangeItemValueEntity.getAfterPriority().toString());
            changeItemList.add(alarmChangeItem);
        }
        //5）若1）2）3）均不满足，则变更事项插入“其他（4）”记录；
        if (isTrue) {
            AlarmChangeItem alarmChangeItem = new AlarmChangeItem();
            alarmChangeItem.setPlanId(alarmChangeItemValueEntity.getPlanId());
            alarmChangeItem.setPlanDetailId(alarmChangeItemValueEntity.getPlanDetailId());
            alarmChangeItem.setChangeItemTypeId(Long.valueOf(CommonEnum.ChangeItemTypeEnum.Other.getIndex()));
            alarmChangeItem.setBeforeContent(null);
            alarmChangeItem.setAfterContent(null);
            changeItemList.add(alarmChangeItem);
        }

        for (AlarmChangeItem alarmChangeItem : changeItemList) {
            CommonResult commonResult = alarmChangeItemRepository.addAlarmChangeItem(alarmChangeItem);

            // 如果失败，直接throw
            if (commonResult.getIsSuccess() == false)
                throw new Exception(commonResult.getMessage());
        }
    }

    //endregion

    //endregion

    //region 报警变更方案详情服务层接口实现

    /**
     * 根据变更方案ID获取变更方案详情分页信息
     *
     * @param planId   变更方案ID
     * @param aproType 审批类型  1.本系统审批 2.工艺系统审批
     * @param page     分页信息
     * @return 方案详情列表
     * <AUTHOR> 2018-01-22
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<AlarmChangePlanDetailEntity> getAlarmChangePlanDetail(Long planId, Integer aproType, Pagination page) throws Exception {
        PaginationBean<AlarmChangePlanDetail> pageList = alarmChangePlanDetailRepository.getAlarmChangePlanDetail(planId, page);
        PaginationBean<AlarmChangePlanDetailEntity> returnList = new PaginationBean<>(page, pageList.getTotal());
        returnList.setPageList(ObjectConverter.listConverter(pageList.getPageList(), AlarmChangePlanDetailEntity.class));
        if (pageList.getPageList().size() == 0) {
            return returnList;
        }
        List<CraftParaChangeApro> craftParaChangeAproList = null;
        if (aproType.intValue() == 2) {
            //根据报警变更方案明细ID查询工艺参数变更审批记录
            Long[] planDetailIds = returnList.getPageList().stream().map(x -> x.getPlanDetailId()).toArray(Long[]::new);
            craftParaChangeAproList = craftParaChangeAproRepository.getAlarmChangeItemByPlanDetail(planDetailIds);
        }
        for (AlarmChangePlanDetailEntity detailEntity : returnList.getPageList()) {
            AlarmChangePlanDetail po = pageList.getPageList().stream().filter(item -> item.getAlarmChangePlan().getPlanId().equals(detailEntity.getPlanId())).findFirst().orElse(null);
            if (po != null) {
                //4.设置报警标识名称
                detailEntity.setAlarmFlagName(AlarmFlagEnum.getName(Integer.parseInt(detailEntity.getAlarmFlagId() + "")));
                //5.设置报警点信息
                detailEntity.setTag(po.getAlarmPoint().getTag());
                detailEntity.setDes(po.getAlarmPoint().getLocation());
                detailEntity.setPrdtName(po.getAlarmPoint().getPrdtCell().getSname());
                detailEntity.setMeasUnitName(po.getAlarmPoint().getMeasUnit().getName());
                //设置工艺参数变更记录
                if (aproType.intValue() == 2 && craftParaChangeAproList != null && craftParaChangeAproList.size() > 0) {
                    CraftParaChangeApro craftParaChangeApro = craftParaChangeAproList.stream().
                            filter(x -> x.getPlanDetailId().longValue() == detailEntity.getPlanDetailId().longValue())
                            .findFirst().orElse(new CraftParaChangeApro());
                    detailEntity.setAproOpnion(craftParaChangeApro.getAproOpnion());
                    detailEntity.setAproStatus(craftParaChangeApro.getAproStatus());
                    detailEntity.setAproTime(craftParaChangeApro.getAproTime());
                    detailEntity.setAproUserId(craftParaChangeApro.getAproUserId());
                    detailEntity.setAproUserName(craftParaChangeApro.getAproUserName());
                }

            }
        }
        return returnList;
    }

    /**
     * 删除数据
     *
     * @param planDetailIds 变更方案详情ID集合
     * @param aproType
     * <AUTHOR> 2018-01-22
     */
    @Transactional
    @Override
    public CommonResult deleteAlarmChangePlanDetailByIds(Long[] planDetailIds, Integer aproType) throws Exception {
        // 判断ID集合是否可用
        if (planDetailIds == null || planDetailIds.length <= 0) {
            throw new Exception("没有需要删除的方案详情数据！");
        }
        List<AlarmChangePlanDetail> detailList = alarmChangePlanDetailRepository.getByIds(planDetailIds);
        if (detailList == null || detailList.isEmpty()) {
            return new CommonResult();
        }
        Long[] pdIds = detailList.stream().map(item -> item.getPlanDetailId()).toArray(Long[]::new);

        CommonResult commonResult;
        if (aproType.intValue() == 2) {
            //0.删除工艺参数变更审批记录
            commonResult = craftParaChangeAproRepository.deleteCraftParaChangeAproByPDId(pdIds);
        }
        // 1.删除AlarmChangeItem
        List<AlarmChangeItem> itemList = alarmChangeItemRepository.getAlarmChangeItemByPlanDetailIds(pdIds);
        Long[] itemIds = itemList.stream().map(AlarmChangeItem::getItemId).toArray(Long[]::new);
        commonResult = alarmChangeItemRepository.deleteAlarmChangeItem(itemIds);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false) {
            throw new Exception(commonResult.getMessage());
        }

        // 2.删除方案详情
        commonResult = alarmChangePlanDetailRepository.deleteAlarmChangePlanDetail(pdIds);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false) {
            throw new Exception(commonResult.getMessage());
        }
        return commonResult;
    }
    //endregion

    //region 审批信息服务层接口实现

    /**
     * 审批信息-服务层实现
     *
     * @param planId 报警变更方案ID
     * @return List<AlarmChangePlanAproEntity> 返回AlarmChangePlanAproEntity实体集合
     * @throws Exception 抛出异常
     * <AUTHOR> 2018-01-23
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<AlarmChangePlanAproEntity> getAlarmChangePlanApro(Long planId) throws Exception {
        try {
            List<AlarmChangePlanApro> list = alarmChangePlanAproRepository.getAlarmChangePlanApro(planId);
            List<AlarmChangePlanAproEntity> returnList = ObjectConverter.listConverter(list, AlarmChangePlanAproEntity.class);
            return returnList;
        } catch (Exception e) {
            throw e;
        }
    }
    //endregion

    //region 报警变更方案新增、编辑、详情服务层接口实现

    /**
     * 新增数据
     *
     * @param alarmChangePlanEntity 报警变更方案实体
     * <AUTHOR> 2018-01-19
     */
    @Transactional
    @Override
    public CommonResult addAlarmChangePlan(AlarmChangePlanEntity alarmChangePlanEntity) throws Exception {
        Date current = new Date();
        //1.实体转换为持久层实体
        AlarmChangePlan po = ObjectConverter.entityConverter(alarmChangePlanEntity, AlarmChangePlan.class);
        //2.处理申请时间
        po.setApplyTime(current);
        //3.处理编号
        String maxCode = alarmChangePlanRepository.getMaxPlanCode();
        if ("null".equals(maxCode) || StringUtils.isEmpty(maxCode)) {
            maxCode = DateFormatUtils.format(current, "yyyyMMdd001");
        } else {
            String index = StringUtils.replace(maxCode, DateFormatUtils.format(current, "yyyyMMdd"), "");
            maxCode = String.valueOf(Integer.parseInt(index) + 1);
            while (maxCode.length() < 3) {
                maxCode = "0" + maxCode;
            }
            maxCode = DateFormatUtils.format(current, "yyyyMMdd") + maxCode;
        }
        po.setPlanCode(maxCode);
        //4.设置设置提交时间
        //po.setSubmitTime(current);
        //5.设置提交状态
        po.setStatus(CommonEnum.AlarmChangePlanStatusEnum.UnSubmit.getIndex());
        //6.设置提交人
        //CommonProperty commonProperty = new CommonProperty();
        //po.setSubmitUserId(commonProperty.getUserId());
        //po.setSubmitUserName(commonProperty.getUserName());

        CommonResult commonResult = alarmChangePlanRepository.add(po);

        if (commonResult.getIsSuccess() == false) {
            throw new Exception(commonResult.getMessage());
        } else {
            //4.设置返回编号和时间
            commonResult.setMessage("{ \"planCode\":\"" + maxCode + "\",\"planId\":\"" + po.getPlanId() + "\",\"date\":\"" + DateFormatUtils.format(current, "yyyy-MM-dd HH:mm:ss") + "\"}");
        }

        return commonResult;
    }

    /**
     * 合法性校验
     *
     * @param po
     * @throws Exception
     * <AUTHOR> 2018-01-26
     */
    @SuppressWarnings("unused")
    private void validate(AlarmChangePlan po) throws Exception {
        CommonResult commonResult = new CommonResult();
        // 实体不能为空
        if (po == null) {
            throw new BusiException("00", "没有装置数据！");
        }
        // 调用DAL与数据库相关的校验
        commonResult = alarmChangePlanRepository.validate(po);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
    }

    /**
     * 更新
     *
     * @param entity 报警变更方案实体
     * <AUTHOR> 2018-01-19
     */
    @Override
    public CommonResult updateAlarmChangePlan(AlarmChangePlanEntity entity) throws Exception {
        // 实体转换持久层实体
        AlarmChangePlan po = ObjectConverter.entityConverter(entity, AlarmChangePlan.class);
        // 实体转换为持久层实体
        po = alarmChangePlanRepository.getSingleAlarmChangePlan(po.getPlanId());
        po.setApplyReason(entity.getApplyReason());
        po.setUnitId(entity.getUnitId());
        // 调用DAL更新方法
        CommonResult commonResult = alarmChangePlanRepository.update(po);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }
    //endregion

    //region 位号清单服务层接口实现

    /**
     * 最频繁的报警-Top20服务层实现
     *
     * @param endTime  查询结束时间
     * @param unitCode 装置编码
     * @param topType  Top20,Top10切换选择
     * @return List<MostFrequentAlarmEntity> 返回MostFrequentAlarmEntity实体集合
     * @throws Exception 抛出异常
     * <AUTHOR> 2018-01-22
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<MostFrequentAlarmEntity> getMostFrequentAlarmTop20(Date endTime, String unitCode, Integer topType) throws Exception {
        // region 公共数据
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(endTime);
        Date startTime = shiftDateCalculator.getQueryStartTime();
        endTime = shiftDateCalculator.getQueryEndTime();
        Map<String, Object> map = basicDataService.getSearchTime(startTime, endTime);
        String endFlag = (String) map.get("endFlag");
        String startFlag = (String) map.get("startFlag");
        if (topType == null) topType = 20;
        List<Object[]> list = alarmEventRepository.getAlarmNumberTop20(new String[]{unitCode}, new Long[]{}, new String[]{}, startTime, endTime, startFlag, endFlag, topType);
        List<Long> alarmPointIdList = list.stream().map(x -> Long.valueOf(String.valueOf(x[6]))).distinct().collect(Collectors.toList());
        List<Long> alarmFlagIdList = list.stream().map(x -> Long.valueOf(String.valueOf(x[7]))).distinct().collect(Collectors.toList());
        List<DictionaryEntity> planList = basicDataService.getUnfinishedPlanList(alarmFlagIdList, alarmPointIdList);
        List<MostFrequentAlarmEntity> returnEntity = new ArrayList<MostFrequentAlarmEntity>();
        for (Object[] objs : list) {
            MostFrequentAlarmEntity entity = new MostFrequentAlarmEntity();
            entity.setTag(String.valueOf(objs[0]));
            entity.setAlarmFlagName(String.valueOf(objs[1]));
            entity.setPrdtCellName(String.valueOf(objs[4]));
            entity.setAlarmCount(NumberUtils.toLong(String.valueOf(objs[2])));
            entity.setAlarmPointId(NumberUtils.toLong(String.valueOf(objs[6])));
            entity.setAlarmFlagId(NumberUtils.toLong(String.valueOf(objs[7])));
            DictionaryEntity de = planList.stream().filter(x -> x.getKey().equals(objs[6]) && x.getValue().equals(objs[7])).findFirst().orElse(new DictionaryEntity());
            if (de.getKey() == null && de.getValue() == null) {
                entity.setChangeStatus(true);
            } else {
                entity.setChangeStatus(false);
            }
            entity.setStartTime(startTime);
            entity.setEndTime(endTime);
            returnEntity.add(entity);
        }
        return returnEntity;
    }

    /**
     * 最频繁的操作-Top20服务层实现
     *
     * @param endTime  查询结束时间
     * @param unitCode 装置编码
     * @param topType  Top20,Top10切换选择
     * @return List<MostFrequentAlarmEntity> 返回MostFrequentAlarmEntity实体集合
     * @throws Exception 抛出异常
     * <AUTHOR> 2018-01-22
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<MostFrequentAlarmEntity> getMostFrequentOperateTop20(Date endTime, String unitCode, Integer topType) throws Exception {
        //获取查询开始和结束时间
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(endTime);
        Date startTime = shiftDateCalculator.getQueryStartTime();
        endTime = shiftDateCalculator.getQueryEndTime();
        if (topType == null) topType = 20;
        List<Object[]> list = alarmEventRepository.getAlarmOperateTop20(startTime, endTime, new String[]{unitCode}, new Long[]{}, topType);
        List<Long> alarmPointIdList = list.stream().map(x -> Long.valueOf(String.valueOf(x[6]))).distinct().collect(Collectors.toList());
        List<Long> alarmFlagIdList = list.stream().map(x -> Long.valueOf(String.valueOf(x[7]))).distinct().collect(Collectors.toList());
        List<DictionaryEntity> planList = basicDataService.getUnfinishedPlanList(alarmFlagIdList, alarmPointIdList);
        List<MostFrequentAlarmEntity> returnEntity = new ArrayList<MostFrequentAlarmEntity>();
        for (Object[] objs : list) {
            MostFrequentAlarmEntity entity = new MostFrequentAlarmEntity();
            entity.setTag(String.valueOf(objs[0]));
            entity.setAlarmFlagName(String.valueOf(objs[1]));
            entity.setPrdtCellName(String.valueOf(objs[4]));
            entity.setAlarmCount(NumberUtils.toLong(String.valueOf(objs[2])));
            entity.setAlarmPointId(NumberUtils.toLong(String.valueOf(objs[6])));
            entity.setAlarmFlagId(NumberUtils.toLong(String.valueOf(objs[7])));
            DictionaryEntity de = planList.stream().filter(x -> x.getKey().equals(objs[6]) && x.getValue().equals(objs[7])).findFirst().orElse(new DictionaryEntity());
            if (de.getKey() == null && de.getValue() == null) {
                entity.setChangeStatus(true);
            } else {
                entity.setChangeStatus(false);
            }
            entity.setStartTime(startTime);
            entity.setEndTime(endTime);
            returnEntity.add(entity);
        }
        return returnEntity;
    }

    /**
     * 持续报警-Top20服务层实现
     *
     * @param endTime  查询结束时间
     * @param unitCode 装置编码
     * @return List<AlarmEventEntity> 返回AlarmEventEntity实体集合
     * @throws Exception 抛出异常
     * <AUTHOR> 2018-01-22
     */
    @Override
    public List<AlarmEventEntity> getPersistentAlarmAnalysisTop20(Date endTime, String unitCode) throws Exception {
        try {
            Pagination page = new Pagination();
            page.setPageNumber(1);
            page.setPageSize(20);
            //获取查询开始和结束时间
            ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(endTime);
            Date startTime = shiftDateCalculator.getQueryStartTime();
            endTime = shiftDateCalculator.getQueryEndTime();
            List<AlarmEventEntity> returnAlarmEvent = basicDataService.getPersistentAlarmAnalysis(new String[]{unitCode}, null, -1, -1, startTime, endTime, page).getPageList();
            List<Long> alarmPointIdList = returnAlarmEvent.stream().map(x -> x.getAlarmPointId()).distinct().collect(Collectors.toList());
            List<Long> alarmFlagIdList = returnAlarmEvent.stream().map(x -> x.getAlarmFlagId()).distinct().collect(Collectors.toList());
            List<DictionaryEntity> planList = basicDataService.getUnfinishedPlanList(alarmFlagIdList, alarmPointIdList);
            for (AlarmEventEntity entity : returnAlarmEvent) {
                entity.setStartTime(startTime);
                entity.setEndTime(endTime);
                DictionaryEntity de = planList.stream().filter(x -> String.valueOf(x.getKey()).equals(String.valueOf(entity.getAlarmPointId())) && String.valueOf(x.getValue()).equals(String.valueOf(entity.getAlarmFlagId()))).findFirst().orElse(new DictionaryEntity());
                if (de.getKey() == null && de.getValue() == null) {
                    entity.setChangeStatus(true);
                } else {
                    entity.setChangeStatus(false);
                }
            }
            return returnAlarmEvent;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 震荡报警-Top20服务层实现
     *
     * @param endTime  查询结束时间
     * @param unitCode 装置编码
     * @return List<MostFrequentAlarmEntity> 返回MostFrequentAlarmEntity实体集合
     * @throws Exception 抛出异常
     * <AUTHOR> 2018-01-22
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<MostFrequentAlarmEntity> getShakeAlarmAnalysisTop20(Date endTime, String unitCode) throws Exception {
        //企业
        CommonProperty commonProperty = new CommonProperty();
        //获取查询开始和结束时间
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(endTime);
        Date startTime = shiftDateCalculator.getQueryStartTime();
        endTime = shiftDateCalculator.getQueryEndTime();
        StoredProcedureQuery query = entityManager.createStoredProcedureQuery("P_AF_GETShakeAlarmAnalysis");
        query.registerStoredProcedureParameter("v_in_startDate", Date.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_endDate", Date.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_tag", String.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_alarmFlagId", Long.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_beginIndex", Integer.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_endIndex", Integer.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_unitIds", String.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_prdtIds", String.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_type", Integer.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_companyId", Integer.class, ParameterMode.IN);
        if ("oracle".equals(dbConfig.getDataBase())) {
            query.registerStoredProcedureParameter("v_results", void.class, ParameterMode.REF_CURSOR);
        }
        query.setParameter("v_in_startDate", startTime);
        query.setParameter("v_in_endDate", endTime);
        query.setParameter("v_in_tag", "");
        query.setParameter("v_in_alarmFlagId", 0L);
        query.setParameter("v_in_beginIndex", 0);
        query.setParameter("v_in_endIndex", 0);
        query.setParameter("v_in_unitIds", unitCode);
        query.setParameter("v_in_prdtIds", "");
        query.setParameter("v_in_type", 0);
        query.setParameter("v_in_companyId", commonProperty.getCompanyId());

        query.setFirstResult(0).setMaxResults(20);
        List<Object[]> result = query.getResultList();
        List<Long> alarmPointIdList = result.stream().map(x -> Long.valueOf(String.valueOf(x[7]))).distinct().collect(Collectors.toList());
        List<Long> alarmFlagIdList = result.stream().map(x -> Long.valueOf(String.valueOf(x[3]))).distinct().collect(Collectors.toList());
        List<DictionaryEntity> planList = basicDataService.getUnfinishedPlanList(alarmFlagIdList, alarmPointIdList);
        List<MostFrequentAlarmEntity> returnList = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            Object[] element = result.get(i);
            MostFrequentAlarmEntity entity = new MostFrequentAlarmEntity();
            entity.setTag(String.valueOf(element[0]));
            entity.setAlarmFlagName(String.valueOf(element[1]));
            entity.setPrdtCellName(String.valueOf(element[5]));
            entity.setAlarmCount(NumberUtils.toLong(element[2].toString()));
            DictionaryEntity de = planList.stream().filter(x -> x.getKey().toString().equals(element[7].toString()) && x.getValue().toString().equals(element[3].toString())).findFirst().orElse(new DictionaryEntity());
            if (de.getKey() == null && de.getValue() == null) {
                entity.setChangeStatus(true);
            } else {
                entity.setChangeStatus(false);
            }
            entity.setAlarmPointId(Long.valueOf(String.valueOf(element[7])));
            entity.setAlarmFlagId(Long.valueOf(String.valueOf(element[3])));
            entity.setStartTime(startTime);
            entity.setEndTime(endTime);
            returnList.add(entity);
        }
        return returnList;
    }

    /**
     * 优先级评估-图形显示服务层服务层显示
     *
     * @param endTime  查询结束时间
     * @param unitCode 装置编码
     * @return List<AlarmPriorityAssessEntity> 返回AlarmPriorityAssessEntity实体集合
     * @throws Exception 抛出异常
     * <AUTHOR> 2018-01-22
     */
    @SuppressWarnings("rawtypes")
    @Override
    public List<AlarmPriorityAssessEntity> getAlarmPriorityAssessGraphical(Date endTime, String unitCode) throws Exception {
        //获取查询开始和结束时间
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(endTime);
        Date startTime = shiftDateCalculator.getQueryStartTime();
        endTime = shiftDateCalculator.getQueryEndTime();

        List statisticData = alarmEventRepository.getAlarmPriorityAssessStatisticData(startTime, endTime, new String[]{unitCode}, new Long[]{});
        AlarmPriorityAssessEntity entity = new AlarmPriorityAssessEntity();
        List<AlarmPriorityAssessEntity> returnList = new ArrayList<AlarmPriorityAssessEntity>();
        returnList.add(entity);
        double tNum = Double.valueOf(((Object[]) statisticData.get(0))[0] + "");
        if (tNum == 0) {
            entity.setTotalAlarmEvents(Integer.valueOf(((Object[]) statisticData.get(0))[0] + ""));
            entity.setSysEmergencyAlarmRate("0.00%");
            entity.setSysImportantAlarmRate("0.00%");
            entity.setSysGeneralAlarmRate("0.00%");
            return returnList;
        }
        double eNum = Double.valueOf(((Object[]) statisticData.get(0))[1] + "");
        double iNum = Double.valueOf(((Object[]) statisticData.get(0))[2] + "");
        double gNum = Double.valueOf(((Object[]) statisticData.get(0))[3] + "");
        entity.setTotalAlarmEvents(Integer.valueOf(((Object[]) statisticData.get(0))[0] + ""));
        entity.setSysEmergencyAlarmRate(String.format("%.2f", eNum / tNum * 100.00) + "%");
        entity.setSysImportantAlarmRate(String.format("%.2f", iNum / tNum * 100.00) + "%");
        entity.setSysGeneralAlarmRate(String.format("%.2f", gNum / tNum * 100.00) + "%");
        return returnList;
    }

    /**
     * 优先级评估-表格显示服务层实现
     *
     * @param endTime  查询结束时间
     * @param unitCode 装置编码
     * @param priority 优先级(1紧急；2重要；3一般)
     * @return List<MostFrequentAlarmEntity> 返回MostFrequentAlarmEntity实体集合
     * @throws Exception 抛出异常
     * <AUTHOR> 2018-01-22
     */
    @Override
    public List<PriorityAssessEntity> getAlarmPriorityAssessTable(Date endTime, String unitCode, Integer priority) throws Exception {
        //获取查询开始和结束时间
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(endTime);
        Date startTime = shiftDateCalculator.getQueryStartTime();
        endTime = shiftDateCalculator.getQueryEndTime();
        List<Object[]> list = alarmEventRepository.getAlarmPriorityAssessTable(startTime, endTime, unitCode, priority);
        List<Long> alarmPointIdList = list.stream().map(x -> Long.valueOf(String.valueOf(x[5]))).distinct().collect(Collectors.toList());
        List<Long> alarmFlagIdList = list.stream().map(x -> Long.valueOf(String.valueOf(x[6]))).distinct().collect(Collectors.toList());
        List<DictionaryEntity> planList = basicDataService.getUnfinishedPlanList(alarmFlagIdList, alarmPointIdList);
        List<PriorityAssessEntity> returnEntity = new ArrayList<PriorityAssessEntity>();
        for (Object[] objs : list) {
            PriorityAssessEntity entity = new PriorityAssessEntity();
            entity.setTag(String.valueOf(objs[0]));
            entity.setAlarmFlagName(String.valueOf(objs[1]));
            entity.setPrdtCellName(String.valueOf(objs[2]));
            entity.setAlarmCount(Long.valueOf(String.valueOf(objs[3])));
            entity.setAlarmPointId(Long.valueOf(String.valueOf(objs[5])));
            entity.setAlarmFlagId(Long.valueOf(String.valueOf(objs[6])));
            DictionaryEntity de = planList.stream().filter(x -> x.getKey().equals(objs[5]) && x.getValue().equals(objs[6])).findFirst().orElse(new DictionaryEntity());
            if (de.getKey() == null && de.getValue() == null) {
                entity.setChangeStatus(true);
            } else {
                entity.setChangeStatus(false);
            }
            entity.setPriority(Integer.parseInt(String.valueOf(objs[4])));
            entity.setStartTime(startTime);
            entity.setEndTime(endTime);
            returnEntity.add(entity);
        }
        return returnEntity;
    }

    /**
     * 位号查询-服务层实现
     *
     * @param unitCode 装置编码
     * @param tag      位号
     * @param page     分页对象
     * @return PaginationBean<AlarmPointViewEntity> 返回AlarmPointViewEntity实体分页对象
     * @throws Exception 抛出异常
     * <AUTHOR> 2018-01-23
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<AlarmPointViewEntity> getAlarmPointView(String unitCode, String tag, Pagination page) throws Exception {
        try {
            PaginationBean<AlarmPointView> listAlarmPointView = alarmPointViewRepository.getAlarmPointView(unitCode, tag, page);
            //获取查询开始和结束时间
            ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(new Date());
            Date startTime = shiftDateCalculator.getQueryStartTime();
            Date endTime = shiftDateCalculator.getQueryEndTime();
            PaginationBean<AlarmPointViewEntity> returnAlarmPoint = new PaginationBean<AlarmPointViewEntity>(page, listAlarmPointView.getTotal());
            returnAlarmPoint.setPageList(ObjectConverter.listConverter(listAlarmPointView.getPageList(), AlarmPointViewEntity.class));
            List<Long> alarmPointIdList = listAlarmPointView.getPageList().stream().map(x -> x.getAlarmPointId()).distinct().collect(Collectors.toList());
            List<Long> alarmFlagIdList = listAlarmPointView.getPageList().stream().map(x -> x.getAlarmFlagId()).distinct().collect(Collectors.toList());
            List<DictionaryEntity> planList = basicDataService.getUnfinishedPlanList(alarmFlagIdList, alarmPointIdList);
            int i = 0;
            for (AlarmPointViewEntity apve : returnAlarmPoint.getPageList()) {
                AlarmPointView apv = listAlarmPointView.getPageList().get(i);
                apve.setPrdtCellName(apv.getPrdtCell().getSname());
                apve.setAlarmFlagName(apv.getAlarmFlag().getName());
                apve.setStartTime(startTime);
                apve.setEndTime(endTime);
                DictionaryEntity de = planList.stream().filter(x -> String.valueOf(x.getKey()).equals(String.valueOf(apve.getAlarmPointId())) && String.valueOf(x.getValue()).equals(String.valueOf(apve.getAlarmFlagId()))).findFirst().orElse(new DictionaryEntity());
                if (de.getKey() == null && de.getValue() == null) {
                    apve.setChangeStatus(true);
                } else {
                    apve.setChangeStatus(false);
                }
                i++;
            }
            return returnAlarmPoint;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 审核信息-服务层实现
     *
     * @param planId 报警变更方案ID
     * @throws Exception 抛出异常
     * <AUTHOR> 2018-01-30
     */
    @Override
    public AlarmChangePlanAproEntity getAlarmChangePlanAproByPlanId(Long planId) throws Exception {
        AlarmChangePlanApro acpa = alarmChangePlanAproRepository.getAlarmChangePlanAproByPlanId(planId);
        AlarmChangePlanAproEntity acpaEntity = ObjectConverter.entityConverter(acpa, AlarmChangePlanAproEntity.class);
        return acpaEntity;
    }

    /**
     * 下发信息，确认信息-服务层实现
     *
     * @param planId       报警变更方案ID
     * @param businessType 业务类型(1下发；2确认)
     * @throws Exception 抛出异常
     * <AUTHOR> 2018-01-30
     */
    @Override
    public AlarmChangePlanExtrEntity getAlarmChangePlanExtrByPlanId(Long planId, Integer businessType) throws Exception {
        AlarmChangePlanExtr acpe = alarmChangePlanExtrRepository.getAlarmChangePlanExtrByPlanId(planId, businessType);
        AlarmChangePlanExtrEntity acpeEntity = ObjectConverter.entityConverter(acpe, AlarmChangePlanExtrEntity.class);
        return acpeEntity;
    }

    //endregion

    //region 下发信息、确认信息

    /**
     * 下发信息-保存、确认信息-保存
     *
     * @param alarmChangePlanExtrEntity 报警变更方案附加信息实体
     * @return
     * <AUTHOR> 2018-01-30
     */
    @Override
    public CommonResult saveAlarmChangePlanExtr(AlarmChangePlanExtrEntity alarmChangePlanExtrEntity) throws Exception {

        //region 删除“报警变更方案ID”对应的业务类型为“1或2”的<报警变更方案附加信息>记录
        AlarmChangePlanExtr alarmChangePlanExtr = alarmChangePlanExtrRepository.getAlarmChangePlanExtrByPlanId(alarmChangePlanExtrEntity.getPlanId(), alarmChangePlanExtrEntity.getBusinessType());
        if (alarmChangePlanExtr != null) {
            // 调用DAL删除方法
            CommonResult commonResult = alarmChangePlanExtrRepository.deleteAlarmChangePlanExtr(new Long[]{alarmChangePlanExtr.getPlanExtrId()});
            // 如果失败，直接throw
            if (commonResult.getIsSuccess() == false)
                throw new Exception(commonResult.getMessage());
        }
        //endregion

        //region 下发信息-保存，确认信息-保存，新增报警变更方案附加信息
        CommonProperty commonProperty = new CommonProperty();
        // 实体转换为持久层实体
        AlarmChangePlanExtr newAlarmChangePlanExtr = ObjectConverter.entityConverter(alarmChangePlanExtrEntity, AlarmChangePlanExtr.class);
        newAlarmChangePlanExtr.setOpUserId(commonProperty.getUserId());
        newAlarmChangePlanExtr.setOpUserName(commonProperty.getUserName());
        newAlarmChangePlanExtr.setOpTime(new Date());
        CommonResult commonResult = alarmChangePlanExtrRepository.addAlarmChangePlanExtr(newAlarmChangePlanExtr);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
        //endregion

    }

    /**
     * 下发信息-下发、确认信息-确认
     *
     * @param alarmChangePlanExtrEntity 报警变更方案附加信息实体
     * @return
     * <AUTHOR> 2018-01-30
     */
    @Override
    public CommonResult addAlarmChangePlanExtr(AlarmChangePlanExtrEntity alarmChangePlanExtrEntity) throws Exception {
        CommonResult commonResult = saveAlarmChangePlanExtr(alarmChangePlanExtrEntity);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        AlarmChangePlan alarmChangePlan = alarmChangePlanRepository.getOne(alarmChangePlanExtrEntity.getPlanId());
        if (alarmChangePlanExtrEntity.getBusinessType().equals(BusinessTypeEnum.Issue.getIndex())) {
            //根据“报警变更事项ID”更新<报警变更方案>状态为“4已下发”
            alarmChangePlan.setStatus(AlarmChangePlanStatusEnum.Issued.getIndex());
        } else {
            //根据“报警变更事项ID”更新<报警变更方案>状态为“5已完成”
            alarmChangePlan.setStatus(AlarmChangePlanStatusEnum.Finished.getIndex());
        }
        commonResult = alarmChangePlanRepository.update(alarmChangePlan);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 获取下发信息、获取确认信息
     *
     * @param planId       报警变更方案id
     * @param businessType 业务类型(1下发；2确认)
     * @param unitCode     装置编码
     * @return
     * <AUTHOR> 2018-01-30
     */
    @Override
    public AlarmChangePlanLinkInfoEntity getAlarmChangePlanLinkInfo(Long planId, Integer businessType, String unitCode) throws Exception {
        String sysRole = CommonPropertiesReader.getValue("sys.role");
        AlarmChangePlanExtrEntity acpeEntity = getAlarmChangePlanExtrByPlanId(planId, businessType);
        AlarmChangePlanLinkInfoEntity acpliEntity = new AlarmChangePlanLinkInfoEntity();
        if (businessType.equals(BusinessTypeEnum.Issue.getIndex())) {
            if (acpeEntity == null) {
                List<DictionaryEntity> listDic = new ArrayList<>();
                CommonProperty commonProperty = new CommonProperty();
                acpliEntity.setIssueUser(commonProperty.getUserName());
                //根据角色获取用户
                List<AAAUserEntity> users = AAAService.listUsersByRole(sysRole);
                //获取该装置查询权限执行用户
                List<String> userIds = getAuthorizedUserIdList(users.stream().map(x -> x.getUserCode().toString()).collect(Collectors.toList()), unitCode);

                users.stream().forEach(x -> {
                    if (userIds.indexOf(x.getUserCode().toString()) != -1)
                        listDic.add(new DictionaryEntity(x.getUserCode().toString(), x.getName()));
                });
                acpliEntity.setExecuteUser(listDic);
            } else {
                List<DictionaryEntity> listDic = new ArrayList<>();
                listDic.add(new DictionaryEntity(acpeEntity.getExecuteUserId(), acpeEntity.getExecuteUserName()));
                acpliEntity.setExecuteUser(listDic);
                acpliEntity.setIssueUser(acpeEntity.getOpUserName());
                acpliEntity.setIssueRemark(acpeEntity.getRemark());
            }
        } else {
            AlarmChangePlanExtrEntity acpeIssueEntity = getAlarmChangePlanExtrByPlanId(planId, BusinessTypeEnum.Issue.getIndex());
            acpliEntity.setIssueUser(acpeIssueEntity.getOpUserName());
            acpliEntity.setIssueDate(acpeIssueEntity.getOpTime());
            acpliEntity.setExecuteUserName(acpeIssueEntity.getExecuteUserName());
            acpliEntity.setIssueRemark(acpeIssueEntity.getRemark());
            if (acpeEntity != null) {
                acpliEntity.setConfirmUser(acpeEntity.getOpUserName());
                acpliEntity.setConfirmRemark(acpeEntity.getRemark());
            } else {
                CommonProperty commonProperty = new CommonProperty();
                acpliEntity.setConfirmUser(commonProperty.getUserName());
            }
        }

        return acpliEntity;
    }


    //endregion

    /**
     * 变更方案审核
     *
     * @param unitCodes 装置编码集合
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param status    状态
     * @param page      分页实体
     * @return 报警变更实体数据
     * <AUTHOR> 2018-03-13
     */
    public PaginationBean<AlarmChangePlanEntity> getAlarmChangePlanAproList(String[] unitCodes, Date startTime, Date endTime, int status, Pagination page)
            throws Exception {
        List<UnitEntity> units = null;
        if (unitCodes == null) {
            units = basicDataService.getUnitList(true);
            unitCodes = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        // 获取查询时间
        ShiftDateCalculator shiftDateCalculator = null;
        if (startTime != null && endTime != null) {
            shiftDateCalculator = new ShiftDateCalculator(startTime, endTime);
            startTime = shiftDateCalculator.getQueryStartTime();
            endTime = shiftDateCalculator.getQueryEndTime();
        } else if (startTime != null) {
            shiftDateCalculator = new ShiftDateCalculator(startTime, new Date());
            startTime = shiftDateCalculator.getQueryStartTime();
        } else if (endTime != null) {
            shiftDateCalculator = new ShiftDateCalculator(endTime);
            endTime = shiftDateCalculator.getQueryEndTime();
        }
        List<ExecuteTaskData> todoTasks = null;
        List<ExecuteTaskData> doneTasks = null;
        if (status == AlarmChangePlanApproStatusEnum.Audit.getIndex()) {
            todoTasks = workFlowService.getWorkFlowTodoList();
        }
        if (status == AlarmChangePlanApproStatusEnum.Audited.getIndex()) {
            doneTasks = workFlowService.getWorkFlowDoneList();
        }
        if (status != AlarmChangePlanApproStatusEnum.Audit.getIndex() && status != AlarmChangePlanApproStatusEnum.Audited.getIndex()) {
            todoTasks = workFlowService.getWorkFlowTodoList();
            doneTasks = workFlowService.getWorkFlowDoneList();
        }
        List<String> instanceIds = new ArrayList<>();
        if (todoTasks != null && todoTasks.size() > 0) {
            instanceIds.addAll(todoTasks.stream().map(x -> x.getInstanceId()).collect(Collectors.toList()));
        }
        if (doneTasks != null && doneTasks.size() > 0) {
            instanceIds.addAll(doneTasks.stream().map(x -> x.getInstanceId()).collect(Collectors.toList()));
        }
        if (instanceIds.size() == 0) {
            return new PaginationBean<AlarmChangePlanEntity>();
        }

        PaginationBean<AlarmChangePlan> listAlarmChangePlan = alarmChangePlanRepository.getAlarmChangePlan(unitCodes, startTime, endTime, instanceIds, page);
        PaginationBean<AlarmChangePlanEntity> returnAlarmChangePlan = new PaginationBean<>(page, listAlarmChangePlan.getTotal());
        returnAlarmChangePlan.setPageList(ObjectConverter.listConverter(listAlarmChangePlan.getPageList(), AlarmChangePlanEntity.class));
        if (listAlarmChangePlan.getPageList().size() == 0) {
            return returnAlarmChangePlan;
        }
        List<ExecuteTaskData> finalTodoTasks = todoTasks;
        List<ExecuteTaskData> finalDoneTasks = doneTasks;
        returnAlarmChangePlan.getPageList().stream().forEach(x -> {
            ExecuteTaskData executeTaskData = null;
            if (finalTodoTasks != null) {
                executeTaskData = finalTodoTasks.stream().filter(y -> x.getFlowCaseId().equals(y.getInstanceId())).findFirst().orElse(null);
            }
            if (executeTaskData != null) {
                x.setAuditStatus(AlarmChangePlanApproStatusEnum.Audit.getIndex());
                x.setAuditStatusName(AlarmChangePlanApproStatusEnum.getName(AlarmChangePlanApproStatusEnum.Audit.getIndex()));
                x.setTaskId(executeTaskData.getTaskId());
            } else {
                if (finalDoneTasks != null) {
                    executeTaskData = finalDoneTasks.stream().filter(y -> x.getFlowCaseId().equals(y.getInstanceId())).findFirst().orElse(null);
                }
                if (executeTaskData != null) {
                    x.setAuditStatus(AlarmChangePlanApproStatusEnum.Audited.getIndex());
                    x.setAuditStatusName(AlarmChangePlanApproStatusEnum.getName(AlarmChangePlanApproStatusEnum.Audited.getIndex()));
                    x.setTaskId(executeTaskData.getTaskId());
                }
            }
        });
        if (units == null) {
            // 通过公共方法获取装置
            String[] filterunitCodes = returnAlarmChangePlan.getPageList().stream().map(e -> e.getUnitId()).distinct().toArray(String[]::new);
            units = basicDataService.getUnitListByIds(filterunitCodes, false);
        }
        // 通过公共方法获取车间
        String[] filterWorkshopCodes = units.stream().map(e -> e.getWorkshopCode()).distinct().toArray(String[]::new);
        List<WorkshopEntity> workshops = basicDataService.getWorkshopListByWorkshopIds(filterWorkshopCodes);
        // 映射字段
        for (int i = 0; i < returnAlarmChangePlan.getPageList().size(); i++) {
            AlarmChangePlanEntity alarmChangePlanEntity = returnAlarmChangePlan.getPageList().get(i);
            AlarmChangePlan alarmChangePlan = listAlarmChangePlan.getPageList().get(i);
            // 填充装置简称
            UnitEntity unit = units.stream().filter(u -> alarmChangePlan.getUnitId().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
            alarmChangePlanEntity.setUnitName(unit.getSname());
            // 填充车间简称
            WorkshopEntity workshop = workshops.stream().filter(w -> unit.getWorkshopCode().equals(w.getStdCode())).findFirst().orElse(new WorkshopEntity());
            alarmChangePlanEntity.setWorkshopId(workshop.getStdCode());
            alarmChangePlanEntity.setWorkshopName(workshop.getSname());
        }
        return returnAlarmChangePlan;
    }

    /**
     * 提交报警变更方案
     *
     * @param planId       报警变更方案ID
     * @param businessId   业务ID
     * @param businessName 业务名称
     * @param aproType
     * @return
     * @throws Exception
     * <AUTHOR> 2018-3-13
     */
    @Transactional
    @Override
    public CommonResult submitAlarmChangePlan(Long planId, String businessId, String businessName, Integer aproType) throws Exception {
        CommonResult commonResult = new CommonResult();
        commonResult.setIsSuccess(true);
        commonResult.setMessage("提交成功！");
        if (aproType.intValue() != 2) {
            //1.校验数据
            if (StringUtils.isBlank(businessId) || StringUtils.isBlank(businessName)) {
                throw new Exception("业务ID和业务名称不能为空，提交失败！");
            }
            //2.获取流程分类
            SystRunParaConfEntity alarmChangePlanApro = basicDataService.getSystRunParamByCode("AlarmChangePlanApro");
            if (alarmChangePlanApro == null || StringUtils.isBlank(alarmChangePlanApro.getCode())) {
                throw new Exception("未配置流程分类参数，提交失败！");
            }
            AppCallResult appCallResult = workFlowService.startWorkFlow(alarmChangePlanApro.getParaValue(), businessId, businessName, "");
            //3.执行成功
            if (appCallResult.getResult().equals(true)) {
                AlarmChangePlan alarmChangePlanPO = alarmChangePlanRepository.getSingleAlarmChangePlan(planId);
                CommonProperty commonProperty = new CommonProperty();
                alarmChangePlanPO.setSubmitUserId(commonProperty.getUserId());
                alarmChangePlanPO.setSubmitUserName(commonProperty.getUserName());
                alarmChangePlanPO.setSubmitTime(new Date());
                alarmChangePlanPO.setFlowCaseId(appCallResult.getReferenceId());
                alarmChangePlanPO.setStatus(AlarmChangePlanStatusEnum.Submitted.getIndex());
                alarmChangePlanPO.setCategoryCode(alarmChangePlanApro.getParaValue());
                commonResult = alarmChangePlanRepository.update(alarmChangePlanPO);

                if (commonResult.getIsSuccess().equals(false)) {
                    throw new Exception(commonResult.getMessage());
                }
            }
            //4.执行失败
            else {
                throw new Exception("提交失败：" + appCallResult.getMessage());
            }
        } else { //工艺审批
            //alarmChangePlanDetail
            List<AlarmChangePlanDetail> alarmChangePlanDetailList = alarmChangePlanDetailRepository.getalarmChangePlanDetailByPlanIds(new Long[]{planId});
            //alarmChangeItem
            Long[] planDetailIds = alarmChangePlanDetailList.stream().map(x -> x.getPlanDetailId()).toArray(Long[]::new);
            List<AlarmChangeItem> alarmChangeItemList = alarmChangeItemRepository.getAlarmChangeItemByPlanDetailIds(planDetailIds);

            List<CraftAproEntity> craftAproEntityList = new ArrayList<>();
            for (AlarmChangePlanDetail e : alarmChangePlanDetailList) {
                CraftAproEntity entity = new CraftAproEntity();
                //报警标识
                entity.setAlarmFlag(e.getAlarmFlagId());
                //申请原因
                entity.setApplyReason(e.getAlarmChangePlan().getApplyReason());
                //dcsCode
                entity.setDCSTagCode(e.getAlarmPoint().getTag());
                //限值
                AlarmChangeItem alarmChangeItem = alarmChangeItemList.stream()
                        .filter(x -> x.getPlanDetailId().longValue() == e.getPlanDetailId().longValue() && x.getChangeItemTypeId().longValue() == 1)
                        .findFirst().orElse(null);
                if (alarmChangeItem != null) {
                    entity.setLimitValue(alarmChangeItem.getAfterContent());
                }
                //装置标准编码
                entity.setUnitStdCode(e.getAlarmPoint().getPrdtCell().getUnitId());
                //提交人ID
                entity.setSubmitUserId(e.getAlarmChangePlan().getSubmitUserId());
                //提交人名称
                entity.setSubmitUserName(e.getAlarmChangePlan().getSubmitUserName());
                //唯一标识
                entity.setPALUniqueId(e.getPlanDetailId().toString());
                craftAproEntityList.add(entity);
            }
            String s = JSONArray.toJSONString(craftAproEntityList);
            System.out.println(s);
            // 调用工艺接口  返回集合“操作报警唯一性标识、返回值、审批状态（0已驳回、1已通过、2已提交）”，根据返回集合新增<工艺参数变更审批记录>数据：
            OperationalAlarmCtmIndexServiceSoapProxy proxy = new OperationalAlarmCtmIndexServiceSoapProxy();
            CtmContrIndexResultExEntity[] proxyEntitys = proxy.operationalAlarmCtmIndex(JSONArray.toJSONString(craftAproEntityList));
            if (proxyEntitys.length > 0
                    && StringUtils.isNotEmpty(proxyEntitys[0].getReturnMsg())
                    && (proxyEntitys[0].getReturnMsg().contains("调用接口出错") || proxyEntitys[0].getReturnMsg().contains("工艺系统不支持工艺指标审批流程"))
            ) {
                if (commonResult.getIsSuccess()) {
                    commonResult.setIsSuccess(false);
                    commonResult.setMessage(proxyEntitys[0].getReturnMsg());
                    logger.error(proxyEntitys[0].getReturnMsg());
                }
                throw new Exception(commonResult.getMessage());
            }
            List<CtmContrIndexResultExEntity> collect = Arrays.stream(proxyEntitys).filter(x -> StringUtils.isNotEmpty(x.getPalUniqueId())).collect(Collectors.toList());
            //根据变更方案明细id查询工艺参数变更审批记录 存在记录更新该数据  不存在则新增一条数据
            List<CraftParaChangeApro> oldCraftParaChangeAproList = craftParaChangeAproRepository.getAlarmChangeItemByPlanDetail(planDetailIds);
            //新增或修改<工艺参数变更审批记录>数据
            List<CraftParaChangeApro> craftParaChangeAproList = new ArrayList<>();
            for (CtmContrIndexResultExEntity entity : collect) {
                CraftParaChangeApro po = new CraftParaChangeApro();
                CraftParaChangeApro existApro = oldCraftParaChangeAproList.stream().filter(x -> x.getPlanDetailId().longValue() == Long.parseLong(entity.getPalUniqueId())).findFirst().orElse(null);
                if (existApro != null) {
                    po.setCraftAproId(existApro.getCraftAproId());
                }
                po.setPlanDetailId(Long.parseLong(entity.getPalUniqueId()));
                Integer status = null;
                try {
                    status = Integer.parseInt(entity.getAproStatus());
                } catch (NumberFormatException e) {
                    status = 3;
                }
                switch (status) {
                    case 0:
                        status = 3;
                        break;
                    case 1:
                        status = 2;
                        break;
                    case 2:
                        status = 1;
                        break;
                }
                po.setAproStatus(status);
                po.setAproOpnion(entity.getReturnMsg());
                po.setAproTime(po.getAproStatus().intValue() == 2 ? new CommonProperty().getSystemDateTime() : null);
                craftParaChangeAproList.add(po);
            }
            if (craftAproEntityList.size() > 0) {
                commonResult = craftParaChangeAproRepository.addCraftParaChangeApro(craftParaChangeAproList);
            }

            //获取报警变更方案id 更新状态 (根据报警变更方案明细id查询报警变更方案id
            // 根据变更方案id查询所有变更方案明细id  根据变更方案明细id查询工艺参数变更审批记录)
            // 工艺参数变更审批记录 都为“2通过”  报警变更方案>状态修改为“5已完成”
            //工艺参数变更审批记录 都为“3驳回”  报警变更方案>状态修改为“1已驳回”

            Long[] allPlanDetailIds = alarmChangePlanDetailList.stream().map(x -> x.getPlanDetailId()).toArray(Long[]::new);
            //根据变更方案明细id查询工艺参数变更审批记录
            List<CraftParaChangeApro> allCraftParaChangeAproList = craftParaChangeAproRepository.getAlarmChangeItemByPlanDetail(allPlanDetailIds);
            //工艺参数变更审批记录 通过 数量
            long passCount = allCraftParaChangeAproList.stream().filter(x -> x.getAproStatus().intValue() == CraftAproStatusEnum.Pass.getIndex()).count();
            //工艺参数变更审批记录 驳回 数量
            long rejectCount = allCraftParaChangeAproList.stream().filter(x -> x.getAproStatus().intValue() == CraftAproStatusEnum.Reject.getIndex()).count();

            AlarmChangePlan alarmChangePlan = alarmChangePlanRepository.getSingleAlarmChangePlan(planId);
            CommonProperty commonProperty = new CommonProperty();
            alarmChangePlan.setSubmitUserId(commonProperty.getUserId());
            alarmChangePlan.setSubmitUserName(commonProperty.getUserName());
            alarmChangePlan.setSubmitTime(new Date());
            if (passCount == allCraftParaChangeAproList.size()) {
                alarmChangePlan.setStatus(AlarmChangePlanStatusEnum.Finished.getIndex());
            }
            if (rejectCount == allCraftParaChangeAproList.size()) {
                alarmChangePlan.setStatus(AlarmChangePlanStatusEnum.Reject.getIndex());
            }
            if (passCount < allCraftParaChangeAproList.size() && rejectCount < allCraftParaChangeAproList.size()) {
                alarmChangePlan.setStatus(AlarmChangePlanStatusEnum.Submitted.getIndex());
            }
            commonResult = alarmChangePlanRepository.update(alarmChangePlan);

            if (commonResult.getIsSuccess().equals(false)) {
                throw new Exception(commonResult.getMessage());
            }
        }
        return commonResult;
    }

    /**
     * 审批通过
     *
     * @param planId
     * @param taskId         待办ID
     * @param approveOpinion 审批意见
     * @return
     * @throws Exception
     * <AUTHOR> 2018-3-13
     */
    @Transactional
    @Override
    public CommonResult completeApprove(Long planId, String taskId, String approveOpinion) throws Exception {
        CommonResult commonResult = new CommonResult();
        commonResult.setIsSuccess(true);
        commonResult.setMessage("申请单审批通过！");
        //1.校验数据
        if (StringUtils.isBlank(taskId)) {
            throw new Exception("待办ID不能为空！");
        }
        AppCallResult appCallResult = workFlowService.runWorkFlow(taskId);
        //3.执行成功
        if (appCallResult.getResult().equals(true)) {
            AlarmChangePlanApro pojo = new AlarmChangePlanApro();
            CommonProperty commonProperty = new CommonProperty();
            pojo.setAproOpnion(approveOpinion);
            pojo.setAproStatus(AlarmChangePlanStatusEnum.Audited.getIndex());
            pojo.setAproTime(new Date());
            pojo.setAproUserId(commonProperty.getUserId());
            pojo.setAproUserName(commonProperty.getUserName());
            pojo.setPlanId(planId);
            commonResult = alarmChangePlanAproRepository.addAlarmChangePlanApro(pojo);
            if (commonResult.getIsSuccess().equals(false)) {
                throw new Exception(commonResult.getMessage());
            }
        }
        //4.执行失败
        else {
            throw new Exception("申请单审批失败：" + appCallResult.getMessage());
        }
        return commonResult;
    }

    /**
     * 审批驳回
     *
     * @param planId
     * @param taskId         待办ID
     * @param approveOpinion 审批意见
     * @return
     * @throws Exception
     * <AUTHOR> 2018-3-13
     */
    @Transactional
    @Override
    public CommonResult revertApprove(Long planId, String taskId, String approveOpinion) throws Exception {
        CommonResult commonResult = new CommonResult();
        commonResult.setIsSuccess(true);
        commonResult.setMessage("申请单驳回成功！");
        //1.校验数据
        if (StringUtils.isBlank(taskId)) {
            throw new Exception("待办ID不能为空！");
        }
        AppCallResult appCallResult = workFlowService.revertWorkFlow(taskId);
        //3.执行成功
        if (appCallResult.getResult().equals(true)) {
            AlarmChangePlanApro pojo = new AlarmChangePlanApro();
            CommonProperty commonProperty = new CommonProperty();
            pojo.setAproOpnion(approveOpinion);
            pojo.setAproStatus(AlarmChangePlanStatusEnum.Reject.getIndex());
            pojo.setAproTime(new Date());
            pojo.setAproUserId(commonProperty.getUserId());
            pojo.setAproUserName(commonProperty.getUserName());
            pojo.setPlanId(planId);
            commonResult = alarmChangePlanAproRepository.addAlarmChangePlanApro(pojo);
            if (commonResult.getIsSuccess().equals(false)) {
                throw new Exception(commonResult.getMessage());
            }
        }
        //4.执行失败
        else {
            throw new Exception("申请单驳回失败：" + appCallResult.getMessage());
        }
        return commonResult;
    }

    /**
     * 工艺审批结果回调
     *
     * @param data json数组
     * @return CommonResult 返回消息
     * @throws Exception
     * <AUTHOR> 2019-04-17
     */
    @Override
    @Transactional
    public CommonResult auditFeedback(String data) throws Exception {
        logger.info("==========工艺审批结果回调开始：==========");
        CommonResult commonResult = new CommonResult();
        if (StringUtils.isEmpty(data)) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage("传入参数不能为空！");
            logger.error("传入参数不能为空！");
            return commonResult;
        } else {
            data = data.replaceAll("PALUniqueId", "planDetailId")
                    .replaceAll("AproStatus", "aproStatus")
                    .replaceAll("AproOpnion", "aproOpnion")
                    .replaceAll("AproTime", "aproTime")
                    .replaceAll("AproUserId", "aproUserId")
                    .replaceAll("AproUserName", "aproUserName");
            //json转实体
            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            CustomJsonDateDeserializer deserializer = new CustomJsonDateDeserializer();
            SimpleModule module =
                    new SimpleModule("CustomJsonDateDeserializer",
                            new Version(1, 0, 0, null, null, null));
            module.addDeserializer(Date.class, deserializer);
            mapper.registerModule(module);
            logger.info("实体转换：" + data);
            List<CraftParaChangeAproEntity> list = mapper.readValue(data, new TypeReference<List<CraftParaChangeAproEntity>>() {
            });
            //根据planDetailId查询
            if (list != null && list.size() > 0) {
                Long[] planDetailIds = list.stream().map(x -> x.getPlanDetailId()).toArray(Long[]::new);
                List<CraftParaChangeApro> craftParaChangeAproList = craftParaChangeAproRepository.getAlarmChangeItemByPlanDetail(planDetailIds);
                //判断接口返回数据在数据库中是否存在  只更新存在的数据
                craftParaChangeAproList.forEach(craftParaChangeApro -> {
                    CraftParaChangeAproEntity entity = list.stream().filter(craftParaChangeAproEntity -> craftParaChangeAproEntity.getPlanDetailId().longValue() == craftParaChangeApro.getPlanDetailId().longValue()).findFirst().orElse(null);
                    if (entity != null) {
                        int status = entity.getAproStatus() == 0 ? CraftAproStatusEnum.Reject.getIndex() : CraftAproStatusEnum.Pass.getIndex();
                        craftParaChangeApro.setAproStatus(status);
                        craftParaChangeApro.setAproOpnion(entity.getAproOpnion());
                        craftParaChangeApro.setAproTime(entity.getAproTime());
                        craftParaChangeApro.setAproUserId(entity.getAproUserId());
                        craftParaChangeApro.setAproUserName(entity.getAproUserName());
                    }
                });
                commonResult = craftParaChangeAproRepository.addCraftParaChangeApro(craftParaChangeAproList);
                //获取报警变更方案id 更新状态 (根据报警变更方案明细id查询报警变更方案id
                // 根据变更方案id查询所有变更方案明细id  根据变更方案明细id查询工艺参数变更审批记录)
                // 工艺参数变更审批记录 都为“2通过”  报警变更方案>状态修改为“5已完成”
                //工艺参数变更审批记录 都为“3驳回”  报警变更方案>状态修改为“1已驳回”


                //根据报警变更方案明细id查询报警变更方案id
                List<AlarmChangePlanDetail> alarmChangePlanDetailsList = alarmChangePlanDetailRepository.getByIds(planDetailIds);
                if (alarmChangePlanDetailsList.size() == 0) {
                    commonResult.setIsSuccess(false);
                    commonResult.setMessage("未找到报警变更方案明细");
                    return commonResult;
                }
                if (alarmChangePlanDetailsList.size() > 0) {
                    Long planId = alarmChangePlanDetailsList.get(0).getPlanId();
                    //根据变更方案id查询所有变更方案明细id
                    alarmChangePlanDetailsList = alarmChangePlanDetailRepository.getalarmChangePlanDetailByPlanIds(new Long[]{planId});
                    Long[] allPlanDetailIds = alarmChangePlanDetailsList.stream().map(x -> x.getPlanDetailId()).toArray(Long[]::new);
                    //根据变更方案明细id查询工艺参数变更审批记录
                    List<CraftParaChangeApro> allCraftParaChangeAproList = craftParaChangeAproRepository.getAlarmChangeItemByPlanDetail(allPlanDetailIds);
                    //工艺参数变更审批记录 通过 数量
                    long passCount = allCraftParaChangeAproList.stream().filter(x -> x.getAproStatus().intValue() == CraftAproStatusEnum.Pass.getIndex()).count();
                    //工艺参数变更审批记录 驳回 数量
                    long rejectCount = allCraftParaChangeAproList.stream().filter(x -> x.getAproStatus().intValue() == CraftAproStatusEnum.Reject.getIndex()).count();

                    if (passCount == allCraftParaChangeAproList.size()) {
                        AlarmChangePlan alarmChangePlan = alarmChangePlanRepository.getSingleAlarmChangePlan(planId);
                        alarmChangePlan.setStatus(AlarmChangePlanStatusEnum.Finished.getIndex());
                        commonResult = alarmChangePlanRepository.update(alarmChangePlan);
                    }
                    if (rejectCount == allCraftParaChangeAproList.size()) {
                        AlarmChangePlan alarmChangePlan = alarmChangePlanRepository.getSingleAlarmChangePlan(planId);
                        alarmChangePlan.setStatus(AlarmChangePlanStatusEnum.Reject.getIndex());
                        commonResult = alarmChangePlanRepository.update(alarmChangePlan);
                    }
                }

            }
        }
        return commonResult;
    }

//    @Override
//    public List<CraftParaAlarmRateEntity> getCraftParaAlarmRate(String[] unitStdCodes, String stattType, Date startDate, Date endDate) throws Exception {
//
//        //将时间与页面查询时间的逻辑同步,保证数据一致性
//        Map<String, Object> searchTimeByDate = basicDataService.getSearchTimeByDate(startDate, endDate);
//        startDate = (Date) searchTimeByDate.get("startTime");
//        endDate = (Date) searchTimeByDate.get("endTime");
//        if ("<".equals(searchTimeByDate.get("endFlag").toString())) {
//            endDate = DateUtils.addSeconds(endDate, -1);
//        }
//
//        HashMap<String, CraftParaAlarmRateEntity> returnMap = new HashMap<>();
//
//        //初始化数据
//        for (String unitCode :unitStdCodes) {
//            CraftParaAlarmRateEntity entity = getCraftParaAlarmRateEntity(unitCode);
//            returnMap.put(unitCode, entity);
//        }
//
//        List<AlarmRec> alarmRecs = alarmRecRepository.getAlarmRec(startDate, endDate, unitStdCodes);
//
//        // 按照装置对数据进行分组
//        Map<String, List<AlarmRec>> unitMaps = alarmRecs.stream().collect(Collectors.groupingBy(AlarmRec::getUnitCode));
//
//        DecimalFormat df = new DecimalFormat("0.00");
//        Date finalEndDate = endDate;
//
//        //时平均报警数
//        if (stattType.contains("1")) {
//            //计算从开始到结束间隔小时数
//            double house = Math.ceil(endDate.getTime() / 1000.0 / 60 / 60 - startDate.getTime() / 1000.0 / 60 / 60);
//
//            for (Map.Entry<String, List<AlarmRec>> s : unitMaps.entrySet()) {
//                CraftParaAlarmRateEntity entity = returnMap.get(s.getKey());
//                List<AlarmRec> value = s.getValue();
//                entity.setAvgAlarmRate(Double.valueOf(df.format(value.size() / house)));
//                entity.setAvgAlarmRateNumerator(value.size());
//                entity.setAvgAlarmRateDenominator(house);
//
//            }
//        }
//
//        //24小时持续报警数
//        if (stattType.contains("2")) {
//
//            for (Map.Entry<String, List<AlarmRec>> s : unitMaps.entrySet()) {
//                CraftParaAlarmRateEntity entity = returnMap.get(s.getKey());
//                //恢复时间为空伏当前查询结束时间->过滤超过24小时的数据
//                List<AlarmRec> recs = s.getValue().stream().filter(x -> (x.getRecoveryTime() == null ? finalEndDate : x.getRecoveryTime()).getTime() - x.getAlarmTime().getTime() > 1000 * 60 * 60 * 24).collect(Collectors.toList());
//                entity.setAlarmAmount((long) recs.size());
//            }
//        }
//
//        //峰值报警率
//        if (stattType.contains("3")) {
//
//            for (Map.Entry<String, List<AlarmRec>> s : unitMaps.entrySet()) {
//                CraftParaAlarmRateEntity entity = returnMap.get(s.getKey());
//                List<AlarmRec> value = s.getValue();
//                //将时间格式化并分组
//                Map<Date, List<AlarmRec>> collect = value.stream().collect(Collectors.groupingBy(x -> this.setHours(x.getAlarmTime())));
//                //找出分组内报警数最多的集合数量
//                int size = collect.values().stream().max(Comparator.comparingInt(List::size)).orElse(Collections.emptyList()).size();
//                entity.setPeakAlarmRate((double) size);
//            }
//        }
//
//        //报警响应及时率
//        if (stattType.contains("4")) {
//
//            for (Map.Entry<String, List<AlarmRec>> s : unitMaps.entrySet()) {
//                CraftParaAlarmRateEntity entity = returnMap.get(s.getKey());
//
//                //响应时间为空伏当前查询结束时间->过滤超过30秒的数据
//                List<AlarmRec> recs = s.getValue().stream().filter(x -> (x.getResponseTime() == null ? finalEndDate : x.getResponseTime()).getTime() - x.getAlarmTime().getTime() <= 1000 * 30).collect(Collectors.toList());
//
//                if (s.getValue().isEmpty()) {
//                    entity.setAlarmTimelyResponseRate(100D);
//                } else {
//                    //响应时间为空伏当前查询结束时间->过滤超过30秒的数据
//                    entity.setAlarmTimelyResponseRate(Double.valueOf(df.format(recs.size() * 1.0 / s.getValue().size() * 100)));
//                }
//                entity.setAlarmTimelyResponseRateNumerator(recs.size());
//                entity.setAlarmTimelyResponseRateDenominator(s.getValue().size());
//            }
//        }
//
//        //报警处置及时率
//        if (stattType.contains("5")) {
//            for (Map.Entry<String, List<AlarmRec>> s : unitMaps.entrySet()) {
//                CraftParaAlarmRateEntity entity = returnMap.get(s.getKey());
//
//                //恢复时间为空伏当前查询结束时间->过滤超过30分钟的数据
//                List<AlarmRec> recs = s.getValue().stream().filter(x -> (x.getRecoveryTime() == null ? finalEndDate : x.getRecoveryTime()).getTime() - x.getAlarmTime().getTime() <= 1000 * 60 * 30).collect(Collectors.toList());
//
//                if (s.getValue().isEmpty()) {
//                    entity.setAlarmTimelyDisposalRate(100D);
//                } else {
//                    entity.setAlarmTimelyDisposalRate(Double.valueOf(df.format(recs.size() * 1.0 / s.getValue().size() * 100)));
//                }
//                entity.setAlarmTimelyDisposalRateNumerator(recs.size());
//                entity.setAlarmTimelyDisposalRateDenominator(s.getValue().size());
//            }
//        }
//
//        return new ArrayList<>(returnMap.values());
//    }


    @Override
    public List<CraftParaAlarmRateEntity> getCraftParaAlarmRate(String[] unitStdCodes, String stattType, Date startDate, Date endDate) throws Exception {

        //将时间与页面查询时间的逻辑同步,保证数据一致性
        Map<String, Object> searchTimeByDate = basicDataService.getSearchTimeByDate(startDate, endDate);
        startDate = (Date) searchTimeByDate.get("startTime");
        endDate = (Date) searchTimeByDate.get("endTime");
        if ("<".equals(searchTimeByDate.get("endFlag").toString())) {
            endDate = DateUtils.addSeconds(endDate, -1);
        }

        HashMap<String, CraftParaAlarmRateEntity> returnMap = new HashMap<>();

        //初始化数据
        for (String unitCode : unitStdCodes) {
            CraftParaAlarmRateEntity entity = getCraftParaAlarmRateEntity(unitCode);
            returnMap.put(unitCode, entity);
        }

        List<String> unitCodesList = Arrays.asList(unitStdCodes);
        AlarmRecInfoReqModel reqModel = new AlarmRecInfoReqModel();
        reqModel.setStartTime(startDate);
        reqModel.setEndTime(endDate);
        reqModel.setUnitCodeList(unitCodesList);
        List<AlarmRecInfoRespModel> alarmRecInfo = alarmRecDAO.getAlarmRecInfo(reqModel);
        // 按照装置对数据进行分组
        Map<String, List<AlarmRecInfoRespModel>> unitMaps = alarmRecInfo.stream().collect(Collectors.groupingBy(AlarmRecInfoRespModel::getUnitCode));

        DecimalFormat df = new DecimalFormat("0.00");
        Date finalEndDate = endDate;
        double house = Math.ceil(endDate.getTime() / 1000.0 / 60 / 60 - startDate.getTime() / 1000.0 / 60 / 60);

        for (Map.Entry<String, List<AlarmRecInfoRespModel>> s : unitMaps.entrySet()) {
            CraftParaAlarmRateEntity entity = returnMap.get(s.getKey());
            List<AlarmRecInfoRespModel> value = s.getValue();
            int modelSize = value.size();

            /*时平均报警数*/
            //计算从开始到结束间隔小时数
            entity.setAvgAlarmRate(Double.valueOf(df.format(modelSize / house)));
            entity.setAvgAlarmRateNumerator(modelSize);
            entity.setAvgAlarmRateDenominator(house);

            /* 24小时持续报警数 */
            //恢复时间为空伏当前查询结束时间->过滤超过24小时的数据
            List<AlarmRecInfoRespModel> alarmAmount = value.stream().filter(x ->
                    (x.getRecoveryTime() == null ? finalEndDate : x.getRecoveryTime())
                            .getTime() - x.getAlarmTime().getTime() > 1000 * 60 * 60 * 24).collect(Collectors.toList());
            entity.setAlarmAmount((long) alarmAmount.size());

            /* 峰值报警率 */
            //将时间格式化并分组
            Map<Date, List<AlarmRecInfoRespModel>> collect = value.stream().collect(Collectors.groupingBy(x ->
                    this.setHours(x.getAlarmTime())));
            //找出分组内报警数最多的集合数量
            int size = collect.values().stream().max(Comparator.comparingInt(List::size)).orElse(Collections.emptyList()).size();
            entity.setPeakAlarmRate((double) size);

            /* 报警响应及时率 */
            //响应时间为空伏当前查询结束时间->过滤超过30秒的数据
            List<AlarmRecInfoRespModel> alarmTimelyResponse = value.stream().filter(x ->
                    (x.getResponseTime() == null ? finalEndDate : x.getResponseTime())
                            .getTime() - x.getAlarmTime().getTime() <= 1000 * 30).collect(Collectors.toList());
            int alarmTimelyResponseRateNumerator = alarmTimelyResponse.size();
            if (value.isEmpty()) {
                entity.setAlarmTimelyResponseRate(100D);
            } else {
                //响应时间为空伏当前查询结束时间->过滤超过30秒的数据
                entity.setAlarmTimelyResponseRate(Double.valueOf(df.format(alarmTimelyResponseRateNumerator * 1.0 / modelSize * 100)));
            }
            entity.setAlarmTimelyResponseRateNumerator(alarmTimelyResponseRateNumerator);
            entity.setAlarmTimelyResponseRateDenominator(modelSize);

            /*报警处置及时率*/
            //恢复时间为空伏当前查询结束时间->过滤超过30分钟的数据
            List<AlarmRecInfoRespModel> alarmTimelyDisposal = value.stream().filter(x ->
                    (x.getRecoveryTime() == null ? finalEndDate : x.getRecoveryTime())
                            .getTime() - x.getAlarmTime().getTime() <= 1000 * 60 * 30).collect(Collectors.toList());
            int alarmTimelyDisposalRateNumerator = alarmTimelyDisposal.size();
            if (value.isEmpty()) {
                entity.setAlarmTimelyDisposalRate(100D);
            } else {
                entity.setAlarmTimelyDisposalRate(Double.valueOf(df.format(alarmTimelyDisposalRateNumerator * 1.0 / modelSize * 100)));
            }
            entity.setAlarmTimelyDisposalRateNumerator(alarmTimelyDisposalRateNumerator);
            entity.setAlarmTimelyDisposalRateDenominator(modelSize);

        }
        return new ArrayList<>(returnMap.values());
    }

    /**
     * 初始化数据
     */
    private static CraftParaAlarmRateEntity getCraftParaAlarmRateEntity(String unitCode) {
        CraftParaAlarmRateEntity entity = new CraftParaAlarmRateEntity();
        entity.setUnitCode(unitCode);
        entity.setAlarmAmount(0L);
        entity.setAvgAlarmRate(0d);
        entity.setAvgAlarmRateNumerator(0);
        entity.setAvgAlarmRateDenominator(0D);
        entity.setPeakAlarmRate(0d);
        entity.setAlarmTimelyResponseRate(100D);
        entity.setAlarmTimelyResponseRateNumerator(0);
        entity.setAlarmTimelyResponseRateDenominator(0);
        entity.setAlarmTimelyDisposalRate(100D);
        entity.setAlarmTimelyDisposalRateNumerator(0);
        entity.setAlarmTimelyDisposalRateDenominator(0);
        return entity;
    }

    public Date setHours(Date date) {
        //不修改原有对象
        Date date1 = new Date(date.getTime());
        date1.setMinutes(date.getHours() / 10 * 10);
        date1.setSeconds(0);
        return date1;
    }
    //region 私有方法

    /**
     * 映射显示状态
     *
     * @param status       状态
     * @param businessType 变更方案业务类型
     * @return 显示状态
     * <AUTHOR> 2018-01-29
     */
    private Integer findShowStatus(Integer status, Integer businessType) {
        if (AlarmChangeBizTypeEnum.Apply.getIndex() == businessType) {
            if (AlarmChangePlanStatusEnum.Submitted.getIndex() == status || AlarmChangePlanStatusEnum.Audited.getIndex() == status || AlarmChangePlanStatusEnum.Issued.getIndex() == status)
                status = AlarmChangeBizApplyEnum.Submitted.getIndex();
            else if (AlarmChangePlanStatusEnum.Finished.getIndex() == status)
                status = AlarmChangeBizApplyEnum.Finished.getIndex();
        } else if (AlarmChangeBizTypeEnum.Issue.getIndex() == businessType) {
            if (AlarmChangePlanStatusEnum.Audited.getIndex() == status)
                status = AlarmChangeBizIssueEnum.Issue.getIndex();
            else
                status = AlarmChangeBizIssueEnum.Issued.getIndex();
        } else {
            if (AlarmChangePlanStatusEnum.Issued.getIndex() == status)
                status = AlarmChangeBizConfirmEnum.Confirm.getIndex();
            else
                status = AlarmChangeBizConfirmEnum.Finished.getIndex();
        }
        return status;
    }

    /**
     * 获取查询状态
     *
     * @param status       状态
     * @param businessType 变更方案业务类型
     * @return 状态数组
     * <AUTHOR> 2018-01-29
     */
    private Integer[] getSearchStatuses(Integer status, Integer businessType) {
        Integer[] statuses = null;
        if (AlarmChangeBizTypeEnum.Apply.getIndex() == businessType) {
            if (status == -1)
                return null;
            else if (AlarmChangeBizApplyEnum.Submitted.getIndex() == status)
                statuses = new Integer[]{AlarmChangePlanStatusEnum.Submitted.getIndex(), AlarmChangePlanStatusEnum.Audited.getIndex(), AlarmChangePlanStatusEnum.Issued.getIndex()};
            else if (AlarmChangeBizApplyEnum.Finished.getIndex() == status)
                statuses = new Integer[]{AlarmChangePlanStatusEnum.Finished.getIndex()};
            else
                statuses = new Integer[]{status};
        } else if (AlarmChangeBizTypeEnum.Issue.getIndex() == businessType) {
            if (status == -1)
                statuses = new Integer[]{AlarmChangePlanStatusEnum.Audited.getIndex(), AlarmChangePlanStatusEnum.Issued.getIndex(), AlarmChangePlanStatusEnum.Finished.getIndex()};
            else if (AlarmChangeBizIssueEnum.Issue.getIndex() == status)
                statuses = new Integer[]{AlarmChangePlanStatusEnum.Audited.getIndex()};
            else
                statuses = new Integer[]{AlarmChangePlanStatusEnum.Issued.getIndex(), AlarmChangePlanStatusEnum.Finished.getIndex()};
        } else {
            if (status == -1)
                statuses = new Integer[]{AlarmChangePlanStatusEnum.Issued.getIndex(), AlarmChangePlanStatusEnum.Finished.getIndex()};
            else if (AlarmChangeBizConfirmEnum.Confirm.getIndex() == status)
                statuses = new Integer[]{AlarmChangePlanStatusEnum.Issued.getIndex()};
            else
                statuses = new Integer[]{AlarmChangePlanStatusEnum.Finished.getIndex()};
        }
        return statuses;
    }

    /**
     * 根据装置编码和用户ID集合 获取已授权用户ID集合
     *
     * @param userIdList 用户ID集合
     * @param unitCode   装置编码
     * @return 已授权用户ID集合
     * <AUTHOR> 2018-1-31
     */
    private List<String> getAuthorizedUserIdList(List<String> userIdList, String unitCode) {
        List<String> result = new ArrayList<>();
        if (userIdList == null || unitCode == null || userIdList.size() == 0) {
            return result;
        }
        try {
            UnitEntity unit = basicDataService.getUnitListByIds(new String[]{unitCode}, false).stream().findFirst().orElse(null);
            if (unit == null) {
                return result;
            }
            userIdList.forEach(uId -> {
                List<String> list = basicDataService.getAuthorizePropertyList(uId).stream().map(item -> item.getStdCode()).collect(Collectors.toList());
                if (list != null && list.size() != 0) {
                    if (list.indexOf(unit.getStdCode()) != -1) {
                        result.add(uId);
                    }
                }
            });
        } catch (Exception ex) {
        }
        return result;
    }

    //endregion
}
