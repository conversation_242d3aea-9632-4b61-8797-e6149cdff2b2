package com.pcitc.opal.common.bll.entity;

import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;

/*
 * 班组排班实体
 * 模块编号：pcitc_opal_bll_class_ShiftWorkTeamEntity
 * 作       者：xuelei.wang
 * 创建时间：2017-11-26
 * 修改编号：1
 * 描       述：班组排班实体
 */
@SuppressWarnings({"serial"})
public class ShiftAreaEntity extends BaseResRep implements Serializable {
    /**
     * 轮班域ID
     */
    private Long shiftAreaId;
    /**
     * 轮班域编码
     */
    private String shiftAreaCode;
    /**
     * 轮班域名称
     */
    private String name;
    /**
     * 是否启用
     */
    private Integer inUse;
    /**
     * 类型
     */
    private Integer type;

    public Long getShiftAreaId() {
        return shiftAreaId;
    }

    public void setShiftAreaId(Long shiftAreaId) {
        this.shiftAreaId = shiftAreaId;
    }

    public String getShiftAreaCode() {
        return shiftAreaCode;
    }

    public void setShiftAreaCode(String shiftAreaCode) {
        this.shiftAreaCode = shiftAreaCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getInUse() {
        return inUse;
    }

    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
