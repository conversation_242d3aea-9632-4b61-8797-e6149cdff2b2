var pmUrl = OPAL.API.pmUrl;
var dcsCodeUrl = OPAL.API.commUrl + "/getDcsCodeList";
var getCacheEventTypeList = pmUrl + '/unclaimData/getCacheEventTypeList';
var getCacheEventNameList = pmUrl + '/unclaimData/getCacheEventNameList';
var getCachePrdtCellList = pmUrl + '/unclaimData/getCachePrdtCellList';
var getCacheAlarmPointList = pmUrl + '/unclaimData/getCacheAlarmPointList';
var getCacheAlarmFlagList = pmUrl + '/unclaimData/getCacheAlarmFlagList';
var getCachePriorityList = pmUrl + '/unclaimData/getCachePriorityList';
var getCacheMeasUnitList = pmUrl + '/unclaimData/getCacheMeasUnitList';
var getMeasUnitUnmatchDataList = OPAL.API.commUrl + '/getMeasUnitUnmatchData';
var getAlarmPointUnmatchDataList = OPAL.API.commUrl + '/getAlarmPointUnmatchData';

var getUnconfiguredEventTypeList = pmUrl + '/unclaimData/getUnconfiguredEventTypeList';
var getUnconfiguredPrdtCellList = pmUrl + '/unclaimData/getUnconfiguredPrdtCellList';
var getUnconfiguredAlarmPointList = pmUrl + '/unclaimData/getUnconfiguredAlarmPointList';
var getUnconfiguredAlarmFlagList = pmUrl + '/unclaimData/getUnconfiguredAlarmFlagList';
var getUnconfiguredPriorityList = pmUrl + '/unclaimData/getUnconfiguredPriorityList';
var getUnconfiguredMeasUnitList = pmUrl + '/unclaimData/getUnconfiguredMeasUnitList';

var getPrdtCellInPrdtCellCompUrl = OPAL.API.pmUrl + "/alarmPrdtCellComp/getPrdtCellInAlarmPrdtCellComp";

var exportMeasUnitUrl = pmUrl + '/unclaimData/exportMeasUnit';
var exportEventTypeUrl = pmUrl + '/unclaimData/exportEventType';
var exportPriorityUrl = pmUrl + '/unclaimData/exportPriority';
var exportAlarmFlagUrl = pmUrl + '/unclaimData/exportAlarmFlag';
var exportAlarmPointUrl = pmUrl + '/unclaimData/exportAlarmPoint';
var exportPrdtCellUrl = pmUrl + '/unclaimData/exportPrdtCell';
$(function () {
    var page = {
        init: function () {
            this.bindUi();
            this.bindTabClick();

            page.logic.initDcsCode();
            page.logic.initQueryTime();
            page.logic.initMeasUnitUnmatchData();
            page.logic.initAlarmPointUnmatchData();

            page.logic.initTable("tbEventType");
            page.logic.initTable("tbAlarmFlag");
            page.logic.initTable("tbPrdtCell");
            page.logic.initTable("tbPriority");
            page.logic.initAlarmPointTable();
            page.logic.initMeasUnitTable();
            //默认查询数据
            setTimeout(function () {
                if ($("#dcsCodeId").val()!=null) {
                    page.logic.search();
                }
            }, 500);

        },
        bindUi: function () {
            $('#export').click(function () {
                var id = $("#navTab li.active").attr("id");

                //1.提示是否继续
                layer.confirm('数据量较大，导出时间可能较长，是否继续？', {
                    btn: ['是', '否'],
                    title: '提示',
                }, function (index) {
                    layer.close(index);
                    if (id == "liEventType") {
                        page.logic.exportExcel("tbEventType", exportEventTypeUrl);
                    } else if (id == "liAlarmPoint") {
                        page.logic.exportExcel("tbAlarmPoint", exportAlarmPointUrl);
                    }
                    else if (id == "liPrdtCell") {
                        page.logic.exportExcel("tbPrdtCell", exportPrdtCellUrl);
                    }
                    else if (id == "liAlarmFlag") {
                        page.logic.exportExcel("tbAlarmFlag", exportAlarmFlagUrl);
                    }
                    else if (id == "liPriority") {
                        page.logic.exportExcel("tbPriority", exportPriorityUrl);
                    }
                    else if (id == "liMeasUnit") {
                        page.logic.exportExcel("tbMeasUnit", exportMeasUnitUrl);
                    }
                }, function (index) {
                    layer.close(index);
                });
            });

            $('#btnQuery').click(function () {
                page.logic.search();
            });
        },
        /**
         * 页签切换事件
         */
        bindTabClick: function () {
            $("#navTab li").click(function () {
                var id = $(this).attr('id');
                if (id == "liEventType") {
                    $("#divFormEventNameSource").css("display", "inline");
                    $("#divFormEventTypeSource").css("display", "inline");

                    $("#divFormAlarmFlag").css("display", "none");
                    $("#divFormAlarmPoint").css("display", "none");
                    $("#divFormPrdtCell").css("display", "none");
                    $("#divFormPriority").css("display", "none");
                    $("#divFormMeasUnit").css("display", "none");
                    $("#divFormAlarmPointDifReason").css("display", "none");
                    $("#divFormMeasUnitDifReason").css("display", "none");
                } else if (id == "liAlarmPoint") {
                    $("#divFormAlarmPoint").css("display", "inline");
                    $("#divFormAlarmPointDifReason").css("display", "inline");

                    $("#divFormAlarmFlag").css("display", "none");
                    $("#divFormPrdtCell").css("display", "none");
                    $("#divFormPriority").css("display", "none");
                    $("#divFormEventNameSource").css("display", "none");
                    $("#divFormEventTypeSource").css("display", "none");
                    $("#divFormMeasUnit").css("display", "none");
                    $("#divFormMeasUnitDifReason").css("display", "none");
                }
                else if (id == "liPrdtCell") {
                    $("#divFormPrdtCell").css("display", "inline");

                    $("#divFormAlarmPoint").css("display", "none");
                    $("#divFormAlarmFlag").css("display", "none");
                    $("#divFormPriority").css("display", "none");
                    $("#divFormEventNameSource").css("display", "none");
                    $("#divFormEventTypeSource").css("display", "none");
                    $("#divFormMeasUnit").css("display", "none");
                    $("#divFormAlarmPointDifReason").css("display", "none");
                    $("#divFormMeasUnitDifReason").css("display", "none");
                }
                else if (id == "liAlarmFlag") {
                    $("#divFormAlarmFlag").css("display", "inline");

                    $("#divFormPrdtCell").css("display", "none");
                    $("#divFormAlarmPoint").css("display", "none");
                    $("#divFormPriority").css("display", "none");
                    $("#divFormEventNameSource").css("display", "none");
                    $("#divFormEventTypeSource").css("display", "none");
                    $("#divFormMeasUnit").css("display", "none");
                    $("#divFormAlarmPointDifReason").css("display", "none");
                    $("#divFormMeasUnitDifReason").css("display", "none");
                }
                else if (id == "liPriority") {
                    $("#divFormPriority").css("display", "inline");

                    $("#divFormAlarmFlag").css("display", "none");
                    $("#divFormPrdtCell").css("display", "none");
                    $("#divFormAlarmPoint").css("display", "none");
                    $("#divFormEventNameSource").css("display", "none");
                    $("#divFormEventTypeSource").css("display", "none");
                    $("#divFormMeasUnit").css("display", "none");
                    $("#divFormAlarmPointDifReason").css("display", "none");
                    $("#divFormMeasUnitDifReason").css("display", "none");
                }
                else if (id == "liMeasUnit") {
                    $("#divFormMeasUnit").css("display", "inline");
                    $("#divFormMeasUnitDifReason").css("display", "inline");

                    $("#divFormPriority").css("display", "none");
                    $("#divFormAlarmFlag").css("display", "none");
                    $("#divFormPrdtCell").css("display", "none");
                    $("#divFormAlarmPoint").css("display", "none");
                    $("#divFormEventNameSource").css("display", "none");
                    $("#divFormEventTypeSource").css("display", "none");
                    $("#divFormAlarmPointDifReason").css("display", "none");
                }
            });
        },
        data: {
            param: {}
        },
        logic: {
            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            search: function() {
                if (!OPAL.util.checkDateIsValid()) {
                    return false;
                }
                var id = $("#navTab li.active").attr("id");
                page.data.param = OPAL.form.getData('searchForm');
                if (id == "liEventType") {
                    $("#tbEventType").bootstrapTable('refresh', {
                        "url": getUnconfiguredEventTypeList,
                        "pageNumber": 1
                    });
                    page.logic.initEventType();
                } else if (id == "liAlarmPoint") {
                    page.logic.initAlarmPoint();
                    $("#tbAlarmPoint").bootstrapTable('refresh', {
                        "url": getUnconfiguredAlarmPointList,
                        "pageNumber": 1
                    });
                }
                else if (id == "liPrdtCell") {
                    $("#tbPrdtCell").bootstrapTable('refresh', {
                        "url": getUnconfiguredPrdtCellList,
                        "pageNumber": 1
                    });
                    page.logic.initPrdtCell();
                }
                else if (id == "liAlarmFlag") {
                    $("#tbAlarmFlag").bootstrapTable('refresh', {
                        "url": getUnconfiguredAlarmFlagList,
                        "pageNumber": 1
                    });
                    page.logic.initAlarmFlag();
                }
                else if (id == "liPriority") {
                    $("#tbPriority").bootstrapTable('refresh', {
                        "url": getUnconfiguredPriorityList,
                        "pageNumber": 1
                    });
                    page.logic.initPriority();
                }
                else if (id == "liMeasUnit") {
                    page.logic.initMeasUnit();
                    page.data.param.measUnit = $("#measUnit").val();
                    $("#tbMeasUnit").bootstrapTable('refresh', {
                        "url": getUnconfiguredMeasUnitList,
                        "pageNumber": 1
                    });
                }
            },
            /**
             * 初始化事件类型列表
             *
             * @param ctrlID 控件ID
             * <AUTHOR>
             */
            initTable: function (ctrlID) {
                OPAL.ui.initBootstrapTable(ctrlID, {
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            var tableOption = $('#' + ctrlID).bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            return (index + 1) + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "写入时间",
                        field: 'writeTime',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "DCS名称",
                        field: 'dcsName',
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "事件类型",
                        field: 'eventType',
                        align: 'center',
                        width: '200px'
                    }, {
                        title: "事件名称",
                        field: 'eventName',
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCell',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "报警点",
                        field: 'alarmPoint',
                        rowspan: 1,
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "描述",
                        field: 'des',
                        rowspan: 1,
                        align: 'left',
                        width: '200px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlag',
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "优先级",
                        field: 'priority',
                        rowspan: 1,
                        align: 'center',
                        width: '60px'
                    }, {
                        title: "发生时间",
                        field: 'startTime',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "报警时间",
                        field: 'alarmTime',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }],
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    }
                }, page.logic.queryParams)
            },
            /**
             * 初始化报警点列表
             */
            initAlarmPointTable: function () {
                OPAL.ui.initBootstrapTable("tbAlarmPoint", {
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            var tableOption = $('#tbAlarmPoint').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            return (index + 1) + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "写入时间",
                        field: 'writeTime',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "DCS名称",
                        field: 'dcsName',
                        align: 'left',
                        width: '140px'
                    }, {
                        title: "事件类型",
                        field: 'eventType',
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "事件名称",
                        field: 'eventName',
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCell',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "报警点",
                        field: 'alarmPoint',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "描述",
                        field: 'des',
                        rowspan: 1,
                        align: 'left',
                        width: '200px'
                    }, {
                        title: "发生时间",
                        field: 'startTime',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "不一致原因",
                        field: 'alarmPointReasonName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '100px',
                        formatter: page.logic.onActionRenderer
                    }],
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    }
                }, page.logic.queryParams);
            }, 

            /**
             * 初始化计量单位列表
             */
            initMeasUnitTable: function () {
                OPAL.ui.initBootstrapTable("tbMeasUnit", {
                    cache: false,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            var tableOption = $('#tbMeasUnit').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            return (index + 1) + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "写入时间",
                        field: 'writeTime',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "DCS名称",
                        field: 'dcsName',
                        align: 'left',
                        width: '140px'
                    }, {
                        title: "事件类型",
                        field: 'eventType',
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "事件名称",
                        field: 'eventName',
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCell',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "报警点",
                        field: 'alarmPoint',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "描述",
                        field: 'des',
                        rowspan: 1,
                        align: 'left',
                        width: '200px'
                    }, {
                        title: "计量单位",
                        field: 'measUnit',
                        rowspan: 1,
                        align: 'center',
                        width: '70px'
                    }, {
                        title: "发生时间",
                        field: 'startTime',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "不一致原因",
                        field: 'resonName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }],
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    }
                }, page.logic.queryParams);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor" class="btn-config"  href="javascript:window.page.logic.setAlarmPoint(\'' + rowData.prdtCell + '\', \'' + rowData.alarmPoint + '\','+ rowData.reson+','+rowData.opcCode+','+rowData.dcsCode+')">配置</a>'
                ]
            },
            /**
             * 初始化DcsCode
             */
            initDcsCode: function () {
                OPAL.ui.getCombobox("dcsCodeId", dcsCodeUrl, {
                    keyField: "dcsCodeId",
                    valueField: "name",
                    data: {
                        isAll: false
                    },
                    selectFirstRecord: true
                }, null);
            },
            /**
             * 初始化事件类型
             */
            initEventType: function () {
                var tmp = $("#eventTypeSource").val();
                OPAL.ui.getCombobox("eventTypeSource", getCacheEventTypeList, {
                    keyField: "parameter",
                    valueField: "des",
                    selectFirstRecord: true,
                    data: {
                        eventType: $("#eventTypeSource").val(),
                        dcsCodeId: $("#dcsCodeId").val(),
                        startTime: $("#startTime").val(),
                        endTime: $("#endTime").val()
                    }
                }, function(){
                    page.logic.echoSelect("eventTypeSource", tmp);
                }, function (value) {
                    if (value != "-1") {
                        page.logic.initEventName(value);
                    } else {
                        $("#eventNameSource").empty();
                    }
                });
            },
            /**
             * 初始化事件名称
             * @param eventType 事件类型
             */
            initEventName: function (eventType) {
                OPAL.ui.getCombobox("eventNameSource", getCacheEventNameList, {
                    keyField: "parameter",
                    valueField: "des",
                    selectFirstRecord: true,
                    data: {
                        eventName: -1,
                        eventType: eventType,
                        dcsCodeId: $("#dcsCodeId").val(),
                        startTime: $("#startTime").val(),
                        endTime: $("#endTime").val()
                    }
                }, null, null);
            },
            /**
             * 初始化生产单元
             */
            initPrdtCell: function () {
                var tmp = $("#prdtCell").val();
                OPAL.ui.getCombobox("prdtCell", getCachePrdtCellList, {
                    keyField: "parameter",
                    valueField: "des",
                    selectFirstRecord: true,
                    data: {
                        prdtCell: $("#prdtCell").val(),
                        dcsCodeId: $("#dcsCodeId").val(),
                        startTime: $("#startTime").val(),
                        endTime: $("#endTime").val()
                    }
                }, function () {
                    page.logic.echoSelect("prdtCell", tmp);
                }, null);
            },
            /**
             * 初始化报警点
             */
            initAlarmPoint: function () {
                var tmp = $("#alarmPoint").val();
                OPAL.ui.getCombobox("alarmPoint", getCacheAlarmPointList, {
                    keyField: "parameter",
                    valueField: "alarmPoint",
                    selectFirstRecord: true,
                    async: false,
                    data: {
                        alarmPoint: $("#alarmPoint").val(),
                        dcsCodeId: $("#dcsCodeId").val(),
                        alarmPointDifReason: $("#alarmPointDifReason").val(),
                        startTime: $("#startTime").val(),
                        endTime: $("#endTime").val()
                    }
                }, function () {
                    page.logic.echoSelect("alarmPoint", tmp);
                    
                }, null);
            },
            /**
             * 初始化报警标识
             */
            initAlarmFlag: function () {
                var tmp = $("#alarmFlag").val();
                OPAL.ui.getCombobox("alarmFlag", getCacheAlarmFlagList, {
                    keyField: "parameter",
                    valueField: "des",
                    selectFirstRecord: true,
                    data: {
                        alarmFlag: $("#alarmFlag").val(),
                        dcsCodeId: $("#dcsCodeId").val(),
                        startTime: $("#startTime").val(),
                        endTime: $("#endTime").val()
                    }
                }, function () {
                    page.logic.echoSelect("alarmFlag", tmp);
                }, null);
            },
            /**
             * 初始化优先级
             */
            initPriority: function () {
                var tmp = $("#priority").val();
                OPAL.ui.getCombobox("priority", getCachePriorityList, {
                    keyField: "parameter",
                    valueField: "des",
                    selectFirstRecord: true,
                    data: {
                        priority: $("#priority").val(),
                        dcsCodeId: $("#dcsCodeId").val(),
                        startTime: $("#startTime").val(),
                        endTime: $("#endTime").val()
                    }
                }, function () {
                    page.logic.echoSelect("priority", tmp);
                }, null);
            },
            /**
             * 初始化计量单位
             */
            initMeasUnit: function () {
                var tmp = $("#measUnit").val();
                OPAL.ui.getCombobox("measUnit", getCacheMeasUnitList, {
                    keyField: "parameter",
                    valueField: "measUnit",
                    selectFirstRecord: true,
                    async: false,
                    data: {
                        dcsCodeIds: new Array($("#dcsCodeId").val()),
                        measUnit: $("#measUnit").val(),
                        difReason: $("#difReason").val(),
                        startTime: $("#startTime").val(),
                        endTime: $("#endTime").val()
                    }
                }, function () {
                    page.logic.echoSelect("measUnit", tmp);
                }, null);
            },
            //下拉框回显
            echoSelect: function (id, originalValue) {
                $("#" + id).val(originalValue);
                if ($("#" + id).val() == '' || $("#" + id).val() == null) {
                    var arr = new Array();
                    $("#" + id + " option").each(function () {
                        arr.push($(this).val());
                    })
                    if (arr.length == 2) {
                        $("#" + id).val(arr[1]);
                    } else {
                        $("#" + id).val("-1");
                    }
                }
            },
            /**
             * 初始化报警点不一致原因
             */
            initAlarmPointUnmatchData: function () {
                OPAL.ui.getCombobox("alarmPointDifReason", getAlarmPointUnmatchDataList, {
                    selectFirstRecord: true
                }, null, null);
            },
            /**
             * 初始化计量单位不一致原因
             */
            initMeasUnitUnmatchData: function () {
                OPAL.ui.getCombobox("difReason", getMeasUnitUnmatchDataList, {
                    selectFirstRecord: true
                }, null, null);
            },
            /**
             * 初始化查询时间
             */
            initQueryTime: function () {
                OPAL.util.getSysDateTime(function (date) {
                    var startTime = moment(date).format("YYYY-01-01");
                    var endTime = moment(date).format("YYYY-MM-DD");
                    laydate.render({
                        elem: '#startTime',
                        type: 'date',
                        trigger: 'click',
                        btns: ['clear', 'confirm'],
                        format: 'yyyy-MM-dd',
                        value: startTime,
                        max: endTime,
                    });
                    laydate.render({
                        elem: '#endTime',
                        type: 'date',
                        trigger: 'click',
                        btns: ['clear', 'confirm'],
                        format: 'yyyy-MM-dd',
                        value: endTime,
                        max: endTime,
                    });
                    $('#startTime').attr('maxDate', endTime);
                    $('#endTime').attr('maxDate', endTime);
                });
            },
            /**
             * 报警点设置
             * @param alarmPoint
             */
            setAlarmPoint: function (prdtCell, tag,reason,opcCode,dcsCode) {
                if (tag == '') {
                    layer.msg("报警点为空！");
                    return;
                }
                if (reason == 2) {
                    layer.msg("报警事件缓存表中生产单元与报警点中生产单元不一致!");
                    return;
                }
                $.ajax({
                    url: getPrdtCellInPrdtCellCompUrl,
                    async: true,
                    dataType: "JSON",
                    data: {"prdtCellSource": prdtCell,"opcCode":opcCode,"dcsCode":dcsCode},
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        layer.open({
                            type: 2,
                            title: '报警点配置',
                            closeBtn: 1,
                            area: ['1000px', '81%'],
                            shadeClose: false,
                            offset: '30px',
                            content: '../AlarmPoint/AlarmPointAddOrEdit.html?' + Math.random(),
                            success: function (layero, index) {
                                var body = layer.getChildFrame('body', index);
                                var iframeWin = window[layero.find('iframe')[0]['name']];
                                var setData = {
                                    "unitId": dataArr[0]["unitId"],
                                    "prdtCellId": dataArr[0]["prdtCellId"],
                                    "tag": tag,
                                    "pageMode": PageModelEnum.View
                                };
                                iframeWin.page.logic.setData(setData);
                            },
                            end: function () {
                                if (window.pageLoadMode == PageLoadMode.Refresh) {
                                    $("#tbAlarmPoint").bootstrapTable('refresh', {
                                        "url": getUnconfiguredAlarmPointList,
                                        "pageNumber": 1
                                    });
                                } else if (window.pageLoadMode == PageLoadMode.Reload) {
                                    $('#tbAlarmPoint').bootstrapTable('selectPage', 1);
                                }
                            }
                        })
                    },
                    error: function (result) {
                        layer.msg("生产单元尚未配置，请先配置生产单元的对应规则！");
                    }
                })
            },
            /**
             * 导出Excel
             *
             * @param tableId   列表ID
             * @param actionURL 导出Excel表单Form ID
             * <AUTHOR> 2018-04-24
             */
            exportExcel: function (tableId, actionURL) {
                var titleArray = new Array();
                var tableTitle = $('#' + tableId).bootstrapTable('getOptions').columns[0];
                $.each(tableTitle, function (i, el) {
                    if(el.title!='序号'&&el.title!='操作'){
                        titleArray.push({
                            'key': el.field,
                            'value': el.title
                        });
                    }
                });
                var data = OPAL.form.getData('searchForm');
                data.titles = JSON.stringify(titleArray);
                OPAL.form.setExportExcelData('formExportExcel', data);

                $('#formExportExcel').attr('action', actionURL);
                $('#formExportExcel').submit();
            },
        }
    };
    page.init();
    window.page = page;
})