var addUrl = OPAL.API.pmUrl + '/mobileList';
var getSingleUrl = OPAL.API.pmUrl + '/mobileList';
var workshopUrl = OPAL.API.pmUrl + '/mobileList' +"/getWorkshopListByFactoryId";
var factoryUrl = OPAL.API.pmUrl + '/unit/getFactoryList';    //工厂
var workshopUrl = OPAL.API.pmUrl + '/unit/getWorkshopListByFactoryId';   //车间
var unitUrl = OPAL.API.pmUrl + "/alarmMsgConfig/getUnitListByWorkshopId";   //装置
var pageMode = PageModelEnum.NewAdd;
var selectValue='';
window.pageLoadMode = PageLoadMode.Refresh;
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
            page.logic.initFactory();
        },
        bindUI: function () {
            $('#saveAddModal').click(function () {
                page.logic.save();
            });
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
            $('#closePage').click(function () {
                page.logic.closeLayer(false);
            })
        },
        logic: {
            /**
             * 保存
             */
            save: function () {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data=OPAL.form.getETCollectionData("AddOrEditModal");
                //处理提交类型
                var ajaxType = "POST";
                if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                }

                $.ajax({
                    url: addUrl,
                    async: false,
                    type: ajaxType,
                    data: JSON.stringify(data),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg(result,{
                                time: 1000
                            },function() {
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                $("#pageTitle").text(data.title);
                if (pageMode == PageModelEnum.NewAdd) {
                    return;
                }
                $.ajax({
                    url: getSingleUrl + "/" + data.mobileListId + "?now=" + Math.random(),
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function (data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        //加载车间有延迟,使用配置方式
                        selectValue=entity['unitCode'];
                        OPAL.form.setData('AddOrEditModal', entity);
                    }
                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                parent.layer.close(index);
            },
            /**
             * 表单校验
             */
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal',{
                    rules: {
                        factoryId: {
                            required: true
                        },
                        name: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        mobile: {
                            required: true,
                            rangelength: [0, 11]
                        }
                    }
                });
            },
            /**
             * 初始化工厂
             */
            initFactory: function () {
                OPAL.ui.getCombobox("factoryId", factoryUrl, {
                    keyField: "factoryId",
                    valueField: "sname",
                    selectFirstRecord: true,
                    data: {
                        isAll: false
                    }
                }, function (data) {
                    // if ($("#factoryId").val()!=null && $("#factoryId").val()!='')
                    //     page.logic.initWorkshop($("#workshopId").val());
                }, function (selectedValue) {
                    if (selectedValue != '' &&selectedValue!=null){
                        page.logic.initWorkshop(selectedValue);
                    }else{
                        $("#workshopId").val([]);
                        $("#workshopId option").remove();
                        $("#unitCode").val([]);
                        $("#unitCode option").remove();
                    }
                });
            },
            /**
             * 初始化车间
             */
            initWorkshop: function (factoryId) {
                OPAL.ui.getCombobox("workshopId", workshopUrl, {
                    keyField: "workshopId",
                    valueField: "sname",
                    selectFirstRecord: false,
                    data: {
                        factoryId: factoryId,
                        isAll: false
                    }
                },function () {
                }, function (selectedValue) {
                    if (selectedValue != '' &&selectedValue!=null && $("#workshopId").val()!=-1){
                        page.logic.initUnit(selectedValue);
                    }else{
                        $("#unitCode").val([]);
                        $("#unitCode option").remove();
                    }
                })
            },
            /**
             * 初始化装置
             */
            initUnit: function(workshopId) {
                OPAL.ui.getCombobox("unitCode", unitUrl , {
                    keyField: "stdCode",
                    valueField: "sname",
                    selectFirstRecord: false,
                    selectValue:selectValue,
                    data: {
                        workshopId: workshopId,
                        isAll: false
                    }
                },null,null)
            },
        }

    }
    page.init();
    window.page = page;
})