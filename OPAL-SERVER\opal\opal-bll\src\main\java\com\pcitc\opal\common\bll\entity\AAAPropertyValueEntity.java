package com.pcitc.opal.common.bll.entity;


import java.io.Serializable;

/*
 * AAAAuthPropertyValueEntity实体,对新旧AAA AuthPropertyValue实体进行封装
 * 模块编号： pcitc_opal_bll_class_AAAAuthPropertyValueEntity
 * 作       者：xuelei.wang
 * 创建时间：2018/3/2
 * 修改编号：1
 * 描       述：AAAAuthPropertyValueEntity实体,对新旧AAA AuthPropertyValue实体进行封装
 */
public class AAAPropertyValueEntity implements Serializable {
    /**
     * 名称
     */
    private String name;
    /**
     * 排序编号
     */
    private Long orderId;
    /**
     * parentID
     */
    private Long parentId;
    /**
     * 属性ID
     */
    private Long propertyValueId;
    /**
     * 资源ID
     */
    private String sourceId;
    /**
     * 值
     */
    private String value;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getPropertyValueId() {
        return propertyValueId;
    }

    public void setPropertyValueId(Long propertyValueId) {
        this.propertyValueId = propertyValueId;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
