package com.pcitc.opal.ad.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AlarmAnlyRecExportParamVO implements Serializable {

    private List<String> unitIds;

    private List<Long> prdtCellIds;

    private Long alarmFlagId;

    private String tag;

    private Integer alarmStatus;

    private Integer anlyStatus;

    private List<Integer> prioritys;

    private List<Integer> monitorType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
