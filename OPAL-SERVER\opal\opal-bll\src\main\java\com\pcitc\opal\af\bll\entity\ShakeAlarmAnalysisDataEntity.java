package com.pcitc.opal.af.bll.entity;

import com.pcitc.opal.common.CommonEnum;

/*
 * 震荡报警分析数据实体
 * 模块编号：pcitc_opal_bll_class_ShakeAlarmAnalysisDataEntity
 * 作  　  者：kun.zhao
 * 创建时间：2017-11-2
 * 修改编号：1
 * 描      述：震荡报警分析数据实体
 */
public class ShakeAlarmAnalysisDataEntity {
	/**
	 * 报警点位号名称
	 */
	private String alarmPointTag;
	/**
	 * 报警标识名称
	 */
	private String alarmFlagName;
	/**
	 * 报警标识ID
	 */
	private Long alarmFlagId;
	/**
	 * 报警数
	 */
	private Long count;
	/**
	 * 装置名称
	 */
	private String unitName;
	/**
	 * 生产单元名称
	 */
	private String prdtName;
	/**
     * 级别(1A；2B)
     */
    private Integer craftRank;
    /**
     * 级别(1A；2B)
     */
    @SuppressWarnings("unused")
	private String craftRankName;
	/**
	 * 限值
	 */
	private String limitValue;
	/**
	 * 报警点ID
	 */
	private Long alarmPointId;

	/**
	 * 位置
	 */
	private String location;

	public String getAlarmPointTag() {
		return alarmPointTag;
	}
	public void setAlarmPointTag(String alarmPointTag) {
		this.alarmPointTag = alarmPointTag;
	}
	public String getAlarmFlagName() {
		return alarmFlagName;
	}
	public void setAlarmFlagName(String alarmFlagName) {
		this.alarmFlagName = alarmFlagName;
	}
	public Long getAlarmFlagId() {
		return alarmFlagId;
	}
	public void setAlarmFlagId(Long alarmFlagId) {
		this.alarmFlagId = alarmFlagId;
	}
	public Long getCount() {
		return count;
	}
	public void setCount(Long count) {
		this.count = count;
	}
	public String getUnitName() {
		return unitName;
	}
	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}
	public String getPrdtName() {
		return prdtName;
	}
	public void setPrdtName(String prdtName) {
		this.prdtName = prdtName;
	}
	public Integer getCraftRank() {
		return craftRank;
	}
	public void setCraftRank(Integer craftRank) {
		this.craftRank = craftRank;
	}
	public String getCraftRankName() {
        if (craftRank == null) return "";
        return CommonEnum.CraftRankEnum.getName(craftRank);
    }
    public void setCraftRankName(String craftRankName) {
        this.craftRankName = craftRankName;
    }

	public Long getAlarmPointId() {
		return alarmPointId;
	}

	public void setAlarmPointId(Long alarmPointId) {
		this.alarmPointId = alarmPointId;
	}

	public String getLimitValue() {
		return limitValue;
	}

	public void setLimitValue(String limitValue) {
		this.limitValue = limitValue;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}
}
