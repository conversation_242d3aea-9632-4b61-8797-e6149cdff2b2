package com.pcitc.opal.af.bll.imp;

import java.text.Collator;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pcitc.opal.common.CommonEnum.DateTypeEnum;
import com.pcitc.opal.common.CommonEnum.PageModelEnum;
import com.pcitc.opal.af.bll.RelevantTagConfigService;
import com.pcitc.opal.af.bll.entity.RelevantAlarmAnalysisEntity;
import com.pcitc.opal.af.bll.entity.RelevantAlarmTableEntity;
import com.pcitc.opal.af.bll.entity.RelevantTagConfigDtlEntity;
import com.pcitc.opal.af.bll.entity.RelevantTagConfigEntity;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.CommonUtil;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.pm.bll.entity.AlarmPointEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.dao.AlarmPointRepository;
import com.pcitc.opal.pm.dao.RelevantAlarmAnalysisRepository;
import com.pcitc.opal.pm.dao.RelevantTagConfigDtlRepository;
import com.pcitc.opal.pm.dao.RelevantTagConfigRepository;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import com.pcitc.opal.pm.pojo.MeasUnit;
import com.pcitc.opal.pm.pojo.PrdtCell;
import com.pcitc.opal.pm.pojo.RelevantTagConfig;
import com.pcitc.opal.pm.pojo.RelevantTagConfigDtl;

import pcitc.imp.common.ettool.utils.ObjectConverter;

/*
 * 相关性位号配置业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_RelevantTagConfigImpl
 * 作       者：dageng.sun
 * 创建时间：2018/8/1
 * 修改编号：1
 * 描       述：相关性位号配置业务逻辑层实现类
 */
@Service
public class RelevantTagConfigImpl implements RelevantTagConfigService {
	
	@Autowired
    private RelevantTagConfigRepository relevantTagConfigRepo;
	@Autowired
    protected BasicDataService basicDataService;
	@Autowired
    private RelevantTagConfigDtlRepository relevantTagConfigDtlRepo;
	@Autowired
	private AlarmPointRepository alarmPointRepo;
	@Autowired
	private RelevantAlarmAnalysisRepository relevantAlarmAnalysisRepo;

	/**
	 * 新增相关性位号配置
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigEntity 相关性位号配置实体
	 * @throws Exception 抛出异常
	 */
	@Override
	public CommonResult addRelevantTagConfig(RelevantTagConfigEntity relevantTagConfigEntity) throws Exception {
		// 实体转换为持久层实体
		RelevantTagConfig relevantTagConfigPO = ObjectConverter.entityConverter(relevantTagConfigEntity, RelevantTagConfig.class);
		// 赋值 创建人、创建名称、创建时间
		CommonUtil.returnValue(relevantTagConfigPO, PageModelEnum.NewAdd.getIndex());
		CommonResult commonResult = relevantTagConfigRepo.addRelevantTagConfig(relevantTagConfigPO);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false){
			throw new Exception(commonResult.getMessage());
		}
		return commonResult;
	}

	/**
	 * 删除相关性位号配置数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigIds 相关性位号配置主键id集合
	 * @throws Exception 抛出异常
	 */
	@Override
	public CommonResult deleteRelevantTagConfig(Long[] relevantTagConfigIds) throws Exception {
		// 判断ID集合是否可用
		if (relevantTagConfigIds == null || relevantTagConfigIds.length <= 0) {
			throw new Exception("没有需要删除的相关性位号配置数据！");
		}
		List<RelevantTagConfig> anlyRelevantTagConfigList = relevantTagConfigRepo.getRelevantTagConfig(relevantTagConfigIds);
		if (anlyRelevantTagConfigList == null || anlyRelevantTagConfigList.isEmpty())
			return new CommonResult();
		Long[] anlyRelevantTagConfigIdList = anlyRelevantTagConfigList.stream().map(item -> item.getRelevantTagConfigId()).toArray(Long[]::new);
		// 调用DAL删除方法
		CommonResult commonResult = relevantTagConfigRepo.deleteRelevantTagConfig(anlyRelevantTagConfigIdList);

		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
		return commonResult;
	}

	/**
	 * 修改相关性位号配置
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigEntity 相关性位号配置实体
	 * @throws Exception 抛出异常
	 */
	@Override
	public CommonResult updateRelevantTagConfig(RelevantTagConfigEntity relevantTagConfigEntity) throws Exception {
		// 实体转换持久层实体
		RelevantTagConfig relevantTagConfigPO = ObjectConverter.entityConverter(relevantTagConfigEntity, RelevantTagConfig.class);
		// 实体转换为持久层实体
		relevantTagConfigPO = relevantTagConfigRepo.getSingleRelevantTagConfig(relevantTagConfigPO.getRelevantTagConfigId());
		CommonUtil.objectExchange(relevantTagConfigEntity, relevantTagConfigPO);
		// 赋值 修改人、修改名称、修改时间
		CommonUtil.returnValue(relevantTagConfigPO, PageModelEnum.Edit.getIndex());
		// 调用DAL更新方法
		CommonResult commonResult = relevantTagConfigRepo.updateRelevantTagConfig(relevantTagConfigPO);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
		return commonResult;
	}

	/**
	 * 通过相关性位号配置ID获取单条数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigId 相关性位号配置主键id
	 * @throws Exception 抛出异常
	 */
	@Override
	public RelevantTagConfigEntity getSingleRelevantTagConfig(Long relevantTagConfigId) throws Exception {
		RelevantTagConfig relevantTagConfig = relevantTagConfigRepo.getSingleRelevantTagConfig(relevantTagConfigId);
		RelevantTagConfigEntity rtce = ObjectConverter.entityConverter(relevantTagConfig, RelevantTagConfigEntity.class);
		AlarmPoint ap = relevantTagConfig.getAlarmpoint();
		rtce.setTag(ap.getTag());
		PrdtCell pc = ap.getPrdtCell();
		rtce.setPrdtCellId(ap.getPrdtCellId());
		rtce.setPrdtCellSname(pc.getSname());
		List<UnitEntity> unitEntityList = basicDataService.getUnitListByIds(new String[]{pc.getUnitId()}, true);
		if(unitEntityList!=null && !unitEntityList.isEmpty() && unitEntityList.size()>0){
			UnitEntity ue = unitEntityList.get(0);
			rtce.setUnitId(ue.getStdCode());
			rtce.setUnitSname(ue.getSname());
		}
		return rtce;
	}

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param unitCodes 装置id数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 主位号
	 * @param page 分页对象
	 * @throws Exception 抛出异常
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<RelevantTagConfigEntity> getRelevantTagConfig(String[] unitCodes, Long[] prdtCellIds, String tag,
			Pagination page) throws Exception {
		if(unitCodes == null || unitCodes.length ==0){
			List<UnitEntity> unitList = basicDataService.getUnitList(true);
			unitCodes = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
		}
		PaginationBean<RelevantTagConfig> listRelevantTagConfig = relevantTagConfigRepo.getRelevantTagConfig(unitCodes, prdtCellIds, tag, page);
		List<UnitEntity> unitEntityList = basicDataService.getUnitListByIds(unitCodes, false);
		PaginationBean<RelevantTagConfigEntity> returnRelevantTagConfig = new PaginationBean<RelevantTagConfigEntity>(page, listRelevantTagConfig.getTotal());
		returnRelevantTagConfig.setPageList(ObjectConverter.listConverter(listRelevantTagConfig.getPageList(), RelevantTagConfigEntity.class));
		int i=0;
		List<RelevantTagConfig> rtcList = listRelevantTagConfig.getPageList();
		for(RelevantTagConfigEntity rtcEntity : returnRelevantTagConfig.getPageList()){
			RelevantTagConfig rtc = rtcList.get(i);
			AlarmPoint ap = rtc.getAlarmpoint();
			rtcEntity.setTag(ap.getTag());
			PrdtCell pc = ap.getPrdtCell();
			rtcEntity.setPrdtCellSname(pc.getSname());
			UnitEntity ue = unitEntityList.stream().filter(x->x.getStdCode().equals(pc.getUnitId())).findFirst().orElse(new UnitEntity());
			rtcEntity.setUnitSname(ue.getSname());
			i++;
		}
		
		return returnRelevantTagConfig;
	}

	/**
	 * 根据参数“相关性位号配置ID”查询<相关性位号配置明细>数据
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 相关性位号配置ID
	 * @param page 分页对象
	 * @throws Exception 抛出异常
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<RelevantTagConfigDtlEntity> getRelevantTagConfigDtl(Long relevantTagConfigId, Pagination page)
			throws Exception {
		PaginationBean<RelevantTagConfigDtl> listRelevantTagConfigDtl = relevantTagConfigDtlRepo.getRelevantTagConfigDtl(relevantTagConfigId, page);
		String[] unitCodes = listRelevantTagConfigDtl.getPageList().stream().map(x->x.getAlarmpoint().getPrdtCell().getUnitId()).distinct().toArray(String[]::new);
		List<UnitEntity> unitEntityList = basicDataService.getUnitListByIds(unitCodes, true);
		PaginationBean<RelevantTagConfigDtlEntity> returnRelevantTagConfig = new PaginationBean<RelevantTagConfigDtlEntity>(page, listRelevantTagConfigDtl.getTotal());
		returnRelevantTagConfig.setPageList(ObjectConverter.listConverter(listRelevantTagConfigDtl.getPageList(), RelevantTagConfigDtlEntity.class));
		int i=0;
		List<RelevantTagConfigDtl> rtcdList = listRelevantTagConfigDtl.getPageList();
		for(RelevantTagConfigDtlEntity rtcdEntity : returnRelevantTagConfig.getPageList()){
			RelevantTagConfigDtl rtcd = rtcdList.get(i);
			AlarmPoint ap = rtcd.getAlarmpoint();
			rtcdEntity.setTag(ap.getTag());
			rtcdEntity.setDes(ap.getDes());
			PrdtCell pc = ap.getPrdtCell();
			rtcdEntity.setPrdtCellSname(pc.getSname());
			UnitEntity ue = unitEntityList.stream().filter(x->x.getStdCode().equals(pc.getUnitId())).findFirst().orElse(new UnitEntity());
			rtcdEntity.setUnitSname(ue.getSname());
			MeasUnit mu = ap.getMeasUnit();
			rtcdEntity.setMeasUnitName(mu.getName()+"("+mu.getSign()+")");
			i++;
		}
		
		return returnRelevantTagConfig;
	}

	/**
	 * 删除相关性位号配置明细服务层方法实现
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigDtlIds 相关性位号配置明细主键Id集合
	 * @throws Exception 抛出异常
	 */
	@Override
	public CommonResult deleteRelevantTagConfigDtl(Long[] relevantTagConfigDtlIds) throws Exception {
		// 判断ID集合是否可用
		if (relevantTagConfigDtlIds == null || relevantTagConfigDtlIds.length <= 0) {
			throw new Exception("没有需要删除的相关性位号配置明细数据！");
		}
		List<RelevantTagConfigDtl> anlyRelevantTagConfigDtlList = relevantTagConfigDtlRepo.getRelevantTagConfigDtl(relevantTagConfigDtlIds);
		if (anlyRelevantTagConfigDtlList == null || anlyRelevantTagConfigDtlList.isEmpty()){
			CommonResult cr = new CommonResult();
			cr.setMessage("没有要删除的数据！");
			return cr;
		}
		Long[] anlyRelevantTagConfigDtlIdList = anlyRelevantTagConfigDtlList.stream().map(item -> item.getRelevantTagConfigDtlId()).toArray(Long[]::new);
		// 调用DAL删除方法
		CommonResult commonResult = relevantTagConfigDtlRepo.deleteRelevantTagConfigDtl(anlyRelevantTagConfigDtlIdList);

		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
		return commonResult;
	}

	/**
	 * 查询报警点服务层方法实现
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param unitCode 装置id
	 * @param prdtCellId 生产单元id
	 * @param tag 主位号
	 * @param page 分页对象
	 * @throws Exception 抛出异常
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<AlarmPointEntity> getAlarmPoint(String unitCode, Long prdtCellId, String tag, Pagination page)
			throws Exception {
		PaginationBean<AlarmPoint> listAlarmPoint = alarmPointRepo.getAlarmPoint(new String[]{unitCode}, new Long[]{prdtCellId}, tag, null, null,null, page);
		PaginationBean<AlarmPointEntity> returnAlarmPoint = new PaginationBean<AlarmPointEntity>(page, listAlarmPoint.getTotal());
		returnAlarmPoint.setPageList(ObjectConverter.listConverter(listAlarmPoint.getPageList(), AlarmPointEntity.class));
		List<UnitEntity> unitList = basicDataService.getUnitListByIds(new String[]{unitCode}, false);
		int i = 0;
		for (AlarmPointEntity ape : returnAlarmPoint.getPageList()) {
			AlarmPoint ap = listAlarmPoint.getPageList().get(i);
			ape.setUnitId(ap.getPrdtCell().getUnitId());
			UnitEntity ue = unitList.stream().filter(ul -> ap.getPrdtCell().getUnitId().equals(ul.getStdCode())).findFirst().orElse(new UnitEntity());
			ape.setUnitSname(ue.getSname());
			ape.setPrdtCellSname(ap.getPrdtCell().getSname());
			ape.setAlarmPointTypeName(ap.getAlarmPointType().getName());
			ape.setMeasunitName(ap.getMeasUnit().getName() + "(" + ap.getMeasUnit().getSign() + ")");
			ape.setSignName(ap.getMeasUnit().getSign());
			i++;
		}
		return returnAlarmPoint;
	}

	/**
	 * 获取报警点列表服务层方法实现
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 相关性位号配置ID
	 * @param unitCode 装置id
	 * @param prdtCellIds 生产单元id
	 * @param tag 主位号
	 * @param page 分页对象
	 * @throws Exception 抛出异常
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<AlarmPointEntity> getRelevantTagConfigDtlAdd(Long relevantTagConfigId, String unitCode, Long prdtCellIds, String tag, Pagination page)
			throws Exception {
		RelevantTagConfig rtc = relevantTagConfigRepo.getSingleRelevantTagConfig(relevantTagConfigId);
		List<RelevantTagConfigDtl>  rtcdList = relevantTagConfigDtlRepo.getRelevantTagConfigDtl(relevantTagConfigId);
		List<Long> apIdList = rtcdList.stream().map(x->x.getAlarmPointId()).distinct().collect(Collectors.toList());
		apIdList.add(rtc.getAlarmPointId());
		
		List<UnitEntity> unitEntityList = basicDataService.getUnitListByIds(new String[]{unitCode}, true);
		PaginationBean<AlarmPoint> listAlarmPoint = alarmPointRepo.getRelevantTagConfigDtlAdd(unitCode, prdtCellIds, tag, apIdList, page);
		PaginationBean<AlarmPointEntity> returnAlarmPoint = new PaginationBean<AlarmPointEntity>(page, listAlarmPoint.getTotal());
		returnAlarmPoint.setPageList(ObjectConverter.listConverter(listAlarmPoint.getPageList(), AlarmPointEntity.class));
		int i=0;
		List<AlarmPoint> apList = listAlarmPoint.getPageList();
		for(AlarmPointEntity apEntity : returnAlarmPoint.getPageList()){
			AlarmPoint ap = apList.get(i);
			PrdtCell pc = ap.getPrdtCell();
			apEntity.setPrdtCellSname(pc.getSname());
			UnitEntity ue = unitEntityList.stream().filter(x->x.getStdCode().equals(pc.getUnitId())).findFirst().orElse(new UnitEntity());
			apEntity.setUnitSname(ue.getSname());
			MeasUnit mu = ap.getMeasUnit();
			apEntity.setMeasunitName(mu.getName()+"("+mu.getSign()+")");
			i++;
		}
		return returnAlarmPoint;
	}

	/**
	 * 保存<相关性位号配置明细>数据服务层方法实现
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 关性位号配置ID
	 * @param alarmPointIds 报警点ID数组
	 * @throws Exception 抛出异常
	 */
	@Override
	public CommonResult addRelevantTagConfigDtl(Long relevantTagConfigId, Long[] alarmPointIds) throws Exception {
		CommonResult commonResult = new CommonResult();
		// 数据校验
		relevantTagConfigDtlValidation(relevantTagConfigId, alarmPointIds);
		for(int i=0;i<alarmPointIds.length;i++){
			RelevantTagConfigDtl rtcd = new RelevantTagConfigDtl();
			rtcd.setRelevantTagConfigId(relevantTagConfigId);
			rtcd.setAlarmPointId(alarmPointIds[i]);
			// 赋值  创建人、创建名称、创建时间
	        CommonUtil.returnValue(rtcd, PageModelEnum.NewAdd.getIndex());
	        commonResult = relevantTagConfigDtlRepo.addRelevantTagConfigDtl(rtcd);
	        // 如果失败，直接throw
	        if (commonResult.getIsSuccess() == false)
	            throw new Exception(commonResult.getMessage());
		}
		return commonResult;
	}
	
	/**
	 * 数据校验
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 关性位号配置ID
	 * @param alarmPointIds 报警点ID数组
	 * @throws Exception 抛出异常
	 * @return void
	 */
	private void relevantTagConfigDtlValidation(Long relevantTagConfigId, Long[] alarmPointIds) throws Exception {
		// 调用DAL与数据库相关的校验
		CommonResult commonResult = relevantTagConfigDtlRepo.relevantTagConfigDtlValidation(relevantTagConfigId, alarmPointIds);

        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
	}

	/**
	 * 获取相关位号服务层方法实现
	 * 
	 * <AUTHOR> 2018-08-03 
	 * @param relevantTagConfigId 关性位号配置ID
	 * @throws Exception 抛出异常
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<RelevantTagConfigDtlEntity> getRelevantTag(Long relevantTagConfigId) throws Exception {
		List<RelevantTagConfigDtl>  rtcdList = relevantTagConfigDtlRepo.getRelevantTagConfigDtl(relevantTagConfigId);
		List<RelevantTagConfigDtlEntity> rtcdeList = ObjectConverter.listConverter(rtcdList, RelevantTagConfigDtlEntity.class);
		String[] unitCodes = rtcdList.stream().map(x->x.getAlarmpoint().getPrdtCell().getUnitId()).toArray(String[]::new);
		List<UnitEntity> unitEntityList = basicDataService.getUnitListByIds(unitCodes, true);
		int i=0;
		for(RelevantTagConfigDtlEntity rtcdEntity : rtcdeList){
			RelevantTagConfigDtl rtcd = rtcdList.get(i);
			AlarmPoint ap = rtcd.getAlarmpoint();
			rtcdEntity.setTag(ap.getTag());
			rtcdEntity.setDes(ap.getDes());
			PrdtCell pc = ap.getPrdtCell();
			rtcdEntity.setPrdtCellSname(pc.getSname());
			UnitEntity ue = unitEntityList.stream().filter(x->x.getStdCode().equals(pc.getUnitId())).findFirst().orElse(new UnitEntity());
			rtcdEntity.setUnitSname(ue.getSname());
			MeasUnit mu = ap.getMeasUnit();
			rtcdEntity.setMeasUnitName(mu.getName()+"("+mu.getSign()+")");
			i++;
		}
		rtcdeList.sort(Comparator.comparing(RelevantTagConfigDtlEntity::getTag, Collator.getInstance(Locale.CHINESE)));
		return rtcdeList;
	}

	/**
	 * 相关性报警分析服务层方法实现
	 * 
	 * <AUTHOR> 2018-08-03 
	 * @param alarmPointIds 报警点主键id数组
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param dateTypeEnum 间粒度枚举
	 */
	@Override
	public Object getRelevantAlarmAnalysis(Long[] alarmPointIds, Date startTime, Date endTime,
			DateTypeEnum dateTypeEnum) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>();
		
		List<Object[]> objsProcessList = relevantAlarmAnalysisRepo.getRelevantProcessAnalysis(alarmPointIds, startTime, endTime, dateTypeEnum);
		String[] unitCodes = objsProcessList.stream().map(x->x[4]).toArray(String[]::new);
		List<UnitEntity> unitList = basicDataService.getUnitListByIds(unitCodes, false);
		String queryTime = " "+basicDataService.getQueryTime().get(0).getValue().toString();
		
		List<RelevantAlarmAnalysisEntity> processList = new ArrayList<RelevantAlarmAnalysisEntity>();
		RelevantAlarmAnalysisEntity raaEntity = new RelevantAlarmAnalysisEntity();
		List<String> xaxis=new ArrayList<String>();
        List<String> processTips=new ArrayList<String>();
        List<Long> processCounts=new ArrayList<Long>();
		
        boolean flag = objsProcessList!=null && !objsProcessList.isEmpty() && objsProcessList.size()>0;
        String nowTag = null;
        String pcSname = null;
        String uId = null;
        Long processId = null;
        if(flag){
        	Object[] os = objsProcessList.get(0);
        	nowTag = os[0].toString();
        	processId = Long.parseLong(os[5].toString());
        	if(processId.equals(alarmPointIds[0])){
        		nowTag = nowTag + "(主)";
			}
        	pcSname = os[3].toString();
        	uId = os[4].toString();
        }
        Date nowTime = startTime;// 循环时间的起点
        int totalProcess = objsProcessList.size();
        
		if(dateTypeEnum.getName().equals("hour")){
			for(int i=0;i<totalProcess;i++){
				Object[] objs = objsProcessList.get(i);
				String tag = objs[0].toString();
				Long alarmPointId = Long.parseLong(objs[5].toString());
				if(alarmPointId.equals(alarmPointIds[0])){
					tag = tag + "(主)";
				}
				Long processCount = Long.parseLong(objs[1].toString());
				String alarmTime = objs[2].toString();
				String prdtCellSname = objs[3].toString();
				String unitCode = objs[4].toString();
				if(!tag.equals(nowTag)){// 新的位号
					while(nowTime.getTime()<endTime.getTime()){
						Date nextTime = DateUtils.addHours(nowTime, 1);
						String nowTimeStr = DateFormatUtils.format(nowTime, "yyyy-MM-dd HH:mm");
						String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd HH:mm");
						xaxis.add(nowTimeStr);// x轴
						processCounts.add(0L);
						processTips.add("从："+nowTimeStr+":00"+" 至："+nextTimeStr+":00"+"<br>"+tag+"：0");
						nowTime = nextTime;
					}
					raaEntity.setPrdtCellSname(pcSname);
					raaEntity.setAlarmPointId(processId);
					String ui = uId;
					UnitEntity ue = unitList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
					raaEntity.setUnitSname(ue.getSname());
					raaEntity.setTag(nowTag);
					pcSname = prdtCellSname;
					processId = alarmPointId;
					uId = unitCode;
					nowTag = tag;
					raaEntity.setXaxis(xaxis);
					raaEntity.setProcessTips(processTips);
					raaEntity.setProcessCounts(processCounts);
					processList.add(raaEntity);
					
					raaEntity = new RelevantAlarmAnalysisEntity();
					xaxis=new ArrayList<String>();
			        processTips=new ArrayList<String>();
			        processCounts=new ArrayList<Long>();
			        nowTime = startTime;
				}
				while(nowTime.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addHours(nowTime, 1);
					String nowTimeStr = DateFormatUtils.format(nowTime, "yyyy-MM-dd HH:mm");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd HH:mm");
					xaxis.add(nowTimeStr);// x轴
					nowTime = nextTime;
					if(nowTimeStr.equals(alarmTime)){
						processCounts.add(processCount);
						processTips.add("从："+nowTimeStr+":00"+" 至："+nextTimeStr+":00"+"<br>"+tag+"："+processCount);
						break;
					}else{
						processCounts.add(0L);
						processTips.add("从："+nowTimeStr+":00"+" 至："+nextTimeStr+":00"+"<br>"+tag+"：0");
					}
				}
				// 判断最后的日期
				if(nowTime.getTime()>endTime.getTime()){
					processTips.remove(processTips.size()-1);
					processTips.add("从："+DateFormatUtils.format(DateUtils.addHours(nowTime, -1), "yyyy-MM-dd HH:mm:ss")+" 至："+DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss")+"<br>"+tag+"："+processCounts.get(processCounts.size()-1));
				}
			}
			if(flag){// 最后一个位号
				while(nowTime.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addHours(nowTime, 1);
					String nowTimeStr = DateFormatUtils.format(nowTime, "yyyy-MM-dd HH:mm");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd HH:mm");
					xaxis.add(nowTimeStr);// x轴
					processCounts.add(0L);
					processTips.add("从："+nowTimeStr+":00"+" 至："+nextTimeStr+":00"+"<br>"+nowTag+"：0");
					nowTime = nextTime;
				}
				raaEntity.setPrdtCellSname(pcSname);
				raaEntity.setAlarmPointId(processId);;
				String ui = uId;
				UnitEntity ue = unitList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
				raaEntity.setUnitSname(ue.getSname());
				raaEntity.setTag(nowTag);
				raaEntity.setXaxis(xaxis);
				raaEntity.setProcessTips(processTips);
				raaEntity.setProcessCounts(processCounts);
				processList.add(raaEntity);
			}
		}else if(dateTypeEnum.getName().equals("day")){
			for(int i=0;i<totalProcess;i++){
				Object[] objs = objsProcessList.get(i);
				String tag = objs[0].toString();
				Long alarmPointId = Long.parseLong(objs[5].toString());
				if(alarmPointId.equals(alarmPointIds[0])){
					tag = tag + "(主)";
				}
				Long processCount = Long.parseLong(objs[1].toString());
				String alarmTime = objs[2].toString();
				String prdtCellSname = objs[3].toString();
				String unitCode = objs[4].toString();
				if(!nowTag.equals(tag)){// 新的位号
					while(nowTime.getTime()<endTime.getTime()){
						Date nextTime = DateUtils.addDays(nowTime, 1);
						String nowTimeStr = DateFormatUtils.format(nowTime, "yyyy-MM-dd");
						String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd");
						xaxis.add(nowTimeStr);// x轴
						processCounts.add(0L);
						processTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+tag+"：0");
						nowTime = nextTime;
					}
					raaEntity.setPrdtCellSname(pcSname);
					raaEntity.setAlarmPointId(processId);
					String ui = uId;
					UnitEntity ue = unitList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
					raaEntity.setUnitSname(ue.getSname());
					raaEntity.setTag(nowTag);
					pcSname = prdtCellSname;
					processId = alarmPointId;
					uId = unitCode;
					nowTag = tag;
					raaEntity.setXaxis(xaxis);
					raaEntity.setProcessTips(processTips);
					raaEntity.setProcessCounts(processCounts);
					processList.add(raaEntity);
					
					raaEntity = new RelevantAlarmAnalysisEntity();
					xaxis=new ArrayList<String>();
			        processTips=new ArrayList<String>();
			        processCounts=new ArrayList<Long>();
			        nowTime = startTime;
				}
				while(nowTime.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addDays(nowTime, 1);
					String nowTimeStr = DateFormatUtils.format(nowTime, "yyyy-MM-dd");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd");
					xaxis.add(nowTimeStr);// x轴
					nowTime = nextTime;
					if(nowTimeStr.equals(alarmTime)){
						processCounts.add(processCount);
						processTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+tag+"："+processCount);
						break;
					}else{
						processCounts.add(0L);
						processTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+tag+"：0");
					}
				}
				if(nowTime.getTime()>endTime.getTime()){
					processTips.remove(processTips.size()-1);
					processTips.add("从："+DateFormatUtils.format(DateUtils.addDays(nowTime, -1), "yyyy-MM-dd HH:mm:ss")+" 至："+DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss")+"<br>"+tag+"："+processCounts.get(processCounts.size()-1));
				}
			}
			if(flag){// 最后一个位号
				while(nowTime.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addDays(nowTime, 1);
					String nowTimeStr = DateFormatUtils.format(nowTime, "yyyy-MM-dd");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd");
					xaxis.add(nowTimeStr);// x轴
					processCounts.add(0L);
					processTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+nowTag+"：0");
					nowTime = nextTime;
				}
				raaEntity.setPrdtCellSname(pcSname);
				raaEntity.setAlarmPointId(processId);
				String ui = uId;
				UnitEntity ue = unitList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
				raaEntity.setUnitSname(ue.getSname());
				raaEntity.setTag(nowTag);
				raaEntity.setXaxis(xaxis);
				raaEntity.setProcessTips(processTips);
				raaEntity.setProcessCounts(processCounts);
				processList.add(raaEntity);
			}
		}else if(dateTypeEnum.getName().equals("week")){
			for(int i=0;i<totalProcess;i++){
				Object[] objs = objsProcessList.get(i);
				String tag = objs[0].toString();
				Long alarmPointId = Long.parseLong(objs[5].toString());
				if(alarmPointId.equals(alarmPointIds[0])){
					tag = tag + "(主)";
				}
				Long processCount = Long.parseLong(objs[1].toString());
				String alarmTime = objs[2].toString();
				String prdtCellSname = objs[3].toString();
				String unitCode = objs[4].toString();
				if(!nowTag.equals(tag)){// 新的位号
					while(nowTime.getTime()<endTime.getTime()){
						Date nextTime = DateUtils.addWeeks(nowTime, 1);
						String nowTimeStr = DateFormatUtils.format(nowTime, "yyyy-MM-dd");
						String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd");
						xaxis.add(nowTimeStr);// x轴
						processCounts.add(0L);
						processTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+tag+"：0");
						nowTime = nextTime;
					}
					raaEntity.setPrdtCellSname(pcSname);
					raaEntity.setAlarmPointId(processId);
					String ui = uId;
					UnitEntity ue = unitList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
					raaEntity.setUnitSname(ue.getSname());
					raaEntity.setTag(nowTag);
					pcSname = prdtCellSname;
					processId = alarmPointId;
					uId = unitCode;
					nowTag = tag;
					raaEntity.setXaxis(xaxis);
					raaEntity.setProcessTips(processTips);
					raaEntity.setProcessCounts(processCounts);
					processList.add(raaEntity);
					
					raaEntity = new RelevantAlarmAnalysisEntity();
					xaxis=new ArrayList<String>();
			        processTips=new ArrayList<String>();
			        processCounts=new ArrayList<Long>();
			        nowTime = startTime;
				}
				while(nowTime.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addWeeks(nowTime, 1);
					String nowTimeStr = DateFormatUtils.format(nowTime, "yyyy-MM-dd");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd");
					xaxis.add(nowTimeStr);// x轴
					nowTime = nextTime;
					if(nowTimeStr.equals(alarmTime)){
						processCounts.add(processCount);
						processTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+tag+"："+processCount);
						break;
					}else{
						processCounts.add(0L);
						processTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+tag+"：0");
					}
				}
				// 判断最后的日期
				if(nowTime.getTime()>endTime.getTime()){
					processTips.remove(processTips.size()-1);
					processTips.add("从："+DateFormatUtils.format(DateUtils.addWeeks(nowTime, -1), "yyyy-MM-dd HH:mm:ss")+" 至："+DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss")+"<br>"+tag+"："+processCounts.get(processCounts.size()-1));
				}
			}
			if(flag){// 最后一个位号
				while(nowTime.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addWeeks(nowTime, 1);
					String nowTimeStr = DateFormatUtils.format(nowTime, "yyyy-MM-dd");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd");
					xaxis.add(nowTimeStr);// x轴
					processCounts.add(0L);
					processTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+nowTag+"：0");
					nowTime = nextTime;
				}
				raaEntity.setPrdtCellSname(pcSname);
				raaEntity.setAlarmPointId(processId);
				String ui = uId;
				UnitEntity ue = unitList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
				raaEntity.setUnitSname(ue.getSname());
				raaEntity.setTag(nowTag);
				raaEntity.setXaxis(xaxis);
				raaEntity.setProcessTips(processTips);
				raaEntity.setProcessCounts(processCounts);
				processList.add(raaEntity);
			}
		}else if(dateTypeEnum.getName().equals("month")){
			nowTime = DateUtils.setDays(nowTime, 1);
			for(int i=0;i<totalProcess;i++){
				Object[] objs = objsProcessList.get(i);
				String tag = objs[0].toString();
				Long alarmPointId = Long.parseLong(objs[5].toString());
				if(alarmPointId.equals(alarmPointIds[0])){
					tag = tag + "(主)";
				}
				Long processCount = Long.parseLong(objs[1].toString());
				String alarmTime = objs[2].toString();
				String prdtCellSname = objs[3].toString();
				String unitCode = objs[4].toString();
				if(!nowTag.equals(tag)){// 新的位号
					while(nowTime.getTime()<endTime.getTime()){
						Date nextTime = DateUtils.addMonths(nowTime, 1);
						String nowTimeStr = DateFormatUtils.format(nowTime, "yyyy-MM");
						String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM");
						xaxis.add(nowTimeStr);// x轴
						processCounts.add(0L);
						processTips.add("从："+nowTimeStr+"-01 "+queryTime+" 至："+nextTimeStr+"-01 "+queryTime+"<br>"+tag+"：0");
						nowTime = nextTime;
					}
					raaEntity.setPrdtCellSname(pcSname);
					raaEntity.setAlarmPointId(processId);
					String ui = uId;
					UnitEntity ue = unitList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
					raaEntity.setUnitSname(ue.getSname());
					raaEntity.setTag(nowTag);
					pcSname = prdtCellSname;
					processId = alarmPointId;
					uId = unitCode;
					nowTag = tag;
					raaEntity.setXaxis(xaxis);
					raaEntity.setProcessTips(processTips);
					raaEntity.setProcessCounts(processCounts);
					processList.add(raaEntity);
					
					raaEntity = new RelevantAlarmAnalysisEntity();
					xaxis=new ArrayList<String>();
			        processTips=new ArrayList<String>();
			        processCounts=new ArrayList<Long>();
			        nowTime = DateUtils.setDays(startTime, 1);
				}
				while(nowTime.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addMonths(nowTime, 1);
					String nowTimeStr = DateFormatUtils.format(nowTime, "yyyy-MM");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM");
					xaxis.add(nowTimeStr);// x轴
					nowTime = nextTime;
					if(nowTimeStr.equals(alarmTime)){
						processCounts.add(processCount);
						processTips.add("从："+nowTimeStr+"-01 "+queryTime+" 至："+nextTimeStr+"-01 "+queryTime+"<br>"+tag+"："+processCount);
						break;
					}else{
						processCounts.add(0L);
						processTips.add("从："+nowTimeStr+"-01 "+queryTime+" 至："+nextTimeStr+"-01 "+queryTime+"<br>"+tag+"：0");
					}
				}
				// 判断最后的日期
				if(nowTime.getTime()>endTime.getTime()){
					processTips.remove(processTips.size()-1);
					processTips.add("从："+DateFormatUtils.format(DateUtils.addMonths(nowTime, -1), "yyyy-MM-dd HH:mm:ss")+" 至："+DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss")+"<br>"+tag+"："+processCounts.get(processCounts.size()-1));
				}
			}
			if(flag){// 最后一个位号
				while(nowTime.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addMonths(nowTime, 1);
					String nowTimeStr = DateFormatUtils.format(nowTime, "yyyy-MM");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM");
					xaxis.add(nowTimeStr);// x轴
					processCounts.add(0L);
					processTips.add("从："+nowTimeStr+"-01 "+queryTime+" 至："+nextTimeStr+"-01 "+queryTime+"<br>"+nowTag+"：0");
					nowTime = nextTime;
				}
				raaEntity.setPrdtCellSname(pcSname);
				raaEntity.setAlarmPointId(processId);
				String ui = uId;
				UnitEntity ue = unitList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
				raaEntity.setUnitSname(ue.getSname());
				raaEntity.setTag(nowTag);
				raaEntity.setXaxis(xaxis);
				raaEntity.setProcessTips(processTips);
				raaEntity.setProcessCounts(processCounts);
				processList.add(raaEntity);
			}
		}
		map.put("processNumber", processList);
		
		List<Object[]> objsOperateList = relevantAlarmAnalysisRepo.getRelevantOperateAnalysis(alarmPointIds, startTime, endTime, dateTypeEnum);
		String[] unitOperCodes = objsOperateList.stream().map(x->x[4]).toArray(String[]::new);
		List<UnitEntity> unitOperList = basicDataService.getUnitListByIds(unitOperCodes, false);
		List<RelevantAlarmAnalysisEntity> operateList = new ArrayList<RelevantAlarmAnalysisEntity>();
		RelevantAlarmAnalysisEntity operateEntity = new RelevantAlarmAnalysisEntity();
		List<String> xaxisOper=new ArrayList<String>();
        List<String> operateTips=new ArrayList<String>();
        List<Long> operateCounts=new ArrayList<Long>();
		
        boolean flagOper = objsOperateList!=null && !objsOperateList.isEmpty() && objsOperateList.size()>0;
        String nowTagOper = null;
        String pcSnameOper = null;
        String uIdOper = null;
        Long operateId = null;
        if(flagOper){
        	Object[] os = objsOperateList.get(0);
        	nowTagOper = os[0].toString();
        	operateId = Long.parseLong(os[5].toString());
        	if(operateId.equals(alarmPointIds[0])){
        		nowTagOper = nowTagOper + "(主)";
        	}
        	pcSnameOper = os[3].toString();
        	uIdOper = os[4].toString();
        }
        Date nowTimeOper = startTime;// 循环时间的起点
        int totalOperate = objsOperateList.size();
        
		if(dateTypeEnum.getName().equals("hour")){
			for(int i=0;i<totalOperate;i++){
				Object[] objs = objsOperateList.get(i);
				String tag = objs[0].toString();
				Long processCount = Long.parseLong(objs[1].toString());
				Long alarmPointId = Long.parseLong(objs[5].toString());
				if(alarmPointId.equals(alarmPointIds[0])){
					tag = tag + "(主)";
				}
				String alarmTime = objs[2].toString();
				String prdtCellSname = objs[3].toString();
				String unitCode = objs[4].toString();
				if(!tag.equals(nowTagOper)){// 新的位号
					while(nowTimeOper.getTime()<endTime.getTime()){
						Date nextTime = DateUtils.addHours(nowTimeOper, 1);
						String nowTimeStr = DateFormatUtils.format(nowTimeOper, "yyyy-MM-dd HH:mm");
						String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd HH:mm");
						xaxisOper.add(nowTimeStr);// x轴
						operateCounts.add(0L);
						operateTips.add("从："+nowTimeStr+":00"+" 至："+nextTimeStr+":00"+"<br>"+tag+"：0");
						nowTimeOper = nextTime;
					}
					operateEntity.setPrdtCellSname(pcSnameOper);
					operateEntity.setAlarmPointId(operateId);
					String ui = uIdOper;
					UnitEntity ue = unitOperList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
					operateEntity.setUnitSname(ue.getSname());
					operateEntity.setTag(nowTagOper);
					pcSnameOper = prdtCellSname;
					operateId = alarmPointId;
					uIdOper = unitCode;
					nowTagOper = tag;
					operateEntity.setXaxis(xaxisOper);
					operateEntity.setProcessTips(operateTips);
					operateEntity.setProcessCounts(operateCounts);
					operateList.add(operateEntity);
					
					operateEntity = new RelevantAlarmAnalysisEntity();
					xaxisOper=new ArrayList<String>();
					operateTips=new ArrayList<String>();
					operateCounts=new ArrayList<Long>();
			        nowTimeOper = startTime;
				}
				while(nowTimeOper.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addHours(nowTimeOper, 1);
					String nowTimeStr = DateFormatUtils.format(nowTimeOper, "yyyy-MM-dd HH:mm");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd HH:mm");
					xaxisOper.add(nowTimeStr);// x轴
					nowTimeOper = nextTime;
					if(nowTimeStr.equals(alarmTime)){
						operateCounts.add(processCount);
						operateTips.add("从："+nowTimeStr+":00"+" 至："+nextTimeStr+":00"+"<br>"+tag+"："+processCount);
						break;
					}else{
						operateCounts.add(0L);
						operateTips.add("从："+nowTimeStr+":00"+" 至："+nextTimeStr+":00"+"<br>"+tag+"：0");
					}
				}
				// 判断最后的日期
				if(nowTimeOper.getTime()>endTime.getTime()){
					operateTips.remove(operateTips.size()-1);
					operateTips.add("从："+DateFormatUtils.format(DateUtils.addHours(nowTimeOper, -1), "yyyy-MM-dd HH:mm:ss")+" 至："+DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss")+"<br>"+tag+"："+operateCounts.get(operateCounts.size()-1));
				}
			}
			if(flagOper){// 最后一个位号
				while(nowTimeOper.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addHours(nowTimeOper, 1);
					String nowTimeStr = DateFormatUtils.format(nowTimeOper, "yyyy-MM-dd HH:mm");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd HH:mm");
					xaxisOper.add(nowTimeStr);// x轴
					operateCounts.add(0L);
					operateTips.add("从："+nowTimeStr+":00"+" 至："+nextTimeStr+":00"+"<br>"+nowTagOper+"：0");
					nowTimeOper = nextTime;
				}
				operateEntity.setPrdtCellSname(pcSnameOper);
				operateEntity.setAlarmPointId(operateId);
				String ui = uIdOper;
				UnitEntity ue = unitOperList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
				operateEntity.setUnitSname(ue.getSname());
				operateEntity.setTag(nowTagOper);
				operateEntity.setXaxis(xaxisOper);
				operateEntity.setProcessTips(operateTips);
				operateEntity.setProcessCounts(operateCounts);
				operateList.add(operateEntity);
			}
		}else if(dateTypeEnum.getName().equals("day")){
			for(int i=0;i<totalOperate;i++){
				Object[] objs = objsOperateList.get(i);
				String tag = objs[0].toString();
				Long alarmPointId = Long.parseLong(objs[5].toString());
				if(alarmPointId.equals(alarmPointIds[0])){
					tag = tag + "(主)";
				}
				Long processCount = Long.parseLong(objs[1].toString());
				String alarmTime = objs[2].toString();
				String prdtCellSname = objs[3].toString();
				String unitCode = objs[4].toString();
				if(!nowTagOper.equals(tag)){// 新的位号
					while(nowTimeOper.getTime()<endTime.getTime()){
						Date nextTime = DateUtils.addDays(nowTimeOper, 1);
						String nowTimeStr = DateFormatUtils.format(nowTimeOper, "yyyy-MM-dd");
						String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd");
						xaxisOper.add(nowTimeStr);// x轴
						operateCounts.add(0L);
						operateTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+tag+"：0");
						nowTimeOper = nextTime;
					}
					operateEntity.setPrdtCellSname(pcSnameOper);
					operateEntity.setAlarmPointId(operateId);
					String ui = uIdOper;
					UnitEntity ue = unitOperList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
					operateEntity.setUnitSname(ue.getSname());
					operateEntity.setTag(nowTagOper);
					pcSnameOper = prdtCellSname;
					uIdOper = unitCode;
					operateId = alarmPointId;
					nowTagOper = tag;
					operateEntity.setXaxis(xaxisOper);
					operateEntity.setProcessTips(operateTips);
					operateEntity.setProcessCounts(operateCounts);
					operateList.add(operateEntity);
					
					operateEntity = new RelevantAlarmAnalysisEntity();
					xaxisOper=new ArrayList<String>();
					operateTips=new ArrayList<String>();
					operateCounts=new ArrayList<Long>();
			        nowTimeOper = startTime;
				}
				while(nowTimeOper.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addDays(nowTimeOper, 1);
					String nowTimeStr = DateFormatUtils.format(nowTimeOper, "yyyy-MM-dd");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd");
					xaxisOper.add(nowTimeStr);// x轴
					nowTimeOper = nextTime;
					if(nowTimeStr.equals(alarmTime)){
						operateCounts.add(processCount);
						operateTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+tag+"："+processCount);
						break;
					}else{
						operateCounts.add(0L);
						operateTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+tag+"：0");
					}
				}
				// 判断最后的日期
				if(nowTimeOper.getTime()>endTime.getTime()){
					operateTips.remove(operateTips.size()-1);
					operateTips.add("从："+DateFormatUtils.format(DateUtils.addDays(nowTimeOper, -1), "yyyy-MM-dd HH:mm:ss")+" 至："+DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss")+"<br>"+tag+"："+operateCounts.get(operateCounts.size()-1));
				}
			}
			if(flagOper){// 最后一个位号
				while(nowTimeOper.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addDays(nowTimeOper, 1);
					String nowTimeStr = DateFormatUtils.format(nowTimeOper, "yyyy-MM-dd");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd");
					xaxisOper.add(nowTimeStr);// x轴
					operateCounts.add(0L);
					operateTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+nowTagOper+"：0");
					nowTimeOper = nextTime;
				}
				operateEntity.setPrdtCellSname(pcSnameOper);
				operateEntity.setAlarmPointId(operateId);
				String ui = uIdOper;
				UnitEntity ue = unitOperList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
				operateEntity.setUnitSname(ue.getSname());
				operateEntity.setTag(nowTagOper);
				operateEntity.setXaxis(xaxisOper);
				operateEntity.setProcessTips(operateTips);
				operateEntity.setProcessCounts(operateCounts);
				operateList.add(operateEntity);
			}
		}else if(dateTypeEnum.getName().equals("week")){
			for(int i=0;i<totalOperate;i++){
				Object[] objs = objsOperateList.get(i);
				String tag = objs[0].toString();
				Long alarmPointId = Long.parseLong(objs[5].toString());
				if(alarmPointId.equals(alarmPointIds[0])){
					tag = tag + "(主)";
				}
				Long processCount = Long.parseLong(objs[1].toString());
				String alarmTime = objs[2].toString();
				String prdtCellSname = objs[3].toString();
				String unitCode = objs[4].toString();
				if(!nowTagOper.equals(tag)){// 新的位号
					while(nowTimeOper.getTime()<endTime.getTime()){
						Date nextTime = DateUtils.addWeeks(nowTimeOper, 1);
						String nowTimeStr = DateFormatUtils.format(nowTimeOper, "yyyy-MM-dd");
						String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd");
						xaxisOper.add(nowTimeStr);// x轴
						operateCounts.add(0L);
						operateTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+tag+"：0");
						nowTimeOper = nextTime;
					}
					operateEntity.setPrdtCellSname(pcSnameOper);
					operateEntity.setAlarmPointId(operateId);
					String ui = uIdOper;
					UnitEntity ue = unitOperList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
					operateEntity.setUnitSname(ue.getSname());
					operateEntity.setTag(nowTagOper);
					pcSnameOper = prdtCellSname;
					operateId = alarmPointId;
					uIdOper = unitCode;
					nowTagOper = tag;
					operateEntity.setXaxis(xaxisOper);
					operateEntity.setProcessTips(operateTips);
					operateEntity.setProcessCounts(operateCounts);
					operateList.add(operateEntity);
					
					operateEntity = new RelevantAlarmAnalysisEntity();
					xaxisOper=new ArrayList<String>();
					operateTips=new ArrayList<String>();
			        operateCounts=new ArrayList<Long>();
			        nowTimeOper = startTime;
				}
				while(nowTimeOper.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addWeeks(nowTimeOper, 1);
					String nowTimeStr = DateFormatUtils.format(nowTimeOper, "yyyy-MM-dd");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd");
					xaxisOper.add(nowTimeStr);// x轴
					nowTimeOper = nextTime;
					if(nowTimeStr.equals(alarmTime)){
						operateCounts.add(processCount);
						operateTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+tag+"："+processCount);
						break;
					}else{
						operateCounts.add(0L);
						operateTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+tag+"：0");
					}
				}
				// 判断最后的日期
				if(nowTimeOper.getTime()>endTime.getTime()){
					operateTips.remove(operateTips.size()-1);
					operateTips.add("从："+DateFormatUtils.format(DateUtils.addWeeks(nowTimeOper, -1), "yyyy-MM-dd HH:mm:ss")+" 至："+DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss")+"<br>"+tag+"："+operateCounts.get(operateCounts.size()-1));
				}
			}
			if(flagOper){// 最后一个位号
				while(nowTimeOper.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addWeeks(nowTimeOper, 1);
					String nowTimeStr = DateFormatUtils.format(nowTimeOper, "yyyy-MM-dd");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM-dd");
					xaxisOper.add(nowTimeStr);// x轴
					operateCounts.add(0L);
					operateTips.add("从："+nowTimeStr+queryTime+" 至："+nextTimeStr+queryTime+"<br>"+nowTagOper+"：0");
					nowTimeOper = nextTime;
				}
				operateEntity.setPrdtCellSname(pcSnameOper);
				operateEntity.setAlarmPointId(operateId);
				String ui = uIdOper;
				UnitEntity ue = unitOperList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
				operateEntity.setUnitSname(ue.getSname());
				operateEntity.setTag(nowTagOper);
				operateEntity.setXaxis(xaxisOper);
				operateEntity.setProcessTips(operateTips);
				operateEntity.setProcessCounts(operateCounts);
				operateList.add(operateEntity);
			}
		}else if(dateTypeEnum.getName().equals("month")){
			nowTimeOper = DateUtils.setDays(startTime, 1);
			for(int i=0;i<totalOperate;i++){
				Object[] objs = objsOperateList.get(i);
				String tag = objs[0].toString();
				Long alarmPointId = Long.parseLong(objs[5].toString());
				if(alarmPointId.equals(alarmPointIds[0])){
					tag = tag + "(主)";
				}
				Long processCount = Long.parseLong(objs[1].toString());
				String alarmTime = objs[2].toString();
				String prdtCellSname = objs[3].toString();
				String unitCode = objs[4].toString();
				if(!nowTagOper.equals(tag)){// 新的位号
					while(nowTimeOper.getTime()<endTime.getTime()){
						Date nextTime = DateUtils.addMonths(nowTimeOper, 1);
						String nowTimeStr = DateFormatUtils.format(nowTimeOper, "yyyy-MM");
						String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM");
						xaxisOper.add(nowTimeStr);// x轴
						operateCounts.add(0L);
						operateTips.add("从："+nowTimeStr+"-01 "+queryTime+" 至："+nextTimeStr+"-01 "+queryTime+"<br>"+tag+"：0");
						nowTimeOper = nextTime;
					}
					operateEntity.setPrdtCellSname(pcSnameOper);
					operateEntity.setAlarmPointId(operateId);
					String ui = uIdOper;
					UnitEntity ue = unitOperList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
					operateEntity.setUnitSname(ue.getSname());
					operateEntity.setTag(nowTagOper);
					pcSnameOper = prdtCellSname;
					operateId = alarmPointId;
					uIdOper = unitCode;
					nowTagOper = tag;
					operateEntity.setXaxis(xaxisOper);
					operateEntity.setProcessTips(operateTips);
					operateEntity.setProcessCounts(operateCounts);
					operateList.add(operateEntity);
					
					operateEntity = new RelevantAlarmAnalysisEntity();
					xaxisOper=new ArrayList<String>();
					operateTips=new ArrayList<String>();
					operateCounts=new ArrayList<Long>();
					nowTimeOper = DateUtils.setDays(startTime, 1);
				}
				while(nowTimeOper.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addMonths(nowTimeOper, 1);
					String nowTimeStr = DateFormatUtils.format(nowTimeOper, "yyyy-MM");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM");
					xaxisOper.add(nowTimeStr);// x轴
					nowTimeOper = nextTime;
					if(nowTimeStr.equals(alarmTime)){
						operateCounts.add(processCount);
						operateTips.add("从："+nowTimeStr+"-01 "+queryTime+" 至："+nextTimeStr+"-01 "+queryTime+"<br>"+tag+"："+processCount);
						break;
					}else{
						operateCounts.add(0L);
						operateTips.add("从："+nowTimeStr+"-01 "+queryTime+" 至："+nextTimeStr+"-01 "+queryTime+"<br>"+tag+"：0");
					}
				}
				// 判断最后的日期
				if(nowTimeOper.getTime()>endTime.getTime()){
					operateTips.remove(operateTips.size()-1);
					operateTips.add("从："+DateFormatUtils.format(DateUtils.addMonths(nowTimeOper, -1), "yyyy-MM-dd HH:mm:ss")+" 至："+DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss")+"<br>"+tag+"："+operateCounts.get(operateCounts.size()-1));
				}
			}
			if(flagOper){// 最后一个位号
				while(nowTimeOper.getTime()<endTime.getTime()){
					Date nextTime = DateUtils.addMonths(nowTimeOper, 1);
					String nowTimeStr = DateFormatUtils.format(nowTimeOper, "yyyy-MM");
					String nextTimeStr = DateFormatUtils.format(nextTime, "yyyy-MM");
					xaxisOper.add(nowTimeStr);// x轴
					operateCounts.add(0L);
					operateTips.add("从："+nowTimeStr+"-01 "+queryTime+" 至："+nextTimeStr+"-01 "+queryTime+"<br>"+nowTagOper+"：0");
					nowTimeOper = nextTime;
				}
				operateEntity.setPrdtCellSname(pcSnameOper);
				operateEntity.setAlarmPointId(operateId);
				String ui = uIdOper;
				UnitEntity ue = unitOperList.stream().filter(x->x.getStdCode().equals(ui)).findFirst().orElse(new UnitEntity());
				operateEntity.setUnitSname(ue.getSname());
				operateEntity.setTag(nowTagOper);
				operateEntity.setXaxis(xaxisOper);
				operateEntity.setProcessTips(operateTips);
				operateEntity.setProcessCounts(operateCounts);
				operateList.add(operateEntity);
			}
		}
		map.put("operateNumber", operateList);
		
		List<RelevantAlarmTableEntity> rateList = new ArrayList<RelevantAlarmTableEntity>();
		for(Long apId:alarmPointIds){
			RelevantAlarmTableEntity ratEntity = new RelevantAlarmTableEntity();
			// 报警数
			RelevantAlarmAnalysisEntity processEntity = processList.stream().filter(x->x.getAlarmPointId().equals(apId)).findFirst().orElse(null);
			if(processEntity!=null){
				ratEntity.setTag(processEntity.getTag());
				ratEntity.setPrdtCellSname(processEntity.getPrdtCellSname());
				ratEntity.setUnitSname(processEntity.getUnitSname());
				ratEntity.setProcessCount(processEntity.getProcessCounts().stream().mapToLong(Long::longValue).sum());
			}
			// 操作数
			RelevantAlarmAnalysisEntity operEntity = operateList.stream().filter(x->x.getAlarmPointId().equals(apId)).findFirst().orElse(null);
			if(operEntity!=null){
				ratEntity.setTag(operEntity.getTag());
				ratEntity.setPrdtCellSname(operEntity.getPrdtCellSname());
				ratEntity.setUnitSname(operEntity.getUnitSname());
				ratEntity.setOperateCount(operEntity.getProcessCounts().stream().mapToLong(Long::longValue).sum());
			}
			if(processEntity!=null || operEntity!=null){
				rateList.add(ratEntity);
			}
		}
		map.put("table", rateList);
		
		return map;
	}
	
}
