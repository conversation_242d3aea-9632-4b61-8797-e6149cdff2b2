package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.AlarmExamineRecEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

//@Service
public interface AlarmExamineRecService {
    /**
     * 主数据查询
     *
     * @param unitIds       装置
     * @param prdtCellIds   生产单元
     * @param tag           位号
     * @param alarmFlagId   报警标识
     * @param priority      优先级
     * @param examineStatus 审查状态
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param alarmDuration 报警时长
     * @param page          分页参数
     */
    PaginationBean<AlarmExamineRecEntity> getAlarmExamineRec(String[] unitIds, Long[] prdtCellIds, String tag, Integer alarmFlagId, Integer priority, Integer examineStatus, Date startTime, Date endTime, Integer alarmDuration, Pagination page);

    /**
     * 报警审查变更,提交
     *
     * @param alarmExamineRecId 审查id
     * @param recoveryTime      恢复时间
     * @param reasonAnly        变更原因
     * @param file              文件
     * @param alarmRecId        报警记录id
     * @param status            变更状态（只能为0，1）
     * @param name              文件名
     */
    CommonResult saveAlarmExamineRec(Long alarmExamineRecId, Date recoveryTime, MultipartFile file, Long alarmRecId, String reasonAnly, Integer status, String name);

    /**
     * 审批通过，驳回
     *
     * @param alarmExamineRecId 审查id
     * @param status            审查状态（只能2，3）
     */
    CommonResult approvalAlarmExamineRec(Long alarmExamineRecId, Integer status);
}
