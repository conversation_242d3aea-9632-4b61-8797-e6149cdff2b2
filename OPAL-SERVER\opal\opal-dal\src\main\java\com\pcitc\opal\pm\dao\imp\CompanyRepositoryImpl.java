package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.CompanyRepositoryCustom;
import com.pcitc.opal.pm.pojo.Company;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Company实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_CompanyRepositoryImpl
 * 作       者：shufei.sui
 * 创建时间：2021/02/22
 * 修改编号：1
 * 描       述：Company实体的Repository实现
 */
public class CompanyRepositoryImpl extends BaseRepository<Company, Long> implements CompanyRepositoryCustom {


    /**
     * 添加企业
     * @param company
     * @return
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addCompany(Company company) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(company);
            commonResult.setResult(company);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 删除企业
     * @param companyIds
     * @return
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult deleteCompany(Long[] companyIds) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            StringBuilder hql = new StringBuilder(" from Company t where t.companyId in (:companyIds)");
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> companyIdsList = Arrays.asList(companyIds);
            paramList.put("companyIds", companyIdsList);

            TypedQuery<Company> query = getEntityManager().createQuery(hql.toString(), Company.class);
            this.setParameterList(query, paramList);
            List<Company> factoryList = query.getResultList();
            factoryList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 删除出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 修改企业数据
     * @param company
     * @return
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult updateCompany(Company company) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(company);
            commonResult.setResult(company);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 通过id获取企业
     * @param companyIds
     * @return
     */
    @Override
    public List<Company> getCompany(Long[] companyIds) {
        try {
            // 查询字符串
            String hql = "from Company t where 1=1 ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (ArrayUtils.isNotEmpty(companyIds)) {
                hql += " and t.companyId in (:companyIds)";
                List<Long> companyIdsList = Arrays.asList(companyIds);
                paramList.put("companyIds", companyIdsList);
            } else {
                hql += " and t.inUse = :inUse ";
                paramList.put("inUse", CommonEnum.InUseEnum.Yes.getIndex());
            }
            TypedQuery<Company> query = getEntityManager().createQuery(hql, Company.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取单条企业数据
     * @param companyId
     * @return
     */
    @Override
    public Company getSingleCompany(Long companyId) {
        try {
            return getEntityManager().find(Company.class, companyId);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取企业分页数据
     * @param name
     * @param stdCode
     * @param inUse
     * @param page
     * @return
     */
    @Override
    public PaginationBean<Company> getCompany(String name, String stdCode, Integer inUse, Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from Company t where 1=1 ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 名称/简称
            if (!StringUtils.isEmpty(name)) {
                hql.append(" and (t.name like :name escape '/' or t.sname like :name escape '/') ");
                paramList.put("name", "%" + this.sqlLikeReplace(name) + "%");
            }
            if (!StringUtils.isEmpty(stdCode)) {
                hql.append(" and (upper(t.stdCode) like upper(:stdCode) escape '/') ");
                paramList.put("stdCode", "%" + this.sqlLikeReplace(stdCode) + "%");
            }
            if (inUse != null) {
                hql.append(" and t.inUse = :inUse ");
                paramList.put("inUse", inUse);
            }
            hql.append(" order by t.sortNum, t.name, t.sname ");
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 数据效验
     * @param company
     * @return
     */
    @Override
    public CommonResult companyValidation(Company company) {
        CommonResult commonResult = new CommonResult();
        try {
            // “名称”唯一性校验，提示“该企业名称已存在！”;
            StringBuilder hql = new StringBuilder(
                    "from Company t where t.name =:name and t.companyId<>:companyId");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("name", company.getName());
            paramList.put("companyId", company.getCompanyId() == null ? 0 : company.getCompanyId());
            long index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("该企业名称已存在！");
            }

            // “简称”唯一性校验，提示“该企业简称已存在！”;
            hql = new StringBuilder(
                    "from Company t where t.sname =:sname and t.companyId<>:companyId");
            paramList = new HashMap<String, Object>();
            paramList.put("sname", company.getSname());
            paramList.put("companyId", company.getCompanyId() == null ? 0 : company.getCompanyId());
            index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("该企业简称已存在！");
            }

            // “标准编码”唯一性校验，提示“该标准编码已存在！”;
            hql = new StringBuilder(
                    "from Company t where t.stdCode =:stdCode and t.companyId<>:companyId");
            paramList = new HashMap<String, Object>();
            paramList.put("stdCode", company.getStdCode());
            paramList.put("companyId", company.getCompanyId() == null ? 0 : company.getCompanyId());
            index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("该标准编码已存在！");
            }
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }
    @Override
    public Integer getCompanyIdByStdCode(String stdCode) {
        try {
            String sql = "select company_id from t_pm_company a where a.std_code ='" + stdCode+"'";
            Query query = getEntityManager().createNativeQuery(sql.toString());
            BigInteger str = (BigInteger) query.getSingleResult();
            return str.intValue();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
