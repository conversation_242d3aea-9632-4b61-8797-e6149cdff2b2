package com.pcitc.opal.pm.dao.imp;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 报警剔除配置
 * @TableName t_pm_alarmpointdelconfig
 */
@Data
public class AlarmPointDelConfigDTO implements Serializable {

    public AlarmPointDelConfigDTO() {
    }

    public AlarmPointDelConfigDTO(
            Long alarmPointDelConfigId,
            String unitCode,
            String unitName,
            String groupName,
            Long alarmPointGroupId,
            Date delStartTime,
            Date delEndTime,
            Integer inUse,
            Date crtDate,
            Date mntDate,
            String crtUserName,
            String mntUserName,
            String des) {
        this.alarmPointDelConfigId = alarmPointDelConfigId;
        this.unitCode = unitCode;
        this.unitName = unitName;
        this.groupName = groupName;
        this.alarmPointGroupId = alarmPointGroupId;
        this.delStartTime = delStartTime;
        this.delEndTime = delEndTime;
        this.inUse = inUse;
        this.crtDate = crtDate;
        this.mntDate = mntDate;
        this.crtUserName = crtUserName;
        this.mntUserName = mntUserName;
        this.des = des;
    }

    public AlarmPointDelConfigDTO(
            Long alarmPointDelConfigId,
            String unitCode,
            String unitName,
            String groupName,
            Long alarmPointGroupId,
            Date delStartTime,
            Date delEndTime,
            Integer inUse,
            Date crtDate,
            Date mntDate,
            String crtUserName,
            String mntUserName,
            String des,
            Integer delStatus) {
        this.alarmPointDelConfigId = alarmPointDelConfigId;
        this.unitCode = unitCode;
        this.unitName = unitName;
        this.groupName = groupName;
        this.alarmPointGroupId = alarmPointGroupId;
        this.delStartTime = delStartTime;
        this.delEndTime = delEndTime;
        this.inUse = inUse;
        this.crtDate = crtDate;
        this.mntDate = mntDate;
        this.crtUserName = crtUserName;
        this.mntUserName = mntUserName;
        this.des = des;
        this.delStatus = delStatus;
    }

    public AlarmPointDelConfigDTO(
            Long alarmPointDelConfigId,
            String unitCode,
            String unitName,
            String groupName,
            Long alarmPointGroupId,
            Date delStartTime,
            Date delEndTime,
            Integer inUse,
            Date crtDate,
            Date mntDate,
            String crtUserName,
            String mntUserName,
            String des,
            Integer delStatus,
            Integer delDataStatus) {
        this.alarmPointDelConfigId = alarmPointDelConfigId;
        this.unitCode = unitCode;
        this.unitName = unitName;
        this.groupName = groupName;
        this.alarmPointGroupId = alarmPointGroupId;
        this.delStartTime = delStartTime;
        this.delEndTime = delEndTime;
        this.inUse = inUse;
        this.crtDate = crtDate;
        this.mntDate = mntDate;
        this.crtUserName = crtUserName;
        this.mntUserName = mntUserName;
        this.des = des;
        this.delStatus = delStatus;
        this.delDataStatus = delDataStatus;
    }

    /**
     * 报警剔除配置ID
     */
    private Long alarmPointDelConfigId;


    /**
     * 装置编码
     */
    private String unitCode;

    /**
     * 装置编码
     */
    private String unitName;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 报警点分组ID
     */
    private Long alarmPointGroupId;

    /**
     * 剔除开始时间
     */
    private Date delStartTime;

    /**
     * 剔除结束时间
     */
    private Date delEndTime;

    /**
     *是否启用（1是；0否）
     */
    private Integer inUse;


    /**
     * 创建时间
     */
    private Date crtDate;

    /**
     * 维护时间
     */
    private Date mntDate;


    /**
     * 创建人名称
     */
    private String crtUserName;

    /**
     * 最后维护人名称
     */
    private String mntUserName;

    /**
     * 描述
     */
    private String des;

    /**
     * 剔除状态
     */
    private Integer delStatus;

    /**
     * 数据剔除状态 (0未剔除，1剔除中，2事件表剔除失败，3记录表剔除失败，4剔除失败，5数据已剔除)
     */
    private Integer delDataStatus;

}