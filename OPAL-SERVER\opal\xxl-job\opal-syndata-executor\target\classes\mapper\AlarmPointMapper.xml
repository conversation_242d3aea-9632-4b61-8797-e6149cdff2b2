<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.mapper.AlarmPointMapper">

    <resultMap id="BaseResultMap" type="com.pcitc.opal.pojo.AlarmPoint">
            <id property="alarmPointId" column="alarm_point_id" jdbcType="BIGINT"/>
            <result property="prdtcellId" column="prdtcell_id" jdbcType="BIGINT"/>
            <result property="tag" column="tag" jdbcType="VARCHAR"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="pidCode" column="pid_code" jdbcType="VARCHAR"/>
            <result property="alarmPointTypeId" column="alarm_point_type_id" jdbcType="BIGINT"/>
            <result property="monitorType" column="monitor_type" jdbcType="BIGINT"/>
            <result property="measunitId" column="measunit_id" jdbcType="BIGINT"/>
            <result property="instrmtType" column="instrmt_type" jdbcType="BIGINT"/>
            <result property="virtualRealityFlag" column="virtual_reality_flag" jdbcType="BIGINT"/>
            <result property="alarmPointHh" column="alarm_point_hh" jdbcType="BIGINT"/>
            <result property="alarmPointHi" column="alarm_point_hi" jdbcType="BIGINT"/>
            <result property="alarmPointLo" column="alarm_point_lo" jdbcType="BIGINT"/>
            <result property="alarmPointLl" column="alarm_point_ll" jdbcType="BIGINT"/>
            <result property="inUse" column="in_use" jdbcType="BIGINT"/>
            <result property="crtDate" column="crt_date" jdbcType="TIMESTAMP"/>
            <result property="mntDate" column="mnt_date" jdbcType="TIMESTAMP"/>
            <result property="crtUserId" column="crt_user_id" jdbcType="VARCHAR"/>
            <result property="mntUserId" column="mnt_user_id" jdbcType="VARCHAR"/>
            <result property="crtUserName" column="crt_user_name" jdbcType="VARCHAR"/>
            <result property="mntUserName" column="mnt_user_name" jdbcType="VARCHAR"/>
            <result property="sortNum" column="sort_num" jdbcType="BIGINT"/>
            <result property="des" column="des" jdbcType="VARCHAR"/>
            <result property="craftUpLimitInclude" column="craft_up_limit_include" jdbcType="BIGINT"/>
            <result property="craftDownLimitInclude" column="craft_down_limit_include" jdbcType="BIGINT"/>
            <result property="craftUpLimitValue" column="craft_up_limit_value" jdbcType="BIGINT"/>
            <result property="craftDownLimitValue" column="craft_down_limit_value" jdbcType="BIGINT"/>
            <result property="interlockUpLimitInclude" column="interlock_up_limit_include" jdbcType="BIGINT"/>
            <result property="interlockDownLimitInclude" column="interlock_down_limit_include" jdbcType="BIGINT"/>
            <result property="interlockUpLimitValue" column="interlock_up_limit_value" jdbcType="BIGINT"/>
            <result property="interlockDownLimitValue" column="interlock_down_limit_value" jdbcType="BIGINT"/>
            <result property="craftRank" column="craft_rank" jdbcType="BIGINT"/>
            <result property="virtualFlag" column="virtual_flag" jdbcType="BIGINT"/>
            <result property="instrmtPriority" column="instrmt_priority" jdbcType="BIGINT"/>
            <result property="inSendmsg" column="in_sendmsg" jdbcType="BIGINT"/>
            <result property="mobilePhone" column="mobile_phone" jdbcType="VARCHAR"/>
            <result property="rtdbTag" column="rtdb_tag" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        alarm_point_id,prdtcell_id,tag,
        location,pid_code,alarm_point_type_id,
        monitor_type,measunit_id,instrmt_type,
        virtual_reality_flag,alarm_point_hh,alarm_point_hi,
        alarm_point_lo,alarm_point_ll,in_use,
        crt_date,mnt_date,crt_user_id,
        mnt_user_id,crt_user_name,mnt_user_name,
        sort_num,des,craft_up_limit_include,
        craft_down_limit_include,craft_up_limit_value,craft_down_limit_value,
        interlock_up_limit_include,interlock_down_limit_include,interlock_up_limit_value,
        interlock_down_limit_value,craft_rank,virtual_flag,
        instrmt_priority,in_sendmsg,mobile_phone,
        rtdb_tag,company_id
    </sql>
    <update id="updateValueById">
        update t_pm_alarmpoint
        <set>
            <if test="value != null">
                <if test="flagId == 4">
                    alarm_point_ll = #{value,jdbcType=NUMERIC},
                </if>
                <if test="flagId == 3">
                    alarm_point_lo = #{value,jdbcType=NUMERIC},
                </if>
                <if test="flagId == 2">
                    alarm_point_hi = #{value,jdbcType=NUMERIC},
                </if>
                <if test="flagId == 1">
                    alarm_point_hh = #{value,jdbcType=NUMERIC},
                </if>
            </if>

        </set>
        where alarm_point_id = #{alarmPointId,jdbcType=NUMERIC}
    </update>
    <select id="selectByCompanyIdToTenantDb" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_pm_alarmpoint
        where
        company_id = #{companyId,jdbcType=NUMERIC}
    </select>
</mapper>
