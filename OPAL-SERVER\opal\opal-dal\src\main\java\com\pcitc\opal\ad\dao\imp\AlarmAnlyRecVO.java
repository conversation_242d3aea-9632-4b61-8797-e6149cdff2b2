package com.pcitc.opal.ad.dao.imp;
import java.util.Date;

public class AlarmAnlyRecVO  {

    public AlarmAnlyRecVO(Long alarmAnlyRecId, Long reasonType, Long alarmReasonId, String name, Date crtTime, String crtUserName,
                          Date submitTime, String submitUserName, String reasonDes,Date confirmTime, String confirmUserName) {
        this.alarmAnlyRecId = alarmAnlyRecId;
        this.reasonType = reasonType;
        this.alarmReasonId = alarmReasonId;
        this.name = name;
        this.crtTime = crtTime;
        this.crtUserName = crtUserName;
        this.submitTime = submitTime;
        this.submitUserName = submitUserName;
        this.reasonDes = reasonDes;
        this.confirmTime = confirmTime;
        this.confirmUserName = confirmUserName;
    }

    /**
     * 报警分析记录ID
     */
    private Long alarmAnlyRecId;

    /**
     * 原因类型（1工艺类；2操作类；3设备类；4电气类；5公用工程类；6仪表类；7原料类；8其他）
     */
    private Long reasonType;

    /**
     * 报警原因ID
     */
    private Long alarmReasonId;

    /**
     *原因名称
     */
    private String name;

    /**
     *创建时间
     */
    private Date crtTime;
    /**
     *创建人名称
     */
    private String crtUserName;
    /**
     *提交时间
     */
    private Date submitTime;
    /**
     *提交人名称
     */
    private String submitUserName;

    /**
     *原因描述
     */
    private String reasonDes;

    private Date confirmTime;

    private String confirmUserName;

    public Long getAlarmReasonId() {
        return alarmReasonId;
    }

    public void setAlarmReasonId(Long alarmReasonId) {
        this.alarmReasonId = alarmReasonId;
    }

    public Long getAlarmAnlyRecId() {
        return alarmAnlyRecId;
    }

    public void setAlarmAnlyRecId(Long alarmAnlyRecId) {
        this.alarmAnlyRecId = alarmAnlyRecId;
    }

    public Long getReasonType() {
        return reasonType;
    }

    public void setReasonType(Long reasonType) {
        this.reasonType = reasonType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCrtTime() {
        return crtTime;
    }

    public void setCrtTime(Date crtTime) {
        this.crtTime = crtTime;
    }

    public String getCrtUserName() {
        return crtUserName;
    }

    public void setCrtUserName(String crtUserName) {
        this.crtUserName = crtUserName;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public String getSubmitUserName() {
        return submitUserName;
    }

    public void setSubmitUserName(String submitUserName) {
        this.submitUserName = submitUserName;
    }

    public String getReasonDes() {
        return reasonDes;
    }

    public void setReasonDes(String reasonDes) {
        this.reasonDes = reasonDes;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getConfirmUserName() {
        return confirmUserName;
    }

    public void setConfirmUserName(String confirmUserName) {
        this.confirmUserName = confirmUserName;
    }
}

