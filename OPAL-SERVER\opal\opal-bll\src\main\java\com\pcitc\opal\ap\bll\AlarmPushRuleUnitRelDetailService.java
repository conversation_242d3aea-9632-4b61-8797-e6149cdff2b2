package com.pcitc.opal.ap.bll;

import com.pcitc.opal.ap.bll.entity.AlarmPushRuleDetailEntity;
import com.pcitc.opal.ap.bll.entity.AlarmPushRuleUnitRelDetailEntity;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleDetailEntityVO;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleUnitRelDetailEntityVO;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.Unit;
import org.springframework.stereotype.Service;

import java.util.List;

/*
 * 报警知识管理业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmKnowlgManagmtService
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理业务逻辑层接口
 */
@Service
public interface AlarmPushRuleUnitRelDetailService {

    /**
     * 获取列表数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPushRuleId id
     * @throws Exception 
     * @return PaginationBean<AlarmPointEntity> 分页对象
     */
    PaginationBean<AlarmPushRuleUnitRelDetailEntityVO> getAlarmPushRuleUnitRelDetail(Long alarmPushRuleId, Pagination page) throws  Exception;


    /**
     * 新增数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPushRuleUnitRelDetailEntity 报警点实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult addAlarmPushRuleUnitRelDetail(AlarmPushRuleUnitRelDetailEntity alarmPushRuleUnitRelDetailEntity) throws Exception;


    /**
     * 删除报警点分组数据
     *
      * <AUTHOR> 2017-10-11
     * @param ids 报警点分组主键Id集合
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult deleteAlarmPushRuleUnitRelDetail(Long[] ids) throws Exception;

    /**
     * 删除报警点分组数据
     *
      * <AUTHOR> 2017-10-11
     * @param ids 报警点分组主键Id集合
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult deleteAlarmPushRuleUnitRelDetailOne(Long ids) throws Exception;
    List<Unit> getAllUnitsUsed(Integer speciality,Long priority) throws Exception;
    /**
     * 报警点分组更新数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPushRuleUnitRelDetailEntity 报警点分组实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult updateAlarmPushRuleUnitRelDetail(AlarmPushRuleUnitRelDetailEntity alarmPushRuleUnitRelDetailEntity) throws Exception;
}
