var relevantTagConfigUrl = OPAL.API.afUrl+ '/relevantTagConfig';
var searchUrl = OPAL.API.afUrl+ '/relevantTagConfig/getAlarmPoint';
var UnitPrdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
window.pageLoadMode = PageLoadMode.None;
var tag, alarmPointId;
$(function () {
    var index = parent.layer.getFrameIndex(window.name);
    var page = {
        /**
         * 初始化
         */
        init: function () {
            //绑定事件
            this.bindUI();
            //初始化查询装置树
            
        },
        /**
         * 绑定事件
         */
        bindUI: function () {
            // 新增
            $('#confirmBtn').click(function () {
                page.logic.save();
            })
            //批量删除
            $('#closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
            //查询
            $('#searchBtn').click(function () {
                page.logic.search();
            })
        },
        data: {
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                page.logic.initUnitTree();
                //初始化表格
                page.logic.initTable();
                $("#unitId").combotree('setValue', data['unitId']);
                page.logic.searchUnitPrdt(data["unitId"]);
                $("#prdtCellId").combotree('setValue', data["prdtCellId"]);
                $("#unitId").combo('disable');
                $("#prdtCellId").combo('disable');
            },
            /**
             * 初始化表格
             */
            initTable: function () {
                OPAL.ui.initBootstrapTable("table",{
                    cache:false,
                    columns: [{
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "生产单元",
                        field: 'prdtCellSname',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "装置",
                        field: 'unitSname',
                        rowspan: 1,
                        align: 'left',
                    },  {
                        title: "计量单位",
                        field: 'measunitName',
                        rowspan: 1,
                        align: 'left',
                    },{
                        title: "描述",
                        field: 'des',
                        rowspan: 1,
                        align: 'left',
                    }],
                },page.logic.queryParams)
                $(table).on('click-row.bs.table', function (e, row, element){
                    tag = row.tag;
                    alarmPointId = row.alarmPointId;
                    $('.add-class-bg').removeClass('add-class-bg');//去除之前选中的行的，选中样式
                    $(element).addClass('add-class-bg');//添加当前选中的 success样式用于区别
                }); 
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param,param);
            },
            /**
             * 搜索
             */
            search: function () {
                page.data.param = OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber":1
                });
            },
            save: function() {
                if(tag == undefined) {
                    layer.msg("请选择位号！");
                    return;
                }
                parent.tag = tag;
                parent.alarmPointId = alarmPointId;
                window.pageLoadMode = PageLoadMode.Refresh;
                page.logic.closeLayer();
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect("unitId", commonUnitTreeUrl, "id", "parentId", "sname", {
                    multiple: false,
                    onlyLeafCheck: true,
                    async:false,
                    data: {
                        'enablePrivilege': false
                    },
                    onSelect: function(node) {
                        OPAL.ui.getComboMultipleSelect("prdtCellId", UnitPrdtCellUrl, {
                            keyField: "prdtCellId",
                            valueField: "sname",
                            async:false,
                            data: {
                                "unitId": node.id
                            },
                            treeviewConfig: {
                                multiple: false,
                                cascadeCheck: false,
                                onlyLeafCheck: true,
                                hasDownArrow: true,
                                lines: false,
                                animate: false,
                            }
                        }, false, function() {
                            $("#prdtCellId").combotree('selectFirstRecord');
                        });
                    }
                }, false, function() {
                });
                $("#unitId").combotree("getValues");
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect("prdtCellId", UnitPrdtCellUrl, {
                        keyField: "prdtCellId",
                        valueField: "sname",
                        async: false,
                        data: {
                            "unitId": unitId
                        },
                        treeviewConfig: {
                            multiple: false,
                            cascadeCheck: false,
                            onlyLeafCheck: true,
                            hasDownArrow: true,
                            lines: false
                        }
                    }, true,
                    function() {

                    }
                );
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
        }
    }
    page.init();
    window.page = page;
})