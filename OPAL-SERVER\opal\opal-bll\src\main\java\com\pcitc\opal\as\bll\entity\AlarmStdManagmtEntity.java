package com.pcitc.opal.as.bll.entity;

import com.pcitc.opal.common.CommonEnum;

import java.io.InputStream;
import java.util.Date;

/*
 * 报警制度管理实体
 * 模块编号：pcitc_opal_bll_class_AlarmStdManagmtEntity
 * 作	者：kun.zhao
 * 创建时间：2018/2/28
 * 修改编号：1
 * 描	述：报警制度管理实体
 */
public class AlarmStdManagmtEntity {
	
	/**
     * 报警制度管理ID
     */
    private Long alarmStdManagmtId;

    /**
     * 名称
     */
    private String name;

    /**
     * 分类（1公司级；2工厂级；3车间级）
     */
    private Integer catgr;
    
    /**
     * 分类名称
     */
    @SuppressWarnings("unused")
	private String catgrName;

    /**
     * 上传时间
     */
    private Date uplTime;

    /**
     * 上传人名称
     */
    private String uplUserName;

    /**
     * 上传附件ID
     */
    private String uplAttaId;

    /**
     * 上传附件名称
     */
    private String uplAttaName;

    /**
     * 描述
     */
    private String des;

	/**
	 * 上传附件流
	 */
	private InputStream uplAttaStream;

	public Long getAlarmStdManagmtId() {
		return alarmStdManagmtId;
	}

	public void setAlarmStdManagmtId(Long alarmStdManagmtId) {
		this.alarmStdManagmtId = alarmStdManagmtId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getCatgr() {
		return catgr;
	}

	public void setCatgr(Integer catgr) {
		this.catgr = catgr;
	}

	public String getCatgrName() {
		if (catgr == null) return "";
	    return CommonEnum.AlarmStdManagmtCatgrEnum.getName(catgr);
	}

	public void setCatgrName(String catgrName) {
		this.catgrName = catgrName;
	}

	public Date getUplTime() {
		return uplTime;
	}

	public void setUplTime(Date uplTime) {
		this.uplTime = uplTime;
	}

	public String getUplUserName() {
		return uplUserName;
	}

	public void setUplUserName(String uplUserName) {
		this.uplUserName = uplUserName;
	}

	public String getUplAttaId() {
		return uplAttaId;
	}

	public void setUplAttaId(String uplAttaId) {
		this.uplAttaId = uplAttaId;
	}

	public String getUplAttaName() {
		return uplAttaName;
	}

	public void setUplAttaName(String uplAttaName) {
		this.uplAttaName = uplAttaName;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}

	public InputStream getUplAttaStream() {
		return uplAttaStream;
	}

	public void setUplAttaStream(InputStream uplAttaStream) {
		this.uplAttaStream = uplAttaStream;
	}
}
