package com.pcitc.opal.af.bll.entity;

import java.util.Date;

/*
 * 震荡报警分析表格数据实体
 * 模块编号：pcitc_opal_bll_class_ShakeAlarmAnalysisTableEntity
 * 作  　  者：kun.zhao
 * 创建时间：2017-11-2
 * 修改编号：1
 * 描       述：震荡报警分析表格数据实体
 */
public class ShakeAlarmAnalysisTableEntity {

	/**
	 *报警事件ID
	 */
	private Long eventId;

	/**
	 * 装置编码
	 */
	private String unitId;
	/**
	 * 报警时间
	 */
	private Date alarmTime;
	/**
	 * 优先级
	 */
	private String priority;
	/**
	 * 值
	 */
	private String nowValue;
	/**
	 * 限值
	 */
	private Double limitValue;
	/**
	 * 计量单位
	 */
	private String measUnitName;
	/**
	 * 报警事件描述信息
	 */
	private String des;

	private String unitName;

	private String prdtName;

	private String alarmPointTag;

	private String alarmFlagName;

	private String location;

	private String monitorTypeStr;

	public Long getEventId() {
		return eventId;
	}

	public void setEventId(Long eventId) {
		this.eventId = eventId;
	}

	public String getUnitId() {
		return unitId;
	}
	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}
	public Date getAlarmTime() {
		return alarmTime;
	}
	public void setAlarmTime(Date alarmTime) {
		this.alarmTime = alarmTime;
	}
	public String getPriority() {
		return priority;
	}
	public void setPriority(String priority) {
		this.priority = priority;
	}
	public String getNowValue() {
		return nowValue;
	}
	public void setNowValue(String nowValue) {
		this.nowValue = nowValue;
	}

	public Double getLimitValue() {
		return limitValue;
	}
	public void setLimitValue(Double limitValue) {
		this.limitValue = limitValue;
	}
	public String getMeasUnitName() {
		return measUnitName;
	}
	public void setMeasUnitName(String measUnitName) {
		this.measUnitName = measUnitName;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getPrdtName() {
		return prdtName;
	}

	public void setPrdtName(String prdtName) {
		this.prdtName = prdtName;
	}

	public String getAlarmPointTag() {
		return alarmPointTag;
	}

	public void setAlarmPointTag(String alarmPointTag) {
		this.alarmPointTag = alarmPointTag;
	}

	public String getAlarmFlagName() {
		return alarmFlagName;
	}

	public void setAlarmFlagName(String alarmFlagName) {
		this.alarmFlagName = alarmFlagName;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getMonitorTypeStr() {
		return monitorTypeStr;
	}

	public void setMonitorTypeStr(String monitorTypeStr) {
		this.monitorTypeStr = monitorTypeStr;
	}
}
