var shakeAlarmAnalysisUrl = OPAL.API.afUrl + '/shakeAlarmAnalysis/getShakeAlarmAnalysisEntity'
var getShakeAlarmAnalysisTableEntityUrl = OPAL.API.afUrl + '/shakeAlarmAnalysis/getShakeAlarmAnalysisTableEntity'
var getShakeAlarmAnalysisChartEntityUrl = OPAL.API.afUrl + '/shakeAlarmAnalysis/getShakeAlarmAnalysisChartEntity'
var getChangeShakeAlarmAnalysislUrl = OPAL.API.afUrl + '/shakeAlarmAnalysis/getChangeShakeAlarmAnalysisl'
var exportShakeAlarmAnalysisUrl = OPAL.API.afUrl + '/shakeAlarmAnalysis/exportShakeAlarmAnalysis'
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var alarmFlagIdArr = new Array();
var alarmFlagNameArr = new Array();
var unitNameArr = new Array();
var prdtNameArr = new Array();
var craftRankNameArr = new Array();
var limitValueArr = new Array();
var locationArr = new Array();
var upLimitValue, downLimitValue; //工艺卡片的范围
var alarmFlagId, alarmFlagName, alarmPointTag; // 位号id
var floodAlarmChart;
var myChart;
var seriesNow = new Array();
var queryTimeArray; // 固定时间点 10：00
var leftValue, rightValue;
var isLoading = true;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            this.bindUi();

            page.logic.getQueryTime();
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                myChart.resize();
                floodAlarmChart.resize();
            };
            //初始化查询装置树
            page.logic.initUnitTree();
            // 日期扩展
            OPAL.util.extendDate();
            // 初始化日期时间选择控件组
            page.logic.initTime();
            // 初始化上面图表
            page.logic.initUpChart();
            // 初始化表格
            page.logic.initTable();
            //初始化下面图表
            page.logic.initDownChart();

            if (isLoading && (page.data.param.unitIds == null || page.data.param.unitIds == undefined || page.data.param.unitIds.length == 0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("ShakeAlarmAnalysis");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
            page.logic.initPage();
        },
        bindUi: function () {
            $('#search').click(function () {
                if (!page.logic.checkDateIsValid()) return;
                if (!OPAL.util.checkDateIsValid()) {
                    return false;
                }
                isLoading = false;
                // 初始化图表
                page.logic.initPage();
            });
            $('#ShakeAlarmAnalysisExport').click(function() {
                page.logic.exportExcel();
            });

            /**
             * 导航切换
             */
            $('.myTab li').click(function () {
                var flag = $(this).attr('showFlag');
                if (flag == 'imgShow') {
                    $(this).find('img').attr('src', '../../../images/one1.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/tweo.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                } else if (flag == 'tableShow') {
                    $(this).find('img').attr('src', '../../../images/tweos.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                } else if (flag == 'unitShow') {
                    $(this).find('img').attr('src', '../../../images/treese.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/tweo.png');
                }
            })

            // 关闭
            $('#closePage').click(function () {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer(false);
            });

            $('#sureBtn').click(function () {
                if ($('#alarmValue').val() == alarmValue) {
                    layer.confirm('修改值须做修改！', {
                        btn: ['确定']
                    })
                    return false;
                }
                page.logic.refreshDetail();
            });
        },
        data: {
            // 设置查询参数
            param: {}
        },
        logic: {
            /**
             * 获得固定的时间点
             */
            getQueryTime: function (dateTimeList, unitIdList, id) {
                OPAL.util.getQueryTime(function (queryTime) {
                    queryTimeArray = queryTime;
                });
            },

            /**
             * 校验时间
             */
            checkDateIsValid: function () {
                var startTime = OPAL.util.strToDate($('#startTime').val());
                var endTime = OPAL.util.strToDate($('#endTime').val());
                if ($('#startTime').val() == "" || $('#endTime').val() == "" || $('#startTime').val() == undefined || $('#endTime').val() == undefined) {
                    layer.msg("开始时间和结束时间不能为空！");
                    return false;
                } else if ((endTime - startTime) < 0) {
                    layer.msg("报警开始时间不能大于结束时间！");
                    return false;
                } else if ((endTime - startTime) > (1000 * 60 * 60 * 24 * 30)) {
                    layer.msg("查询时间范围不能超过30天！");
                    return false;
                }
                return true;
            },
            // 初始化时间
            initTime: function () {
                OPAL.ui.initDateTimePeriodPicker({
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm:ss'
                })
            },
            initPage: function () {
                $("#search").attr("disabled", true);
                $.ajax({
                    url: shakeAlarmAnalysisUrl,
                    async: true,
                    data: OPAL.form.getData("searchForm"),
                    dataType: "JSON",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        page.logic.initUpChart(res);
                        $("#search").attr("disabled", false);
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                        $('#search').attr('disabled', false);
                    }
                });
            },
            initUpChart: function (data) {
                if (data == undefined) {
                    data = []
                }
                if (data.length != 0) {
                    $('#msgLine').css('display', 'block');
                } else {
                    $('#msgLine').css('display', 'none');
                }
                if (myChart && !myChart.isDisposed()) {
                    myChart.clear();
                    myChart.dispose();
                }
                if (data == undefined || data == null || data.length == 0) {
                    myChart = OPAL.ui.chart.initEmptyChart('shock-alarm-chart_list');
                    $("#floodTable").bootstrapTable("removeAll");
                    return;
                }
                myChart = echarts.init(document.getElementById('shock-alarm-chart_list'));
                option = {
                    color: ['#3398DB'],
                    grid: {
                        left: '1%',
                        right: '3%',
                        bottom: '10%',
                        top: '10%',
                        height: '240px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        axisTick: {
                            alignWithLabel: true
                        },
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 0,
                            rotate: 15,
                            textStyle: {
                                fontSize: 10
                            }
                        }
                    }],
                    yAxis: [{
                        name: '报警数\r\r\r\r\r\r\r\r\r',
                        type: 'value',
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        }
                    }],
                    series: [{
                        name: '',
                        type: 'bar',
                        barWidth: '15',
                        data: [],
                    }],
                    dataZoom: [{
                        type: 'slider',
                        yAxisIndex: 0,
                        filterMode: 'none',
                        width: 20
                    }, {
                        orient: "horizontal",
                        show: true,
                        start: 0,
                        end: 100,
                        height: 20,
                        bottom: 2,
                    }]
                };
                page.logic.chartClick(myChart);
                page.logic.setCharts(data);
            },
            initDownChart: function (data) {
                if (data == undefined) {
                    data = []
                }
                if (floodAlarmChart && !floodAlarmChart.isDisposed()) {
                    floodAlarmChart.clear();
                    floodAlarmChart.dispose();
                }
                if (data == undefined || data == null || data.length == 0) {
                    floodAlarmChart = OPAL.ui.chart.initEmptyChart('floodAlarmChart');
                    $("#floodTable").bootstrapTable("removeAll");
                    return;
                }
            },
            chartClick: function (myChart) {
                // 点击图表 改变颜色
                myChart.on('click', function (params) {
                    var i = params.dataIndex;
                    page.logic.changChartColor(myChart, option, i);
                    alarmFlagId = alarmFlagIdArr[i];
                    alarmPointTag = params.name;
                    alarmFlagName = alarmFlagNameArr[i];
                    unitName = unitNameArr[i];
                    prdtName = prdtNameArr[i];
                    craftRankName = craftRankNameArr[i];
                    limitValue = limitValueArr[i];
                    locationName = locationArr[i];

                    $('#unitName').html(unitName);
                    $('#prdtCellName').html(prdtName);
                    $('#tag').html(alarmPointTag);
                    $('#alarmFlagName').html(alarmFlagName);
                    $('#craftRankName').html(craftRankName);
                    $('#limitValue').html(limitValue);
                    $('#locationName').html(locationName);

                    page.data.param = {
                        alarmFlagId: alarmFlagId,
                        alarmPointTag: alarmPointTag,
                        startTime: $('#startTime').val(),
                        endTime: $('#endTime').val(),
                        now: Math.random()
                    };

                    $('#floodTable').bootstrapTable('refresh', {
                        "url": getShakeAlarmAnalysisTableEntityUrl,
                        "pageNumber": 1
                    });
                    page.logic.initChart();

                })
            },
            // 初始化 图表
            setCharts: function (data) {
                alarmFlagIdArr = new Array();
                alarmFlagNameArr = new Array();
                unitNameArr = new Array();
                prdtNameArr = new Array();
                craftRankNameArr = new Array();
                limitValueArr = new Array();
                locationArr = new Array();
                if (data != undefined && data != null && data != '') {
                    var res = data;
                    alarmFlagId = res[0].alarmFlagId; // 默认第一条柱子
                    alarmFlagName = res[0].alarmFlagName;
                } else {
                    res = []
                }
                var totalArray = new Array();
                var tagArray = new Array();
                for (var i = 0; i < res.length; i++) {
                    tagArray.push(res[i]['alarmPointTag']);
                    totalArray.push(res[i]["count"]);
                    alarmFlagIdArr.push(res[i].alarmFlagId);
                    alarmFlagNameArr.push(res[i].alarmFlagName);
                    unitNameArr.push(res[i].unitName);
                    prdtNameArr.push(res[i].prdtName);
                    craftRankNameArr.push(res[i].craftRankName);
                    limitValueArr.push(res[i].limitValue);
                    locationArr.push(res[i].location);
                }
                option.xAxis[0].data = tagArray;
                option.series[0].data = totalArray;
                option.tooltip = {
                    trigger: 'axis',
                    axisPointer: { // 坐标轴指示器，坐标轴触发有效
                        type: '' // 默认为直线，可选为：'line' | 'shadow'
                    },
                    formatter: function (params) {
                        if (params[0] != null && params[0] != undefined && params[0] != '') {
                            var tag = params[0].name;
                            var count = params[0].value;
                            var dataIndex = params[0].dataIndex;
                            var alarmFlagName = res[dataIndex].alarmFlagName;
                        }
                        var str = '位号：' + tag + '&nbsp;&nbsp;&nbsp;报警标识：' + alarmFlagName + '<br>报警数：' + count
                        return str;
                    }
                }
                alarmPointTag = tagArray[0];
                unitName = unitNameArr[0];
                prdtName = prdtNameArr[0];
                craftRankName = craftRankNameArr[0];
                limitValue = limitValueArr[0];
                locationName = locationArr[0];
                if (data.length == 0) {
                    $("#floodTable").bootstrapTable("removeAll");
                } else {

                    page.logic.processLimitValueDisplay(alarmFlagName);
                    $('#unitName').html(unitName);
                    $('#prdtCellName').html(prdtName);
                    $('#tag').html(alarmPointTag);
                    $('#alarmFlagName').html(alarmFlagName);
                    $('#craftRankName').html(craftRankName);
                    $('#limitValue').html(limitValue);
                    $('#locationName').html(locationName);

                    page.data.param = {
                        alarmFlagId: alarmFlagId,
                        alarmPointTag: alarmPointTag,
                        startTime: $('#startTime').val(),
                        endTime: $('#endTime').val(),
                        now: Math.random()
                    };
                    //加载下面列表
                    $('#floodTable').bootstrapTable('refresh', {
                        "url": getShakeAlarmAnalysisTableEntityUrl,
                        "pageNumber": 1
                    });

                    //加载下面图表
                    page.logic.initChart();
                }
                page.logic.changChartColor(myChart, option, 0);
            },

            changChartColor: function (myChart, option, i) {
                option.series[0].itemStyle = {
                    normal: {
                        color: function (params) {
                            if (params.dataIndex == i) {
                                return "#ffac28";
                            } else {
                                return "#2a8ce4";
                            }
                        }
                    }
                };
                myChart.setOption(option);
            },
            initTable: function () {
                OPAL.ui.initBootstrapTable("floodTable", {
                    columns: [{
                        title: "序号", formatter: function (value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1) * pageSize;
                        }, rowspan: 1, align: 'center', width: '60px'
                    }, {
                        title: "装置", field: 'unitName', align: 'center', width: '140px'
                    }, {
                        title: "生产单元", field: 'prdtName', align: 'center', width: '140px'
                    }, {
                        title: "报警时间", field: 'alarmTime', align: 'center', width: '140px'
                    }, {
                        title: "参数名称", field: 'location', align: 'center', width: '140px'
                    }, {
                        title: "位号", field: 'alarmPointTag', align: 'center', width: '140px'
                    }, {
                        title: "报警等级", field: 'alarmFlagName', align: 'center', width: '80px'
                    }, {
                        title: "优先级", field: 'priority', align: 'center', width: '60px'
                    }, {
                        title: "专业", field: 'monitorTypeStr', align: 'left', width: '60px'
                    }, {
                        title: "单位", field: 'measUnitName', align: 'left', width: '80px'
                    }],
                    responseHandler: function (res) {
                        var rows = $.ET.toObjectArr(res);

                        for (var i = 0; i < rows.length; i++) {
                            rows[i].unitName = unitName
                            rows[i].prdtName = prdtName;
                            rows[i].alarmPointTag = alarmPointTag;
                            rows[i].alarmFlagName = alarmFlagName;
                        }
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": rows
                        };
                        page.logic.processLimitValueDisplay(alarmFlagName);
                        $('#unitName').html(unitName);
                        $('#prdtCellName').html(prdtName);
                        $('#tag').html(alarmPointTag);
                        $('#alarmFlagName').html(alarmFlagName);
                        $('#craftRankName').html(craftRankName);
                        $('#limitValue').html(limitValue);
                        $('#locationName').html(locationName);
                        return item;
                    },
                }, page.logic.queryParams);
            },

            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) {
                var startTime = $('#startTime').val();
                var endTime = $('#endTime').val();
                if (!OPAL.util.checkDateIsValid()) {
                    return false;
                }
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function (nodes) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (nodeIds != undefined && nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    }
                }, false);
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function (unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },

            /**
             * 查询下面图表数据
             */
            initChart: function () {
                $.ajax({
                    url: getShakeAlarmAnalysisChartEntityUrl,
                    async: true,
                    data: {
                        startTime: OPAL.form.getData("searchForm").startTime,
                        endTime: OPAL.form.getData("searchForm").endTime,
                        alarmPointTag: alarmPointTag,
                        alarmFlagId: alarmFlagId
                    },
                    dataType: "JSON",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        if (res != null && res != undefined && res != '') {

                            if (floodAlarmChart && !floodAlarmChart.isDisposed()) {
                                floodAlarmChart.clear();
                                floodAlarmChart.dispose();
                            }

                            var alarmTimes = res[0].alarmTimes; //报警次数
                            alarmValue = res[0].alarmValue; // 修改值
                            maxValue = res[0].maxValue;
                            minValue = res[0].minValue;
                            var nowAlarmTimes = res[0].nowAlarmTimes;
                            var reduceRate = res[0].reduceRate; //减少比例
                            downLimitValue = res[0].downLimitValue;
                            upLimitValue = res[0].upLimitValue;
                            unitFlag = res[0].unitFlag;
                            limitValue = res[0].limitValue;
                            limitValueLength = page.logic.limitValueLength(limitValue);
                            var upAnddownLimitValue = page.logic.upAnddownLimitValue(downLimitValue, upLimitValue);
                            page.logic.upValueRange(res);

                            $('#upAnddownLimitValue').html(upAnddownLimitValue);
                            $('#upLimitValue').val(upLimitValue);
                            $('#downLimitValue').val(downLimitValue);
                            $('#alarmTimes').html(alarmTimes);
                            var histogramData = res[0].histogramData;
                            var lineChartData = res[0].lineChartData;
                            if (alarmFlagName == 'PVHH' || alarmFlagName == 'PVHI' || alarmFlagName == 'PVLO' || alarmFlagName == 'PVLL') {
                                $('#alarmValueBox').css('display', 'inline-block');
                                $('#nowAlarmTimesBox').css('display', 'inline-block');
                                $('#reduceRateBox').css('display', 'inline-block');
                                $('#alarmValue').val(alarmValue);
                                $('#nowAlarmTimes').html(nowAlarmTimes);
                                $('#reduceRate').html(reduceRate + '%');
                                $('#leftUpValue').html(leftValue);
                                $('#rightUpValue').html(rightValue);

                                page.logic.initFloodAlarmChart(histogramData, lineChartData);
                            } else {
                                $('#alarmValueBox').css('display', 'none');
                                $('#nowAlarmTimesBox').css('display', 'none');
                                $('#reduceRateBox').css('display', 'none');
                                page.logic.initChartsOther(histogramData, lineChartData);
                            }
                        }

                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            /**
             * 初始化图表 (报警标识为PVHH、PVHI、PVLO、PVLL)
             */
            initFloodAlarmChart: function (data1, data2) {
                var begin = $("#startTime").val();
                var end = $("#endTime").val();
                var anchor = [{
                    name: begin, value: [begin, 0]
                }, { name: end, value: [end, 0] }];
                var histogramData = JSON.parse(data1);
                var lineChartData = JSON.parse(data2);
                var seriesOld = new Array();
                var seriesAlarmValue = new Array();
                $.each(histogramData, function (i, el) {
                    seriesOld[i] = [histogramData[i].key, histogramData[i].value.key];
                    seriesNow[i] = [histogramData[i].key, histogramData[i].value.value];
                })
                $.each(lineChartData, function (i, el) {
                    seriesAlarmValue[i] = [lineChartData[i].key, lineChartData[i].value];
                })
                floodAlarmChart = echarts.init(document.getElementById('floodAlarmChart'));
                var option = {
                    toolbox: {
                        right: '20px',
                        // left: 'right',
                        itemSize: 15,
                        lineStyle: {
                            color: ['#e5e5e5']
                        },

                        top: -7,
                        feature: {
                            dataZoom: {
                                yAxisIndex: 'none'
                            },
                            restore: {}
                        }
                    },
                    tooltip: {
                        trigger: 'item', //axis
                        axisPointer: { // 坐标轴指示器，坐标轴触发有效
                            type: '' // 默认为直线，可选为：'line' | 'shadow'
                        }
                    },
                    grid: {
                        left: '1%',
                        right: '2%',
                        bottom: '1%',
                        top: '12%',
                        height: '220px',
                        containLabel: true
                    },
                    legend: {
                        left: '80px',
                        top: '7%',
                        itemWidth: 20, //设置icon大小
                        itemHeight: 10, //设置icon大小
                        data: [{
                            name: '报警数',
                            icon: 'roundRect',
                            textStyle: {
                                color: '#9d9d9d',
                            }
                        }, {
                            name: '报警值',
                            icon: '',
                            textStyle: {
                                color: '#9d9d9d',
                            }
                        }, {
                            name: '修改值',
                            icon: 'line',
                            textStyle: {
                                color: '#9d9d9d',
                            }
                        }, {
                            name: '报警值（限值）',
                            icon: 'line',
                            textStyle: {
                                color: '#9d9d9d',
                            }
                        }]
                    },
                    xAxis: [{
                        type: 'time',
                        minInterval: 3600 * 24 * 1000,
                        maxInterval: 3600 * 24 * 1000,
                        min: function (value) {
                            return Date.parse(($('#startTime').val()).replace(/-/g, "/")) - 3600 * 15 * 1000;
                        },
                        max: function (value) {
                            return Date.parse(($('#endTime').val()).replace(/-/g, "/")) + 3600 * 5 * 1000;
                        },
                        axisLine: {
                            onZero: false
                        },
                        axisLabel: {
                            interval: 0,
                            rotate: 50,
                            textStyle: {
                                fontSize: 10
                            },
                            showMinLabel: false,
                            showMaxLabel: false,
                            formatter: function (value, index) {
                                var date = new Date(value);
                                var y = date.getFullYear();
                                var m = date.getMonth() + 1;
                                var d = date.getDate();
                                return y + '-' + m + '-' + d;
                            }
                        },
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                    }],
                    yAxis: [{
                        name: '报警数',
                        type: 'value',
                        min: 0,
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        }
                    }, {
                        name: '报警值(' + unitFlag + ')',
                        nameLocation: 'end',
                        position: 'right',
                        // max: maxValue,
                        // min: 0,
                        //minInterval: 1,
                        //interval:1,
                        splitNumber: 3,
                        axisLabel: {
                            formatter: function (value, index) {
                                return value;
                            }
                        },
                        type: 'value',
                        splitLine: { //网格线
                            show: false,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        }
                    }],
                    series: [{
                        name: '原报警数',
                        type: 'bar',
                        barWidth: '15',
                        silent: false, // 图形是否不响应和触发鼠标事件
                        itemStyle: {
                            normal: {
                                color: '#e3e8ed'
                            },
                            emphasis: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        barGap: '-100%',
                        data: [],
                        tooltip: {
                            formatter: function (params) {
                                var seriesOldValue = seriesOld[(params.dataIndex)][1];
                                var seriesNowValue = seriesNow[(params.dataIndex)][1];
                                var name = seriesOld[(params.dataIndex)][0].replace(/\//g, "-");
                                var d1 = Date.parse(new Date(seriesOld[(params.dataIndex)][0]));
                                var d2 = d1 + (60 * 60 * 24 * 1000);
                                var Y = (new Date(d2)).getFullYear();
                                var M = (new Date(d2)).getMonth() + 1;
                                var D = (new Date(d2)).getDate();
                                if (Math.floor(M / 10) == 0) {
                                    M = '0' + M
                                }
                                if (Math.floor(D / 10) == 0) {
                                    D = '0' + D
                                }
                                var d3 = Y + "-" + M + "-" + D;
                                var timeData = '从：' + name + ' ' + queryTimeArray + '<br>至：' + d3 + ' ' + queryTimeArray + '<br>原报警数：' + seriesOldValue + '&nbsp;&nbsp;&nbsp;&nbsp;现报警数：' + seriesNowValue
                                return timeData;
                            }
                        }
                    }, {
                        name: '报警数',
                        type: 'bar',
                        barWidth: '15',
                        itemStyle: {
                            normal: {
                                color: '#3398DB',
                            }
                        },
                        data: [],
                        tooltip: {
                            formatter: function (params) {
                                var seriesOldValue = seriesOld[(params.dataIndex)][1];
                                var name = seriesOld[(params.dataIndex)][0].replace(/\//g, "-");
                                var d1 = Date.parse(new Date(seriesOld[(params.dataIndex)][0]));
                                var d2 = d1 + (60 * 60 * 24 * 1000);
                                var d3 = moment(d2).format("YYYY-MM-DD");
                                var timeData = '从：' + name + ' ' + queryTimeArray + '<br>至：' + d3 + ' ' + queryTimeArray + '<br>原报警数：' + seriesOldValue + '&nbsp;&nbsp;&nbsp;&nbsp;现报警数：' + params.value[1]
                                return timeData;
                            }
                        }
                    }, {
                        name: '报警值',
                        type: 'line',
                        yAxisIndex: 1,
                        smooth: true,
                        itemStyle: {
                            normal: {
                                color: '#1cb964',
                            }
                        },
                        data: seriesAlarmValue,
                        tooltip: {
                            formatter: function (params) {
                                var timeData = '报警时间：' + params.value[0] + ' <br> 报警值：' + params.value[1]
                                return timeData;
                            }
                        }
                    }, {
                        name: '报警值（限值）',
                        type: 'line',
                        yAxisIndex: 0,
                        itemStyle: {
                            normal: {
                                color: '#ffc200'
                            }
                        },
                        markLine: {
                            lineStyle: {
                                normal: {
                                    type: 'solid',
                                    color: '#ffc200'
                                }
                            },
                            silent: true,
                            label: {
                                normal: {
                                    show: false
                                }
                            },
                            data: [{
                                name: '报警值（限值）',
                                yAxis: 0
                            }]
                        }
                    }, {
                        name: '修改值',
                        yAxisIndex: 1,
                        type: 'line',
                        itemStyle: {
                            normal: {
                                color: '#ff6b5c'
                            }
                        },
                        markLine: {
                            silent: true,
                            label: {
                                normal: {
                                    show: true,
                                    position: 'middle',
                                }
                            },
                            lineStyle: {
                                normal: {
                                    type: 'solid',
                                    color: '#ff6b5c'
                                }
                            },
                            data: [{
                                name: '修改值',
                                yAxis: alarmValue
                            }]
                        }
                    },
                    ],
                    dataZoom: [{
                        type: 'slider',
                        yAxisIndex: 0,
                        left: '0',
                        filterMode: 'none',
                        width: 20
                    }]
                };
                if (alarmFlagName == 'PVHH' || alarmFlagName == 'PVHI') {
                    option.yAxis[0].inverse = false;
                    option.yAxis[1].inverse = false;
                    option.yAxis[1].nameLocation = 'end';
                    option.yAxis[1].min = (limitValue == '' && limitValue !== 0) ? minValue : limitValue;
                    option.yAxis[1].max = maxValue;
                    option.yAxis[1].interval = Math.floor((Math.abs(option.yAxis[1].max - option.yAxis[1].min) / 3) * 100) / 100;
                    option.yAxis[1].interval = option.yAxis[1].interval < 1 ? option.yAxis[1].interval = 1 : Math.ceil(option.yAxis[1].interval);
                    option.xAxis[0].position = 'bottom';
                    option.legend.top = '7%';
                    option.grid.top = '20%'
                } else if (alarmFlagName == 'PVLO' || alarmFlagName == 'PVLL') {
                    option.yAxis[0].inverse = true;
                    option.yAxis[1].nameLocation = 'start';
                    // option.yAxis[1].max = (limitValue == '') ? ((maxValue == minValue)?(maxValue-0.00001):maxValue) : limitValue;
                    option.yAxis[1].max = (limitValue == '' && limitValue !== 0) ? maxValue : limitValue;
                    option.yAxis[1].min = minValue;
                    option.yAxis[1].interval = Math.floor((Math.abs(option.yAxis[1].max - option.yAxis[1].min) / 3) * 100) / 100;
                    option.yAxis[1].interval = option.yAxis[1].interval < 1 ? option.yAxis[1].interval = 1 : Math.ceil(option.yAxis[1].interval);
                    option.xAxis[0].position = 'top';
                    option.legend.top = '7%';
                    option.grid.top = '22%';
                }
                if (limitValue == '' && limitValue !== 0) {
                    option.legend.data[3] = {};
                    option.series[3].markLine.data = []
                }
                option.series[0].data = seriesOld;
                option.series[1].data = seriesNow;
                floodAlarmChart.setOption(option);
            },
            /**
             * 初始化图表 (报警标识除了PVHH、PVHI、PVLO、PVLL之外的其他的情况)
             */
            initChartsOther: function (data1, data2) {
                var begin = $("#startTime").val();
                var end = $("#endTime").val();
                var anchor = [{ name: begin, value: [begin, 0] },
                {
                    name: end, value: [end, 0]
                }];
                var histogramData = JSON.parse(data1);
                var lineChartData = JSON.parse(data2);
                var seriesAlarmValue = new Array();
                $.each(histogramData, function (i, el) {
                    seriesNow[i] = [histogramData[i].key, histogramData[i].value.value];
                })
                $.each(lineChartData, function (i, el) {
                    seriesAlarmValue[i] = [lineChartData[i].key, lineChartData[i].value];
                })
                floodAlarmChart = echarts.init(document.getElementById('floodAlarmChart'));

                var option = {

                    toolbox: {
                        right: '20px',
                        // left: 'right',
                        itemSize: 15,
                        lineStyle: {
                            color: ['#e5e5e5']
                        },

                        top: -7,
                        feature: {
                            dataZoom: {
                                yAxisIndex: 'none'
                            },
                            restore: {}
                        }
                    },
                    tooltip: {
                        trigger: 'item', //axis
                        axisPointer: { // 坐标轴指示器，坐标轴触发有效
                            type: '' // 默认为直线，可选为：'line' | 'shadow'
                        }
                    },
                    grid: {
                        left: '1%',
                        right: '3%',
                        bottom: '1%',
                        top: '20%',
                        height: '230px',
                        containLabel: true
                    },
                    legend: {
                        left: '80px',
                        top: '4%',
                        itemWidth: 20, //设置icon大小
                        itemHeight: 10, //设置icon大小
                        data: [{
                            name: '报警数',
                            icon: 'roundRect',
                            textStyle: {
                                color: '#9d9d9d',
                            }
                        }, {
                            name: '报警值',
                            icon: '',
                            textStyle: {
                                color: '#9d9d9d',
                            }
                        }]
                    },
                    xAxis: [{
                        type: 'time',
                        minInterval: 3600 * 24 * 1000,
                        maxInterval: 3600 * 24 * 1000,
                        min: function (value) {
                            return Date.parse(($('#startTime').val()).replace(/-/g, "/")) - 3600 * 15 * 1000;
                        },
                        max: function (value) {
                            return Date.parse(($('#endTime').val()).replace(/-/g, "/")) + 3600 * 5 * 1000;
                        },
                        axisLine: {
                            onZero: false
                        },
                        axisLabel: {
                            interval: 0,
                            rotate: 50,
                            textStyle: {
                                fontSize: 10
                            },
                            showMinLabel: false,
                            showMaxLabel: false,
                            formatter: function (value, index) {
                                var date = new Date(value);
                                var y = date.getFullYear();
                                var m = date.getMonth() + 1;
                                var d = date.getDate();
                                return y + '-' + m + '-' + d;
                            }
                        },
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                    }],
                    yAxis: [{
                        name: '报警数',
                        type: 'value',
                        min: 0,
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        }
                    }, {
                        name: '报警值(' + unitFlag + ')',
                        nameLocation: 'end',
                        position: 'right',
                        type: 'value',
                        splitLine: { //网格线
                            show: false,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        }
                    }],
                    series: [{
                        name: '报警数',
                        type: 'bar',
                        barWidth: '15',
                        itemStyle: {
                            normal: {
                                color: '#3398DB',
                            }
                        },
                        data: [],
                        tooltip: {
                            formatter: function (params) {
                                var name = params.value[0].replace(/\//g, "-");
                                var d1 = Date.parse(new Date(params.value[0]));
                                var d2 = d1 + (60 * 60 * 24 * 1000);
                                var Y = (new Date(d2)).getFullYear();
                                var M = (new Date(d2)).getMonth() + 1;
                                var D = (new Date(d2)).getDate();
                                if (Math.floor(M / 10) == 0) {
                                    M = '0' + M
                                }
                                if (Math.floor(D / 10) == 0) {
                                    D = '0' + D
                                }
                                var d3 = Y + "-" + M + "-" + D;
                                var timeData = '从：' + name + ' ' + queryTimeArray + '<br>至：' + d3 + ' ' + queryTimeArray + '<br>报警数：' + params.value[1]
                                return timeData;
                            }
                        }
                    }, {
                        name: '报警值',
                        type: 'line',
                        yAxisIndex: 1,
                        smooth: true,
                        itemStyle: {
                            normal: {
                                color: '#1cb964',
                            }
                        },
                        data: seriesAlarmValue,
                        tooltip: {
                            formatter: function (params) {
                                var timeData = '报警时间：' + params.value[0] + ' <br> 报警值：' + params.value[1]
                                return timeData;
                            }
                        }
                    }, {
                        name: 'anchor',
                        yAxisIndex: 0,
                        type: 'line',
                        showSymbol: false,
                        data: anchor,
                        itemStyle: {
                            normal: {
                                opacity: 0
                            }
                        },
                        lineStyle: {
                            normal: {
                                opacity: 0
                            }
                        }
                    }],
                    dataZoom: [{
                        type: 'slider',
                        yAxisIndex: 0,
                        left: '0',
                        filterMode: 'none',
                        width: 20
                    }]
                };
                option.series[0].data = seriesNow;
                if (seriesAlarmValue.length == 0) {
                    option.legend.data[1] = [];
                    option.yAxis = [{
                        name: '报警数',
                        type: 'value',
                        min: 0,
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        }
                    }];
                    option.series[1].yAxisIndex = 0;
                    option.xAxis[0].min = function (value) {
                        return Date.parse(($('#startTime').val()).replace(/-/g, "/")) - 3600 * 15 * 1000;
                    }
                    option.xAxis[0].max = function (value) {
                        return value.max + 3600 * 5 * 1000;
                    }
                } else {
                    option.yAxis[1].min = minValue;
                    option.yAxis[1].max = maxValue;
                    option.yAxis[1].name = '报警值(' + unitFlag + ')';
                }
                floodAlarmChart.setOption(option);
            },
            /**
             * 刷新详情列表
             */
            refreshDetail: function () {
                var flag1 = page.logic.checkRules(limitValue, $('#alarmValue').val());
                if (alarmFlagName == 'PVHH' || alarmFlagName == 'PVHI') { // 高
                    var flag2 = page.logic.checklimtArea('H', limitValue, maxValue, $('#alarmValue').val());
                } else if (alarmFlagName == 'PVLO' || alarmFlagName == 'PVLL') { // 低
                    var flag2 = page.logic.checklimtArea('L', limitValue, minValue, $('#alarmValue').val());
                }
                var flag3 = page.logic.checkAreas($('#alarmValue').val());
                if (!flag1) {
                    layer.confirm('修改值的小数位数不可超过5位！', {
                        btn: ['确定']
                    })
                    return false;
                } else if (flag2 != true) {
                    layer.confirm(flag2, {
                        btn: ['确定']
                    })
                    return false;
                } else if (!flag3) {
                    layer.confirm('该修改值不在工艺卡片值范围，是否继续？', {
                        btn: ['是', '否']
                    }, function (index) {
                        layer.close(index)
                        page.logic.changeAlarmAnalysis()
                    }, function (index) {
                        layer.close(index)
                    });
                } else {
                    page.logic.changeAlarmAnalysis()
                }
            },
            changeAlarmAnalysis: function () {
                var lineData = floodAlarmChart.getOption().series[2].data;
                lineData = JSON.stringify(lineData);
                $.ajax({
                    url: getChangeShakeAlarmAnalysislUrl,
                    async: true,
                    data: {
                        startTime: OPAL.form.getData("searchForm").startTime,
                        endTime: OPAL.form.getData("searchForm").endTime,
                        alarmPointTag: alarmPointTag,
                        alarmFlagId: alarmFlagId,
                        alarmValue: $('#alarmValue').val()
                    },
                    dataType: "JSON",
                    type: 'get',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        if (res != null && res != undefined && res != '') {
                            if (res.length > 0) {
                                var nowAlarmTimes = res[0].nowAlarmTimes;
                                var reduceRate = res[0].reduceRate;
                                $('#nowAlarmTimes').html(nowAlarmTimes);
                                $('#reduceRate').html(reduceRate + "%");
                                alarmValue = $('#alarmValue').val();
                                page.logic.setDownCharts(res[0].histogramData, res[0].lineChartData);
                            }
                        }
                    },
                    error: function (result) {
                        //var errorResult = $.parseJSON(result.responseText);
                        //layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            /**
             * 重新渲染chart
             */
            setDownCharts: function (histogramData2, lineChartData2) {
                var option = floodAlarmChart.getOption();
                var seriesOld = new Array();
                var seriesAlarmValue = new Array();
                var histogramDataValue = JSON.parse(histogramData2);
                var lineChartDatavalue = JSON.parse(lineChartData2);
                $.each(histogramDataValue, function (i, el) {
                    seriesNow[i] = [histogramDataValue[i].key, histogramDataValue[i].value.value];
                })
                $.each(lineChartDatavalue, function (i, el) {
                    seriesAlarmValue[i] = [lineChartDatavalue[i].key, lineChartDatavalue[i].value];
                })
                option.series[1].data = seriesNow;
                option.series[2].data = seriesAlarmValue;
                option.series[4].markLine.data[0].yAxis = alarmValue;
                floodAlarmChart.setOption(option)
            },
            processLimitValueDisplay: function (alarmFlag) {
                var arr = ["PVHH", "PVHI", "PVLO", "PVLL"];
                if (arr.indexOf(alarmFlag) == -1) {
                    $("#limitValueSpan").css("display", "none");
                    $("#limitValue").css("display", "none");
                } else {
                    $("#limitValueSpan").css("display", "inline");
                    $("#limitValue").css("display", "inline");
                }
            },
            /**
             * 填写工艺卡片值
             */
            upAnddownLimitValue: function (downLimitValue, upLimitValue) {
                var flagUp, flagDown, valueUp, valueDowm;
                var str = '';
                if (upLimitValue != '{}' && downLimitValue != '{}') {
                    var downValue = JSON.parse(downLimitValue);
                    var upValue = JSON.parse(upLimitValue);
                    if (upValue.key == 1) {
                        flagUp = '<='
                    } else if (upValue.key == 0) {
                        flagUp = '<'
                    }
                    if (downValue.key == 1) {
                        flagDown = '>='
                    } else if (downValue.key == 0) {
                        flagDown = '>'
                    }
                    str = downValue.value + '~' + upValue.value
                } else if (upLimitValue != '{}' && downLimitValue == '{}') {
                    var upValue = JSON.parse(upLimitValue);
                    if (upValue.key == 1) {
                        flagUp = '<='
                    } else if (upValue.key == 0) {
                        flagUp = '<'
                    }
                    str = flagUp + upValue.value
                } else if (upLimitValue == '{}' && downLimitValue != '{}') {
                    var downValue = JSON.parse(downLimitValue);
                    if (downValue.key == 1) {
                        flagDown = '>='
                    } else if (downValue.key == 0) {
                        flagDown = '>'
                    }
                    str = flagDown + downValue.value
                } else if (upLimitValue == '{}' && downLimitValue == '{}') {
                    str = ''
                }
                return str;
            },

            upValueRange: function (res) {
                if (res[0].alarmFlag == AlarmFlagEnum.PVHH || res[0].alarmFlag == AlarmFlagEnum.PVHI) {
                    if (res[0].limitValue == undefined || res[0].limitValue == null || (res[0].limitValue == '' && res[0].limitValue !== 0)) {
                        leftValue = res[0].minValue;
                    } else {
                        leftValue = res[0].limitValue;
                    }
                    rightValue = res[0].maxValue;
                }
                else if (res[0].alarmFlag == AlarmFlagEnum.PVLO || res[0].alarmFlag == AlarmFlagEnum.PVLL) {
                    leftValue = res[0].minValue;
                    if (res[0].limitValue == undefined || res[0].limitValue == null || (res[0].limitValue == '' && res[0].limitValue !== 0)) {
                        rightValue = res[0].maxValue;
                    } else {
                        rightValue = res[0].limitValue;
                    }

                }
            },
            /**
             * 校验修改值的数值规则
             */
            checkRules: function (limtStr, strVal) {
                r = '^-?\\d+(\\.\\d{0,5})?$'
                return (new RegExp(r)).test(strVal);
            },
            /**
             * 校验报警值的数值是否在限值和报警值范围内
             */
            checklimtArea: function (flag, limitValue, alarmValue, strVal) {
                if (flag == 'H') {
                    if (limitValue == '' && limitValue !== 0) {
                        limitValue = minValue;
                        if (strVal >= limitValue && strVal <= alarmValue) {
                            return true;
                        } else {
                            return '修改值须大于等于该位号的最小报警值，小于等于最大报警值！报警值范围为（' + limitValue + '（最小报警值）～' + alarmValue + '（最大报警值））'
                        }
                    } else {
                        if (strVal >= limitValue && strVal <= alarmValue) {
                            return true;
                        } else {
                            return '修改值须大于等于该位号的限值，小于等于最大报警值！报警值范围为（' + limitValue + '（限值）～' + alarmValue + '（最大报警值））'
                        }
                    }

                } else if (flag == 'L') {
                    if (limitValue == '' && limitValue !== 0) {
                        limitValue = maxValue;
                        if (strVal <= limitValue && strVal >= alarmValue) {
                            return true;
                        } else {
                            return '修改值须大于等于该位号的最小报警值，小于等于最大报警值！报警值范围为（' + alarmValue + '（最小报警值）～' + limitValue + '（最大报警值））'
                        }
                    } else {
                        if (strVal <= limitValue && strVal >= alarmValue) {
                            return true;
                        } else {
                            return '修改值须大于等于该位号的最小报警值，小于等于最大报警值！报警值范围为（' + alarmValue + '（最小报警值）～' + limitValue + '（限值））'
                        }
                    }
                }
            },
            /**
             * 校验修改值的数值是否在工艺范围
             */
            checkAreas: function (strVal) {
                var upValue = $('#upLimitValue').val();
                var downValue = $('#downLimitValue').val();
                var limitFlag, limitUp, limitDown;
                if (upValue != '{}' && downValue != '{}') {
                    var upVal = JSON.parse(upValue);
                    var downVal = JSON.parse(downValue);
                    if (upVal.key == 1) {
                        flagUp = '<=';
                        limitUp = strVal <= upVal.value
                    } else if (upVal.key == 0) {
                        flagUp = '<';
                        limitUp = strVal < upVal.value
                    }
                    if (downVal.key == 1) {
                        flagDown = '>=';
                        limitDown = strVal >= downVal.value
                    } else if (downVal.key == 0) {
                        flagDown = '>';
                        limitDown = strVal > downVal.value
                    }
                    if (limitUp && limitDown) {
                        limitFlag = true
                    } else {
                        limitFlag = false
                    }
                } else if (upValue != '{}' && downValue == '{}') {
                    var upVal = JSON.parse(upValue);
                    if (upVal.key == 1) {
                        flagUp = '<='
                        limitFlag = strVal <= upVal.value
                    } else if (upVal.key == 0) {
                        flagUp = '<'
                        limitFlag = strVal < upVal.value
                    }
                } else if (upValue == '{}' && downValue != '{}') {
                    var downVal = JSON.parse(downValue);
                    if (downVal.key == 1) {
                        flagDown = '>=';
                        limitFlag = strVal >= downVal.value
                    } else if (downVal.key == 0) {
                        flagDown = '>';
                        limitFlag = strVal > downVal.value
                    }
                } else if (upValue == '{}' && downValue == '{}') {
                    return true;
                }
                return limitFlag;
            },
            limitValueLength: function (limtStr) {
                if (limtStr != '') {
                    if (String(limtStr).indexOf(".") == -1) { // 整数
                        var n = 0;
                    } else {
                        var n = String(limtStr).split('.')[1].length
                    }
                } else {
                    n = 2
                }

                return n;
            },
            exportExcel: function () {
                var titleArray = new Array();
                var tableTitle;
                tableTitle = new Array(
                    { 'title': "装置", 'field': "unitName" },
                    { 'title': "生产单元", 'field': "prdtName" },
                    { 'title': "报警时间", 'field': "alarmTime" },
                    { 'title': "参数名称", 'field': "location" },
                    { 'title': "位号", 'field': "alarmPointTag" },
                    { 'title': "报警等级", 'field': "alarmFlagName" },
                    { 'title': "优先级", 'field': "priority" },
                    { 'title': "专业", 'field': "monitorTypeStr" },
                    { 'title': "单位", 'field': "measUnitName" }
                );
                $.each(tableTitle, function (i, el) {
                    titleArray.push({
                        'key': el.field,
                        'value': el.title
                    })
                })
                var data = {};
                data.titles = JSON.stringify(titleArray);
                data.startTime = $('#startTime').val();
                data.endTime = $('#endTime').val();
                data.alarmFlagId = alarmFlagId;
                data.alarmPointTag = alarmPointTag;
                data.unitName = unitName;
                data.prdtName = prdtName;
                data.alarmFlagName = alarmFlagName;


                $('#formExPort').attr('action', exportShakeAlarmAnalysisUrl);
                $('#titles').val(data.titles);
                $('#startTime1').val(data.startTime);
                $('#endTime1').val(data.endTime);
                $('#alarmPointTag1').val(data.alarmPointTag);
                $('#alarmFlagId1').val(data.alarmFlagId);
                $('#unitName1').val(data.unitName);
                $('#prdtName1').val(data.prdtName);
                $("#alarmFlagName1").val(data.alarmFlagName);
                $('#formExPort').submit();
            }
        }
    }
    page.init();
    window.page = page;
})