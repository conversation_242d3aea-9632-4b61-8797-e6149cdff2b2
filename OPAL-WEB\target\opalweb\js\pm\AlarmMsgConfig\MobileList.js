var searchUrl = OPAL.API.pmUrl + '/alarmMsgConfig/getMobileList';   //电话本
var factoryUrl = OPAL.API.pmUrl + '/unit/getFactoryList';    //工厂
var workshopUrl = OPAL.API.pmUrl + '/unit/getWorkshopListByFactoryId';   //车间
var unitUrl = OPAL.API.pmUrl + "/alarmMsgConfig/getUnitListByWorkshopId";   //装置
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.None;
var mobileBookIds = [];
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
            page.logic.initTable(JSON.stringify(""));
            page.logic.initFactory();
        },
        bindUI: function () {
            $('#btnSave').click(function () {
                page.logic.save();
            });
            $("#btnSearch").click(function () {
                page.logic.search();
            })
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
        },
        data: {
            param: {}
        },
        logic: {
            /**
             * 确定
             */
            save: function () {
                var selects = [];
                $("#telBook li").each(function () {
                    selects.push($(this).find(".tel").text());
                })
                parent.page.logic.getSelect(selects,mobileBookIds.join(","));
                page.logic.closeLayer(false);
            },
            initTable: function (data) {
                var results = JSON.parse(data);
                var options = {
                    striped: true, //是否显示行间隔色
                    height:320,
                    uniqueId:"mobileListId",
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "工厂",
                        field: 'factoryName',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    },  {
                        title: "车间",
                        field: 'workshopName',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    },  {
                        title: "装置",
                        field: 'unitCodeName',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    },  {
                        title: "姓名",
                        field: 'name',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }, {
                        title: "手机号",
                        field: 'mobile',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }],
                    onPostBody:function () {
                        var tds = $('#table').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    },
                    onCheckAll:function(rows){
                        for(var i = 0;i < rows.length;i++){
                            mobileBookIds.push(rows[i].mobileListId)
                            $("#table").bootstrapTable('removeByUniqueId', rows[i].mobileListId);
                            var tel = rows[i].name+"("+ rows[i].mobile +")";
                            $("#telBook").append('<li><span class="tel">'+tel+'</span><a class="dels" mobileListId="'+rows[i].mobileListId+'" href="javascript:page.logic.dels('+rows[i].mobileListId+')">×</a></li>');
                        }
                    },
                    onCheck:function (row) {
                        $("#table").bootstrapTable('removeByUniqueId', row.mobileListId);
                        mobileBookIds.push(row.mobileListId)
                        var tel = row.name+"("+ row.mobile +")";
                        $("#telBook").append('<li><span class="tel">'+tel+'</span><a class="dels" mobileListId="'+row.mobileListId+'" href="javascript:page.logic.dels('+row.mobileListId+')">×</a></li>');
                    }
                };
                $('#table').bootstrapTable(options);
                $('#table').bootstrapTable('refreshOptions', options);
                if (results == undefined) {
                    results = [];
                }
                $("#table").bootstrapTable("load", results);
            },
            dels:function (mobileBookId) {
                for(var i = 0; i< mobileBookIds.length;i++){
                    if(mobileBookIds[i] == mobileBookId){
                        mobileBookIds.splice(i, 1)
                    }
                }
                $(".dels").each(function () {
                    if($(this).attr("mobilelistid") == mobileBookId){
                        $(this).parent("li").remove();
                    }
                })
                page.logic.search();
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                $("#pageTitle").text(data.title);
                if(data.mobileBook){
                    mobileBookIds = data.mobileBookId.split(",");
                    var mobBook = data.mobileBook.split("#");
                    for(var i = 0;i<mobBook.length; i++){
                        $("#telBook").append('<li><span class="tel">'+mobBook[i]+'</span><a class="dels" mobileListId="'+mobileBookIds[i]+'" href="javascript:page.logic.dels('+mobileBookIds[i]+')">×</a></li>');
                    }
                }
                page.logic.search();
            },
            search:function () {
                page.data.param = OPAL.form.getData("searchForm");
                $.ajax({
                    url: searchUrl + "?now=" + Math.random(),
                    data:page.data.param,
                    type: "get",
                    dataType: "json",
                    success: function (data) {
                        var datas = $.ET.toObjectArr(data);
                        var results = [];
                        var mobileListIds = mobileBookIds.join(",");
                        var dataIntArr =[];
                        mobileBookIds.forEach(function(data,index,arr){
                            dataIntArr.push(+data);
                        });
                        for(var i= 0;i< datas.length;i++){
                            if(dataIntArr.indexOf(datas[i].mobileListId) == -1){
                                results.push(datas[i]);
                            }
                        }
                        page.logic.initTable(JSON.stringify(results));
                    },
                    complete: function (XMLHttpRequest, textStatus) {

                    },
                    error: function (XMLHttpRequest, textStatus) {

                    }
                });
            },
            /**
             * 初始化工厂
             */
            initFactory: function () {
                OPAL.ui.getCombobox("factoryId", factoryUrl, {
                    keyField: "factoryId",
                    valueField: "sname",
                    selectFirstRecord: true,
                    data: {
                        isAll: true
                    }
                }, function (data) {
                    if ($("#factoryId").val()!=null && $("#factoryId").val()!='')
                        page.logic.initWorkshop($("#workshopId").val());
                }, function (selectedValue) {
                    if (selectedValue != '' &&selectedValue!=null){
                        page.logic.initWorkshop(selectedValue);
                    }else{
                        $("#workshopId").val([]);
                        $("#workshopId option").remove();
                        $("#unitId").val([]);
                        $("#unitId option").remove();
                    }
                });
            },
            /**
             * 初始化车间
             */
            initWorkshop: function (factoryId) {
                OPAL.ui.getCombobox("workshopId", workshopUrl, {
                    keyField: "workshopId",
                    valueField: "sname",
                    selectFirstRecord: true,
                    data: {
                        factoryId: factoryId,
                        isAll: true
                    }
                },function () {
                    if ($("#workshopId").val()!=null && $("#workshopId").val()!='' && $("#workshopId").val()!=-1)
                        page.logic.initUnit($("#workshopId").val());
                }, function (selectedValue) {
                    if (selectedValue != '' &&selectedValue!=null && $("#workshopId").val()!=-1){
                        page.logic.initUnit(selectedValue);
                    }else{
                        $("#unitId").val([]);
                        $("#unitId option").remove();
                    }
                })
            },
            /**
             * 初始化装置
             */
            initUnit: function(workshopId) {
                OPAL.ui.getCombobox("unitId", unitUrl , {
                    keyField: "stdCode",
                    valueField: "sname",
                    selectFirstRecord: true,
                    data: {
                        workshopId: workshopId,
                        isAll: true
                    }
                },null,null)
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            }
        }
    }
    page.init();
    window.page = page;
})