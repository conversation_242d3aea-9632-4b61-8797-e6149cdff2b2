$(function() {
	var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
	var getSearchTimeUrl = OPAL.API.commUrl + '/getSearchTime';
	var searchUrl = OPAL.API.aaUrl + '/alarmNumberAssess/getAlarmDetail';
	var page = {
		/**
		 * 初始化
		 */
		init: function() {
			this.bindUi();
		},
		bindUi: function() {
			// 关闭
			$('#closePageChild').click(function() {
				page.logic.closeLayer(false);
			});
		},
		data: {
			// 设置查询参数
			param: {}
		},
		logic: {
			setData: function(data) {
				page.logic.initTable();
                page.data.param = data;
                if(data.startTime.length == 10 && data.endTime.length == 10){
                	OPAL.util.getSearchTime({
						startTime: data.startTime,
						endTime: data.endTime
					},function(data) {
						page.data.param.startTime = OPAL.util.dateFormat(OPAL.util.strToDate(data["startDate"]), "yyyy-MM-dd HH:mm:ss");
	                	page.data.param.endTime = OPAL.util.dateFormat(OPAL.util.strToDate(data["endDate"]), "yyyy-MM-dd HH:mm:ss");
					});
                }
                // page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
        		// page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
        		page.data.param.startTime = OPAL.util.dateFormat(OPAL.util.strToDate(page.data.param.startTime), "yyyy-MM-dd HH:mm:ss");
        		page.data.param.endTime = OPAL.util.dateFormat(OPAL.util.strToDate(page.data.param.endTime), "yyyy-MM-dd HH:mm:ss");

				// var eventTypeIds = new Array();
                // eventTypeIds.push(1001);	
                // page.data.param.eventTypeIds = eventTypeIds;
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
            	});
			},
			/**
			 * 初始化表格
			 */
			initTable: function() {
				OPAL.ui.initBootstrapTable("table", {
					columns: [{
						title: "序号",
						formatter: function(value, row, index) {
							var data = page.data.param;
							var pageNumber = data.pageNumber;
							var pageSize = data.pageSize;
							if(index == 0) {
								$.each(row, function(i, val) {
                                $("#"+i).html(val);
                                $("#"+i).attr("title",val);
                        		});
							}
							return index + 1 + (pageNumber - 1) * pageSize;
						},
						rowspan: 1,
						align: 'center',
						width:'80px'
					}, {
						field: 'alarmTime',
						title: '报警时间',
						align: 'center',
						width:'150px'
					}, {
						field: 'des',
						title: '描述',
						align: 'left'
					}, {
						field: 'previousValue',
						title: '调整前的值',
						align: 'right',
						width:'100px'
					}, {
						field: 'nowValue',
						title: '当前值',
						align: 'right',
						width:'100px'
					}, {
						field: 'limitValue',
						title: '报警值（限值）',
						align: 'right',
						width:'105px'
					}]
				}, page.logic.queryParams)
			},
			/**
			 * 查询参数
			 * @param params
			 * @returns {{pageSize: *, pageNumber: *}}
			 */
			queryParams: function(p) {
				var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
			},
			/**
			 * 关闭弹出层
			 */
			closeLayer: function(isRefresh) {
				parent.layer.close(index);
			},
		}
	}
	page.init();
	window.page = page;


})