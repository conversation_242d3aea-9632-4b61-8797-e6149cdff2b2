package com.pcitc.opal.common.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.bll.vo.CraftStartStopDispositionVO;
import com.pcitc.opal.common.bll.vo.PointStatusDispositionVO;

import java.util.List;

public interface CraftStartStopDispositionService {

    CommonResult addDisposition(CraftStartStopDispositionVO vo);

    CommonResult pointStatusDisposition(List<PointStatusDispositionVO> vo);
}
