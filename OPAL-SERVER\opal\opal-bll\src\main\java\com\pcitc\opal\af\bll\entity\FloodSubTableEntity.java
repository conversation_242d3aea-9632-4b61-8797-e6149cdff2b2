package com.pcitc.opal.af.bll.entity;

import org.apache.commons.lang.time.DateFormatUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/*
 * 高频报警分析二级列表展示实体
 * 模块编号：pcitc_opal_bll_class_FloodSubTableEntity
 * 作  　者：xuelei.wang
 * 创建时间：2017-11-17
 * 修改编号：1
 * 描    述：高频报警分析二级列表展示实体
 */
@SuppressWarnings("serial")
public class FloodSubTableEntity implements Serializable {

    public FloodSubTableEntity(List<FloodThirdTableEntity> thirdTable, String name, Date startTime, Date endTime, Long datePeriod, Long alarmCounts, String alarmRate) {
        this.thirdTable = thirdTable;
        this.name = name;
        this.startTime = startTime;
        this.endTime = endTime;
        this.datePeriod = datePeriod;
        this.alarmCounts = alarmCounts;
        this.alarmRate = alarmRate;
    }

    public FloodSubTableEntity() {
    }

    /**
     * 单元ID
     */
    private Long prdtId;
    /**
     * 装置编码
     */
    private String unitId;
    /**
     * 高频报警名称
     */
    private String floodName;
    /**
     * 装置编码或者单元ID
     */
    private String id;
    /**
     * 高频报警三级列表
     */
    private List<FloodThirdTableEntity> thirdTable=new ArrayList<>();
    /**
     * 名称
     */
    private String name;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 开始时间字符串
     */
    @SuppressWarnings("unused")
	private String startTimeStr;
    /**
     * 结束时间字符串
     */
    @SuppressWarnings("unused")
	private String endTimeStr;
    /**
     * 时间间隔(分钟)
     */
    private Long datePeriod;
    /**
     * 报警数
     */
    private Long alarmCounts;
    /**
     * 峰值报警率
     */
    private String alarmRate;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }


    public Long getAlarmCounts() {
        return alarmCounts;
    }

    public void setAlarmCounts(Long alarmCounts) {
        this.alarmCounts = alarmCounts;
    }

    public List<FloodThirdTableEntity> getThirdTable() {
        return thirdTable;
    }

    public void setThirdTable(List<FloodThirdTableEntity> thirdTable) {
        this.thirdTable = thirdTable;
    }

    public Long getDatePeriod() {
        return datePeriod;
    }

    public void setDatePeriod(Long datePeriod) {
        this.datePeriod = datePeriod;
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFloodName() {
        return floodName;
    }

    public void setFloodName(String floodName) {
        this.floodName = floodName;
    }

    public String getStartTimeStr() {
        return DateFormatUtils.format(startTime,"yyyy-MM-dd HH:mm:ss");
    }

    public void setStartTimeStr(String startTimeStr) {
        this.startTimeStr = startTimeStr;
    }

    public String getEndTimeStr() {
        return DateFormatUtils.format(endTime,"yyyy-MM-dd HH:mm:ss");
    }

    public void setEndTimeStr(String endTimeStr) {
        this.endTimeStr = endTimeStr;
    }

    public String getAlarmRate() {
        return alarmRate;
    }

    public void setAlarmRate(String alarmRate) {
        this.alarmRate = alarmRate;
    }

    public Long getPrdtId() {
        return prdtId;
    }

    public void setPrdtId(Long prdtId) {
        this.prdtId = prdtId;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }
}