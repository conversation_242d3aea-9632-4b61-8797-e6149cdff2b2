var editUrl = OPAL.API.adUrl+'/AlarmEventDelApprove/updateReason'; //修改
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.None;
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var alarmEventDelApproveId = '';
    var page = {
        init: function () {
            this.bindUI();
        },
        bindUI: function () {
            $('#saveAddModal').click(function () {
                page.logic.submit();
            });
            $('.closeBtn').click(function () {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer(false);
            })
        },
        logic: {
            /**
             * 提交
             */
            submit: function () {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                //处理提交类型
                $.ajax({
                    url: editUrl,
                    async: false,
                    data: {
                        alarmEventDelApproveId: alarmEventDelApproveId,
                        reason: $('#reason').val()
                    },
                    dataType: "text", //后台返回为汉字时,dataType=text
                    type: 'POST', //PUT DELETE POST
                    success: function (result) {
                        layer.msg(result, {
                            time: 1000
                        }, function() {
                            window.pageLoadMode = PageLoadMode.Refresh;
                            page.logic.closeLayer(true);
                        });
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                alarmEventDelApproveId = data.id;
                $('#reason').val(data.reason);
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal',{
                    rules: {
                        reason: {
                            required: true
                        },
                    }
                })
                
            }
        }

    }
    page.init();
    window.page = page;
})