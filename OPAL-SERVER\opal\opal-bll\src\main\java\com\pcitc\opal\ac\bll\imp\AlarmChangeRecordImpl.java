package com.pcitc.opal.ac.bll.imp;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.pcitc.opal.ac.bll.AlarmChangeRecordService;
import com.pcitc.opal.ac.bll.entity.AlarmChangeRecordEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.DateRangeEntity;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;

import pcitc.imp.common.ettool.utils.ObjectConverter;

/*
 * 变更记录业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmChangeRecordImpl
 * 作    者：kun.zhao
 * 创建时间：2017/11/10
 * 修改编号：1
 * 描    述：变更记录业务逻辑层实现类
 */
@Service
@Component
public class AlarmChangeRecordImpl implements AlarmChangeRecordService {

	@Autowired
	private AlarmEventRepository alarmEventRepository;

	@Autowired
	private BasicDataService basicDataService;

	@Autowired
	private ShiftService shiftService;

	/**
	 * 获取变更记录实体
	 *
	 * <AUTHOR> 2017-11-10
	 * @param unitCodes 	        装置编码数组
	 * @param prdtCellIds   生产单元ID数组
	 * @param workTeamIds   'ID数组
	 * @param startTime     时间范围起始
	 * @param endTime	        时间范围结束
	 * @param page	                分页对象
	 * @return 变更记录实体数据集合
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<AlarmChangeRecordEntity> getAlarmChangeRecord(String[] unitCodes, Long[] prdtCellIds, Long[] workTeamIds,
																		Date startTime, Date endTime, Pagination page, Integer AlarmChangeRecordBusinessType) throws Exception {
		List<UnitEntity> units = null;
		if(unitCodes == null){
			units = basicDataService.getUnitList(true);
			unitCodes = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
		}
		// '
		if (workTeamIds == null) {
			workTeamIds = new Long[]{};
		}
		List<Long> workTeamIdList = Arrays.asList(workTeamIds);
		List<DateRangeEntity> dateRangeList = new ArrayList<>();
		List<ShiftWorkTeamEntity> shiftWorkList = new ArrayList<>();
		if (workTeamIdList.size() != 0 && unitCodes != null && unitCodes.length == 1) {
			shiftWorkList = shiftService.getShiftList(unitCodes[0], startTime, endTime, workTeamIdList);
			dateRangeList = shiftWorkList.stream().map(item -> new DateRangeEntity(item.getStartTime(), item.getEndTime())).collect(Collectors.toList());
		}

//		if (dateRangeList != null && dateRangeList.size() > 0) {
//			startTime = null;
//			endTime = null;
//		}

		PaginationBean<AlarmEvent> listAlarmEvent = alarmEventRepository.getAlarmChangeRecord(unitCodes, prdtCellIds, startTime, endTime, dateRangeList, page, AlarmChangeRecordBusinessType);
		PaginationBean<AlarmChangeRecordEntity> returnAlarmEvent = new PaginationBean<AlarmChangeRecordEntity>(page,
				listAlarmEvent.getTotal());
		returnAlarmEvent.setPageList(ObjectConverter.listConverter(listAlarmEvent.getPageList(), AlarmChangeRecordEntity.class));
		// 装置不选或班组全选时补全班组信息
		if (dateRangeList.size()==0&&workTeamIdList.size()==0) {
			Date minDate=returnAlarmEvent.getPageList().stream().reduce((item1,item2)->item1.getStartTime().getTime()<item2.getStartTime().getTime()?item1:item2).orElse(new AlarmChangeRecordEntity()).getStartTime();
			Date maxDate=returnAlarmEvent.getPageList().stream().reduce((item1,item2)->item1.getStartTime().getTime()>item2.getStartTime().getTime()?item1:item2).orElse(new AlarmChangeRecordEntity()).getStartTime();
			if(minDate!=null&&maxDate!=null) {
				shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCodes), minDate, maxDate);
			}
		}
		if(units == null) {
			// 通过公共方法获取装置
			String[] filterUnitCodes = listAlarmEvent.getPageList().stream().map(e -> e.getAlarmPoint().getPrdtCell().getUnitId()).distinct().toArray(String[]::new);
			units = basicDataService.getUnitListByIds(filterUnitCodes, false);
		}
		for (int i = 0; i < returnAlarmEvent.getPageList().size(); i++) {
			AlarmChangeRecordEntity alarmChangeRecordEntity = returnAlarmEvent.getPageList().get(i);
			AlarmEvent alarmEvent = listAlarmEvent.getPageList().get(i);
			//填充装置简称
			UnitEntity unit = units.stream().filter(y -> alarmEvent.getAlarmPoint().getPrdtCell().getUnitId().equals(y.getStdCode())).findFirst().orElse(new UnitEntity());
			alarmChangeRecordEntity.setUnitName(unit.getSname());
			//填充生产单元简称
			alarmChangeRecordEntity.setPrdtCellName(alarmEvent.getAlarmPoint().getPrdtCell().getSname());
			//填充报警点位号
			alarmChangeRecordEntity.setTag(alarmEvent.getAlarmPoint().getTag());
			//填充报警标识名称
			alarmChangeRecordEntity.setAlarmFlagName((null !=alarmEvent.getAlarmFlag())?alarmEvent.getAlarmFlag().getName():null);
			//填充事件类型
			alarmChangeRecordEntity.setEventTypeName(alarmEvent.getEventType().getName());
			//填充班组名称
			alarmChangeRecordEntity.setWorkTeamSName(shiftWorkList.parallelStream().filter(item -> alarmChangeRecordEntity.getStartTime().getTime() >= item.getStartTime().getTime() && alarmChangeRecordEntity.getStartTime().getTime() < item.getEndTime().getTime()).findFirst().orElse(new ShiftWorkTeamEntity()).getWorkTeamSName());
			//填充工艺卡片值和表明是否高亮
			alarmChangeRecordEntity.setCraftDownLimitInclude(alarmEvent.getAlarmPoint().getCraftDownLimitInclude());
			alarmChangeRecordEntity.setCraftDownLimitValue(alarmEvent.getAlarmPoint().getCraftDownLimitValue());
			alarmChangeRecordEntity.setCraftUpLimitInclude(alarmEvent.getAlarmPoint().getCraftUpLimitInclude());
			alarmChangeRecordEntity.setCraftUpLimitValue(alarmEvent.getAlarmPoint().getCraftUpLimitValue());
			alarmChangeRecordEntity.setLocation(alarmEvent.getAlarmPoint().getLocation());
			getCraftAndFlag(alarmChangeRecordEntity);
		}

		return returnAlarmEvent;
	}

	/**
	 * 填充工艺卡片值和表明是否高亮
	 *
	 * <AUTHOR> 2017-11-07
	 * @param alarmChangeRecordEntity
	 */
	private void getCraftAndFlag(AlarmChangeRecordEntity alarmChangeRecordEntity) {

		String nowValue = complementNumer(alarmChangeRecordEntity.getNowValue());
		Double craftDownLimitValue = alarmChangeRecordEntity.getCraftDownLimitValue();
		Double craftUpLimitValue = alarmChangeRecordEntity.getCraftUpLimitValue();
		Integer craftDownLimitInclude = alarmChangeRecordEntity.getCraftDownLimitInclude();
		Integer craftUpLimitInclude = alarmChangeRecordEntity.getCraftUpLimitInclude();

		craftDownLimitInclude = craftDownLimitInclude == null ? 0 : craftDownLimitInclude;
		craftUpLimitInclude = craftUpLimitInclude == null ? 0 : craftUpLimitInclude;
		Pattern pattern = Pattern.compile("\\d+|-\\d+|\\d+\\.\\d+|-\\d+\\.\\d+");
		String symbol = null;
		String craftTemp = null;
		Integer flag = 0;

		if(craftDownLimitValue != null){
			if(craftDownLimitInclude.equals(CommonEnum.CraftDownLimitEnum.YES.getIndex())){
				symbol = "≥";
				if(nowValue != null && pattern.matcher(nowValue).matches() && !(Double.valueOf(nowValue) >= craftDownLimitValue)){
					flag = 1;
				}
			}else{
				symbol = ">";
				if(nowValue != null && pattern.matcher(nowValue).matches() && !(Double.valueOf(nowValue) > craftDownLimitValue)){
					flag = 1;
				}
			}
			craftTemp = symbol + changeDouble(craftDownLimitValue);
			if(craftUpLimitValue != null){
				if(craftUpLimitInclude.equals(CommonEnum.CraftUpLimitEnum.YES.getIndex())){
					symbol = "~";
					if(nowValue != null && pattern.matcher(nowValue).matches() && !(Double.valueOf(nowValue) <= craftUpLimitValue)){
						flag = 1;
					}
				}else{
					symbol = "~";
					if(nowValue != null && pattern.matcher(nowValue).matches() && !(Double.valueOf(nowValue) < craftUpLimitValue)){
						flag = 1;
					}
				}
				craftTemp = changeDouble(craftDownLimitValue) + symbol + changeDouble(craftUpLimitValue);
			}
		}else{
			if(craftUpLimitValue != null){
				if(craftUpLimitInclude.equals(CommonEnum.CraftUpLimitEnum.YES.getIndex())){
					symbol = "≤";
					if(nowValue != null && pattern.matcher(nowValue).matches() && !(Double.valueOf(nowValue) <= craftUpLimitValue)){
						flag = 1;
					}
				}else{
					symbol = "<";
					if(nowValue != null && pattern.matcher(nowValue).matches() && !(Double.valueOf(nowValue) < craftUpLimitValue)){
						flag = 1;
					}
				}
				craftTemp = symbol + changeDouble(craftUpLimitValue);
			}
		}

		alarmChangeRecordEntity.setCraft(craftTemp);
		alarmChangeRecordEntity.setFlag(flag);
	}

	/**
	 * 工艺卡片上下限值为整数时去除小数位
	 *
	 * <AUTHOR> 2017-12-05
	 * @param num 浮点数值
	 * @return 修整后数值字符串
	 */
	private String changeDouble(Double num){
		if((num+"").endsWith(".0")){
			return num.intValue()+"";
		}
		return num+"";
	}

	/**
	 * 补全数字
	 *
	 * <AUTHOR> 2017-12-05
	 * @param number 字符串数字
	 * @return 补全后字符串数字
	 */
	private String complementNumer(String number){
		if(StringUtils.isEmpty(number)) return number;
		Pattern prePattern = Pattern.compile("\\d+\\.|-\\d+\\.");
		Pattern sufPatternPositive = Pattern.compile("\\.\\d+");
		Pattern sufPatternMinus = Pattern.compile("-\\.\\d+");

		if(prePattern.matcher(number).matches())
			return number + "0";
		if(sufPatternPositive.matcher(number).matches())
			return "0" + number;
		if(sufPatternMinus.matcher(number).matches())
			return "-0" + number.substring(1);
		return number;
	}
}
