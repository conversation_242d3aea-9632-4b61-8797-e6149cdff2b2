package com.pcitc.opal.ap.dao.imp;


/*
 * 报警点实体
 * 模块编号：pcitc_pojo_class_Group
 * 作       者：guoganxin
 * 创建时间：2023/04/16
 * 修改编号：1
 * 描       述：群组
 */
public class AlarmPushRuleDetailEntityVO  {

    public AlarmPushRuleDetailEntityVO(Long apRuleDetailId, Integer companyId, Long alarmPushRuleId, Long groupId, Long startPushPeriod, Long cycleFlag, Long cyclePeriod, Long alarmEndPushFlag, Long alarmFlagId, Integer inUse, Integer sortNum, String des, String ruleName, String flagName, String groupName) {
        this.apRuleDetailId = apRuleDetailId;
        this.companyId = companyId;
        this.alarmPushRuleId = alarmPushRuleId;
        this.groupId = groupId;
        this.startPushPeriod = startPushPeriod;
        this.cycleFlag = cycleFlag;
        this.cyclePeriod = cyclePeriod;
        this.alarmEndPushFlag = alarmEndPushFlag;
        this.alarmFlagId = alarmFlagId;
        this.inUse = inUse;
        this.sortNum = sortNum;
        this.des = des;
        this.ruleName = ruleName;
        this.flagName = flagName;
        this.groupName = groupName;
    }

    /**
     * 报警推送规则明细ID
     */
    private Long apRuleDetailId;

    /**
     * 企业ID
     */
    private Integer companyId;

    /**
     * 报警推送规则ID
     */
    private Long alarmPushRuleId;

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 开始推送间隔时间（分钟）
     */
    private Long startPushPeriod;

    /**
     * 周期推送标志（0 非周期、1 周期）
     */
    private Long cycleFlag;

    /**
     * 周期间隔时间（分钟）
     */
    private Long cyclePeriod;

    /**
     * 报警结束推送标志(0 不推送、1 推送)
     */
    private Long alarmEndPushFlag;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     * 是否启用（1是；0否）
     */
    private Integer inUse;

    /**
     * 排序
     */
    private Integer sortNum;

    /**
     * 描述
     */
    private String des;

    /**
     * 报警推送规则Name
     */
    private String ruleName;

    /**
     * 报警标识Name
     */
    private String flagName;

    /**
     * 群组Name
     */
    private String groupName;

    public Long getApRuleDetailId() {
        return apRuleDetailId;
    }

    public void setApRuleDetailId(Long apRuleDetailId) {
        this.apRuleDetailId = apRuleDetailId;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Long getAlarmPushRuleId() {
        return alarmPushRuleId;
    }

    public void setAlarmPushRuleId(Long alarmPushRuleId) {
        this.alarmPushRuleId = alarmPushRuleId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getStartPushPeriod() {
        return startPushPeriod;
    }

    public void setStartPushPeriod(Long startPushPeriod) {
        this.startPushPeriod = startPushPeriod;
    }

    public Long getCycleFlag() {
        return cycleFlag;
    }

    public void setCycleFlag(Long cycleFlag) {
        this.cycleFlag = cycleFlag;
    }

    public Long getCyclePeriod() {
        return cyclePeriod;
    }

    public void setCyclePeriod(Long cyclePeriod) {
        this.cyclePeriod = cyclePeriod;
    }

    public Long getAlarmEndPushFlag() {
        return alarmEndPushFlag;
    }

    public void setAlarmEndPushFlag(Long alarmEndPushFlag) {
        this.alarmEndPushFlag = alarmEndPushFlag;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public Integer getInUse() {
        return inUse;
    }

    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getFlagName() {
        return flagName;
    }

    public void setFlagName(String flagName) {
        this.flagName = flagName;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
}
