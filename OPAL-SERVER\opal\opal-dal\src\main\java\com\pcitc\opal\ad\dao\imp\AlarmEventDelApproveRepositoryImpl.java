package com.pcitc.opal.ad.dao.imp;

import com.pcitc.opal.ad.dao.AlarmEventDelApproveRepositoryCustom;
import com.pcitc.opal.ad.pojo.AlarmEventDelApprove;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Query;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;

public class AlarmEventDelApproveRepositoryImpl extends BaseRepository<AlarmEventDelApprove, Integer> implements AlarmEventDelApproveRepositoryCustom {

    public PaginationBean<AlarmEventDelApprove> getAlarmEventDelApprove(String[] unitCode, Integer[] priority,
                                                                        Long[] alarmFlagId, Integer monitorType,
                                                                        Date startTime, Date endTime, String alarmPointTag,
                                                                        Integer delStatus,
                                                                        Pagination page) {

        HashMap<String, Object> param = new HashMap<>();
        String hql = "select  a from AlarmEventDelApprove a left join (AlarmEvent UNION ALL AlarmEventDe) b  on a.eventId = b.eventId " +
                "left join AlarmPoint ap on b.alarmPointId = ap.alarmPointId " +
                "where 1=1 ";
        if (ArrayUtils.isNotEmpty(unitCode)) {
            hql += " and a.unitCode in (:unitCode) ";
            param.put("unitCode", Arrays.asList(unitCode));
        }
        if (StringUtils.isNotBlank(alarmPointTag)) {
            //过滤位号
            hql += " and ap.tag=:tag ";
            param.put("tag", alarmPointTag);
        }
        //过滤优先级
        if (ArrayUtils.isNotEmpty(priority)) {
            if (ArrayUtils.contains(priority, 9)) {
                hql += "and ( b.priority in (:prioritys) or b.priority is null )";
            } else {
                hql += "and b.priority in (:prioritys) ";
            }
            param.put("prioritys", Arrays.asList(priority));
        }
        //过滤监测类型
        if (monitorType != null && monitorType != -1) {
            hql += "  and ap.monitorType = :monitorType ";
            param.put("monitorType", monitorType);
        }
        // 过滤报警点标识
        if (alarmFlagId != null && alarmFlagId.length > 0) {
            for (int i = 0; i < alarmFlagId.length; i++) {
                if (alarmFlagId[i] == (-9L)) {
                    hql += " and ( b.alarmFlagId in (:alarmFlagIds) or b.alarmFlagId is null) ";
                    break;
                }
                if (i == alarmFlagId.length - 1) {
                    hql += " and  b.alarmFlagId in (:alarmFlagIds) ";
                }
            }
            param.put("alarmFlagIds", Arrays.asList(alarmFlagId));
        }
        if (startTime != null) {
            hql += " and b.startTime >= :beginTime";
            param.put("beginTime", startTime);

        }
        if (endTime != null) {
            hql += " and b.startTime < :endTime";
            param.put("endTime", endTime);
        }
        // 只能查询已提交和已驳回的数据
        if (delStatus != null) {
            hql += " and a.delStatus = (:delStatus) ";
            param.put("delStatus", delStatus);
        } else {
            hql += " and a.delStatus in (1, 2, 3) ";
        }
        hql += " order by a.alarmEventDelApproveId desc";
        return this.findAll(page, hql, param);
    }

    @Override
    public boolean existsAlarmEvent(Long[] eventIds, Integer[] delStatus) {
        String hql = "select count(1) from AlarmEventDelApprove where delStatus in (:delStatus) and eventId in (:eventId)";

        Query query = getEntityManager().createQuery(hql);
        query.setParameter("eventId", Arrays.asList(eventIds));
        query.setParameter("delStatus", Arrays.asList(delStatus));

        Long size = (Long) query.getResultList().get(0);

        return size > 0;
    }
}
