package com.pcitc.opal.common.dao;

import com.pcitc.opal.common.DbConversion;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用于原生sql语句修改
 */
@Component
public class CountSqlBuilder {

	public static String toCountHql(String sql) {
		String arg = (CountSqlBuilder.trimEndOrderBy(sql.replace("\r", " ").replace("\n", " ").replace("fetch"," ")));
		Pattern r = Pattern.compile("^select\\s+distinct\\s+(.*?)\\s+from.*", Pattern.CASE_INSENSITIVE);
		Matcher m = r.matcher(sql);
		return m.matches()
				? String.format("select "+ DbConversion.nvlFunction() +"(sum((count(*)+1)/(count(*)+1)),0) from %s group by %s", arg, m.group(1))
				: String.format("select count(*) from (%s) t", arg);
	}

	private static String trimEndOrderBy(String sql) {
		Pattern r = Pattern.compile("(.*)\\s+order by.*?", Pattern.CASE_INSENSITIVE);
		Matcher m = r.matcher(sql);
		return m.matches() ? m.group(1).trim() : sql;
	}

	private static String trimStartFrom(String sql) {
		Pattern r = Pattern.compile(".*?from\\s(.*)", Pattern.CASE_INSENSITIVE);
		Matcher m = r.matcher(sql);
		return m.matches() ? m.group(1).trim() : sql;
	}
}
