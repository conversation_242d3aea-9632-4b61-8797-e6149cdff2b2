package com.pcitc.opal.ac.dao.imp;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;
import javax.persistence.TypedQuery;

import com.pcitc.opal.common.CommonEnum;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.pcitc.opal.ac.dao.AlarmChangePlanRepositoryCustom;
import com.pcitc.opal.ac.pojo.AlarmChangePlan;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;

/*
 * 报警变更方案实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_AlarmChangePlanRepositoryImpl
 * 作       者：kun.zhao
 * 创建时间：2017/01/19
 * 修改编号：1
 * 描       述：报警变更方案实体的Repository实现    
 */
public class AlarmChangePlanRepositoryImpl extends BaseRepository<AlarmChangePlan, Long> 
		implements AlarmChangePlanRepositoryCustom {

	/**
	 * 获取当天最大的编号
	 * @return
	 * <AUTHOR> 2018-1-22
	 */
	@Override
	public String getMaxPlanCode() {
		try {
			Map<String,Object> paramList=new HashMap<String, Object>();
			String hql = "select max(CAST(t.planCode AS long)) from AlarmChangePlan t where t.planCode like :planCode";
			paramList.put("planCode", this.sqlLikeReplace(DateFormatUtils.format(new Date(),"yyyyMMdd")) + "%");

			Query query=this.getEntityManager().createQuery(hql);
			setParameterList(query,paramList);
			String code = String.valueOf(query.getSingleResult());
			return code;
		}catch (Exception ex){
			return "";
		}
	}
	/**
	 * 校验数据
	 *
	 * <AUTHOR> 2018-01-26
	 * @param po  实体
	 * @return    返回结果信息类
	 */
	@Override
	public CommonResult validate(AlarmChangePlan po){
		CommonResult commonResult = new CommonResult();
		try {
			//1.名称和车间ID联合唯一性校验
			StringBuilder jpql = new StringBuilder(
					"from AlarmChangePlan t where t.unitId=:unitId");
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("unitId", po.getUnitId());

			long count = this.getCount(jpql.toString(), paramList);
			if (count > 0) {
				throw new Exception("变更方案中已存在此装置!");
			}
		} catch (Exception e) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(e.getMessage());
		}
		return commonResult;
	}
	/**
	 * 新增
	 * @return
	 * <AUTHOR> 2018-1-22
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult add(AlarmChangePlan po) {
		CommonResult commonResult = new CommonResult();
		try {
			this.getEntityManager().persist(po);
			commonResult.setResult(po);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		return commonResult;
	}
	
	/**
	 * 删除报警变更方案实体
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangePlanIds 报警变更方案Id数组
	 * @return 返回结果信息类
	 */
	@Override
	@Transactional
	public CommonResult deleteAlarmChangePlan(Long[] alarmChangePlanIds) {
		// 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            String hql = " from AlarmChangePlan t where t.planId in (:anlyAlarmChangePlanIds)";
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> anlyAlarmChangePlanIdsList = Arrays.asList(alarmChangePlanIds);
            paramList.put("anlyAlarmChangePlanIds", anlyAlarmChangePlanIdsList);

            TypedQuery<AlarmChangePlan> query = getEntityManager().createQuery(hql, AlarmChangePlan.class);
            this.setParameterList(query, paramList);
            List<AlarmChangePlan> alarmChangePlanList = query.getResultList();
            alarmChangePlanList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 删除出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
	}
	
	/**
	 * 根据报警变更方案维护ID获取单条数据信息
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangePlanId 报警变更方案Id
	 * @return 报警变更方案实体数据
	 * @throws Exception
	 */
	@Override
	public AlarmChangePlan getSingleAlarmChangePlan(Long alarmChangePlanId) {
		try {
            return getEntityManager().find(AlarmChangePlan.class, alarmChangePlanId);
        } catch (Exception ex) {
            throw ex;
        }
	}

	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangePlanIds 报警变更方案Id数组
	 * @return 报警变更方案实体集合
	 */
	@Override
	public List<AlarmChangePlan> getAlarmChangePlan(Long[] alarmChangePlanIds) {
		try {
            // 查询字符串
            String hql = "from AlarmChangePlan t ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (alarmChangePlanIds.length > 0) {
                hql += " where t.planId in (:alarmChangePlanIds)";
                List<Long> alarmChangePlanIdsList = Arrays.asList(alarmChangePlanIds);
                paramList.put("alarmChangePlanIds", alarmChangePlanIdsList);
            }
            TypedQuery<AlarmChangePlan> query = getEntityManager().createQuery(hql, AlarmChangePlan.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
	}

	/**
	 * 加载报警变更方案维护主数据
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param unitCodes   装置编码数组
	 * @param startTime 申请时间范围开始
	 * @param endTime   申请时间范围结束
	 * @param statuses  状态数组
	 * @param page	分页实体
	 * @return 报警变更实体数据
	 */
	@Override
	public PaginationBean<AlarmChangePlan> getAlarmChangePlan(String[] unitCodes, Date startTime, Date endTime,
			Integer[] statuses,Integer businessType, Pagination page) {
		try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from AlarmChangePlan t where 1=1 ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
            	hql.append(" and t.unitId in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            // 过滤日期
            if (startTime != null && endTime != null) {
            	hql.append(" and t.applyTime between :startTime and :endTime ");
                paramList.put("startTime", startTime);
                paramList.put("endTime", endTime);
            }else if(startTime != null){
            	hql.append(" and t.applyTime >= :startTime ");
            	paramList.put("startTime", startTime);
            }else if(endTime != null){
            	hql.append(" and t.applyTime <= :endTime ");
            	paramList.put("endTime", endTime);
            }
            //过滤状态
            if (ArrayUtils.isNotEmpty(statuses)) {
                hql.append(" and t.status in (:statuses) ");
                paramList.put("statuses", Arrays.asList(statuses));
            }
            if (businessType == CommonEnum.AlarmChangeBizTypeEnum.Apply.getIndex() ) {
				hql.append(" order by case when t.status=3 then 2 when t.status=4 then 2 else t.status end asc, t.planCode desc ");
			} else if(businessType == CommonEnum.AlarmChangeBizTypeEnum.Issue.getIndex()) {
				hql.append(" order by case when t.status=5 then 4 else t.status end asc, t.planCode desc ");
			} else  {
				hql.append(" order by t.status asc, t.planCode desc ");
			}
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
	}

	/**
	 * 加载报警变更方案数据
	 *
	 * <AUTHOR> 2018-03-14
	 * @param unitCodes   装置编码数组
	 * @param startTime 申请时间范围开始
	 * @param endTime   申请时间范围结束
	 * @param instanceIds   流程实例id集合
	 * @param page	分页实体
	 * @return 报警变更实体数据
	 */
	@Override
	public PaginationBean<AlarmChangePlan> getAlarmChangePlan(String[] unitCodes, Date startTime, Date endTime,List<String> instanceIds, Pagination page) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("from AlarmChangePlan t where 1=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			// 过滤装置
			if (ArrayUtils.isNotEmpty(unitCodes)) {
				hql.append(" and t.unitId in (:unitIds) ");
				paramList.put("unitIds", Arrays.asList(unitCodes));
			}
			if (instanceIds!=null&&instanceIds.size()>0){
				hql.append(" and t.flowCaseId in (:instanceIds) ");
				paramList.put("instanceIds",instanceIds);
			}
			// 过滤日期
			if (startTime != null && endTime != null) {
				hql.append(" and t.applyTime between :startTime and :endTime ");
				paramList.put("startTime", startTime);
				paramList.put("endTime", endTime);
			}else if(startTime != null){
				hql.append(" and t.applyTime >= :startTime ");
				paramList.put("startTime", startTime);
			}else if(endTime != null){
				hql.append(" and t.applyTime <= :endTime ");
				paramList.put("endTime", endTime);
			}
			hql.append(" order by t.submitTime desc, t.planCode asc ");
			// 调用基类方法查询返回结果
			return this.findAll(page, hql.toString(), paramList);
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 更新
	 * @return
	 * <AUTHOR> 2018-1-22
	 */
    @Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult update(AlarmChangePlan po) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			getEntityManager().merge(po);
			commonResult.setResult(po);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("更新成功！");
		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
    }

}
