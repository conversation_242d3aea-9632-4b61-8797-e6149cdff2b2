var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var alarmPriorityListUrl = OPAL.API.commUrl + '/getAlarmPriorityList';
var searchUrl = OPAL.API.aeUrl + '/alarmBadpvDistribution';
var workTeamUrl = OPAL.API.commUrl + "/getWorkTeam";
var exportAlarmBadpvDistributionUrl = OPAL.API.aeUrl + '/alarmBadpvDistribution/exportAlarmBadpvDistribution';
var isLoading = true;
$(function() {
    var page = {
        /**
         * 初始化
         */
        init: function() {
            this.bindUi();
            //扩展日期插件
            OPAL.util.extendDate();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化表格
            page.logic.initTable();
            //初始化优先级
            page.logic.initAlarmPriorityList();
            //初始化日期时间控件
            page.logic.initTime();
            $('#workTeamIds').html("");
            $("#workTeamIds").prop('disabled', true);

            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("AlarmBadpvDistribution");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
            //默认查询数据
            setTimeout(function () {
                if ($("#priority").val()!=null) {
                    page.logic.search();
                }
            }, 500);
        },
        bindUi: function() {
            //查询
            $('#search').click(function() {
                if (OPAL.util.checkDateIsValid() == true) {
                    isLoading = false;
                    page.logic.search();
                }
            })
            $("#alarmBadpvDistributionExport").click(function() {
                page.logic.exportExcel();
            })
            $("#startTime").unbind("change");
             $("#endTime").unbind("change");
             $("#startTime").change(function() {
                page.logic.changeTeam(this);
             })
             $("#endTime").change(function() {
                page.logic.changeTeam(this);
             })
        },
        data: {
            // 设置查询参数
            param: {}
        },
        logic: {
            changeTeam: function(t) {
                page.logic.initWorkTeam();
             },
            initTable: function() {
                OPAL.ui.initBootstrapTable2("table", {
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            var tableOption = $('#table').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            return (index + 1) + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "位置",
                        field: 'alarmPointLocation',
                        rowspan: 1,
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "坏点次数",
                        field: 'count',
                        rowspan: 1,
                        align: 'right',
                        width: '100px'
                    }, {
                        title: "操作",
                        field: '',
                        rowspan: 1,
                        align: 'center',
                        formatter: page.logic.onActionRenderer,
                        width: '100px'
                    }]
                }, page.logic.queryParams,"search")
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a name="TableEditor" href="javascript:window.page.logic.detail(\'' + rowData.alarmPointId + '\',\'' + rowData.unitName + '\',\'' + rowData.prdtCellName + '\',\'' + rowData.tag + '\')">详情</a> '
                ]
            },
            detail:function(alarmPointId,unitName,prdtCellName,tag) {
                var title = "报警坏点分布详情";
                layer.open({
                    type: 2,
                    title: title,
                    closeBtn: 1,
                    area: ['1000px', '490px'],
                    shadeClose: false,
                    content: 'AlarmBadpvDistributionDtl.html?'+ Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "alarmPointId": alarmPointId,
                            'title': title,
                            "unitName":unitName,
                            "prdtCellName":prdtCellName,
                            "tag":tag,
                            "startTime":$("#startTime").val(),
                            "endTime":$("#endTime").val()
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 设置日期插件
             */
            initTime: function() {
                /**
                 * 初始化日期时间选择控件组
                 */
                OPAL.ui.initDateTimePeriodPicker({
                    format: 'yyyy-MM-dd HH:mm:ss',
                    type: 'datetime',
                }, function() {
                    page.logic.initWorkTeam();
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function(nodes) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $("#workTeamIds").prop('disabled', false);
                            page.logic.initWorkTeam();
                            $('.textbox,.combo').css('background-color','');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('#workTeamIds').html("");
                            $("#workTeamIds").prop('disabled', true);
                            $('.textbox-disabled').css('background-color','rgb(235, 235, 228)');
                        }
                    }
                }, false, function() {
                    $("#searchPrdt").combotree("checkAllNodes");
                });
            },
            /**
             * 搜索
             */
            search: function() {
                page.logic.setData();
                if (!OPAL.util.checkDateIsValid()) {
                    return false;
                }
                $("#search").attr('disabled', true);
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            setData: function() {
                page.data.param = OPAL.form.getData("searchForm");
            },
            /**
             * 初始化查询 优先级
             */
            initAlarmPriorityList: function() {
                OPAL.ui.getCombobox("priority", alarmPriorityListUrl, {
                    selectValue: -1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function() {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化班组选择
             */
            initWorkTeam: function() {
                var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                if (unitIds.length != 1) return;
                if ($("#startTime").val() =='' || $("#endTime").val() == '') {
                    $('#workTeamIds').html("");
                    $("#workTeamIds").prop('disabled', true);
                    return;
                 }else {
                    $("#workTeamIds").prop('disabled', false);
                 }
                OPAL.ui.getCombobox("workTeamIds", workTeamUrl, {
                    keyField: "workTeamId",
                    valueField: "workTeamSName",
                    selectFirstRecord: true,
                    mapManyValues: true, //是否一条记录匹配多个隐藏值
                    mapManyDataFieldName: 'workTeamIdList',
                    data: {
                        "startTime": $("#startTime").val(),
                        "endTime": $("#endTime").val(),
                        "unitId": unitIds[0],
                    }
                }, null);

            },
            exportExcel: function() {
                var titleArray = new Array();
                var tableTitle = $('#table').bootstrapTable('getOptions').columns[0];
                $.each(tableTitle, function(i, el) {
                    if (i >= 1) {
                        titleArray.push({
                            'key': el.field,
                            'value': el.title
                        })
                    }
                })
                var data = {};
                var pageSize = $('#table').bootstrapTable('getOptions').pageSize;
                var pageNumber = $('#table').bootstrapTable('getOptions').pageNumber;
                data.titles = JSON.stringify(titleArray);
                data.pageSize = pageSize;
                data.pageNumber = pageNumber;
                page.logic.setData();
                $.extend(data, page.data.param);
                $('#formExPort').attr('action', exportAlarmBadpvDistributionUrl);
                $('#titles').val(data.titles);
                $('#pageSize').val(data.pageSize);
                $('#pageNumber').val(data.pageNumber);
                $('#unitIds1').val(data.unitIds);
                $('#prdtCellIds1').val(data.prdtCellIds);
                $('#tag1').val(data.tag);
                $('#priorityExport').val(data.priority);
                $("#startDate").val(data.startTime);
                $("#endDate").val(data.endTime);
                $("#team").val(data.workTeamIds);
                $('#formExPort').submit();
            }

        }
    };
    page.init();
    window.page = page;
});