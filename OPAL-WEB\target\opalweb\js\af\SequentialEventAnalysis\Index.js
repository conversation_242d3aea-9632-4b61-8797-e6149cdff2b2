var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var getQueryStartAndEndDateUrl = OPAL.API.commUrl + '/getShowTime';
var getSequentialEventGraphUrl = OPAL.API.afUrl + '/sequentialEventAnalysis/getSequentialEventGraph';
var queryTimeArray = new Array();
var setStartTime;
var setNowTime;
var option;
var myChart;
var tableData;
var isLoading = true;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            this.bindUi();
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                myChart.resize();
            };
            //初始化查询装置树
            page.logic.initUnitTree();
            // 日期扩展
            OPAL.util.extendDate();
            // 初始化 报警时间的时间点
            page.logic.getQueryTime();
            // 初始化表格
            page.logic.initTable();
            //初始化图表
            //page.logic.initChart();
            myChart = OPAL.ui.chart.initEmptyChart('Sequentia');

            if (isLoading && (page.data.param.unitIds == null || page.data.param.unitIds == undefined || page.data.param.unitIds.length == 0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("SequentialEventAnalysis");
                if (cookieValue != null && cookieValue != undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue[0]);

                    //默认查询数据
                    setTimeout(function () {
                        if ($("#startTime").val() != null && $("#endTime").val() != null) {
                            page.logic.search();
                        }
                    }, 500);
                }
            }

        },
        bindUi: function () {
            $('#btnSearch').click(function () {
                isLoading = false;
                page.logic.search();
            })
        },
        data: {
            //查询参数
            param: {}
        },
        logic: {
            /**
             * 查询
             */
            search: function () {
                var startTime = $('#startTime').val();
                var endTime = $('#endTime').val();
                var unitIds = $("#unitIds").val();
                if (unitIds == '') {
                    layer.msg('请选择装置！');
                    return false;
                }
                var flag = page.logic.checkTime(startTime, endTime);
                if (!flag) {
                    return false;
                }
                $('#btnSearch').attr('disabled', true);
                $.ajax({
                    url: getSequentialEventGraphUrl,
                    async: true,
                    data: $('#formSearch').serialize(),
                    dataType: "JSON",
                    type: 'GET',
                    success: function (result) {
                        if (result.listEntity.length == 0) {
                            myChart = OPAL.ui.chart.initEmptyChart('Sequentia');
                        } else {
                            page.logic.initChart();
                            page.logic.setCharts(result);
                        }
                        $("#table").bootstrapTable("removeAll");
                        if (result == null || result == undefined || result.length == 0) {
                            return;
                        }
                        tableData = result.listEntity;
                        $("#table").bootstrapTable("load", result.listEntity);
                        var tds = $('#table').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text());
                        })
                        $('#btnSearch').attr('disabled', false);
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });

            },
            /**
             * 获得固定的时间点
             */
            getQueryTime: function () {
                OPAL.util.getQueryTime(function (queryTime) {
                    queryTimeArray = queryTime.split(':');
                    // 初始化 开始时间和结束时间
                    page.logic.getQueryStartAndEndDate(queryTime);
                    // 初始化 时间设置
                    page.logic.initTime();
                });
            },
            // 初始化 开始时间和结束时间
            getQueryStartAndEndDate: function (queryTime) {
                $.ajax({
                    url: getQueryStartAndEndDateUrl,
                    async: false,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        setStartTime = dataArr[1].value;
                        setNowTime = dataArr[2].value;
                        var hour = (setNowTime.split(' ')[1]).split(':')[0];
                        if (Number(hour) >= Number(queryTimeArray[0])) {
                            setStartTime = setNowTime.split(' ')[0] + ' ' + queryTime;
                        }
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 设置日期插件
             */
            initTime: function () {
                start = laydate.render({
                    elem: '#startTime', //指定元素
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd HH:mm:ss', //日期格式
                    value: setStartTime,
                    max: setNowTime //最大日期

                });
                end = laydate.render({
                    elem: '#endTime',
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd HH:mm:ss',
                    value: setNowTime,
                    max: setNowTime

                });
            },
            /**
             * 检查时间
             */
            checkTime: function (startTime, endTime) {
                var time1 = OPAL.util.strToDate(startTime).getTime();
                var time2 = OPAL.util.strToDate(endTime).dateAdd('s', 1).getTime();
                var timeDifference = (time2 - time1) / (1000 * 60 * 60 * 24) > 1;
                var flag = true;
                if (startTime == '' || endTime == '') {
                    layer.msg('开始时间和结束时间不能为空！')
                    flag = false;
                }
                if (OPAL.util.strToDate(startTime) > OPAL.util.strToDate(endTime)) {
                    layer.msg('开始时间不能大于结束时间！')
                    flag = false;
                }
                if (timeDifference) {
                    layer.msg('查询时间范围不能超过24小时！')
                    flag = false;
                }
                if (OPAL.util.strToDate(endTime) > OPAL.util.strToDate(setNowTime)) {
                    layer.msg('不能大于当前系统时间！')
                    flag = false;
                }
                return flag;
            },
            setCharts: function (result) {
                if (result.listEntity.length == 0) {
                    myChart = OPAL.ui.chart.initEmptyChart('Sequentia');
                    return;
                }
                var startTime = $("#startTime").val();
                var endTime = $("#endTime").val();
                var minutes = endTime.substring(14, 16);
                var seconds = endTime.substring(17);
                if (minutes != '00' || seconds != '00') {
                    var hours = endTime.substring(11, 13);
                    hours = (parseInt(hours) + 1).toString();
                    endTime = endTime.substring(0, 11) + hours + ':00:00';
                }
                startTime = startTime.substring(0, 14) + '00:00';
                //startTime = OPAL.util.dateFormat(OPAL.util.strToDate(startTime).dateAdd('h',-0.5),'yyyy-MM-dd HH:mm:ss');
                var anchor = [{
                    name: startTime,
                    value: [startTime, 0]
                }, {
                    name: endTime,
                    value: [endTime, 0]
                }];
                option.series[0].data = result.processB.yaxisList;
                option.series[1].data = result.processACraft.yaxisList;
                option.series[2].data = result.processALock.yaxisList;
                option.series[3].data = result.processACraftLock.yaxisList;
                option.series[4].data = result.processOperate.yaxisList;
                option.series[5].data = result.processOther.yaxisList;
                option.series[6].data = anchor;
                option.series[0].tooltip = result.processB.tipsList;
                option.series[1].tooltip = result.processACraft.tipsList;
                option.series[2].tooltip = result.processALock.tipsList;
                option.series[3].tooltip = result.processACraftLock.tipsList;
                option.series[4].tooltip = result.processOperate.tipsList;
                option.series[5].tooltip = result.processOther.tipsList;

                myChart.setOption(option);
            },
            /**
             * 点击图形交互
             */
            chartClick: function () {
                myChart.on('click', function (params) {
                    page.logic.setTable(params);
                })
            },
            /**
             * 高亮符合条件的行
             */
            setTable: function (params) {
                var rowIndex;
                for (var i = 0; i < tableData.length; i++) {
                    if (params.value[0] == tableData[i].startTimeStr) {
                        if (params.value[1] == 1 && tableData[i].eventTypeId != 1001 && tableData[i].eventTypeId.toString().substring(0, 2) != '30') {
                            rowIndex = i;
                        }
                        if (params.value[1] == 2 && tableData[i].eventTypeId.toString().substring(0, 2) == '30') {
                            rowIndex = i;
                        }
                        if (params.value[1] == 3 && tableData[i].eventTypeId == 1001) {
                            rowIndex = i;
                        }
                    }
                };
                var tableOption = $('#table').bootstrapTable('getOptions');
                if (rowIndex != undefined) {
                    tableOption.pageNumber = Math.ceil((rowIndex + 1) / tableOption.pageSize);
                    tableOption.rowStyle = function (row, index) {
                        var style = "";
                        if (row.startTimeStr == params.value[0]) {
                            if (params.value[1] == 1 && row.eventTypeId != 1001 && row.eventTypeId.toString().substring(0, 2) != '30') {
                                style = 'add-class-bg';
                            }
                            if (params.value[1] == 2 && parseInt(row.eventTypeId.toString().substring(0, 2)) == 30) {
                                style = 'add-class-bg';
                            }
                            if (params.value[1] == 3 && row.eventTypeId == 1001) {
                                style = 'add-class-bg';
                            }
                        }
                        return {
                            classes: style
                        }
                    }
                }
                $("#table").bootstrapTable('refreshOptions', tableOption);
                var tds = $('#table').find('tbody tr td');
                $.each(tds, function (i, el) {
                    $(this).attr("title", $(this).text());
                })
                rowIndex = undefined;
            },
            /**
             * 初始化图表
             */
            initChart: function () {
                if (myChart && !myChart.isDisposed()) {
                    myChart.clear();
                    myChart.dispose();
                }
                myChart = echarts.init(document.getElementById('Sequentia'));
                option = {
                    grid: {
                        left: '1%',
                        right: '3%',
                        bottom: '1%',
                        top: '14%',
                        height: '260px',
                        containLabel: true
                    },

                    tooltip: {
                        padding: 10,
                        backgroundColor: '#222',
                        borderColor: '#777',
                        borderWidth: 1,
                        trigger: 'item',
                        formatter: function (param) {
                            return option.series[param['seriesIndex']]['tooltip'][param['dataIndex']];
                        }
                    },
                    toolbox: {
                        right: '20px',
                        // left: 'right',
                        itemSize: 15,
                        lineStyle: {
                            color: ['#e5e5e5']
                        },

                        top: 7,
                        feature: {
                            dataZoom: {
                                yAxisIndex: 'none'
                            },
                            restore: {}
                        }
                    },
                    dataZoom: [{
                        type: 'inside',
                        throttle: 250
                    }],
                    xAxis: [{
                        type: 'time',
                        minInterval: 1,
                        axisLine: {
                            onZero: false
                        },
                        splitLine: { //网格线
                            show: false,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            showMinLabel: true
                        }
                    }],
                    yAxis: [{
                        type: 'category',
                        data: ['', '其他', '操作', '报警', ''],

                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            show: false
                        },
                        boundaryGap: false,
                        nameLocation: 'end',
                        nameGap: 20,
                        nameTextStyle: {
                            color: '#fff',
                            fontSize: 16
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#333',

                            }
                        },
                        splitLine: {
                            show: true
                        }
                    }, {
                        type: 'category',
                        data: ['', '其他', '操作', '报警', ''],
                        offset: -1135,
                        show: false,
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            show: false
                        },
                        boundaryGap: false,
                        nameLocation: 'end',
                        nameGap: 20,
                        nameTextStyle: {
                            color: '#fff',
                            fontSize: 16
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#333',

                            }
                        },
                        splitLine: {
                            show: true
                        }
                    }],
                    legend: {
                        itemGap: 25,
                        itemWidth: 17, //设置icon大小
                        itemHeight: 17, //设置icon大小
                        left: '50px',
                        top: '3%',
                        data: [{
                            name: '报警(级别B)',
                            icon: 'image://../../../images/echart2.png',
                            itemWidth: '10px',
                            textStyle: {
                                color: '#333',
                                fontSize: 12
                            }
                        }, {
                            name: '报警(级别A)工艺卡片',
                            icon: 'image://../../../images/echart1.png',
                            itemWidth: '10px',
                            textStyle: {
                                color: '#333',
                                fontSize: 12
                            }
                        }, {
                            name: '报警(级别A)联锁',
                            icon: 'image://../../../images/echart3.png',
                            itemWidth: '10px',
                            textStyle: {
                                color: '#333',
                                fontSize: 12
                            }
                        }, {
                            name: '报警(级别A)工艺卡片且联锁',
                            icon: 'image://../../../images/echart5.png',
                            itemWidth: '10px',
                            textStyle: {
                                color: '#333',
                                fontSize: 12
                            }
                        }, {
                            name: '操作数',
                            icon: 'image://../../../images/echart6.png',
                            itemWidth: '10px',
                            textStyle: {
                                color: '#333',
                                fontSize: 12
                            }
                        }, {
                            name: '其他',
                            icon: 'image://../../../images/echart7.png',
                            itemWidth: '10px',
                            textStyle: {
                                color: '#333',
                            }
                        },

                        ]
                    },
                    series: [{
                        name: '报警(级别B)',
                        yAxisIndex: 0,
                        type: 'scatter',
                        symbol: 'image://../../../images/echart2.png',
                        symbolSize: 18,
                        animation: false,
                        itemStyle: {
                            normal: {
                                color: '#94c94e',
                                lineStyle: {
                                    color: "#2ec7c9",

                                }
                            }
                        },
                        data: []
                    }, {
                        name: '报警(级别A)工艺卡片',
                        yAxisIndex: 0,
                        type: 'scatter',
                        symbol: 'image://../../../images/echart1.png',
                        symbolSize: 18,
                        animation: false,
                        itemStyle: {
                            normal: {
                                color: '#ffa200',
                                lineStyle: {
                                    color: "#2ec7c9",

                                }
                            }
                        },
                        data: []
                    }, {
                        name: '报警(级别A)联锁',
                        yAxisIndex: 0,
                        type: 'scatter',
                        symbol: 'image://../../../images/echart3.png',
                        symbolSize: 18,
                        animation: false,
                        itemStyle: {
                            normal: {
                                color: '#b17ed9',
                                lineStyle: {
                                    color: "#2ec7c9",

                                }
                            }
                        },
                        data: []
                    }, {
                        name: '报警(级别A)工艺卡片且联锁',
                        yAxisIndex: 0,
                        type: 'scatter',
                        symbol: 'image://../../../images/echart5.png',
                        symbolSize: 18,
                        animation: false,
                        itemStyle: {
                            normal: {
                                color: '#f74242',
                                lineStyle: {
                                    color: "#2ec7c9",

                                }
                            }
                        },
                        data: []
                    }, {
                        name: '操作数',
                        yAxisIndex: 0,
                        type: 'scatter',
                        symbol: 'image://../../../images/echart6.png',
                        symbolSize: 18,
                        animation: false,
                        itemStyle: {
                            normal: {
                                color: '#1296db',
                                lineStyle: {
                                    color: "#2ec7c9",

                                }
                            }
                        },
                        data: []
                    }, {
                        name: '其他',
                        yAxisIndex: 0,
                        type: 'scatter',
                        symbol: 'image://../../../images/echart7.png',
                        symbolSize: 18,
                        animation: false,
                        itemStyle: {
                            normal: {
                                color: '#62cf96',
                                lineStyle: {
                                    color: "#2ec7c9",

                                }
                            }
                        },
                        data: []
                    }, {
                        name: 'anchor',
                        yAxisIndex: 0,
                        type: 'line',
                        showSymbol: false,
                        data: [],
                        itemStyle: {
                            normal: {
                                opacity: 0
                            }
                        },
                        lineStyle: {
                            normal: {
                                opacity: 0
                            }
                        }
                    }]
                };
                myChart.setOption(option);
                page.logic.chartClick(myChart);
            },
            /**
             * 初始化表格
             */
            initTable: function (params) {
                $("#table").bootstrapTable({
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "时间",
                        field: 'startTimeStr',
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "装置",
                        field: 'unitName',
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "位号",
                        field: 'alarmPointTag',
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "参数名称",
                        field: 'location',
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "级别",
                        field: 'craftRankName',
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "报警等级",
                        field: 'alarmFlagName',
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "优先级",
                        field: 'priorityName',
                        align: 'center',
                        width: '60px'
                    }, {
                        title: "事件类型",
                        field: 'eventTypeName',
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "单位",
                        field: 'measUnitName',
                        align: 'left',
                        width: '100px'
                    }],
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    sidePagination: "client",
                    cache: false,
                    pagination: true, //是否分页
                    pageSize: 20,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [20, 50, 100],
                    striped: true,
                    rowStyle: function (row, index) {
                        var style = "";
                        if (row.endTime != null) {
                            style = 'redRow';
                        }
                        return {
                            classes: style
                        }
                    }
                });

                $('#table').colResizable({
                    liveDrag: true
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect("unitIds", commonUnitTreeUrl, "id", "parentId", "sname", {
                    multiple: false,
                    onlyLeafCheck: true,
                    onChange: function (node) {
                        OPAL.ui.getComboMultipleSelect("prdtCellIds", prdtCellUrl, {
                            keyField: "prdtCellId",
                            valueField: "sname",
                            data: {
                                "unitId": node,
                                'isAll': true
                            },
                        }, false, function () {
                            var treeView = $("#prdtCellIds").combotree('tree');
                            var nd = treeView.tree('find', -1);
                            if (nd != null) {
                                treeView.tree('update', {
                                    target: nd.target,
                                    text: '全选'
                                });
                            }
                            $("#prdtCellIds").combotree("checkAllNodes");
                        });
                    }
                }, false, function () { });
                $("#unitIds").combotree("getValues");
            }
        }
    }
    page.init();
    window.page = page;
})