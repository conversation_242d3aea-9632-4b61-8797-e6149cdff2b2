package com.pcitc.opal.common.bll.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import pcitc.imp.common.ettool.Annotaion.ResourceContract;

import java.util.Date;

@Data
@ResourceContract(ReadOnly = false)
public class CraftStartStopDispositionVO {

    private String unitCode;

    private String prdtCode;
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8" )
    private Date startTime;
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date stopTime;

}
