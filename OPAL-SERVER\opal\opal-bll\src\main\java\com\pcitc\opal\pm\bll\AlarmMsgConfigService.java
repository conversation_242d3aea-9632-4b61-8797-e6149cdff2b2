package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.AlarmMsgConfigEntity;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface AlarmMsgConfigService {
     PaginationBean<AlarmMsgConfigEntity> getAlarmMsgConfigByInfo(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inSendMsg, Integer timeInterval, Pagination page )throws Exception;
     List<AlarmMsgConfigEntity> getAlarmMsgConfigByAlarmMsgConfId(Long[] alarmMsgConfId);
     CommonResult updateAlarmMsgConfigInfo(Long[] alarmMsgConfIds, String mobileBookId,Long timeInterval, Integer inSendMsg);
     CommonResult deleteAlarmMsgConfigById(Long[] id);
     CommonResult addOrUpdateAlarmMsgConfig(Long[] alarmPointIds,Long[] alarmFlagIds,String mobileBookId,Long timeInterval,Integer inSendMsg);
}
