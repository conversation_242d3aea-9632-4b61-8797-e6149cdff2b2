
var AuthHttp = 'http://10.238.255.145:30313/AuthService/'; // http://10.238.255.145:30313 正式    // 'http://10.238.255.10:31697/AuthService/'  测试
var ResourceHttp = 'http://10.238.255.145:32409/ResourceService/';  // http://10.238.255.145:32409/ 正式   //  'http://10.238.255.10:31817/ResourceService/' 测试
var OrgAndUserHttp = 'http://10.238.255.145:30041/OrgAndUserService/'; // http://10.238.255.145:30041  正式   //  'http://10.238.255.10:31455/OrgAndUserService/'  测试
var userAdmin = "admin";
var userAdminId = 'adminId';
var dataComple = [];
var succData = {};
 var arrData = []
$(function(){
	$("button[type=reset]").click(function(){
        $("#defaultForm .form-control").not("[readonly=readonly]").val("");
        return false;
    })
	$("#enabled").val("1")
	$("#orderId").val(0)
	$("#orderId").attr("min",0)
//	$("#demo-table tr").each(function(index){
//		$(this).click(function(){
//			alert(index)
//			$(this).children("td").eq(0).children("input").attr("selected","selected")
//		})
//	})
	
})
  //增删改查
function getData(urlD,dataS){
		$('#demo-table').bootstrapTable({
            method: "get",  //使用get请求到服务器获取数据  
            url: urlD,
            responseHandler: function(res) {
                return {
                    "total": $.ET.getPageInfo(res)[0] == undefined?0:$.ET.getPageInfo(res)[0].recordCount,//总页数
                    "rows": $.ET.toObjectArr(JSON.stringify(res).replace(/\</g,"&lt;").replace(/\>/g,"&gt;"))//数据
                };
            },
            cache: false,
            pagination: true, //启动分页  
            showColumns: true,//显示列
            pageSize: 10,  //每页显示的记录数  
            pageNumber:1, //当前第几页  
            pageList: [10, 20, 50, 100],  //记录数可选列表  
            search: false,  //是否启用查询 
            sidePagination: "server", //表示服务端请求  
            queryParamsType : "undefined",
            queryParams: function queryParams(params) {   //设置查询参数  
                var param = {
                    $skip: (params.pageNumber-1)*params.pageSize,
                    $top: params.pageSize
                };
                return param;
            },
                columns: dataS
        });
        $('#demo-table').bootstrapTable('hideColumn', 'crtUserName');
        $('#demo-table').bootstrapTable('hideColumn', 'crtUserId');
        $('#demo-table').bootstrapTable('hideColumn', 'crtUserDate');      
        $('#demo-table').bootstrapTable('hideColumn', 'mntUserName');
		$('#demo-table').bootstrapTable('hideColumn', 'mntUserId');
		$('#demo-table').bootstrapTable('hideColumn', 'mntUserDate');
}
function getDataNo(urlD,dataS){
		$('#demo-table').bootstrapTable({
            method: "get",  //使用get请求到服务器获取数据  
            url: urlD,
            responseHandler: function(res) {
                return {
                    "total": $.ET.getPageInfo(res)[0] == undefined?0:$.ET.getPageInfo(res)[0].recordCount,//总页数
                    "rows": $.ET.toObjectArr(JSON.stringify(res).replace(/\</g,"&lt;").replace(/\>/g,"&gt;"))//数据
                };
            },
            cache: false,
            pagination: true, //启动分页  
            showColumns: true,//显示列
            pageSize: 10,  //每页显示的记录数  
            pageNumber:1, //当前第几页  
            clickToSelect: true, //是否启用点击选中行
            pageList: [10, 20, 50, 100],  //记录数可选列表  
            search: false,  //是否启用查询 
            sidePagination: "server", //表示服务端请求  
            queryParamsType : "undefined",
            queryParams: function queryParams(params) {   //设置查询参数  
                var param = {
                    $skip: (params.pageNumber-1)*params.pageSize,
                    $top: params.pageSize
                };
                return param;
            },
                columns: dataS
        });
}
//                      删除 +++++++++++++++++提示
function deleteD(url){
	
		layer.confirm('确定删除这条记录么？', {
		  btn: ['确定','取消'] //按钮
		}, function(){
			   $.ajax({
			        url: url,
			        async: false,//
				    type: 'DELETE',//PUT DELETE POST
				    dataType:"text",
				    success: function (result) {
				       if(result.indexOf('collection') < 0){
				       		layer.msg("删除成功")
				            setTimeout(function(){
				                window.location.reload()
				            },500)
				        }else{
				           result =JSON.parse(result)
				            layer.msg(result.collection.error.message )
				        }
				    }
				})
		}, function(index){
		  layer.close(index)
		});
	   
} 

function deleteDO(url){
	
		layer.confirm('确定删除这条记录么？', {
		  btn: ['确定','取消'] //按钮
		}, function(){
			   $.ajax({
			        url: url,
			        async: false,//
				    type: 'DELETE',//PUT DELETE POST
				    dataType:"text",
				    success: function (result) {
				       if(result.indexOf('collection') < 0){
				       		layer.msg("删除成功")
				       		authShow()
				        }else{
				           result =JSON.parse(result)
				            layer.msg(result.collection.error.message )
				        }
				    }
				})
		}, function(index){
		  layer.close(index)
		});
	   
} 
// 修改 +++++++++++++++++++++  提示
function reviseD(url,data){
	$("input.form-control").each(function(){
	 	$(this).css("border","1px solid #ccc")
			if($(this).parent().prev().children("b").length > 0){
				if($(this).val() == ""){
					layer.msg("请输入表单必填项")
					$(this).css("border","1px solid red")
					return flag = false;
				}
				
			}
			return flag = true;
		})
	  if(flag){
	  		$.ajax({
			        url: url,
			        async: false,//
			        type: 'PUT',//PUT DELETE POST
			        data:JSON.stringify(data),
			        'processData': false,
			        contentType: "application/json;charset=utf-8",
			        dataType: "text",
			        success: function (result) {
			            if(result.indexOf('collection') < 0){
				           layer.msg("修改成功")
			                setTimeout(function(){
			                    parent.location.reload();
			                    var index = parent.layer.getFrameIndex(window.name);
			                    parent.layer.close(index);
			                },500)
			            }else{
				           result =JSON.parse(result)
				            layer.msg(result.collection.error.message)
				        }
			        }, error: function () {
			            alert("网络出错啦")
			        }
			    })
	  }
}

function reviseDT(url,data){
		 $("input").each(function(){
	 	$(this).css("border","1px solid #ccc")
			if($(this).prev().children("b").length > 0){
				if($(this).val() == ""){
					layer.msg("请输入表单必填项")
					$(this).css("border","1px solid red")
					return flag = false;
				}
				
			}
			return flag = true;
		})
		 if($("#orderId").val() < 0){
		 	layer.msg("排序值不能小于0")
		 	return flag = false;
		 }
	  if(flag){
			$.ajax({
		        url: url,
		        async: false,//
		        type: 'PUT',//PUT DELETE POST
		        data:JSON.stringify(data),
		        'processData': false,
		        contentType: "application/json;charset=utf-8",
		        dataType: "text",
		        success: function (result) {
		            if(result.indexOf('collection') < 0){
			           layer.msg("修改成功")
		                setTimeout(function(){
		                    location.reload();
		                },500)
		            }else{
			           result =JSON.parse(result)
			            layer.msg(result.collection.error.message)
			        }
		        }, error: function () {
		            alert("网络出错啦")
		        }
		    })
	}
}
function addD(url,data){
	 $("input").each(function(){
	 	$(this).css("border","1px solid #ccc")
			if($(this).parent().prev().children("b").length > 0){
				if($(this).val() == ""){
					layer.msg("请输入表单必填项")
					$(this).css("border","1px solid red")
					return flag = false;
				}
			}
			return flag = true;
		})
	 if($("#orderId").val() < 0){
	 	layer.msg("排序值不能小于0")
	 	return flag = false;
	 }
	  if(flag){
	  	$.ajax({
	        url: url,
	        async: false,
	        type: 'POST',
	        data:JSON.stringify(data),
	        'processData': false,
	        contentType: "application/json;charset=utf-8",
	        dataType: "json",
	        success: function (result) {
	            if(!isNaN(result)){
	                layer.msg("新增成功")
	                setTimeout(function(){
	                    parent.location.reload();
	                    var index = parent.layer.getFrameIndex(window.name);
	                    parent.layer.close(index);
	                },500)
	            }else{
	                layer.msg(result.collection.error.message)
	            }
	        }, error: function () {
	            alert("网络出错啦")
	        }
	    })
	  }
}
function addDMessage(url,data,vul){
	$("input.form-control").each(function(){
	 	$(this).css("border","1px solid #ccc")
			if($(this).parent().prev().children("b").length > 0){
				if($(this).val() == ""){
					layer.msg("请输入表单必填项")
					$(this).css("border","1px solid red")
					return flag = false;
				}
				
			}
			return flag = true;
		})
	  if(flag){
			  $.ajax({
			        url: url,
			        async: false,
			        type: 'POST',
			        data:JSON.stringify(data),
			        'processData': false,
			        contentType: "application/json;charset=utf-8",
			        dataType: "json",
			        success: function (result) {
			            if(result == 1){
			                $("#firstS").hide();
		    				$("#seconS").show();
		    				$.ajax({
						        url: url + '/' + vul,
						        async: false,//
						        type: 'get',//PUT DELETE POST
						//                dataType: 'multipart/form-data',
						        success: function (result,status,xhr) {
						        	var res = $.ET.toObjectArr(JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;"))
						        	localStorage.setItem("permissionCode",res[0].permissionCode)
						        },
						        error: function () {
						            alert("网络出错啦")
						        }
						    })
			            }else{
			                layer.msg(result.collection.error.message)
			            }
			        }, error: function () {
			            alert("网络出错啦")
			        }
			    })
	  }
}
function reviseDMessage(url,data,vul){
	$("input.form-control").each(function(){
	 	$(this).css("border","1px solid #ccc")
			if($(this).parent().prev().children("b").length > 0){
				if($(this).val() == ""){
					layer.msg("请输入表单必填项")
					$(this).css("border","1px solid red")
					return flag = false;
				}
				
			}
			return flag = true;
		})
	  if(flag){
			$.ajax({
		        url: url,
		        async: false,//
		        type: 'PUT',//PUT DELETE POST
		        data:JSON.stringify(data),
		        'processData': false,
		        contentType: "application/json;charset=utf-8",
		        dataType: "text",
		        success: function (result) {
		            if(result.indexOf('collection') < 0){
		                $("#firstS").hide();
		    			$("#seconS").show();
		            }else{
			           result =JSON.parse(result)
			            layer.msg(result.collection.error.message)
			        }
		        }, error: function () {
		            alert("网络出错啦")
		        }
		    })
	}
}
function addDL(url,data){
	
	  $.ajax({
	        url: url,
	        async: false,
	        type: 'POST',
	        data:JSON.stringify(data),
	        'processData': false,
	        contentType: "application/json;charset=utf-8",
	        dataType: "json",
	        success: function (result) {
	            if(!isNaN(result)){
	                layer.msg("操作成功")
	                setTimeout(function(){
	                    parent.location.reload();
	                    var index = parent.layer.getFrameIndex(window.name);
	                    parent.layer.close(index);
	                },500)
	            }else{
	                layer.msg(result.collection.error.message)
	            }
	        }, error: function () {
	            alert("网络出错啦")
	        }
	    })
}

function addDT(url,data){
	  $.ajax({
	        url: url,
	        async: false,
	        type: 'POST',
	        data:JSON.stringify(data),
	        'processData': false,
	        contentType: "application/json;charset=utf-8",
	        dataType: "json",
	        success: function (result) {
	            if(result == 1){
	                layer.msg("新增成功")
	               setTimeout(function(){
                    location.reload();
                },500)
	            }else{
	                layer.msg(result.collection.error.message)
	            }
	        }, error: function () {
	            alert("网络出错啦")
	        }
	    })
}

 function initTable(url,len,dataS){
        $('#demo-table').bootstrapTable({
            method: "get",  //使用get请求到服务器获取数据  
            url: url,
            responseHandler: function(res) {
                return {
                    "total": len,//总页数
                    "rows": $.ET.toObjectArr(JSON.stringify(res).replace(/\</g,"&lt;").replace(/\>/g,"&gt;"))//数据
                };
            },
            pagination: true, //启动分页  
            pageSize: 10,  //每页显示的记录数  
            pageNumber:1, //当前第几页  
            pageList: [10, 20, 50, 100],  //记录数可选列表  
            search: false,  //是否启用查询 
            sidePagination: "server", //表示服务端请求  
            queryParamsType : "undefined",
            queryParams: function queryParams(params) {   //设置查询参数  
                var param = {
                    $skip: (params.pageNumber-1)*params.pageSize,
                    $top: params.pageSize
                };
                return param;
            },
                columns: dataS
        });
}
  function initAuthTable(url,len,dataS){
        $('#auth-table').bootstrapTable({
            method: "get",  //使用get请求到服务器获取数据  
            url: url,
            responseHandler: function(res) {
                return {
                    "total": len,//总页数
                    "rows": $.ET.toObjectArr(JSON.stringify(res).replace(/\</g,"&lt;").replace(/\>/g,"&gt;"))//数据
                };
            },
            pagination: true, //启动分页  
            pageSize: 10,  //每页显示的记录数  
            pageNumber:1, //当前第几页  
            pageList: [10, 20, 50, 100],  //记录数可选列表  
            search: false,  //是否启用查询 
            sidePagination: "server", //表示服务端请求  
            queryParamsType : "undefined",
            queryParams: function queryParams(params) {   //设置查询参数  
                var param = {
                    $skip: (params.pageNumber-1)*params.pageSize,
                    $top: params.pageSize
                };
                return param;
            },
                columns: dataS
        });
         $('#auth-table').bootstrapTable('hideColumn', 'crtUserName');
        $('#auth-table').bootstrapTable('hideColumn', 'crtUserId');
        $('#auth-table').bootstrapTable('hideColumn', 'crtUserDate');      
        $('#auth-table').bootstrapTable('hideColumn', 'mntUserName');
		$('#auth-table').bootstrapTable('hideColumn', 'mntUserId');
		$('#auth-table').bootstrapTable('hideColumn', 'mntUserDate');
}
 
 //获取应用列表
function getApps(){
	 $.ajax({
        url: ResourceHttp + 'apps',
        async: false,//
        type: 'get',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	  result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
                var data = $.ET.toObjectArr(result);
                var str = ""
               for(var i = 0;i<data.length;i++){
                    str = str + '<option value='+data[i].appCode+'>'+ data[i].appName +'</option>';
               }
              $("#appS").html("")
               $("#appS").append(str);
               if(localStorage.getItem("appCode") == "" || localStorage.getItem("appCode") == null){
					$("#appS option:first").prop("selected", 'selected'); 
				}else{
					$("#appS").val(localStorage.getItem("appCode"))
				}
			
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}
// 获取组织机构列表
function getOrgs(){
	 $.ajax({
        url: OrgAndUserHttp + 'orgs',
        async: false,//
        type: 'get',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	  result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
                var data = $.ET.toObjectArr(result);
                var str = ""
               for(var i = 0;i<data.length;i++){
                    str = str + '<option value='+data[i].orgCode+'>'+ data[i].orgName +'</option>';
               }
                $("#orgS").html("")
               $("#orgS").append(str);
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}

// 获取组织机构列表
function getOrgUnits(){
	 $.ajax({
        url: OrgAndUserHttp + 'orgs/' + $("#orgS").val() +'/orgUnits',
        async: false,//
        type: 'get',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	  result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
                var data = $.ET.toObjectArr(result);
                var str = ""
               for(var i = 0;i<data.length;i++){
                    str = str + '<option value='+data[i].orgUnitCode+'>'+ data[i].orgUnitName +'</option>';
               }
             $("#orgUnitS").html("")
               $("#orgUnitS").append(str);
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}

// 获取机构视图列表
function getOrgViews(){
	 $.ajax({
        url: OrgAndUserHttp + 'apps/' + $("#appS").val() +'/orgViews',
        async: false,//
        type: 'get',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	  result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
                var data = $.ET.toObjectArr(result);
                var str = ""
               for(var i = 0;i<data.length;i++){
                    str = str + '<option value='+data[i].orgViewCode+'>'+ data[i].orgViewName +'</option>';
               }
             $("#orgViews").html("")
               $("#orgViews").append(str);
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}

// 获取鉴权类
function authSO(){
	 $.ajax({
        url:  ResourceHttp + 'apps/' + localStorage.getItem("appCode") + '/authClasses',
        async: false,//
        type: 'get',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	  result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
                var data = $.ET.toObjectArr(result);
                var str = ""
               for(var i = 0;i<data.length;i++){
                    str = str + '<option value='+data[i].classCode+' data-aId="'+data[i].classId+'">'+ data[i].className +'</option>';
               }
             $("#authSO").html("")
               $("#authSO").append(str);
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}

// 获取鉴权类下属性
function authSOpro(){
	 $.ajax({
        url:  ResourceHttp + 'apps/' + localStorage.getItem("appCode")  + '/authClasses/' + $("#authSO").val() +'/properties',
        async: false,//
        type: 'get',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	  result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
                var data = $.ET.toObjectArr(result);
                var str = ""
               for(var i = 0;i<data.length;i++){
                    str = str + '<option value='+data[i].propertyCode+' data-aId="'+data[i].propertyId+'">'+ data[i].propertyName +'</option>';
               }
             $("#authSOpro").html("")
               $("#authSOpro").append(str);
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}




// 获取已分配许可
function getLicenC(){
	 $.ajax({
        url:AuthHttp + 'apps/' + localStorage.getItem("appCode") + '/roles/'+localStorage.getItem("roleCode")+'/permissions' ,
        async: false,//
        type: 'get',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
                var data = $.ET.toObjectArr(result);
                var str = []
               for(var i = 0;i<data.length;i++){
                   str.push(data[i].permissionId)
               }
	        setTimeout(function(){
        		$("#demo-table").bootstrapTable("checkBy", {field:"permissionId", values:str}) 
        	},100)
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}

// 获取已授权用户
function getAuthoC(){
	 $.ajax({
        url:OrgAndUserHttp + 'apps/' + localStorage.getItem("appCode") + '/roles/'+localStorage.getItem("roleCode")+'/users',
        async: false,//
        type: 'get',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
                var data = $.ET.toObjectArr(result);
                var str = []
               for(var i = 0;i<data.length;i++){
                   str.push(data[i].userId)
               }
	        setTimeout(function(){
        		$("#demo-table").bootstrapTable("checkBy", {field:"userId", values:str}) 
        	},100)
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}

// 获取已分配属性
function getProVC(){
	 $.ajax({
        url:ResourceHttp + 'apps/' + localStorage.getItem("appCode")  + '/authClasses/' + localStorage.getItem("classCode") +'/properties',
        async: false,//
        type: 'get',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
                var data = $.ET.toObjectArr(result);
                var str = []
               for(var i = 0;i<data.length;i++){
                   str.push(data[i].propertyId)
               }
	        setTimeout(function(){
        		$("#demo-table").bootstrapTable("checkBy", {field:"propertyId", values:str}) 
        	},100)
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}

// 获取已分配操作
function getProOpeR(){
	 $.ajax({
        url:AuthHttp + 'apps/' + localStorage.getItem("appCode") + '/properties/'+localStorage.getItem("propertyCode")+'/operations',
        async: false,//
        type: 'get',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
                var data = $.ET.toObjectArr(result);
               for(var i = 0;i<data.length;i++){
                   $(".tree li").each(function(){
                   	 if($(this).data("name") == data[i].operationId){
                   	 	$(this).children("input").prop("checked",true)
                   	 	$(this).addClass("active")
                   	 }
                   })
               }
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}

// 同步
function synch(url){
	
	 $.ajax({
        url:url,
        async: false,//
        type: 'PATCH',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
//              var data = $.ET.toObjectArr(result);
           if(result == 1){
                layer.msg("同步成功")
	            setTimeout(function(){
	                location.reload();
	            },500)
            }else{
            	result = JSON.parse(result)
                layer.msg(result.collection.error.message)
            }
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}
function synchPar(url){
	
	 $.ajax({
        url:url,
        async: false,//
        type: 'PATCH',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
//              var data = $.ET.toObjectArr(result);
           if(result == 1){
                layer.msg("同步成功")
	            setTimeout(function(){
	                parent.location.reload();
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
	            },500)
            }else{
            	result = JSON.parse(result)
                layer.msg(result.collection.error.message)
            }
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}

// 获取许可已有鉴权对象的  属性值
function getPerAuth(){
	 $.ajax({
        url:AuthHttp + 'apps/' + localStorage.getItem("appCode")+ '/permissions/' + localStorage.getItem("permissionCode") + '/authObjects',
        async: false,//
        type: 'get',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
                var data = $.ET.toObjectArr(result);
                $("#com-table tbody").html("")
                if(data.length != 0){
                	$(".hiddent").show();
                	dataComple = data;
//              	localStorage.setItem("olo",data)
                }
               for(var i = 0;i<data.length;i++){
               		var datas = [data[i].operationName,data[i].propertyId,data[i].propertyValueName,data[i].sourceId,data[i].propertyValueId,"<a onclick='dele(this)'>删除</a>"]
               		var html = '<tr>';
    				 for(var j=0;j<datas.length;j++){
    				 	html += '<td>' + datas[j] + '</td>'
    				 }
    				 html += '</tr>'
    				$("#com-table tbody").append(html)
                   
                   
//                 $(".pros li").each(function(){
//                 	 if($(this).data("name") == data[i].propertyValueId){
//                 	 	$(this).addClass("active")
//                 	 	$(this).children("input").prop("checked",true)
//                 	 }
//                 })
//                 $(".menuTree li").each(function(){
//                 	var x = $(this).data("names");
//                 	var arr = data[i].operationName.split(",")
//                 	if(arr.indexOf(x) > -1){
//	                   	 	$(this).addClass("activeT")
//	                   	 	$(this).children("input").prop("checked",true)
//		             }
//                 })
               }
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}

// 获取许可已有鉴权对象的 
function getAuthObj(){
	 $.ajax({
        url:AuthHttp + 'apps/' + localStorage.getItem("appCode")+ '/permissions/' + localStorage.getItem("permissionCode") + '/authObjects',
        async: false,//
        type: 'get',//PUT DELETE POST
//                dataType: 'multipart/form-data',
        success: function (result,status,xhr) {
        	result=JSON.stringify(result).replace(/\</g,"&lt;").replace(/\>/g,"&gt;")
                var data = $.ET.toObjectArr(result);
                var arr = []
               for(var i = 0;i<data.length;i++){
               		var url = AuthHttp + 'apps/' + localStorage.getItem("appCode") + '/permissions/' + localStorage.getItem("permissionCode")+ '/authObjects/' + data[i].authObjectId ;
        			deleteDObj(url)
        			if(i == data.length-1){
        				layer.msg("清除成功")
        				setTimeout(function(){
	                    	parent.location.reload();
		                    var index = parent.layer.getFrameIndex(window.name);
		                    parent.layer.close(index);
		                },500)
        			}
               }
        },
        error: function () {
            alert("网络出错啦")
        }
    })
}
function deleteDObj(url){
			   $.ajax({
			        url: url,
			        async: false,//
				    type: 'DELETE',//PUT DELETE POST
				    dataType:"text",
				    success: function (result) {
				    
				    }
				})
} 