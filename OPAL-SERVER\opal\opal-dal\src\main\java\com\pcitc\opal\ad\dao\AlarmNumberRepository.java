package com.pcitc.opal.ad.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.ad.pojo.AlarmEvent;

/*
 * AlarmNumber实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmNumberRepository
 * 作       者：kun.zhao
 * 创建时间：2017/10/09 
 * 修改编号：1
 * 描       述：AlarmNumber实体的Repository实现   
 */
public interface AlarmNumberRepository extends JpaRepository<AlarmEvent, Long>, AlarmNumberRepositoryCustom {

}
