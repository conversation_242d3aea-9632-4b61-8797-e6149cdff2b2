package com.pcitc.opal.ad.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pcitc.opal.ad.entity.AlarmEventDelApproveEntity;
import com.pcitc.opal.ad.vo.AlarmEventDelApproveVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AlarmEventDelApproveDAO extends BaseMapper<AlarmEventDelApproveEntity> {

    IPage<AlarmEventDelApproveVO> getAlarmEventDelApprove(@Param("unitCode") List<String> unitCode,
                                                          @Param("priority") List<Integer> priority,
                                                          @Param("alarmFlagId") List<Long> alarmFlagId,
                                                          @Param("monitorType") Integer monitorType,
                                                          @Param("startTime") Date startTime,
                                                          @Param("endTime") Date endTime,
                                                          @Param("alarmPointTag") String alarmPointTag,
                                                          @Param("delStatus") Integer delStatus,
                                                          @Param("pager") Page pager);

}
