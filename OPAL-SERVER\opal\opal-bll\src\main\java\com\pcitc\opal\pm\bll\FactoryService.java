package com.pcitc.opal.pm.bll;

import com.pcitc.opal.pm.bll.entity.DBFactoryEntity;
import org.springframework.stereotype.Service;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

/*
 * 工厂业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_FactoryService
 * 作       者：kun.zhao
 * 创建时间：2017/12/11
 * 修改编号：1
 * 描       述：工厂业务逻辑层接口 
 */
@Service
public interface FactoryService {

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param DBFactoryEntity 工厂实体
	 */
	CommonResult addFactory(DBFactoryEntity DBFactoryEntity) throws Exception;

	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryIds  工厂ID集合
	 */
	CommonResult deleteFactory(Long[] factoryIds) throws Exception;

	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param DBFactoryEntity 工厂实体
	 */
	CommonResult updateFactory(DBFactoryEntity DBFactoryEntity) throws  Exception;

	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryId  工厂ID
	 * @return 工厂实体
	 */
	DBFactoryEntity getSingleFactory(Long factoryId) throws  Exception;

	/**
     * 加载工厂维护主数据
     * 
     * <AUTHOR> 2017-12-11
     * @param name    工厂名称/简称
     * @param stdCode 标准编码
     * @param inUse   是否启用
     * @param page    分页实体
     * @return 工厂实体数据
     * @throws Exception
     */
	PaginationBean<DBFactoryEntity> getFactory(Long companyId, String name, String stdCode, Integer inUse, Pagination page) throws  Exception;

}
