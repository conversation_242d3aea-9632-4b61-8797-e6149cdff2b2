package com.pcitc.opal.ad.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AlarmEventTableVO implements Serializable {

    /**
     * 报警事件ID
     */
    private Long eventId;

    /**
     *装置编码
     */
    private String unitCode;

    /**
     *生产单元ID
     */
    private Long prdtCellId;

    /**
     *DCS编码(缓存表)
     */
    private String dcsCode;

    /**
     * 事件类型ID
     */
    private Long eventTypeId;

    /**
     *报警点ID
     */
    private Long alarmPointId;

    /**
     *位号(缓存表)
     */
    private String tag;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     *报警标识(缓存表)
     */
    private String alarmFlagType;

    /**
     *发生时间
     */
    private Date startTime;

    /**
     *报警时间
     */
    private Date alarmTime;

    /**
     *优先级(1紧急；2重要；3一般)
     */
    private Integer priority;

    /**
     *优先级(缓存表)
     */
    private String priorityCache;

    /**
     *先前值
     */
    private String previousValue;

    /**
     *值
     */
    private String nowValue;

    /**
     *限值
     */
    private Double limitValue;

    /**
     *是否搁置(1是；0否)
     */
    private Integer inShelved;

    /**
     *是否屏蔽（1是；0否）
     */
    private Integer inSuppressed;

    /**
     *操作人
     */
    private String operator;

    /**
     *描述
     */
    private String des;

    /**
     *参数
     */
    private String parameter;

    /**
     * 写入时间
     */
    private Date writeTime;

    /**
     * 班组ID
     */
    private Long workTeamId;
    /**
     * 班组名称
     */
    private String workTeamName;
    /**
     * 持续时长
     */
    private String continuousHour;

    /**
     * 装置名称
     */
    private String unitName;

    /**
     * 生产单元名称
     */
    private String prdtCellName;

    /**
     * 事件类型名称
     */
    private String eventTypeName;

    /**
     * 报警点位号
     */
    private String alarmPointTag;

    /**
     * 报警点位置
     */
    private String location;
    /**
     * 报警标识名称
     */
    private String alarmFlagName;
    /**
     * 优先级名称
     */
    private String priorityName;
    /**
     * 计量单位id
     */
    private String measUnitId;

    /**
     * 计量单位名称
     */
    private String measUnitName;
    /**
     * 报警点位置
     */
    private String alarmPointLocation;
    /**
     * 级别(1A；2B)
     */
    private Integer craftRank;
    /**
     * 级别(1A；2B)
     */
    private String craftRankName;

    /**
     * 工艺卡片上限值是否包含(1是；0否)
     */
    private Integer craftUpLimitInclude;

    /**
     * 工艺卡片下限值是否包含(1是；0否)
     */
    private Integer craftDownLimitInclude;

    /**
     * 工艺卡片上限值
     */
    private Double craftUpLimitValue;

    /**
     * 工艺卡片下限值
     */
    private Double craftDownLimitValue;

    /**
     * 联锁上限值是否包含(1是；0否)
     */
    private Integer interlockUpLimitInclude;

    /**
     * 联锁下限值是否包含(1是；0否)
     */
    private Integer interlockDownLimitInclude;

    /**
     * 联锁上限值
     */
    private Double interlockUpLimitValue;

    /**
     * 联锁下限值
     */
    private Double interlockDownLimitValue;
    /**
     * 工艺卡片值
     */
    private String craftLimitValue;
    /**
     * 报警点高高报
     */
    private Double alarmPointHH;
    /**
     * 报警点高报
     */
    private Double alarmPointHI;
    /**
     * 报警点低报
     */
    private Double alarmPointLO;
    /**
     * 报警点低低报
     */
    private Double alarmPointLL;

    /**
     * 班组简称
     */
    private String workTeamSName;
    /**
     * 联锁值
     */
    private String interlockLimitValue;
    /**
     * 次数
     */
    private Long count;
    /**
     * 报警点说明
     */
    private String alarmPointExplain;

    /**
     * 总报警数
     */
    private Long totalAlarmNum;

    /**
     * 报警平均数
     */
    private String avgAlarmNum;

    /**
     * 监测类型（1物料;2能源;3质量;4:工艺;5:设备:;6:安全;7:其他;）
     */
    private Integer monitorType;

    /**
     * 监测类型Str（1:物料;2:能源;3:质量;4:工艺;5:设备;6:安全;7:其他;）
     */
    private String monitorTypeStr;

    private Integer delStatus;

    private String delStatusShow;

    private Date delApplyDate;

    private String delApplyUser;

}
