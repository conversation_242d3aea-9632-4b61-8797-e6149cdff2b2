package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmMobileConfRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmMobileConf;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AlarmMobileConfRepositoryImpl extends BaseRepository<AlarmMobileConf, Long> implements AlarmMobileConfRepositoryCustom {
    @Override
    public List<AlarmMobileConf> getAlarmMobileConfPointId(Long[] alarmPointId) {
        try {
            StringBuilder hql = new StringBuilder("from AlarmMobileConf amc where amc.alarmPointId in(:alarmPointId)");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("alarmPointId", Arrays.asList(alarmPointId));
            TypedQuery<AlarmMobileConf> query = getEntityManager().createQuery(hql.toString(), AlarmMobileConf.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        }catch (Exception e){
            throw e;
        }
    }


    @Override
    public List<AlarmMobileConf> getAlarmMobileConfPointIdDesc(Long[] alarmPointId) {
        try {
            StringBuilder hql = new StringBuilder("from AlarmMobileConf amc where amc.alarmPointId in(:alarmPointId) and amc.timeInterval is not null order by amc.timeInterval desc");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("alarmPointId", Arrays.asList(alarmPointId));
            TypedQuery<AlarmMobileConf> query = getEntityManager().createQuery(hql.toString(), AlarmMobileConf.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        }catch (Exception e){
            throw e;
        }
    }

    @Override
    public List<AlarmMobileConf> getAlarmMobileConfMobileId(Long[] mobileListId) {
        try {
            String hqlConf = " from AlarmMobileConf f " +
                    "where 1=1 " +
                    "and f.mobileListId in (:mobileListId)";
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("mobileListId", Arrays.asList(mobileListId));
            TypedQuery<AlarmMobileConf> query = getEntityManager().createQuery(hqlConf, AlarmMobileConf.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception e) {
            throw e;
        }
    }


    @Override
    public List findGroupListInfo() {
        try {
            StringBuilder sql = new StringBuilder("select t.alarm_point_id,t.time_interval from t_pm_alarmMobileConf t where t.time_interval is not null group by t.alarm_point_id,t.time_interval");
            Query query = getEntityManager().createNativeQuery(sql.toString());
            return query.getResultList();
        }catch (Exception e){
            throw e;
        }
    }

    @Override
    public List<Long> getConfListByPointId(Long[] alarmPointIds) {
        try {
            StringBuilder sql = new StringBuilder("select t.alarmMobileConfId from AlarmMobileConf t where t.alarmPointId in (:alarmPointIds)");
            Query query = getEntityManager().createQuery(sql.toString());
            query.setParameter("alarmPointIds", Arrays.asList(alarmPointIds));
            return query.getResultList();
        }catch (Exception e){
            throw e;
        }
    }

    @Override
    public List<AlarmMobileConf> getByAlarmPointIdTimeInterval(Long alarmPointId, Long timeInterval){
        try {
            StringBuilder hql = new StringBuilder("from AlarmMobileConf t where t.alarmPointId =(:alarmPointId) and t.timeInterval=(:timeInterval)");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("alarmPointId", alarmPointId);
            paramList.put("timeInterval",timeInterval);
            TypedQuery<AlarmMobileConf> query = getEntityManager().createQuery(hql.toString(), AlarmMobileConf.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        }catch (Exception e){
            throw e;
        }
    }

}
