package com.pcitc.opal.af.bll.entity;

import java.util.List;

/*
 * 报警分析-操作详情数据实体
 * 模块编号：pcitc_opal_bll_class_OperateDetailEntity
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/30
 * 修改编号：1
 * 描    述：报警分析-操作详情数据实体
 */
public class OperateDetailEntity {
    /**
     * 位号
     */
    private String tag;

    /**
     * 报警标识名称
     */
    private String alarmFlagName;

    /**
     * 生产单元简称
     */
    private String prdtCellSname;

    /**
     * 位置
     */
    private String location;

    /**
     * 柱状图中x轴的时间
     */
    private List<String> positionDate;

    /**
     * 操作数
     */
    private List<Long> operatorTimes;

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

    public String getPrdtCellSname() {
        return prdtCellSname;
    }

    public void setPrdtCellSname(String prdtCellSname) {
        this.prdtCellSname = prdtCellSname;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public List<String> getPositionDate() {
        return positionDate;
    }

    public void setPositionDate(List<String> positionDate) {
        this.positionDate = positionDate;
    }

    public List<Long> getOperatorTimes() {
        return operatorTimes;
    }

    public void setOperatorTimes(List<Long> operatorTimes) {
        this.operatorTimes = operatorTimes;
    }

}
