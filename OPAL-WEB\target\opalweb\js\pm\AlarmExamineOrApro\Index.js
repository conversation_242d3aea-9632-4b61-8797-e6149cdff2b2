var searchUrl = OPAL.API.pmUrl + '/AlarmExamineRec/getAlarmExamineRec';
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var alarmFlagListUrl = OPAL.API.adUrl + '/alarmEvents/getAlarmFlagList';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var alarmPriorityListUrl = OPAL.API.afUrl + "/alarmDurationStatt/getAlarmPriorityList";
var getQueryStartAndEndDateUrl = OPAL.API.commUrl + '/getShowTime';
var exportUrl = OPAL.API.pmUrl + '/AlarmExamineRec/exportAlarmExamineRec';//导出
var approvalUrl = OPAL.API.pmUrl + '/AlarmExamineRec/approvalAlarmExamineRec'; //审批通过驳回
var downloadFileUrl = OPAL.API.pmUrl + '/AlarmExamineRec/downloadFile';//文件下载
var queryTimeArray;
var setStartTime, setNowTime;
var isLoading = true;
var pageMode;
var BusinessType = 1;
var BusinessTypeTitle = '';
var AlarmDurationApply  = [
    {value: '1',text: '0.5~1'},
    {value: '2',text: '1~12'},
    {value: '3',text: '12~24'},
    {value: '4',text: '＞＝24小时'},
];
//调整状态
var examineStatusList = [
    {value: '-1',text: '未调整'},
    {value: '0',text: '未提交'},
    {value: '1',text: '已提交'},
    {value: '2',text: '已通过'},
    {value: '3',text: '已驳回'},
];
var tableData = [];
$(function() {
    var page = {
        /**
         * 初始化
         */
        init: function() {
            BusinessType = OPAL.util.getQueryParam('BusinessType');
            if (BusinessType == 1) {
                BusinessTypeTitle = '申请';
            } else {
                BusinessTypeTitle = '审批';
            }
            $(".alarm-public-title-dis").html('报警审查' + BusinessTypeTitle);
            this.bindUi();
            //扩展日期插件
            OPAL.util.extendDate();
            // 初始化 报警时间的时间点
            page.logic.getQueryTime();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化表格
            page.logic.initTable();
            //初始化优先级
            page.logic.initAlarmPriorityList();
            //初始化报警标识
            page.logic.initAlarmFlagList();
            //报警时长
            page.logic.initAlarmDurationList();
            //初始化调整状态
            page.logic.initExamineStatus();
            $('#workTeamIds').html("");
            $("#workTeamIds").prop('disabled', true);

            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("AlarmEvent");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }

            //默认查询数据
            setTimeout(function () {
                if ($("#alarmFlagId").val()!=null&&$("#craftRank").val()!=null&&$("#priority").val()!=null) {
                    page.logic.search();
                }
            }, 500);
        },
        bindUi: function() {
            //选择发生时间
            $('#occurrenceTime').change(function() {
                page.logic.timeChange();
            });
            //导出
            $('#AlarmPointExport').click(function() {
                page.logic.exportExcel();
            });
            //查询
            $('#search').click(function() {
                isLoading = false;
                page.logic.search();
            })
        },
        data: {
            //查询参数
            param: {}
        },
        logic: {
            /**
             * 报警制度管理新增或者编辑详细页面
             */
            detail: function(index) {
                // var row = $('#table').bootstrapTable('getRowByUniqueId', uid);
                var row = tableData[index];
                if (row.alarmExamineRecId == '') {
                    pageMode = PageModelEnum.NewAdd
                } else {
                    pageMode = PageModelEnum.Edit
                }
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: '',
                    area: ['1000px', '400px'],
                    shadeClose: false,
                    content: 'AlarmExamineOrAproAddOrEdit.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "alarmRecId": row.alarmRecId,
                            'row': JSON.stringify(row)
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function() {
                        if(window.isRefresh){
                            $('#table').bootstrapTable('selectPage', 1);
                        }
                    }
                })
            },
            initTable: function() {
                OPAL.ui.initBootstrapTable("table", {
                    uniqueId: 'alarmRecId',
                    columns: [{
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '90px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "序号",
                        formatter: function(value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1) * data.pageSize;
                        },
                        rowspan: 1,
                        field: 'number',
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "调整状态",
                        field: 'examineStatusName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "报警时间",
                        field: 'alarmTime',
                        rowspan: 1,
                        align: 'left',
                        width: '140px'
                    }, {
                        title: "原恢复时间",
                        field: 'oldRecoveryTime',
                        rowspan: 1,
                        align: 'left',
                        width: '140px'
                    }, {
                        title: "调整后恢复时间",
                        field: 'recoveryTime',
                        rowspan: 1,
                        align: 'center',
                        width: '140px'
                    }, {
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'left',
                        width: '130px'
                    }, {
                        title: "生产单元",
                        field: 'prdName',
                        rowspan: 1,
                        align: 'center',
                        width: '130px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'center',
                        width: '130px'
                    }, {
                        title: "参数名称",
                        field: 'location',
                        rowspan: 1,
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "报警等级",
                        field: 'flagName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "优先级",
                        field: 'priorityName',
                        rowspan: 1,
                        align: 'center',
                        width: '60px'
                    }, {
                        title: "报警持续时间(小时)",
                        field: 'alarmDuration',
                        rowspan: 1,
                        align: 'right',
                        width: '140px'
                    }, {
                        field: 'uplAttaName',
                        title: '原因附件',
                        align: 'left',
                        width: '140px',
                        formatter: page.logic.onActionDownload
                    }, {
                        title: "调整原因分析",
                        field: 'reasonAnly',
                        rowspan: 1,
                        align: 'left',
                        width: '130px'
                    }, {
                        title: "提交时间",
                        field: 'submitTime',
                        rowspan: 1,
                        align: 'left',
                        width: '140px'
                    }, {
                        title: "提交人",
                        field: 'submitUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "审批时间",
                        field: 'aproTime',
                        rowspan: 1,
                        align: 'left',
                        width: '140px'
                    }, {
                        title: "审批人",
                        field: 'aproUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }],
                    onLoadSuccess: function(res){
                        tableData = res.rows;
                        $("#search").attr('disabled',false);
                        //设置鼠标浮动提示
                        var tds = $('#table').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                }, page.logic.queryParams,"search")
            },
            //下载
            download: function(uplAttaId,uplAttaName) {
                $('#downExPort').attr('action', downloadFileUrl);
                $('#uplAttaName').val(uplAttaName);
                $('#uplAttaId').val(uplAttaId);
                $('#downExPort').submit();
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function() {
                var rowData = arguments[1];
                if (BusinessType == 1) {
                    if (rowData.examineStatus == '-1' || rowData.examineStatus == 0 || rowData.examineStatus == 3) {
                        return [
                            '<a  name="TableEditor"  href="javascript:window.page.logic.detail(\'' + arguments[2] + '\')">调整</a>'
                        ]
                    }else{
                        return [
                            '<a  name="TableEditor" style="color: #aaa">调整</a>'
                        ]
                    }
                } else if (BusinessType == 2) {
                    if (rowData.examineStatus == 1) {
                        return [
                            '<a  name="TableEditor"  href="javascript:window.page.logic.approval(2,\'' + rowData.alarmExamineRecId + '\')">通过&nbsp;&nbsp;&nbsp;&nbsp;</a>' + 
                            '<a  name="TableEditor"  href="javascript:window.page.logic.approval(3,\'' + rowData.alarmExamineRecId + '\')">驳回</a>'
                        ]
                    }else {
                        return [
                            '<a  name="TableEditor" style="color: #aaa">通过&nbsp;&nbsp;&nbsp;&nbsp;</a>' + 
                            '<a  name="TableEditor" style="color: #aaa">驳回</a>'
                        ]
                    }
                }
            },
            // 通过
            approval: function(type,id) {
                var obj = {
                    status: type,
                    alarmExamineRecId: id
                };
                if (type == 2) {
                    str = '确定审批通过吗？';
                } else {
                    str = '确定驳回吗？';
                }
                layer.confirm(str, {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: approvalUrl,
                        async: false,
                        data: obj,
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'get', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 触发下载
             * @returns {string[]}
             */
            onActionDownload: function() {
                var rowData = arguments[1];
                return [
                    '<a  name="DownLoad"  href="javascript:window.page.logic.download(\'' + rowData.uplAttaId + '\',\'' + rowData.uplAttaName + '\')" >'+rowData.uplAttaName+'</a> '
                ]
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 获得固定的时间点
             */
            getQueryTime: function() {
                OPAL.util.getQueryTime(function(queryTime) {
                    queryTimeArray = queryTime.split(':');
                    // 初始化 开始时间和结束时间
                    page.logic.getQueryStartAndEndDate();
                    // 初始化 时间设置
                    page.logic.initTime();
                });
            },
            // 初始化 开始时间和结束时间
            getQueryStartAndEndDate: function() {
                $.ajax({
                    url: getQueryStartAndEndDateUrl,
                    async: false,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function(result) {
                        var dataArr = $.ET.toObjectArr(result);
                        setStartTime = dataArr[0].value;
                        setNowTime = dataArr[2].value;
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 设置日期插件
             */
            initTime: function() {
                OPAL.ui.initDateTimePeriodPicker({
                    format: 'yyyy-MM-dd HH:mm:ss',
                    type: 'datetime',
                }, function() {
                });
            },
            /**
             * 更改发生时间
             */
            timeChange: function() {
                var _V = $('#occurrenceTime').val();
                $('#startTime').attr('disabled', true);
                $('#endTime').attr('disabled', true);
                switch (_V) {
                    case '1':
                        $('#startTime').val(setStartTime);
                        $('#endTime').val(setNowTime);
                        break;
                    case '2':
                        page.logic.setTime('d', -15);
                        break;
                    case '3':
                        page.logic.setTime('d', -30);
                        break;
                    case '4':
                        page.logic.setTime('d', -60);
                        break;
                    case '5':
                        page.logic.setTime('d', -90);
                        break;
                    case '6':
                        page.logic.setTime('d', -180);
                        break;
                    case '7':
                        page.logic.setTime('y', -1);
                        break;
                    default:
                        $('#startTime').attr('disabled', false);
                        $('#endTime').attr('disabled', false);
                        $('#startTime').val(setStartTime);
                        $('#endTime').val(setNowTime);
                        break;
                }
            },
            /**
             * 设置查询日期
             */
            setTime: function(str, num) {
                var myDate = new Date(setNowTime);
                var currentTime = new Date(myDate.getFullYear(), myDate.getMonth(), myDate.getDate(), queryTimeArray[0], queryTimeArray[1], queryTimeArray[2]);
                laydate.render({
                    elem: '#startTime',
                    format: 'yyyy-MM-dd HH:mm:ss',
                    value: new Date(currentTime.dateAdd(str, num))
                });
                laydate.render({
                    elem: '#endTime',
                    format: 'yyyy-MM-dd HH:mm:ss',
                    value: myDate
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function(nodes) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $("#workTeamIds").prop('disabled', false);
                            $('.textbox,.combo').css('background-color','');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('#workTeamIds').html("");
                            $("#workTeamIds").prop('disabled', true);
                            $('.textbox-disabled').css('background-color','rgb(235, 235, 228)');
                        }
                    }
                }, false, function() {
                    $("#searchPrdt").combotree("checkAllNodes");
                });
            },
            /**
             * 搜索
             */
            search: function() {
                if (!OPAL.util.checkDateIsValid()) {
                    return false;
                }
                $("#search").attr('disabled',true);

                page.data.param = OPAL.form.getData("searchForm",true);
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 初始化查询 报警标识
             */
            initAlarmFlagList: function() {
                OPAL.ui.getCombobox("alarmFlagId", alarmFlagListUrl, {
                    selectValue: '-1',
                    data: {
                        'isAll': true
                    }
                }, null);

                // OPAL.ui.getComboMultipleSelect('alarmFlagId', alarmFlagListUrl, {
                //     data: {
                //         'isAll': true
                //     }
                // }, true, function() {
                //     var treeView = $("#alarmFlagId").combotree('tree');
                //     var nd = treeView.tree('find', -1);
                //     if (nd != null) {
                //         treeView.tree('update', {
                //             target: nd.target,
                //             text: '全选'
                //         });
                //     }
                //     $("#alarmFlagId").combotree("checkAllNodes");
                // });
            },
            /**
             * 初始化查询 优先级
             */
            initAlarmPriorityList: function() {
                OPAL.ui.getCombobox("priority", alarmPriorityListUrl, {
                    selectValue: '-1',
                    data: {
                        'isAll': true
                    }
                }, function (e) {
                    $("#priority option").eq(0).html("全部");
                });
            },
            /**
             * 初始化查询 报警时长
             */
            initAlarmDurationList: function() {
                var str = '';
                var selectValue = '';
                if (BusinessType == 1) {
                    //'申请'默认显示“＞＝24小时”
                    selectValue = '4';
                } else {
                    AlarmDurationApply.unshift({value: '-1',text: '全部'})
                    //'审批'默认显示“全部”
                    selectValue = '-1';
                }
                $.each(AlarmDurationApply, function (i, el) {
                    str += '<option value="' + el.value + '">' + el.text + '</option>';
                });
                $('#alarmDuration').html(str);
                //设置默认值
                $('#alarmDuration').val(selectValue);
            },
            /**
             * 初始化查询 报警时长
             */
            initExamineStatus: function() {
                var str = '';
                var selectValue = '';
                if (BusinessType == 1) {
                    //'申请' 默认显示-1未调整
                    selectValue = '-1';
                } else {
                    examineStatusList.splice(0,2);
                    //'审批' 默认显示1已提交
                    selectValue = '1';
                }
                $.each(examineStatusList, function (i, el) {
                    str += '<option value="' + el.value + '">' + el.text + '</option>';
                });
                $('#examineStatus').html(str);
                //设置默认值
                $('#examineStatus').val(selectValue);
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function() {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },

            /**
             * 设置参数
             */
            setData: function() {
                page.data.param = OPAL.form.getData('searchForm');
            },
            /**
             * 导出
             */
            exportExcel: function() {
                $("#AlarmPointExport").attr("disabled",true);
                var titleArray = new Array();
                var tableTitle = $('#table').bootstrapTable('getOptions').columns[0];
                $.each(tableTitle, function(i, el) {
                    if (i >= 1) {
                        titleArray.push({
                            'key': el.field,
                            'value': el.title
                        })
                    }
                })
                var data = {};
                var pageSize = $('#table').bootstrapTable('getOptions').pageSize;
                var pageNumber = $('#table').bootstrapTable('getOptions').pageNumber;
                data.titles = JSON.stringify(titleArray);
                data.pageSize = pageSize;
                data.pageNumber = pageNumber;
                page.logic.setData();
                $.extend(data, page.data.param);
                $('#formExPort').attr('action', exportUrl);
                $('#titles').val(data.titles);
                $('#pageSize').val(data.pageSize);
                $('#pageNumber').val(data.pageNumber);
                $('#unitIds1').val(data.unitIds);
                $('#prdtCellIds1').val(data.prdtCellIds);
                $('#tag1').val(data.tag);
                $('#alarmFlagId1').val(data.alarmFlagId);
                $('#priority1').val(data.priority);
                $('#examineStatus1').val(data.examineStatus);
                $('#alarmDuration1').val(data.alarmDuration);
                $('#startTime1').val(data.startTime);
                $('#endTime1').val(data.endTime);
                var options = {
                    success: function() {
                        $("#AlarmPointExport").attr("disabled",false);
                    },
                    error:function () {

                    }
                };

                $("#formExPort").ajaxSubmit(options);
                $('#formExPort').submit();
            }

        }
    };
    page.init();
    window.page = page;
});