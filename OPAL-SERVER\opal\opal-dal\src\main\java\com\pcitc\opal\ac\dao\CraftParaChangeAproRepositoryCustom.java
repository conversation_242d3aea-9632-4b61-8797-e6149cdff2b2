package com.pcitc.opal.ac.dao;

import com.pcitc.opal.ac.pojo.CraftParaChangeApro;
import com.pcitc.opal.common.CommonResult;

import java.util.List;

/*
 * 工艺参数变更审批记录实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_CraftParaChangeAproRepositoryCustom
 * 作       者：zheng.yang
 * 创建时间：2019/4/15
 * 修改编号：1
 * 描       述：工艺参数变更审批记录实体的Repository的JPA自定义接口
 */
public interface CraftParaChangeAproRepositoryCustom {

    /**
     * 新增编辑工艺参数变更审批记录
     *
     * @param craftParaChangeAproList 报警变更事项数据
     * @return 返回结果信息类
     * <AUTHOR> 2019-04-15
     */
    CommonResult addCraftParaChangeApro(List<CraftParaChangeApro> craftParaChangeAproList);

    /**
     * 删除工艺参数变更审批记录
     *
     * @param craftAproIds 工艺参数变更审批记录ID
     * @return 返回结果信息类
     * <AUTHOR> 2019-04-15
     */
    CommonResult deleteCraftParaChangeApro(Long[] craftAproIds);

    /**
     * 删除“报警变更方案明细ID”对应的<工艺参数变更审批记录>
     *
     * @param planDetailId 报警变更明细Id
     * @return 返回结果信息类
     * <AUTHOR> 2019-04-15
     */
    CommonResult deleteCraftParaChangeAproByPDId(Long... planDetailId);

    /**
     * 根据PlanDetailId获取工艺参数变更审批记录
     *
     * @param planDetailIds 报警变更方案明细ID
     * @return AlarmChangePlanDetail 报警变更事项集合
     * <AUTHOR> 2019-04-15
     */
    List<CraftParaChangeApro> getAlarmChangeItemByPlanDetail(Long... planDetailIds);


}
