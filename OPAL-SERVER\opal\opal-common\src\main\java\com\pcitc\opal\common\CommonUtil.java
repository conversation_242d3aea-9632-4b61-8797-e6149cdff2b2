package com.pcitc.opal.common;

import org.springframework.beans.BeanUtils;

import com.pcitc.opal.common.pojo.BasicInfo;

import java.rmi.RemoteException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

/*
 * 公共转换实现类
 * 模块编号：pcitc_opal_common_class_CommonUtil
 * 作       者：dongsheng.zhao
 * 创建时间：2017/09/17
 * 修改编号：1
 * 描       述：公共转换实现类
 */
public class CommonUtil {

	static String[] ignoreList = { "crtUserId", "crtDate", "crtUserName", "mntUserId", "mntDate", "mntUserName" };

	/**
	 * 两个实体相互赋值使用(创建人、创建时间等字段不进行拷贝)
	 * 
	 * @param sourceObj
	 *            源实体
	 * @param targetObj
	 *            目标实体
	 * @throws Exception
	 */
	public static <S, T> void objectExchange(S sourceObj, T targetObj) throws Exception {
		BeanUtils.copyProperties(sourceObj, targetObj, ignoreList);
	}
	
	/**
	 * 统一处理创建人、修改人的信息、时间
	 * 
	 * @param t
	 * 			泛型
	 * @param type
	 * 			新增还是修改（1：新增；2：修改）
	 * @return
	 */
    public static <T extends BasicInfo> T returnValue(T t, int type) throws RemoteException {
    	CommonProperty commonProperty = new CommonProperty(); 
        t.setMntUserId(commonProperty.getUserId());
        t.setMntUserName(commonProperty.getUserName());
        t.setMntDate(commonProperty.getSystemDateTime());
        if (type == 1)
        {
            t.setCrtUserId(commonProperty.getUserId());
            t.setCrtUserName (commonProperty.getUserName());
            t.setCrtDate(commonProperty.getSystemDateTime()); 
        }
        return t;
    }
	/**
	 * List<Entity> Distinct
	 *
	 * <AUTHOR> 2018-9-3
	 * @param keyExtractor
	 * @param <T>
	 * @return
	 */
	public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor)
	{
		Map<Object, Boolean> map = new ConcurrentHashMap<>();
		return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
	}
}
