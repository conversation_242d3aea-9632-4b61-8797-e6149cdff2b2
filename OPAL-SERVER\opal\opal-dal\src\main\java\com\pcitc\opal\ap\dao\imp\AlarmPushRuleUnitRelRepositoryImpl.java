package com.pcitc.opal.ap.dao.imp;

import com.pcitc.opal.ap.dao.AlarmPushRuleRepositoryCustom;
import com.pcitc.opal.ap.dao.AlarmPushRuleUnitRelRepositoryCustom;
import com.pcitc.opal.ap.pojo.AlarmPushRule;
import com.pcitc.opal.ap.pojo.AlarmPushRuleUnitRel;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/*
 * 报警知识管理实体的Repository的JPA接口实现
 * 模块编号： pcitc_opal_dal_class_AlarmKnowlgManagmtRepositoryImpl
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理实体的Repository的JPA接口实现
 */
public class AlarmPushRuleUnitRelRepositoryImpl extends BaseRepository<AlarmPushRuleUnitRel, Long>
		implements AlarmPushRuleUnitRelRepositoryCustom {


	@Override
	public PaginationBean<AlarmPushRuleUnitRelEntityVO> getAlarmPushRuleUnitRelPage(String name, Integer companyId, Integer pushType,Integer speciality,Long priority, Pagination page) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("select new com.pcitc.opal.ap.dao.imp.AlarmPushRuleUnitRelEntityVO( " +
					"t.apRuleUnitRelId,r.alarmPushRuleId,r.name,t.priority,t.alarmSpeciality,t.inUse,t.des,t.mntDate,t.mntUserName) " +
					"from AlarmPushRuleUnitRel t " +
					"left join t.alarmPushRule r " +
					" where 1=1 and t.inUse=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			//
			if (null != companyId){
				hql.append(" and t.companyId = :companyId ");
				paramList.put("companyId",companyId);
			}
			if (null != pushType){
				hql.append(" and r.pushType = :pushType ");
				paramList.put("pushType",pushType);
				if (pushType==1){
					if (null !=priority&&priority!=-1) {
						hql.append(" and t.priority = :priority ");
						paramList.put("priority", priority);
					}
				}
			}
			// 名称
			if (!StringUtils.isEmpty(name)) {
				hql.append(" and (r.name like :name escape '/') ");
				paramList.put("name", "%" + this.sqlLikeReplace(name) + "%");
			}
			hql.append(" order by t.mntDate,r.name desc");

//			Query query = getEntityManager().createQuery(hql.toString());
//			this.setParameterList(query, paramList);
//			Long count =Long.valueOf(query.getResultList().size());

			Long count =Long.valueOf(this.findCusCount(hql.toString(),paramList));
			BaseRepository<AlarmPushRuleUnitRelEntityVO, Long> br =new BaseRepository();
			return br.findCusTomAll(this.getEntityManager(),page,count, hql.toString(), paramList,AlarmPushRuleUnitRelEntityVO.class);
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addAlarmPushRuleUnitRel(AlarmPushRuleUnitRel alarmPushRuleUnitRel) {
		// 初始化消息结果类
		CommonProperty commonProperty = new CommonProperty();
		alarmPushRuleUnitRel.setCompanyId(commonProperty.getCompanyId());
		CommonResult commonResult = new CommonResult();
		try {
			this.getEntityManager().persist(alarmPushRuleUnitRel);
			commonResult.setResult(alarmPushRuleUnitRel);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		return commonResult;
	}

	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteAlarmPushRuleUnitRel(Long[] ids) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = " from AlarmPushRuleUnitRel t " +
					"where t.companyId=:companyId " +
					"and t.apRuleUnitRelId in (:ids)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("ids", Arrays.asList(ids));
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			TypedQuery<AlarmPushRuleUnitRel> query = getEntityManager().createQuery(hql, AlarmPushRuleUnitRel.class);
			this.setParameterList(query, paramList);
			List<AlarmPushRuleUnitRel> alarmPointList = query.getResultList();
			alarmPointList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult updateAlarmPushRuleUnitRel(AlarmPushRuleUnitRel alarmPushRuleUnitRel) {
		// 初始化消息结果类
		CommonProperty commonProperty = new CommonProperty();
		alarmPushRuleUnitRel.setCompanyId(commonProperty.getCompanyId());
		CommonResult commonResult = new CommonResult();
		try {
			getEntityManager().merge(alarmPushRuleUnitRel);
			commonResult.setResult(alarmPushRuleUnitRel);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("更新成功！");
		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	@Override
	public List<String> findAllNameRule() {
		return null;
	}

	@Override
	public List<RelAndDetailEntity> getAlarmPushRuleUnitRelById(Long apRuleUnitRelId) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("select new com.pcitc.opal.ap.dao.imp.RelAndDetailEntity( " +
					"t.apRuleUnitRelId,r.name,t.priority,t.alarmSpeciality,t.des) " +
					"from AlarmPushRuleUnitRel t " +
					"left join t.alarmPushRule r " +
					" where 1=1 and t.inUse=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			//
			if (null != apRuleUnitRelId){
				hql.append(" and t.apRuleUnitRelId = :apRuleUnitRelId ");
				paramList.put("apRuleUnitRelId",apRuleUnitRelId);
			}

			Query query = getEntityManager().createQuery(hql.toString());
			this.setParameterList(query, paramList);
			List<RelAndDetailEntity> list =query.getResultList();
			return list;
			} catch (Exception ex) {
			throw ex;
		}
	}
}
