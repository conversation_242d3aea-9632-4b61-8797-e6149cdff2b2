package com.pcitc.opal.ad.dao;

import com.pcitc.opal.ad.pojo.AlarmEventCache;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * AlarmEventCache实体的Repository的JPA标准接口
 * 模块编号：pcitc_opal_dal_interface_AlarmEventCacheRepository
 * 作       者：zheng.yang
 * 创建时间：2019/03/14
 * 修改编号：1
 * 描       述：AlarmEventCache实体的Repository实现
 */
public interface AlarmEventCacheRepository extends JpaRepository<AlarmEventCache,Long>,AlarmEventCacheRepositoryCustom {
}
