package com.pcitc.opal.common.bll.imp;

import com.alibaba.fastjson.JSONArray;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.HttpUtil;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import com.pcitc.opal.pm.pojo.Unit;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.shiftservice.data.IShiftDataServiceProxy;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.shiftservice.data.entity.ShiftWorkTeamDataEntity;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.shiftservice.data.entity.WorkTeamDicEntity;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.shiftservice.data.entity.WorkTeamDictionary;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.shiftservice.dic.IShiftDictionaryServiceProxy;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.shiftservice.dic.entity.ShiftAreaDicEntity;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.shiftservice.dic.entity.ShiftAreaDictionary;
import com.pcitc.opal.webservice.common.AppPropertiesReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/*
 * AAA班组服务接口实现
 * 模块编号：pcitc_opal_bll_interface_ShiftServiceImpl
 * 作       者：xuelei.wang
 * 创建时间：2017-11-28
 * 修改编号：1
 * 描       述：AAA班组服务接口实现
 */
@Service
@ConditionalOnProperty(name ="runtime_type",havingValue = "other")
@Slf4j
public class AAAShiftServiceImpl implements ShiftService {

    @Autowired
    private EntityManager entityManager;

    @Resource
    private RedisTemplate<String, List<ShiftWorkTeamDataEntity>> redisTemplate;

    @Value("${cache.timeout:5}")
    private Integer cacheTimeout;
    /**
     * 获取班组列表
     *
     * @param unitCode    装置编码
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 班组列表信息
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    @Override
    public List<ShiftWorkTeamEntity> getShiftWorkTeamList(String unitCode, Date startTime, Date endTime) throws Exception {
        if (unitCode == null) return new ArrayList();
        try {
            Long shiftAreaId = getShiftAreaIdByunitCode(unitCode).get(unitCode);

            //2.获取班组
            List<ShiftWorkTeamEntity> workTeamList = new ArrayList<>();
            try {
                List<ShiftWorkTeamDataEntity> resultList = getShiftWorkTeam(shiftAreaId, startTime, endTime);
                for (ShiftWorkTeamDataEntity teamEntity : resultList) {
                    ShiftWorkTeamEntity entity = new ShiftWorkTeamEntity();
                    entity.setWorkTeamName(teamEntity.getWorkTeamName());
                    entity.setWorkTeamSortNum(teamEntity.getWorkTeamSortNum().longValue());
                    entity.setWorkTeamSName(teamEntity.getWorkTeamShortName());
                    entity.setWorkTeamId(teamEntity.getWorkTeamId().longValue());
                    workTeamList.add(entity);
                }
            } catch (Exception ex) {
                throw ex;
            }
            //3.班组名称去重
            Map<String, List<ShiftWorkTeamEntity>> dicList = workTeamList.stream()
                    .collect(Collectors.groupingBy(ShiftWorkTeamEntity::getWorkTeamSName, Collectors.collectingAndThen(Collectors.toCollection(ArrayList::new), item -> item)));

            //4.班组合并
            List mapKeys = new ArrayList(dicList.keySet());
            List<ShiftWorkTeamEntity> workTeamEntityList = new ArrayList<>();
            for (int i = 0; i < mapKeys.size(); i++) {
                ShiftWorkTeamEntity entity = dicList.get(mapKeys.get(i)).stream().findFirst().orElse(new ShiftWorkTeamEntity());
                entity.setWorkTeamIdList(dicList.get(mapKeys.get(i)).stream().map(item -> item.getWorkTeamId()).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList()));
                workTeamEntityList.add(entity);
            }
            try {
                workTeamEntityList = workTeamEntityList.stream().sorted(Comparator.comparing(ShiftWorkTeamEntity::getWorkTeamSortNum, Comparator.naturalOrder())).collect(Collectors.toList());
            } catch (Exception ex) {

            }
            if (workTeamEntityList.size() > 1) {
                ShiftWorkTeamEntity entity = new ShiftWorkTeamEntity();
                entity.setWorkTeamId(-1L);
                entity.setWorkTeamSName("全部");
                workTeamEntityList.add(0, entity);
            }
            return workTeamEntityList;
        } catch (Exception ex) {

        }
        return new ArrayList<>();
    }

    /**
     * 获取班组列表
     *
     * @param unitCodeList 轮班域ID集合
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 班组列表信息
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    @Override
    public List<ShiftWorkTeamEntity> getShiftWorkTeamList(List<String> unitCodeList, Date startTime, Date endTime) throws Exception {
        List<ShiftWorkTeamEntity> teamList = new ArrayList<>();
        List<Long> shiftAreaIdList = new ArrayList<>();
        shiftAreaIdList = this.getShiftAreaIdByunitCode(unitCodeList==null?null:unitCodeList.stream().toArray(String[]::new)).values().stream().distinct().collect(Collectors.toList());
        for (Long shiftAreaId : shiftAreaIdList) {
            List<ShiftWorkTeamDataEntity> dataList = getShiftWorkTeam(shiftAreaId, startTime, endTime);
            for (ShiftWorkTeamDataEntity shiftEntity : dataList) {
                ShiftWorkTeamEntity entity = new ShiftWorkTeamEntity();
                entity.setStartTime(strToDate(shiftEntity.getStartTime()));
                entity.setEndTime(strToDate(shiftEntity.getEndTime()));
                entity.setWorkTeamId(shiftEntity.getWorkTeamId().longValue());
                entity.setShiftName(shiftEntity.getShiftName());
                entity.setShiftSName(shiftEntity.getShiftShortName());
                entity.setWorkTeamSName(shiftEntity.getWorkTeamShortName());
                entity.setWorkTeamName(shiftEntity.getWorkTeamName());
                entity.setShiftId(shiftEntity.getShiftId().longValue());
                entity.setShiftSortNum(shiftEntity.getShiftSortNum().longValue());
                teamList.add(entity);
            }
        }
        return teamList;
    }

    /**
     * 根据装置编码获取轮班域ID
     *
     * @param unitCodes 装置编码集合
     * @return Map<装置编码，轮班域ID>
     * <AUTHOR> 2017-12-12
     */
    private Map<String, Long> getShiftAreaIdByunitCode(String... unitCodes) throws Exception {
        StringBuilder hqlBuilder = new StringBuilder();
        Map<String, Object> paramList = new HashedMap();
        Map<String, Long> result = new LinkedHashMap<>();
        hqlBuilder.append("select u from Unit u where 1=1 and u.companyId=:companyId");
        if (ArrayUtils.isNotEmpty(unitCodes)) {
            hqlBuilder.append(" and u.stdCode in (:unitCodes) ");
            paramList.put("unitCodes", Arrays.asList(unitCodes));
        }
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId",commonProperty.getCompanyId());
        Query query = entityManager.createQuery(hqlBuilder.toString(), Unit.class);
        paramList.entrySet().forEach(pair -> query.setParameter(pair.getKey(), pair.getValue()));
        List<Unit> unitList = query.getResultList();
        unitList.forEach(u -> result.put(u.getStdCode(), u.getShiftAreaId()));
        return result;
    }

    /**
     * 班组服务日期转换
     *
     * @param dateStr
     * @return
     * <AUTHOR> 2017-12-14
     */
    public Date strToDate(String dateStr) {
        try {
            String str = StringUtils.replace(StringUtils.replace(dateStr, "/Date(", ""), ")/", "");
            if (NumberUtils.isNumber(str)) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTimeInMillis(NumberUtils.toLong(str));
                return calendar.getTime();
            } else {
                return DateUtils.parseDate("1900-00-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new Date();
    }

    /**
     * 根据班组名称集合及开始结束时间获取排班时间段列表
     *
     * @param unitCode         装置编码
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param workTeamIdList 班组Id集合
     * @return 排班时间段列表
     * @throws Exception
     * <AUTHOR> 2017-11-26
     */
    @Override
    public List<ShiftWorkTeamEntity> getShiftList(String unitCode, Date startTime, Date endTime, List<Long> workTeamIdList) throws Exception {
        if (unitCode == null) return new ArrayList();
        List<ShiftWorkTeamEntity> list = new ArrayList();
        Long shiftAreaId = getShiftAreaIdByunitCode(unitCode).get(unitCode);
        try {
            List<ShiftWorkTeamDataEntity> resultList = getWorkShiftList(shiftAreaId, startTime, endTime, null, null);
            for (ShiftWorkTeamDataEntity shiftEntity : resultList) {
                ShiftWorkTeamEntity entity = new ShiftWorkTeamEntity();
                entity.setStartTime(strToDate(shiftEntity.getStartTime()));
                entity.setEndTime(strToDate(shiftEntity.getEndTime()));
                entity.setWorkTeamId(shiftEntity.getWorkTeamId().longValue());
                entity.setShiftName(shiftEntity.getShiftName());
                entity.setShiftSName(shiftEntity.getShiftShortName());
                entity.setWorkTeamSName(shiftEntity.getWorkTeamShortName());
                entity.setWorkTeamName(shiftEntity.getWorkTeamName());
                entity.setShiftId(shiftEntity.getShiftId().longValue());
                entity.setShiftSortNum(shiftEntity.getShiftSortNum().longValue());
                entity.setShiftDate(strToDate(shiftEntity.getShiftDate()));
                list.add(entity);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return list.stream().filter(item -> workTeamIdList.contains(item.getWorkTeamId())).collect(Collectors.toList());
    }

    /**
     * 获取指定班组的生产排班信息
     *
     * @param unitCode     装置编码
     * @param workTeamId 班组ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    @Override
    public List<ShiftWorkTeamEntity> getShiftList(String unitCode, Long workTeamId, Date startTime, Date endTime) throws Exception {
        if (unitCode == null) return new ArrayList();
        List<ShiftWorkTeamEntity> list = new ArrayList();
        Long shiftAreaId = getShiftAreaIdByunitCode(unitCode).get(unitCode);
        try {
            List<ShiftWorkTeamDataEntity> resultList = getWorkShiftList(shiftAreaId, startTime, endTime, null, workTeamId);
            for (ShiftWorkTeamDataEntity shiftEntity : resultList) {
                ShiftWorkTeamEntity entity = new ShiftWorkTeamEntity();
                entity.setStartTime(strToDate(shiftEntity.getStartTime()));
                entity.setEndTime(strToDate(shiftEntity.getEndTime()));
                entity.setWorkTeamId(shiftEntity.getWorkTeamId().longValue());
                entity.setShiftName(shiftEntity.getShiftName());
                entity.setShiftSName(shiftEntity.getShiftShortName());
                entity.setWorkTeamSName(shiftEntity.getWorkTeamShortName());
                entity.setWorkTeamName(shiftEntity.getWorkTeamName());
                entity.setShiftId(shiftEntity.getShiftId().longValue());
                entity.setShiftSortNum(shiftEntity.getShiftSortNum().longValue());
                list.add(entity);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return list;
    }

    /**
     * 根据轮班域ID或编码集合获取轮班域列表
     *
     * @param ids 轮班域ID或编码集合
     * @return 轮班域集合
     * <AUTHOR> 2017-12-12
     */
    @Override
    public List<ShiftWorkTeamEntity> getShiftAreaList(List<String> ids) throws Exception {
        //2.获取班组
        List<ShiftWorkTeamEntity> workTeamList = new ArrayList<>();
        try {
            List<ShiftAreaDicEntity> resultList = getAllShiftAreaList(ids==null?null: Arrays.asList((Long[]) ConvertUtils.convert(ids, Long[].class)));
            for (ShiftAreaDicEntity teamEntity : resultList) {
                ShiftWorkTeamEntity entity = new ShiftWorkTeamEntity();
                entity.setShiftAreaId(teamEntity.getShiftAreaId().longValue());
                entity.setShiftAreaName(teamEntity.getName());
                workTeamList.add(entity);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return workTeamList;
    }
    /***
     * 获取排班班组列表
     *
     * @param shiftAreaId 轮班域ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 排班信息列表
     */
    private List<ShiftWorkTeamDataEntity> getShiftWorkTeam(Long shiftAreaId, Date startTime, Date endTime) throws Exception {
        String key = "work:team:" + (shiftAreaId.hashCode() + startTime.hashCode() + endTime.hashCode());

        List<ShiftWorkTeamDataEntity> value = redisTemplate.opsForValue().get(key);

        if (CollectionUtils.isNotEmpty(value)) {
            log.info("缓存获取班组");
            return value;
        }
        List<ShiftWorkTeamDataEntity> resultList = new ArrayList<>();
        try {
            List<NameValuePair> list = new ArrayList<>();
            list.add(new BasicNameValuePair("shiftAreaId", String.valueOf(shiftAreaId.longValue())));
            list.add(new BasicNameValuePair("periodType", "2"));
            list.add(new BasicNameValuePair("startTime", DateFormatUtils.format(startTime, "yyyy-MM-dd HH:mm:ss")));
            list.add(new BasicNameValuePair("endTime", DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss")));
            list.add(new BasicNameValuePair("pageIndex", "0"));
            list.add(new BasicNameValuePair("pageSize", "10"));
            String str = postRequest(AppPropertiesReader.getValue("aaa.shiftcalendarsvc_address_base") + "/IP/ShiftService/Shift_UI/ST/ShiftTest/GetShiftData", list);
            resultList = JSONArray.parseArray(str, ShiftWorkTeamDataEntity.class);
            redisTemplate.boundValueOps(key).set(resultList);
            redisTemplate.expire(key, cacheTimeout, TimeUnit.MINUTES);
        } catch (Exception ex) {
            System.out.println("未查询到数据!");
        }
        return resultList;
    }

    /**
     * Http发送请求
     *
     * @param url
     * @param nvps
     * @return
     * <AUTHOR> 2017-12-14
     */
    private String postRequest(String url, List<NameValuePair> nvps) {
        CloseableHttpResponse response = null;
        try {
            int index = url.lastIndexOf(":");
            String srr = url.substring(0, index);
            CloseableHttpClient httpclient = HttpUtil.wrapClient();
            if ("https".equals(srr)) {
                httpclient = HttpUtil.wrapSslClient();
            }
            HttpPost httpPost = new HttpPost(url);
            httpPost.setEntity(new UrlEncodedFormEntity(nvps));
            response = httpclient.execute(httpPost);
            InputStream responseStream = response.getEntity().getContent();
            String result = StreamUtils.copyToString(responseStream, Charset.forName("utf-8"));
            return result;
        } catch (Exception ex) {
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                System.out.println("班组服务调用出现了异常!");
            }
        }
        return "'";
    }

    /***
     * 根据轮班域ID获取所有班组列表
     *
     * @param shiftAreaId 轮班域ID
     * @return
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    private List<WorkTeamDicEntity> getAllWorkTeam(Long shiftAreaId) throws Exception {
        List<WorkTeamDicEntity> resultList = new ArrayList<>();
        try {
            IShiftDataServiceProxy proxy = new IShiftDataServiceProxy();
            Calendar startCalendar = Calendar.getInstance();
            startCalendar.setTime(DateUtils.parseDate("1900-12-01 12:00:00", "yyyy-MM-dd HH:mm:ss"));
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(DateUtils.parseDate("2999-12-01 12:00:00", "yyyy-MM-dd HH:mm:ss"));
            BigDecimal shitAreaIdBig = null;
            if (shiftAreaId != null) {
                shitAreaIdBig = new BigDecimal(shiftAreaId);
            }
            WorkTeamDictionary dicEntity = proxy.getWorkTeamByDateTimeSpan(shitAreaIdBig, startCalendar, endCalendar, null);
            if (dicEntity != null) {
                WorkTeamDicEntity[] workTeamArray = dicEntity.getWorkTeamDicList();
                if (workTeamArray != null)
                    resultList = Arrays.asList(workTeamArray);
            }
        } catch (Exception ex) {
            System.out.println("班组服务调用出现了异常!");
        }
        return resultList;
    }

    /***
     * 获取排班信息列表
     *
     * @param shiftAreaId 轮班域ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param shiftId     班次ID(可为空)
     * @param workTeamId  班组ID(可为空)
     * @return 排班信息列表
     */
    private List<ShiftWorkTeamDataEntity> getWorkShiftList(Long shiftAreaId, Date startTime, Date endTime, Long shiftId, Long workTeamId) throws Exception {
        String key = "work:team:" + (shiftAreaId.hashCode() + startTime.hashCode() + endTime.hashCode() + (shiftId == null ? 0 : shiftId));

        List<ShiftWorkTeamDataEntity> value = redisTemplate.opsForValue().get(key);

        if (CollectionUtils.isNotEmpty(value)) {
            log.info("缓存获取班组");
            return value;
        }

        List<ShiftWorkTeamDataEntity> resultList = new ArrayList<>();
        try {
            List<NameValuePair> list = new ArrayList<>();
            list.add(new BasicNameValuePair("shiftAreaId", String.valueOf(shiftAreaId.longValue())));
            list.add(new BasicNameValuePair("periodType", "2"));
            list.add(new BasicNameValuePair("startTime", DateFormatUtils.format(startTime, "yyyy-MM-dd HH:mm:ss")));
            list.add(new BasicNameValuePair("endTime", DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss")));
            list.add(new BasicNameValuePair("pageIndex", "0"));
            list.add(new BasicNameValuePair("pageSize", "10"));
            String str = postRequest(AppPropertiesReader.getValue("aaa.shiftcalendarsvc_address_base") + "/IP/ShiftService/Shift_UI/ST/ShiftTest/GetShiftData", list);
            resultList = JSONArray.parseArray(str, ShiftWorkTeamDataEntity.class);
            redisTemplate.boundValueOps(key).set(resultList);
            redisTemplate.expire(key, cacheTimeout, TimeUnit.MINUTES);
        } catch (Exception ex) {
            System.out.println("未查询到数据!");
        }
        return resultList;
    }
    /***
     * 根据轮班域ID获取所有轮班域列表
     *
     * @param shiftAreaIdList 轮班域ID集合
     * @return
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    private List<ShiftAreaDicEntity> getAllShiftAreaList(List<Long> shiftAreaIdList) throws Exception {
        List<ShiftAreaDicEntity> resultList = new ArrayList<>();
        if (shiftAreaIdList != null) {
            shiftAreaIdList = shiftAreaIdList.stream().distinct().collect(Collectors.toList());
        }
        try {
            IShiftDictionaryServiceProxy proxy = new IShiftDictionaryServiceProxy();
            ShiftAreaDictionary dicEntity = null;
            if (shiftAreaIdList == null) {
                dicEntity = proxy.getIShiftDictionaryService().queryShiftAreaList(null);
            } else {
                BigDecimal[] shitAreaIdArr = new BigDecimal[shiftAreaIdList.size()];
                for (int i = 0; i < shiftAreaIdList.size(); i++) {
                    shitAreaIdArr[i] = new BigDecimal(shiftAreaIdList.get(i));
                }
                dicEntity = proxy.getIShiftDictionaryService().queryShiftAreaList(shitAreaIdArr);
            }
            if (dicEntity != null) {
                ShiftAreaDicEntity[] workTeamArray = dicEntity.getShiftAreaDicList();
                if (workTeamArray != null)
                    resultList.addAll(Arrays.asList(workTeamArray));
            }
        } catch (Exception ex) {
            System.out.println("班组服务调用出现了异常!");
        }
        return resultList;
    }
}
