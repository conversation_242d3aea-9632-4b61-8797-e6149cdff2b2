package com.pcitc.opal.pm.dao;

import java.util.List;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.Factory;

/*
 * Factory实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_FactoryRepositoryCustom
 * 作       者：kun.zhao
 * 创建时间：2017/12/11
 * 修改编号：1
 * 描       述：Factory实体的Repository的JPA自定义接口 
 */
public interface FactoryRepositoryCustom {
	
	/**
	 * 校验
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryPO 工厂实体
	 * @return 返回结果信息类
	 */
	CommonResult factoryValidation(Factory factoryPO);
	
	/**
     * 新增工厂
     * 
     * <AUTHOR> 2017-12-11
     * @param factoryPO 工厂实体
     * @return 返回结果信息类
     */
	CommonResult addFactory(Factory factoryPO);
	
	/**
     * 删除工厂
     *
     * <AUTHOR> 2017-12-11
     * @param factoryIds 工厂ID集合
     * @return 返回结果信息类
     */
	CommonResult deleteFactory(Long[] factoryIds);
	
	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryPO 工厂实体
	 * @return 返回结果信息类
	 */
	CommonResult updateFactory(Factory factoryPO);
	
	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryId 工厂ID
	 * @return 工厂实体
	 */
	Factory getSingleFactory(Long factoryId);
	
	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryIds 工厂ID数组
	 * @return 工厂实体集合
	 */
	List<Factory> getFactory(Long[] factoryIds);
	
	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param name    工厂名称/简称
	 * @param stdCode 标准编码
	 * @param inUse   是否启用
	 * @param page    分页参数
	 * @return 工厂实体（分页）
	 */
	PaginationBean<Factory> getFactory(Long companyId, String name, String stdCode, Integer inUse, Pagination page);

	/**
	 * 根据企业id查询工厂
	 * @param companyId 企业id
	 * @return list
	 */
	List<Factory> getFactoryByCompanyId(Long companyId);
	
}
