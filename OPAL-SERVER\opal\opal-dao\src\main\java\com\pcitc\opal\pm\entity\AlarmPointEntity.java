package com.pcitc.opal.pm.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 报警点
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_pm_alarmpoint")
public class AlarmPointEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "alarm_point_id", type = IdType.AUTO)
    private Long alarmPointId;

    /**
     * 生产单元id
     */
    @TableField("prdtcell_id")
    private Long prdtcellId;

    /**
     * 位号
     */
    @TableField("tag")
    private String tag;

    /**
     * 位置
     */
    @TableField("location")
    private String location;

    /**
     * PID图号
     */
    @TableField("pid_code")
    private String pidCode;

    /**
     * 报警点类型id
     */
    @TableField("alarm_point_type_id")
    private Long alarmPointTypeId;

    @TableField("monitor_type")
    private Long monitorType;

    @TableField("measunit_id")
    private Long measunitId;

    @TableField("instrmt_type")
    private Long instrmtType;

    @TableField("virtual_reality_flag")
    private Long virtualRealityFlag;

    @TableField("alarm_point_hh")
    private BigDecimal alarmPointHh;

    @TableField("alarm_point_hi")
    private BigDecimal alarmPointHi;

    @TableField("alarm_point_lo")
    private BigDecimal alarmPointLo;

    @TableField("alarm_point_ll")
    private BigDecimal alarmPointLl;

    @TableField("in_use")
    private Long inUse;

    /**
     * 创建时间
     */
    @TableField("crt_date")
    private Date crtDate;

    /**
     * 维护时间
     */
    @TableField("mnt_date")
    private Date mntDate;

    /**
     * 创建人ID
     */
    @TableField("crt_user_id")
    private String crtUserId;

    /**
     * 最后维护人ID
     */
    @TableField("mnt_user_id")
    private String mntUserId;

    /**
     * 创建人名称
     */
    @TableField("crt_user_name")
    private String crtUserName;

    /**
     * 最后维护人名称
     */
    @TableField("mnt_user_name")
    private String mntUserName;

    @TableField("sort_num")
    private Long sortNum;

    /**
     * 描述
     */
    @TableField("des")
    private String des;

    @TableField("craft_up_limit_include")
    private Long craftUpLimitInclude;

    @TableField("craft_down_limit_include")
    private Long craftDownLimitInclude;

    @TableField("craft_up_limit_value")
    private BigDecimal craftUpLimitValue;

    @TableField("craft_down_limit_value")
    private BigDecimal craftDownLimitValue;

    @TableField("interlock_up_limit_include")
    private Long interlockUpLimitInclude;

    @TableField("interlock_down_limit_include")
    private Long interlockDownLimitInclude;

    @TableField("interlock_up_limit_value")
    private BigDecimal interlockUpLimitValue;

    @TableField("interlock_down_limit_value")
    private BigDecimal interlockDownLimitValue;

    @TableField("craft_rank")
    private Long craftRank;

    @TableField("virtual_flag")
    private Long virtualFlag;

    @TableField("instrmt_priority")
    private Long instrmtPriority;

    @TableField("in_sendmsg")
    private Long inSendmsg;

    /**
     * 手机号（多个手机号用英文逗号隔开）
     */
    @TableField("mobile_phone")
    private String mobilePhone;

    /**
     * 实时数据库位号
     */
    @TableField("rtdb_tag")
    private String rtdbTag;

    /**
     * 企业ID
     */
    @TableField("company_id")
    private Long companyId;


    @TableField("is_timeout")
    private Integer isTimeout;



}
