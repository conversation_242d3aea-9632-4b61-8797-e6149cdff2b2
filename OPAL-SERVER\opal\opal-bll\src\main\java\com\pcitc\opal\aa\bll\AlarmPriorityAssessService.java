package com.pcitc.opal.aa.bll;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Service;

import com.pcitc.opal.aa.bll.entity.AlarmPriorityAssessEntity;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

/*
 * 优先级评估逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_AlarmPriorityAssessService
 * 作       者：kun.zhao
 * 创建时间：2017/10/17
 * 修改编号：1
 * 描       述：优先级评估逻辑层接口 
 */
@Service
public interface AlarmPriorityAssessService {

	/**
	 * 获取主列表数据
	 *
	 * @param unitCodes     装置ID数组
	 * @param prdtCellIds 生产单元ID数组
	 * @param priority    优先级
	 * @param startTime   报警时间范围起始
	 * @param endTime     报警时间范围结束
	 * @param endFlag     时间校正标识
     * @param page        分页参数
	 * @return 报警事件实体集合
	 * @throws Exception
	 * <AUTHOR> 2019-12-30
	 */
    PaginationBean<AlarmEventEntity> getAllAlarmEvent(String[] unitCodes, Long[] prdtCellIds,
                                            Integer priority, Date startTime, Date endTime, String endFlag, Pagination page) throws Exception;

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-10-17
	 * @param unitCodes		装置ID数组
	 * @param prdtCellIds	生产单元ID数组
	 * @param priority		优先级
	 * @param startTime		报警时间范围起始
	 * @param endTime		报警时间范围结束
	 * @param endFlag		时间校正标识
	 * @param page			分页参数
	 * @return 报警事件实体集合
	 * @throws Exception
	 */
	PaginationBean<AlarmEventEntity> getAlarmEvent(String[] unitCodes, Long[] prdtCellIds,
			Integer priority, Date startTime, Date endTime, String endFlag, String alarmPointTag, Long alarmFlagId, Pagination page) throws Exception;

	/**
	 * 获取报警事件优先级评估统计数据
	 * 
	 * <AUTHOR> 2017-11-21
	 * @param startTime		报警时间范围起始
	 * @param endTime		报警时间范围结束
	 * @param unitCodes		装置ID数组
	 * @param prdtCellIds	生产单元ID数组
	 * @param endFlag		时间校正标识
	 * @return 报警事件优先级评估统计数据
	 * @throws Exception
	 */
	List<AlarmPriorityAssessEntity> getAlarmPriorityAssessStatisticData(Date startTime, Date endTime, String[] unitCodes,
			Long[] prdtCellIds, String endFlag) throws Exception;
}
