package com.pcitc.opal.ac.dao.imp;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;
import javax.persistence.TypedQuery;

import org.springframework.transaction.annotation.Transactional;

import com.pcitc.opal.ac.dao.AlarmChangePlanAproRepositoryCustom;
import com.pcitc.opal.ac.pojo.AlarmChangePlanApro;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.dao.BaseRepository;

/*
 * 报警变更方案案审批记实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_AlarmChangePlanAproRepositoryImpl
 * 作       者：kun.zhao
 * 创建时间：2017/01/19
 * 修改编号：1
 * 描       述：报警变更方案案审批记实体的Repository实现    
 */
public class AlarmChangePlanAproRepositoryImpl extends BaseRepository<AlarmChangePlanApro, Long>
		implements AlarmChangePlanAproRepositoryCustom {

	/**
	 * 删除报警变更方案审批记录实体
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangePlanAproIds 报警变更方案审批记录Id数组
	 * @return 返回结果信息类
	 */
	@Override
	@Transactional
	public CommonResult deleteAlarmChangePlanApro(Long[] alarmChangePlanAproIds) {
		// 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            String hql = " from AlarmChangePlanApro t where t.aproId in (:alarmChangePlanAproIds)";
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> alarmChangePlanAproIdsList = Arrays.asList(alarmChangePlanAproIds);
            paramList.put("alarmChangePlanAproIds", alarmChangePlanAproIdsList);

            TypedQuery<AlarmChangePlanApro> query = getEntityManager().createQuery(hql, AlarmChangePlanApro.class);
            this.setParameterList(query, paramList);
            List<AlarmChangePlanApro> alarmChangePlanAproList = query.getResultList();
            alarmChangePlanAproList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 删除出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
	}

	
	/**
	 * 通过报警变更方案Id获取多条数据
	 * 
	 * <AUTHOR> 2018-01-19 报警变更方案Id数组
	 * @param alarmChangePlanIds 报警变更方案Id数组
	 * @return 报警变更方案审批记录实体集合
	 */
	@Override
	public List<AlarmChangePlanApro> getalarmChangePlanAproByPlanIds(Long[] alarmChangePlanIds) {
		try {
            // 查询字符串
            String hql = "from AlarmChangePlanApro t ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (alarmChangePlanIds.length > 0) {
                hql += " where t.planId in (:alarmChangePlanIds)";
                List<Long> alarmChangePlanIdsList = Arrays.asList(alarmChangePlanIds);
                paramList.put("alarmChangePlanIds", alarmChangePlanIdsList);
            }
            TypedQuery<AlarmChangePlanApro> query = getEntityManager().createQuery(hql, AlarmChangePlanApro.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
	}

	/**
	 * 审批信息
	 * 
	 * <AUTHOR> 2018-01-23 
	 * @param planId 报警变更方案ID
	 * @return List<AlarmChangePlanApro> 返回AlarmChangePlanApro实体集合
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<AlarmChangePlanApro> getAlarmChangePlanApro(Long planId) {
		try {
            // 查询字符串
            String hql = "select acpa from AlarmChangePlanApro acpa ";
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 报警变更方案ID
            hql += " where acpa.planId = :planId ";
        	paramList.put("planId", planId);
        	hql += " order by acpa.aproTime desc";
            Query query = getEntityManager().createQuery(hql);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception e) {
            throw e;
        }
	}


	/**
	 * 审核信息
	 * 
	 * <AUTHOR> 2018-01-30 
	 * @param planId 报警变更方案ID
	 */
	@SuppressWarnings("unchecked")
	@Override
	public AlarmChangePlanApro getAlarmChangePlanAproByPlanId(Long planId) {
		try {
            // 查询字符串
            String hql = "select acpa from AlarmChangePlanApro acpa ";
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 报警变更方案ID
            hql += " where acpa.planId = :planId ";
        	paramList.put("planId", planId);
        	hql += " order by acpa.aproTime desc";
            Query query = getEntityManager().createQuery(hql);
            this.setParameterList(query, paramList);
            query.setFirstResult(0).setMaxResults(1);
            List<AlarmChangePlanApro> list=query.getResultList();
            AlarmChangePlanApro acpa=list.size()>0?list.get(0):null;
            return acpa;
        } catch (Exception e) {
            throw e;
        }
	}
    /**
     * 变更方案审批记录
     *
     * <AUTHOR> 2018-3-13
     * @param alarmChangePlanApro 报警变更方案审批记录实体
     * @return 返回结果信息类
     */
    @SuppressWarnings("all")
    @Override
    public CommonResult addAlarmChangePlanApro(AlarmChangePlanApro alarmChangePlanApro){
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(alarmChangePlanApro);
            commonResult.setResult(alarmChangePlanApro);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

}
