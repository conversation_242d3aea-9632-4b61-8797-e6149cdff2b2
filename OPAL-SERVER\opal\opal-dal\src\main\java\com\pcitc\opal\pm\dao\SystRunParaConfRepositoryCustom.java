package com.pcitc.opal.pm.dao;

import java.util.List;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.pm.pojo.SystRunParaConf;

/*
 * 系统运行参数配置实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_SystRunParaConfRepositoryCustom
 * 作       者：kun.zhao
 * 创建时间：2018/01/22
 * 修改编号：1
 * 描       述：系统运行参数配置实体的Repository的JPA自定义接口 
 */
public interface SystRunParaConfRepositoryCustom {
	
	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2018-01-22
	 * @param systRunParaConf 系统运行参数配置实体
	 * @return 更新结果提示信息
	 */
	CommonResult updateSystRunParaConf(SystRunParaConf systRunParaConf);
	
	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2018-01-22
	 * @param systRunParaConfIds 系统运行参数配置Id数组
	 * @return 系统运行参数配置实体集合
	 */
	List<SystRunParaConf> getSystRunParaConf(Long[] systRunParaConfIds);
	
	/**
	 * 加载系统运行参数维护主数据
	 * 
	 * <AUTHOR> 2018-01-22
	 * @param name 名称
	 * @param code 编码
	 * @return 系统运行参数配置实体集合
	 */
	List<SystRunParaConf> getSystRunParaConf(String name, String code);
	public String findParaValueByCode(String code,Integer companyId);
}
