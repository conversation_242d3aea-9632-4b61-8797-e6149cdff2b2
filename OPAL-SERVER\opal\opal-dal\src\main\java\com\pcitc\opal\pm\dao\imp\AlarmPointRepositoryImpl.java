package com.pcitc.opal.pm.dao.imp;

import com.alibaba.fastjson.JSON;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmPointRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import org.apache.commons.lang.ArrayUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * AlarmPoint实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_AlarmPointRepositoryImpl
 * 作    者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描    述：AlarmPoint实体的Repository实现
 */
public class AlarmPointRepositoryImpl extends BaseRepository<AlarmPoint, Long> implements AlarmPointRepositoryCustom {

	@PersistenceContext
	EntityManager em;

	/**
	 * 批量插入报警点数据
	 *
	 * <AUTHOR> 2017-11-22
	 * @param list 报警点实体集合
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public void batchInsert(List<AlarmPoint> list) {
		try {
			//一次100条插入
			int batchSize = 100;
			for (int i = 0; i < list.size(); i++) {
				em.persist(list.get(i));
				if (i % batchSize == 0 && i > 0) {
					em.flush();
					em.clear();
				}
			}
		}catch (Exception e){
			throw e;
		}
	}

	/**
	 * 新增报警点
	 *
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointEntity
	 *            报警点实体
	 * @return 消息结果类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addAlarmPoint(AlarmPoint alarmPointEntity) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			this.getEntityManager().persist(alarmPointEntity);
			commonResult.setResult(alarmPointEntity);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		return commonResult;
	}

	/**
	 * 删除报警点实体
	 *
	 * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointIds
	 *            报警点ID集合
	 * @return 消息结果类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteAlarmPoint(Long[] alarmPointIds) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = " from AlarmPoint t where t.companyId=:companyId and t.alarmPointId in (:alarmPointIds)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			List<Long> alarmPointIdList = Arrays.asList(alarmPointIds);
			paramList.put("alarmPointIds", alarmPointIdList);
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			TypedQuery<AlarmPoint> query = getEntityManager().createQuery(hql, AlarmPoint.class);
			this.setParameterList(query, paramList);
			List<AlarmPoint> alarmPointList = query.getResultList();
			alarmPointList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	/**
	 * 更新报警点
	 *
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointEntity
	 *            报警点实体
	 * @return 消息结果类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult updateAlarmPoint(AlarmPoint alarmPointEntity) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			getEntityManager().merge(alarmPointEntity);
			commonResult.setResult(alarmPointEntity);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("更新成功！");
		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	/**
	 * 获取报警点实体
	 *
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointId
	 *            报警点id
	 * @return 报警点实体
	 */
	@Override
	public AlarmPoint getSingleAlarmPoint(Long alarmPointId) {
		try {
			StringBuilder hql = new StringBuilder("select ap from AlarmPoint ap ");
			hql.append("left join fetch ap.prdtCell pc ");
			hql.append("left join fetch ap.alarmPointType apt ");
			hql.append("left join fetch ap.measUnit mu where ap.companyId=:companyId and pc.companyId=:companyId and ap.alarmPointId=:alarmPointId ");
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("alarmPointId", alarmPointId);
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			TypedQuery<AlarmPoint> query = getEntityManager().createQuery(hql.toString(), AlarmPoint.class);
			this.setParameterList(query, paramList);
			return query.getSingleResult();
		} catch (Exception ex) {
			throw ex;
		}
	}
	/**
	 * 获取单条数据
	 *
	  * <AUTHOR> 2018-04-23
	 * @param alarmPointTag  报警点位号
	 * @return AlarmPoint 报警点实体
	 */
	public AlarmPoint getSingleAlarmPointByTag(String alarmPointTag){
		try {
			StringBuilder hql = new StringBuilder("select ap from AlarmPoint ap ");
			hql.append("left join fetch ap.prdtCell pcs ");
			hql.append("left join fetch ap.alarmPointType apt ");
			hql.append("left join fetch ap.measUnit mu where ap.tag=:tag and ap.companyId=:companyId and pcs.companyId=:companyId ");
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("tag", alarmPointTag);
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			TypedQuery<AlarmPoint> query = getEntityManager().createQuery(hql.toString(), AlarmPoint.class);
			this.setParameterList(query, paramList);
			return query.getResultList().get(0);
		} catch (Exception ex) {
			throw ex;
		}
	}
	/**
	 * 获取报警点集合
	 *
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointIds
	 *            报警点Ids集合
	 * @return 报警点集合
	 */
	@Override
	public List<AlarmPoint> getAlarmPoint(Long[] alarmPointIds) {

		try {
			// 查询字符串
			String hql = "from AlarmPoint t join fetch t.measUnit where t.companyId=:companyId";
			Map<String, Object> paramList = new HashMap<String, Object>();
			if (alarmPointIds.length > 0) {
				hql += " and t.alarmPointId in (:alarmPointIds)";
				List<Long> alarmPointIdsList = Arrays.asList(alarmPointIds);
				paramList.put("alarmPointIds", alarmPointIdsList);
			}
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());

			TypedQuery<AlarmPoint> query = getEntityManager().createQuery(hql, AlarmPoint.class);
			this.setParameterList(query, paramList);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 获取报警点集合
	 *
	 * @return 报警点集合
	 */
	@Override
	public List<AlarmPointLimitDTO> getAlarmPointLimit() {

		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("select new com.pcitc.opal.pm.dao.imp.AlarmPointLimitDTO(" +
					"c.unitId,t.alarmPointId,t.prdtCellId,t.tag,t.craftUpLimitInclude," +
					"t.craftDownLimitInclude,t.craftUpLimitValue,t.craftDownLimitValue," +
					"t.interlockUpLimitInclude,t.interlockDownLimitInclude,t.interlockUpLimitValue," +
					"t.interlockDownLimitValue,t.craftRank,t.location,t.pidCode,t.alarmPointTypeId," +
					"t.monitorType,t.measunitId,t.instrmtType,t.instrmtPriority,t.virtualRealityFlag," +
					"t.virtualFlag,t.alarmPointHH,t.alarmPointHI,t.alarmPointLO,t.alarmPointLL,t.inUse," +
					"t.inSendMsg,t.mobilePhone,t.sortNum,t.des,t.rtdbTag,t.companyId," +
					"t.crtDate,t.crtUserId,t.crtUserName,t.mntDate,t.mntUserId,t.mntUserName) from AlarmPoint t ");
			hql.append("left join PrdtCell c on c.prdtCellId=t.prdtCellId ");
			hql.append("where t.inUse = 1");
			Query query =this.getEntityManager().createQuery(hql.toString());
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 获取报警点实体（分页）
	 *
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param unitCodes  装置编码
	 * @param prdtCellIds 生产单元id
	 * @param tag  位号
	 * @param typeId  报警点类型id
	 * @param inUse	  是否启用
	 * @param instrmtPriority 仪表优先级
	 * @param page 翻页实现类
	 * @return 翻页对象
	 */
	@Override
	public PaginationBean<AlarmPoint> getAlarmPoint(String[] unitCodes, Long[] prdtCellIds, String tag, Long typeId,
													Integer inUse, Integer instrmtPriority, Pagination page) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("from AlarmPoint ap ");
			StringBuilder hqlWhere = new StringBuilder("where 1=1 and ap.virtualFlag = 0 and ap.companyId=:companyId ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();

			// 装置
			hql.append(" inner join fetch ap.prdtCell pc ");
			if (ArrayUtils.isNotEmpty(unitCodes)) {
				hqlWhere.append("and pc.unitId in (:unitIds) ");
				List<String> unitIdsList = Arrays.asList(unitCodes);
				paramList.put("unitIds", unitIdsList);
				// 生产单元
				if (unitCodes.length == 1 && ArrayUtils.isNotEmpty(prdtCellIds)) {
					hqlWhere.append("and pc.prdtCellId in (:prdtCellIds) ");
					List<Long> prdtCellIdsList = Arrays.asList(prdtCellIds);
					paramList.put("prdtCellIds", prdtCellIdsList);
				}
			}
			// 报警点类型
			hql.append("left join fetch ap.alarmPointType apt ");
			if (!StringUtils.isEmpty(typeId) && typeId != -1) {
				hqlWhere.append("and apt.alarmPointTypeId=:alarmPointTypeId ");
				paramList.put("alarmPointTypeId", typeId);
			}
			// 位号
			if (!StringUtils.isEmpty(tag)) {
				hqlWhere.append("and upper(ap.tag) like upper(:tag) escape '/' ");
				paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
			}
			// 是否启用
			if (!StringUtils.isEmpty(inUse) && inUse != -1) {
				hqlWhere.append("and ap.inUse=:inUse ");
				paramList.put("inUse", inUse);
			}
			// 仪表优先级
			if (!StringUtils.isEmpty(instrmtPriority) && instrmtPriority != -1) {
				if(instrmtPriority!=9){
					hqlWhere.append("and ap.instrmtPriority=:instrmtPriority ");
					paramList.put("instrmtPriority", instrmtPriority);
				}else{
					hqlWhere.append("and ap.instrmtPriority is null ");
				}
			}
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());

			hql.append(" left join fetch ap.measUnit mu ");
			hqlWhere.append("order by (case when (ap.craftDownLimitValue is not null and ap.alarmPointLL is not null and  ap.craftDownLimitInclude = 0 and ap.craftDownLimitValue >= ap.alarmPointLL) then 1 " +
					"                 when (ap.craftDownLimitValue is not null and ap.alarmPointLL is not null and ap.craftDownLimitInclude = 1 and  ap.craftDownLimitValue > ap.alarmPointLL) then 1 " +
					"                 when (ap.craftUpLimitValue is not null and ap.alarmPointHH is not null and ap.craftUpLimitInclude = 0 and  ap.craftUpLimitValue <= ap.alarmPointHH) then 1 " +
					"                 when (ap.craftUpLimitValue is not null and ap.alarmPointHH is not null and ap.craftUpLimitInclude = 1 and  ap.craftUpLimitValue < ap.alarmPointHH) then 1 " +
					"                 when (ap.craftDownLimitValue is not null and ap.alarmPointLO is not null and  ap.craftDownLimitInclude = 0 and ap.craftDownLimitValue >= ap.alarmPointLO) then 1 " +
					"                 when (ap.craftDownLimitValue is not null and ap.alarmPointLO is not null and ap.craftDownLimitInclude = 1 and  ap.craftDownLimitValue > ap.alarmPointLO) then 1 " +
					"                 when (ap.craftUpLimitValue is not null and ap.alarmPointHI is not null and ap.craftUpLimitInclude = 0 and  ap.craftUpLimitValue <= ap.alarmPointHI) then 1 " +
					"                 when (ap.craftUpLimitValue is not null and ap.alarmPointHI is not null and ap.craftUpLimitInclude = 1 and  ap.craftUpLimitValue < ap.alarmPointHI) then 1 " +
					"                 else 0   " +
					" end) desc, pc.unitId,pc.sname,ap.sortNum,ap.tag ");

			// 调用基类方法查询返回结果
			PaginationBean<AlarmPoint> bean = this.findAll(page, hql.toString() + hqlWhere.toString(), paramList);
			return bean;
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 校验
	 *
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointEntity
	 *            报警点实体
	 * @return 消息结果类
	 */
	@Override
	public CommonResult alarmPointValidation(AlarmPoint alarmPointEntity) {
		CommonResult commonResult = new CommonResult();
		try {
			// “生产单元”、“位号”联合唯一，提示“该生产单元下此位号已存在！”；
			StringBuilder hql = new StringBuilder(
					"from AlarmPoint t where t.tag =:tag and t.prdtCellId = :prdtCellId and t.alarmPointId<>:alarmPointId and t.companyId=:companyId");
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("prdtCellId", alarmPointEntity.getPrdtCellId());
			paramList.put("tag", alarmPointEntity.getTag());
			paramList.put("alarmPointId",alarmPointEntity.getAlarmPointId() == null ? 0 : alarmPointEntity.getAlarmPointId());
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());

			long index = this.getCount(hql.toString(), paramList);
			if (index > 0) {
				commonResult.setIsSuccess(false);
				commonResult.setMessage("该生产单元下此位号已存在！");
				return commonResult;
			}
		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
			commonResult.setResult(null);
		}
		return commonResult;
	}

	/**
	 * 导入校验
	 *
	 * <AUTHOR> 2018-08-15
	 *
	 * @param alarmPointEntity 报警点实体
	 * @return 消息结果类
	 */
	@Override
	public CommonResult alarmPointImportValidation(AlarmPoint alarmPointEntity) {
		CommonResult commonResult = new CommonResult();
		try {
			// “生产单元”、“位号”联合唯一，提示“该生产单元下此位号已存在！”；
			StringBuilder hql = new StringBuilder("from AlarmPoint t where t.tag =:tag and t.prdtCellId = :prdtCellId and t.companyId=:companyId");
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("prdtCellId", alarmPointEntity.getPrdtCellId());
			paramList.put("tag", alarmPointEntity.getTag());
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());

			Query query = getEntityManager().createQuery(hql.toString());
			this.setParameterList(query, paramList);
			AlarmPoint alarmPoint = (AlarmPoint)query.getSingleResult();

			commonResult.setResult(alarmPoint);
			if (alarmPoint!=null) {
				commonResult.setIsSuccess(true);
				return commonResult;
			}
		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
			commonResult.setResult(null);
		}
		return commonResult;
	}

	/**
	 * 判断生产单元在报警点中是否使用
	 *
	 * <AUTHOR> 2017-11-30
	 * @param prdtCellId 报警点实体
	 * @return CommonResult 消息结果类
	 */
	public Long getPrdtCellIsUseInAlarmPoint(Long prdtCellId) {
		String hql ="select count(*) from AlarmPoint ap where ap.prdtCellId = :prdtCellId and ap.companyId=:companyId";
		Map<String, Object> paramList = new HashMap<String, Object>();
		paramList.put("prdtCellId", prdtCellId);
		//企业
		CommonProperty commonProperty = new CommonProperty();
		paramList.put("companyId",commonProperty.getCompanyId());

		Query query = getEntityManager().createQuery(hql);
		this.setParameterList(query, paramList);
		return (Long)query.getSingleResult();
	}

	/**
	 * 获取相关性位号配置明细
	 *
	 * <AUTHOR> 2018-08-02
	 * @param unitCode 装置编码
	 * @param prdtCellIds 生产单元id
	 * @param tag 主位号
	 * @param apIdList 报警点ID集合
	 * @param page 分页对象
	 */
	@Override
	public PaginationBean<AlarmPoint> getRelevantTagConfigDtlAdd(String unitCode, Long prdtCellIds,
																 String tag, List<Long> apIdList, Pagination page) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("select ap from AlarmPoint ap ");
			hql.append("left join fetch ap.prdtCell pc ");
			hql.append("left join fetch ap.alarmPointType apt ");
			hql.append("left join fetch ap.measUnit mu where 1=1 and ap.companyId=:companyId");
			hql.append(" and case when ap.alarmPointId is not null then ap.inUse  end =1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			// 装置
			if (!StringUtils.isEmpty(unitCode)) {
				hql.append("and pc.unitId = :unitId ");
				paramList.put("unitId", unitCode);
				// 生产单元
				if (!StringUtils.isEmpty(prdtCellIds)) {
					hql.append("and ap.prdtCellId = :prdtCellIds ");
					paramList.put("prdtCellIds", prdtCellIds);
				}
			}
			// 位号
			if (!StringUtils.isEmpty(tag)) {
				hql.append("and upper(ap.tag) like upper(:tag) escape '/' ");
				paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
			}
			// 去重报警点id
			if(!apIdList.isEmpty()){
				hql.append("and ap.alarmPointId not in (:alarmPointIds) ");
				paramList.put("alarmPointIds", apIdList);
			}
			// 已启用
			hql.append("and ap.inUse=:inUse ");
			paramList.put("inUse", CommonEnum.InUseEnum.Yes.getIndex());
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			// 调用基类方法查询返回结果
			PaginationBean<AlarmPoint> bean = this.findAll(page, hql.toString(), paramList);
			return bean;
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	public PaginationBean<AlarmPoint> getAlarmMsgConfig(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inSendMsg, Pagination page) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("from AlarmPoint ap ");
			StringBuilder hqlWhere = new StringBuilder("where 1=1 and ap.inUse=:inUse and ap.companyId=:companyId ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("inUse", CommonEnum.InSendMsgEnum.Yes.getIndex());
			// 装置
			hql.append(" inner join fetch ap.prdtCell pc ");
			if (ArrayUtils.isNotEmpty(unitCodes)) {
				hqlWhere.append("and pc.unitId in (:unitIds) ");
				List<String> unitIdsList = Arrays.asList(unitCodes);
				paramList.put("unitIds", unitIdsList);
				// 生产单元
				if (unitCodes.length == 1 && ArrayUtils.isNotEmpty(prdtCellIds)) {
					hqlWhere.append("and pc.prdtCellId in (:prdtCellIds) ");
					List<Long> prdtCellIdsList = Arrays.asList(prdtCellIds);
					paramList.put("prdtCellIds", prdtCellIdsList);
				}
			}

			// 位号
			if (!StringUtils.isEmpty(tag)) {
				hqlWhere.append("and upper(ap.tag) like upper(:tag) escape '/' ");
				paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
			}
			//hql.append(" left join fetch ap.measUnit mu ");
			//是否发送短信
			if (null != inSendMsg && inSendMsg != -1) {
				hqlWhere.append(" and ap.inSendMsg =(:inSendMsg) ");
				paramList.put("inSendMsg",inSendMsg);
			}
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());

			//“装置ID+生产单元简称+排序+位号”正序
			hqlWhere.append(" order by pc.unitId,pc.sname,ap.sortNum,ap.tag ");

			// 调用基类方法查询返回结果
			PaginationBean<AlarmPoint> bean = this.findAll(page, hql.toString() + hqlWhere.toString(), paramList);
			return bean;
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	public List<AlarmPoint> getAlarmMsgConfigByAlarmPointIds(Long[] alarmPointIds) {
		try {
			// 查询字符串
			String hql = "from AlarmPoint t inner join fetch t.prdtCell pc where t.companyId=:companyId";
			Map<String, Object> paramList = new HashMap<String, Object>();
			if (alarmPointIds.length > 0) {
				hql += " and t.alarmPointId in (:alarmPointIds)";
				List<Long> alarmPointIdsList = Arrays.asList(alarmPointIds);
				paramList.put("alarmPointIds", alarmPointIdsList);
			}
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());

			TypedQuery<AlarmPoint> query = getEntityManager().createQuery(hql, AlarmPoint.class);
			this.setParameterList(query, paramList);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	public List<AlarmPoint> findAlarmPonitListByInfo(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inSendMsg) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("from AlarmPoint ap ");
			StringBuilder hqlWhere = new StringBuilder("where 1=1 and ap.inUse=:inUse ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("inUse", CommonEnum.InSendMsgEnum.Yes.getIndex());
			// 装置
			hql.append(" inner join fetch ap.prdtCell pc ");

			//企业id判断
			hqlWhere.append(" and ap.companyId = :companyId and pc.companyId = :companyId ");
			paramList.put("companyId", new CommonProperty().getCompanyId());
			if (ArrayUtils.isNotEmpty(unitCodes)) {
				hqlWhere.append("and pc.unitId in (:unitIds) ");
				List<String> unitIdsList = Arrays.asList(unitCodes);
				paramList.put("unitIds", unitIdsList);
				// 生产单元
				if (unitCodes.length == 1 && ArrayUtils.isNotEmpty(prdtCellIds)) {
					hqlWhere.append("and pc.prdtCellId in (:prdtCellIds) ");
					List<Long> prdtCellIdsList = Arrays.asList(prdtCellIds);
					paramList.put("prdtCellIds", prdtCellIdsList);
				}
			}

			// 位号
			if (!StringUtils.isEmpty(tag)) {
				hqlWhere.append("and upper(ap.tag) like upper(:tag) escape '/' ");
				paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
			}
			//hql.append(" left join fetch ap.measUnit mu ");
			//是否发送短信
			if (null != inSendMsg && inSendMsg != -1) {
				hqlWhere.append(" and ap.inSendMsg =(:inSendMsg) ");
				paramList.put("inSendMsg",inSendMsg);
			}
			//非分页限制展示条数
			//hqlWhere.append("  and rownum <= 1000 ");
			//“装置ID+生产单元简称+排序+位号”正序
			hqlWhere.append(" order by pc.unitId,pc.sname,ap.sortNum,ap.tag ");

			TypedQuery<AlarmPoint> query = this.getEntityManager().createQuery(hql.toString()+hqlWhere.toString(),AlarmPoint.class);
			this.setParameterList(query,paramList);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}

	}

	@Transactional
	@Override
	public Integer updateInSendMsgByAlarmPointId(Long[] alarmPointId, Integer inSendMsg) {
		String sql = "UPDATE AlarmPoint set inSendMsg=:inSendMsg where alarmPointId in (:ids) and companyId = :companyId";
		return getEntityManager().createQuery(sql)
				.setParameter("inSendMsg",inSendMsg)
				.setParameter("ids",Arrays.asList(alarmPointId))
				.setParameter("companyId", new CommonProperty().getCompanyId())
				.executeUpdate();
	}

	@Override
	public List<ModelDTO> getModel(String tag) {
		String sql =  "select tag.tag as tag, tag.des as des, tag.instrmt_priority as instrmtPriority, " +
				"tag.monitor_type as monitorType, workshop.std_code as workshopCode, workshop.name as workshopName, " +
				"unit.std_code as unitCode, unit.name as unitName, tag.rtdb_tag as rtdbTag, prd.name as prdtcellName, " +
				"tag.location as location from t_pm_alarmpoint tag"
		+ " left join t_pm_prdtcell prd on tag.prdtcell_id = prd.prdtcell_id"
		+ " left join t_pm_unit unit on prd.unit_code = unit.std_code"
		+ " left join t_pm_workshop workshop on unit.workshop_id = workshop.workshop_id where 1=1 ";
		if (org.apache.commons.lang3.StringUtils.isNoneBlank(tag)) {
			sql +=" and tag.tag = '" +  tag +"'";
		}
		Query query = getEntityManager().createNativeQuery(sql.toString());
		query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
		List<Map<String,Object>> resultList = query.getResultList();
		String irsStr = JSON.toJSONString(resultList);
		List<ModelDTO> sendMsgInfoDTOS = JSON.parseArray(irsStr,ModelDTO.class);
		return sendMsgInfoDTOS;
	}
}
