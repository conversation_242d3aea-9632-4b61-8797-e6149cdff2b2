package com.pcitc.opal.pm.bll.imp;

import java.util.List;

import com.pcitc.opal.pm.bll.entity.DBFactoryEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.CommonUtil;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.CommonEnum.PageModelEnum;
import com.pcitc.opal.pm.bll.FactoryService;
import com.pcitc.opal.pm.dao.FactoryRepository;
import com.pcitc.opal.pm.pojo.Factory;
import pcitc.imp.common.ettool.utils.ObjectConverter;

/*
 * 工厂业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_FactoryImpl
 * 作       者：kun.zhao
 * 创建时间：2017/12/11
 * 修改编号：1
 * 描       述：工厂业务逻辑层实现类
 */
@Service
public class FactoryImpl implements FactoryService {

	/**
     * 实例化数据访问层接口
     */
    @Autowired
    private FactoryRepository factoryRepository;
    
    /**
     * 新增工厂
     * 
     * <AUTHOR> 2017-12-11
     * @param DBFactoryEntity 工厂实体
     * @return 返回结果信息类
	 * @throws Exception
     */
	@Override
	public CommonResult addFactory(DBFactoryEntity DBFactoryEntity) throws Exception {
		// 实体转换为持久层实体
		Factory factoryPO = ObjectConverter.entityConverter(DBFactoryEntity, Factory.class);
        // 数据校验
        factoryValidation(factoryPO);
        // 赋值  创建人、创建名称、创建时间
        CommonUtil.returnValue(factoryPO, PageModelEnum.NewAdd.getIndex());
        CommonResult commonResult = factoryRepository.addFactory(factoryPO);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
	}

	/**
     * 删除工厂
     *
     * <AUTHOR> 2017-12-11
     * @param factoryIds 工厂ID集合
     * @return 返回结果信息类
     * @throws Exception
     */
	@Override
	public CommonResult deleteFactory(Long[] factoryIds) throws Exception {
		List<Factory> anlyFactoryList = factoryRepository.getFactory(factoryIds);
        if (anlyFactoryList == null || anlyFactoryList.isEmpty())
            return new CommonResult();
        Long[] anlyFactoryIdList = anlyFactoryList.stream().map(item -> item.getFactoryId()).toArray(Long[]::new);
        // 调用DAL删除方法
        CommonResult commonResult = factoryRepository.deleteFactory(anlyFactoryIdList);

        return commonResult;
	}

	/**
     * 修改工厂
     *
     * <AUTHOR> 2017-12-11
     * @param factoryEntity 工厂实体
     * @return 返回结果信息类
     * @throws Exception
     */
	@Override
	public CommonResult updateFactory(DBFactoryEntity factoryEntity) throws Exception {
		// 实体转换持久层实体
		Factory factoryPO = ObjectConverter.entityConverter(factoryEntity, Factory.class);
        // 校验
		factoryValidation(factoryPO);
        // 实体转换为持久层实体
		factoryPO = factoryRepository.getSingleFactory(factoryEntity.getFactoryId());
        CommonUtil.objectExchange(factoryEntity, factoryPO);
        // 赋值 修改人、修改名称、修改时间
        CommonUtil.returnValue(factoryPO, PageModelEnum.Edit.getIndex());
        // 调用DAL更新方法
        CommonResult commonResult = factoryRepository.updateFactory(factoryPO);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
	}

	/**
     * 通过工厂ID获取单条数据
     *
     * <AUTHOR> 2017-12-11
     * @param factoryId 计量单位ID
     * @return 指定工厂实体
     */
	@Override
	public DBFactoryEntity getSingleFactory(Long factoryId) throws Exception {
		Factory factoryPO = factoryRepository.getSingleFactory(factoryId);
        return ObjectConverter.entityConverter(factoryPO, DBFactoryEntity.class);
	}

	/**
     * 工厂查询
     *
     * <AUTHOR> 2017-12-11
     * @param name    工厂名称/简称
     * @param stdCode 标准编码
     * @param inUse   是否启用
     * @param page    分页参数
     * @return 工厂实体（分页）
     */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<DBFactoryEntity> getFactory(Long companyId, String name, String stdCode, Integer inUse, Pagination page)
			throws Exception {
		PaginationBean<Factory> listFactory = factoryRepository.getFactory(companyId,name, stdCode, inUse, page);
        PaginationBean<DBFactoryEntity> returnFactory = new PaginationBean<>(page,
        		listFactory.getTotal());
        returnFactory.setPageList(ObjectConverter.listConverter(listFactory.getPageList(), DBFactoryEntity.class));
		for (int i = 0; i < returnFactory.getPageList().size(); i++) {
			DBFactoryEntity dbFactoryEntity = returnFactory.getPageList().get(i);
			Factory factory = listFactory.getPageList().get(i);
			dbFactoryEntity.setCompanyName(null != factory.getCompanyId()?factory.getCompany().getSname():"");
		}
        return returnFactory;
	}
	
	/**
	 * 校验
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryPO 工厂实体
	 * @throws Exception
	 */
	private void factoryValidation(Factory factoryPO) throws Exception {
		CommonResult commonResult = null;
    	// 实体不能为空
        if (factoryPO == null) {
            throw new Exception("没有工厂数据！");
        }
        // 调用DAL与数据库相关的校验
        commonResult = factoryRepository.factoryValidation(factoryPO);

        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
	}

}
