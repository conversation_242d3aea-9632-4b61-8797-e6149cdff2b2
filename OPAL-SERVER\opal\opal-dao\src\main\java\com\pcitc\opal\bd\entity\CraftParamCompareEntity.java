package com.pcitc.opal.bd.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 工艺参数对照
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_bd_craftparamcompare")
public class CraftParamCompareEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 工艺装置编码
     */
    @TableField("craft_unit_code")
    private String craftUnitCode;
    /**
     * 工艺参数位号
     */
    @TableField("craft_param_dcs_code")
    private String craftParamDcsCode;

    /**
     * 报警点位号
     */
    @TableField("alarm_point_code")
    private String alarmPointCode;


}
