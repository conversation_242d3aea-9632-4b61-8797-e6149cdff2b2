package com.pcitc.opal.aa.bll.imp;

import com.pcitc.opal.aa.bll.AlarmOperateAssessService;
import com.pcitc.opal.aa.bll.entity.AlarmOperateAssessChartEntity;
import com.pcitc.opal.aa.bll.entity.AlarmOperateAssessEntity;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.AlarmEventExLocationEntity;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.common.bll.entity.WorkshopEntity;
import com.pcitc.opal.pm.bll.entity.PrdtCellEntity;
import org.apache.commons.beanutils.converters.DateConverter;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.*;
import java.util.stream.Collectors;

/*
 * 报警操作评估业务逻辑层接口实现
 * 模块编号：pcitc_opal_bll_class_AlarmOperateAssessImpl
 * 作  　者：xuelei.wang
 * 创建时间：2017-10-24
 * 修改编号：1
 * 描    述：报警操作评估业务逻辑层接口实现
 */
@SuppressWarnings("all")
@Service
public class AlarmOperateAssessImpl implements AlarmOperateAssessService {

    @Autowired
    private BasicDataService basicDataService;
    @Autowired
    private AlarmEventRepository alarmEventRepository;

    /**
     * 工厂模式
     */
    private static final int FACTORY_MODE = 1;
    /**
     * 日期格式化 天
     */
    private static final String DAY_PATTERN = "yyyy-MM-dd";
    /**
     * 日期格式化 周
     */
    private static final String WEEK_PATTERN = "yyyy-MM-dd";
    /**
     * 日期格式化 月
     */
    private static final String MONTH_PATTERN = "yyyy-MM";
    /**
     * 日期格式化 小时
     */
    private static final String HOUR_PATTERN = "yyyy-MM-dd HH";
    /**
     * 日期格式化 年-月-日 时:分:秒
     */
    private static final String FULL_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";


    /**
     * 获取报警操作评估首页数据
     *
     * @param startTime  开始日期
     * @param endTime    结束日期
     * @param unitCodes    装置编码集合
     * @param prdtIds    生产单元ID集合
     * @param dateType   查询日期类型(日,周,月)
     * @param checkModel 模式 0:装置模式,1:工厂车间模式
     * @return
     * @throws Exception
     * <AUTHOR> 2017-10-24
     */
    @Override
    public List<AlarmOperateAssessChartEntity> getAlarmOperateAssess(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, CommonEnum.DateTypeEnum dateType, int checkModel) throws Exception {
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime);
        List<AlarmOperateAssessChartEntity> chartEntityList = new ArrayList<>();
        String[] workshopIds = unitCodes;
        List<AlarmOperateAssessEntity> resList = new ArrayList<>();
        //如果是工厂车间模式
        if (checkModel == FACTORY_MODE) {
            prdtIds = null;
            unitCodes = basicDataService.getUnitListByWorkshopIds(unitCodes, true).stream().map(item -> item.getStdCode()).toArray(String[]::new);
        }

        if (unitCodes != null && unitCodes.length == 0) unitCodes = null;
        if (prdtIds != null && prdtIds.length == 0) prdtIds = null;

        CommonEnum.EquipmentTypeEnum equipmentTypeEnum;
        String hourTime = basicDataService.getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();
        List<UnitEntity> unitList = basicDataService.getUnitList(true);
        List<PrdtCellEntity> prdtCellList = basicDataService.getAllPrdtCellList();
        List<WorkshopEntity> workshopEntityList = null;
        List<UnitEntity> queryUnitList;
        String datePattern;
        if (unitCodes == null || unitCodes.length == 0) {
            unitCodes = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
        }
        try {
            //1.计算分组类型
            switch (dateType) {
                case Day:
                    datePattern = DAY_PATTERN;
                    break;
                case Week:
                    datePattern = WEEK_PATTERN;
                    break;
                case Month:
                    datePattern = MONTH_PATTERN;
                    break;
                case Hour:
                    datePattern = HOUR_PATTERN;
                    break;
                default:
                    datePattern = DAY_PATTERN;
            }

            //2.计算查询类型
            if (prdtIds != null) {
                equipmentTypeEnum = CommonEnum.EquipmentTypeEnum.PrdtCell;
            } else {
                equipmentTypeEnum = CommonEnum.EquipmentTypeEnum.Unit;
            }

            //3.计算数据分组中的小时
            DateConverter dateConverter = new DateConverter();
            dateConverter.setPattern("HH:mm:ss");
            Date dateTime = dateConverter.convert(Date.class, hourTime);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(dateTime);

            int hour = basicDataService.getQueryTimeHour();
            List<Object[]> resultList = alarmEventRepository.getAlarmOperateAssess(shiftDateCalculator.getQueryStartTime(), shiftDateCalculator.getQueryEndTime(), unitCodes, prdtIds, dateType.getName(), hour);
            List<AlarmOperateAssessEntity> operateAssessEntityList = new ArrayList<>();
            for (Object[] obj : resultList) {
                operateAssessEntityList.add(new AlarmOperateAssessEntity(
                        obj[0].toString(),
                        NumberUtils.toLong(obj[1].toString()),
                        obj[2].toString()
                ));
            }
            //4.如果是工厂模式,实体加上工厂的ID
            if (checkModel == FACTORY_MODE) {
                workshopEntityList = basicDataService.getWorkshopListByWorkshopIds(workshopIds);
                queryUnitList = basicDataService.getUnitListByIds(operateAssessEntityList.stream().map(AlarmOperateAssessEntity::getId).toArray(String[]::new), true);
                List<UnitEntity> finalQueryUnitList = queryUnitList;
                operateAssessEntityList.stream().forEach(item -> {
                    item.setWorkshopId(finalQueryUnitList.stream().filter(unit -> unit.getStdCode().equals(item.getId())).findFirst().orElse(new UnitEntity()).getWorkshopCode());
                });
            }
            //5.计算开始和结束时间
            DateConverter converter = new DateConverter();
            converter.setPattern(datePattern);
            Date finalStartTime = shiftDateCalculator.getDisplayStartTime();
            Date finalEndTime = shiftDateCalculator.getDisplayEndTime();
            operateAssessEntityList.stream().forEach((entity) -> {

                if (CommonEnum.EquipmentTypeEnum.Unit.equals(equipmentTypeEnum)) {
                    UnitEntity unitEntity = unitList.stream().filter(item -> item.getStdCode().toString().equals(entity.getId())).findFirst().orElse(new UnitEntity());
                    entity.setName(unitEntity.getSname());
                } else if (CommonEnum.EquipmentTypeEnum.PrdtCell.equals(equipmentTypeEnum)) {
                    PrdtCellEntity prdtCellEntity = prdtCellList.stream().filter(item -> item.getPrdtCellId().toString().equals(entity.getId())).findFirst().orElse(new PrdtCellEntity());
                    entity.setName(prdtCellEntity.getSname());
                }
                switch (dateType) {
                    case Day:
                        entity.setAlartTime(converter.convert(Date.class, entity.getGroupByTime()));
                        entity.setStartTime(DateUtils.setHours(entity.getAlartTime(), calendar.get(Calendar.HOUR_OF_DAY)));
                        Date endDay = DateUtils.addDays(entity.getAlartTime(), 1);
                        endDay = DateUtils.setHours(endDay, calendar.get(Calendar.HOUR_OF_DAY));
                        endDay = DateUtils.setMinutes(endDay, calendar.get(Calendar.MINUTE));
                        endDay = DateUtils.setSeconds(endDay, calendar.get(Calendar.SECOND));
                        if (endDay.getTime() > finalEndTime.getTime()) {
                            entity.setEndDate(finalEndTime);
                        } else {
                            entity.setEndDate(endDay);
                        }
                        break;
                    case Week:
                        //处理开始日期
                        entity.setAlartTime(converter.convert(Date.class, entity.getGroupByTime()));
                        entity.setStartTime(DateUtils.setHours(entity.getAlartTime(), calendar.get(Calendar.HOUR_OF_DAY)));
                        entity.setGroupByTime(DateFormatUtils.format(entity.getAlartTime(), DAY_PATTERN));
                        Date weekEnd = DateUtils.addWeeks(entity.getAlartTime(), 1);
                        weekEnd = DateUtils.setHours(weekEnd, calendar.get(Calendar.HOUR_OF_DAY));
                        weekEnd = DateUtils.setMinutes(weekEnd, calendar.get(Calendar.MINUTE));
                        weekEnd = DateUtils.setSeconds(weekEnd, calendar.get(Calendar.SECOND));
                        entity.setEndDate(weekEnd);
                        break;
                    case Month:
                        entity.setAlartTime(converter.convert(Date.class, entity.getGroupByTime()));
                        if (entity.getAlartTime().getTime() < finalStartTime.getTime()) {
                            entity.setAlartTime(finalStartTime);
                            entity.setStartTime(DateUtils.setHours(finalStartTime, calendar.get(Calendar.HOUR_OF_DAY)));
                            Date end = DateUtils.addMonths(entity.getAlartTime(), 1);
                            end = DateUtils.setHours(end, calendar.get(Calendar.HOUR_OF_DAY));
                            end = DateUtils.setMinutes(end, calendar.get(Calendar.MINUTE));
                            end = DateUtils.setSeconds(end, calendar.get(Calendar.SECOND));
                            if (end.getTime() > finalEndTime.getTime()) {
                                entity.setEndDate(finalEndTime);
                            } else {
                                end = DateUtils.setDays(end, 1);
                                entity.setEndDate(end);
                            }
                        } else {
                            entity.setAlartTime(converter.convert(Date.class, entity.getGroupByTime()));
                            entity.setStartTime(DateUtils.setHours(entity.getAlartTime(), calendar.get(Calendar.HOUR_OF_DAY)));
                            Date end = DateUtils.addMonths(entity.getAlartTime(), 1);
                            end = DateUtils.setHours(end, calendar.get(Calendar.HOUR_OF_DAY));
                            end = DateUtils.setMinutes(end, calendar.get(Calendar.MINUTE));
                            end = DateUtils.setSeconds(end, calendar.get(Calendar.SECOND));
                            if (end.getTime() > finalEndTime.getTime()) {
                                entity.setEndDate(finalEndTime);
                            } else {
                                end = DateUtils.setDays(end, 1);
                                entity.setEndDate(end);
                            }
                        }
                        break;
                    case Hour:
                        entity.setAlartTime(DateUtils.addHours(converter.convert(Date.class, entity.getGroupByTime()), calendar.get(Calendar.HOUR_OF_DAY)));
                        entity.setStartTime(entity.getAlartTime());
                        entity.setEndDate(DateUtils.addHours(entity.getStartTime(), 1));
                        break;
                }

            });
            operateAssessEntityList.sort(Comparator.comparing(AlarmOperateAssessEntity::getAlartTime, Comparator.naturalOrder()));
            //6.计算series
            Map<String, List<AlarmOperateAssessEntity>> seriesMap = operateAssessEntityList.stream()
                    .collect(Collectors.groupingBy(AlarmOperateAssessEntity::getName,
                            Collectors.collectingAndThen(Collectors.toCollection(ArrayList::new), item -> {
                                item.sort(Comparator.comparing(AlarmOperateAssessEntity::getAlartTime, Comparator.naturalOrder()));
                                return item;
                            })));

            //6.7如果是工厂模式,处理列表显示信息
            List<AlarmOperateAssessEntity> factoryList = new ArrayList<>();
            //6.1.如果是工厂用户,进行结果集组合并;
            if (checkModel == FACTORY_MODE) {
                Map<String, List<AlarmOperateAssessEntity>> wsSeriesMap = operateAssessEntityList
                        .stream()
                        .collect(
                                Collectors.groupingBy(AlarmOperateAssessEntity::getWorkshopId,
                                        Collectors.collectingAndThen(Collectors.toCollection(ArrayList::new), item -> item)));
                seriesMap = new HashedMap();
                List wsMapkeys = new ArrayList(wsSeriesMap.keySet());
                //6.1各分组数据按照日期pattern合并
                for (int i = 0; i < wsMapkeys.size(); i++) {
                    int finalIndex = i;
                    String wsName = workshopEntityList.stream().filter(ws -> ws.getStdCode().equals(wsMapkeys.get(finalIndex).toString())).findFirst().orElse(new WorkshopEntity()).getSname();

                    //6.2取出各个车间的装置分组
                    Map<String, List<AlarmOperateAssessEntity>> subMap = wsSeriesMap.get(wsMapkeys.get(i)).stream().collect(Collectors.groupingBy(AlarmOperateAssessEntity::getGroupByTime,
                            Collectors.collectingAndThen(Collectors.toCollection(ArrayList::new), item -> item)));

                    //6.3分组合并
                    List subMapKeys = new ArrayList(subMap.keySet());
                    Map<String, AlarmOperateAssessEntity> workshopMap = new HashedMap();
                    for (int j = 0; j < subMapKeys.size(); j++) {
                        AlarmOperateAssessEntity entity = subMap.get(subMapKeys.get(j)).stream().findFirst().orElse(new AlarmOperateAssessEntity());
                        entity.setCounts(subMap.get(subMapKeys.get(j)).stream().collect(Collectors.summingLong(AlarmOperateAssessEntity::getCounts)));
                        workshopMap.put(subMapKeys.get(j).toString(), entity);
                    }
                    //6.4分组添加
                    wsSeriesMap.get(wsMapkeys.get(i)).forEach(item -> item.setName(wsName));
                    seriesMap.put(wsName, new ArrayList(workshopMap.values()));
                }
            }
            for (List<AlarmOperateAssessEntity> list : seriesMap.values()) {
                factoryList.addAll(list);
            }
            //车间排序
            factoryList.sort(Comparator.comparing(AlarmOperateAssessEntity::getAlartTime).thenComparing(AlarmOperateAssessEntity::getName));
            //6.5日期补全
            List<Date> datePeriodList = new ArrayList<>();
            String newPattern = StringUtils.replace(StringUtils.replace(datePattern, "-", ""), " ", "");

            Date tempEndTime = shiftDateCalculator.getDisplayEndTime();
            String queryTime = CommonPropertiesReader.getValue("query.time");
            //如果是当天日期
            if (DateUtils.isSameDay(shiftDateCalculator.getDisplayEndTime(), new Date()) &&queryTime!=null&& !queryTime.equals(DateFormatUtils.format(shiftDateCalculator.getDisplayEndTime(), "HH:mm:ss"))) {
                //如果是小于当天10点,则不显示当天日期
                try {
                    if (Long.valueOf(DateFormatUtils.format(shiftDateCalculator.getDisplayEndTime(), "HHmmss")) < Long.valueOf(DateFormatUtils.format(dateTime, "HHmmss"))) {
                        tempEndTime = DateUtils.addDays(shiftDateCalculator.getDisplayEndTime(), -1);
                    }
                } catch (Exception ex) {

                }
            } else {
                tempEndTime = DateUtils.addDays(shiftDateCalculator.getDisplayEndTime(), -1);
            }
            Date startTemp = shiftDateCalculator.getDisplayStartTime();
            switch (dateType) {
                case Day:
                    datePeriodList.add(startTemp);
                    if (!datePeriodList.contains(tempEndTime))
                        datePeriodList.add(tempEndTime);
                    while (Long.valueOf(DateFormatUtils.format(startTemp, newPattern)) < Long.valueOf(DateFormatUtils.format(tempEndTime, newPattern))) {
                        if (!datePeriodList.contains(startTemp))
                            datePeriodList.add(startTemp);
                        startTemp = DateUtils.addDays(startTemp, 1);
                    }
                    break;
                case Week:
                    datePeriodList.add(startTemp);
                    while (Long.valueOf(DateFormatUtils.format(startTemp, newPattern)) <= Long.valueOf(DateFormatUtils.format(tempEndTime, newPattern))) {
                        if (!datePeriodList.contains(startTemp))
                            datePeriodList.add(startTemp);
                        startTemp = DateUtils.addDays(startTemp, 7);
                    }
                    break;
                case Month:
                    datePeriodList.add(startTemp);
                    if (!datePeriodList.contains(tempEndTime))
                        datePeriodList.add(tempEndTime);
                    while (Long.valueOf(DateFormatUtils.format(startTemp, newPattern)) < Long.valueOf(DateFormatUtils.format(tempEndTime, newPattern))) {
                        if (!datePeriodList.contains(startTemp))
                            datePeriodList.add(startTemp);
                        startTemp = DateUtils.addMonths(startTemp, 1);
                    }
                    break;
                case Hour:
                    Date endTemp = shiftDateCalculator.getDisplayEndTime();
                    datePeriodList.add(startTemp);
                    while (Long.valueOf(DateFormatUtils.format(startTemp, newPattern)) <= Long.valueOf(DateFormatUtils.format(endTemp, newPattern))) {
                        if (!datePeriodList.contains(startTemp))
                            datePeriodList.add(startTemp);
                        startTemp = DateUtils.addHours(startTemp, 1);
                    }
                    break;
            }

            Map<String, List<AlarmOperateAssessEntity>> finalSeriesMap = seriesMap;
            List keys = new ArrayList(finalSeriesMap.keySet());
            datePeriodList.stream().forEach(date -> {
                for (int i = 0; i < keys.size(); i++) {
                    switch (dateType) {
                        case Day:
                            if (!finalSeriesMap.get(keys.get(i)).stream().anyMatch(item -> DateFormatUtils.format(item.getStartTime() == null ? new Date() : item.getStartTime(), datePattern).equals(DateFormatUtils.format(date, datePattern)))) {
                                AlarmOperateAssessEntity entity = new AlarmOperateAssessEntity();
                                entity.setName(String.valueOf(keys.get(i)));
                                entity.setAlartTime(date);
                                entity.setCounts(0L);
                                entity.setStartTime(DateUtils.setHours(entity.getAlartTime(), calendar.get(Calendar.HOUR_OF_DAY)));
                                entity.setEndDate(DateUtils.addDays(DateUtils.addHours(entity.getAlartTime(), calendar.get(Calendar.HOUR_OF_DAY)), 1));
                                finalSeriesMap.get(keys.get(i)).add(entity);
                            }
                            break;
                        case Week:
                            if (!finalSeriesMap.get(keys.get(i)).stream().anyMatch(item -> DateFormatUtils.format(item.getStartTime() == null ? new Date() : item.getStartTime(), datePattern).equals(DateFormatUtils.format(date, datePattern)))) {
                                AlarmOperateAssessEntity entity = new AlarmOperateAssessEntity();
                                entity.setName(String.valueOf(keys.get(i)));
                                entity.setAlartTime(date);
                                entity.setCounts(0L);
                                entity.setStartTime(DateUtils.setHours(entity.getAlartTime(), calendar.get(Calendar.HOUR_OF_DAY)));
                                entity.setEndDate(DateUtils.addWeeks(DateUtils.addHours(entity.getAlartTime(), calendar.get(Calendar.HOUR_OF_DAY)), 1));
                                finalSeriesMap.get(keys.get(i)).add(entity);
                            }
                            break;
                        case Month:
                            if (!finalSeriesMap.get(keys.get(i)).stream().anyMatch(item -> DateFormatUtils.format(item.getStartTime() == null ? new Date() : item.getStartTime(), datePattern).equals(DateFormatUtils.format(date, datePattern)))) {
                                AlarmOperateAssessEntity entity = new AlarmOperateAssessEntity();
                                entity.setName(String.valueOf(keys.get(i)));
                                entity.setAlartTime(date);
                                entity.setCounts(0L);
                                entity.setStartTime(DateUtils.setHours(entity.getAlartTime(), calendar.get(Calendar.HOUR_OF_DAY)));
                                entity.setEndDate(DateUtils.addMonths(DateUtils.addHours(entity.getAlartTime(), calendar.get(Calendar.HOUR_OF_DAY)), 1));
                                finalSeriesMap.get(keys.get(i)).add(entity);
                            }
                            break;
                        case Hour:
                            if (!finalSeriesMap.get(keys.get(i)).stream().anyMatch(item -> DateFormatUtils.format(item.getStartTime() == null ? new Date() : item.getStartTime(), datePattern).equals(DateFormatUtils.format(date, datePattern)))) {
                                AlarmOperateAssessEntity entity = new AlarmOperateAssessEntity();
                                entity.setName(String.valueOf(keys.get(i)));
                                entity.setAlartTime(date);
                                entity.setCounts(0L);
                                entity.setStartTime(DateUtils.setHours(entity.getAlartTime(), calendar.get(Calendar.HOUR_OF_DAY)));
                                entity.setEndDate(DateUtils.addHours(entity.getStartTime(), 1));
                                finalSeriesMap.get(keys.get(i)).add(entity);
                            }
                            break;
                    }
                }
            });

            //6.6日期进行排序
            for (int i = 0; i < keys.size(); i++) {
                if (keys.get(i) == null) continue;
                List list = finalSeriesMap.get(keys.get(i)).stream().sorted(Comparator.comparing(AlarmOperateAssessEntity::getAlartTime, Comparator.naturalOrder())).collect(Collectors.toList());
                if (finalSeriesMap.containsKey(keys.get(i).toString()))
                    finalSeriesMap.remove(keys.get(i).toString());
                finalSeriesMap.put(keys.get(i).toString(), list);
            }

            Date currentDate = new Date();
            //7.计算xAxis
            for (int i = 0; i < keys.size(); i++) {
                List<String> dateList = new ArrayList<>();
                List<Long> countsList = new ArrayList<>();
                List<String> tipList = new ArrayList<>();
                AlarmOperateAssessChartEntity entity = new AlarmOperateAssessChartEntity();
                //7.1计算日期
                for (AlarmOperateAssessEntity item : finalSeriesMap.get(keys.get(i))) {
                    if (!item.getCounts().equals(0L)) {
                        AlarmOperateAssessEntity listEntity = new AlarmOperateAssessEntity();
                        listEntity.setCounts(item.getCounts());
                        listEntity.setStartTime(item.getStartTime());
                        listEntity.setEndDate(item.getEndDate());
                        listEntity.setName(item.getName());
                        listEntity.setId(item.getId());
                        listEntity.setGroupByTime(item.getGroupByTime());
                        listEntity.setWorkshopId(item.getWorkshopId());
                        resList.add(listEntity);
                    }
                    switch (dateType) {
                        case Week:
                            dateList.add(DateFormatUtils.format(item.getAlartTime(), DAY_PATTERN));
                            countsList.add(item.getCounts());
                            if (item.getEndDate().getTime() >= currentDate.getTime()) {
                                tipList.add("从: " + DateFormatUtils.format(item.getStartTime(), FULL_DATETIME_PATTERN)
                                        + " 至: " + DateFormatUtils.format(currentDate, FULL_DATETIME_PATTERN)
                                        + "<br>" + item.getName() + ": " + item.getCounts());
                            } else {
                                tipList.add("从: " + DateFormatUtils.format(item.getStartTime(), FULL_DATETIME_PATTERN)
                                        + " 至: " + DateFormatUtils.format(item.getEndDate(), FULL_DATETIME_PATTERN)
                                        + "<br>" + item.getName() + ": " + item.getCounts());
                            }
                            break;
                        case Day:
                            dateList.add(DateFormatUtils.format(item.getAlartTime(), datePattern));
                            countsList.add(item.getCounts());
                            if (item.getEndDate().getTime() >= currentDate.getTime()) {
                                tipList.add("从: " + DateFormatUtils.format(item.getStartTime(), FULL_DATETIME_PATTERN)
                                        + " 至: " + DateFormatUtils.format(currentDate, FULL_DATETIME_PATTERN)
                                        + "<br>" + item.getName() + ": " + item.getCounts());
                            } else {
                                tipList.add("从: " + DateFormatUtils.format(item.getStartTime(), FULL_DATETIME_PATTERN)
                                        + " 至: " + DateFormatUtils.format(item.getEndDate(), FULL_DATETIME_PATTERN)
                                        + "<br>" + item.getName() + ": " + item.getCounts());
                            }
                            break;
                        case Hour:
                            dateList.add(DateFormatUtils.format(item.getAlartTime(), datePattern));
                            item.setGroupByTime(DateFormatUtils.format(item.getAlartTime(), datePattern));
                            countsList.add(item.getCounts());
                            if (item.getEndDate().getTime() >= currentDate.getTime()) {
                                tipList.add("从: " + DateFormatUtils.format(item.getStartTime(), FULL_DATETIME_PATTERN)
                                        + " 至: " + DateFormatUtils.format(currentDate, FULL_DATETIME_PATTERN)
                                        + "<br>" + item.getName() + ": " + item.getCounts());
                            } else {
                                tipList.add("从: " + DateFormatUtils.format(item.getStartTime(), FULL_DATETIME_PATTERN)
                                        + " 至: " + DateFormatUtils.format(item.getEndDate(), FULL_DATETIME_PATTERN)
                                        + "<br>" + item.getName() + ": " + item.getCounts());
                            }

                            break;
                        case Month:
                            dateList.add(DateFormatUtils.format(item.getAlartTime(), datePattern));
                            countsList.add(item.getCounts());
                            if (item.getEndDate().getTime() >= currentDate.getTime()) {
                                tipList.add("从: " + DateFormatUtils.format(item.getStartTime(), FULL_DATETIME_PATTERN)
                                        + " 至: " + DateFormatUtils.format(currentDate, FULL_DATETIME_PATTERN)
                                        + "<br>" + item.getName() + ": " + item.getCounts());
                            } else {
                                tipList.add("从: " + DateFormatUtils.format(item.getStartTime(), FULL_DATETIME_PATTERN)
                                        + " 至: " + DateFormatUtils.format(item.getEndDate(), FULL_DATETIME_PATTERN)
                                        + "<br>" + item.getName() + ": " + item.getCounts());
                            }
                            break;
                    }
                }
                entity.setId(finalSeriesMap.get(keys.get(i)).stream().filter(o -> o.getId() != null).findFirst().orElse(new AlarmOperateAssessEntity()).getId());
                entity.setName(String.valueOf(keys.get(i)));
                entity.setCounts(countsList);
                entity.setTip(tipList);
                entity.setXaxis(dateList);
                entity.setTotalCounts(finalSeriesMap.get(keys.get(i)).stream().mapToLong(item -> item.getCounts()).sum());
                chartEntityList.add(entity);
            }
            if (checkModel == FACTORY_MODE) {
                resList = factoryList;
            }
        } catch (Exception ex) {
            throw ex;
        }
        resList = resList.stream().sorted(Comparator.comparing(AlarmOperateAssessEntity::getStartTime).thenComparing(AlarmOperateAssessEntity::getName, ComparatorList.orderByASC())).collect(Collectors.toList());
        //按照装置或者车间简称进行排序
        chartEntityList = chartEntityList.stream().sorted(Comparator.comparing(AlarmOperateAssessChartEntity::getName, ComparatorList.orderByASC())).collect(Collectors.toList());
        if (chartEntityList != null && chartEntityList.size() >= 1)
            chartEntityList.get(0).setList(resList);
        for (int i = 0; i < chartEntityList.size(); i++) {
            //7.2处理颜色循环显示
            //蓝色#6699VCC  灰绿#669999  紫色#CC99CC  兰色#66CCCC 浅蓝#9999CC五种颜色
            switch (i % 5) {
                case 0:
                    chartEntityList.get(i).setColor("#6699CC");
                    break;
                case 1:
                    chartEntityList.get(i).setColor("#669999");
                    break;
                case 2:
                    chartEntityList.get(i).setColor("#CC99CC");
                    break;
                case 3:
                    chartEntityList.get(i).setColor("#66CCCC");
                    break;
                case 4:
                    chartEntityList.get(i).setColor("#9999CC");
                    break;
            }
        }
        return chartEntityList;
    }

    /**
     * 获取报警操作评估首页统计值数据
     *
     * @param startTime  开始日期
     * @param endTime    结束日期
     * @param unitCodes    装置编码集合
     * @param prdtIds    生产单元ID集合
     * @param checkModel 模式 0:装置模式,1:工厂车间模式
     * @param topType Top20,Top10切换选择
     * @return
     * @throws Exception
     * <AUTHOR> 2017-10-25
     */
    @Override
    public List<AlarmEventExLocationEntity> getAlarmOperateAssessTop20(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, int checkModel, Integer topType) throws Exception {

        if (unitCodes != null && unitCodes.length == 0) unitCodes = null;
        if (prdtIds != null && prdtIds.length == 0) prdtIds = null;
        if (checkModel == FACTORY_MODE) {
            unitCodes = basicDataService.getUnitListByWorkshopIds(unitCodes, true).stream().map(UnitEntity::getStdCode).toArray(String[]::new);
        }
        try {
            List<AlarmEventExLocationEntity> alarmEventExEntityList = new ArrayList<>();
            List<Object[]> objList = alarmEventRepository.getAlarmOperateTop20(startTime, endTime, unitCodes, prdtIds, topType);
            for (Object[] objArr : objList) {
                alarmEventExEntityList.add(new AlarmEventExLocationEntity(
                        objArr[0].toString(),
                        objArr[1].toString(),
                        NumberUtils.toLong(objArr[2].toString()),
                        NumberUtils.toInt(objArr[3].toString()),
                        objArr[4].toString(),
                        objArr[5].toString(),
                        NumberUtils.toLong(objArr[6].toString()),
                        NumberUtils.toLong(objArr[7].toString()),
                        objArr[8].toString()
                ));
            }
            List<UnitEntity> unitList = basicDataService.getUnitList(true);
            alarmEventExEntityList.forEach(item -> item.setUnitName(unitList.stream().filter((unit) -> unit.getStdCode().equals(item.getUnitId())).findFirst().orElse(new UnitEntity()).getSname()));
            return alarmEventExEntityList;
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取报警操作评估获取操作数详情
     *
     * @param startTime  开始日期
     * @param endTime    结束日期
     * @param id         标识(装置或者生产单元)
     * @param type       装置|单元
     * @param checkModel 模式 0:装置模式,1:工厂车间模式
     * @param page       分页信息
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-9
     */
    @Override
    public PaginationBean<AlarmEventEntity> getAlarmOperateAsseessDetail(Date startTime, Date endTime, String id, CommonEnum.EquipmentTypeEnum type, int checkModel, Pagination page) throws Exception {
        try {
            endTime = DateUtils.addSeconds(endTime, -1);
            List<UnitEntity> unitList = basicDataService.getUnitList(true);
            Long[] prdtIds = null;
            String[] unitCodes = null;


            if (CommonEnum.EquipmentTypeEnum.PrdtCell.equals(type)) {
                prdtIds = new Long[]{Long.valueOf(id)};
            } else if(CommonEnum.EquipmentTypeEnum.Unit.equals(type)){
                unitCodes = new String[]{id};
            }
            if (checkModel == FACTORY_MODE) {
                unitCodes = basicDataService.getUnitListByWorkshopIds(new String[]{id}, true).stream().map(UnitEntity::getStdCode).toArray(String[]::new);
            }
            PaginationBean<AlarmEvent> pageList = alarmEventRepository.getAlarmEventListByUnitId(startTime, endTime, unitCodes, prdtIds, page);
            PaginationBean<AlarmEventEntity> returnList = new PaginationBean<>(page, pageList.getTotal());
            returnList.setPageList(ObjectConverter.listConverter(pageList.getPageList(), AlarmEventEntity.class));
            returnList.getPageList().stream().forEach(item -> {
                pageList.getPageList().stream().forEach(ae -> {
                    if (item.getEventId().equals(ae.getEventId())) {
                        item.setUnitName(unitList.stream().filter(unit -> ae.getAlarmPoint().getPrdtCell().getUnitId().equals(unit.getStdCode())).findFirst().orElse(new UnitEntity()).getSname());
                        item.setPrdtCellName(ae.getAlarmPoint().getPrdtCell().getSname());
                        item.setTag(ae.getAlarmPoint().getTag());
                        item.setAlarmFlagName(ae.getAlarmFlag().getName());
                        item.setEventTypeName(ae.getEventType().getName());
                        item.setCraftRank(ae.getAlarmPoint().getCraftRank());
                    }
                });
            });
            return returnList;
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 查询最频繁报警详情集合
     *
      * <AUTHOR> 2018-04-13
     * @param alarmPointTag 位号
     * @param alarmFlagId 报警标识id
     * @param startTime 发生时间
     * @param endTime 报结束时间时间
     * @param page 查询分页对象
     * @throws Exception 
     * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity实体分页对象
     */
    public PaginationBean<AlarmEventEntity> getOperateDtail(String alarmPointTag, Long alarmFlagId, Date startTime, Date endTime, Pagination page) throws Exception{
        List<UnitEntity> units = basicDataService.getUnitList(true);
//        Long[] eventTypeIds = basicDataService.getEventTypeIdsByParentId(30L);

        Long[] eventTypeIds = new Long[]{1006l, 1003l, 1004l, 3001l, 3002l, 3003l, 3004l, 3005l, 3006l, 3007l, 300101l, 300102l};

//        eventTypeIds = Arrays.stream(ArrayUtils.add(eventTypeIds, CommonEnum.EventTypeEnum.ConfirmedEvent.getIndex())).toArray(Long[]::new);
        PaginationBean<AlarmEvent> listAlarmEvent = alarmEventRepository.getAlarmDtail(alarmPointTag,alarmFlagId,eventTypeIds,startTime,endTime, CommonEnum.TimeFilterTypeEnum.StartTime.getName(),page);
        PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<AlarmEventEntity>(page,
                listAlarmEvent.getTotal());
        returnAlarmEvent.setPageList(ObjectConverter.listConverter(listAlarmEvent.getPageList(), AlarmEventEntity.class));
        // 映射字段
        for (int i = 0; i < returnAlarmEvent.getPageList().size(); i++) {
            AlarmEventEntity alarmEventEntity = returnAlarmEvent.getPageList().get(i);
            AlarmEvent alarmEvent = listAlarmEvent.getPageList().get(i);
            // 填充装置简称
            UnitEntity unit = units.stream().filter(u -> alarmEvent.getAlarmPoint().getPrdtCell().getUnitId().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
            alarmEventEntity.setUnitName(unit.getSname());
            // 填充生产单元简称
            alarmEventEntity.setPrdtCellName(alarmEvent.getAlarmPoint().getPrdtCell().getSname());
            // 填充报警点位号
            alarmEventEntity.setAlarmPointTag(alarmEvent.getAlarmPoint().getTag());
            // 填充报警标识名称
            alarmEventEntity.setAlarmFlagName(alarmEvent.getAlarmFlag().getName());
            // 填充事件类型
            alarmEventEntity.setEventTypeName(alarmEvent.getEventType().getName());
            // 填充级别
            alarmEventEntity.setCraftRank(alarmEvent.getAlarmPoint().getCraftRank());
            alarmEventEntity.setCraftRankName(alarmEventEntity.getCraftRankName());
            //工艺卡片值 联锁值赋值
            Double culv=alarmEvent.getAlarmPoint().getCraftUpLimitValue();//工艺卡片上限值
            Double cdlv=alarmEvent.getAlarmPoint().getCraftDownLimitValue();//工艺卡片下限值
            Integer culi=alarmEvent.getAlarmPoint().getCraftUpLimitInclude();//工艺卡片上限值是否包含(1是；0否)
            Integer cdli=alarmEvent.getAlarmPoint().getCraftDownLimitInclude();//工艺卡片下限值是否包含(1是；0否)
            Double iulv=alarmEvent.getAlarmPoint().getInterlockUpLimitValue();//联锁上限值
            Double idlv=alarmEvent.getAlarmPoint().getInterlockDownLimitValue();//联锁下限值
            Integer iuli=alarmEvent.getAlarmPoint().getInterlockUpLimitInclude();//联锁上限值是否包含(1是；0否)
            Integer idli=alarmEvent.getAlarmPoint().getInterlockDownLimitInclude();//联锁下限值是否包含(1是；0否)
            if(culv!=null&&cdlv!=null){
                String culvStr = changeDouble(culv);
                String cdlvStr = changeDouble(cdlv);
                alarmEventEntity.setCraftLimitValue(cdlvStr+"~"+culvStr);
            }else if(culv!=null&&cdlv==null){
                if(culi!=null && culi.intValue()==1){
                    String culvStr = changeDouble(culv);
                    alarmEventEntity.setCraftLimitValue("≤"+culvStr);
                }else if(culi!=null && culi.intValue()==0){
                    String culvStr = changeDouble(culv);
                    alarmEventEntity.setCraftLimitValue("<"+culvStr);
                }
            }else if(culv==null&&cdlv!=null){
                if(cdli!=null && cdli.intValue()==1){
                    String cdlvStr = changeDouble(cdlv);
                    alarmEventEntity.setCraftLimitValue("≥"+cdlvStr);
                }else if(cdli!=null && cdli.intValue()==0){
                    String cdlvStr = changeDouble(cdlv);
                    alarmEventEntity.setCraftLimitValue(">"+cdlvStr);
                }
            }else if(culv==null&&cdlv==null){
                alarmEventEntity.setCraftLimitValue("");
            }
            if(iulv!=null&&idlv!=null){
                String iulvStr = changeDouble(iulv);
                String idlvStr = changeDouble(idlv);
                alarmEventEntity.setInterlockLimitValue(idlvStr+"~"+iulvStr);
            }else if(iulv!=null&&idlv==null){
                if(iuli.intValue()==1){
                    String iulvStr = changeDouble(iulv);
                    alarmEventEntity.setInterlockLimitValue("≤"+iulvStr);
                }else if(iuli.intValue()==0){
                    String iulvStr = changeDouble(iulv);
                    alarmEventEntity.setInterlockLimitValue("<"+iulvStr);
                }
            }else if(iulv==null&&idlv!=null){
                if(idli.intValue()==1){
                    String idlvStr = changeDouble(idlv);
                    alarmEventEntity.setInterlockLimitValue("≥"+idlvStr);
                }else if(idli.intValue()==0){
                    String idlvStr = changeDouble(idlv);
                    alarmEventEntity.setInterlockLimitValue(">"+idlvStr);
                }
            }else if(iulv==null&&idlv==null){
                alarmEventEntity.setInterlockLimitValue("");
            }
        }
        return returnAlarmEvent;
    }
    //Double类型转String
    private String changeDouble(Double num){
        if((num+"").endsWith(".0")){
            return num.intValue()+"";
        }
        return num+"";
    }
}
