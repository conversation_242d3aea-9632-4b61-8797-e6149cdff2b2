package com.pcitc.opal.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/*
 * 公共枚举
 * 模块编号：pcitc_opal_common_class_CommonEnum
 * 作       者：dongsheng.zhao
 * 创建时间：2017/09/10
 * 修改编号：1
 * 描       述：公共枚举
 */
public class CommonEnum {

    /**
     * 页面模式枚举(1新增,2编辑,3查看)
     */
    public enum PageModelEnum {
        NewAdd("新增", 1), Edit("编辑", 2), View("查看", 3);

        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private PageModelEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (PageModelEnum c : PageModelEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 是否启用枚举(0否、1是)
     */
    public enum InUseEnum {
        Yes("是", 1), No("否", 0);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private InUseEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (InUseEnum c : InUseEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 日期类型枚举
     *
     * <AUTHOR> 2017-12-22
     */
    public enum DateTypeEnum {
        Hour("hour", 0), Day("day", 1), Week("week", 2), Month("month", 3);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private DateTypeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (DateTypeEnum c : DateTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        public static DateTypeEnum fromValue(String dateType) {
            DateTypeEnum dateTypeEnum = null;
            switch (dateType) {
                case "hour":
                    dateTypeEnum = DateTypeEnum.Hour;
                    break;
                case "day":
                    dateTypeEnum = DateTypeEnum.Day;
                    break;
                case "week":
                    dateTypeEnum = DateTypeEnum.Week;
                    break;
                case "month":
                    dateTypeEnum = DateTypeEnum.Month;
                    break;
                default:
                    break;
            }
            return dateTypeEnum;
        }
    }

    /**
     * 机构单元类型枚举
     *
     * <AUTHOR> 2017-9-26
     *         机构单元类型枚举(1生产单位，2职能单位)
     */
    public enum OUTypeEnum {
        PrdtCell("生产单位", 1), FunctionCell("职能单位", 2);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private OUTypeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (OUTypeEnum c : OUTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 监测类型枚举
     *
     * <AUTHOR> 2017-10-9
     *         1物料；2能源；3质量;4其他
     */
    public enum MonitorTypeEnum {
        Technology("工艺", 4),
        Device("设备", 5),Safe("安全", 6),Other("生产", 7),Nothing("-", 8);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private MonitorTypeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (MonitorTypeEnum c : MonitorTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 报警标识枚举
     *
     * <AUTHOR> 2017-10-9
     *         1PVHH、2PVHI、3PVLO、4PVLL
     */
    public enum AlarmFlagEnum {
        PVHH("PVHH", 1), PVHI("PVHI", 2), PVLO("PVLO", 3), PVLL("PVLL", 4), BADOC("BADOC", 9), BADPV("BADPV", 10),IOP_("IOP_",12),IOP("IOP",16);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private AlarmFlagEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmFlagEnum c : AlarmFlagEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return "";
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 报警优先级枚举
     *
     * <AUTHOR> 2017-10-9
     *         1紧急、2重要、3一般
     */
    public enum AlarmPriorityEnum {
        Emergency("紧急", 1), Importance("重要", 2), Normal("一般", 3);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private AlarmPriorityEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmPriorityEnum c : AlarmPriorityEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public static List<Integer> getIndexList() {
            List<Integer> indexList = new ArrayList<Integer>();
            for (AlarmPriorityEnum c : AlarmPriorityEnum.values()) {
                indexList.add(c.getIndex());
            }
            return indexList;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }


    }

    /**
     * 仪表类型枚举
     *
     * <AUTHOR> 2017-10-9
     *         1监测表；2控制表 4其他
     */
    public enum InstrmtTypeEnum {
        MonitoringInstrmt("监测表", 1), ControlInstrmt("控制表", 2),Other("其他", 4),Nothing("-", 5);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private InstrmtTypeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (InstrmtTypeEnum c : InstrmtTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 仪表优先级枚举
     *
     * <AUTHOR> 2019-09-25
     *         1紧急；2重要；3一般
     */
    public enum InstrmtPriorityEnum {
        MonitoringInstrmt("紧急", 1), ControlInstrmt("重要", 2),Other("一般", 3),NoLevel("无级别",9);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private InstrmtPriorityEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (InstrmtPriorityEnum c : InstrmtPriorityEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 是否虚表
     *
     * <AUTHOR> 2017-10-9
     *         0 否；1 是
     */
    public enum VirtualRealityFlagEnum {
        YES("是", 1), NO("否", 0);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private VirtualRealityFlagEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (VirtualRealityFlagEnum c : VirtualRealityFlagEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 发生时间枚举
     *
     * <AUTHOR> 2017-10-9
     *         0自定义、1近七天、2近半个月、3近一个月、4近两个月、5近三个月、6近半年、7近一年
     */
    public enum StartTimeEnum {
        Custom("自定义", 0), SevenDay("近七天", 1), HalfMonth("近半个月", 2), OneMonth("近一个月", 3), TwoMonth("近两个月", 4), ThreeMonth("近三个月", 5), HalfYear("近半年", 6), OneYear("近一年", 7);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private StartTimeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (StartTimeEnum c : StartTimeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 报警级别枚举
     *
     * <AUTHOR> 2017-10-17
     *         1超负荷的，2反应性的，3稳定的，4鲁棒的，5可预测的
     */
    public enum AlarmLevelEnum {
        Overloaded("超负荷的", 1), Reactive("反应性的", 2), Stable("稳定的", 3), Robust("鲁棒的", 4), Predictive("可预测的", 5);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private AlarmLevelEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmLevelEnum c : AlarmLevelEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 评估等级枚举（根据系统运行参数中“报警评估等级信息”为 2，用此枚举展示）
     *
     * <AUTHOR> 2019-10-18
     *         1超负荷的，2反应性的，3稳定的，4鲁棒的，5可预测的
     */
    public enum AlarmLevelGradeEnum {
        Overloaded("等级五", 1), Reactive("等级四", 2), Stable("等级三", 3), Robust("等级二", 4), Predictive("等级一", 5);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private AlarmLevelGradeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmLevelGradeEnum c : AlarmLevelGradeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 报警级别枚举
     *
     * <AUTHOR> 2017-10-17
     *         1增加，2降低，3持平
     */
    public enum StateComparisonEnum {
        Increase("增加", 1), Lower("降低", 2), Unchange("持平", 3);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private StateComparisonEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (StateComparisonEnum c : StateComparisonEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 是否搁置枚举(0否、1是)
     */
    public enum InShelvedEnum {
        Yes("是", 1), No("否", 0);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private InShelvedEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (InShelvedEnum c : InShelvedEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 是否屏蔽枚举(0否、1是)
     */
    public enum InSuppressedEnum {
        Yes("是", 1), No("否", 0);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private InSuppressedEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (InSuppressedEnum c : InSuppressedEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /***
     * 分组函数参数分组类型枚举
     * <AUTHOR> 2017-11-2
     */
    public enum QueryTypeEnum {
        /**
         * 分钟
         */
        MINUTE("MINUTE", 1),
        /**
         * 小时
         */
        HOUR("HOUR", 2),
        /**
         * 天
         */
        DAY("DAY", 3),
        /**
         * 周
         */
        WEEK("WEEK", 4),
        /**
         * 月份
         */
        MONTH("MONTH", 5),
        /**
         * 季度
         */
        QUARTER("QUARTER", 6),
        /**
         * 年
         */
        YEAR("YEAR", 7);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private QueryTypeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (QueryTypeEnum c : QueryTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 报警事件类型
     *
     * <AUTHOR> 2017-11-4
     */
    public enum EventTypeEnum {
        OperateEvent("操作记录", 30L),
        ProcessEvent("过程报警", 1001L),
        RecoverUnconfirmedEvent("过程报警恢复未确认", 1002L),
        RecoverConfirmedEvent("过程报警恢复已确认", 1003L),
        NoRecoverConfirmedEvent("过程报警未恢复已确认", 1004L),
        RecoverEvent("过程报警恢复", 1005L),
        ConfirmedEvent("过程报警确认", 1006L),
        ShieldEvent("屏蔽报警", 3003L),
        ShelveEvent("搁置报警", 3005L),
        ChangeEvent("操作记录", 3001L),
        ChangeRecordEvent("变更记录", 300101L),
        AdjustRecordEvent("调整记录", 300102L);
        private String name;
        private Long index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private EventTypeEnum(String name, Long index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (EventTypeEnum c : EventTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Long getIndex() {
            return index;
        }

        public void setIndex(Long index) {
            this.index = index;
        }
    }

    /**
     * 工艺卡片上限值是否包含枚举
     *
     * <AUTHOR> 2017-11-8
     */
    public enum CraftUpLimitEnum {
        YES("是", 1), NO("否", 0);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private CraftUpLimitEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (CraftUpLimitEnum c : CraftUpLimitEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 工艺卡片下限值是否包含枚举
     *
     * <AUTHOR> 2017-11-8
     */
    public enum CraftDownLimitEnum {
        YES("是", 1), NO("否", 0);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private CraftDownLimitEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (CraftDownLimitEnum c : CraftDownLimitEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 联锁下限值是否包含枚举
     *
     * <AUTHOR> 2017-11-8
     */
    public enum CraftInterlockDownLimitEnum {
        YES("是", 1), NO("否", 0);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private CraftInterlockDownLimitEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (CraftInterlockDownLimitEnum c : CraftInterlockDownLimitEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 联锁上限值是否包含枚举
     *
     * <AUTHOR> 2017-11-8
     */
    public enum CraftInterlockUpLimitEnum {
        YES("是", 1), NO("否", 0);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private CraftInterlockUpLimitEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (CraftInterlockUpLimitEnum c : CraftInterlockUpLimitEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 工艺卡片级别
     *
     * <AUTHOR> 2017-11-8
     */
    public enum CraftRankEnum {
        A("A", 1), B("B", 2);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private CraftRankEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (CraftRankEnum c : CraftRankEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 过滤时间的类型枚举
     * 1：过滤发生时间，2：过滤报警时间
     *
     * <AUTHOR>
     */
    public enum TimeFilterTypeEnum {
        StartTime("StartTime", 1), ALarmTime("ALarmTime", 2);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private TimeFilterTypeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (TimeFilterTypeEnum c : TimeFilterTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 设备类别枚举
     *
     * <AUTHOR> 2018-01-05
     */
    public enum EquipmentTypeEnum {
        PrdtCell("prdt", 1), Unit("unit", 2), Factory("factory", 3);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private EquipmentTypeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (EquipmentTypeEnum c : EquipmentTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public static EquipmentTypeEnum fromValue(String type) {
            EquipmentTypeEnum equipmentEnum = null;
            switch (type) {
                case "prdt":
                case "3":
                    equipmentEnum = EquipmentTypeEnum.PrdtCell;
                    break;
                case "unit":
                case "2":
                    equipmentEnum = EquipmentTypeEnum.Unit;
                    break;
                case "factory":
                case "1":
                    equipmentEnum = EquipmentTypeEnum.Factory;
                    break;
                default:
                    break;
            }
            return equipmentEnum;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 报警变更方案状态枚举（0未提交；1已驳回；2已提交；3已审核；4已下发；5已完成）
     *
     * <AUTHOR> 2018-01-19
     */
    public enum AlarmChangePlanStatusEnum {
        UnSubmit("未提交", 0),
        Reject("已驳回", 1),
        Submitted("已提交", 2),
        Audited("已审核", 3),
        Issued("已下发", 4),
        Finished("已完成", 5);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        AlarmChangePlanStatusEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmChangePlanStatusEnum c : AlarmChangePlanStatusEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 报警变更方案状态枚举（0待审核；1已审核）
     *
     * <AUTHOR> 2018-03-14
     */
    public enum AlarmChangePlanApproStatusEnum {
        Audit("待审核", 0),
        Audited("已审核", 1);

        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        AlarmChangePlanApproStatusEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmChangePlanApproStatusEnum c : AlarmChangePlanApproStatusEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 报警变更事项分类枚举
     * 1：限值，2：优先级，3：是否屏蔽，4：其他
     *
     * <AUTHOR>
     */
    public enum ChangeItemTypeEnum {
        LimitValue("限值", 1), Priority("优先级", 2), InSuppressed("是否屏蔽", 3), Other("其他", 4);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private ChangeItemTypeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (ChangeItemTypeEnum c : ChangeItemTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 变更方案业务类型枚举
     * 1：申请，2：下发，3：确认;
     *
     * <AUTHOR> 2018-01-29
     */
    public enum AlarmChangeBizTypeEnum {
        Apply("申请", 1), Issue("下发", 2), Confirm("确认", 3);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        AlarmChangeBizTypeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmChangeBizTypeEnum c : AlarmChangeBizTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        public static AlarmChangeBizTypeEnum fromValue(int bizType) {
            AlarmChangeBizTypeEnum bizTypeEnum = null;
            switch (bizType) {
                case 1:
                    bizTypeEnum = AlarmChangeBizTypeEnum.Apply;
                    break;
                case 2:
                    bizTypeEnum = AlarmChangeBizTypeEnum.Issue;
                    break;
                case 3:
                    bizTypeEnum = AlarmChangeBizTypeEnum.Confirm;
                    break;
                default:
                    break;
            }
            return bizTypeEnum;
        }
    }
    /**
     * 变更方案业务申请枚举
     * 0:未提交,1:已驳回,2:已提交,3:已完成;
     *
     * <AUTHOR> 2018-01-29
     */
    public enum AlarmChangeBizApplyEnum {
        UnSubmit("未提交", 0),
        Reject("已驳回", 1),
        Submitted("已提交", 2),
        Finished("已完成", 3),
        TecFinished("已完成", 5);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        AlarmChangeBizApplyEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmChangeBizApplyEnum c : AlarmChangeBizApplyEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }
    /**
     * 变更方案业务下发枚举
     * 0:待下发,1:已下发,2:已完成;
     *
     * <AUTHOR> 2018-01-29
     */
    public enum AlarmChangeBizIssueEnum {
        Issue("待下发", 0),
        Issued("已下发", 1);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        AlarmChangeBizIssueEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmChangeBizIssueEnum c : AlarmChangeBizIssueEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }
    /**
     * 变更方案业务确认类型枚举
     * 0:待确认,1:已完成;
     *
     * <AUTHOR> 2018-01-29
     */
    public enum AlarmChangeBizConfirmEnum {
        Confirm("待确认", 0),
        Finished("已完成", 1);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        AlarmChangeBizConfirmEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmChangeBizConfirmEnum c : AlarmChangeBizConfirmEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }
    
    /**
     * 报警变更方案审批记录-审批状态枚举
     * 0:驳回,1:通过;
     *
     * <AUTHOR> 2018-01-30
     */
    public enum AproStatusEnum {
    	Reject("驳回", 0),
        Pass("通过", 1);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        AproStatusEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AproStatusEnum c : AproStatusEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }
    
    /**
     * 报警变更方案附加信息-业务类型枚举
     * 1:下发,2:确认;
     *
     * <AUTHOR> 2018-01-29
     */
    public enum BusinessTypeEnum {
    	Issue("下发", 1), Confirm("确认", 2);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        BusinessTypeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (BusinessTypeEnum c : BusinessTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }
    /**
     * 工艺参数审批状态枚举
     * 审批状态（0未提交；1已提交；2通过；3驳回）
     *
     * <AUTHOR> 2019-04-16
     */
    public enum CraftAproStatusEnum {
        UnSubmit("未提交", 0),
        Submitted("已提交", 1),
        Pass("通过", 2),
        Reject("驳回", 3);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        CraftAproStatusEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (CraftAproStatusEnum c : CraftAproStatusEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }
    /**
     * 报警制度管理分类枚举（1公司级；2工厂级；3车间级）
     *
     * <AUTHOR> 2018-02-28
     */
    public enum AlarmStdManagmtCatgrEnum {
        Company("公司级", 1), Factory("工厂级", 2), Workshop("车间级", 3);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        AlarmStdManagmtCatgrEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmStdManagmtCatgrEnum c : AlarmStdManagmtCatgrEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }
    /**
     * 计量单位未匹配数据枚举（1不存在；2不一致）
     *
     * <AUTHOR> 2018-04-19
     */
    public enum MeasUnitUnmatchEnum {
        None("不存在", 1),  Different("不一致", 2);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        MeasUnitUnmatchEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (MeasUnitUnmatchEnum c : MeasUnitUnmatchEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }
    /**
     * 报警点未匹配枚举（1报警点不存在；2生产单元不一致）
     *
     * <AUTHOR> 2018-04-19
     */
    public enum AlarmPointUnmatchEnum {
        None("报警点不存在", 1),  Different("生产单元不一致", 2);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        AlarmPointUnmatchEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmPointUnmatchEnum c : AlarmPointUnmatchEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 工艺参数报警率得分配置类型枚举
     *
     * <AUTHOR> 2019-12-11
     */
    public enum CraftAlarmRateConfTypeEnum {
        AverageScore("平均报警数得分", 1), HourContinuousScore("24小时持续报警数得分", 2),
        MinutesScore("分钟峰值报警数得分", 3), AverageCoefficient("平均报警数得分系数", 4),
        HourCoefficient("24小时持续报警数得分系数", 5), PeakCoefficient("峰值报警数得分系数", 6),
        AlarmTimelyResponseRateScore("报警响应及时率得分", 7), AlarmTimelyDisposalRateScore("报警处置及时率得分", 8),
        AlarmTimelyResponseRateCoefficient("报警响应及时率得分系数", 9), AlarmTimelyDisposalRateCoefficient("报警处置及时率得分系数", 10),
        ;
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private CraftAlarmRateConfTypeEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (CraftAlarmRateConfTypeEnum c : CraftAlarmRateConfTypeEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 是否发送报警短信（1是；0否）
     */
    public enum InSendMsgEnum {
        Yes("是", 1), No("否", 0);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private InSendMsgEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (InSendMsgEnum c : InSendMsgEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 报警标识（短信配置）（1、高高报PVHH，2、高报PVHI，3、低报PVLO,4、低低报PVLL）
     */
    public enum AlarmFlagNameEnum {
        PVHH("高高报PVHH", 1), PVHI("高报PVHI", 2), PVLO("低报PVLO", 3), PVLL("低低报PVLL", 4);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private AlarmFlagNameEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (AlarmFlagNameEnum c : AlarmFlagNameEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 短信发送状态 （1已发送；2发送失败；）
     */
    public enum SendMsgStatusEnum {
        Success("已发送", 1), Fail("发送失败", 2);
        private String name;
        private int index;

        /**
         * 构造函数
         *
         * @param name  名称
         * @param index 枚举值
         */
        private SendMsgStatusEnum(String name, int index) {
            this.name = name;
            this.index = index;
        }

        public static String getName(int index) {
            for (SendMsgStatusEnum c : SendMsgStatusEnum.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 报警处置及时率和报警响应及时率用
     */
    @AllArgsConstructor
    @Getter
    public enum AlarmResponseAndAlarmHandlingDateTypeEnum {
        /**
         * 秒
         */
        Second("second"),
        /**
         * 分钟
         */
        Minute("minute");
        final private String name;
    }

    /**
     * 报警审查-审查状态
     */
    @AllArgsConstructor
    @Getter
    public enum ExamineStatus{
        Unchanged(-1, "未变更"),
        NotSubmitted(0, "未提交"),
        Submitted(1, "已提交"),
        Passed(2, "已通过"),
        Rejected(3, "已驳回");


        private final Integer status;
        private final String name;

        public static String getName(int index) {
            for (ExamineStatus c : ExamineStatus.values()) {
                if (c.status == index) {
                    return c.name;
                }
            }
            return null;
        }
    }

    /**
     * 数据剔除状态
     */
    @AllArgsConstructor
    @Getter
    public enum DelDataStatus {
        NotDel(1, "未剔除"),
        DelIng(1, "剔除中"),
        EventFail(2, "事件表剔除失败"),
        RecFail(3, "记录表剔除失败"),
        DelFail(4, "剔除失败"),
        DelSuccess(5, "数据已剔除");


        private final Integer status;
        private final String name;

        public static String getName(int index) {
            for (ExamineStatus c : ExamineStatus.values()) {
                if (c.status == index) {
                    return c.name;
                }
            }
            return null;
        }
    }

    /**
     * 报警原因-类型
     */
    @AllArgsConstructor
    @Getter
    public enum ReasonType{
        Technics(1L, "工艺类"),
        Operation(2L, "操作类"),
        Equipment(3L, "设备类"),
        ElectricEquipment(4L, "电气类"),
        PublicWorks(5L, "公共工程"),
        Instrument(6L, "仪表类"),
        RawMaterial(7L, "原料类"),
        Other(8L, "其他");


        private final Long type;
        private final String name;

        public static String getName(Long type) {
            if (type == null) {
                return null;
            }
            for (ReasonType c : ReasonType.values()) {
                if (Objects.equals(c.type, type)) {
                    return c.name;
                }
            }
            return null;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum AnlyStatus {

        not(0, "未分析"),
        already(1, "已分析"),
        submit(2, "已提交"),
        confirm(3, "已确认");

        private final Integer type;
        private final String name;

        public static String getName(Integer type) {
            if (type == null) {
                return null;
            }
            for (AnlyStatus c : AnlyStatus.values()) {
                if (Objects.equals(c.type, type)) {
                    return c.name;
                }
            }
            return null;
        }
    }


}
