package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.common.CommonEnum.PageModelEnum;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.CommonUtil;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.MeasUnitService;
import com.pcitc.opal.pm.bll.entity.MeasUnitEntity;
import com.pcitc.opal.pm.pojo.MeasUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.List;

/*
 * 计量单位业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_MeasUnitImpl
 * 作       者：jiangtao.xue
 * 创建时间：2017-09-25
 * 修改编号：1
 * 描       述：计量单位业务逻辑层实现类
 */
@Service
@Component
public class MeasUnitImpl implements MeasUnitService {

    /**
     * 实例化数据访问层接口
     */
    @Autowired
    private com.pcitc.opal.pm.dao.MeasUnitRepository repo;

    /**
     * 新增计量单位
     *
     * <AUTHOR> 2017-09-25
     * @param measUnitEntity 计量单位实体
     * @throws Exception
     */
    @Override
    public CommonResult addMeasUnit(MeasUnitEntity measUnitEntity) throws Exception {
        // 实体转换为持久层实体
        MeasUnit measUnitPO = ObjectConverter.entityConverter(measUnitEntity, MeasUnit.class);
        // 数据校验
        measUnitValidation(measUnitPO);
        // 赋值  创建人、创建名称、创建时间
        CommonUtil.returnValue(measUnitPO, PageModelEnum.NewAdd.getIndex());
        CommonResult commonResult = repo.addMeasUnit(measUnitPO);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 删除计量单位
     *
     * <AUTHOR> 2017-09-25
     * @param measUnitIds 计量单位ID集合
     * @throws Exception
     */
    @Override
    public CommonResult deleteMeasUnit(Long[] measUnitIds) throws Exception {
        List<MeasUnit> anlyMeasUnitList = repo.getMeasUnit(measUnitIds);
        if (anlyMeasUnitList == null || anlyMeasUnitList.isEmpty())
            return new CommonResult();
        Long[] anlyMeasUnitIdList = anlyMeasUnitList.stream().map(item -> item.getMeasUnitId()).toArray(Long[]::new);
        // 调用DAL删除方法
        CommonResult commonResult = repo.deleteMeasUnit(anlyMeasUnitIdList);

        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 修改计量单位
     *
     * <AUTHOR> 2017-09-25
     * @param measUnitEntity 计量单位实体
     * @throws Exception
     */
    @Override
    public CommonResult updateMeasUnit(MeasUnitEntity measUnitEntity) throws Exception {
        // 实体转换持久层实体
        MeasUnit measUnitPO = ObjectConverter.entityConverter(measUnitEntity, MeasUnit.class);
        // 校验
        measUnitValidation(measUnitPO);
        // 实体转换为持久层实体
        measUnitPO = repo.getSingleMeasUnit(measUnitEntity.getMeasUnitId());
        CommonUtil.objectExchange(measUnitEntity, measUnitPO);
        // 赋值 修改人、修改名称、修改时间
        CommonUtil.returnValue(measUnitPO, PageModelEnum.Edit.getIndex());
        // 调用DAL更新方法
        CommonResult commonResult = repo.updateMeasUnit(measUnitPO);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 通过计量单位ID获取单条数据
     *
     * <AUTHOR> 2017-09-25
     * @param measUnitId 计量单位ID
     */
    @Override
    public MeasUnitEntity getSingleMeasUnit(Long measUnitId) throws Exception {
        MeasUnit measUnit = repo.getSingleMeasUnit(measUnitId);
        return ObjectConverter.entityConverter(measUnit, MeasUnitEntity.class);
    }

    /**
     * 计量单位查询
     *
     * <AUTHOR> 2017-09-25
     * @param page  分页参数
     * @param sign  符号
     * @param name  计量单位名称
     * @param inUse 是否启用
     * @return 计量单位实体（分页）
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<MeasUnitEntity> getMeasUnit(String name,String sign, Integer inUse, Pagination page)
            throws Exception {
        PaginationBean<MeasUnit> listMeasUnit = repo.getMeasUnit(name,sign, inUse, page);
        PaginationBean<MeasUnitEntity> returnMeasUnit = new PaginationBean<MeasUnitEntity>(page,
                listMeasUnit.getTotal());
        returnMeasUnit
                .setPageList(ObjectConverter.listConverter(listMeasUnit.getPageList(), MeasUnitEntity.class));
        return returnMeasUnit;
    }

    /**
     * 校验
     *
     * <AUTHOR> 2017-09-25
     * @param entity 计量单位实体
     * @throws Exception
     */
    private void measUnitValidation(MeasUnit entity) throws Exception {
    	CommonResult commonResult = null;
    	// 实体不能为空
        if (entity == null) {
            throw new Exception("没有计量单位数据！");
        }
        // 调用DAL与数据库相关的校验
        commonResult = repo.measUnitValidation(entity);

        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
    }
}
