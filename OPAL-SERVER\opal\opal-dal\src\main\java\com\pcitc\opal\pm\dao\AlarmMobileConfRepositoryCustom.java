package com.pcitc.opal.pm.dao;

import com.pcitc.opal.pm.pojo.AlarmMobileConf;

import java.util.List;

public interface AlarmMobileConfRepositoryCustom {

    List<AlarmMobileConf> getAlarmMobileConfPointId(Long[] alarmPointId);
    List<AlarmMobileConf> getAlarmMobileConfPointIdDesc(Long[] alarmPointId);

    public List findGroupListInfo();
    public List<Long> getConfListByPointId(Long[] alarmPointIds);
    List<AlarmMobileConf>  getByAlarmPointIdTimeInterval(Long alarmPointId, Long timeInterval);

    List<AlarmMobileConf> getAlarmMobileConfMobileId(Long[] mobileListId);

}
