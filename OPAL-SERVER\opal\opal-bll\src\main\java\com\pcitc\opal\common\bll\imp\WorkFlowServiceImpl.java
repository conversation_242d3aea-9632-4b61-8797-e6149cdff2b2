package com.pcitc.opal.common.bll.imp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.WorkFlowConfig;
import com.pcitc.opal.common.bll.AAAService;
import com.pcitc.opal.common.bll.WorkFlowService;
import com.pcitc.opal.common.bll.entity.AAAOrgUnitEntity;
import com.pcitc.ssc.dps.inte.workflow.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/*
 * 流程服务接口实现
 * 模块编号： pcitc_opal_bll_class_WorkFlowServiceImpl
 * 作       者：xuelei.wang
 * 创建时间：2018/3/7
 * 修改编号：1
 * 描       述：流程服务接口实现
 */

@Service
public class WorkFlowServiceImpl implements WorkFlowService {

    @Autowired
    private WorkFlowConfig config;

    @Autowired
    private AAAService AAAService;

    /**
     * 发起流程
     *
     * @param categoryCode 流程类别编码
     * @param businessId   业务ID
     * @param businessName 业务名称
     * @param businessCode 业务代码
     * @return 流程执行结果
     * <AUTHOR> 2018-03-08
     */
    @Override
    public AppCallResult startWorkFlow(String categoryCode, String businessId,String businessName, String businessCode) throws Exception {
        return restartWorkFlow(categoryCode, businessId,businessName, businessCode);
    }

    /**
     * 重新发起流程
     *
     * @param categoryCode 流程分类编码
     * @param businessId   业务ID
     * @param businessName 业务名称
     * @param businessCode 业务代码
     * @return 流程执行结果
     * <AUTHOR> 2018-03-09
     */
    @Override
    public AppCallResult restartWorkFlow(String categoryCode,String businessId, String businessName, String businessCode) throws Exception {
        StartContext context = new StartContext();

        //1.设置应用ID
        context.setAppId(config.getAppId());

        //2.设置流程分类
        context.setCategoryCode(categoryCode);

        //3.设置流程模板ID
        String workFlowId = getFlowTemplateId(context.getCategoryCode());
        if (StringUtils.isBlank(workFlowId)) {
            throw new Exception("未找到流程模板,流程发起失败!");
        }
        context.setWorkflowId(workFlowId);

        //4.设置用户及组织
        CommonProperty commonProperty = new CommonProperty();
        context.setUserId(commonProperty.getUserId());
        context.setUserName(commonProperty.getUserName());
        AAAOrgUnitEntity org = AAAService.getOrgByUserId(commonProperty.getUserId());
        if (org == null) {
            throw new Exception("未找到组织单元,流程发起失败!");
        }
        context.setOrganiseName(org.getName());
        //当为公共模板时,OrganiseId固定为#templateorgId#
        context.setOrganiseId("#templateorgId#");

        //5.设置流程执行时间
        Date executeDate = new Date();
        context.setExecuteDate(executeDate);
        context.setExecuteDateUtc(executeDate);

        //6.设置业务相关
        if (StringUtils.isBlank(businessName)) {
            throw new Exception("业务名称为空,发起流程失败!");
        }
        if (StringUtils.isBlank(businessId)) {
            throw new Exception("业务ID为空,发起流程失败!");
        }
        context.setBusinessId(businessId);
        context.setBusinessName(businessName);
        if (StringUtils.isNotBlank(businessCode)) {
            context.setBusinessCode(businessCode);
        }

        String json = JSON.toJSONString(context);
        String url = config.getStartUrl();
        AppCallResult result = executePost(json, url);
        return result;
    }

    /**
     * 推进工作流
     *
     * @param taskId 执行的待办ID
     * @return 流程执行结果
     * <AUTHOR> 2018-03-09
     */
    @Override
    public AppCallResult runWorkFlow(String taskId) throws Exception {
        ExecuteContext context = new ExecuteContext();
        //1.设置应用ID
        context.setAppId(config.getAppId());

        //2.设置用户及组织
        CommonProperty commonProperty = new CommonProperty();
        context.setExecutorId(commonProperty.getUserId());
        context.setExecutorName(commonProperty.getUserName());
        AAAOrgUnitEntity org = AAAService.getOrgByUserId(commonProperty.getUserId());
        if (org == null) {
            throw new Exception("未找到组织单元,流程推进失败!");
        }

        //3.设置taskId
        context.setTaskId(taskId);

        //4.设置流程执行时间
        Date executeDate = new Date();
        context.setExecuteDate(executeDate);
        context.setExecuteDateUtc(executeDate);

        String json = JSON.toJSONString(context);
        String url = config.getCompleteUrl();
        AppCallResult result = executePost(json, url);
        return result;
    }

    /**
     * 流程退回
     *
     * @param taskId 执行的待办ID
     * @return 流程执行结果
     * <AUTHOR> 2018-03-09
     */
    @Override
    public AppCallResult revertWorkFlow(String taskId) throws Exception {
        ExecuteContext context = new ExecuteContext();
        //1.设置应用ID
        context.setAppId(config.getAppId());

        //2.设置用户及组织
        CommonProperty commonProperty = new CommonProperty();
        context.setExecutorId(commonProperty.getUserId());
        context.setExecutorName(commonProperty.getUserName());
        AAAOrgUnitEntity org = AAAService.getOrgByUserId(commonProperty.getUserId());
        if (org == null) {
            throw new Exception("未找到组织单元,流程退回失败!");
        }

        //3.设置taskId
        context.setTaskId(taskId);

        //4.设置流程执行时间
        Date executeDate = new Date();
        context.setExecuteDate(executeDate);
        context.setExecuteDateUtc(executeDate);

        String json = JSON.toJSONString(context);
        String url = config.getRevertUrl();
        AppCallResult result = executePost(json, url);
        return result;
    }

    /**
     * 获取流程待办列表
     *
     * @return
     * <AUTHOR> 2018-03-09
     */
    @Override
    public List<ExecuteTaskData> getWorkFlowTodoList() throws Exception {
        PagedList result = new PagedList();
        String url = config.getTodoTaskUrl();
        CommonProperty commonProperty = new CommonProperty();
        url += String.format("/%s/%s",
                config.getAppId(), commonProperty.getUserId());
        String tmpResult = executeGet(url);
        if (StringUtils.isNoneEmpty(tmpResult)) {
            result = JSON.parseObject(tmpResult, PagedList.class);
        }
        return result.getExecuteTaskList();
    }

    /**
     * 获取流程已办列表
     *
     * @return
     * <AUTHOR> 2018-03-09
     */
    @Override
    public List<ExecuteTaskData> getWorkFlowDoneList() throws Exception {
        PagedList result = new PagedList();
        String url = config.getDoneTaskUrl();

        CommonProperty commonProperty = new CommonProperty();
        url += String.format("/%s/%s",
                config.getAppId(), commonProperty.getUserId());
        String tmpResult = executeGet(url);
        if (StringUtils.isNoneEmpty(tmpResult)) {
            result = JSON.parseObject(tmpResult, PagedList.class);
        }
        return result.getExecuteTaskList();
    }

    /**
     * 获取流程Token
     *
     * @return token
     * @throws Exception
     * <AUTHOR> 2018-03-09
     */
    @Override
    public String getWorkFlowToken() throws Exception {
        String url = config.getTokenUrl();
        CommonProperty commonProperty = new CommonProperty();
        url = String.format("%s/%s/%s", url, config.getAppId(), commonProperty.getUserId());
        return executeGet(url);
    }

    /**
     * 发送Post请求方法
     *
     * @param json 参数信息
     * @param url  流程执行URL
     * @return 流程执行结果
     * @throws Exception
     * <AUTHOR> 2018-03-08
     */
    private AppCallResult executePost(String json, String url) throws Exception {
        AppCallResult result = new AppCallResult();
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        HttpEntity message = null;
        try {
            client = HttpClients.createDefault();
            HttpPost post = new HttpPost(url);
            if (StringUtils.isNotEmpty(json)) {
                StringEntity entity = new StringEntity(
                        json, ContentType.create("application/json", "UTF-8"));
                entity.setChunked(true);
                post.setEntity(entity);
                response = client.execute(post);
                message = response.getEntity();
            }

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                if (message != null) {
                    String tmpResult = EntityUtils.toString(message);
                    result = JSON.parseObject(tmpResult, AppCallResult.class);
                }
            } else {
                throw new Exception(String.format("请求过程中发生异常(StatusCode:%s)", statusCode));
            }
        } catch (Exception exc) {
            exc.printStackTrace();
            throw exc;
        } finally {
            EntityUtils.consume(message);
            if (response != null) {
                response.close();
            }
            if (client != null) {
                client.close();
            }
        }
        return result;
    }

    /**
     * 发送Get请求方法
     *
     * @param url 流程执行URL
     * @return 流程执行结果
     * @throws Exception
     * <AUTHOR> 2018-03-08
     */
    private String executeGet(String url) throws Exception {
        String result = null;
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        HttpEntity message = null;
        try {
            client = HttpClients.createDefault();
            HttpGet get = new HttpGet(url);
            response = client.execute(get);
            message = response.getEntity();

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                if (message != null) {
                    result = EntityUtils.toString(message);
                }
            } else {
                throw new Exception(String.format("请求过程中发生异常(StatusCode:%s)", statusCode));
            }
        } catch (Exception exc) {
            exc.printStackTrace();
            throw exc;
        } finally {
            EntityUtils.consume(message);
            if (response != null) {
                response.close();
            }
            if (client != null) {
                client.close();
            }
        }
        return result;
    }

    /**
     * 根据流程编码获取获取流程模板ID
     *
     * @param categoryCode 流程分类编码
     * @return 流程模板ID
     * @throws Exception
     * <AUTHOR> 2018-03-08
     */
    private String getFlowTemplateId(String categoryCode) throws Exception {
        String result = null;
        String url = config.getFlowCategoryUrl() + "/" + config.getAppId() + "/" + categoryCode + "/1";
        String tmpResult = executeGet(url);
        JSONArray jsonArray = JSON.parseArray(tmpResult);
        if (jsonArray.size() == 1) {
            JSONObject jsonObject = (JSONObject) jsonArray.get(0);
            if (jsonObject != null && jsonObject.containsKey("workflowId")) {
                result = jsonObject.getString("workflowId");
            }
        }
        return result;
    }
}
