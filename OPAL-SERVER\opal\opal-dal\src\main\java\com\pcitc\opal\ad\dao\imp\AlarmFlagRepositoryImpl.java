package com.pcitc.opal.ad.dao.imp;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.TypedQuery;

import com.pcitc.opal.ad.dao.AlarmFlagRepositoryCustom;
import com.pcitc.opal.ad.pojo.AlarmFlag;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.dao.BaseRepository;

/*
 * AlarmFlag实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_AlarmFlagRepositoryImpl
 * 作       者：kun.zhao
 * 创建时间：2017/10/19
 * 修改编号：1
 * 描       述：AlarmFlag实体的Repository实现 
 */
public class AlarmFlagRepositoryImpl extends BaseRepository<AlarmFlag, Long> implements AlarmFlagRepositoryCustom {

	/**
	 * 得到所有已启用的报警标识实体集合
	 * 
	 * <AUTHOR> 2017-10-19
	 * @return 报警标识实体集合
	 */
	@Override
	public List<AlarmFlag> getAllAlarmFlag() {
		try {
			StringBuilder hql = new StringBuilder("from AlarmFlag af where af.inUse=:inUse order by af.sortNum, af.name");
			return this.getEntityManager().createQuery(hql.toString(), AlarmFlag.class)
					.setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex()).getResultList();
		} catch (Exception e) {
			throw e;
		}
	}

	/**
	 * 根据报警标识Id获取单条报警标识实体集合
	 *
	 * @param alarmFlagId 报警标识id
	 * <AUTHOR> 2017-10-30
	 * @return 报警标识实体
	 */
	@Override
	public AlarmFlag getSingleAlarmFlag(Long alarmFlagId){
		try {
			StringBuilder hql = new StringBuilder("from AlarmFlag a ");
			hql.append(" where a.alarmFlagId=:alarmFlagId ");
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("alarmFlagId", alarmFlagId);
			TypedQuery<AlarmFlag> query = getEntityManager().createQuery(hql.toString(), AlarmFlag.class);
			this.setParameterList(query, paramList);
			return query.getSingleResult();
		} catch (Exception e) {
			throw e;
		}
	}
}
