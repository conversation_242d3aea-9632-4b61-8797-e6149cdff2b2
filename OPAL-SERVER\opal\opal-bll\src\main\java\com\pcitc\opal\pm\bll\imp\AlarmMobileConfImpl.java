package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.pm.bll.AlarmMobileConfService;
import com.pcitc.opal.pm.dao.AlarmMobileConfRepository;
import com.pcitc.opal.pm.pojo.AlarmMobileConf;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class AlarmMobileConfImpl implements AlarmMobileConfService {

    @Autowired
    private AlarmMobileConfRepository alarmMobileConfRepository;

    @Override
    public List getGroupListInfo() {
        List list = alarmMobileConfRepository.findGroupListInfo();
        return list;
    }
}
