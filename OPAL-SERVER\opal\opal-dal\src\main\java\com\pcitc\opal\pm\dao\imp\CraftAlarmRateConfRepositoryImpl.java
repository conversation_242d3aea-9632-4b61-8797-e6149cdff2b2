package com.pcitc.opal.pm.dao.imp;


import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.CraftAlarmRateConfRepositoryCustom;
import com.pcitc.opal.pm.pojo.CraftAlarmRateConf;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;


public class CraftAlarmRateConfRepositoryImpl extends BaseRepository<CraftAlarmRateConf, Long> implements CraftAlarmRateConfRepositoryCustom {

    @Resource
    RedisTemplate<String, List<CraftAlarmRateConf>> redisTemplate;


    @Override
    public List<CraftAlarmRateConf> getAllCraftAlarmRateConf() {
        try {
            // 查询字符串
            String hql = "from CraftAlarmRateConf  ";
            TypedQuery<CraftAlarmRateConf> query = getEntityManager().createQuery(hql, CraftAlarmRateConf.class);

            List<CraftAlarmRateConf> craftAlarmRateConf = redisTemplate.opsForValue().get("CraftAlarmRateConf");

            if (CollectionUtils.isEmpty(craftAlarmRateConf)) {
                craftAlarmRateConf = query.getResultList();
                redisTemplate.opsForValue().set("CraftAlarmRateConf", craftAlarmRateConf, 30, TimeUnit.MINUTES);
            }

            return craftAlarmRateConf;
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public List<Object[]> getAlarmResponseRate(String[] unitId, Date startTime, Date endTime, CommonEnum.AlarmResponseAndAlarmHandlingDateTypeEnum dateTypeEnum) {

        String time = "";
        if ("second".equals(dateTypeEnum.getName())){
            time = "response_time";
        }else {
            time = "recovery_time";
        }
        
        
        String sql = "select tpp.name prdName,\n" +
                "       t.tag,\n" +
                "       t.des,\n" +
                "       taa.name flagName,\n" +
                "       t.priority,\n" +
                "       CONCAT(MU.NAME, '(', MU.SIGN, ')') AS MEASUNITNAME,\n" +
                "       t.alarm_time,\n" +
                time+",\n" +
                "       timestampdiff(" +
                dateTypeEnum.getName()+
                ", alarm_time, ifnull("+time+", sysdate()))\n" +
                "from t_ad_alarmrec t\n" +
                "         inner join t_pm_alarmpoint tpa on t.alarm_point_id = tpa.alarm_point_id\n" +
                "         left join T_PM_MEASUNIT MU ON tpa.MEASUNIT_ID = MU.MEASUNIT_ID\n" +
                "         inner join t_ad_alarmflag taa on t.alarm_flag_id = taa.alarm_flag_id\n" +
                "         inner join t_pm_prdtcell tpp on t.prdtcell_id = tpp.prdtcell_id\n" +
                "where alarm_time between :startTime and :endTime\n" +
                "  and timestampdiff(" +
                dateTypeEnum.getName()+
                ", alarm_time, ifnull("+time+", sysdate())) > 30\n" +
                "  and t.unit_code in (:unitId)\n" +
                "  and t.company_id = :companyId and tpa.in_use = 1 and tpp.in_use =1 " +
                "order by t.prdtcell_id, tag;";

        Query query = getEntityManager().createNativeQuery(sql);
        query.setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("unitId", Arrays.asList(unitId))
                .setParameter("companyId", new CommonProperty().getCompanyId());

        return (List<Object[]>) query.getResultList();

    }
}
