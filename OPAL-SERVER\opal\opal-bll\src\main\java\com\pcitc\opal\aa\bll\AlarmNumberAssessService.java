package com.pcitc.opal.aa.bll;

import com.pcitc.opal.aa.bll.entity.*;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;
//import com.pcitc.opal.common.CommonEnum;
//import com.pcitc.opal.common.Pagination;
//import com.pcitc.opal.common.PaginationBean;

/*
 * 报警数量评估业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmNumberAssessService
 * 作  　者：
 * 创建时间：2017-10-27
 * 修改编号：1
 * 描    述：报警数量评估业务逻辑层接口
 */
@Service
public interface AlarmNumberAssessService {

	/**
	 * 报警数量评估-报警数-图形显示
	 * 
	 * <AUTHOR> 2017-10-30
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param dateType 日期枚举类型
	 * @throws Exception 
	 * @return List<AlarmNumberEntity> 返回AlarmNumberEntity集合
	 */
	List<AlarmNumberConvertEntity> getAlarmNumber(String[] unitCodes, Long[] prdtCellIds, Date beginTime, Date endTime, CommonEnum.DateTypeEnum dateType, String checkTeam, Long[] team)
            throws Exception;
	
	/**
     * 获取报警数量评估首页统计值数据
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param unitCodes   装置编码集合
     * @param prdtIds   生产单元ID集合
     * @param topType Top20,Top10切换选择
     * @return
     * @throws Exception
     * <AUTHOR> 2017-10-28
     */
    List<AlarmNumberAssessDataEntity> getAlarmNumberAssessTop20(String[] unitCodes, Long[] prdtIds, String[] wokrUnitCodes, Date startTime, Date endTime, Integer topType) throws Exception;
    
    /**
     * 报警数量评估-报警数-单元显示
     * 
     * <AUTHOR> 2017-10-30
     * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
     * @throws Exception 
     * @return List<AlarmNumberModel> 返回AlarmNumberModel实体类集合
     */
    @RequestMapping("getAlarmNumberUnit")
	List<AlarmNumberConvertEntity> getAlarmNumberUnit(String[] unitCodes, Long[] prdtCellIds, Date beginTime, Date endTime)
    		throws Exception;
    
    /**
     * 报警数量评估——趋势图数据
     * 
     * <AUTHOR> 2017-10-30
     * @param startTime 时间范围起始
     * @param endTime 时间范围结束
     * @param unitCodes 装置编码数组
     * @param prdtIds 生产单元ID数组
     * @param workUnitCodes 车间编码数组
     * @param dateType 时间粒度
     * @return 报警数量评估趋势图数据实体集合
     */
	List<AlarmNumberAssessEntity> getAlarmNumberAssessTrendEntity(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, String[] workUnitCodes, CommonEnum.DateTypeEnum dateType) throws Exception;

    /**
     * 报警数量评估——单一装置或生产单元趋势图数据
     *  
     * <AUTHOR> 2017-11-07
     * @param startTime 时间范围起始
     * @param endTime 时间范围结束
     * @param id 装置或生产单元ID
     * @param queryType 查询类型
     * @param dateType 时间粒度
     * @return 报警数量评估趋势图数据实体集合
     * @throws Exception
     */
	List<AlarmNumberAssessEntity> getAlarmNumberAssessTrendEntity(Date startTime, Date endTime, String id,
																  CommonEnum.EquipmentTypeEnum queryType, CommonEnum.DateTypeEnum dateType) throws Exception;
	
	/**
	 * 查询报警数详情集合
	 * 
	 * <AUTHOR> 2017-11-07
	 * @param id 车间/装置/生产单元的id
     * @param searchType 搜索的类型
     * @param beginTime 报警事件的开始间
     * @param endTime 报警事件的结束时间
     * @param page 查询分页对象
	 * @throws Exception 
	 * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity实体分页对象
	 */
	PaginationBean<AlarmEventEntity> getAlarmNumberDetail(String id, Integer searchType, Date beginTime, Date endTime, Pagination page) throws Exception;
	
	/**
	 * 查询车间报警数量评估-报警数-图形显示
	 * 
	 * <AUTHOR> 2017-11-08
	 * @param workShopCodes 车间编码数组
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param dateType 日期枚举类型
	 * @throws Exception 
	 * @return List<AlarmNumberEntity> 返回AlarmNumberEntity实体类集合
	 */
	List<AlarmNumberConvertEntity> getWorkShopAlarmNumber(String[] workShopCodes, Date beginTime, Date endTime, CommonEnum.DateTypeEnum dateType) throws Exception;

	/**
     * 获取最频繁报警数量——单一装置/车间/生产单元
     *
     * <AUTHOR> 2017-11-08
     * @param id 装置/车间/生产单元ID
     * @param equipmentTypeEnum  查询类型
     * @param startTime 开始日期
     * @param endTime 结束日期
     * @param topType Top20,Top10切换选择
     * @return 最频繁报警数量数据
     * @throws Exception
     */
	List<AlarmNumberAssessDataEntity> getAlarmNumberAssessTop20(String id, CommonEnum.EquipmentTypeEnum equipmentTypeEnum, Date startTime,
																Date endTime, Integer topType) throws Exception;
	/**
     * 获取最频繁报警数量——单一装置/车间/生产单元
     *
     * <AUTHOR> 2019-09-30
     * @param id 装置/车间/生产单元ID
     * @param equipmentTypeEnum  查询类型
     * @param startTime 开始日期
     * @param endTime 结束日期
     * @param topType Top20,Top10切换选择
     * @param priority 优先级
     * @return 最频繁报警数量数据
     * @throws Exception
     */
	List<AlarmNumberAssessDataEntity> getAlarmNumberAssessTop20(String[] id,Long[] alarmFlagId, CommonEnum.EquipmentTypeEnum equipmentTypeEnum, Date startTime,
																Date endTime, Integer topType, Integer[] priority, Boolean priorityFlag, Integer isElimination, Long[] prdtIds);
	/**
	 * 获取最频繁报警数量——单一装置/车间/生产单元（新）
	 *
	 * <AUTHOR> 2019-09-30
	 * @param id 装置/车间/生产单元ID
	 * @param equipmentTypeEnum  查询类型
	 * @param startTime 开始日期
	 * @param endTime 结束日期
	 * @param topType Top20,Top10切换选择
	 * @param priority 优先级
	 * @return 最频繁报警数量数据
	 * @throws Exception
	 */
	PaginationBean<AlarmNumberAssessDataEntity> getAlarmNumberAssessAll(String[] workshopCodes,String[] unitCode,Long[] prdtCellId, String id, Long[] alarmFlagId, CommonEnum.EquipmentTypeEnum equipmentTypeEnum, Date startTime,
																Date endTime, Integer topType, Integer[] priority, Boolean priorityFlag, Integer isElimination, Long[] prdtIds,Pagination page);
	PaginationBean<AlarmNumberAssessDataEntity> getAlarmNumberAssessAllType(String[] workshopCodes,String[] unitCode,Long[] prdtCellId, String id, Long[] alarmFlagId, CommonEnum.EquipmentTypeEnum equipmentTypeEnum, Date startTime,
																Date endTime, Integer topType, Integer[] monitorType, Boolean priorityFlag, Integer isElimination, Long[] prdtIds,Pagination page);

	/**
	 * 查询车间报警数量评估-报警数-单元显示
	 * 
	 * <AUTHOR> 2017-11-08
	 * @param workShopCodes 车间编码数组
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @throws Exception 
	 * @return List<AlarmNumberEntity> 返回AlarmNumberEntity实体类集合
	 */
	List<AlarmNumberConvertEntity> getWorkShopAlarmNumberUnit(String[] workShopCodes, Date beginTime, Date endTime) throws Exception;

	/**
	 * 查询最频繁报警详情集合
	 *
	  * <AUTHOR> 2018-04-13
	 * @param alarmPointTag 位号
	 * @param alarmFlagId 报警标识id
	 * @param alarmTime 报警时间
	 * @param endTime 报结束时间时间
	 * @param page 查询分页对象
	 * @throws Exception 
	 * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity实体分页对象
	 */
	PaginationBean<AlarmEventEntity> getAlarmDtail(String alarmPointTag, Long alarmFlagId, Date alarmTime, Date endTime, Pagination page) throws Exception;

	List<AlarmNumberAssessDateTopEntity> getAlarmNumberAssessTop3(String[] workUnitIds, String[] unitIds, Date startTime, Date endTime, Integer[] priority,Boolean priorityFlag, Integer topType) throws Exception;

	List<AlarmUnitCountEntity> unitAlarm(String[] unitIds, Date queryStartTime, Date queryEndTime, CommonEnum.DateTypeEnum dateTypeEnum) throws Exception;

	List<DeptmUnitAlarmStattEntity> deptmUnitAlarmStatt(String[] workUnitIds, Date queryStartTime, Date displayEndTime) throws Exception;
}
