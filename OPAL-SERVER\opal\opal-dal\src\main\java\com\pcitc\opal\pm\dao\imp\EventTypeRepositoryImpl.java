package com.pcitc.opal.pm.dao.imp;

import java.util.ArrayList;
import java.util.List;

import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.EventTypeRepositoryCustom;
import com.pcitc.opal.pm.pojo.EventType;

/*
 * EventType实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_EventTypeRepositoryImpl
 * 作       者：kun.zhao
 * 创建时间：2017/10/09
 * 修改编号：1
 * 描       述：EventType实体的Repository实现
 */
public class EventTypeRepositoryImpl extends BaseRepository<EventType, Long> implements EventTypeRepositoryCustom {

	/**
	 * 获取所有已经启用的事件类型集合
	 *
	 * <AUTHOR> 2017-10-10
	 * @return 已经启用的事件类型集合
	 */
	@Override
	public List<EventType> getEventType() {
		List<EventType> unitEntityList= new ArrayList<EventType>();
		try {
			//获取所有已启用的生产装置列表
			String eventTypeHql = "from EventType e where e.inUse=:inUse order by e.sortNum,e.name asc ";
			 unitEntityList =this.getEntityManager().createQuery(eventTypeHql, EventType.class).setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex()).getResultList();
		} catch (Exception ex) {
			throw ex;
		}
		return unitEntityList;
	}

}
