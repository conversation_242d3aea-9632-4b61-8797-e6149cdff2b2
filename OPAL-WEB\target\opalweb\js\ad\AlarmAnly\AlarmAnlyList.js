$(function () {
    var anlyUrl = OPAL.API.adUrl + '/alarmAnly/batchAddAnly'; //分析保存和提交
    var getUserNameUrl = OPAL.API.commUrl + '/getSysUserInfo';
    var reasonTpyeDataUrl = OPAL.API.pmUrl + '/alarmReason/getReasonType'; //原因分类
    var reasonUrl = OPAL.API.pmUrl + '/alarmReason/getReasonByType'; //根据原因分类获取原因
    var recIdList;
    window.pageLoadMode = PageLoadMode.Refresh;
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var saveData = {};
    var page = {
        init: function () {
            this.bindUI();
            //初始化时间
            page.logic.initDate();
            //初始化上传人
            page.logic.initUserName();
            //初始化原因分类
            page.logic.initReasonTypeList();
        },
        bindUI: function () {
            /*关闭弹窗*/
            $('#closePage').click(function () {
                page.logic.closeLayer(true);
            });
            $('.closeBtn').click(function () {
                page.logic.closeLayer(true);
            });
            //保存
            $("#saveAddModal").click(function () {
                page.logic.save();
            })
            //提交
            $("#submitAddModal").click(function () {
                page.logic.submit();
            })
            $('#reasonType').change(function(e){
                page.logic.initReasonList();
            })
        },
        logic: {
            /**
             * 初始化查询 原因分类
             */
            initReasonTypeList: function() {
                $.ajax({
                    url: reasonTpyeDataUrl + "?now=" + Math.random(),
                    type: "get",
                    data: {
                        'isAll': false
                    },
                    async: false,
                    dataType: "json",
                    success: function (data) {
                        //初始化原因分类
                        delete data[0];
                        let str = '';
                        data.forEach((item,index) => {
                            str+= '<option value="'+item.key+'">'+item.value+'</option>'
                        })
                        $('#reasonType').html(str);
                        $('#reasonType').val(1);
                        page.logic.initReasonList();
                    }
                });
            },
            //获取原因下拉框
            initReasonList: function() {
                $.ajax({
                    url: reasonUrl + "?now=" + Math.random(),
                    type: "get",
                    data: {
                        'reasonType': $('#reasonType').val()
                    },
                    async: false,
                    dataType: "json",
                    success: function (data) {
                        let str = '';
                        data.forEach((item,index) => {
                            str+= '<option value="'+item.id+'">'+item.name+'</option>'
                        })
                        $('#alarmReasonId').html(str);
                    }
                });
            },
            //初始化时间
            initDate: function () {
                var date = laydate.render({
                    elem: '#recoveryTime', //指定元素
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd HH:mm:ss', //日期格式
                });
            },
            //初始化登录人
            initUserName: function () {
                $.ajax({
                    url: getUserNameUrl,
                    async: true,
                    dataType: "json",
                    success: function (data) {
                        var dataArr = $.ET.toObjectArr(data);
                        $("#uplUserName").val(dataArr[1].value);
                    },
                    error: function (jqXHR, textStatus, errorThrown) { }
                });
            },
            /**
             * 保存
             */
            save: function () {
                page.logic.formValidate();
                if (!$('#batchAddAnly').valid()) {
                    return;
                }
                saveData = OPAL.form.getData('batchAddAnly');
                saveData.alarmRecIdList = recIdList;
                saveData.anlyStatus = 1;
                $.ajax({
                    url: anlyUrl,
                    async: false,
                    type: "POST",
                    data: saveData,
                    dataType: 'text',
                    success: function (result, XMLHttpRequest) {
                        if (result > 0) {
                            layer.msg('保存成功！', {
                                time: 1000
                            },function () {
                                $('#submitAddModal').attr('disabled',false);
                            });
                        } else {
                            layer.msg(result)
                        }
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })

            },
            /**
             * 提交
             */
            submit: function () {
                page.logic.formValidate();
                if (!$('#batchAddAnly').valid()) {
                    return;
                }
                saveData = OPAL.form.getData('batchAddAnly');
                saveData.alarmRecIdList = recIdList;
                saveData.anlyStatus = 2;
                $.ajax({
                    url: anlyUrl,
                    async: false,
                    type: "POST",
                    data: saveData,
                    dataType: 'text',
                    success: function (result, XMLHttpRequest) {
                        if (result > 0) {
                            layer.msg('提交成功！', {
                                time: 1000
                            }, function () {
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result)
                        }
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })

            },
            //数据校验
            formValidate: function () {
                OPAL.form.formValidate('batchAddAnly', {
                    rules: {
                        reasonType: {
                            required: true
                        },
                        alarmReasonId: {
                            required: true
                        },
                        des: {
                            required: true
                        }
                    }
                })
            },

            initTable: function (anlyRows) {
                OPAL.ui.initBootstrapTable("table", {
                    pagination: false,
                    data: anlyRows,
                    columns: [{
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'left',
                        width: '130px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'left',
                        width: '130px'
                    }, {
                        title: "参数名称",
                        field: 'tagDes',
                        rowspan: 1,
                        align: 'center',
                        width: '130px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'center',
                        width: '130px'
                    }, {
                        title: "单位",
                        field: 'measUnitName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "优先级",
                        field: 'priorityName',
                        rowspan: 1,
                        align: 'center',
                        width: '60px'
                    }, {
                        title: "专业",
                        field: 'monitorTypeName',
                        rowspan: 1,
                        align: 'center',
                        width: '60px'
                    }]
                },null)
            },

            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                recIdList = data.alarmRecIdList;
                var anlyRows = data.anlyRows;
                page.logic.initTable(anlyRows);
                // $("#table").bootstrapTable('refresh', anlyRows);
                // //初始化原因分类
                page.logic.initReasonTypeList();
                monitorType = data.monitorType;
                // pageMode = data.pageMode;
                // $("#pageTitle").text(data.title);
                // detailData = JSON.parse(data.row);
                // OPAL.form.setData('formDetail', detailData);
                // $('#alarmStatusName').val(detailData.alarmStatusName);
                // $('#anlyStatus').val(detailData.anlyStatus);
                // if (pageMode == PageModelEnum.NewAdd) {
                //
                // }
                // $.ajax({
                //     url: getSingleUrl + data.alarmRecId,
                //     type: "get",
                //     async: false,
                //     dataType: "json",
                //     success: function (data) {
                //         $('#des').val($.ET.toObjectArr(data)[0].des);
                //         $('#priorityName').val($.ET.toObjectArr(data)[0].priorityName);
                //     }
                // });
                // if (detailData.alarmAnlyRecId) {
                //     $.ajax({
                //         url: searchChildUrl + detailData.alarmRecId,
                //         type: "get",
                //         async: false,
                //         dataType: "json",
                //         success: function (data) {
                //             var childDetail = $.ET.toObjectArr(data)[0];
                //             OPAL.form.setData('AddOrEditModal', childDetail);
                //             $('.child-des').val(childDetail.reasonDes)
                //         }
                //     });
                // }
                // $('#alarmRecId').val(detailData.alarmRecId);
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                parent.layer.close(index);
            }
        }
    }
    page.init();
    window.page = page;
})