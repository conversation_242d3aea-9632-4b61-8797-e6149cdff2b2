package com.pcitc.opal.ac.bll.entity;

import java.util.Date;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 变更记录实体
 * 模块编号：pcitc_opal_bll_class_AlarmChangeRecordEntity
 * 作       者：kun.zhao
 * 创建时间：2017/11/10
 * 修改编号：1
 * 描       述：变更记录实体
 */
public class AlarmChangeRecordEntity extends BasicEntity{
	
	/**
	 * 报警事件ID
	 */
	private Long eventId;
	/**
	 *发生时间
	 */
    private Date startTime;
    
    /**
     * 装置编码
     */
    private String unitId;
    
    /**
     * 装置名称
     */
    private String unitName;
    
    /**
     * 生产单元ID
     */
    private Long prdtCellId;
    
    /**
     * 生产单元名称
     */
    private String prdtCellName;
    
    /**
     * 报警点ID
     */
    private Long alarmPointId;
    
    /**
     * 报警点位号
     */
    private String tag;
    
    /**
     * 报警标识ID
     */
    private Long alarmFlagId;
    
    /**
     * 报警标识名称
     */
    private String alarmFlagName;
    
    /**
     * 事件类型ID
     */
    private Long eventTypeId;
    
    /**
     * 事件类型名称
     */
    private String eventTypeName;
    
    /**
     * 先前值
     */
    private String previousValue;
    
    /**
     * 值
     */
    private String nowValue;
    
    /**
     * 工艺卡片上限值是否包含(1是；0否)
     */
    private Integer craftUpLimitInclude;
    
    /**
     * 工艺卡片下限值是否包含(1是；0否)
     */
    private Integer craftDownLimitInclude;

    /**
     * 工艺卡片上限值
     */
    private Double craftUpLimitValue;

    /**
     * 工艺卡片下限值
     */
    private Double craftDownLimitValue;
    
    /**
     * 工艺卡片值
     */
    private String craft;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 描述
     */
    private String des;
    
    /**
     * 是否标红
     */
    private Integer flag;
    
    /**
     * 班组简称
     */
    private String workTeamSName;

	private String location;
    
	public Long getEventId() {
		return eventId;
	}

	public void setEventId(Long eventId) {
		this.eventId = eventId;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public Long getPrdtCellId() {
		return prdtCellId;
	}

	public void setPrdtCellId(Long prdtCellId) {
		this.prdtCellId = prdtCellId;
	}

	public String getPrdtCellName() {
		return prdtCellName;
	}

	public void setPrdtCellName(String prdtCellName) {
		this.prdtCellName = prdtCellName;
	}

	public Long getAlarmPointId() {
		return alarmPointId;
	}

	public void setAlarmPointId(Long alarmPointId) {
		this.alarmPointId = alarmPointId;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public Long getAlarmFlagId() {
		return alarmFlagId;
	}

	public void setAlarmFlagId(Long alarmFlagId) {
		this.alarmFlagId = alarmFlagId;
	}

	public String getAlarmFlagName() {
		return alarmFlagName;
	}

	public void setAlarmFlagName(String alarmFlagName) {
		this.alarmFlagName = alarmFlagName;
	}

	public Long getEventTypeId() {
		return eventTypeId;
	}

	public void setEventTypeId(Long eventTypeId) {
		this.eventTypeId = eventTypeId;
	}

	public String getEventTypeName() {
		return eventTypeName;
	}

	public void setEventTypeName(String eventTypeName) {
		this.eventTypeName = eventTypeName;
	}

	public String getPreviousValue() {
		return previousValue;
	}

	public void setPreviousValue(String previousValue) {
		this.previousValue = previousValue;
	}

	public String getNowValue() {
		return nowValue;
	}

	public void setNowValue(String nowValue) {
		this.nowValue = nowValue;
	}

	public Integer getCraftUpLimitInclude() {
		return craftUpLimitInclude;
	}

	public void setCraftUpLimitInclude(Integer craftUpLimitInclude) {
		this.craftUpLimitInclude = craftUpLimitInclude;
	}

	public Integer getCraftDownLimitInclude() {
		return craftDownLimitInclude;
	}

	public void setCraftDownLimitInclude(Integer craftDownLimitInclude) {
		this.craftDownLimitInclude = craftDownLimitInclude;
	}

	public Double getCraftUpLimitValue() {
		return craftUpLimitValue;
	}

	public void setCraftUpLimitValue(Double craftUpLimitValue) {
		this.craftUpLimitValue = craftUpLimitValue;
	}

	public Double getCraftDownLimitValue() {
		return craftDownLimitValue;
	}

	public void setCraftDownLimitValue(Double craftDownLimitValue) {
		this.craftDownLimitValue = craftDownLimitValue;
	}

	public String getCraft() {
		return craft;
	}

	public void setCraft(String craft) {
		this.craft = craft;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}

	public Integer getFlag() {
		return flag;
	}

	public void setFlag(Integer flag) {
		this.flag = flag;
	}

	public String getWorkTeamSName() {
		return workTeamSName;
	}

	public void setWorkTeamSName(String workTeamSName) {
		this.workTeamSName = workTeamSName;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}
}
