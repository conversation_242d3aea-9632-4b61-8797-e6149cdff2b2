package com.pcitc.opal.pm.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class AlarmPointTableVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long alarmPointId;

    /**
     * 生产单元id
     */
    private Long prdtcellId;

    /**
     * 位号
     */
    private String tag;

    /**
     * 位置
     */
    private String location;

    /**
     * PID图号
     */
    private String pidCode;

    /**
     * 报警点类型id
     */
    private Long alarmPointTypeId;

    private Integer monitorType;

    private Long measunitId;

    private Long instrmtType;

    private Long virtualRealityFlag;

    private Double alarmPointHh;

    private Double alarmPointHi;

    private Double alarmPointLo;

    private Double alarmPointLl;

    private Long inUse;

    /**
     * 创建时间
     */
    private Date crtDate;

    /**
     * 维护时间
     */
    private Date mntDate;

    /**
     * 创建人ID
     */
    private String crtUserId;

    /**
     * 最后维护人ID
     */
    private String mntUserId;

    /**
     * 创建人名称
     */
    private String crtUserName;

    /**
     * 最后维护人名称
     */
    private String mntUserName;

    private Long sortNum;

    /**
     * 描述
     */
    private String des;

    private Integer craftUpLimitInclude;

    private Integer craftDownLimitInclude;

    private Double craftUpLimitValue;

    private Double craftDownLimitValue;

    private Integer interlockUpLimitInclude;

    private Integer interlockDownLimitInclude;

    private Double interlockUpLimitValue;

    private Double interlockDownLimitValue;

    private Long craftRank;

    private Long virtualFlag;

    private Integer instrmtPriority;

    private String instrmtPriorityShow;

    private Long inSendmsg;

    /**
     * 手机号（多个手机号用英文逗号隔开）
     */
    private String mobilePhone;

    /**
     * 实时数据库位号
     */
    private String rtdbTag;

    /**
     * 企业ID
     */
    private Long companyId;
    /**
     * 报警点类型名称
     */
    private String alarmPointTypeName;

    /**
     * 生产单元简称
     */
    private String prdtCellSname;

    /**
     * 装置编码
     */
    private String unitId;
    /**
     * 工厂名称
     */
    private String factoryName;
    /**
     * 车间名称
     */
    private String workshopName;
    /**
    /**
     * 装置名称简称
     */
    private String unitSname;
    /**
     * 计量单位名称
     */
    private String measunitName;
    /**
     * 符号
     */
    private String signName;
    /**
     * 工艺卡片值
     */
    private String craftLimitValue;
    /**
     * 联锁值
     */
    private String interlockLimitValue;
    /**
     * 数据是否标红
     */
    private int isRed;

    private String monitorTypeStr;

    private Integer isTimeout;

}
