var getSingleUrl = OPAL.API.pmUrl + '/alarmMsgConfigNew/getAlarmMsgConfigByAlarmMsgConfId';
var saveUrl = OPAL.API.pmUrl + '/alarmMsgConfigNew/updateAlarmMsgConfigInfo';
// var alarmFlagUrl = OPAL.API.pmUrl + '/alarmMsgConfigNew/getAlarmFlagList';   //获取报警标识
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.None;
var alarmMsgConfIds;
var mobileBookId;
var mobileBook;
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
            page.logic.initTable(JSON.stringify(""));
            // page.logic.initAlarmFlag();
        },
        bindUI: function () {
            $('#saveModal').click(function () {
                page.logic.save();
            });
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            });
            // $("#inSendMsg").click(function () {
            // if($("#inSendMsg").prop("checked")){
            //     $(".requireds").show();
            // }else {
            //     $(".requireds").hide();
            // }
            // })
            $("#choice").click(function () {
                page.logic.choice();
            })
        },
        logic: {
            /**
             * 保存
             */
            save: function () {
                page.logic.formValidate();
                if (!$('#EditModal').valid()) {
                    return;
                }
                var checks = [];
                if($("#inSendMsg").prop("checked")){
                    if(!$("#mobileBook").val()){
                        layer.msg("请选择电话本！");
                        return
                    }
                }

                var data = OPAL.form.getData('EditModal');
                // data.alarmPointIds = alarmPointIds;
                // data.alarmflagId = checks.join(",");
                data.alarmMsgConfIds=alarmMsgConfIds;
                data.mobileBookId = mobileBookId;
                $.ajax({
                    url: saveUrl,
                    async: false,
                    type: "put",
                    data: data,
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！",{
                                time: 1000
                            },function() {
                                window.pageLoadMode = PageLoadMode.Refresh;
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            formValidate:function(){
                OPAL.form.formValidate('EditModal',{
                    rules:{
                        timeInterval:{
                            required: true,
                            digits: true,
                            min: 0
                        }
                    },
                    messages:{
                        timeInterval: {
                            number: '请输入正确数值'
                        }
                    }
                })
            },
            initTable: function (data) {
                var results = JSON.parse(data);
                var options = {
                    striped: true, //是否显示行间隔色
                    height: 200,
                    columns: [{
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    },  {
                        title: "生产单元",
                        field: 'prdtcellName',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }, {
                        title: "时间间隔",
                        field: 'timeInterval',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }]
                };
                $('#table').bootstrapTable(options);
                $('#table').bootstrapTable('refreshOptions', options);
                if (results == undefined) {
                    results = [];
                }
                $("#table").bootstrapTable("load", results);
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                $("#pageTitle").text(data.title);
                alarmMsgConfIds = data.alarmMsgConfId.split(",");
                $.ajax({
                    url: getSingleUrl + "?now=" + Math.random(),
                    data:{
                        alarmMsgConfIds:alarmMsgConfIds
                    },
                    type: "get",
                    dataType: "json",
                    success: function (data) {
                        var results = $.ET.toObjectArr(data);
                        if(results.length == 1){
                            OPAL.form.setData('EditModal',results[0]);
                            mobileBookId = results[0].mobileListId;
                            mobileBook = results[0].mobileListStr;

                            // var alarmFlags = results[0].alarmflagId.split(",");
                            // $("input[type='checkbox'][name='alarmFlag']").removeAttr("checked");
                            // for(var i = 0;i < alarmFlags.length;i++){
                            //     $("input[type='checkbox'][name='alarmFlag'][value='"+alarmFlags[i]+"']").attr("checked","checked");
                            // }
                        }
                        page.logic.initTable(JSON.stringify(results));
                    },
                    complete: function (XMLHttpRequest, textStatus) {

                    },
                    error: function (XMLHttpRequest, textStatus) {

                    }
                });
            },
            // 选择
            choice:function () {
                var title = "手机号选择";
                layer.open({
                    type: 2,
                    title: false,
                    closeBtn:0,
                    area: ['920px', '560px'],
                    shadeClose: false,
                    content: 'MobileList.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "mobileBookId": mobileBookId,
                            "mobileBook": mobileBook.replace(/,/g,"#"),
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function() {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            // 报警标识
            // initAlarmFlag:function () {
            //     $.ajax({
            //         url:alarmFlagUrl,
            //         type:"get",
            //         dataType: "json",
            //         success: function (data) {
            //             var results = $.ET.toObjectArr(data);
            //             var _li = ""
            //             for(var i = 0;i < results.length;i++){
            //                 _li+= '<li><input type="checkbox" value="'+results[i].key+'" name="alarmFlag" checked="checked" class="top-and-bottom-style"><span>'+results[i].value+'</span></li>';
            //             }
            //             $("#alarmIdent").html(_li);
            //         }
            //     })
            // },

            getSelect:function (selects,mobileBookIds) {
                $("#mobileBook").val(selects.join(","));
                mobileBook = selects.join("#");
                mobileBookId = mobileBookIds;
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            }
        }
    }
    page.init();
    window.page = page;
})