package com.pcitc.opal.af.bll.entity;


import lombok.Data;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/*
 * 工艺参数报警率数据实体
 * 作  　  者：shufei.sui
 * 创建时间：2019/12/11
 * 修改编号：1
 * 描       述：工艺参数报警率数据实体
 */
@Data
public class PortalAlarmRateEntity {

    //装置编码
    private String unitid;

    //装置名称
    private String unitCode;

    // 时平均报警数、
    private Double avgAlarmRate;//

    // 24小时持续报警数、
    private Double alarmAmount;//

    // 10分钟峰值报警数
    private Double peakAlarmRate;//

    //报警响应及时率
    private Double alarmTimelyResponseRate;//

    //报警处置及时率
    private Double alarmTimelyDisposalRate;//

    //工艺参数报警率
    private Double craftParaAlarmRate;

    public void setAvgAlarmRate(Double avgAlarmRate) {
        this.avgAlarmRate = formatToTwoDecimalPlaces(avgAlarmRate);
    }

    public void setAlarmAmount(Double alarmAmount) {
        this.alarmAmount = formatToTwoDecimalPlaces(alarmAmount);
    }

    public void setPeakAlarmRate(Double peakAlarmRate) {
        this.peakAlarmRate = formatToTwoDecimalPlaces(peakAlarmRate);
    }

    public void setAlarmTimelyResponseRate(Double alarmTimelyResponseRate) {
        this.alarmTimelyResponseRate = formatToTwoDecimalPlaces(alarmTimelyResponseRate);
    }

    public void setAlarmTimelyDisposalRate(Double alarmTimelyDisposalRate) {
        this.alarmTimelyDisposalRate = formatToTwoDecimalPlaces(alarmTimelyDisposalRate);
    }

    public void setCraftParaAlarmRate(Double craftParaAlarmRate) {
        this.craftParaAlarmRate = formatToTwoDecimalPlaces(craftParaAlarmRate);
    }

    private double formatToTwoDecimalPlaces(double value) {
        // 使用 DecimalFormat 将值格式化为两位小数
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        return Double.parseDouble(decimalFormat.format(value));
    }

}

