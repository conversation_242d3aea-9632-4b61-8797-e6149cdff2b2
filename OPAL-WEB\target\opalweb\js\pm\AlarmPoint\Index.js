var delUrl = OPAL.API.pmUrl + '/alarmPoints';
var searchUrl = OPAL.API.pmUrl + '/alarmPoints';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var inUseUrl = OPAL.API.commUrl + "/getInUse";
var AlarmPointTypeUrl = OPAL.API.commUrl + '/getAlarmPointTypeList';
var UnitPrdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var importAlarmPointUrl = OPAL.API.pmUrl + '/alarmPoints/importAlarmPoint'
var exportAlarmPointUrl = OPAL.API.pmUrl + '/alarmPoints/exportAlarmPoint';
var downAlarmPointTemplateUrl = OPAL.API.pmUrl + '/alarmPoints/downAlarmPointTemplate';
var getInstrmtPriUrl = OPAL.API.pmUrl + "/alarmPoints/getInstrmtPriority"; //仪表优先级
window.pageLoadMode = PageLoadMode.None;
var enablePrivilege ;
var isQueryFlag;
var isPointQuery;
$(function() {
    var page = {
        /**
         * 初始化
         */
        init: function() {            
            // 获得是否查询权限类型(1，是；0，否；)
            isQueryFlag = page.logic.getQueryParam('isQueryFlag');
            isPointQuery= page.logic.getQueryParam('isPointQuery');
            if (isQueryFlag != 0 && isQueryFlag != 1 && isPointQuery !=0 && isPointQuery!=1) {
                isQueryFlag = 1
            }
            if (isPointQuery !=0 && isPointQuery!=1) {
                isPointQuery = 1
            }
            // 更改页面标题
            page.logic.changeTitle();
            //绑定事件
            this.bindUI();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化查询是否启用
            page.logic.initInUse();
            //初始化报警点类型
            page.logic.initAlarmPointType();
            //初始化仪表优先级
            page.logic.initInstrmtPriority();
            //初始化表格
            page.logic.initTable();
            //导入
            page.logic.importExcel();

            //默认查询数据
            document.getElementById("searched").click();
        },
        /**
         * 绑定事件
         */
        bindUI: function() {
            // 新增
            $('#AlarmPointAdd').click(function() {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            });
            //批量删除
            $('#AlarmPointDel').click(function() {
                page.logic.delAll();
            });
                //查询
            $('#searched').click(function() {
                page.logic.search();
            });
            $('#AlarmPointExport').click(function() {
                page.logic.exportExcel();
            });
            $('#DownFile').click(function() {
                page.logic.downFile();
            });
        },
        data: {
            // 设置查询参数
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化表格
             */
            initTable: function () {
                if (isQueryFlag==0 && isPointQuery==0) {
                    OPAL.ui.initBootstrapTable("table", {
                        cache: false,
                        columns: [{
                            field: 'state',
                            checkbox: true,
                            rowspan: 1,
                            align: 'center'
                        }, {
                            title: "操作",
                            field: 'event_cancel',
                            rowspan: 1,
                            align: 'center',
                            formatter: page.logic.onActionRenderer,
                            width: '90px'
                        }, {
                            title: "装置",
                            field: 'unitSname',
                            rowspan: 1,
                            align: 'left',
                            width: '120px'
                        }, {
                            title: "生产单元",
                            field: 'prdtCellSname',
                            rowspan: 1,
                            align: 'left',
                            width: '120px'
                        }, {
                            title: "位号",
                            field: 'tag',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        }, {
                            title: "级别",
                            field: 'craftRankName',
                            rowspan: 1,
                            align: 'center',
                            width: '75px'
                        },{
                            title: "仪表优先级",
                            field: 'instrmtPriorityShow',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        },  {
                            title: "工艺卡片值",
                            field: 'craftLimitValue',
                            rowspan: 1,
                            align: 'center',
                            width: '150px',
                            formatter: page.logic.formatterNumber
                        }, {
                            title: "联锁值",
                            field: 'interlockLimitValue',
                            rowspan: 1,
                            align: 'center',
                            width: '130px',
                            //formatter: page.logic.formatterNumber
                        }, {
                            title: "位置",
                            field: 'location',
                            rowspan: 1,
                            align: 'left',
                            width: '150px'
                        }, {
                            title: "PID图号",
                            field: 'pidCode',
                            rowspan: 1,
                            align: 'left',
                            width: '130px'
                        }, {
                            title: "报警点类型",
                            field: 'alarmPointTypeName',
                            rowspan: 1,
                            align: 'center',
                            width: '90px'
                        }, {
                            title: "专业",
                            field: 'monitorTypeStr',
                            rowspan: 1,
                            align: 'center',
                            width: '60px'
                        }, {
                            title: "计量单位",
                            field: 'measunitName',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        }, {
                            title: "实时数据库位号",
                            field: 'rtdbTag',
                            rowspan: 1,
                            align: 'left',
                            width: '130px'
                        }, {
                            title: "仪表类型",
                            field: 'instrmtTypeStr',
                            rowspan: 1,
                            align: 'center',
                            width: '75px'
                        }, {
                            title: "是否虚表",
                            field: 'virtualRealityFlagStr',
                            rowspan: 1,
                            align: 'center',
                            width: '75px'
                        }, {
                            title: "报警点高高报",
                            field: 'alarmPointHH',
                            rowspan: 1,
                            align: 'right',
                            width: '130px'
                        }, {
                            title: "报警点高报",
                            field: 'alarmPointHI',
                            rowspan: 1,
                            align: 'right',
                            width: '130px'
                        }, {
                            title: "报警点低报",
                            field: 'alarmPointLO',
                            rowspan: 1,
                            align: 'right',
                            width: '130px'
                        }, {
                            title: "报警点低低报",
                            field: 'alarmPointLL',
                            rowspan: 1,
                            align: 'right',
                            width: '130px'
                        }, {
                            title: "是否启用",
                            field: 'inUseShow',
                            rowspan: 1,
                            align: 'center',
                            width: '70px'
                        },{
                            title: "工艺启停",
                            field: 'isTimeoutShow',
                            rowspan: 1,
                            align: 'center',
                            width: '70px'
                        }, {
                            title: "创建时间",
                            field: 'crtDate',
                            rowspan: 1,
                            align: 'center',
                            width: '150px'
                        }, {
                            title: "创建人",
                            field: 'crtUserName',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        }, {
                            title: "维护时间",
                            field: 'mntDate',
                            rowspan: 1,
                            align: 'center',
                            width: '150px'
                        }, {
                            title: "维护人",
                            field: 'mntUserName',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        }, {
                            title: "排序",
                            field: 'sortNum',
                            rowspan: 1,
                            align: 'right',
                            width: '70px'
                        }, {
                            title: "描述",
                            field: 'des',
                            rowspan: 1,
                            align: 'left',
                            width: '120px'
                        }],
                        rowStyle: function (row, index) {
                            var style;
                            if (row.isRed == 1) {
                                style = 'redRow1'
                            }
                            return {
                                classes: style
                            }
                        }
                    }, page.logic.queryParams)
                }else if(isQueryFlag==0 && isPointQuery==1){
                    OPAL.ui.initBootstrapTable("table", {
                        cache: false,
                        columns: [
                         {
                            title: "装置",
                            field: 'unitSname',
                            rowspan: 1,
                            align: 'left',
                            width: '120px'
                        }, {
                            title: "生产单元",
                            field: 'prdtCellSname',
                            rowspan: 1,
                            align: 'left',
                            width: '120px'
                        }, {
                            title: "位号",
                            field: 'tag',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        }, {
                            title: "级别",
                            field: 'craftRankName',
                            rowspan: 1,
                            align: 'center',
                            width: '75px'
                        },{
                            title: "仪表优先级",
                            field: 'instrmtPriorityShow',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        },  {
                            title: "工艺卡片值",
                            field: 'craftLimitValue',
                            rowspan: 1,
                            align: 'center',
                            width: '150px',
                            formatter: page.logic.formatterNumber
                        }, {
                            title: "联锁值",
                            field: 'interlockLimitValue',
                            rowspan: 1,
                            align: 'center',
                            width: '130px',
                            //formatter: page.logic.formatterNumber
                        }, {
                            title: "位置",
                            field: 'location',
                            rowspan: 1,
                            align: 'left',
                            width: '150px'
                        }, {
                            title: "PID图号",
                            field: 'pidCode',
                            rowspan: 1,
                            align: 'left',
                            width: '130px'
                        }, {
                            title: "报警点类型",
                            field: 'alarmPointTypeName',
                            rowspan: 1,
                            align: 'center',
                            width: '90px'
                        }, {
                            title: "专业",
                            field: 'monitorTypeStr',
                            rowspan: 1,
                            align: 'center',
                            width: '60px'
                        }, {
                            title: "计量单位",
                            field: 'measunitName',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        }, {
                            title: "实时数据库位号",
                            field: 'rtdbTag',
                            rowspan: 1,
                            align: 'left',
                            width: '130px'
                        }, {
                            title: "仪表类型",
                            field: 'instrmtTypeStr',
                            rowspan: 1,
                            align: 'center',
                            width: '75px'
                        }, {
                            title: "是否虚表",
                            field: 'virtualRealityFlagStr',
                            rowspan: 1,
                            align: 'center',
                            width: '75px'
                        }, {
                            title: "报警点高高报",
                            field: 'alarmPointHH',
                            rowspan: 1,
                            align: 'right',
                            width: '130px'
                        }, {
                            title: "报警点高报",
                            field: 'alarmPointHI',
                            rowspan: 1,
                            align: 'right',
                            width: '130px'
                        }, {
                            title: "报警点低报",
                            field: 'alarmPointLO',
                            rowspan: 1,
                            align: 'right',
                            width: '130px'
                        }, {
                            title: "报警点低低报",
                            field: 'alarmPointLL',
                            rowspan: 1,
                            align: 'right',
                            width: '130px'
                        }, {
                            title: "是否启用",
                            field: 'inUseShow',
                            rowspan: 1,
                            align: 'center',
                            width: '70px'
                        }, {
                            title: "创建时间",
                            field: 'crtDate',
                            rowspan: 1,
                            align: 'center',
                            width: '150px'
                        }, {
                            title: "创建人",
                            field: 'crtUserName',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        }, {
                            title: "维护时间",
                            field: 'mntDate',
                            rowspan: 1,
                            align: 'center',
                            width: '150px'
                        }, {
                            title: "维护人",
                            field: 'mntUserName',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        }, {
                            title: "排序",
                            field: 'sortNum',
                            rowspan: 1,
                            align: 'right',
                            width: '70px'
                        }, {
                            title: "描述",
                            field: 'des',
                            rowspan: 1,
                            align: 'left',
                            width: '120px'
                        }],
                        rowStyle: function (row, index) {
                            var style;
                            if (row.isRed == 1) {
                                style = 'redRow1'
                            }
                            return {
                                classes: style
                            }
                        }
                    }, page.logic.queryParams)
                }else if(isQueryFlag==1 || isPointQuery==1) {
                    OPAL.ui.initBootstrapTable("table", {
                        cache: false,
                        columns: [{
                            title: "序号",
                            formatter: function(value, row, index) {
                                var data = page.data.param;
                                var pageNumber = data.pageNumber;
                                var pageSize = data.pageSize;
                                return index + 1 + (pageNumber - 1 ) * pageSize;
                            },
                            rowspan: 1,
                            align: 'center',
                            width: '80px'
                        },  {
                            title: "装置",
                            field: 'unitSname',
                            rowspan: 1,
                            align: 'left',
                            width: '120px'
                        }, {
                            title: "生产单元",
                            field: 'prdtCellSname',
                            rowspan: 1,
                            align: 'left',
                            width: '120px'
                        }, {
                            title: "位号",
                            field: 'tag',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        },{
                            title: "仪表优先级",
                            field: 'instrmtPriorityShow',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        },  {
                            title: "位置",
                            field: 'location',
                            rowspan: 1,
                            align: 'left',
                            width: '150px'
                        },  {
                            title: "计量单位",
                            field: 'measunitName',
                            rowspan: 1,
                            align: 'left',
                            width: '100px'
                        }, {
                            title: "实时数据库位号",
                            field: 'rtdbTag',
                            rowspan: 1,
                            align: 'left',
                            width: '130px'
                        }, {
                            title: "报警点高高报",
                            field: 'alarmPointHH',
                            rowspan: 1,
                            align: 'right',
                            width: '130px'
                        }, {
                            title: "报警点高报",
                            field: 'alarmPointHI',
                            rowspan: 1,
                            align: 'right',
                            width: '130px'
                        }, {
                            title: "报警点低报",
                            field: 'alarmPointLO',
                            rowspan: 1,
                            align: 'right',
                            width: '130px'
                        }, {
                            title: "报警点低低报",
                            field: 'alarmPointLL',
                            rowspan: 1,
                            align: 'right',
                            width: '130px'
                        },  {
                            title: "描述",
                            field: 'des',
                            rowspan: 1,
                            align: 'left',
                            width: '120px'
                        }],
                        // rowStyle: function (row, index) {
                        //     var style;
                        //     if (row.isRed == 1) {
                        //         style = 'redRow1'
                        //     }
                        //     return {
                        //         classes: style
                        //     }
                        // }
                    }, page.logic.queryParams)
                }

            },
            formatterNumber: function (value, row, index) {
                if (value != '')
                    return value.indexOf('～') > -1 ? new Number(value.split("～")[0]) + "～" + new Number(value.split("～")[1]) : value.substring(0, 1) + new Number(value.substring(1));
                return '';
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    isQueryFlag:isQueryFlag,
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.edit(\'' + rowData.alarmPointId + '\')">编辑</a> &nbsp;&nbsp;' +
                    '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.alarmPointId + '\')" >删除</a> '
                ]
            },
            /**
             * 批量删除
             */
            delAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmPointId);

                })

                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST

                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function () {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function (id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false, //
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function () {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 新增
             */
            add: function () {
                var pageMode = PageModelEnum.NewAdd;
                var title = "报警点新增";
                page.logic.detail(title, "", pageMode);
            },
            /**
             * 编辑
             * @param factoryId
             */
            edit: function (prdtCellId) {
                var pageMode = PageModelEnum.Edit;
                var title = "报警点编辑";
                page.logic.detail(title, prdtCellId, pageMode);
            },
            /**
             * 装置新增或者编辑详细页面
             */
            detail: function (title, alarmPointId, pageMode) {
                layer.open({
                    type: 2,
                    title: title,
                    closeBtn: 1,
                    area: ['1000px', '81%'],
                    shadeClose: false,
                    offset: '30px',
                    content: 'AlarmPointAddOrEdit.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "alarmPointId": alarmPointId,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                        }
                    }
                })
            },
            /**
             * 设置参数
             */
            setData: function () {
                page.data.param = OPAL.form.getData('searchForm');
            },
            /**
             * 搜索
             */
            search: function () {
                page.logic.setData();
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect("unitIds", commonUnitTreeUrl, "id", "parentId", "sname", {
                    data: {
                        'enablePrivilege': enablePrivilege
                    },
                    onChange: function (node) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds");
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }

                    }
                }, false, function () {

                });
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function (unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', UnitPrdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化查询inUse
             */
            initInUse: function () {
                OPAL.ui.getCombobox("inUse", inUseUrl, {
                    selectValue: 1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            /**
             * 初始化查询报警点类型
             */
            initAlarmPointType: function () {
                OPAL.ui.getCombobox('typeId', AlarmPointTypeUrl, {
                    keyField: "alarmPointTypeId",
                    valueField: "name",
                    selectValue: -1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            // 初始化仪表优先级
            initInstrmtPriority: function() {
                OPAL.ui.getCombobox('instrmtPriority', getInstrmtPriUrl, {
                    async: false,
                    selectValue: -1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            /**
             * 导入
             */
            importExcel: function () {
                //实例化一个plupload上传对象
                var uploader = new plupload.Uploader({
                    browse_button: 'AlarmPointImport', //触发文件选择对话框的按钮，为那个元素id
                    url: importAlarmPointUrl, //服务器端的上传页面地址
                    flash_swf_url: '../../../js/common/plupload/js/Moxie.swf', //swf文件，当需要使用swf方式进行上传时需要配置该参数
                    multi_selection: false,
                    filters: {
                        mime_types: [{
                            title: "Excel files",
                            extensions: "xls,xlsx"
                        }]
                    }
                });
                uploader.init();
                uploader.bind('FilesAdded', function (uploader, files) {
                    uploader.start();
                    index = layer.msg('加载中', {
                        icon: 16,
                        shade: 0.1,
                        time: 0
                    });
                });
                uploader.bind('FileUploaded', function (uploader, file, responseObject) {
                    var msg = responseObject.response;
                    if (msg.indexOf('导入成功') != -1) {
                        var index = layer.confirm(msg, {
                            btn: ['确定']
                        }, function () {
                            layer.close(index);
                            $("#table").bootstrapTable('refresh', {
                                "url": searchUrl,
                                "pageNumber": 1
                            });
                        })
                    } else {
                        layer.confirm(msg, {
                            btn: ['确定']
                        })
                    }
                })
                uploader.bind('Error', function (uploader, errObject) {
                    layer.close(index);
                    if (errObject.response != undefined && errObject.response != '') {
                        var message = $.parseJSON(errObject.response).collection.error.message;
                    } else {
                        var message = errObject.message
                    }
                    layer.confirm(message, {
                        btn: ['确定']
                    })
                });
            },
            /**
             * 导出
             */
            exportExcel: function () {
                var titleArray = new Array();
                var tableTitle = $('#table').bootstrapTable('getOptions').columns[0];
                $.each(tableTitle, function(i, el) {
                    if (el.field !== 'state' && el.title !== '序号' && el.title !== '操作') {
                        titleArray.push({
                            'key': el.field,
                            'value': el.title
                        })
                    }
                })
                var data = {};
                var pageSize = $('#table').bootstrapTable('getOptions').pageSize;
                var pageNumber = $('#table').bootstrapTable('getOptions').pageNumber;
                data.titles = JSON.stringify(titleArray);
                data.pageSize = pageSize;
                data.pageNumber = pageNumber;
                page.logic.setData();
                $.extend(data, page.data.param);
                $('#formExPort').attr('action', exportAlarmPointUrl);
                $('#titles').val(data.titles);
                $('#pageSize').val(data.pageSize);
                $('#pageNumber').val(data.pageNumber);
                $('#unitIds1').val(data.unitIds);
                $('#prdtCellIds1').val(data.prdtCellIds);
                $('#tag1').val(data.tag);
                $('#instrmtPriority1').val(data.instrmtPriority);
                $('#typeId1').val(data.typeId);
                $('#inUse1').val(data.inUse);
                $("#pageModel").val(isQueryFlag);
                $('#formExPort').submit();
            },

            /**
             * 导入模板下载
             */
            downFile: function () {
                $('#form_Down').attr('action', downAlarmPointTemplateUrl);
                $('#fileName').val('报警点信息导入模板');
                $('#templateName').val('alarmPointTemplate');
                $('#form_Down').submit();
            },
            /**
             * 获取页面URL参数
             * @param  {name}
             */
            getQueryParam: function (name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
                var r = window.location.search.substr(1).match(reg); //匹配目标参数
                if (r != null) return unescape(r[2]);
                return null; //返回参数值
            },

            /**
             * 改变页面标题
             */
            changeTitle: function () {
                if (isQueryFlag != null && isQueryFlag != '' && isQueryFlag != undefined && isPointQuery != null && isPointQuery!= '' && isPointQuery!= undefined) {
                    if (isQueryFlag == 0) {
                        enablePrivilege = true;
                        if(isPointQuery == 1){
                        $('#pageTitle').text('报警点查询');
                         document.title = '报警点查询';
                        $('#AlarmPointAdd').css('display', 'none');
                        $('#AlarmPointDel').css('display', 'none');
                        $('#AlarmPointImport').css('display', 'none');
                        // $('#AlarmPointExport').css('display', 'none');
                        $('#AlarmPointExport').css('display', 'inline-block');
                        $('#DownFile').css('display', 'none');
                        }else if(isPointQuery == 0){
                        $('#pageTitle').text('报警点维护');
                         document.title = '报警点维护';
                        $('#AlarmPointAdd').css('display', 'inline-block');
                        $('#AlarmPointDel').css('display', 'inline-block');
                        $('#AlarmPointImport').css('display', 'inline-block');
                        $('#AlarmPointExport').css('display', 'inline-block');
                        $('#DownFile').css('display', 'inline-block');
                        }
                    }
                    else if (isQueryFlag == 1) {
                        enablePrivilege = true;
                        $('#pageTitle').text('报警台账查询');
                        document.title = '报警台账查询'
                        if(isPointQuery == 1){
                        $('#pageTitle').text('报警点查询');
                         document.title = '报警点查询';
                        $('#AlarmPointAdd').css('display', 'none');
                        $('#AlarmPointDel').css('display', 'none');
                        $('#AlarmPointImport').css('display', 'none');
                        $('#AlarmPointExport').css('display', 'inline-block');
                        $('#DownFile').css('display', 'none');
                        }else if(isPointQuery == 0){
                        $('#pageTitle').text('报警台账查询');
                        document.title = '报警台账查询'
                        $('#AlarmPointAdd').css('display', 'none');
                        $('#AlarmPointDel').css('display', 'none');
                        $('#AlarmPointImport').css('display', 'none');
                        $('#AlarmPointExport').css('display', 'inline-block');
                        $('#DownFile').css('display', 'none');
                        }
                    }
                }
            },

        }

    }
    page.init();
    window.page = page;
})