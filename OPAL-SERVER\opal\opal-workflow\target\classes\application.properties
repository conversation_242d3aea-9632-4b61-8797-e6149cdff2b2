﻿#web服务端口
server.port=8089
server.context-path=/opalworkflow
#配置数据---Oracle--开发环境

OPAL_DB=10.238.220.173:1521:opal
OPAL_DB_USERNAME=mes_opal_dev
OPAL_DB_PASSWORD=mes_opal_dev

spring.datasource.driverClassName=oracle.jdbc.OracleDriver
spring.datasource.url=jdbc:oracle:thin:@${OPAL_DB}
spring.datasource.username=${OPAL_DB_USERNAME}
spring.datasource.password=${OPAL_DB_PASSWORD}
spring.jpa.database-platform=org.hibernate.dialect.Oracle10gDialect
#
# HikariCP settings
# spring.datasource.hikari.*

spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.maximum-pool-size=500

#spring.jpa
spring.jpa.hibernate.show_sql = false
spring.jpa.show_sql = false
spring.jpa.open-in-view= true
spring.jpa.properties.hibernate.proc.param_null_passing=true
hibernate.proc.param_null_passing=true
spring.jpa.hibernate.jdbc.batch_size = 30
spring.jpa.hibernate.cache.use_second_level_cache = false

#应用配置
imp.appcode = demo
#平台服务地址配置
imp.bizlogsvc_address_base = **************:30009/bizlog

#spring boot 设置文件上传最大限制
spring.http.multipart.max-file-size=50MB
spring.http.multipart.max-request-size=50MB


aaa.aaa_address_base=http://**************:8080/IP/AAA/LoginService
#测试环境
#aaa.aaa_address_base=http://*************:8080/IP/AAA/LoginService
#AAA组织机构和用户服务访问地址
aaa.organduser.url=http://*************
#Promace AAA资源服务访问地址
aaa_code =OPAL
aaa.appCode = ${aaa_code}
aaa_resouce_url=http://*************
aaa.resource.url=${aaa_resouce_url}/ResourceService
#操作报警装置属性配置
aaa_resource_oaplrunitProperty = OPALUNIT_PROPERTY
aaa.resource.opalunitProperty =${aaa_resource_oaplrunitProperty}
#AAA使用版本old:老版本,new:新版本
aaa_version=promace


#AAA班组WebService地址(后缀不用带"/")
aaa.shiftcalendarsvc_address_base=http://**************:8080
#promace shift
promace.imp.shift.base.url=http://shift.wsm.qlsh.promace.sinopec.com


#工厂模型-根Url
factorymodel.base.url=http://pm.wsm.qlsh.promace.sinopec.com
fm_bizCode=qlsh
fm_unit_type_code=plants
factorymodel.bizCode=${fm_bizCode}
fm_factoryTypeCode=1005
factorymodel.factoryTypeCode=${fm_factoryTypeCode}
fm_workshopTypeCode=1007
factorymodel.workshopTypeCode=${fm_workshopTypeCode}

#运行环境类型promace,other
runtime_type=promace
#是否开启装置数据权限 1：开启，0：不开启
aaa_auth=1 


#工艺系统审批地址
tec_audit_url=http://************:8080
tec.audit.url=${tec_audit_url}/IP/WebService/OpeAlarmCraftIndexService.asmx

#附件上传浏览器地址
alfresco_browser_url=http://**************:28080/alfresco/api/-default-/public/cmis/versions/1.1/browser
#附件上传工具用户名
alfresco_username=admin
#附件上传工具密码
alfresco_password=123456
#附件上传工具目录名
alfresco_folder_name=OPAL
#附件上传工具文件大小限制,如果不配置此值，或者value配置为空，则认为不限制上传文件大小
alfresco_file_size=50