package com.pcitc.opal.ad.dao.imp;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import com.pcitc.opal.common.*;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.pcitc.opal.ad.dao.AlarmEventViewExRepositoryCustom;
import com.pcitc.opal.ad.pojo.AlarmEventViewEx;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.common.pojo.DateRange;

import pcitc.imp.common.ettool.utils.ObjectConverter;

/*
 * AlarmEventViewEx视图的Repository实现   
 * 模块编号：pcitc_opal_dal_class_AlarmEventViewExRepositoryImpl
 * 作	者：dageng.sun
 * 创建时间：2017/10/25
 * 修改编号：1
 * 描	述：AlarmEventViewEx视图的Repository实现
 */
public class AlarmEventViewExRepositoryImpl extends BaseRepository<AlarmEventViewEx, Long> implements AlarmEventViewExRepositoryCustom {
	 @Autowired
     private DbConfig dbConfig;
	/**
	 * 报警响应分页查询
	 * 
	 * <AUTHOR> 2017-10-24
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 位号
	 * @param alarmFlagId 报警表示
	 * @param priority 优先级
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param isTimeResponse 是否及时响应
	 * @param dateRangeList 日期区间集合对象
	 * @param responseDuration 响应时长
	 * @param page 翻页实现类
	 * @return
	 * @throws Exception 
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	@Transactional
	public PaginationBean<AlarmEventViewEx> getAlarmRespond(String[] unitCodes, Long[] prdtCellIds,
			String tag, Long alarmFlagId, Integer priority, Date beginTime, Date endTime, Integer isTimeResponse,
			List dateRangeList, Integer responseDuration, Pagination page) throws Exception {
		try {
			List<DateRange> dateList = ObjectConverter.listConverter(dateRangeList, DateRange.class);
			Map<String, Object> map=getHqlAndParams(unitCodes, prdtCellIds, tag, alarmFlagId, priority, beginTime, endTime, isTimeResponse, dateList, responseDuration);
			
			// 调用基类方法查询返回结果
			PaginationBean<AlarmEventViewEx> bean = this.findAll(page, (String)map.get("hql"), (Map<String, Object>)map.get("paramList"));
			return bean;
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 报警响应分页查询
	 *
	  * <AUTHOR> 2017-10-24
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 位号
	 * @param alarmFlagId 报警表示
	 * @param priority 优先级
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param isTimeResponse 是否及时响应
	 * @param dateRangeList 日期区间集合对象
	 * @param responseDuration 响应时长
	 * @param page 翻页实现类
	 * @return
	 * @throws Exception 
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	@Transactional
	public PaginationBean<AlarmEventViewEx> getAlarmRespondFor20S(String[] unitCodes, Long[] prdtCellIds,
															String tag, Long alarmFlagId, Integer priority, Date beginTime, Date endTime, Integer isTimeResponse,
															List dateRangeList, Integer responseDuration, Pagination page) throws Exception {
		try {
			List<DateRange> dateList = ObjectConverter.listConverter(dateRangeList, DateRange.class);
			Map<String, Object> map=getHqlAndParamsFor20S(unitCodes, prdtCellIds, tag, alarmFlagId, priority, beginTime, endTime, isTimeResponse, dateList, responseDuration);

			// 调用基类方法查询返回结果
			PaginationBean<AlarmEventViewEx> bean = this.findAll(page, (String)map.get("hql"), (Map<String, Object>)map.get("paramList"));
			return bean;
		} catch (Exception ex) {
			throw ex;
		}
	}
	/**
	 * 报警响应及时的总数(不包含是否响应及时)
	 * 
	 * <AUTHOR> 2017-10-25
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 位号
	 * @param alarmFlagId 报警表示
	 * @param priority 优先级
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param dateRangeList 班组时间集合
	 * @param responseDuration 响应时长
	 * @return
	 * @throws Exception 
	 * @return Integer 
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	@Transactional
	public Long getRespondTimeCount(String[] unitCodes, Long[] prdtCellIds, String tag, Long alarmFlagId,
			Integer priority, Date beginTime, Date endTime, List dateRangeList, Integer responseDuration) throws Exception {
		try {
			List<DateRange> dateList = ObjectConverter.listConverter(dateRangeList, DateRange.class);
			Map<String, Object> map=getHqlAndParams(unitCodes, prdtCellIds, tag, alarmFlagId, priority, beginTime, endTime, 1, dateList, responseDuration);
			
			// 调用基类方法查询返回结果
			Long count = this.getCount((String)map.get("hql"), (Map<String, Object>)map.get("paramList"));
			return count;
		} catch (Exception ex) {
			throw ex;
		}
	}
	
	/**
	 * 符合条件的总数(不包含是否响应及时)
	 * 
	 * <AUTHOR> 2017-10-25
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 位号
	 * @param alarmFlagId 报警表示
	 * @param priority 优先级
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param dateRangeList 班组时间集合
	 * @param responseDuration 响应时长
	 * @return
	 * @throws Exception 
	 * @return Integer 
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	@Transactional
	public Long getRespondCount(String[] unitCodes, Long[] prdtCellIds, String tag, Long alarmFlagId,
			Integer priority, Date beginTime, Date endTime, List dateRangeList, Integer responseDuration) throws Exception {
		try {
			List<DateRange> dateList = ObjectConverter.listConverter(dateRangeList, DateRange.class);
			Map<String, Object> map=getHqlAndParams(unitCodes, prdtCellIds, tag, alarmFlagId, priority, beginTime, endTime, null, dateList, responseDuration);
			
			// 调用基类方法查询返回结果
			Long count = this.getCount((String)map.get("hql"), (Map<String, Object>)map.get("paramList"));
			return count;
		} catch (Exception ex) {
			throw ex;
		}
	}
	
	/**
	 * 抽取公共方法，获得hql语句和params参数
	 * 
	 * <AUTHOR> 2017-10-26
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 位号
	 * @param alarmFlagId 报警表示
	 * @param priority 优先级
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param isTimeResponse 是否及时响应
	 * @param responseDuration 响应时长
	 * @return 
	 * @return Map<String,Object> map对象
	 */
	@Transactional
	public Map<String, Object> getHqlAndParams(String[] unitCodes, Long[] prdtCellIds, String tag, Long alarmFlagId,
			Integer priority, Date beginTime, Date endTime, Integer isTimeResponse, List<DateRange> dateList, Integer responseDuration){
		// 查询字符串
		StringBuilder hql = new StringBuilder("select aevx from AlarmEventViewEx aevx ");
		StringBuilder hqlWhere = new StringBuilder("where 1=1 and ap.companyId=:companyId ");
		hqlWhere.append(" and aevx.priority is not null ");
		// 参数集合
		Map<String, Object> paramList = new HashMap<String, Object>();
		// 位号
		hql.append("inner join fetch aevx.alarmPoint ap ");
		if (!StringUtils.isEmpty(tag)) {
			hqlWhere.append("and upper(ap.tag) like upper(:tag) escape '/' ");
			paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
		}
		// 装置
		hql.append("inner join fetch ap.prdtCell pc ");
		if (ArrayUtils.isNotEmpty(unitCodes)) {
			hqlWhere.append(" and pc.unitId in (:unitIds) ");
			paramList.put("unitIds", Arrays.asList(unitCodes));
		}
		// 生产单元
		if (ArrayUtils.isNotEmpty(prdtCellIds) && unitCodes.length == 1) {
			hqlWhere.append("and pc.prdtCellId in (:prdtCellIds) ");
			paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
		}
		// 报警点标识
		hql.append("inner join fetch aevx.alarmFlag af ");
		if (!StringUtils.isEmpty(alarmFlagId) && alarmFlagId != -1) {
			hqlWhere.append("and af.alarmFlagId=:alarmFlagId ");
			paramList.put("alarmFlagId", alarmFlagId);
		}
		// 优先级
		if (!StringUtils.isEmpty(priority) && priority != -1) {
			hqlWhere.append("and aevx.priority=:priority ");
			paramList.put("priority", priority);
		}
		// 日期
		hqlWhere.append("and aevx.alarmTime between :beginTime and :endTime ");
		paramList.put("beginTime", beginTime);
		paramList.put("endTime", endTime);
		// 是否及时响应
		if (!StringUtils.isEmpty(isTimeResponse) && isTimeResponse != -1) {
			if(isTimeResponse == 1){
//				hqlWhere.append("and aevx.responseTime is not null and (aevx.responseTime-aevx.alarmTime)*24*60*60<=tem.prescribedResponseDuration and aevx.responseTime<=:filterTime ");
				hqlWhere.append("and aevx.responseTime is not null and ("+ DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime") +")*24*60*60<=tem.prescribedResponseDuration and aevx.responseTime<=:filterTime ");
				paramList.put("filterTime", endTime);
			}else if(isTimeResponse == 2){
//				hqlWhere.append("and (aevx.responseTime is null or (aevx.responseTime-aevx.alarmTime)*24*60*60>tem.prescribedResponseDuration or aevx.responseTime>:timeOut) ");
				hqlWhere.append("and (aevx.responseTime is null or ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60*60>tem.prescribedResponseDuration or aevx.responseTime>:timeOut) ");
				paramList.put("timeOut", endTime);
			}
		}
		if (dateList != null && dateList.size() > 0) {
			this.getEntityManager().clear();
			this.saveDateRangeList(dateList);
			hqlWhere.append("and exists(select dr from  DateRange dr where aevx.alarmTime >=dr.startTime and aevx.alarmTime<dr.endTime) ");
		}
		//过滤报警标识
		hqlWhere.append(" and exists (from AlarmFlagComp afc where afc.alarmFlagId=af.alarmFlagId and afc.inUse=1) ");
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		// 查询条件响应时长
		if(!StringUtils.isEmpty(responseDuration) && responseDuration != -1){
			if(responseDuration.intValue() == 1){
//				hqlWhere.append("and (aevx.responseTime-aevx.alarmTime)*24*60<=0.5 ");
				hqlWhere.append("and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60<=0.5 ");
			}else if(responseDuration.intValue() == 2){
//				hqlWhere.append("and (aevx.responseTime-aevx.alarmTime)*24*60>0.5 and (aevx.responseTime-aevx.alarmTime)*24*60<=2 ");
				hqlWhere.append("and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60>0.5 and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60<=2 ");
			}else if(responseDuration.intValue() == 3){
//				hqlWhere.append("and (aevx.responseTime-aevx.alarmTime)*24*60>2 and (aevx.responseTime-aevx.alarmTime)*24*60<=5 ");
				hqlWhere.append("and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60>2 and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60<=5 ");
			}else if(responseDuration.intValue() == 4){
//				hqlWhere.append("and (aevx.responseTime-aevx.alarmTime)*24*60>5 ");
				hqlWhere.append("and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60>5 ");
			}else if(responseDuration.intValue() == 5){
				hqlWhere.append("and aevx.responseTime is null ");
			}
		}
		// 响应时间不为空
//			hqlWhere.append("order by (case when aevx.responseTime>to_date('"+sdf.format(endTime)+"','yyyy-mm-dd hh24:mi:ss') then null else (aevx.responseTime-aevx.alarmTime) end) desc,aevx.alarmTime desc,ap.tag,af.name ");
		hqlWhere.append("order by (case when aevx.responseTime>"+DbConversion.DbDateTransformYmdhmsToDate(sdf.format(endTime))+" then null else ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+") end) desc,aevx.alarmTime desc,ap.tag,af.name ");
		// 报警点附加信息
		hql.append("inner join fetch aevx.tagExtraMessage tem ");
		//企业
		CommonProperty commonProperty = new CommonProperty();
		paramList.put("companyId",commonProperty.getCompanyId());

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("hql", hql.toString() + hqlWhere.toString());
		map.put("paramList", paramList);
		return map;
	}

	@Transactional
	public Map<String, Object> getHqlAndParamsFor20S(String[] unitCodes, Long[] prdtCellIds, String tag, Long alarmFlagId,
											   Integer priority, Date beginTime, Date endTime, Integer isTimeResponse, List<DateRange> dateList, Integer responseDuration){
		// 查询字符串
		StringBuilder hql = new StringBuilder("select aevx from AlarmEventViewEx aevx ");
		StringBuilder hqlWhere = new StringBuilder("where 1=1 ");
		hqlWhere.append(" and aevx.priority is not null and ap.inUse =1");
		// 参数集合
		Map<String, Object> paramList = new HashMap<String, Object>();
		// 位号
		hql.append("inner join fetch aevx.alarmPoint ap ");
		if (!StringUtils.isEmpty(tag)) {
			hqlWhere.append("and upper(ap.tag) like upper(:tag) escape '/' ");
			paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
		}
		// 装置
		hql.append("inner join fetch ap.prdtCell pc ");
		if (ArrayUtils.isNotEmpty(unitCodes)) {
			hqlWhere.append(" and pc.unitId in (:unitIds) ");
			paramList.put("unitIds", Arrays.asList(unitCodes));
		}
		// 生产单元
		if (ArrayUtils.isNotEmpty(prdtCellIds) && unitCodes.length == 1) {
			hqlWhere.append("and pc.prdtCellId in (:prdtCellIds) ");
			paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
		}
		// 报警点标识
		hql.append("inner join fetch aevx.alarmFlag af ");
		if (!StringUtils.isEmpty(alarmFlagId) && alarmFlagId != -1) {
			hqlWhere.append("and af.alarmFlagId=:alarmFlagId ");
			paramList.put("alarmFlagId", alarmFlagId);
		}
		// 优先级
		if (!StringUtils.isEmpty(priority) && priority != -1) {
			hqlWhere.append("and aevx.priority=:priority ");
			paramList.put("priority", priority);
		}
		// 日期
		hqlWhere.append("and aevx.alarmTime between :beginTime and :endTime ");
		paramList.put("beginTime", beginTime);
		paramList.put("endTime", endTime);
		// 是否及时响应
		if (!StringUtils.isEmpty(isTimeResponse) && isTimeResponse != -1) {
			if(isTimeResponse == 1){
//				hqlWhere.append("and aevx.responseTime is not null and (aevx.responseTime-aevx.alarmTime)*24*60*60<=tem.prescribedResponseDuration and aevx.responseTime<=:filterTime ");
				hqlWhere.append("and aevx.responseTime is not null and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60*60<=tem.prescribedResponseDuration and aevx.responseTime<=:filterTime ");
				paramList.put("filterTime", endTime);
			}else if(isTimeResponse == 2){
//				hqlWhere.append("and (aevx.responseTime is null or (aevx.responseTime-aevx.alarmTime)*24*60*60>tem.prescribedResponseDuration or aevx.responseTime>:timeOut) ");
				hqlWhere.append("and (aevx.responseTime is null or ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60*60>tem.prescribedResponseDuration or aevx.responseTime>:timeOut) ");
				paramList.put("timeOut", endTime);
			}
		}
		if (dateList != null && dateList.size() > 0) {
			this.getEntityManager().clear();
			this.saveDateRangeList(dateList);
			hqlWhere.append("and exists(select dr from  DateRange dr where aevx.alarmTime >=dr.startTime and aevx.alarmTime<dr.endTime) ");
		}
		//过滤报警标识
		hqlWhere.append(" and exists (from AlarmFlagComp afc where afc.alarmFlagId=af.alarmFlagId and afc.inUse=1) ");
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		// 查询条件响应时长
		if(!StringUtils.isEmpty(responseDuration) && responseDuration != -1){
			if(responseDuration.intValue() == 1){
//				hqlWhere.append("and (aevx.responseTime-aevx.alarmTime)*24*60<=0.5 ");
				hqlWhere.append("and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60<=0.5 ");
			}else if(responseDuration.intValue() == 2){
//				hqlWhere.append("and (aevx.responseTime-aevx.alarmTime)*24*60>0.5 and (aevx.responseTime-aevx.alarmTime)*24*60<=2 ");
				hqlWhere.append("and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60>0.5 and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60<=2 ");
			}else if(responseDuration.intValue() == 3){
//				hqlWhere.append("and (aevx.responseTime-aevx.alarmTime)*24*60>2 and (aevx.responseTime-aevx.alarmTime)*24*60<=5 ");
				hqlWhere.append("and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60>2 and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60<=5 ");
			}else if(responseDuration.intValue() == 4){
//				hqlWhere.append("and (aevx.responseTime-aevx.alarmTime)*24*60>5 ");
				hqlWhere.append("and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60>5 ");
			}else if(responseDuration.intValue() == 5){
				hqlWhere.append("and aevx.responseTime is null ");
			}else if(responseDuration.intValue() == 20){
//				hqlWhere.append("and (aevx.responseTime-aevx.alarmTime)*24*60*60<=20 ");
				hqlWhere.append("and ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+")*24*60*60<=20 ");
			}
		}
		// 响应时间不为空
//			hqlWhere.append("order by aevx.alarmTime desc,(case when aevx.responseTime>to_date('"+sdf.format(endTime)+"','yyyy-mm-dd hh24:mi:ss') then null else (aevx.responseTime-aevx.alarmTime) end) desc,ap.tag,af.name ");
		hqlWhere.append("order by aevx.alarmTime desc,(case when aevx.responseTime>"+DbConversion.DbDateTransformYmdhmsToDate(sdf.format(endTime))+" then null else ("+DbConversion.dateFieldSub("aevx.responseTime","aevx.alarmTime")+") end) desc,ap.tag,af.name ");

		// 报警点附加信息
		hql.append("inner join fetch aevx.tagExtraMessage tem ");

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("hql", hql.toString() + hqlWhere.toString());
		map.put("paramList", paramList);
		return map;
	}

	
}
