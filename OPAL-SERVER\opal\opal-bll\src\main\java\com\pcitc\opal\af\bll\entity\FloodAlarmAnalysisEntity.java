package com.pcitc.opal.af.bll.entity;

import com.pcitc.opal.ad.bll.entity.AlarmEventViewEntity;

import java.io.Serializable;
/*
 * 高频报警分析实体
 * 模块编号：pcitc_opal_bll_class_FloodAlarmAnalysisEntity
 * 作  　者：xuelei.wang
 * 创建时间：2017-11-15
 * 修改编号：1
 * 描    述：高频报警分析实体
 */
@SuppressWarnings("serial")
public class FloodAlarmAnalysisEntity implements Serializable {
    /**
     * 报警事件视图实体
     */
    private AlarmEventViewEntity viewEntity;
    /**
     * 报警事件视图实体
     */
    private AlarmEventViewEntity nextEntity;

    public AlarmEventViewEntity getViewEntity() {
        return viewEntity;
    }

    public void setViewEntity(AlarmEventViewEntity viewEntity) {
        this.viewEntity = viewEntity;
    }

    public AlarmEventViewEntity getNextEntity() {
        return nextEntity;
    }

    public void setNextEntity(AlarmEventViewEntity nextEntity) {
        this.nextEntity = nextEntity;
    }
}