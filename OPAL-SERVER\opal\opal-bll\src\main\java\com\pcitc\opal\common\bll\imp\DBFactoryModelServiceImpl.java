package com.pcitc.opal.common.bll.imp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonUtil;
import com.pcitc.opal.common.ComparatorList;
import com.pcitc.opal.common.bll.AAAService;
import com.pcitc.opal.common.bll.FactoryModelService;
import com.pcitc.opal.common.bll.entity.*;
import com.pcitc.opal.pm.pojo.Unit;
import com.pcitc.opal.pm.pojo.Workshop;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
@Service
@ConditionalOnProperty(name ="runtime_type",havingValue = "other")
public class DBFactoryModelServiceImpl implements FactoryModelService{

    private final static Log logger = LogFactory.getLog(DBFactoryModelServiceImpl.class);

    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private AAAService aaaService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 获取装置树形结构
     *
     * @param enablePrivilege 是否启用权限过滤
     * @return 树形实体集合
     * <AUTHOR> 2017-09-25
     */
    @Override
    public List<OrgEntity> getAllUnitTree(boolean enablePrivilege) throws Exception {
        try {
            List<OrgEntity> orgEntityList = new ArrayList<>();
            //1.获取所有已启用的生产装置列表
            StringBuilder hql = new StringBuilder();
            hql.append("from Unit u ");
            hql.append("inner join fetch u.workshop w ");
            hql.append("inner join fetch w.factory f ");
            hql.append("where u.companyId=:companyId and u.inUse=:inUse ");
            hql.append(" and w.inUse=:inUse ");
            hql.append(" and f.inUse=:inUse ");
            Map<String, Object> paramList = new HashMap<>();
            paramList.put("inUse", CommonEnum.InUseEnum.Yes.getIndex());

//ToLocal
            if (enablePrivilege) {
                List<String> codeList = getAuthorizePropertyList(new CommonProperty().getUserId()).stream().map(item -> item.getStdCode()).collect(Collectors.toList());
                hql.append("and u.stdCode in(:stdCode) ");
                paramList.put("stdCode", codeList);
            }
//ToLocal
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            Query query = entityManager.createQuery(hql.toString(), Unit.class);
            setParameterList(query, paramList);
            List<Unit> unitList = query.getResultList();
            //Long id = 0L;
            for (Unit u : unitList) {
                OrgEntity unitEntity = new OrgEntity();
                unitEntity.setId(u.getStdCode());
                unitEntity.setOriginalId(u.getStdCode());
                unitEntity.setType(2);
                unitEntity.setSname(u.getSname());
                unitEntity.setSortNum(u.getSortNum());
                orgEntityList.add(unitEntity);
                OrgEntity wsEntity = orgEntityList.stream().filter(i->i.getType()==1 && i.getOriginalId().equals(u.getWorkshop().getStdCode())).findFirst().orElse(null);
                if(wsEntity == null) {
                    wsEntity = new OrgEntity();
                    wsEntity.setId(u.getWorkshop().getStdCode());
                    wsEntity.setOriginalId(u.getWorkshop().getStdCode());
                    wsEntity.setType(1);
                    wsEntity.setSname(u.getWorkshop().getSname());
                    wsEntity.setSortNum(u.getWorkshop().getSortNum());
                    orgEntityList.add(wsEntity);
                }
                OrgEntity ftEntity = orgEntityList.stream().filter(i->i.getType()==0 && i.getOriginalId().equals(u.getWorkshop().getFactory().getStdCode())).findFirst().orElse(null);
                if(ftEntity == null) {
                    ftEntity = new OrgEntity();
                    ftEntity.setId(u.getWorkshop().getFactory().getStdCode());
                    ftEntity.setOriginalId(u.getWorkshop().getFactory().getStdCode());
                    ftEntity.setType(0);
                    ftEntity.setSname(u.getWorkshop().getFactory().getSname());
                    ftEntity.setSortNum(u.getWorkshop().getFactory().getSortNum());
                    orgEntityList.add(ftEntity);
                }
                ftEntity.setParentId("0");
                unitEntity.setParentId(wsEntity.getId());
                wsEntity.setParentId(ftEntity.getId());
            }
            orgEntityList = orgEntityList.stream()
                    .sorted(Comparator.comparing(OrgEntity::getType)
                            .thenComparing(item -> item.getSortNum() == null ? 0 : item.getSortNum(), Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(OrgEntity::getSname, ComparatorList.orderByASC()))
                    .collect(Collectors.toList());
            return orgEntityList;
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取生产装置列表
     *
     * @param enablePrivilege 是否启用权限过滤
     * @return 生产装置列表
     * <AUTHOR> 2017-09-26
     */
    @Override
    public List<UnitEntity> getUnitList(boolean enablePrivilege) throws Exception {
        return this.getUnitListByIds(null, false, enablePrivilege);
    }

    /**
     * 根据装置编码集合获取装置列表
     *
     * @param unitCodes         装置编码集合
     * @param isAll              是否显示全部
     * @param enablePrivilege 是否启用权限过滤
     * @return 装置列表
     * <AUTHOR> 2017-09-26
     */
    @Override
    public List<UnitEntity> getUnitListByIds(String[] unitCodes, boolean isAll, boolean enablePrivilege) throws Exception {
        List<UnitEntity> unitEntityList = new ArrayList<>();
        Map<String, Object> paramList = new HashedMap();
        List<String> codeList = null;
        try {
            StringBuilder hql = new StringBuilder();
            hql.append("from Unit u where u.inUse = 1 and u.companyId=:companyId ");
            if (enablePrivilege) {
                codeList = getAuthorizePropertyList(new CommonProperty().getUserId()).stream().map(item -> item.getStdCode()).collect(Collectors.toList());
                if(unitCodes != null && unitCodes.length > 0){
                    codeList.retainAll(CollectionUtils.arrayToList(unitCodes));
                }
                if (codeList.size() == 0) {
                    UnitEntity entity = new UnitEntity();
                    entity.setStdCode("-1");
                    unitEntityList.add(entity);
                    return unitEntityList;
                }
            }else{
                if (unitCodes != null && unitCodes.length > 0)
                    codeList = Arrays.asList(unitCodes);
            }
//ToLocal
            if(codeList!=null && codeList.size()>0) {
                hql.append("and u.stdCode in (:stdCode) ");
                paramList.put("stdCode", codeList);
            }
//ToLocal
            hql.append(" order by u.sortNum,u.sname asc ");
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            Query query = entityManager.createQuery(hql.toString(), Unit.class);
            setParameterList(query, paramList);
            List<Unit> unitList = query.getResultList();
            unitEntityList = this.convertToEntity(unitList);
            if (isAll && unitEntityList.size() > 1) {
                UnitEntity unitEntity = new UnitEntity();
                unitEntity.setStdCode("-1");
                unitEntity.setSname("全部");
                unitEntityList.add(0, unitEntity);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return unitEntityList;
    }

    /**
     * 根据车间ID集合获取该车间下所有的已启用的装置列表
     *
     * @param workshopCodes
     * @param enablePrivilege enablePrivilege
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-7
     */
    @Override
    public List<UnitEntity> getUnitListByWorkshopIds(String[] workshopCodes, boolean enablePrivilege) throws Exception {
        try {
            if (workshopCodes == null) return new ArrayList<>();
            Map<String, Object> paramList = new HashedMap();
            paramList.put("inUse", CommonEnum.InUseEnum.Yes.getIndex());
            paramList.put("workshopCodes", Arrays.asList(workshopCodes));
            StringBuilder hql = new StringBuilder();
            hql.append("select u from Unit u inner join u.workshop w where u.inUse=:inUse and w.inUse=:inUse and u.companyId=:companyId ");
            if (enablePrivilege) {
                List<String> codeList = getAuthorizePropertyList(new CommonProperty().getUserId()).stream().map(item -> item.getStdCode()).collect(Collectors.toList());
                if (codeList.size() == 0) {
                    List<UnitEntity> list = new ArrayList<>();
                    UnitEntity entity = new UnitEntity();
                    entity.setStdCode("-1");
                    list.add(entity);
                    return list;
                }
                hql.append("and u.stdCode in (:stdCode) ");
                paramList.put("stdCode", codeList);
            }
            hql.append(" and w.stdCode in (:workshopCodes) order by u.sortNum,u.sname asc");
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            Query query = entityManager.createQuery(hql.toString(), Unit.class);
            setParameterList(query, paramList);
            List<Unit> unitList = query.getResultList();
            return convertToEntity(unitList);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 根据车间编码集合获取车间列表
     *
     * @param workshopCodes 车间编码
     * @return 车间集合
     * @throws Exception
     * <AUTHOR> 2017-11-7
     */
    @Override
    public List<WorkshopEntity> getWorkshopListByWorkshopIds(String[] workshopCodes) throws Exception {
        Map<String, Object> paramList = new HashedMap();
        String workshopHql = "select w from Workshop w inner join fetch w.factory f where w.inUse=:inUse %s order by w.sortNum,w.sname asc";
        String where = " ";
        if (workshopCodes != null) {
            paramList.put("workshopCodes", Arrays.asList(workshopCodes));
            where = " and w.stdCode in(:workshopCodes)  ";
        }
        paramList.put("inUse", CommonEnum.InUseEnum.Yes.getIndex());
        workshopHql = String.format(workshopHql, where);
        Query query = entityManager.createQuery(workshopHql, Workshop.class);
        setParameterList(query, paramList);
        List<Workshop> workshopList = query.getResultList();
        List<WorkshopEntity> entities = new ArrayList<>();
        for(Workshop workshop : workshopList){
            WorkshopEntity entity = new WorkshopEntity();
            CommonUtil.objectExchange(workshop,entity);
            entity.setFactoryCode(workshop.getFactory().getStdCode());
            entity.setFactorySname(workshop.getFactory().getSname());
            entities.add(entity);
        }
        return entities;
    }

    /**
     * 获取已经授权的装置的属性列表
     *
     * @param userId 用户ID
     * @return
     * <AUTHOR> 2017-12-20
     */
    private List<AuthorizeEntity> getAuthorizePropertyList(String userId) {

        String key  = "auth:unit:" + userId;

        String s = redisTemplate.opsForValue().get(key);

        if (StringUtils.isNotEmpty(s)){
            try {
                logger.info("使用缓存查询"+userId+"权限" );
                return JSON.parseArray(s, AuthorizeEntity.class);
            } catch (Exception e){
                logger.error("缓存数据转换异常" + e);
            }
        }


        List<AuthorizeEntity> resultList = new ArrayList();
        try {
            logger.error("当前用户艾迪："+userId);
            List<AAAPropertyValueEntity> authPropertyValueList = aaaService.getAuthPropertyValueList(userId);
            if (authPropertyValueList != null) {
                for (AAAPropertyValueEntity authPropertyValue : authPropertyValueList) {
                    AuthorizeEntity entity = new AuthorizeEntity();
                    entity.setSname(authPropertyValue.getName());
                    entity.setStdCode(authPropertyValue.getValue());
                    resultList.add(entity);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        String s1 = JSON.toJSONString(resultList);

        //存入redis
        redisTemplate.opsForValue().set(key, s1);

        //设置过期时间
        redisTemplate.expire(key, 10, TimeUnit.MINUTES);

        return resultList;
    }

    /**
     * 设置查询参数
     *
     * @param query
     * @param paramList 查询参数列表
     * <AUTHOR> 2017-11-12
     */
    private void setParameterList(Query query, Map<String, Object> paramList) {
        for (Map.Entry<String, Object> pair : paramList.entrySet()) {
            query.setParameter(pair.getKey(), pair.getValue());
        }
    }

    /**
     * 转换POJO到实体
     * @param unitList
     * @return  装置集合
     * @throws Exception
     */
    private List<UnitEntity> convertToEntity(List<Unit> unitList) throws Exception {
        List<UnitEntity> entities = new ArrayList<>();
        for(Unit unit : unitList){
            UnitEntity entity  = new UnitEntity();
            CommonUtil.objectExchange(unit,entity);
            entity.setWorkshopCode(unit.getWorkshop().getStdCode());
            entity.setWorkshopName(unit.getWorkshop().getSname());
            entities.add(entity);
        }
        return entities;
    }
}
