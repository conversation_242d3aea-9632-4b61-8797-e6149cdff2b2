package com.pcitc.opal.ap.bll;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ap.bll.entity.AlarmPushRuleEntity;
import com.pcitc.opal.ap.pojo.AlarmPushRule;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.AlarmPointGroupConfigEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointGroupEntity;
import com.pcitc.opal.pm.dao.imp.AlarmPointGroupConfig;
import org.springframework.stereotype.Service;

import java.util.Date;

/*
 * 报警知识管理业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmKnowlgManagmtService
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理业务逻辑层接口
 */
@Service
public interface AlarmPushRuleService {

    /**
     * 获取分页数据
     *
      * <AUTHOR> 2017-10-11
     * @param name 名称
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmPointEntity> 分页对象
     */
    PaginationBean<AlarmPushRuleEntity> getAlarmPushRule(String name,Integer companyId, Integer pushType,  Pagination page) throws  Exception;


    /**
     * 新增数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPushRuleEntity 报警点实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult addAlarmPushRule(AlarmPushRuleEntity alarmPushRuleEntity) throws Exception;


    /**
     * 删除报警点分组数据
     *
      * <AUTHOR> 2017-10-11
     * @param ids 报警点分组主键Id集合
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult deleteAlarmPushRule(Long[] ids) throws Exception;

    /**
     * 报警点分组更新数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPushRuleEntity 报警点分组实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult updateAlarmPushRule(AlarmPushRuleEntity alarmPushRuleEntity) throws Exception;
}
