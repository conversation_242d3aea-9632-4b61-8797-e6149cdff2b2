$(function () {
    var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";   //装置
    var unitPrdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';   //生产单元
    var initSendMsgUrl = OPAL.API.pmUrl + "/alarmMsgConfig/getInSendMsgList";     //是否发送报警短信
    var searchUrl = OPAL.API.pmUrl + "/alarmMsgConfig/getAlarmMsgConfig";   //查询
    var saveUrl = OPAL.API.pmUrl + '/alarmMsgConfig/updateAlarmMsgConfigForInUse';
    var isRefresh = false;
    window.pageLoadMode = PageLoadMode.None;
    var pageIndex = 1;
    var page = {
        /**
         * 初始化
         */
        init: function () {
            //绑定事件
            this.bindUI();
            //初始化查询是否发送报警短信
            page.logic.initSendMsg();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化表格
            page.logic.initTable();
            //默认查询数据
            page.logic.search();
        },
        /**
         * 绑定事件
         */
        bindUI: function () {
            //批量配置
            $('#batchConfig').click(function () {
                page.logic.config();
            })
            $('#inUse').click(function () {
                page.logic.notOrInUse(1);
            })
            $('#notUse').click(function () {
                page.logic.notOrInUse(0);
            })
            //查询
            $('#btnSearch').click(function () {
                page.logic.search();
            })
        },
        data: {
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化表格
             */
            initTable: function () {
                OPAL.ui.initBootstrapTable("table", {
                    cache: false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '110px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "装置",
                        field: 'unitSname',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }, {
                        title: "生产单元",
                        field: 'prdtCellSname',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }, {
                        title: "是否发送报警短信",
                        field: 'inSendMsgShow',
                        rowspan: 1,
                        width: '100px',
                        align: 'center'
                    }, {
                        title: "报警标识",
                        field: 'alarmflagName',
                        rowspan: 1,
                        width: '180px',
                        align: 'left'
                    }, {
                        title: "电话本",
                        field: 'mobileBook',
                        rowspan: 1,
                        width: '270px',
                        align: 'left'
                    }, {
                        title: "手机号",
                        field: 'mobilePhone',
                        rowspan: 1,
                        width: '270px',
                        align: 'left',
                    }]
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.edit(\'' + rowData.alarmPointId + '\')">编辑</a>'
                ]
            },
            /**
             * 批量配置
             */
            config: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections');
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmPointId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要配置的报警点！");
                    return;
                }
                var pageMode = PageModelEnum.Edit;
                var title = "报警短信配置";
                var alarmPointIds = idsArray.join(",");
                page.logic.detail(title, alarmPointIds, pageMode);

            },
            /**
             * 批量配置
             */
            notOrInUse: function (inSendMsg) {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections');
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmPointId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要配置的报警点！");
                    return;
                }
                var alarmPointIds = idsArray.join(",");
                var data={
                    "alarmPointIds":alarmPointIds,
                    "inSendMsg":inSendMsg
                }
                $.ajax({
                    url: saveUrl,
                    async: false,
                    type: "put",
                    data: data,
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！", {
                                time: 1000
                            }, function () {
                                page.logic.search();
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },

            /**
             * 编辑
             * @param prdtCellId
             */
            edit: function (alarmPointId) {
                var pageMode = PageModelEnum.Edit;
                var title = "报警短信配置";
                page.logic.detail(title, alarmPointId, pageMode);
            },
            /**
             * 编辑详细页面
             */
            detail: function (title, alarmPointIds, pageMode) {
                layer.open({
                    type: 2,
                    title: false,
                    closeBtn: 0,
                    area: ['920px', '560px'],
                    shadeClose: false,
                    content: 'AlarmMsgConfigDtl.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "alarmPointIds": alarmPointIds,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            pageIndex = page.data.pageNumber;
                            page.logic.search();
                            pageIndex = 1;
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 搜索
             */
            search: function () {
                page.data.param = OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": pageIndex
                });
            },

            /**
             * 初始化是否发送报警短信
             */
            initSendMsg: function () {
                OPAL.ui.getCombobox("inSendMsg", initSendMsgUrl, {
                    selectValue: 1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect("unitIds", commonUnitTreeUrl, "id", "parentId", "sname", {
                    data: {
                        'enablePrivilege': false
                    },
                    onChange: function (node) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds");
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }

                    }
                }, false, function () {

                });
            },
            /**
             * 生产单元
             */
            searchUnitPrdt: function (unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', unitPrdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            }
        }
    }
    page.init();
    window.page = page;
})