var delUrl = OPAL.API.acUrl + '/alarmChangePlanApply/deleteAlarmChangePlan';
var searchUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getAlarmChangePlan';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var getTecAlarmChangeBizApplyListUrl = OPAL.API.commUrl + "/getTecAlarmChangeBizApplyList"; 
var getAlarmChangeBizApplyListUrl = OPAL.API.commUrl + "/getAlarmChangeBizApplyList";
var getAlarmChangeBizIssueListUrl = OPAL.API.commUrl + "/getAlarmChangeBizIssueList";
var getAlarmChangeBizConfirmListUrl = OPAL.API.commUrl + "/getAlarmChangeBizConfirmList";
var getSystRunParaConfParaValueUrl = OPAL.API.commUrl + "/getSystRunParaConfParaValue";//调整方案审批方式
var currentTimeUrl = OPAL.API.commUrl + "/getSysDateTime";
window.pageLoadMode = PageLoadMode.None;
var changePlanBusinessType;
var aproType = 1; //调整方案审批类型 1本系统审批；2工艺系统审批
var isLoading = true;
$(function() {
    var page = {
        /**
         * 初始化
         */
        init: function() {
            // 获得调整方案业务类型(1，申请；2，下发；3，确认)
            changePlanBusinessType = page.logic.getQueryParam('ChangePlanBusinessType');
            if (changePlanBusinessType != 1 && changePlanBusinessType != 2 && changePlanBusinessType != 3) {
                changePlanBusinessType = 1
            }
            //调整方案审批方式
            page.logic.getAlarmChangePlanAproType();
            // 更改页面标题
            page.logic.changeTitle();
            //绑定事件
            this.bindUI();
            // 初始化 时间设置
            this.logic.initTime();
            //初始化表格
            page.logic.initTable();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化查询状态下拉
            page.logic.initStatusList(changePlanBusinessType,aproType);

            //装置赋值
            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("AlarmChangePlanApply");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }

            //默认查询数据
            setTimeout(function () {
                if ($("#status").val()!=null) {
                    page.logic.search();
                }
            }, 500);
        },
        /**
         * 绑定事件
         */
        bindUI: function() {
            //表格自适应页面拉伸宽度
            $(window).resize(function() {
                $('#table').bootstrapTable('resetView');
            });
            // 新增
            $('#changePlanAdd').click(function() {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            })
            //批量删除
            $('#changePlanDel').click(function() {
                page.logic.delAll();
            })
            //查询
            $('#btnSearch').click(function() {
                isLoading=false;
                page.logic.search();
            })
        },
        data: {
            // 设置查询参数
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            getAlarmChangePlanAproType:function() {
                $.ajax({
                    url:getSystRunParaConfParaValueUrl,
                    async:false,
                    data:{
                        code:"AlarmChangePlanAproType"
                    },
                    success:function(result) {
                        aproType = result;
                    }
                })
            },
            /**
             * 初始化表格
             */
            initTable: function() {
                var columns = [{
                    field: 'state',
                    checkbox: true,
                    align: 'center',
                    width: '40px',
                    visible: true
                }, {
                    title: "操作",
                    field: 'event_cancel',
                    align: 'center',
                    width: '90px',
                    formatter: page.logic.onActionRenderer
                }, {
                    title: "序号",
                    formatter: function(value, row, index) {
                        return index + 1;
                    },
                    align: 'center',
                    width: '50px'
                }, {
                    title: "处理状态",
                    field: 'showStatusName',
                    align: 'center',
                    width: '80px',
                    formatter: function(value, row, index) {
                        var className;
                        // 申请状态时 0（未提交）、1（已驳回）、2（已提交）、3和4（已提交）、5（已完成）
                        if (changePlanBusinessType == 1) {
                            switch (row.status) {
                                case AlarmChangePlanStatusEnum.UnSubmit: //0
                                    className = "wf-status-unsubmit";
                                    break;
                                case AlarmChangePlanStatusEnum.Reject: //1
                                    className = "wf-status-reject";
                                    break;
                                case AlarmChangePlanStatusEnum.Submitted: //2
                                    className = "wf-status-submitted"; //已提交
                                    break;
                                case AlarmChangePlanStatusEnum.Audited: //3
                                    className = "wf-status-submitted";
                                    break;
                                case AlarmChangePlanStatusEnum.Issued: //4
                                    className = "wf-status-submitted";
                                    break;
                                case AlarmChangePlanStatusEnum.Finished: //5
                                    className = "wf-status-finished";
                                    break;
                            }
                        } else if (changePlanBusinessType == 2) { // 下发状态时 3（待下发）、4和5（已下发）
                            switch (row.status) {
                                case AlarmChangePlanStatusEnum.Audited: //3
                                    className = "wf-status-audited"; //待下发状态=已审核状态
                                    break;
                                case AlarmChangePlanStatusEnum.Issued: //4
                                    className = "wf-status-issued";
                                    break;
                                case AlarmChangePlanStatusEnum.Finished: //5
                                    className = "wf-status-issued";
                                    break;
                            }
                        } else if (changePlanBusinessType == 3) { // 确认状态时4（待确认），5（已完成）
                            switch (row.status) {
                                case AlarmChangePlanStatusEnum.Issued: //4
                                    className = "wf-status-issued"; //待确认状态=已下发状态
                                    break;
                                case AlarmChangePlanStatusEnum.Finished: //5
                                    className = "wf-status-finished";
                                    break;
                            }
                        }
                        return ["<span class='wf-status " + className + "'>" + value + "</span>"]
                    },
                }, {
                    title: "申请单编号",
                    field: 'planCode',
                    align: 'center',
                    width: '80px'
                }, {
                    title: "车间",
                    field: 'workshopName',
                    align: 'left',
                    width: '60px'
                }, {
                    title: "装置",
                    field: 'unitName',
                    align: 'left',
                    width: '60px'
                }, {
                    title: "申请时间",
                    field: 'applyTime',
                    align: 'center',
                    width: '120px'
                }, {
                    title: '提交时间',
                    field: 'submitTime',
                    align: 'center',
                    width: '120px'
                }, {
                    title: "提交人",
                    field: 'submitUserName',
                    align: 'center',
                    width: '60px'
                }];
                if (changePlanBusinessType != null && changePlanBusinessType != '' && changePlanBusinessType != undefined) {
                    if (changePlanBusinessType == 1) { // 申请
                        columns[0].visible = true;
                    } else if (changePlanBusinessType == 2) { // 下发
                        columns[0].visible = false;
                    } else if (changePlanBusinessType == 3) { // 确认
                        columns[0].visible = false;
                    }
                }
                OPAL.ui.initBootstrapTable("table", {
                    cache: false,
                    striped: true,
                    columns: columns,
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function() {
                var rowData = arguments[1];
                if (changePlanBusinessType == 1 || aproType == 2) { // 申请
                    if (rowData.showStatusName == "已提交" || rowData.showStatusName == "已完成") {
                        return [
                            '<span style="color: #999">编辑</span> &nbsp;&nbsp;&nbsp;' +
                            '<a name="TableDelete" href="javascript:window.page.logic.delSingle(\'' + rowData.planId + '\',\'' + rowData.status + '\')" >删除</a> &nbsp;&nbsp;&nbsp;' +
                            '<a name="DeTails" href="javascript:window.page.logic.seeDetail(\'' + rowData.planId + '\')">详情</a> '
                        ]
                    } else {
                        return [
                            '<a name="TableEditor" href="javascript:window.page.logic.edit(\'' + rowData.planId + '\')">编辑</a> &nbsp;&nbsp;&nbsp;' +
                            '<a name="TableDelete" href="javascript:window.page.logic.delSingle(\'' + rowData.planId + '\',\'' + rowData.status + '\')" >删除</a> &nbsp;&nbsp;&nbsp;' +
                            '<a name="DeTails" href="javascript:window.page.logic.seeDetail(\'' + rowData.planId + '\')">详情</a> '
                        ]
                    }
                } else if (changePlanBusinessType == 2) { // 下发
                    if (rowData.status == 4 || rowData.status == 5) {
                        return [
                            '<span style="color: #999">下发</span> &nbsp;&nbsp;&nbsp;' +
                            '<a name="DeTails" href="javascript:window.page.logic.seeDetail(\'' + rowData.planId + '\')">详情</a> '
                        ]
                    } else {
                        return [
                            '<a name="DeTails" href="javascript:window.page.logic.IssuedInfo(\'' + rowData.planId + '\')">下发</a> &nbsp;&nbsp;&nbsp;' +
                            '<a name="DeTails" href="javascript:window.page.logic.seeDetail(\'' + rowData.planId + '\')">详情</a> '
                        ]
                    }
                } else if (changePlanBusinessType == 3) { // 确认
                    if (rowData.status == 5) {
                        return [
                            '<span style="color: #999">确认</span> &nbsp;&nbsp;&nbsp;' +
                            '<a name="DeTails" href="javascript:window.page.logic.seeDetail(\'' + rowData.planId + '\')">详情</a> '
                        ]
                    } else {
                        return [
                            '<a name="DeTails" href="javascript:window.page.logic.confirmInfo(\'' + rowData.planId + '\')">确认</a> &nbsp;&nbsp;&nbsp;' +
                            '<a name="DeTails" href="javascript:window.page.logic.seeDetail(\'' + rowData.planId + '\')">详情</a> '
                        ]
                    }

                }
            },
            /**
             * 下发
             */
            IssuedInfo: function(planId) {
                var pageMode = 1;
                var title = '下发信息';
                page.logic.linkInfo(title, planId, pageMode);
            },
            /**
             * 确认
             */
            confirmInfo: function(planId) {
                var pageMode = 2;
                var title = '确认信息';
                page.logic.linkInfo(title, planId, pageMode);
            },
            /**
             * 流程信息
             */
            linkInfo: function(title, planId, pageMode) {
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: false,
                    area: ['65%', '100%'],
                    // shadeClose: false,
                    content: 'AlarmChangePlanLinkInfo.html?' + Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "planId": planId,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function() {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 新增
             */
            add: function() {
                var pageMode = PageModelEnum.NewAdd;
                var title = aproType == 1 ? "新增调整方案" : "新增工艺参数调整方案";
                page.logic.detail(title, "", pageMode);
            },
            /**
             * 编辑
             * @param factoryId
             */
            edit: function(planId) {
                var pageMode = PageModelEnum.Edit;
                var title = aproType == 1 ? "编辑调整方案" : "编辑工艺参数调整方案";
                page.logic.detail(title, planId, pageMode);
            },
            /**
             *  详情
             */
            seeDetail: function(planId) {
                var pageMode = PageModelEnum.View;
                var title = "调整方案详情";
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: false,
                    area: ['65%', '99%'],
                    shadeClose: false,
                    content: 'AlarmChangePlanDtl.html?' + Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "planId": planId,
                            'title': title,
                            'aproType': aproType
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function() {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 弹窗
             */
            detail: function(title, planId, pageMode) {
                var url = aproType == 1 ? "AlarmChangePlanOp.html?" : "CraftParaChangeOp.html?";
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: false,
                    area: ['95%', '100%'],
                    // shadeClose: false,
                    content: url + Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "planId": planId,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function() {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 批量删除
             */
            delAll: function() {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections')
                $.each(rowsArray, function(i, el) {
                    idsArray.push(el.planId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    var statusFlag;
                    $.each(rowsArray, function(i, el) {
                        if (el.status != 0 && el.status != 1) {
                            statusFlag = true
                        }
                    })
                    if (statusFlag) {
                        layer.confirm('只能删除未提交或者已驳回的数据！', {
                            btn: ['确定']
                        })
                    } else {
                        $.ajax({
                            url: delUrl,
                            async: false,
                            data: {
                                data:idsArray,
                                aproType:aproType
                            },
                            dataType: "text",
                            // contentType: "application/json;charset=utf-8",
                            type: 'POST', //PUT DELETE POST
                            success: function(result) {
                                if (result.indexOf('collection') < 0) {
                                    layer.msg(result, {
                                        time: 1000
                                    }, function() {
                                        $('#table').bootstrapTable('selectPage', 1);
                                    });
                                } else {
                                    result = JSON.parse(result)
                                    layer.msg(result.collection.error.message)
                                }
                            },
                            error: function(result) {
                                var errorResult = $.parseJSON(result.responseText);
                                layer.msg(errorResult.collection.error.message);
                            }
                        })
                    }
                }, function(index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function(id, statusId) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    if (statusId != 0 && statusId != 1) {
                        layer.confirm('只能删除未提交或者已驳回的数据！', {
                            btn: ['确定']
                        })
                    } else {
                        $.ajax({
                            url: delUrl,
                            async: false,
                            data: {
                                data:data,
                                aproType:aproType
                            },
                            dataType: "text",
                            // contentType: "application/json;charset=utf-8",
                            type: 'POST',
                            success: function(result) {
                                if (result.indexOf('collection') < 0) {
                                    layer.msg(result, {
                                        time: 1000
                                    }, function() {
                                        $('#table').bootstrapTable('selectPage', 1);
                                    });
                                } else {
                                    result = JSON.parse(result)
                                    layer.msg(result.collection.error.message)
                                }
                            },
                            error: function(result) {
                                var errorResult = $.parseJSON(result.responseText);
                                layer.msg(errorResult.collection.error.message);
                            }
                        })
                    }
                }, function(index) {
                    layer.close(index)
                });
            },

            /**
             * 搜索
             */
            search: function() {
                if (!page.logic.checkDateIsValid()) {
                    return false;
                }
                page.data.param = OPAL.form.getData("searchForm");
                page.data.param.businessType = changePlanBusinessType;
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {

                }, false);
            },
            /**
             * 初始化查询inUse
             */
            initStatusList: function(changePlanBusinessType,aproType) {
                if(aproType == 1) {
                    if (changePlanBusinessType == 1) { // 申请
                        var url = getAlarmChangeBizApplyListUrl;
                        var selectValue = -1;
                    } else if (changePlanBusinessType == 2) { // 下发
                        var url = getAlarmChangeBizIssueListUrl;
                        var selectValue = 0;
                    } else if (changePlanBusinessType == 3) { // 确认
                        var url = getAlarmChangeBizConfirmListUrl;
                        var selectValue = 0;
                    }
                }
                if(aproType == 2) {
                    var url = getTecAlarmChangeBizApplyListUrl;
                    var selectValue = -1;
                } 
                OPAL.ui.getCombobox("status", url, {
                    selectValue: selectValue,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            /**
             * 初始化 时间
             */
            initTime: function() {
                var now;
                $.ajax({
                    url:currentTimeUrl,
                    async:false,
                    success:function(data){
                        var dataArr = $.ET.toObjectArr(data);
                        now = dataArr[0].value;
                    }
                })
                //
                OPAL.ui.initDateTimePeriodPicker({
                    type: 'date',
                    format: 'yyyy-MM-dd',
                    startValue: now.substring(0,8)+"01",
                    endValue: now.substring(0,10)
                })
            },
            checkDateIsValid: function() {
                if ($('#startTime').val() != '' && $('#endTime').val() != '') {
                    var startDate = OPAL.util.strToDate($('#startTime').val());
                    var endDate = OPAL.util.strToDate($('#endTime').val());
                    if ((endDate - startDate) < 0) {
                        layer.msg("开始时间须小于等于结束时间！");
                        return false;
                    }
                }
                if(($('#startTime').val() != '' && $('#endTime').val() == '') ||
                    ($('#startTime').val() == '' && $('#endTime').val() != '')) {
                    layer.msg("请选择时间！");
                    return false;
                }
                return true;
            },
            /**
             * 获取页面URL参数
             * @param  {name} 
             */
            getQueryParam: function(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
                var r = window.location.search.substr(1).match(reg); //匹配目标参数
                if (r != null) return unescape(r[2]);
                return null; //返回参数值
            },
            /**
             * 改变页面标题
             */
            changeTitle: function() {
                if (changePlanBusinessType != null && changePlanBusinessType != '' && changePlanBusinessType != undefined) {
                    if (changePlanBusinessType == 1) { // 申请
                        $('#pageTitle').text('调整方案申请');
                        document.title = '调整方案申请';
                        $('#changePlanAdd').css('display', 'inline-block');
                        $('#changePlanDel').css('display', 'inline-block');
                    } else if (changePlanBusinessType == 2) { // 下发
                        $('#pageTitle').text('调整方案下发');
                        document.title = '调整方案下发';
                        $('#changePlanAdd').css('display', 'none');
                        $('#changePlanDel').css('display', 'none');
                    } else if (changePlanBusinessType == 3) { // 确认
                        $('#pageTitle').text('调整方案确认');
                        document.title = '调整方案确认';
                        $('#changePlanAdd').css('display', 'none');
                        $('#changePlanDel').css('display', 'none');
                    }
                }
            }
        }
    }
    page.init();
    window.page = page;

})