var searchUrl = OPAL.API.adUrl + '/AlarmEventDelApprove/getAlarmEventDelApprove';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var delUrl = OPAL.API.adUrl + '/AlarmEventDelApprove/remove';//删除
var passUrl = OPAL.API.adUrl + '/AlarmEventDelApprove/approve'; //审批通过
var rejectUrl = OPAL.API.adUrl + '/AlarmEventDelApprove/reject'; //驳回
var getQueryTimeUrl = OPAL.API.commUrl + '/getQueryTime';
var monitorTypeUrl = OPAL.API.commUrl + "/getMonitorTypeList"; //专业
var alarmFlagListUrl = OPAL.API.adUrl + '/alarmEvents/getAlarmFlagList';
var alarmPriorityListUrl = OPAL.API.afUrl + "/alarmDurationStatt/getAlarmPriorityList";
var getQueryStartAndEndDateUrl = OPAL.API.commUrl + '/getShowTime';


var queryTimeArray;
var setStartTime, setNowTime;
var isLoading = true;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {

            this.bindUi();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化表格
            page.logic.initTable();
            //初始化 专业
            page.logic.initMonitorType();
            //扩展日期插件
            OPAL.util.extendDate();
            // 初始化 报警时间的时间点
            page.logic.getQueryTime();
            //初始化优先级
            page.logic.initAlarmPriorityList();
            //初始化 专业
            page.logic.initMonitorType();

            //默认查询数据
            setTimeout(function () {
                if ($("#unitCode").val() != null && $("#delStatus").val() != null) {
                    page.logic.search();
                }
            }, 500);
        },
        bindUi: function () {
            //查询
            $('#search').click(function () {
                isLoading = false;
                page.logic.search();
            })
            //批量删除
            $('#AlarmEventDel').click(function () {
                page.logic.delAll();
            });
            //批量通过
            $('#AlarmEventPass').click(function () {
                page.logic.passAll();
            });
            //批量驳回
            $('#AlarmEventReject').click(function () {
                page.logic.rejectAll();
            });
        },
        data: {
            //查询参数
            param: {}
        },
        logic: {
            /**
             * 获得固定的时间点
             */
            getQueryTime: function () {
                OPAL.util.getQueryTime(function (queryTime) {
                    queryTimeArray = queryTime.split(':');
                    // 初始化 开始时间和结束时间
                    page.logic.getQueryStartAndEndDate();
                    // 初始化 时间设置
                    page.logic.initTime();
                });
            },
            // 初始化 开始时间和结束时间
            getQueryStartAndEndDate: function () {
                $.ajax({
                    url: getQueryStartAndEndDateUrl,
                    async: false,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        setStartTime = dataArr[0].value;
                        setNowTime = dataArr[2].value;
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 设置日期插件
             */
            initTime: function () {
                OPAL.ui.initDateTimePeriodPicker({
                    format: 'yyyy-MM-dd HH:mm:ss',
                    type: 'datetime',
                });
            },
            /**
             * 初始化查询 报警标识
             */
            initAlarmFlagList: function () {
                OPAL.ui.getComboMultipleSelect('alarmFlagId', alarmFlagListUrl, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#alarmFlagId").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#alarmFlagId").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化查询 优先级
             */
            initAlarmPriorityList: function () {
                OPAL.ui.getComboMultipleSelect('priority', alarmPriorityListUrl, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#priority").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#priority").combotree("checkAllNodes");
                    //初始化报警标识
                    page.logic.initAlarmFlagList();
                });
            },
            /**
             * 初始化检测类型
             */
            initMonitorType: function () {
                OPAL.ui.getCombobox('monitorType', monitorTypeUrl, {
                    selectValue: 9,
                    async: false,
                    isAll: true
                }, null);
            },
            // 批量通过
            passAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmEventDelApproveId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要审批通过的数据！");
                    return;
                }
                let arr = rowsArray.filter(item => {
                    return item.delStatus != 1
                })
                if (arr.length > 0) {
                    layer.msg("只能审批状态为已提交的数据！");
                    return;
                }
                layer.confirm('确定审批通过吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: passUrl,
                        async: false,
                        data: {alarmEventDelApproveId: idsArray},
                        dataType: "text",
                        // contentType: "application/json;charset=utf-8",
                        type: 'POST', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("审批通过成功！", {
                                    time: 1000
                                }, function () {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            // 通过单条
            passSingle: function (id) {
                let arr = [id];
                layer.confirm('确定审批通过吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: passUrl,
                        async: false,
                        data: {alarmEventDelApproveId: arr},
                        dataType: "text",
                        type: 'POST', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("审批通过成功！", {
                                    time: 1000
                                }, function () {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            // 批量驳回
            rejectAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmEventDelApproveId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要驳回的数据！");
                    return;
                }
                let arr = rowsArray.filter(item => {
                    return item.delStatus != 1
                })
                if (arr.length > 0) {
                    layer.msg("只能驳回状态为已提交的数据！");
                    return;
                }
                layer.confirm('确定驳回吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: rejectUrl,
                        async: false,
                        data: {alarmEventDelApproveId: idsArray},
                        dataType: "text",
                        // contentType: "application/json;charset=utf-8",
                        type: 'POST', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("驳回成功！", {
                                    time: 1000
                                }, function () {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            // 驳回单条
            rejectSingle: function (id) {
                let arr = [id];
                layer.confirm('确定驳回吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: rejectUrl,
                        async: false,
                        data: {alarmEventDelApproveId: arr},
                        dataType: "text",
                        // contentType: "application/json;charset=utf-8",
                        type: 'POST', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("驳回成功！", {
                                    time: 1000
                                }, function () {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            debugger
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 批量删除
             */
            delAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmEventDelApproveId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据！");
                    return;
                }
                let arr = rowsArray.filter(item => {
                    return item.delStatus != 0 && item.delStatus != 3
                })
                if (arr.length > 0) {
                    layer.msg("只能删除状态为未提交和已驳回的数据！");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: {
                            alarmEventDelApproveId: idsArray
                        },
                        dataType: "text",
                        // contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function (id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false, //
                        data: {
                            alarmEventDelApproveId: data
                        },
                        dataType: "text",
                        // contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             *
             * 编辑
             */
            edit: function (id, reason) {
                layer.open({
                    type: 2,
                    title: '剔除原因编辑',
                    closeBtn: 1,
                    area: ['800px', '280px'],
                    shadeClose: false,
                    content: 'AlarmEventDelEdit.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "id": id,
                            "reason": reason
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                if (rowData.delStatus == 1) {
                    return [
                        '<a name="TableEditor" href="javascript:window.page.logic.passSingle(\'' + rowData.alarmEventDelApproveId + '\')">通过</a> &nbsp;' +
                        '<a name="TableDelete" href="javascript:window.page.logic.rejectSingle(\'' + rowData.alarmEventDelApproveId + '\')" >驳回</a>'
                    ]
                } else {
                    return [
                        '<a name="TableEditor" style="color: #999;">通过</a> &nbsp;' +
                        '<a name="TableEditor" style="color: #999;">驳回</a>'
                    ]
                }
            },
            //初始化表格
            initTable: function () {
                OPAL.ui.initBootstrapTable2("table", {
                    striped: true,
                    columns: [{
                        field: 'state', checkbox: true, rowspan: 1, align: 'center', width: '50px'
                    }, {
                        title: "序号", formatter: function (value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1) * data.pageSize;
                        }, rowspan: 1, align: 'center', width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '140px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "剔除状态", field: 'delStatusShow', rowspan: 1, align: 'center', width: '100px'
                    }, {
                        title: "装置", field: 'unitName', rowspan: 1, align: 'left', width: '120px'
                    }, {
                        title: "生产单元", field: 'prdtCellName', rowspan: 1, align: 'left', width: '120px'
                    }, {
                        title: "发生时间", field: 'startTime', rowspan: 1, align: 'center', width: '140px'
                    }, {
                        title: "参数名称", field: 'location', rowspan: 1, align: 'left', width: '200px'
                    }, {
                        title: "位号", field: 'alarmPointTag', rowspan: 1, align: 'left', width: '100px'
                    }, {
                        title: "报警等级", field: 'alarmFlagName', rowspan: 1, align: 'center', width: '100px'
                    }, {
                        title: "优先级", field: 'priorityName', rowspan: 1, align: 'center', width: '60px'
                    }, {
                        title: "剔除原因", field: 'reason', rowspan: 1, align: 'left', width: '150px'
                    }, {
                        title: "申请时间", field: 'crtDate', rowspan: 1, align: 'left', width: '150px'
                    }, {
                        title: "申请人", field: 'crtUserName', rowspan: 1, align: 'left', width: '150px'
                    }, {
                        title: "审批时间", field: 'aProTime', rowspan: 1, align: 'left', width: '150px'
                    }, {
                        title: "审批人", field: 'aProUserName', rowspan: 1, align: 'left', width: '150px'
                    }, {
                        title: "级别", field: 'craftRankName', rowspan: 1, align: 'center', width: '50px'
                    }, {
                        title: "报警值(限值)", field: 'limitValue', rowspan: 1, align: 'right', width: '100px'
                    }, {
                        title: "单位", field: 'measUnitName', rowspan: 1, align: 'left', width: '100px'
                    }, {
                        title: "高高报", field: 'alarmPointHH', rowspan: 1, align: 'center', width: '100px'
                    }, {
                        title: "高报", field: 'alarmPointHI', rowspan: 1, align: 'center', width: '100px'
                    }, {
                        title: "低报", field: 'alarmPointLO', rowspan: 1, align: 'center', width: '100px'
                    }, {
                        title: "低低报", field: 'alarmPointLL', rowspan: 1, align: 'center', width: '100px'
                    }]
                }, page.logic.queryParams, "search")
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitCode', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function (nodes) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues('unitCode');
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $("#workTeamIds").prop('disabled', false);
                            page.logic.initWorkTeam();
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('#workTeamIds').html("");
                            $("#workTeamIds").prop('disabled', true);
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    }
                }, false, function () {
                    $("#searchPrdt").combotree("checkAllNodes");
                });
            },
            /**
             * 搜索
             */
            search: function () {
                $("#search").attr('disabled', true);
                page.data.param = OPAL.form.getData("searchForm", true);
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 设置参数
             */
            setData: function () {
                page.data.param = OPAL.form.getData('searchForm');
            }
        }
    };
    page.init();
    window.page = page;
});