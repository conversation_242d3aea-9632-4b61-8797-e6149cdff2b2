package com.pcitc.opal.ad.dao.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

@Data
@AllArgsConstructor
public class AlarmRecAnlyVO {


    /**
     * 报警记录ID
     */
    private Long alarmRecId;

    /**
     * 报警分析记录ID
     */
    private Long alarmAnlyRecId;

    /**
     * 分析状态（0未分析; 1已分析；2已提交；3已确认）
     */
    private Integer anlyStatus;
    /**
     * 装置编码
     */
    private String unitCode;

    /**
     * 装置名称
     */
    private String unitName;

    /**
     * 车间
     */
    private String workshopName;
    /**
     * 工厂
     */
    private String factoryName;

    /**
     * 生产单元ID
     */
    private Long prdtCellId;

    /**
     * 生产单元名称
     */
    private String prdtCellName;

    /**
     * 位号
     */
    private String tag;
    /**
     * 位号描述
     */
    private String tagDes;

    /**
     * 计量单位
     */
    private String measUnitName;
    /**
     * 报警点高高报
     */
    private Double alarmPointHH;
    /**
     * 报警点高报
     */
    private Double alarmPointHI;
    /**
     * 报警点低报
     */
    private Double alarmPointLO;
    /**
     * 报警点低低报
     */
    private Double alarmPointLL;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     * 报警标识名称
     */
    private String alarmFlagName;

    /**
     * 报警时间
     */
    private Date alarmTime;

    /**
     * 恢复时间
     */
    private Date recoveryTime;

    /**
     * 报警优先级
     */
    private Integer priority;

    /**
     * 专业
     */
    private Integer monitorType;

    /**
     * 原因类型（1工艺类；2操作类；3设备类；4电气类；5公用工程类；6仪表类；7原料类；8其他）
     */
    private Long reasonType;

    /**
     * 报警原因ID
     */
    private Long alarmReasonId;

    /**
     *原因名称
     */
    private String name;

    /**
     *创建时间
     */
    private Date crtTime;
    /**
     *创建人名称
     */
    private String crtUserName;
    /**
     *提交时间
     */
    private Date submitTime;
    /**
     *提交人名称
     */
    private String submitUserName;

    /**
     *原因描述
     */
    private String reasonDes;

    private Date confirmTime;

    private String confirmUserName;



}

