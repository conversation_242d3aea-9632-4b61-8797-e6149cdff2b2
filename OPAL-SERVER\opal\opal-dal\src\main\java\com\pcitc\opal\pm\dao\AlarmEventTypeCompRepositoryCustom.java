package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.AlarmEventTypeComp;

import java.util.List;

/*
 * AlarmEventTypeComp实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmEventTypeCompRepositoryCustom
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：AlarmEventTypeComp实体的Repository的JPA自定义接口 
 */
public interface AlarmEventTypeCompRepositoryCustom {

	/**
	 * 校验数据
	 * 
	 * <AUTHOR>    2018-03-30
	 * @param alarmEventTypeCompEntity  报警事件类型对照实体
	 * @return 返回结果信息类
	 */
	CommonResult alarmEventTypeCompValidation(AlarmEventTypeComp alarmEventTypeCompEntity);

	/**
	 * 新增数据
	 * 
	 * <AUTHOR>   2018-03-30
	 * @param alarmEventTypeCompEntity 报警事件类型对照实体
	 * @return 返回结果信息类
	 */
	CommonResult addAlarmEventTypeComp(AlarmEventTypeComp alarmEventTypeCompEntity);

	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmEventTypeCompIds  报警事件类型对照ID集合
	 * @return 返回结果信息类
	 */
	CommonResult deleteAlarmEventTypeComp(Long[] alarmEventTypeCompIds);

	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmEventTypeCompEntity
	 * @return 返回结果信息类
	 */
	CommonResult updateAlarmEventTypeComp(AlarmEventTypeComp alarmEventTypeCompEntity);

	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmEventTypeCompId  报警事件类型对照ID
	 * @return 报警事件类型对照实体
	 */
	AlarmEventTypeComp getSingleAlarmEventTypeComp(Long alarmEventTypeCompId);

	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmEventTypeCompIds  报警事件类型对照ID集合
	 * @return 报警事件类型对照实体集合
	 */
	List<AlarmEventTypeComp> getAlarmEventTypeComp(Long[] alarmEventTypeCompIds);

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param dcsCodeId       DcsCodeID
	 * @param eventTypeSource 源事件类型
	 * @param eventNameSource 源事件名称
	 * @param eventTypeIds    事件类型ID集合
	 * @param inUse           是否启用
	 * @param page            分页信息
	 * @return 报警事件类型对照实体集合
	 */
	PaginationBean<AlarmEventTypeComp> getAlarmEventTypeComp(Long dcsCodeId, String eventTypeSource, String eventNameSource, Long[] eventTypeIds, Integer inUse, Pagination page);
}
