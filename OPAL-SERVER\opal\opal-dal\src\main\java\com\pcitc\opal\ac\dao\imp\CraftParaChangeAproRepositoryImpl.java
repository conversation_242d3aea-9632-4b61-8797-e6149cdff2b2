package com.pcitc.opal.ac.dao.imp;

import com.pcitc.opal.ac.dao.CraftParaChangeAproRepositoryCustom;
import com.pcitc.opal.ac.pojo.CraftParaChangeApro;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.dao.BaseRepository;

import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * CraftParaChangeApro实体的Repository的JPA接口实现
 * 模块编号： pcitc_opal_dal_class_CraftParaChangeAproRepositoryImpl
 * 作       者：zheng.yang
 * 创建时间：2019/4/15
 * 修改编号：1
 * 描       述：CraftParaChangeApro实体的Repository的JPA接口实现
 */
public class CraftParaChangeAproRepositoryImpl extends BaseRepository<CraftParaChangeApro,Long> implements CraftParaChangeAproRepositoryCustom {
    /**
     * 新增编辑工艺参数变更审批记录
     *
     * @param craftParaChangeAproList 报警变更事项数据
     * @return 返回结果信息类
     * <AUTHOR> 2019-04-15
     */
    @Override
    public CommonResult addCraftParaChangeApro(List<CraftParaChangeApro> craftParaChangeAproList) {
        CommonResult commonResult = new CommonResult();
        try {
            craftParaChangeAproList.forEach(x->{
                this.getEntityManager().merge(x);
            });
            commonResult.setResult(craftParaChangeAproList);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }
    /**
     * 删除工艺参数变更审批记录
     *
     * @param craftAproIds 工艺参数变更审批记录ID
     * @return 返回结果信息类
     * <AUTHOR> 2019-04-15
     */
    @Override
    public CommonResult deleteCraftParaChangeApro(Long[] craftAproIds) {
        return null;
    }
    /**
     * 删除“报警变更方案明细ID”对应的<工艺参数变更审批记录>
     *
     * @param planDetailId 报警变更明细Id
     * @return 返回结果信息类
     * <AUTHOR> 2019-04-15
     */
    @Override
    public CommonResult deleteCraftParaChangeAproByPDId(Long... planDetailId) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            StringBuilder hql = new StringBuilder(" from CraftParaChangeApro t where t.planDetailId in(:planDetailId)");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("planDetailId", Arrays.asList(planDetailId));

            TypedQuery<CraftParaChangeApro> query = getEntityManager().createQuery(hql.toString(), CraftParaChangeApro.class);
            this.setParameterList(query, paramList);
            List<CraftParaChangeApro> list = query.getResultList();
            list.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 删除出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }
    /**
     * 根据PlanDetailId获取工艺参数变更审批记录
     *
     * @param planDetailIds 报警变更方案明细ID
     * @return AlarmChangePlanDetail 报警变更事项集合
     * <AUTHOR> 2019-04-15
     */
    @Override
    public List<CraftParaChangeApro> getAlarmChangeItemByPlanDetail(Long... planDetailIds) {
        StringBuilder hql = new StringBuilder(" from CraftParaChangeApro t where t.planDetailId in(:planDetailId)");
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("planDetailId", Arrays.asList(planDetailIds));
        TypedQuery<CraftParaChangeApro> query = getEntityManager().createQuery(hql.toString(), CraftParaChangeApro.class);
        this.setParameterList(query, paramList);
        return query.getResultList();
    }
}
