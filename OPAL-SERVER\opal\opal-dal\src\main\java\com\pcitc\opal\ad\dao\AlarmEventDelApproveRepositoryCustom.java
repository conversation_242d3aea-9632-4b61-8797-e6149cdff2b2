package com.pcitc.opal.ad.dao;

import com.pcitc.opal.ad.pojo.AlarmEventDelApprove;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

import java.util.Date;

public interface AlarmEventDelApproveRepositoryCustom {
    PaginationBean<AlarmEventDelApprove> getAlarmEventDelApprove(String[] unitCode, Integer[] priority,
                                                                 Long[] alarmFlagId, Integer monitorType,
                                                                 Date startTime, Date endTime,String alarmPointTag, Integer delStatus,
                                                                 Pagination page);

    boolean existsAlarmEvent(Long[] eventIds, Integer[] delStatus);
}
