package com.pcitc.opal.ap.dao;

import com.pcitc.opal.ap.dao.imp.AlarmPushRuleDetailEntityVO;
import com.pcitc.opal.ap.pojo.AlarmPushRule;
import com.pcitc.opal.ap.pojo.AlarmPushRuleDetail;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

import java.util.List;

/*
 * 报警知识管理实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_AlarmKnowlgManagmtRepositoryCustom
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理实体的Repository的JPA自定义接口
 */
public interface AlarmPushRuleDetailRepositoryCustom {

    List<AlarmPushRuleDetailEntityVO> getAlarmPushRuleDetails(Long alarmPushRuleId);

    CommonResult addAlarmPushRuleDetail(AlarmPushRuleDetail alarmPushRuleDetail);

    CommonResult deleteAlarmPushRuleDetail(List<Long> ids);

    CommonResult updateAlarmPushRuleDetail(AlarmPushRuleDetail alarmPushRuleDetail);

}
