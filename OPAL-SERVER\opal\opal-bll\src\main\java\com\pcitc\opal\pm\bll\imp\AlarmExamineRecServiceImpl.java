package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.ad.dao.AlarmRecRepository;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.AlarmExamineRecService;
import com.pcitc.opal.pm.bll.entity.AlarmExamineRecEntity;
import com.pcitc.opal.pm.dao.AlarmExamineRecRepository;
import com.pcitc.opal.pm.pojo.AlarmExamineRec;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.rmi.RemoteException;
import java.text.DecimalFormat;
import java.util.*;

/**
 * @USER: chenbo
 * @DATE: 2023/4/14
 * @DESC: 报警审查记录
 **/
@Slf4j
@Service
public class AlarmExamineRecServiceImpl implements AlarmExamineRecService {

    @Resource
    private BasicDataService basicDataService;

    @Resource
    private AlarmRecRepository alarmRecRepository;

    @Resource
    private AlarmExamineRecRepository alarmExamineRecRepository;


    @Override
    public PaginationBean<AlarmExamineRecEntity> getAlarmExamineRec(String[] unitIds, Long[] prdtCellIds, String tag, Integer alarmFlagId, Integer priority, Integer examineStatus, Date startTime, Date endTime, Integer alarmDuration, Pagination page) {

        List<UnitEntity> unitList = null;

        //按照权限获取装置信息
        try {
            if (ArrayUtils.isEmpty(unitIds)) {

                unitList = basicDataService.getUnitList(true);
            } else {
                unitList = basicDataService.getUnitListByIds(unitIds, true);
            }
            unitIds = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
        } catch (Exception e) {
            throw new RuntimeException("装置获取失败" + e.getMessage());
        }

        PaginationBean<AlarmExamineRecEntity> returnDate = null;

        ArrayList<AlarmExamineRecEntity> alarmExamineRecEntities = new ArrayList<>();

        //报警时长格式化
        DecimalFormat format = new DecimalFormat("#.#");
        //不四舍五入，直接截取
        format.setRoundingMode(RoundingMode.FLOOR);

        //变更状态为-1查询记录表
        if (examineStatus == -1) {
            PaginationBean<AlarmRec> paginationBean = alarmRecRepository.getAlarmExamineRec(unitIds, prdtCellIds, tag, alarmFlagId, priority, startTime, endTime, alarmDuration, page);

            returnDate = new PaginationBean<>(page, paginationBean.getTotal());

            for (AlarmRec alarmRec : paginationBean.getPageList()) {

                if (alarmRec.getRecoveryTime() == null){
                    alarmRec.setRecoveryTime(endTime);
                }

                AlarmExamineRecEntity a = new AlarmExamineRecEntity();
                a.setAlarmRecId(alarmRec.getAlarmRecId());
                a.setExamineStatus(examineStatus);
                a.setAlarmTime(alarmRec.getAlarmTime());
                a.setOldRecoveryTime(alarmRec.getRecoveryTime());
                a.setUnitName(unitList.stream().filter(x -> x.getStdCode().equals(alarmRec.getUnitCode())).map(UnitEntity::getSname).findFirst().orElse(null));
                a.setPrdName(alarmRec.getPrdtCell().getName());
                a.setTag(alarmRec.getTag());
                a.setDes(alarmRec.getDes());
                a.setAlarmFlagId(alarmRec.getAlarmFlagId() == null ? null : alarmRec.getAlarmFlagId().intValue());
                a.setFlagName(alarmRec.getAlarmFlag().getName());
                a.setPriority(alarmRec.getPriority());
                a.setAlarmDuration((alarmRec.getRecoveryTime().getTime() - alarmRec.getAlarmTime().getTime()) / (60.0 * 60.0 * 1000.0));
                a.setAlarmDuration(Double.valueOf(format.format(a.getAlarmDuration())));
                a.setLocation(alarmRec.getAlarmPoint().getLocation());
                alarmExamineRecEntities.add(a);
            }
        } else {
            PaginationBean<AlarmExamineRec> paginationBean = alarmExamineRecRepository.getAlarmExamineRec(unitIds, prdtCellIds, tag, alarmFlagId, priority, startTime, endTime, alarmDuration, examineStatus, page);

            returnDate = new PaginationBean<>(page, paginationBean.getTotal());

            for (AlarmExamineRec alarmExamineRec : paginationBean.getPageList()) {
                AlarmExamineRecEntity a = new AlarmExamineRecEntity();
                a.setAlarmExamineRecId(alarmExamineRec.getAlarmExamineRecId());
                a.setAlarmRecId(alarmExamineRec.getAlarmRecId());
                a.setExamineStatus(examineStatus);
                a.setAlarmTime(alarmExamineRec.getAlarmRec().getAlarmTime());
                a.setUnitName(unitList.stream().filter(x -> x.getStdCode().equals(alarmExamineRec.getAlarmRec().getUnitCode())).map(UnitEntity::getSname).findFirst().orElse(null));
                a.setPrdName(alarmExamineRec.getAlarmRec().getPrdtCell().getName());
                a.setTag(alarmExamineRec.getAlarmRec().getTag());
                a.setDes(alarmExamineRec.getAlarmRec().getDes());
                a.setAlarmFlagId(alarmExamineRec.getAlarmRec().getAlarmFlagId().intValue());
                a.setFlagName(alarmExamineRec.getAlarmRec().getAlarmFlag().getName());
                a.setPriority(alarmExamineRec.getAlarmRec().getPriority());
                if (examineStatus.equals(CommonEnum.ExamineStatus.Passed.getStatus())){
                    //已通过->时长用新恢复时间-报警时间
                    if (alarmExamineRec.getRecoveryTime() != null){
                        a.setAlarmDuration((alarmExamineRec.getRecoveryTime().getTime() - alarmExamineRec.getAlarmRec().getAlarmTime().getTime()) / (60.0 * 60.0 * 1000.0));
                    }
                } else {
                    //否则->时长用旧恢复时间-报警时间，如果为空赋值为查询结束时间
                    if (alarmExamineRec.getAlarmRec().getRecoveryTime() == null){
                        alarmExamineRec.getAlarmRec().setRecoveryTime(endTime);
                    }
                    a.setAlarmDuration((alarmExamineRec.getAlarmRec().getRecoveryTime().getTime() - alarmExamineRec.getAlarmRec().getAlarmTime().getTime()) / (60.0 * 60.0 * 1000.0));

                }
                a.setOldRecoveryTime(alarmExamineRec.getOldRecoveryTime() == null ? endTime : alarmExamineRec.getOldRecoveryTime());
                a.setReasonAnly(alarmExamineRec.getReasonAnly());
                a.setSubmitTime(alarmExamineRec.getSubmitTime());
                a.setSubmitUserName(alarmExamineRec.getSubmitUserName());
                a.setAproTime(alarmExamineRec.getAproTime());
                a.setAproUserName(alarmExamineRec.getAproUserName());
                a.setRecoveryTime(alarmExamineRec.getRecoveryTime());
                a.setUplAttaId(alarmExamineRec.getUplAttaId());
                a.setUplAttaName(alarmExamineRec.getUplAttaName());
                a.setAlarmDuration(Double.valueOf(format.format(a.getAlarmDuration())));
                a.setLocation(alarmExamineRec.getAlarmRec().getAlarmPoint().getLocation());
                alarmExamineRecEntities.add(a);
            }

        }

        returnDate.setPageList(alarmExamineRecEntities);
        return returnDate;
    }

    @Override
    public CommonResult saveAlarmExamineRec(Long alarmExamineRecId, Date recoveryTime, MultipartFile file, Long alarmRecId, String reasonAnly, Integer status, String name) {
        //校验状态
        if (!Arrays.asList(CommonEnum.ExamineStatus.NotSubmitted.getStatus(), CommonEnum.ExamineStatus.Submitted.getStatus()).contains(status)) {
            throw new RuntimeException("状态非法");
        }


        CMISClient cmisClient = new CMISClient();
        AlarmExamineRec alarmExamineRec = null;
        if (alarmExamineRecId == null) {
            alarmExamineRec = new AlarmExamineRec();
        } else {
            alarmExamineRec = alarmExamineRecRepository.findById(alarmExamineRecId).get();
        }

        AlarmRec alarmRec = alarmRecRepository.findById(alarmRecId).get();


        if (recoveryTime.getTime() < alarmRec.getAlarmTime().getTime()){
            throw new RuntimeException("变更后恢复时间必须大于等于报警时间");
        }

        //上传文件
        try {
            if (file != null && !file.isEmpty()) {
                String uplAttaId = alarmExamineRec.getUplAttaId();
                //如果已经存在文件，先删除
                if (StringUtils.isNotEmpty(uplAttaId)) {
                    log.info("删除文件{}-结果{}", uplAttaId, cmisClient.deleteFile(uplAttaId));
                }

                String s = cmisClient.uploadFile("", name, file.getInputStream());
                alarmExamineRec.setUplAttaId(s);
                alarmExamineRec.setUplAttaName(name);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("文件上传失败" + e.getMessage());
        }



        alarmExamineRec.setAlarmExamineRecId(alarmExamineRecId);
        alarmExamineRec.setOldRecoveryTime(alarmRec.getRecoveryTime());
        alarmExamineRec.setRecoveryTime(recoveryTime);
        alarmExamineRec.setAlarmRecId(alarmRecId);
        alarmExamineRec.setOldUpdateTime(alarmRec.getUpdateTime());
        //设置状态为未提交
        alarmExamineRec.setExamineStatus(status);
        alarmExamineRec.setReasonAnly(reasonAnly);

        CommonProperty commonProperty = new CommonProperty();
        String userName = null;
        try {
            userName = commonProperty.getUserName();
        } catch (RemoteException e) {
            log.error("saveAlarmExamineRec获取用户名失败" + e.getMessage());
        }

        if (status.equals(CommonEnum.ExamineStatus.Submitted.getStatus())) {
            alarmExamineRec.setSubmitTime(new Date());
            alarmExamineRec.setSubmitUserName(userName);
            alarmExamineRec.setSubmitUserId(commonProperty.getUserId());
        }


        CommonResult commonResult = new CommonResult();
        try {
            AlarmExamineRec save = alarmExamineRecRepository.save(alarmExamineRec);
            commonResult.setMessage("变更成功");
            commonResult.setIsSuccess(true);
        } catch (Exception e) {
            e.printStackTrace();
            commonResult.setMessage("变更失败" + e.getMessage());
            commonResult.setIsSuccess(false);
            commonResult.setResult(e.getMessage());
        }


        return commonResult;
    }

    @Override
    public CommonResult approvalAlarmExamineRec(Long alarmExamineRecId, Integer status) {
        //校验状态
        if (!Arrays.asList(CommonEnum.ExamineStatus.Passed.getStatus(), CommonEnum.ExamineStatus.Rejected.getStatus()).contains(status)) {
            throw new RuntimeException("状态非法");
        }

        //获取用户信息
        CommonProperty commonProperty = new CommonProperty();
        String userName = null;
        try {
            userName = commonProperty.getUserName();
        } catch (RemoteException e) {
            log.error("saveAlarmExamineRec获取用户名失败" + e.getMessage());
        }

        //查询当前数据
        AlarmExamineRec alarmExamineRec = alarmExamineRecRepository.findById(alarmExamineRecId).get();




        //如果是已通过，更新rec表对应数据
        if (Objects.equals(status, CommonEnum.ExamineStatus.Passed.getStatus())) {
            AlarmRec alarmRec = alarmExamineRec.getAlarmRec();
            alarmRec.setRecoveryTime(alarmExamineRec.getRecoveryTime());
            alarmRec.setUpdateTime(new Date());
            alarmRecRepository.saveAndFlush(alarmRec);
        }

        alarmExamineRec.setAproTime(new Date());
        alarmExamineRec.setAproUserName(userName);
        alarmExamineRec.setAproUserId(commonProperty.getUserId());

        alarmExamineRec.setExamineStatus(status);

        CommonResult commonResult = new CommonResult();

        try {
            alarmExamineRecRepository.saveAndFlush(alarmExamineRec);
            commonResult.setMessage(CommonEnum.ExamineStatus.getName(status));
        } catch (Exception e) {
            log.error("审批失败" + e.getMessage());
            commonResult.setMessage("操作失败" + e.getMessage());
        }

        return commonResult;
    }
}
