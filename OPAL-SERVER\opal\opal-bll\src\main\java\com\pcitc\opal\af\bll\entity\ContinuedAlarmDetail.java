package com.pcitc.opal.af.bll.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @USER: chenbo
 * @DATE: 2022/11/29
 * @TIME: 14:31
 * @DESC:
 **/
@Data
@EqualsAndHashCode
@ToString
@Accessors(chain=true)
public class ContinuedAlarmDetail {
    /**
     * 报警时间
     */
    private String alarmTime;

    /**
     * 时长
     */
    private String timeLen;

    /**
     * 恢复时间
     */
    private String recoveryTime;

    /**
     * 响应时间
     */
    private String responseTime;
}
