package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.common.*;
import com.pcitc.opal.pm.bll.AlarmEventTypeCompService;
import com.pcitc.opal.pm.bll.entity.AlarmEventTypeCompEntity;
import com.pcitc.opal.pm.dao.AlarmEventTypeCompRepository;
import com.pcitc.opal.pm.pojo.AlarmEventTypeComp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.List;

/*
 * 事件类型对照配置业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmEventTypeCompServiceImpl
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：事件类型对照配置业务逻辑层实现类
 */
@Service
@Component
public class AlarmEventTypeCompServiceImpl implements AlarmEventTypeCompService {

    /**
     * 实例化数据访问层接口
     */
    @Autowired
    private AlarmEventTypeCompRepository repo;

    /**
     * 新增事件类型对照配置
     *
     * @param alarmEventTypeCompEntity 事件类型对照配置实体
     * @throws Exception
     * <AUTHOR>   2018-03-30
     */
    @Override
    public void addAlarmEventTypeComp(AlarmEventTypeCompEntity alarmEventTypeCompEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmEventTypeComp alarmEventTypeCompPO = ObjectConverter.entityConverter(alarmEventTypeCompEntity, AlarmEventTypeComp.class);
        // 数据校验
        alarmEventTypeCompValidation(alarmEventTypeCompPO);
        // 赋值  创建人、创建名称、创建时间
        CommonUtil.returnValue(alarmEventTypeCompPO, CommonEnum.PageModelEnum.NewAdd.getIndex());
        CommonResult commonResult = repo.addAlarmEventTypeComp(alarmEventTypeCompPO);

        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
    }

    /**
     * 删除事件类型对照配置
     *
     * @param alarmEventTypeCompIds 事件类型对照配置ID集合
     * @throws Exception
     * <AUTHOR> 2018-03-30
     */
    @Override
    public void deleteAlarmEventTypeComp(Long[] alarmEventTypeCompIds) throws Exception {
        // 判断ID集合是否可用
        if (alarmEventTypeCompIds == null || alarmEventTypeCompIds.length <= 0) {
            throw new Exception("没有需要删除的事件类型对照配置数据~！");
        }
        List<AlarmEventTypeComp> anlyAlarmEventTypeCompList = repo.getAlarmEventTypeComp(alarmEventTypeCompIds);
        if (anlyAlarmEventTypeCompList == null || anlyAlarmEventTypeCompList.isEmpty())
            return;
        Long[] anlyAlarmEventTypeCompIdList = anlyAlarmEventTypeCompList.stream().map(item -> item.getAlarmEventTypeCompId()).toArray(Long[]::new);
        CommonResult commonResult = repo.deleteAlarmEventTypeComp(anlyAlarmEventTypeCompIdList);
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
    }

    /**
     * 修改事件类型对照配置
     *
     * @param alarmEventTypeCompEntity 事件类型对照配置实体
     * @throws Exception
     * <AUTHOR>   2018-03-30
     */
    @Override
    public void updateAlarmEventTypeComp(AlarmEventTypeCompEntity alarmEventTypeCompEntity) throws Exception {
        // 实体转换持久层实体
        AlarmEventTypeComp alarmEventTypeCompPO = ObjectConverter.entityConverter(alarmEventTypeCompEntity, AlarmEventTypeComp.class);
        // 校验
        alarmEventTypeCompValidation(alarmEventTypeCompPO);
        // 实体转换为持久层实体
        alarmEventTypeCompPO = repo.getSingleAlarmEventTypeComp(alarmEventTypeCompEntity.getAlarmEventTypeCompId());
        CommonUtil.objectExchange(alarmEventTypeCompEntity, alarmEventTypeCompPO);
        // 赋值 修改人、修改名称、修改时间
        CommonUtil.returnValue(alarmEventTypeCompPO, CommonEnum.PageModelEnum.Edit.getIndex());
        // 调用DAL更新方法
        CommonResult commonResult = repo.updateAlarmEventTypeComp(alarmEventTypeCompPO);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
    }

    /**
     * 通过事件类型对照配置ID获取单条数据
     *
     * @param alarmEventTypeCompId 事件类型对照配置ID
     * <AUTHOR> 2018-03-30
     */
    @Override
    public AlarmEventTypeCompEntity getSingleAlarmEventTypeComp(Long alarmEventTypeCompId) {
        try {
            AlarmEventTypeComp alarmEventTypeComp = repo.getSingleAlarmEventTypeComp(alarmEventTypeCompId);
            return ObjectConverter.entityConverter(alarmEventTypeComp, AlarmEventTypeCompEntity.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 事件类型对照配置查询
     *
     * @param dcsCodeId       DcsCodeID
     * @param eventTypeSource 源事件类型
     * @param eventNameSource 源事件名称
     * @param eventTypeIds    事件类型ID集合
     * @param inUse           是否启用
     * @param page            分页信息
     * @return alarmEventTypeComp实体（分页）
     * <AUTHOR> 2018-03-30
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<AlarmEventTypeCompEntity> getAlarmEventTypeComp(Long dcsCodeId, String eventTypeSource, String eventNameSource, Long[] eventTypeIds, Integer inUse, Pagination page) throws Exception {
        try {
            PaginationBean<AlarmEventTypeComp> listAlarmEventTypeComp = repo.getAlarmEventTypeComp(dcsCodeId, eventTypeSource, eventNameSource, eventTypeIds,inUse, page);
            PaginationBean<AlarmEventTypeCompEntity> returnAlarmEventTypeComp = new PaginationBean<>(page,
                    listAlarmEventTypeComp.getTotal());
            returnAlarmEventTypeComp.setPageList(ObjectConverter.listConverter(listAlarmEventTypeComp.getPageList(), AlarmEventTypeCompEntity.class));
            for (AlarmEventTypeCompEntity entity : returnAlarmEventTypeComp.getPageList()) {
                AlarmEventTypeComp pojo = listAlarmEventTypeComp.getPageList().stream().filter(item -> item.getAlarmEventTypeCompId().equals(entity.getAlarmEventTypeCompId())).findFirst().orElse(new AlarmEventTypeComp());
                entity.setDcsName(pojo.getDcsCode().getName());
                entity.setEventTypeName(pojo.getEventType().getName());
            }
            return returnAlarmEventTypeComp;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 校验
     *
     * @param entity 事件类型对照配置实体
     * @throws Exception
     * <AUTHOR> 2018-03-30
     */
    private void alarmEventTypeCompValidation(AlarmEventTypeComp entity) throws Exception {
        // 实体不能为空
        if (entity == null) {
            throw new Exception("没有事件类型对照配置数据！");
        }
        // 调用DAL与数据库相关的校验
        CommonResult commonResult = repo.alarmEventTypeCompValidation(entity);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
    }
}
