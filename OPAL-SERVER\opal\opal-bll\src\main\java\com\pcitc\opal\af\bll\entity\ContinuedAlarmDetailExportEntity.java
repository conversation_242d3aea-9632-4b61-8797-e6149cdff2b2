package com.pcitc.opal.af.bll.entity;

import lombok.Data;
import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;

@Data
public class ContinuedAlarmDetailExportEntity extends BaseResRep implements Serializable {

    /**
     *装置名称
     */
    private String unitName;

    /**
     * 生产单元名称
     */
    private String prdtCellName;

    /**
     * 位号
     */
    private String alarmPointTag;

    /**
     * 描述
     */
    private String des;

    /**
     * 优先级（1紧急；2重要；3一般）
     */
    private String priority;


    /**
     * 报警标识
     */
    private String alarmFlagName;

    /**
     * 计量单位
     */
    private String measUnitName;
    /**
     * 报警时间
     */
    private String alarmTime;

    /**
     * 时长
     */
    private String timeLen;

    /**
     * 恢复时间
     */
    private String recoveryTime;

    /**
     * 响应时间
     */
    private String responseTime;
}
