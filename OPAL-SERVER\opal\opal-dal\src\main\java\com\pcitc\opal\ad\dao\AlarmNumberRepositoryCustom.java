package com.pcitc.opal.ad.dao;

import java.util.Date;
import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;

import com.pcitc.opal.common.CommonEnum;

/*
 * AlarmNumber实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmNumberRepositoryCustom
 * 作       者：kun.zhao
 * 创建时间：2017/10/09 
 * 修改编号：1
 * 描       述：AlarmNumber实体的Repository的JPA自定义接口 
 */
public interface AlarmNumberRepositoryCustom {

	/**
	 * 报警数量评估-报警数-图形显示
	 * 
	 * <AUTHOR> 2017-10-30
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param dateType 日期枚举类型
	 * @return List<Object[]> 返回对象数组集合
	 */
	List<Object[]> getAlarmNumber(String[] unitCodes, Long[] prdtCellIds, Date beginTime, Date endTime, CommonEnum.DateTypeEnum dateType);
	
	    /**
	     * 报警数量评估-报警数-单元显示
	     * 
	     * <AUTHOR> 2017-10-30
	     * @param unitCodes 装置编码数组
		 * @param prdtCellIds 生产单元id数组
		 * @param beginTime 报警事件的开始间
		 * @param endTime 报警事件的结束时间
	     * @throws Exception 
	     * @return List<Object[]> 返回对象数组集合
	     */
	    @RequestMapping("getAlarmNumberUnit")
		List<Object[]> getAlarmNumberUnit(String[] unitCodes, Long[] prdtCellIds, Date beginTime, Date endTime)
	    		throws Exception;
	    
	    /**
		 * 查询车间报警数量评估-报警数-图形显示
		 * 
		 * <AUTHOR> 2017-11-08
		 * @param unitCodes 车间编码数组
		 * @param beginTime 报警事件的开始间
		 * @param endTime 报警事件的结束时间
		 * @param dateType 日期枚举类型
		 * @throws Exception 
		 * @return List<Object[]> 返回对象数组集合
		 */
		 List<Object[]> getWorkShopAlarmNumber(String[] unitCodes,Date beginTime,Date endTime,CommonEnum.DateTypeEnum dateType) throws Exception;
		
		/**
		 * 查询车间报警数量评估-报警数-单元显示
		 * 
		 * <AUTHOR> 2017-11-08
		 * @param unitCodes 车间编码数组
		 * @param beginTime 报警事件的开始间
		 * @param endTime 报警事件的结束时间
		 * @throws Exception 
		 * @return List<Object[]> 返回对像数组集合
		 */
		 List<Object[]> getWorkShopAlarmNumberUnit(String[] unitCodes,Date beginTime,Date endTime) throws Exception;

    List<Object[]> deptmUnitAlarmStatt(String[] unitCodes, Date queryStartTime, Date displayEndTime) throws Exception;
}
