package com.pcitc.opal.pm.dao;

import org.springframework.data.jpa.repository.JpaRepository;
 
import com.pcitc.opal.pm.pojo.MeasUnit;

/*
 * MeasUnit实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_MeasUnitRepository
 * 作       者：jiangtao.xue
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描       述：MeasUnit实体的Repository实现   
 */
public interface MeasUnitRepository extends JpaRepository<MeasUnit, Long>, MeasUnitRepositoryCustom {

}
