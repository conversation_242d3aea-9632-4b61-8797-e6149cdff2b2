package com.pcitc.opal.pm.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.pm.pojo.PrdtCell;

/*
 * PrdtCell实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_PrdtCellRepository
 * 作       者：zheng.yang
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描       述：PrdtCell实体的Repository实现   
 */
public interface PrdtCellRepository extends JpaRepository<PrdtCell, Long>, PrdtCellRepositoryCustom {

}
