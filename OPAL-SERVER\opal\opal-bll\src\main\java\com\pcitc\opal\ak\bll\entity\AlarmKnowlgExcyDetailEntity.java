package com.pcitc.opal.ak.bll.entity;

import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;

import java.util.List;
import java.util.Map;

/*
 * 报警知识维护数据实体
 * 模块编号：pcitc_opal_bll_class_AlarmKnowlgExcyDetailEntity
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识维护数据实体
 */
public class AlarmKnowlgExcyDetailEntity {

    /**
     * 装置
     */
    private String unitName;

    /**
     * 生产单元简称
     */
    private String prdtCellSname;

    /**
     * 位号
     */
    private String tag;

    /**
     * 报警标识名称
     */
    private String alarmFlagName;
    /**
     * 级别(1A；2B)
     */
    private Integer craftRank;

    /**
     * 级别
     */
    private String craftRankName;

    /**
     * 报警值
     */
    private Double alarmValue;

    /**
     * 工艺联锁值
     */
    private String interlockLimitValue;

    /**
     * 工艺卡片值
     */
    private String craftLimitValue;

    /**
     * 计量单位
     */
    private String unitFlag;

    /**
     * 位置
     */
    private String location;

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPrdtCellSname() {
        return prdtCellSname;
    }

    public void setPrdtCellSname(String prdtCellSname) {
        this.prdtCellSname = prdtCellSname;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

    public Integer getCraftRank() {
        return craftRank;
    }

    public void setCraftRank(Integer craftRank) {
        this.craftRank = craftRank;
    }

    public String getCraftRankName() {
        if (craftRank == null) return "";
        return CommonEnum.CraftRankEnum.getName(craftRank);
    }

    public void setCraftRankName(String craftRankName) {
        this.craftRankName = craftRankName;
    }

    public Double getAlarmValue() {
        return alarmValue;
    }

    public void setAlarmValue(Double alarmValue) {
        this.alarmValue = alarmValue;
    }

    public String getInterlockLimitValue() {
        return interlockLimitValue;
    }

    public void setInterlockLimitValue(String interlockLimitValue) {
        this.interlockLimitValue = interlockLimitValue;
    }

    public String getCraftLimitValue() {
        return craftLimitValue;
    }

    public void setCraftLimitValue(String craftLimitValue) {
        this.craftLimitValue = craftLimitValue;
    }

    public String getUnitFlag() {
        return unitFlag;
    }

    public void setUnitFlag(String unitFlag) {
        this.unitFlag = unitFlag;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}

