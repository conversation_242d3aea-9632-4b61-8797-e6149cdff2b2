package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.pm.bll.SendMsgService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @USER: chenbo
 * @DATE: 2023/1/30
 * @TIME: 11:14
 * @DESC: 石家庄短信发送实现类
 **/
@Slf4j
@Service
@ConditionalOnProperty(name ="send_type", havingValue = "sjz", matchIfMissing = true)
public class SendMsgServiceImplSJZ implements SendMsgService {
    @Override
    public Map<String, String> sendBatch(List<String> mobiles, String content) {

        //key->手机号，value->发送结果
        HashMap<String, String> resMap = new HashMap<>();
        OkHttpClient client = new OkHttpClient();


        for (String phone : mobiles) {
            StringBuilder result = new StringBuilder();

            RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_XML_VALUE), "<?xml version=\"1.0\"?>\n" +
                    "<MT>\n" +
                    "\t<UserID>alarm</UserID>\n" +
                    "\t<Password>alarm</Password>\n" +
                    "<SPNumber>040</SPNumber>\n" +
                    "<ReceiverName>报警短信推送</ReceiverName>\n" +
                    "<ReceiverDepart>报警短信推送</ReceiverDepart>\n" +
                    "<Handset>" + phone + "</Handset>\n" +
                    "<Message>" + content + "</Message>\n" +
                    "<SendTime></SendTime>\n" +
                    "</MT>");
            Request request = new Request.Builder()
                    .url("http://10.125.29.199/SinoPecSMS.aspx?op=mt")
                    .post(body)
                    .addHeader("content-type", "application/xml")
                    .build();
            try {
                Response response = client.newCall(request).execute();
                boolean successful = response.isSuccessful();
                if (successful) {
                    String res = Objects.requireNonNull(response.body()).string();
                    Document document = DocumentHelper.parseText(res);
                    Element root = document.getRootElement();
                    String result1 = root.elementText("Result");//返回结果
                    String handset = root.elementText("Handset");//手机号
                    String seqNumber = root.elementText("SeqNumber");//查询的状态码
                    result.append("手机号：").append(handset).append(",短信发送结果：").append(getMessage(result1)).append(",回调参数：").append(seqNumber);
                }
            } catch (IOException e) {
                log.error("当前发送短信接口调用失败，手机号为{}，短信内容为{}，异常内容{}", phone, content, e.getMessage());
                result.append("当前发送短信接口调用失败，手机号为").append(phone).append("，异常内容").append(e.getMessage());
                e.printStackTrace();
            } catch (DocumentException e) {
                log.error("当前发送短信返回结果解析失败，手机号为{}，短信内容为{}，异常内容{}", phone, content, e.getMessage());
                result.append("当前发送短信接口调用失败，手机号为").append(phone).append("，异常内容").append(e.getMessage());
                e.printStackTrace();
            } catch (Exception e){
                log.error("发送异常" + e.toString());
            }

            resMap.put(phone, result.toString());

        }

        return resMap;
    }


    /**
     * 对照短信接口返回的状态码
     * @param code 返回值状态码
     */
    public String getMessage(String code){
        if ("0".equals(code)){
            return "发送成功";
        } else if ("1".equals(code)) {
            return "提交参数不能为空";
        } else if ("2".equals(code)) {
            return "账号无效";
        } else if ("3".equals(code)) {
            return "账号密码错误";
        } else if ("4".equals(code)) {
            return "预约发送时间无效";
        } else if ("5".equals(code)) {
            return "IP不合法";
        } else if ("6".equals(code)) {
            return "号码中含有无效号码";
        } else if ("7".equals(code)) {
            return "内容中含有非法关键字";
        } else if ("8".equals(code)) {
            return "内容长度超过上限，最大120字符";
        } else if ("9".equals(code)) {
            return "格式错误";
        }else {
            return "未知状态";
        }
    }
}
