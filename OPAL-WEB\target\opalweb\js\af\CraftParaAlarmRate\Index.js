var getQueryTimeUrl = OPAL.API.commUrl + '/getQueryTime';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var getCurrentTimeUrl = OPAL.API.commUrl + '/getCurrentTime';
var workTeamUrl = OPAL.API.commUrl + "/getWorkTeam";

var searchUrl = OPAL.API.afUrl + '/craftParaAlarmRate/getCraftParaAlarmRate';
var exportUrl = OPAL.API.afUrl + '/craftParaAlarmRate/alarmRateExportExcel';
var alarmNumberExportUrl = OPAL.API.afUrl + '/craftParaAlarmRate/alarmAmountDetailExport';
var respondExportUrl  = OPAL.API.afUrl + '/craftParaAlarmRate/alarmTimelyResponseRateDetailExport';
var handleExportUrl = OPAL.API.afUrl + '/craftParaAlarmRate/alarmTimelyDisposalRateDetailExport';
var isFilterWorkTeam = OPAL.API.afUrl + "/craftParaAlarmRate/isFilterWorkTeam";
var monitorTypeUrl = OPAL.API.commUrl + "/getMonitorTypeList"; //专业

var dateTimeList = '';
var alarmTime = '';
var isLoading = true;
var avgAlarmRateCharts, peakAlarmRateCharts, excitationRateCharts;
var clickUnitId = '';
var unitTableArr = [];
var alarmRateData;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            this.bindUi();
            //初始化 专业
            page.logic.initMonitorType();
            page.logic.initTable(JSON.stringify(""));
            // 初始化 报警时间的时间点
            page.logic.getQueryTime();
            //初始化日期
            page.logic.getShowTime();
            // 初始化装置数
            page.logic.initUnitTree();
            //24小时表格
            page.logic.initOpetateTable();

            //装置赋值
            if (isLoading && (page.data.param.unitIds == null || page.data.param.unitIds == undefined || page.data.param.unitIds.length == 0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("FloodAlarmAnalysis");
                if (cookieValue != null && cookieValue != undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
            $('#workTeamIds').html("");
            $("#workTeamIds").prop('disabled', true);

        },
        bindUi: function () {
            $('#myTab li').click(function () {
                var flag = $(this).attr('showFlag');
                if (flag == 'table24') {
                    if (clickUnitId != '') {

                        page.logic.initOpetateTable();
                    }
                } else if (flag == 'Respond') {
                    if (clickUnitId != '') {

                        page.logic.initRespondTable();
                    }
                } else if (flag == 'Handle') {
                    if (clickUnitId != '') {

                        page.logic.initHandleTable();
                    }
                }
            })
            //查询
            $('#btnSearch').click(function () {
                if (OPAL.util.checkDateIsValid() == true) {
                    isLoading = false;
                    page.logic.search();
                }
            })
            /**
             * 24小时持续报警数导出
             */
            $('#alarmNumberExportBut').click(function () {
                page.logic.alarmNumberExport();
            });
            /**
             * 报警确认及时率
             */
            $('#respondExportBut').click(function () {
                page.logic.respondExport();
            });
            /**
             * 报警处置及时率
             */
            $('#handleExportBut').click(function () {
                page.logic.handleExport();
            });
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                // avgAlarmRateCharts.resize();
                // peakAlarmRateCharts.resize();
                // excitationRateCharts.resize();
            };
            // 导出
            $("#CraftExport").click(function () {
                page.logic.exportExcel();
            })
        },
        data: {
            param: {}
        },
        logic: {
            /**
             * 初始化页面
             */
            search: function () {
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                page.data.param = OPAL.form.getData("searchForm");
                var unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                page.data.param.unitIds = unitIds;
                if (page.data.param.workTeamIds) {
                    page.data.param.workTeamIds = page.data.param.workTeamIds[0];
                }

                page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                $("#btnSearch").prop('disabled', true);

                $.ajax({
                    url: searchUrl,
                    data: page.data.param,
                    dataType: "JSON",
                    type: 'GET',
                    success: function (result) {
                        $("#btnSearch").prop('disabled', false);
                        if (page.data.param.workTeamIds) {
                            $("#Alarmnumber24").hide()
                        } else {
                            $("#Alarmnumber24").show()
                        }

                        var dataArr = $.ET.toObjectArr(result);
                        alarmRateData = $.ET.toObjectArr(result);
                        unitTableArr = [];
                        dataArr.forEach(item => {
                            unitTableArr.push(item.unitid);
                        })
                        $("#avgCraftParaAlarmRate").html(dataArr[0].avgCraftParaAlarmRate);
                        page.logic.initTable(dataArr);
                        dateTimeList = dataArr[0].endTime;
                        if (dataArr[0].avgAlarmRateDetail) {
                            var avgAlarmRateDetail = JSON.parse(dataArr[0].avgAlarmRateDetail)
                        } else {
                            var avgAlarmRateDetail = {}
                        }

                        var variationTrendDateArr = []
                        var alarmRate = []
                        for (let key in avgAlarmRateDetail) {
                            if (Object.prototype.hasOwnProperty.call(avgAlarmRateDetail, key)) {

                                variationTrendDateArr.push(key);
                                alarmRate.push(avgAlarmRateDetail[key]);
                            }
                        }
                        var newdatalist = []
                        var newdata = {
                            alarmRate: alarmRate,
                            unitId: dataArr[0].unitid,
                            unitName: dataArr[0].unitCode
                        }


                        newdatalist.push(newdata)
                        if (dataArr[0].peakAlarmRateDetail) {
                            var peakAlarmRateDetail = JSON.parse(dataArr[0].peakAlarmRateDetail)
                        } else {
                            var peakAlarmRateDetail = {}
                        }

                        var tenMinArr = []
                        for (let key in peakAlarmRateDetail) {
                            if (Object.prototype.hasOwnProperty.call(peakAlarmRateDetail, key)) {

                                // variationTrendDateArr.push(key);
                                tenMinArr.push(peakAlarmRateDetail[key]);
                            }
                        }
                        var newdata = {
                            alarmRate: tenMinArr,
                            unitId: dataArr[0].unitid,
                            unitName: dataArr[0].unitCode
                        }
                        newdatalist.push(newdata)
                        page.logic.initCharts(newdatalist, variationTrendDateArr, 1)
                        clickUnitId = [dataArr[0].unitid];
                        page.logic.initOpetateTable();
                        //确认及时率和处置及时率
                        page.logic.initRespondTable();
                        page.logic.initHandleTable()
                    },
                    error: function (result) {
                        $("#btnSearch").prop('disabled', false);
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            /**
             * 主表格导出
             */
            exportExcel: function () {
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                page.data.param = OPAL.form.getData("searchForm");
                var titleArray = new Array();
                var tableTitle = $('#floodTable').bootstrapTable('getOptions').columns[0];
                $.each(tableTitle, function (i, el) {
                    titleArray.push({
                        'key': el.field,
                        'value': el.title
                    })
                })
                page.data.param.titles = JSON.stringify(titleArray);

                var unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                page.data.param.unitIds = unitIds;
                if (page.data.param.workTeamIds) {
                    page.data.param.workTeamIds = page.data.param.workTeamIds[0];
                }
                page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                var url = exportUrl + "?" + $.param(page.data.param);
                window.location = url;
            },
            /**
             * 初始化表格
             */
            initTable: function (data) {
                var results = data;
                let avgAlarmRateNum = 0; // 时平均报警数
                let alarmAmountNum = 0; // 24小时持续报警数
                let craftParaAlarmRateNum = 0; // 工艺参数报警率
                let alarmTimelyResponseRateNum = 0; // 报警确认及时率
                let alarmTimelyDisposalRateNum = 0; // 报警处置及时率
                let showFooter = false;
                if (results != "\"\"") {
                    showFooter = true;
                    results.forEach(item => {
                        avgAlarmRateNum += Number(item.avgAlarmRate)
                        alarmAmountNum += Number(item.alarmAmount)
                        craftParaAlarmRateNum += Number(item.craftParaAlarmRate)
                        alarmTimelyResponseRateNum += Number(item.alarmTimelyResponseRate)
                        alarmTimelyDisposalRateNum += Number(item.alarmTimelyDisposalRate)
                    });
                    // avgAlarmRateNum = (avgAlarmRateNum / results.length).toFixed(2)
                    // craftParaAlarmRateNum = (craftParaAlarmRateNum / results.length).toFixed(2)
                    // alarmTimelyResponseRateNum = (alarmTimelyResponseRateNum / results.length).toFixed(2)
                    // alarmTimelyDisposalRateNum = (alarmTimelyDisposalRateNum / results.length).toFixed(2)
                    avgAlarmRateNum = results[0].avgAvgAlarmRate.toFixed(2)
                    alarmAmountNum = results[0].avgAlarmAmount.toFixed(2)
                    craftParaAlarmRateNum = results[0].avgCraftParaAlarmRate.toFixed(2)
                    alarmTimelyResponseRateNum = results[0].avgAlarmTimelyResponseRate.toFixed(2)
                    alarmTimelyDisposalRateNum = results[0].avgAlarmTimelyDisposalRate.toFixed(2)
                } else {
                    showFooter = false;
                    results = [];
                }
                if (page.data.param.workTeamIds) {
                    var options = {
                        detailView: false,
                        striped: true, //是否显示行间隔色
                        sidePagination: "client",
                        sortOrder: "asc", //排序方式
                        pageNumber: 1, //初始化加载第一页，默认第一页
                        height: 350,

                        columns: [{
                            field: 'unitCode',
                            title: '装置',
                            align: 'left',
                            formatter: function (value, row, index) {
                                return '<a href="javascript:page.logic.selectUnit(\'' + row.unitid + '\')">' + row.unitCode + '</a>'
                            },
                            footerFormatter: function (data) {
                                return '<a class="text-center" href="javascript:page.logic.selectUnittall()">合计</a>'
                            }
                        }, {
                            field: 'avgAlarmRate',
                            title: '时平均报警数',
                            align: 'right',
                            formatter: function (value, row, index) {
                                if (row.avgAlarmRate > 6) {
                                    return '<div class="text-right" style="color:red;">' + row.avgAlarmRate + '</div>'
                                } else {
                                    return '<div class="text-right">' + row.avgAlarmRate + '</div>'
                                }
                            },
                            footerFormatter: function (data) {
                                if (avgAlarmRateNum > 6) {
                                    return '<div class="text-right" style="color:red;">' + avgAlarmRateNum + '</div>'
                                } else {
                                    return '<div class="text-right">' + avgAlarmRateNum + '</div>'
                                }
                            }
                        }, {
                            field: 'peakAlarmRate',
                            title: '峰值报警数',
                            align: 'right',
                        }, {
                            field: 'alarmTimelyResponseRate',
                            title: '报警确认及时率',
                            align: 'right',
                            formatter: function (value, row, index) {
                                if (row.alarmTimelyResponseRate < 95) {
                                    return '<div class="text-right" style="color:red;">' + row.alarmTimelyResponseRate + '</div>'
                                } else {
                                    return '<div class="text-right">' + row.alarmTimelyResponseRate + '</div>'
                                }
                            },
                            footerFormatter: function (data) {
                                if (alarmTimelyResponseRateNum < 95) {
                                    return '<div class="text-right" style="color:red;">' + alarmTimelyResponseRateNum + '</div>'
                                } else {
                                    return '<div class="text-right">' + alarmTimelyResponseRateNum + '</div>'
                                }
                            }
                        }, {
                            field: 'alarmTimelyDisposalRate',
                            title: '报警处置及时率',
                            align: 'right',
                            formatter: function (value, row, index) {
                                if (row.alarmTimelyDisposalRate < 95) {
                                    return '<div class="text-right" style="color:red;">' + row.alarmTimelyDisposalRate + '</div>'
                                } else {
                                    return '<div class="text-right">' + row.alarmTimelyDisposalRate + '</div>'
                                }
                            },
                            footerFormatter: function (data) {
                                if (alarmTimelyDisposalRateNum < 95) {
                                    return '<div class="text-right" style="color:red;">' + alarmTimelyDisposalRateNum + '</div>'
                                } else {
                                    return '<div class="text-right">' + alarmTimelyDisposalRateNum + '</div>'
                                }
                            }
                        }, {
                            field: 'craftParaAlarmRate',
                            title: '工艺参数报警率',
                            align: 'right',
                            footerFormatter: function (data) {
                                return '<div class="text-right">' + craftParaAlarmRateNum + '</div>'
                            }
                        }
                        ],
                        showFooter: showFooter
                    };
                } else {
                    var options = {
                        detailView: true,
                        striped: true, //是否显示行间隔色
                        sidePagination: "client",
                        sortOrder: "asc", //排序方式
                        pageNumber: 1, //初始化加载第一页，默认第一页
                        height: 350,

                        columns: [{
                            field: 'unitCode',
                            title: '装置',
                            align: 'left',
                            formatter: function (value, row, index) {
                                return '<a href="javascript:page.logic.selectUnit(\'' + row.unitid + '\')">' + row.unitCode + '</a>'
                            },
                            footerFormatter: function (data) {
                                return '<a class="text-center" href="javascript:page.logic.selectUnittall()">合计</a>'
                            }
                        }, {
                            field: 'avgAlarmRate',
                            title: '时平均报警数',
                            align: 'right',
                            formatter: function (value, row, index) {
                                if (row.avgAlarmRate > 6) {
                                    return '<div class="text-right" style="color:red;">' + row.avgAlarmRate + '</div>'
                                } else {
                                    return '<div class="text-right">' + row.avgAlarmRate + '</div>'
                                }
                            },
                            footerFormatter: function (data) {
                                if (avgAlarmRateNum > 6) {
                                    return '<div class="text-right" style="color:red;">' + avgAlarmRateNum + '</div>'
                                } else {
                                    return '<div class="text-right">' + avgAlarmRateNum + '</div>'
                                }
                            }
                        }, {
                            field: 'alarmAmount',
                            title: '24小时持续报警数',
                            align: 'right',
                            footerFormatter: function (data) {
                                return '<div class="text-right">' + alarmAmountNum + '</div>'
                            }

                        }, {
                            field: 'peakAlarmRate',
                            title: '峰值报警数',
                            align: 'right',
                        }, {
                            field: 'alarmTimelyResponseRate',
                            title: '报警确认及时率',
                            align: 'right',
                            formatter: function (value, row, index) {
                                if (row.alarmTimelyResponseRate < 95) {
                                    return '<div class="text-right" style="color:red;">' + row.alarmTimelyResponseRate + '</div>'
                                } else {
                                    return '<div class="text-right">' + row.alarmTimelyResponseRate + '</div>'
                                }
                            },
                            footerFormatter: function (data) {
                                if (alarmTimelyResponseRateNum < 95) {
                                    return '<div class="text-right" style="color:red;">' + alarmTimelyResponseRateNum + '</div>'
                                } else {
                                    return '<div class="text-right">' + alarmTimelyResponseRateNum + '</div>'
                                }
                            }
                        }, {
                            field: 'alarmTimelyDisposalRate',
                            title: '报警处置及时率',
                            align: 'right',
                            formatter: function (value, row, index) {
                                if (row.alarmTimelyDisposalRate < 95) {
                                    return '<div class="text-right" style="color:red;">' + row.alarmTimelyDisposalRate + '</div>'
                                } else {
                                    return '<div class="text-right">' + row.alarmTimelyDisposalRate + '</div>'
                                }
                            },
                            footerFormatter: function (data) {
                                if (alarmTimelyDisposalRateNum < 95) {
                                    return '<div class="text-right" style="color:red;">' + alarmTimelyDisposalRateNum + '</div>'
                                } else {
                                    return '<div class="text-right">' + alarmTimelyDisposalRateNum + '</div>'
                                }
                            }
                        }, {
                            field: 'craftParaAlarmRate',
                            title: '工艺参数报警率',
                            align: 'right',
                            footerFormatter: function (data) {
                                return '<div class="text-right">' + craftParaAlarmRateNum + '</div>'
                            }
                        }
                        ],
                        showFooter: showFooter,
                        onExpandRow: function (index, row, $detail) {
                            page.logic.initMainTable(index, row, $detail);
                        }
                    };
                }
                $('#floodTable').bootstrapTable(options);
                $('#floodTable').bootstrapTable('refreshOptions', options);
                if (results == undefined) {
                    results = [];
                }
                $("#floodTable").bootstrapTable("load", results);
                if (!page.data.param.workTeamIds) {
                    $('.fixed-table-footer table tbody tr td:first').css('width', '30px');
                }
            },
            /**
             * 最上边的二级列表
             */
            initMainTable: function (index, row, $detail) {
                var subId = 'sub_mainable' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                let options = {
                    columns: [{
                        field: 'unitCode',
                        title: '班组',
                        align: 'center',
                    }, {
                        field: 'avgAlarmRate',
                        title: '时平均报警数',
                        align: 'center',
                    }, {
                        field: 'peakAlarmRate',
                        title: '峰值报警数',
                        align: 'center',
                    }, {
                        field: 'alarmTimelyResponseRate',
                        title: '报警确认及时率',
                        align: 'center',
                    }, {
                        field: 'alarmTimelyDisposalRate',
                        title: '报警处置及时率',
                        align: 'center',
                    }, {
                        field: 'craftParaAlarmRate',
                        title: '工艺参数报警率',
                        align: 'center',
                    }],
                };

                $('#' + subId).bootstrapTable(options);
                let arr = JSON.parse(row.teamDetail);
                $('#' + subId).bootstrapTable("load", arr);
            },
            /**
             * 单个装置
             */
            initUnitTabs: function (unitId) {
                var chartsData;
                var variationTrendDateArr = [];
                var newdatalist = [];
                var alarmRate = [];
                $.each(alarmRateData, function (i, el) {
                    if (el.unitid == unitId[0]) {
                        chartsData = (el);
                        return;
                    }
                })
                if (chartsData.avgAlarmRateDetail) {
                    var avgAlarmRateDetail = JSON.parse(chartsData.avgAlarmRateDetail)
                } else {
                    var avgAlarmRateDetail = {}
                }


                for (let key in avgAlarmRateDetail) {
                    if (Object.prototype.hasOwnProperty.call(avgAlarmRateDetail, key)) {

                        variationTrendDateArr.push(key);
                        alarmRate.push(avgAlarmRateDetail[key]);
                    }
                }
                var newdata = {
                    alarmRate: alarmRate,
                    unitId: chartsData.unitid,
                    unitName: chartsData.unitCode
                }
                newdatalist.push(newdata)
                if (chartsData.peakAlarmRateDetail) {
                    var peakAlarmRateDetail = JSON.parse(chartsData.peakAlarmRateDetail)
                } else {
                    var peakAlarmRateDetail = {}
                }

                var tenMinArr = []
                for (let key in peakAlarmRateDetail) {
                    if (Object.prototype.hasOwnProperty.call(peakAlarmRateDetail, key)) {

                        // variationTrendDateArr.push(key);
                        tenMinArr.push(peakAlarmRateDetail[key]);
                    }
                }
                var newdata = {
                    alarmRate: tenMinArr,
                    unitId: chartsData.unitid,
                    unitName: chartsData.unitCode
                }
                newdatalist.push(newdata)

                page.logic.initCharts(newdatalist, variationTrendDateArr);
            },
            initUnitTabstall: function (unitId) {

                var chartsData = [];
                var variationTrendDateArr = [];
                var newdatalist = [];
                var legenddatax = [];
                var variationTrendDateArr1 = [];
                var newdatalist1 = [];
                var alarmRate1 = [];
                var legenddatax1 = [];


                for (var i = 0; i < alarmRateData.length; i++) {
                    if (alarmRateData[i].unitid == unitId[i]) {
                        chartsData.push(alarmRateData[i]);

                    }
                }

                if (chartsData[0].avgAlarmRateDetail) {
                    var avgAlarmRateDetailx = JSON.parse(chartsData[0].avgAlarmRateDetail)
                } else {
                    var avgAlarmRateDetailx = {}
                }

                //let avgAlarmRateDetailx = JSON.parse(chartsData[0].avgAlarmRateDetail)

                for (let key in avgAlarmRateDetailx) {
                    if (Object.prototype.hasOwnProperty.call(avgAlarmRateDetailx, key)) {

                        variationTrendDateArr.push(key);

                    }
                }
                for (var i = 0; i < chartsData.length; i++) {
                    if (chartsData[i].unitid == unitId[i]) {
                        let avgAlarmRateDetail = chartsData[i].avgAlarmRateDetail ? JSON.parse(chartsData[i].avgAlarmRateDetail) : chartsData[i].avgAlarmRateDetail
                        var alarmRate = [];
                        for (let key in avgAlarmRateDetail) {
                            if (Object.prototype.hasOwnProperty.call(avgAlarmRateDetail, key)) {

                                // variationTrendDateArr.push(key);
                                alarmRate.push(avgAlarmRateDetail[key]);
                            }
                        }
                        var newdata = {
                            data: alarmRate,
                            type: 'line',
                            stack: 'Total',
                            smooth: true,
                            name: chartsData[i].unitCode
                        }
                        newdatalist.push(newdata)
                        legenddatax.push(chartsData[i].unitCode)

                    } else {
                        return
                    }
                }


                let newdatalistnew = JSON.parse(JSON.stringify(newdatalist))

                let option = {
                    tooltip: {
                        trigger: 'axis',
                        confine: true,
                        formatter: function (params) {
                            var endTime = dateTimeList;
                            var endTimeValue = moment(endTime).format("YYYY-MM-DD HH:mm:ss");
                            var lastTime = variationTrendDateArr[(variationTrendDateArr.length - 1)];
                            var timeData1, timeData2 = '';
                            var timeDiv = '<div></div>';
                            params.forEach(item => {
                                var name = item.name.replace(/\//g, "-");
                                var d1 = Date.parse(new Date(item.name));
                                var d2 = d1 + (60 * 60 * 24 * 1000);
                                var d3 = moment(d2).format("YYYY-MM-DD");
                                if (item.name != lastTime) {
                                    timeData1 = '从： ' + name + ' ' + queryTimeValue + ' 至：' + d3 + ' ' + queryTimeValue
                                } else {
                                    timeData1 = '从： ' + name + ' ' + queryTimeValue + ' 至：' + endTimeValue
                                }
                                // if (params[1] == null || params[1] == undefined || params[1] == '') {
                                timeData2 += '<br> <span style="display:inline-block;width:9px;height:9px;border-radius: 50%"></span> ' + item.seriesName + '：' + item.value
                                // }
                            })
                            if (params.length <= 1) {
                                str = timeData1 + timeData2
                            } else {
                                str = '<div style="height: 300px;overflow: auto;">' + timeData1 + timeData2 + '</div>'
                            }
                            return str;
                        }
                    },

                    legend: {
                        data: legenddatax,
                        padding: 15, // 图例内边距，单位px，默认各方向内边距为5，
                        // 接受数组分别设定上右下左边距，同css
                        right: 34,
                    },
                    toolbox: {
                        show: false,
                        feature: {
                            magicType: {
                                show: true,
                            },
                        }
                    },
                    xAxis: {
                        type: 'category', //类目轴
                        boundaryGap: false,
                        data: variationTrendDateArr,
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#ebf2f4']
                            }
                        },
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#333',
                                width: 1, //这里是为了突出显示加上的  
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#333', //改变x轴字体颜色
                            },
                        },

                    },
                    yAxis: {
                        type: 'value',
                        splitLine: {
                            lineStyle: {
                                color: ['#ebf2f4'],
                            }
                        },
                        axisTick: { //坐标轴 刻度显示
                            show: false,
                        },
                        axisLine: {
                            // show: false,
                            lineStyle: {
                                color: '#333',
                                width: 1,
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#333', //改变y轴字体颜色
                            },
                        },
                    },
                    grid: {
                        top: '40px',
                        left: '45px',
                        right: '35px',
                        height: '300px'
                    },
                    series: newdatalistnew
                };

                avgAlarmRateCharts.clear();
                avgAlarmRateCharts.dispose();
                avgAlarmRateCharts = echarts.init(document.getElementById('Alarmverif'));
                avgAlarmRateCharts.setOption(option);

                let peakAlarmRateDetailx = JSON.parse(chartsData[0].peakAlarmRateDetail)

                for (let key in peakAlarmRateDetailx) {
                    if (Object.prototype.hasOwnProperty.call(peakAlarmRateDetailx, key)) {

                        variationTrendDateArr1.push(key);

                    }
                }

                for (var i = 0; i < chartsData.length; i++) {
                    if (chartsData[i].unitid == unitId[i]) {
                        let peakAlarmRateDetail = JSON.parse(chartsData[i].peakAlarmRateDetail)
                        var alarmRate = [];
                        for (let key in peakAlarmRateDetail) {
                            if (Object.prototype.hasOwnProperty.call(peakAlarmRateDetail, key)) {

                                // variationTrendDateArr.push(key);
                                alarmRate.push(peakAlarmRateDetail[key]);
                            }
                        }
                        var newdata = {
                            data: alarmRate,
                            type: 'line',
                            stack: 'Total',
                            name: chartsData[i].unitCode
                        }
                        newdatalist1.push(newdata)
                        legenddatax1.push(chartsData[i].unitCode)

                    } else {
                        return
                    }
                }
                let newdatalistnew1 = JSON.parse(JSON.stringify(newdatalist1))
                let option1 = {
                    tooltip: {
                        trigger: 'axis',
                        confine: true,
                        formatter: function (params) {
                            var endTime = dateTimeList;
                            var endTimeValue = moment(endTime).format("YYYY-MM-DD HH:mm:ss");
                            var lastTime = variationTrendDateArr1[(variationTrendDateArr1.length - 1)];
                            var timeData1, timeData2 = '';
                            var timeDiv = '<div></div>';
                            params.forEach(item => {
                                var name = item.name.replace(/\//g, "-");
                                var d1 = Date.parse(new Date(item.name));
                                var d2 = d1 + (60 * 60 * 24 * 1000);
                                var d3 = moment(d2).format("YYYY-MM-DD");
                                if (item.name != lastTime) {
                                    timeData1 = '从： ' + name + ' ' + queryTimeValue + ' 至：' + d3 + ' ' + queryTimeValue
                                } else {
                                    timeData1 = '从： ' + name + ' ' + queryTimeValue + ' 至：' + endTimeValue
                                }
                                // if (params[1] == null || params[1] == undefined || params[1] == '') {
                                timeData2 += '<br> <span style="display:inline-block;width:9px;height:9px;border-radius: 50%"></span> ' + item.seriesName + '：' + item.value
                                // }
                            })
                            if (params.length <= 1) {
                                str = timeData1 + timeData2
                            } else {
                                str = '<div style="height: 300px;overflow: auto;">' + timeData1 + timeData2 + '</div>'
                            }
                            return str;
                        }
                    },

                    legend: {
                        data: legenddatax1,
                        padding: 15, // 图例内边距，单位px，默认各方向内边距为5，
                        // 接受数组分别设定上右下左边距，同css
                        right: 34,
                    },
                    toolbox: {
                        show: false,
                        feature: {
                            magicType: {
                                show: true,
                            },
                        }
                    },
                    xAxis: {
                        type: 'category', //类目轴
                        boundaryGap: false,
                        data: variationTrendDateArr1,
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#ebf2f4']
                            }
                        },
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#333',
                                width: 1, //这里是为了突出显示加上的  
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#333', //改变x轴字体颜色
                            },
                        },

                    },
                    yAxis: {
                        type: 'value',
                        splitLine: {
                            lineStyle: {
                                color: ['#ebf2f4'],
                            }
                        },
                        axisTick: { //坐标轴 刻度显示
                            show: false,
                        },
                        axisLine: {
                            // show: false,
                            lineStyle: {
                                color: '#333',
                                width: 1,
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#333', //改变y轴字体颜色
                            },
                        },
                    },
                    grid: {
                        top: '40px',
                        left: '45px',
                        right: '35px',
                        height: '300px'
                    },
                    series: newdatalistnew1
                };
                peakAlarmRateCharts.clear();
                peakAlarmRateCharts.dispose();
                peakAlarmRateCharts = echarts.init(document.getElementById('Alarmver'));
                peakAlarmRateCharts.setOption(option1);
            },
            /**
             * 初始化图表
             */
            initCharts: function (data, xAxisArray, type) {
                if (avgAlarmRateCharts && !avgAlarmRateCharts.isDisposed()) {
                    avgAlarmRateCharts.clear();
                    avgAlarmRateCharts.dispose();
                }
                if (peakAlarmRateCharts && !peakAlarmRateCharts.isDisposed()) {
                    peakAlarmRateCharts.clear();
                    peakAlarmRateCharts.dispose();
                }
                if (excitationRateCharts && !excitationRateCharts.isDisposed()) {
                    excitationRateCharts.clear();
                    excitationRateCharts.dispose();
                }
                if (data[0] == undefined || data[0] == null || data[0].length == 0) {
                    avgAlarmRateCharts = OPAL.ui.chart.initEmptyChart('Alarmverif');
                    peakAlarmRateCharts = OPAL.ui.chart.initEmptyChart('Alarmver');
                    return;
                }

                var avgAlarmRateArray = data[0];
                var legendArray = new Array;
                var avgAlarmRateRateArr = new Array;
                var peakAlarmRateRateArr = new Array;


                $.each(avgAlarmRateArray, function (i, el) {
                    legendArray.push(el.unitName);
                })
                legendArray.push(avgAlarmRateArray.unitName);
                avgAlarmRateRateArr.push(avgAlarmRateArray.alarmRate);
                peakAlarmRateRateArr.push(data[1].alarmRate);
                avgAlarmRateCharts = echarts.init(document.getElementById('Alarmverif'));
                peakAlarmRateCharts = echarts.init(document.getElementById('Alarmver'));
                excitationRateCharts = echarts.init(document.getElementById('Alarmvermust'));
                var option = page.logic.setOptions(legendArray, xAxisArray);
                avgAlarmRateCharts.setOption(option);
                peakAlarmRateCharts.setOption(option);
                excitationRateCharts.setOption(option);
                page.logic.changeOption(avgAlarmRateCharts, legendArray, avgAlarmRateRateArr, '平均报警数', xAxisArray);
                page.logic.changeOption(peakAlarmRateCharts, legendArray, peakAlarmRateRateArr, '峰值报警数', xAxisArray);
            },
            /**
             * 设置图表的配置
             */
            setOptions: function (legendArray, xAxisArray) {
                var option = {
                    title: {
                        text: '',
                        left: 'center',
                    },
                    tooltip: {
                        trigger: 'item',
                    },
                    legend: {
                        data: legendArray,
                        padding: 15, // 图例内边距，单位px，默认各方向内边距为5，
                        // 接受数组分别设定上右下左边距，同css
                        right: 34,
                    },
                    toolbox: {
                        show: false,
                        feature: {
                            magicType: {
                                show: true,
                            },
                        }
                    },
                    xAxis: {
                        type: 'category', //类目轴
                        boundaryGap: false,
                        data: xAxisArray,
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#ebf2f4']
                            }
                        },
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#333',
                                width: 1, //这里是为了突出显示加上的  
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#333', //改变x轴字体颜色
                            },
                        },

                    },
                    yAxis: {
                        type: 'value',
                        splitLine: {
                            lineStyle: {
                                color: ['#ebf2f4'],
                            }
                        },
                        axisTick: { //坐标轴 刻度显示
                            show: false,
                        },
                        axisLine: {
                            // show: false,
                            lineStyle: {
                                color: '#333',
                                width: 1,
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#333', //改变y轴字体颜色
                            },
                        },
                    },
                    grid: {
                        top: '40px',
                        left: '45px',
                        right: '35px',
                        height: '300px'
                    }
                };
                let series = [];
                legendArray.forEach(item => {
                    series.push({
                        name: item,
                        type: 'line',
                        smooth: true,
                        radius: '95%',
                        max: 100
                    })
                })
                option.series = series;
                return option;
            },
            changeOption: function (charts, legendArray, dataArr, promptData, xAxisArray) {

                let seriesData = [];
                dataArr.forEach((item, index) => {
                    seriesData.push(
                        {
                            name: legendArray[index],
                            data: item
                        }
                    )
                })
                charts.setOption({
                    tooltip: {
                        trigger: 'axis',
                        confine: true,
                        formatter: function (params) {
                            var endTime = dateTimeList;
                            var endTimeValue = moment(endTime).format("YYYY-MM-DD HH:mm:ss");
                            var lastTime = xAxisArray[(xAxisArray.length - 1)];
                            var timeData1, timeData2 = '';
                            var timeDiv = '<div></div>';
                            params.forEach(item => {
                                var name = item.name.replace(/\//g, "-");
                                var d1 = Date.parse(new Date(item.name));
                                var d2 = d1 + (60 * 60 * 24 * 1000);
                                var d3 = moment(d2).format("YYYY-MM-DD");
                                if (item.name != lastTime) {
                                    timeData1 = '从： ' + name + ' ' + queryTimeValue + ' 至：' + d3 + ' ' + queryTimeValue
                                } else {
                                    timeData1 = '从： ' + name + ' ' + queryTimeValue + ' 至：' + endTimeValue
                                }
                                // if (params[1] == null || params[1] == undefined || params[1] == '') {
                                timeData2 += '<br> <span style="display:inline-block;width:9px;height:9px;border-radius: 50%"></span> ' + item.seriesName + '：' + item.value
                                // }
                            })
                            if (params.length <= 1) {
                                str = timeData1 + timeData2
                            } else {
                                str = '<div style="height: 300px;overflow: auto;">' + timeData1 + timeData2 + '</div>'
                            }
                            return str;
                        }
                    },

                    series: seriesData
                })
            },
            setRateArr: function (arr1, arr2) {
                $.each(arr1, function (i, el) {
                    arr2.push(el.alarmRate);
                })
            },
            initMonitorType: function () {
                OPAL.ui.getComboMultipleSelect('monitorType', monitorTypeUrl, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#monitorType").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    var roots = treeView.tree('getRoots');
                    for (var i = 0; i < roots.length; i++) {
                        treeView.tree('check', roots[i].target);
                    }
                });
            },
            /**
             * 获得固定的时间点
             */
            getQueryTime: function () {
                $.ajax({
                    url: getQueryTimeUrl,
                    async: false,
                    data: '',
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        queryTimeValue = res[0].value;
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 获得班组是否开启
             */
            getisFilterWorkTeam: function (unitIds) {
                $.ajax({
                    url: isFilterWorkTeam,
                    async: false,
                    data: {unitId: unitIds},
                    type: 'post',
                    success: function (result) {
                        if (result == true) {
                            $("#workTeamIds").prop('disabled', false);
                            page.logic.initWorkTeam();
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $('#workTeamIds').html("");
                            $("#workTeamIds").prop('disabled', true);
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 点击装置展示下方曲线图
             */
            selectUnit: function (unitId) {
                if (unitId) {
                    clickUnitId = [unitId];
                } else {
                    clickUnitId = unitTableArr
                }
                page.logic.initOpetateTable();
                //确认及时率和处置及时率
                page.logic.initRespondTable();
                page.logic.initHandleTable();
                page.logic.initUnitTabs(clickUnitId);

            },
            //合计
            selectUnittall: function (unitId) {
                if (unitId) {
                    clickUnitId = [unitId];
                } else {
                    clickUnitId = unitTableArr

                }//确认及时率和处置及时率
                page.logic.initRespondTable();
                page.logic.initHandleTable();
                page.logic.initOpetateTable();
                page.logic.initUnitTabstall(clickUnitId);

            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function (node, checked) {
                        var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');

                        if (unitIds != undefined && unitIds.length == 1) {
                            $("#workTeamIds").prop('disabled', false);
                            page.logic.initWorkTeam();
                            $('.textbox,.combo').css('background-color', '');
                        }
                        if (unitIds != undefined && unitIds.length > 1) {
                            page.logic.getisFilterWorkTeam(unitIds);
                        }
                        if (unitIds != undefined && unitIds.length < 1) {
                            $('#workTeamIds').html("");
                            $("#workTeamIds").prop('disabled', true);
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    }
                }, false);
                //$('#floodTable').bootstrapTable('hideColumn', 'SellerName');
            },
            /**
             * 初始化班组选择
             */
            initWorkTeam: function () {
                OPAL.util.getSearchTime({
                    startTime: $("#startTime").val(),
                    endTime: $("#endTime").val()
                }, function (data) {
                    var startTime = OPAL.util.dateFormat(OPAL.util.strToDate(data["startDate"]), "yyyy-MM-dd HH:mm:ss");
                    var endTime = OPAL.util.dateFormat(OPAL.util.strToDate(data["endDate"]), "yyyy-MM-dd HH:mm:ss");
                    var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                    if ($("#startTime").val() == '' || $("#endTime").val() == '') return;
                    OPAL.ui.getCombobox("workTeamIds", workTeamUrl, {
                        keyField: "workTeamId",
                        valueField: "workTeamSName",
                        selectFirstRecord: true,
                        mapManyValues: true, //是否一条记录匹配多个隐藏值
                        mapManyDataFieldName: 'workTeamIdList',
                        data: {
                            "startTime": startTime,
                            "endTime": endTime,
                            "unitId": unitIds[0],
                        }
                    }, null);

                });

            },
            getShowTime: function () {
                $.ajax({
                    url: getCurrentTimeUrl,
                    // async: true,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        getStartTime = dataArr[0].value;
                        getEndTime = dataArr[1].value;
                        //设置时间插件
                        page.logic.initTime();
                        document.getElementById("btnSearch").click();
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 设置日期插件
             */
            initTime: function () {
                var myDate = new Date();
                var start = laydate.render({
                    elem: '#startTime', //指定元素
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd HH:mm:ss', //日期格式
                    value: getStartTime,
                    max: getEndTime, //最大日期
                    done: function (value, date) {
                        page.logic.initWorkTeam();
                    },
                });
                var end = laydate.render({
                    elem: '#endTime',
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd HH:mm:ss',
                    value: getEndTime,
                    max: getEndTime,
                    done: function (value, date) {
                        page.logic.initWorkTeam();
                    },
                });
                $('#startTime').attr('maxDate', getEndTime)
                $('#endTime').attr('maxDate', getEndTime)
            },
            /**
             * 报警确认及时率表格
             */
            initRespondTable: function () {
                var options = {
                    detailView: true,
                    striped: true, //是否显示行间隔色
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'prdtCellName',
                        title: '生产单元',
                        align: 'left',
                    }, {
                        field: 'alarmPointTag',
                        title: '位号',
                        align: 'left',
                    }, {
                        field: 'des',
                        title: '参数描述',
                        align: 'left',
                        width: '500px',
                        formatter : function (value,row,index) {
                            var span=document.createElement('span');
                            span.setAttribute('title',value);
                            span.innerText = value;
                            return span.outerHTML;
                        }
                    }, {
                        field: 'alarmFlagName',
                        title: '报警标识',
                        align: 'center',
                    }, {
                        field: 'monitorTypeShow',
                        title: '专业',
                        align: 'center',
                        width: '90px'
                    }, {
                        field: 'priority',
                        title: "优先级",
                        align: 'center',
                        width: '90px',
                    }, {
                        field: 'measUnitName',
                        title: '计量单位',
                        align: 'center',
                    }, {
                        field: 'alarmNumber',
                        title: '未及时确认次数',
                        align: 'center',
                    }],
                    onExpandRow: function (index, row, $detail) {
                        page.logic.initRespondSubTable(index, row, $detail);
                    }
                };
                let dataArr = [];
                let param = {
                    startTime: page.data.param.startTime,
                    endTime: page.data.param.endTime,
                    unitId: clickUnitId,
                };

                if (clickUnitId) {
                    var respondData = [];
                    if (clickUnitId.length == 1) {
                        $.each(alarmRateData, function (i, el) {
                            if (el.unitid == clickUnitId[0]) {
                                if (el.alarmTimelyResponseRateDetail) {
                                    var data = JSON.parse(el.alarmTimelyResponseRateDetail)
                                    respondData = (data);
                                    return;
                                }

                            }
                        })
                    } else {
                        $.each(alarmRateData, function (i, el) {
                            if (el.alarmTimelyResponseRateDetail) {
                                $.each(JSON.parse(el.alarmTimelyResponseRateDetail), function (i, el) {
                                    respondData.push(el)
                                })
                            }

                        })
                    }
                    $('#RespondTable').bootstrapTable(options);
                    $('#RespondTable').bootstrapTable('refreshOptions', options);
                    $("#RespondTable").bootstrapTable("load", respondData);
                }
            },
            /**
             * 报警确认及时率二级列表
             */
            initRespondSubTable: function (index, row, $detail) {
                var subId = 'sub_restable' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                let options = {
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        align: 'center',
                        width: '80'
                    }, {
                        field: 'alarmTime',
                        title: '报警时间',
                        align: 'center',
                    }, {
                        field: 'timeLen',
                        title: '时长(秒)',
                        align: 'right',
                    }, {
                        field: 'responseTime',
                        title: '确认时间',
                        align: 'center',
                    }],
                };

                $('#' + subId).bootstrapTable(options);
                // let arr = JSON.parse(row.details);
                $('#' + subId).bootstrapTable("load", row.details);
            },
            /**
             * 报警确认及时率导出
             */
            respondExport: function () {
                var respondExportParam = {};
                $.extend(respondExportParam, page.data.param);
                respondExportParam.titles = JSON.stringify(new Array(
                    {
                        'key': 'prdtCellName',
                        'value': '生产单元'
                    }, {
                        'key': 'alarmPointTag',
                        'value': '位号'
                    }, {
                        'key': 'des',
                        'value': '参数描述'
                    }, {
                        'key': 'alarmFlagName',
                        'value': '报警标识'
                    }, {
                        'key': 'priority',
                        'value': "优先级"
                    }, {
                        'key': 'measUnitName',
                        'value': '计量单位'
                    }, {
                        'key': 'alarmTime',
                        'value': '报警时间'
                    }, {
                        'key': 'timeLen',
                        'value': '时长(秒)'
                    }, {
                        'key': 'responseTime',
                        'value': '确认时间'
                    }
                ));
                respondExportParam.clickUnitId=clickUnitId;
                var url = respondExportUrl + "?" + $.param(respondExportParam);
                window.location = url;
            },
            /**
             * 报警处置及时率表格
             */
            initHandleTable: function () {
                var options = {
                    detailView: true,
                    striped: true, //是否显示行间隔色
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    // height: 350,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'prdtCellName',
                        title: '生产单元',
                        align: 'left',
                    }, {
                        field: 'alarmPointTag',
                        title: '位号',
                        align: 'left',
                    }, {
                        field: 'des',
                        title: '参数描述',
                        align: 'left',
                        width: '500px',
                        formatter : function (value,row,index) {
                            var span=document.createElement('span');
                            span.setAttribute('title',value);
                            span.innerText = value;
                            return span.outerHTML;
                        }
                    }, {
                        field: 'alarmFlagName',
                        title: '报警标识',
                        align: 'center',
                    }, {
                        field: 'monitorTypeShow',
                        title: '专业',
                        align: 'center',
                        width: '90px'
                    }, {
                        field: 'priority',
                        title: "优先级",
                        align: 'center',
                        width: '90px'
                    }, {
                        field: 'measUnitName',
                        title: '计量单位',
                        align: 'center',
                    }, {
                        field: 'alarmNumber',
                        title: '未及时处置次数',
                        align: 'center',
                    }],
                    onExpandRow: function (index, row, $detail) {
                        page.logic.initHandleSubTable(index, row, $detail);
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#HandleTable').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                };
                let dataArr = [];
                let param = {
                    startTime: page.data.param.startTime,
                    endTime: page.data.param.endTime,
                    unitId: clickUnitId,
                };
                if (clickUnitId) {
                    var tableData = [];
                    if (clickUnitId.length == 1) {
                        $.each(alarmRateData, function (i, el) {
                            if (el.unitid == clickUnitId[0]) {
                                if (el.alarmTimelyDisposalRateDetail) {
                                    tableData = (JSON.parse(el.alarmTimelyDisposalRateDetail));
                                }

                            }
                        })
                    } else {
                        $.each(alarmRateData, function (i, el) {
                            if (el.alarmTimelyDisposalRateDetail) {
                                $.each(JSON.parse(el.alarmTimelyDisposalRateDetail), function (i, el) {
                                    tableData.push(el)
                                })
                            }

                        })
                    }
                    $('#HandleTable').bootstrapTable(options);
                    $('#HandleTable').bootstrapTable('refreshOptions', options);
                    $("#HandleTable").bootstrapTable("load", tableData);
                } else {
                    $('#HandleTable').bootstrapTable(options);
                    $('#HandleTable').bootstrapTable('refreshOptions', options);
                    $("#HandleTable").bootstrapTable("load", dataArr);
                }
            },
            /**
             * 报警处置及时率二级列表
             */
            initHandleSubTable: function (index, row, $detail) {
                var subId = 'sub_handletable' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                let options = {
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        align: 'center',
                        width: '80'
                    }, {
                        field: 'alarmTime',
                        title: '报警时间',
                        align: 'center',
                    }, {
                        field: 'timeLen',
                        title: '时长(分钟)',
                        align: 'right',
                    }, {
                        field: 'recoveryTime',
                        title: '处置时间',
                        align: 'center',
                    }],
                };
                $('#' + subId).bootstrapTable(options);
                // let arr = JSON.parse(row.details);
                $('#' + subId).bootstrapTable("load", row.details);
            },
            /**
             * 报警处置及时率导出
             */
            handleExport: function () {
                var handleExportParam = {};
                $.extend(handleExportParam, page.data.param);
                handleExportParam.titles = JSON.stringify(new Array(
                    {
                        'key': 'prdtCellName',
                        'value': '生产单元'
                    }, {
                        'key': 'alarmPointTag',
                        'value': '位号'
                    }, {
                        'key': 'des',
                        'value': '参数描述'
                    }, {
                        'key': 'alarmFlagName',
                        'value': '报警标识'
                    }, {
                        'key': 'priority',
                        'value': "优先级"
                    }, {
                        'key': 'measUnitName',
                        'value': '计量单位'
                    }, {
                        'key': 'alarmTime',
                        'value': '报警时间'
                    }, {
                        'key': 'timeLen',
                        'value': '时长(分钟)'
                    }, {
                        'key': 'recoveryTime',
                        'value': '处置时间'
                    }
                ));
                handleExportParam.clickUnitId=clickUnitId;
                var url = handleExportUrl + "?" + $.param(handleExportParam);
                window.location = url;
            },
            paramsMatter:function (value,row,index) {
                var span=document.createElement('span');
                span.setAttribute('title',row.description);
                span.innerHTML = '<a href="/teams/data_structure_detail/' + row.id + '/" target="_blank">' + row.title + '</a>';
                return span.outerHTML
            },
            /**
             * 初始化24小时持续报警数表格
             */
            initOpetateTable: function () {
                var options = {
                    detailView: true,
                    striped: true, //是否显示行间隔色
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    // height: 350,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'prdtCellName',
                        title: '生产单元',
                        align: 'left',
                    }, {
                        field: 'alarmPointTag',
                        title: '位号',
                        align: 'left',
                    }, {
                        field: 'des',
                        title: '参数描述',
                        align: 'left',
                        width: '500px',
                        formatter : function (value,row,index) {
                            var span=document.createElement('span');
                            span.setAttribute('title',value);
                            span.innerHTML = value;
                            return span.outerHTML;
                        }
                    }, {
                        field: 'alarmFlagName',
                        title: '报警标识',
                        align: 'center',
                    }, {
                        field: 'priority',
                        title: "优先级",
                        align: 'center',
                        width: '90px'
                    }, {
                        field: 'measUnitName',
                        title: '计量单位',
                        align: 'center',
                    }, {
                        field: 'alarmNumber',
                        title: '持续报警次数',
                        align: 'center',
                    }],
                    onExpandRow: function (index, row, $detail) {
                        page.logic.initCausalSubTable(index, row, $detail);
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#MostAlarmOperateTable').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                };
                let dataArr = [];
                let param = {
                    startTime: page.data.param.startTime,
                    endTime: page.data.param.endTime,
                    unitId: clickUnitId,
                };
                if (clickUnitId) {
                    var table24Data = [];
                    if (clickUnitId.length == 1) {
                        $.each(alarmRateData, function (i, el) {
                            if (el.unitid == clickUnitId[0]) {
                                if (el.alarmAmountDetail) {
                                    table24Data = (JSON.parse(el.alarmAmountDetail));
                                }

                            }
                        })
                    } else {
                        $.each(alarmRateData, function (i, el) {
                            if (el.alarmAmountDetail) {
                                $.each(JSON.parse(el.alarmAmountDetail), function (i, el) {
                                    table24Data.push(el)
                                })
                            }
                        })
                    }
                    $('#MostAlarmOperateTable').bootstrapTable(options);
                    $('#MostAlarmOperateTable').bootstrapTable('refreshOptions', options);
                    $("#MostAlarmOperateTable").bootstrapTable("load", table24Data);
                } else {
                    $('#MostAlarmOperateTable').bootstrapTable(options);
                    $('#MostAlarmOperateTable').bootstrapTable('refreshOptions', options);
                    $("#MostAlarmOperateTable").bootstrapTable("load", dataArr);
                }
            },
            /**
             * 初始化24小时二级列表
             */
            initCausalSubTable: function (index, row, $detail) {
                var subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                let options = {
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        align: 'center',
                        width: '80'
                    }, {
                        field: 'alarmTime',
                        title: '报警时间',
                        align: 'center',
                    }, {
                        field: 'timeLen',
                        title: '时长(小时)',
                        align: 'right',
                    }, {
                        field: 'recoveryTime',
                        title: '恢复时间',
                        align: 'center',
                    }],
                };
                $('#' + subId).bootstrapTable(options);
                // let arr = JSON.parse(row.details);
                $('#' + subId).bootstrapTable("load", row.details);
            },

            alarmNumberExport: function () {
                var alarmNumberExportParam = {};
                $.extend(alarmNumberExportParam, page.data.param);
                alarmNumberExportParam.titles = JSON.stringify(new Array(
                    {
                        'key': 'prdtCellName',
                        'value': '生产单元'
                    }, {
                        'key': 'alarmPointTag',
                        'value': '位号'
                    }, {
                        'key': 'des',
                        'value': '参数描述'
                    }, {
                        'key': 'alarmFlagName',
                        'value': '报警标识'
                    }, {
                        'key': 'priority',
                        'value': "优先级"
                    }, {
                        'key': 'measUnitName',
                        'value': '计量单位'
                    }, {
                        'key': 'alarmTime',
                        'value': '报警时间'
                    }, {
                        'key': 'timeLen',
                        'value': '时长(小时)'
                    }, {
                        'key': 'recoveryTime',
                        'value': '恢复时间'
                    }
                ));
                alarmNumberExportParam.clickUnitId=clickUnitId;
                var url = alarmNumberExportUrl + "?" + $.param(alarmNumberExportParam);
                window.location = url;
            }
        }
    };
    page.init();
    window.page = page;
})



