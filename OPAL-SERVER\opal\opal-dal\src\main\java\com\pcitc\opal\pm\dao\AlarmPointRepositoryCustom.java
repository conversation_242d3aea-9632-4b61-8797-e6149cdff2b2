package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.dao.imp.AlarmPointLimitDTO;
import com.pcitc.opal.pm.dao.imp.ModelDTO;
import com.pcitc.opal.pm.pojo.AlarmPoint;

import java.util.List;
import java.util.Map;

/*
 * AlarmPoint实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmPointRepositoryCustom
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：AlarmPoint实体的Repository的JPA自定义接口 
 */
public interface AlarmPointRepositoryCustom {

	/**
	 * 批量插入报警点数据
	 *
	 * <AUTHOR> 2017-11-22
	 * @param list 报警点实体集合
	 */
	void batchInsert(List<AlarmPoint> list);

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointEntity 报警点实体
	 * @return CommonResult 消息结果类
	 */
	CommonResult addAlarmPoint(AlarmPoint alarmPointEntity);
	
	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointIds 报警点ID集合
	 * @return CommonResult 消息结果类
	 */
	CommonResult deleteAlarmPoint(Long[] alarmPointIds);
	
	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointEntity 报警点实体
	 * @return CommonResult 消息结果类
	 */
	CommonResult updateAlarmPoint(AlarmPoint alarmPointEntity);
	
	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointId  报警点ID
	 * @return AlarmPoint 报警点实体
	 */
	AlarmPoint getSingleAlarmPoint(Long alarmPointId);

	/**
	 * 获取单条数据
	 *
	  * <AUTHOR> 2018-04-23
	 * @param alarmPointTag  报警点位号
	 * @return AlarmPoint 报警点实体
	 */
	AlarmPoint getSingleAlarmPointByTag(String alarmPointTag);

	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointIds 报警点ID集合
	 * @return List<AlarmPoint> 报警点实体集合
	 */
	List<AlarmPoint> getAlarmPoint(Long[] alarmPointIds);

	/**
	 * 获取所有数据
	 *
	  * <AUTHOR> 2017-10-11
	 * @return List<AlarmPoint> 报警点实体集合
	 */
	List<AlarmPointLimitDTO> getAlarmPointLimit();

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param unitCodes 装置编码
	 * @param prdtCellIds 生产单元id
	 * @param tag 位号
	 * @param typeId 报警点类型id
	 * @param inUse 是否启用
	 * @param instrmtPriority 仪表优先级
	 * @param page 翻页实现类
	 * @return PaginationBean<AlarmPoint> 翻页对象
	 */
	PaginationBean<AlarmPoint> getAlarmPoint(String[] unitCodes, Long[] prdtCellIds, String tag, Long typeId, Integer inUse, Integer instrmtPriority, Pagination page);
	
	/**
	 * 校验数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointEntity 报警点实体
	 * @return CommonResult 消息结果类
	 */
	CommonResult alarmPointValidation(AlarmPoint alarmPointEntity);

	/**
	 * 导入校验
	 *
	 * <AUTHOR> 2018-08-15
	 *
	 * @param alarmPointEntity 报警点实体
	 * @return 消息结果类
	 */
	 CommonResult alarmPointImportValidation(AlarmPoint alarmPointEntity);

	/**
	 * 判断生产单元在报警点中是否使用
	 *
	 * <AUTHOR> 2017-11-30
	 * @param prdtCellId 报警点实体
	 * @return CommonResult 消息结果类
	 */
	Long getPrdtCellIsUseInAlarmPoint(Long prdtCellId);
	
	/**
	 * 获取报警点
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param unitCode 装置编码
	 * @param prdtCellIds 生产单元id
	 * @param tag 主位号
	 * @param apIdList 报警点ID集合
	 * @param page 分页对象
	 * @return PaginationBean<AlarmPoint> 返回AlarmPoint实体类集合
	 */
	PaginationBean<AlarmPoint> getRelevantTagConfigDtlAdd(String unitCode, Long prdtCellIds, String tag, List<Long> apIdList, Pagination page);

	PaginationBean<AlarmPoint> getAlarmMsgConfig(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inSendMsg, Pagination page);

	List<AlarmPoint> getAlarmMsgConfigByAlarmPointIds(Long[] alarmPointIds);

	List<AlarmPoint> findAlarmPonitListByInfo(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inSendMsg);

	/**
	 * 根据报警点id更新是否发送报警短信字段
	 *
	 * @param alarmPointId 报警点id
	 * @param inSendMsg    否发送报警短信
	 * @return 受影响的行数
	 */
	Integer updateInSendMsgByAlarmPointId(Long[] alarmPointId, Integer inSendMsg);


	List<ModelDTO> getModel(String tag);
}
