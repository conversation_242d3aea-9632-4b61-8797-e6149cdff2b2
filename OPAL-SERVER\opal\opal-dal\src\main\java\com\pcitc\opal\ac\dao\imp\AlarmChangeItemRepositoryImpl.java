package com.pcitc.opal.ac.dao.imp;

import com.pcitc.opal.ac.dao.AlarmChangeItemRepositoryCustom;
import com.pcitc.opal.ac.pojo.AlarmChangeItem;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.dao.BaseRepository;

import javax.persistence.TypedQuery;
import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * AlarmChangeItem实体的Repository的JPA接口实现
 * 模块编号： pcitc_opal_dal_class_AlarmChangeItemRepositoryImpl
 * 作       者：xuelei.wang
 * 创建时间：2018/1/19
 * 修改编号：1
 * 描       述：AlarmChangeItem实体的Repository的JPA接口实现
 */
public class AlarmChangeItemRepositoryImpl extends BaseRepository<AlarmChangeItem, Long> implements AlarmChangeItemRepositoryCustom {

    /**
     * 新增报警变更事项数据
     *
     * @param alarmChangeItem 报警变更事项数据
     * @return 返回结果信息类
     * <AUTHOR> 2018-01-23
     */
    public CommonResult addAlarmChangeItem(AlarmChangeItem alarmChangeItem){
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(alarmChangeItem);
            commonResult.setResult(alarmChangeItem);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

	/**
	 * 删除报警变更事项实体
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangeItemIds 报警变更事项Id数组
	 * @return 返回结果信息类
	 */
	@Override
	@Transactional
	public CommonResult deleteAlarmChangeItem(Long[] alarmChangeItemIds) {
		// 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            String hql = " from AlarmChangeItem t where t.itemId in (:alarmChangeItemIds)";
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> alarmChangeItemIdsList = Arrays.asList(alarmChangeItemIds);
            paramList.put("alarmChangeItemIds", alarmChangeItemIdsList);

            TypedQuery<AlarmChangeItem> query = getEntityManager().createQuery(hql, AlarmChangeItem.class);
            this.setParameterList(query, paramList);
            List<AlarmChangeItem> alarmChangeItemList = query.getResultList();
            alarmChangeItemList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 删除出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
	}
    /**
     * 删除“报警变更方案明细ID”对应的<报警变更事项>记录
     *
     * <AUTHOR> 2018-01-23
     * @param planDetailId 报警变更明细Id
     * @return 返回结果信息类
     */
     public CommonResult deleteAlarmChangeItemByPDId(Long planDetailId){
         // 初始化消息结果类
         CommonResult commonResult = new CommonResult();
         try {
             StringBuilder hql = new StringBuilder(" from AlarmChangeItem t where t.planDetailId =:planDetailId ");
             Map<String, Object> paramList = new HashMap<String, Object>();
             paramList.put("planDetailId", planDetailId);

             TypedQuery<AlarmChangeItem> query = getEntityManager().createQuery(hql.toString(), AlarmChangeItem.class);
             this.setParameterList(query, paramList);
             List<AlarmChangeItem> alarmChangeItemList = query.getResultList();
             alarmChangeItemList.forEach(x -> {
                 this.getEntityManager().remove(x);
             });

             commonResult.setIsSuccess(true);
             commonResult.setMessage("删除成功！");
         } catch (Exception ex) {
             // 删除出现异常，绑定异常信息在消息结果对象
             commonResult.setIsSuccess(false);
             commonResult.setMessage(ex.getMessage());
         }
         // 返回消息结果对象
         return commonResult;
    }

    /**
     * 根据PlanDetailId获取报警变更事项数据
     *
     * <AUTHOR> 2018-01-22
     * @param planId         报警变更方案ID
     * @param planDetailId  报警变更方案明细ID
     * @return AlarmChangePlanDetail 报警变更事项
     */
    @Override
    public List<AlarmChangeItem> getAlarmChangeItemByPlanDetail(Long planId, Long planDetailId) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder(" from AlarmChangeItem t where t.planId =:planId and t.planDetailId = :planDetailId");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("planId", planId);
            paramList.put("planDetailId", planDetailId);
            TypedQuery<AlarmChangeItem> query = getEntityManager().createQuery(hql.toString(), AlarmChangeItem.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 根据报警变更方案ID和变更方案详情ID获取报警变更事项列表
     *
     * @param planId       变更方案ID
     * @param planDetailId 变更详情ID
     * @return 报警变更事项列表
     * <AUTHOR> 2018-01-22
     */
    @Override
    public List<AlarmChangeItem> getAlarmChangeItemByPlanDetailId(Long planId, Long planDetailId) {
        try {
            StringBuilder hql = new StringBuilder();
            hql.append("from AlarmChangeItem t join fetch t.changeItemType where 1=1 ");
            Map<String, Object> paramList = new HashMap<String, Object>();
            if(planId!=null){
                hql.append("and t.planId=:planId ");
                paramList.put("planId",planId);
            }
            if(planDetailId!=null){
                hql.append("and t.planDetailId=:planDetailId ");
                paramList.put("planDetailId",planDetailId);
            }
            TypedQuery<AlarmChangeItem> query = getEntityManager().createQuery(hql.toString(), AlarmChangeItem.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }
    /**
     * 根据报警变更方案详情ID获取报警变更事项列表
     *
     * @param planDetailIds 变更详情ID集合
     * @return 报警变更事项列表
     * <AUTHOR> 2018-01-22
     */
    @Override
    public List<AlarmChangeItem> getAlarmChangeItemByPlanDetailIds(Long[] planDetailIds) {
        try {
            StringBuilder hql = new StringBuilder();
            hql.append("from AlarmChangeItem t join fetch t.changeItemType where 1=1 ");
            Map<String, Object> paramList = new HashMap<String, Object>();
            if(planDetailIds!=null&&planDetailIds.length!=0){
                hql.append("and t.planDetailId in(:planDetailIds)");
                paramList.put("planDetailIds",Arrays.asList(planDetailIds));
            }
            TypedQuery<AlarmChangeItem> query = getEntityManager().createQuery(hql.toString(), AlarmChangeItem.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }
    
    /**
	 * 通过报警变更方案Id获取多条数据
	 * 
	 * <AUTHOR> 2018-01-19
	 * @param alarmChangePlanIds 报警变更方案Id数组
	 * @return 报警变更事项实体集合
	 */
	@Override
	public List<AlarmChangeItem> getAlarmChangeItemByPlanIds(Long[] alarmChangePlanIds) {
		try {
            // 查询字符串
            String hql = "from AlarmChangeItem t ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (alarmChangePlanIds.length > 0) {
                hql += " where t.planId in (:alarmChangePlanIds)";
                List<Long> alarmChangePlanIdsList = Arrays.asList(alarmChangePlanIds);
                paramList.put("alarmChangePlanIds", alarmChangePlanIdsList);
            }
            TypedQuery<AlarmChangeItem> query = getEntityManager().createQuery(hql, AlarmChangeItem.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
	}
}
