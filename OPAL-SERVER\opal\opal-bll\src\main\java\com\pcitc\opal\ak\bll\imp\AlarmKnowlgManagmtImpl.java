package com.pcitc.opal.ak.bll.imp;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.AlarmFlagRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.ad.pojo.AlarmFlag;
import com.pcitc.opal.ak.bll.AlarmKnowlgManagmtService;
import com.pcitc.opal.ak.bll.entity.AlarmKnowlgExcyDetailEntity;
import com.pcitc.opal.ak.bll.entity.AlarmKnowlgManagmtEntity;
import com.pcitc.opal.ak.dao.AlarmKnowlgManagmtRepository;
import com.pcitc.opal.ak.pojo.AlarmKnowlgManagmt;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.dao.AlarmPointRepository;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.Date;
import java.util.List;

/*
 * 报警制度管理业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmStdManagmtImpl
 * 作	者：kun.zhao
 * 创建时间：2018/02/28
 * 修改编号：1
 * 描    述：报警制度管理业务逻辑层实现类
 */
@Service
public class AlarmKnowlgManagmtImpl implements AlarmKnowlgManagmtService {

    @Autowired
    private AlarmKnowlgManagmtRepository alarmKnowlgManagmtRepository;
    @Autowired
    private AlarmPointRepository alarmPointRepository;
    @Autowired
    private AlarmFlagRepository alarmFlagRepository;
    @Autowired
    private AlarmEventRepository alarmEventRepository;
    @Autowired
    private BasicDataService basicDataService;

    /**
     * 加载报警事件数据
     *
     * <AUTHOR> 2018-03-09
     * @param unitCodes		    装置ID数组
     * @param prdtCellIds	    生产单元ID数组
     * @param alarmPointTag	报警点位号
     * @param alarmFlagId	    报警标识ID
     * @param startTime		发生时间范围起始
     * @param endTime		    发生时间范围结束
     * @param page			    分页参数
     * @return 报警事件数据
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<AlarmEventEntity> getAlarmEvent(String[] unitCodes, Long[] prdtCellIds, String alarmPointTag,
                                                          Long alarmFlagId, Date startTime, Date endTime, Pagination page) throws Exception{
        List<UnitEntity> units = null;
        if(unitCodes == null) {
            units = basicDataService.getUnitList(true);
            unitCodes = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }

        PaginationBean<AlarmEvent> listAlarmEvent = alarmEventRepository.getAlarmEvent(unitCodes,prdtCellIds,alarmPointTag, alarmFlagId, startTime, endTime,  page);
        PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<AlarmEventEntity>(page,listAlarmEvent.getTotal());
        returnAlarmEvent.setPageList(ObjectConverter.listConverter(listAlarmEvent.getPageList(), AlarmEventEntity.class));

        if(units == null) {
            // 通过公共方法获取装置
            String[] filterUnitIds = listAlarmEvent.getPageList().stream().map(e -> e.getAlarmPoint().getPrdtCell().getUnitId()).distinct().toArray(String[]::new);
            units = basicDataService.getUnitListByIds(filterUnitIds, false);
        }
        // 映射字段
        for (int i = 0; i < returnAlarmEvent.getPageList().size(); i++) {
            AlarmEventEntity alarmEventEntity = returnAlarmEvent.getPageList().get(i);
            AlarmEvent alarmEvent = listAlarmEvent.getPageList().get(i);
            // 填充装置简称
            UnitEntity unit = units.stream().filter(u -> alarmEvent.getAlarmPoint().getPrdtCell().getUnitId().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
            alarmEventEntity.setUnitName(unit.getSname());
            // 填充生产单元简称
            alarmEventEntity.setPrdtCellName(alarmEvent.getAlarmPoint().getPrdtCell().getSname());
            // 填充报警点位号
            alarmEventEntity.setAlarmPointTag(alarmEvent.getAlarmPoint().getTag());
            // 填充报警标识名称
            alarmEventEntity.setAlarmFlagName(alarmEvent.getAlarmFlag().getName());
        }
        return returnAlarmEvent;
    }

    /**
     * 新增报警知识维护数据
     *
     * <AUTHOR> 2018-03-09
     * @param akmEntity 报警知识实体
     * @throws Exception
     * @return CommonResult 消息结果类
     */
    @Override
    public CommonResult addAlarmKnowlgManagmt(AlarmKnowlgManagmtEntity akmEntity) throws Exception{
        // 实体转换为持久层实体
        AlarmKnowlgManagmt alarmKnowlgManagmt = ObjectConverter.entityConverter(akmEntity, AlarmKnowlgManagmt.class);

        // 赋值  创建人、创建名称、创建时间
        CommonProperty commonProperty = new CommonProperty();
        alarmKnowlgManagmt.setCrtUserId(commonProperty.getUserId());
        alarmKnowlgManagmt.setCrtUserName (commonProperty.getUserName());
        alarmKnowlgManagmt.setCrtDate(commonProperty.getSystemDateTime());

        CommonResult commonResult = alarmKnowlgManagmtRepository.addAlarmKnowlgManagmt(alarmKnowlgManagmt);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 加载报警知识维护详情数据
     *
     * @param eventId       报警事件id
     * @param alarmPointId  报警点id
     * @param alarmFlagId   报警标识id
     * @return 报警知识维护数据
     * @throws Exception
     * <AUTHOR> 2018-03-09
     */
    @Override
     public AlarmKnowlgExcyDetailEntity getAlarmKnowlgExcyDetail(Long eventId, Long alarmPointId, Long alarmFlagId) throws Exception{
         AlarmKnowlgExcyDetailEntity akeDetailEntity = new AlarmKnowlgExcyDetailEntity();
         AlarmFlag alarmFlag = alarmFlagRepository.getSingleAlarmFlag(alarmFlagId);
         AlarmPoint alarmPoint = alarmPointRepository.getSingleAlarmPoint(alarmPointId);
         List<UnitEntity> units = basicDataService.getUnitListByIds(new String[]{alarmPoint.getPrdtCell().getUnitId()}, true);

        // 填充装置简称
         UnitEntity unit = units.get(0);
         akeDetailEntity.setUnitName(unit.getSname());
         akeDetailEntity.setPrdtCellSname(alarmPoint.getPrdtCell().getSname());
         akeDetailEntity.setTag(alarmPoint.getTag());
         akeDetailEntity.setAlarmFlagName(alarmFlag.getName());
         akeDetailEntity.setLocation(alarmPoint.getLocation());
         akeDetailEntity.setCraftRank(alarmPoint.getCraftRank());
         akeDetailEntity.setCraftRankName(akeDetailEntity.getCraftRankName());
         akeDetailEntity.setAlarmValue(getLimitValue(alarmPoint,alarmFlagId));
         akeDetailEntity.setUnitFlag(alarmPoint.getMeasUnit().getSign());
         akeDetailEntity.setLocation(alarmPoint.getLocation());

         // region 工艺卡片值、联锁值
         //工艺卡片值
         Double culv=alarmPoint.getCraftUpLimitValue();//工艺卡片上限值
         Double cdlv=alarmPoint.getCraftDownLimitValue();//工艺卡片下限值
         Integer culi=alarmPoint.getCraftUpLimitInclude();//工艺卡片上限值是否包含(1是；0否)
         Integer cdli=alarmPoint.getCraftDownLimitInclude();//工艺卡片下限值是否包含(1是；0否)
         if(culv!=null&&cdlv!=null){
             String culvStr = changeDouble(culv);
             String cdlvStr = changeDouble(cdlv);
             akeDetailEntity.setCraftLimitValue(cdlvStr+"~"+culvStr);
         }else if(culv!=null&&cdlv==null){
             if(culi!=null && culi.intValue()==1){
                 String culvStr = changeDouble(culv);
                 akeDetailEntity.setCraftLimitValue("≤"+culvStr);
             }else if(culi!=null && culi.intValue()==0){
                 String culvStr = changeDouble(culv);
                 akeDetailEntity.setCraftLimitValue("<"+culvStr);
             }
         }else if(culv==null&&cdlv!=null){
             if(cdli!=null && cdli.intValue()==1){
                 String cdlvStr = changeDouble(cdlv);
                 akeDetailEntity.setCraftLimitValue("≥"+cdlvStr);
             }else if(cdli!=null && cdli.intValue()==0){
                 String cdlvStr = changeDouble(cdlv);
                 akeDetailEntity.setCraftLimitValue(">"+cdlvStr);
             }
         }else if(culv==null&&cdlv==null){
             akeDetailEntity.setCraftLimitValue("");
         }

         //联锁值
         Double iulv=alarmPoint.getInterlockUpLimitValue();//联锁上限值
         Double idlv=alarmPoint.getInterlockDownLimitValue();//联锁下限值
         Integer iuli=alarmPoint.getInterlockUpLimitInclude();//联锁上限值是否包含(1是；0否)
         Integer idli=alarmPoint.getInterlockDownLimitInclude();//联锁下限值是否包含(1是；0否)
         if(iulv!=null&&idlv!=null){
             String iulvStr = changeDouble(iulv);
             String idlvStr = changeDouble(idlv);
             akeDetailEntity.setInterlockLimitValue(idlvStr+"~"+iulvStr);
         }else if(iulv!=null&&idlv==null){
             if(iuli.intValue()==1){
                 String iulvStr = changeDouble(iulv);
                 akeDetailEntity.setInterlockLimitValue("≤"+iulvStr);
             }else if(iuli.intValue()==0){
                 String iulvStr = changeDouble(iulv);
                 akeDetailEntity.setInterlockLimitValue("<"+iulvStr);
             }
         }else if(iulv==null&&idlv!=null){
             if(idli.intValue()==1){
                 String idlvStr = changeDouble(idlv);
                 akeDetailEntity.setInterlockLimitValue("≥"+idlvStr);
             }else if(idli.intValue()==0){
                 String idlvStr = changeDouble(idlv);
                 akeDetailEntity.setInterlockLimitValue(">"+idlvStr);
             }
         }else if(iulv==null&&idlv==null){
             akeDetailEntity.setInterlockLimitValue("");
         }
         // endregion

         return ObjectConverter.entityConverter(akeDetailEntity, AlarmKnowlgExcyDetailEntity.class);
     }

    /**
     * 加载报警知识维护主数据
     *
     * @param eventId       报警事件id
     * @param alarmPointId  报警点id
     * @param alarmFlagId   报警标识id
     * @param page   分页信息
     * @return 报警知识维护数据集合
     * @throws Exception
     * <AUTHOR> 2018-03-09
     */
    @Override
     public PaginationBean<AlarmKnowlgManagmtEntity> getAlarmKnowlgManagmt(Long eventId,Long alarmPointId,Long alarmFlagId, Pagination page)
             throws Exception {
        PaginationBean<AlarmKnowlgManagmt> akmList = alarmKnowlgManagmtRepository.getAlarmKnowlgManagmt(eventId, alarmPointId, alarmFlagId, page);
        PaginationBean<AlarmKnowlgManagmtEntity> akmEntityPage = new PaginationBean<AlarmKnowlgManagmtEntity>(page, akmList.getTotal());
        akmEntityPage.setPageList(ObjectConverter.listConverter(akmList.getPageList(), AlarmKnowlgManagmtEntity.class));
        akmEntityPage.getPageList().stream().forEach(x -> {
            x.setAlarmTime(x.getAlarmEvent().getAlarmTime());
            if (x.getEventId().equals(eventId)) {
                x.setIsRed(1);
            }else {
                x.setIsRed(0);
            }
        });

        return akmEntityPage;
    }

     // region 私有方法
    private String changeDouble(Double num){
        if((num+"").endsWith(".0")){
            return num.intValue()+"";
        }
        return num+"";
    }
    /**
     * 获取限值
     *
     * <AUTHOR> 2017-10-30
     * @param alarmPoint  报警点对象
     * @param alarmFlagId 报警标识
     * @return
     */
    private Double getLimitValue(AlarmPoint alarmPoint, Long alarmFlagId) {
        Double limitValue = 0d;
        switch (Integer.valueOf(alarmFlagId.toString())) {
            case 1:
                limitValue = alarmPoint.getAlarmPointHH();
                break;
            case 2:
                limitValue = alarmPoint.getAlarmPointHI();
                break;
            case 3:
                limitValue = alarmPoint.getAlarmPointLO();
                break;
            case 4:
                limitValue = alarmPoint.getAlarmPointLL();
                break;
        }
        return limitValue;
    }
    // endregion
}
