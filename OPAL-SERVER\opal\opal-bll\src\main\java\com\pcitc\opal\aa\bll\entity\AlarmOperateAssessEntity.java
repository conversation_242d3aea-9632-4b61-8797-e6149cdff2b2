package com.pcitc.opal.aa.bll.entity;

import java.util.Date;

/*
 * 报警操作评估实体
 * 模块编号：pcitc_opal_bll_class_AlarmOperateAssessEntity
 * 作  　者：xuelei.wang
 * 创建时间：2017-10-24
 * 修改编号：1
 * 描    述：报警操作评估实体
 */
public class AlarmOperateAssessEntity {
    public AlarmOperateAssessEntity() {

    }

    public AlarmOperateAssessEntity(String groupByTime, Long counts, String id) {
        this.id = id;
        this.counts = counts;
        this.groupByTime = groupByTime;
    }

    /**
     * 车间Id
     */
    private String workshopId;
    /**
     * ID
     */
    private String id;
    /**
     * 分组后的时间
     */
    private String groupByTime;
    /**
     * 名称
     */
    private String name;
    /**
     * 报警时间
     */
    private Date alartTime;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 数量
     */
    private Long counts;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getCounts() {
        return counts;
    }

    public void setCounts(Long counts) {
        this.counts = counts;
    }

    public String getGroupByTime() {
        return groupByTime;
    }

    public void setGroupByTime(String groupByTime) {
        this.groupByTime = groupByTime;
    }

    public Date getAlartTime() {
        return alartTime;
    }

    public void setAlartTime(Date alartTime) {
        this.alartTime = alartTime;
    }

    public String getWorkshopId() {
        return workshopId;
    }

    public void setWorkshopId(String workshopId) {
        this.workshopId = workshopId;
    }
}
