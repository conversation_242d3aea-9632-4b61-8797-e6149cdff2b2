package com.pcitc.opal.common.bll.vo;

import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;

/*
 * 区域实体
 * 模块编号：pcitc_wm_common_class_AreaVO
 * 作    者：xuelei.wang
 * 创建时间：2018/06/26
 * 修改编号：1
 * 描    述：装置实体
 */
public class AreaVO extends BaseResRep implements Serializable {
    /**
     * 装置编码
     */
    private String areaCode;
    /**
     * 装置名称
     */
    private String areaName;
    /**
     * 装置简称
     */
    private String areaAlias;
    /**
     * 所属组织机构编码
     */
    private String orgCode;
    /**
     * 所属组织机构名称
     */
    private String orgName;
    /**
     * 所属组织机构简称
     */
    private String orgAlias;
    /**
     * 区域类型编码
     */
    private String areaTypeCode;
    /**
     * 区域类型名称
     */
    private String areaTypeName;
    /**
     * 排序
     */
    private Integer sortNum;
    /**
     * 描述
     */
    private String des;
    /**
     * 是否启用
     */
    private Integer inUse;

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAreaAlias() {
        return areaAlias;
    }

    public void setAreaAlias(String areaAlias) {
        this.areaAlias = areaAlias;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgAlias() {
        return orgAlias;
    }

    public void setOrgAlias(String orgAlias) {
        this.orgAlias = orgAlias;
    }

    public String getAreaTypeCode() {
        return areaTypeCode;
    }

    public void setAreaTypeCode(String areaTypeCode) {
        this.areaTypeCode = areaTypeCode;
    }

    public String getAreaTypeName() {
        return areaTypeName;
    }

    public void setAreaTypeName(String areaTypeName) {
        this.areaTypeName = areaTypeName;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public Integer getInUse() {
        return inUse;
    }

    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }
}
