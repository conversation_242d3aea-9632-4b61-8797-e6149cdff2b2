package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.AlarmPointGroupDtlService;
import com.pcitc.opal.pm.bll.AlarmPointGroupService;
import com.pcitc.opal.pm.bll.entity.AlarmPointEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointGroupConfigEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointGroupDtlEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointGroupEntity;
import com.pcitc.opal.pm.dao.AlarmPointGroupDtlRepository;
import com.pcitc.opal.pm.dao.AlarmPointGroupRepository;
import com.pcitc.opal.pm.dao.imp.AlarmPointGroupConfig;
import com.pcitc.opal.pm.dao.imp.AlarmPointGroupDtlDTO;
import com.pcitc.opal.pm.pojo.AlarmPointGroup;
import com.pcitc.opal.pm.pojo.AlarmPointGroupDtl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.rmi.RemoteException;
import java.util.List;

/*
 * 报警点分组明细逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmPointGroupDtlImpl
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点分组明细逻辑层实现类
 */
@Service
@Component
public class AlarmPointGroupDtlImpl implements AlarmPointGroupDtlService {

    @Autowired
    AlarmPointGroupDtlRepository alarmPointGroupDtlRepository;

    @Override
    public List<AlarmPointGroupDtlDTO> getAlarmPointGroupDtls(Long alarmPointGroupId)  {
        List<AlarmPointGroupDtlDTO> alarmPointGroupConfigList = alarmPointGroupDtlRepository.getAlarmPointGroupDtls(alarmPointGroupId);
       return alarmPointGroupConfigList;
    }

    @Override
    public CommonResult deleteAlarmPointGroupDtl(List<Long> alarmPointGroupDtlIds) throws Exception {

        return alarmPointGroupDtlRepository.deleteAlarmPointGroupDtl(alarmPointGroupDtlIds);
    }

    @Override
    public CommonResult addAlarmPointGroupDtl(List<AlarmPointGroupDtlEntity> entityList)throws Exception {
        // 实体转换为持久层实体
        List<AlarmPointGroupDtl> alarmPointGroupDtlList = ObjectConverter.listConverter(entityList, AlarmPointGroupDtl.class);

        CommonProperty commonProperty = new CommonProperty();
        alarmPointGroupDtlList.forEach(t->{
            t.setCrtUserId(commonProperty.getUserId());
            try {
                t.setCrtUserName (commonProperty.getUserName());
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            t.setCrtDate(commonProperty.getSystemDateTime());
        });

        return alarmPointGroupDtlRepository.addAlarmPointGroupDtl(alarmPointGroupDtlList);
    }


}
