package com.pcitc.opal.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

@Component
public class DbConversion {

    private static DbConfig dbConfig;
    @Autowired
    public DbConversion(DbConfig dbConfig){
        DbConversion.dbConfig=dbConfig;
    }

    public static String DbDateTransformYmdhToStr(String field){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str= "to_char("+field+",'yyyy-MM-dd HH24')";
        }else{
            str ="date_format("+field+",'%Y-%m-%d %H')";
        }
        return str;
    }
    public static String DbDateSubTransformYmdToStr(String field,float h ,int hours){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str = "to_char("+field+"-"+h+",'yyyy-MM-dd')";
        }else{
            str ="DATE_FORMAT(F_OPAL_DATESUB("+field+","+hours+",hour),'%Y-%m-%d')";
        }
        return str;
    }
    public static String DbDateSubTransformYmToStr(String field,float h ,int hours){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str = "to_char("+field+"-"+h+",'yyyy-MM')";
        }else{
            str ="DATE_FORMAT(F_OPAL_DATESUB("+field+","+hours+",hour),'%Y-%m')";
        }
        return str;
    }
    public static String DbDateTransformYmdhmsToChar(String field){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str= "TO_CHAR( "+field+", 'YYYY-MM-DD HH24:MI:SS')";
        }else{
            str = "date_format( "+field+", '%Y-%m-%d %H:%i:%s')";
        }
        return str;
    }
    public static String DbDateTransformYmdhmsToDate(String field){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str= "to_date( "+field+", 'YYYY-MM-DD HH24:MI:SS')";
        }else{
            str = "str_to_date( "+field+", '%Y-%m-%d %H:%i:%s')";
        }
        return str;
    }
    public static String numtodsintervalYmdh(String field){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str = "TO_CHAR(" + field + " - NUMTODSINTERVAL(0, 'HOUR'), 'YYYY-MM-DD HH24')";
        }else{
            str ="DATE_FORMAT(subdate("+field+",interval 0 hour),'%Y-%m-%d %H')";
        }
        return str;
    }
    public static String numtodsintervalYmd(String field,int hours){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str = "TO_CHAR(" + field + " - NUMTODSINTERVAL(" + hours + ", 'HOUR'),'YYYY-MM-DD')";
        }else{
            str ="DATE_FORMAT(subdate("+field+",interval "+hours+" hour),'%Y-%m-%d')";
        }
        return str;
    }
    public static String numtodsintervalYm(String field,int hours){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str = "TO_CHAR(" + field + " - NUMTODSINTERVAL(" + hours + ", 'HOUR'), 'YYYY-MM')";
        }else{
            str ="DATE_FORMAT(subdate("+field+",interval "+hours+" hour),'%Y-%m')";
        }
        return str;
    }
    public static String numtodsintervalYmdWeekSql(String field, int hours, Date startTime){
        String str="";
        Calendar cal = Calendar.getInstance();
        cal.setTime(startTime);
        int w = cal.get(Calendar.DAY_OF_WEEK);
        if("oracle".equals(dbConfig.getDataBase())){
            str = "to_char(next_day(ae." + field + "-7- " + hours + "/24," + w + "),'YYYY-MM-DD')";
        }
        else{
            String week =DateHelper.strDate(startTime);
            str="date_format(date_sub(F_OPAL_NEXTDAY(date_sub(ae."+field+", interval 7 day),'"+week+"'), interval "+hours+" hour),'%Y-%m-%d')";
        }
        return str;
    }
    public static String numtodsintervalYmdWeekHql(String field, int hours, Date beginTime){
        String str="";
        Calendar cal = Calendar.getInstance();
        cal.setTime(beginTime);
        int w = cal.get(Calendar.DAY_OF_WEEK);
        float h=(float)hours/(float)24;
        if("oracle".equals(dbConfig.getDataBase())){
            str="to_char(next_day("+field+"-7-"+h+","+w+"),'YYYY-MM-DD')";
        }else{
            String week =DateHelper.strDate(beginTime);
            str="date_format(F_OPAL_DATESUB(F_OPAL_NEXTDAY(F_OPAL_DATESUB("+field+", 7 ,day),'"+week+"'), "+hours+" ,hour),'%Y-%m-%d')";
        }
        return str;
    }

    public static String dateFunction(){
        String str = "";
        if("oracle".equals(dbConfig.getDataBase())){
            str="to_date";
        }else{
            str="str_to_date";
        }
        return str;
    }
    public static String toCharFunction(){
        String str = "";
        if("oracle".equals(dbConfig.getDataBase())){
            str="to_char";
        }else{
            str="date_format";
        }
        return str;
    }
    public static String dateYmdFunction(){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str= "YYYY-MM-DD";
        }else{
            str= "%Y-%m-%d";
        }
        return str;
    }
    public static String dateYmdhmsFunction(){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str= "YYYY-MM-DD HH24:MI:SS";
        }else{
            str= "%Y-%m-%d %H:%i:%s";
        }
        return str;
    }
    public static String nvlFunction(){
        String str = "";
        if("oracle".equals(dbConfig.getDataBase())){
            str= "NVL";
        }else{
            str= "IFNULL";
        }
        return str;
    }
    public static String aliasName(){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str =" ";
        }else{
            str = " as tt";
        }
        return str;
    }
    public static String sysdateDatesub(int hour){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str = "sysdate - 6 / 24 ";
        }else{
            str = "date_sub(now(),interval 6 hour)";
        }
        return str;
    }

    public static String dateFieldSub(String dateField1,String dateField2){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str = dateField1 +"-"+ dateField2;
        }else{
            str = "TIMESTAMPDIFF(second,"+dateField2+","+dateField1+" )/60/60/24";
        }
        return str;
    }
    public static String toNumber(){
        String str="";
        if("oracle".equals(dbConfig.getDataBase())){
            str= "to_number";
        }else{

        }
        return str;
    }
//    public static String DbDateTransformYmdhmsToStr(String dateStr){
//        String str="";
//        if("oracle".equals(dbConfig.getDataBase())){
//        }else{
//        }
//        return str;
//    }
}
