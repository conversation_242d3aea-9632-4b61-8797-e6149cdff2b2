package com.pcitc.opal.ap.dao.imp;

import com.pcitc.opal.ap.dao.AlarmPushRuleRepositoryCustom;
import com.pcitc.opal.ap.dao.GroupRepositoryCustom;
import com.pcitc.opal.ap.pojo.AlarmPushRule;
import com.pcitc.opal.ap.pojo.Group;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.pojo.AlarmPointGroup;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/*
 * 报警知识管理实体的Repository的JPA接口实现
 * 模块编号： pcitc_opal_dal_class_AlarmKnowlgManagmtRepositoryImpl
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理实体的Repository的JPA接口实现
 */
public class AlarmPushRuleRepositoryImpl extends BaseRepository<AlarmPushRule, Long>
		implements AlarmPushRuleRepositoryCustom {


	@Override
	public PaginationBean<AlarmPushRule> getAlarmPushRulePage(String name,Integer companyId, Integer pushType, Pagination page) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("from AlarmPushRule t where 1=1 and t.inUse=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			//
			if (null != companyId){
				hql.append(" and t.companyId = :companyId ");
				paramList.put("companyId",companyId);
			}
			if (null != pushType){
				hql.append(" and t.pushType = :pushType ");
				paramList.put("pushType",pushType);
			}
			// 名称
			if (!StringUtils.isEmpty(name)) {
				hql.append(" and (t.name like :name escape '/') ");
				paramList.put("name", "%" + this.sqlLikeReplace(name) + "%");
			}
			hql.append(" order by t.mntDate desc,t.name asc");
			// 调用基类方法查询返回结果
			return this.findAll(page, hql.toString(), paramList);
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addAlarmPushRule(AlarmPushRule alarmPushRule) {
		// 初始化消息结果类
		CommonProperty commonProperty = new CommonProperty();
		alarmPushRule.setCompanyId(commonProperty.getCompanyId());
		CommonResult commonResult = new CommonResult();
		try {
			this.getEntityManager().persist(alarmPushRule);
			commonResult.setResult(alarmPushRule);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		return commonResult;
	}

	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteAlarmPushRule(Long[] ids) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = " from AlarmPushRule t " +
					"where t.companyId=:companyId " +
					"and t.alarmPushRuleId in (:ids)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("ids", Arrays.asList(ids));
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			TypedQuery<AlarmPushRule> query = getEntityManager().createQuery(hql, AlarmPushRule.class);
			this.setParameterList(query, paramList);
			List<AlarmPushRule> alarmPointList = query.getResultList();
			alarmPointList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult updateAlarmPushRule(AlarmPushRule alarmPushRule) {
		// 初始化消息结果类
		CommonProperty commonProperty = new CommonProperty();
		alarmPushRule.setCompanyId(commonProperty.getCompanyId());
		CommonResult commonResult = new CommonResult();
		try {
			getEntityManager().merge(alarmPushRule);
			commonResult.setResult(alarmPushRule);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("更新成功！");
		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	@Override
	public List<AlarmPushRule> findAllNameRule(Integer pushType ) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder(" from AlarmPushRule t where 1=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			if (pushType!=null) {
				hql.append(" and t.pushType = :pushType ");
				paramList.put("pushType", pushType);
			}
			Query query = getEntityManager().createQuery(hql.toString());
			this.setParameterList(query, paramList);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	public List<AlarmPushRule> getAlarmPushRule(Integer companyId) {
		String hql = "select new AlarmPushRule(apru.alarmSpeciality, aprd.alarmFlagId, aprd.alarmEndPushFlag, aprd.apRuleDetailId, aprd.startPushPeriod, apru.priority, aprd.cycleFlag, aprd.cyclePeriod, aprud.unitCode, aprd.groupId, apr.pushType) from AlarmPushRule apr " +
				" join FETCH AlarmPushRuleDetail aprd on apr.alarmPushRuleId = aprd.alarmPushRuleId " +
				" join FETCH AlarmPushRuleUnitRel apru on apr.alarmPushRuleId = apru.alarmPushRuleId " +
				" join FETCH AlarmPushRuleUnitRelDetail aprud on apru.apRuleUnitRelId = aprud.apRuleUnitRelId " +
				"where apr.companyId = :companyId " +
				"and apr.inUse = 1 and aprd.inUse = 1 and apru.inUse = 1 and aprud.inUse = 1 ";

		Query query = getEntityManager().createQuery(hql);

		query.setParameter("companyId", companyId);

		return query.getResultList();
	}

}
