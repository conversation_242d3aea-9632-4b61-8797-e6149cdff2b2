<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.ad.dao.AlarmEventDelApproveDAO">

    <select id="getAlarmEventDelApprove" resultType="com.pcitc.opal.ad.vo.AlarmEventDelApproveVO">
        SELECT
        a.alarm_event_del_approve_id as alarmEventDelApproveId,
        a.unit_code as unitCode,
        a.reason as reason,
        a.crt_date as crtDate,
        a.crt_user_id as crtUserId,
        a.crt_user_name as crtUserName,
        a.del_status as delStatus,
        a.apro_time as aProTime,
        a.apro_user_id as aProUserId,
        a.apro_user_name as aProUserName,
        b.event_type_id as eventTypeId,
        b.start_time as startTime,
        b.alarm_time as alarmTime,
        b.priority as priority,
        b.limit_value as limitValue,
        b.previous_value as previousValue,
        b.now_value as nowValue,
        b.tag as alarmPointTag,
        ap.alarm_point_id as alarmPointId,
        ap.tag as tag,
        ap.location as location,
        ap.alarm_point_hh as alarmPointHH,
        ap.alarm_point_hi as alarmPointHI,
        ap.alarm_point_lo as alarmPointLO,
        ap.alarm_point_ll as alarmPointLL,
        ap.craft_rank as craftRank,
        unit.sname as unitName,
        prdtcell.prdtcell_id as prdtCellId,
        prdtcell.sname as prdtCellName,
        af.alarm_flag_id as alarmFlagId,
        af.name as alarmFlagName,
        et.name as eventTypeName,
        mu.measunit_id as measUnitId,
        mu.name as measUnitName,
        mu.sign as sign
        FROM T_AD_ALARMEVENTDELAPPROVE a
        LEFT JOIN (select event_id,
        event_type_id,
        alarm_point_id,
        alarm_flag_id,
        start_time,
        alarm_time,
        priority,
        limit_value,
        in_shelved,
        in_suppressed,
        operator,
        des,
        previous_value,
        now_value,
        parameter,
        unit_code,
        prdtcell_id,
        dcs_code,
        tag,
        alarm_flag,
        priority_cache,
        write_time,
        company_id
        from T_AD_ALARMEVENT
        union all
        select event_id,
        event_type_id,
        alarm_point_id,
        alarm_flag_id,
        start_time,
        alarm_time,
        priority,
        limit_value,
        in_shelved,
        in_suppressed,
        operator,
        des,
        previous_value,
        now_value,
        parameter,
        unit_code,
        prdtcell_id,
        dcs_code,
        tag,
        alarm_flag,
        priority_cache,
        write_time,
        company_id
        from t_ad_alarmevent_del) b ON a.event_id = b.event_id
        LEFT JOIN T_PM_ALARMPOINT ap ON b.alarm_point_id = ap.alarm_point_id
        left join T_PM_UNIT unit on b.UNIT_CODE = unit.STD_CODE
        left join T_PM_PRDTCELL prdtcell on ap.PRDTCELL_ID = prdtcell.PRDTCELL_ID and unit.STD_CODE = prdtcell.unit_code
        left join T_AD_ALARMFLAG af on b.ALARM_FLAG_ID = af.ALARM_FLAG_ID
        left join T_PM_EVENTTYPE et on b.EVENT_TYPE_ID = et.EVENT_TYPE_ID
        left join T_PM_MEASUNIT mu on ap.MEASUNIT_ID = mu.MEASUNIT_ID
        <where>
            <if test="unitCode != null and unitCode.size() > 0">
                AND a.unit_code IN
                <foreach item="item" collection="unitCode" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="alarmPointTag != null and alarmPointTag != ''">
                AND ap.tag = #{alarmPointTag}
            </if>

            <if test="priority != null and priority.size() > 0">
                <choose>
                    <when test="priority.contains(9)">
                        AND (b.priority IN
                        <foreach item="item" collection="priority" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR b.priority IS NULL)
                    </when>
                    <otherwise>
                        AND b.priority IN
                        <foreach item="item" collection="priority" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <if test="monitorType != null and monitorType != -1">
                AND ap.monitor_type = #{monitorType}
            </if>

            <if test="alarmFlagId != null and alarmFlagId.size() > 0">
                <choose>
                    <when test="alarmFlagId.contains(-9L)">
                        AND (b.alarm_flag_id IN
                        <foreach item="item" collection="alarmFlagId" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR b.alarm_flag_id IS NULL)
                    </when>
                    <otherwise>
                        AND b.alarm_flag_id IN
                        <foreach item="item" collection="alarmFlagId" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <if test="startTime != null">
                AND b.start_time &gt;= #{startTime}
            </if>

            <if test="endTime != null">
                AND b.start_time &lt; #{endTime}
            </if>

            <choose>
                <when test="delStatus != null">
                    AND a.del_status = #{delStatus}
                </when>
                <otherwise>
                    AND a.del_status IN (1, 2, 3)
                </otherwise>
            </choose>
        </where>
        ORDER BY a.alarm_event_del_approve_id DESC
    </select>

</mapper>