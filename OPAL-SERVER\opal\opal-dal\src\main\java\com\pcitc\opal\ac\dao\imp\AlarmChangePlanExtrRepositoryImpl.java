package com.pcitc.opal.ac.dao.imp;

import com.pcitc.opal.ac.dao.AlarmChangePlanExtrRepositoryCustom;
import com.pcitc.opal.ac.pojo.AlarmChangePlanExtr;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.pojo.MeasUnit;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * 报警变更方案附加信息实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_AlarmChangePlanExtrRepositoryImpl
 * 作       者：dageng.sun
 * 创建时间：2017/01/30
 * 修改编号：1
 * 描       述：报警变更方案附加信息实体的Repository实现    
 */
public class AlarmChangePlanExtrRepositoryImpl extends BaseRepository<AlarmChangePlanExtr, Long> 
		implements AlarmChangePlanExtrRepositoryCustom {
	/**
	 * 新增报警变更方案附加信息
	 *
	 * <AUTHOR> 2018-01-30
	 * @param alarmChangePlanExtr 报警变更方案附加信息
	 * @return 返回结果信息类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addAlarmChangePlanExtr(AlarmChangePlanExtr alarmChangePlanExtr){
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			this.getEntityManager().persist(alarmChangePlanExtr);
			commonResult.setResult(alarmChangePlanExtr);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}

	/**
	 * 删除数据
	 *
	 * <AUTHOR> 2018-01-30
	 * @param planExtrIds 报警变更方案附加信息ID
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteAlarmChangePlanExtr(Long[] planExtrIds) throws Exception{
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = " from AlarmChangePlanExtr t where t.planExtrId in (:planExtrIds)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			List<Long> ids = Arrays.asList(planExtrIds);
			paramList.put("planExtrIds", ids);

			TypedQuery<AlarmChangePlanExtr> query = getEntityManager().createQuery(hql, AlarmChangePlanExtr.class);
			this.setParameterList(query, paramList);
			List<AlarmChangePlanExtr> acpeList = query.getResultList();
			acpeList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}
	/**
	 * 获取报警变更方案附加信息
	 * 
	 * <AUTHOR> 2018-01-30 
	 * @param planId 返回AlarmChangePlanExt实体
	 * @param businessType 业务类型(1下发；2确认)
	 */
	@SuppressWarnings("unchecked")
	@Override
	public AlarmChangePlanExtr getAlarmChangePlanExtrByPlanId(Long planId,Integer businessType) {
		try {
            // 查询字符串
            String hql = "select acpe from AlarmChangePlanExtr acpe ";
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 报警变更方案ID
            hql += " where acpe.planId = :planId ";
        	paramList.put("planId", planId);
        	// 业务类型
        	hql += " and acpe.businessType = :businessType ";
        	paramList.put("businessType", businessType);
        	hql += " order by acpe.opTime desc";
            Query query = getEntityManager().createQuery(hql);
            this.setParameterList(query, paramList);
            query.setFirstResult(0).setMaxResults(1);
            List<AlarmChangePlanExtr> list=query.getResultList();
            AlarmChangePlanExtr acpe=list.size()>0?list.get(0):null;
            return acpe;
        } catch (Exception e) {
            throw e;
        }
	}
	
}
