package com.pcitc.opal.oracle.dialect;
import org.hibernate.dialect.Oracle10gDialect;
import org.hibernate.dialect.function.SQLFunctionTemplate;
import org.hibernate.type.StandardBasicTypes;
import java.sql.Types;

/*
 * 在OracleDialect中注册自定义函数方便JHQL中调用
 *
 * 模块编号：pcitc_opal_dal_oracle_dialect_CustomOracleDialect
 * 作  　者：xuelei.wang
 * 创建时间：2017/11/2
 * 修改编号：1
 * 描    述：在OracleDialect中注册自定义函数方便JHQL中调用
 */
public class CustomOracleDialect{
//public class CustomOracleDialect extends Oracle10gDialect {
//	public CustomOracleDialect() {
//		super();
//		this.registerFunction("F_OPAL_GETTIMESEGMENT",
//				new SQLFunctionTemplate(StandardBasicTypes.STRING, "F_OPAL_GETTIMESEGMENT(?1,?2,?3,?4)"));
//		this.registerFunction("F_OPAL_INCRAFTRANGE",
//				new SQLFunctionTemplate(StandardBasicTypes.STRING, "F_OPAL_INCRAFTRANGE(?1,?2,?3,?4,?5)"));
//		this.registerFunction("F_OPAL_INTIMERANGE",
//				new SQLFunctionTemplate(StandardBasicTypes.NUMERIC_BOOLEAN, "F_OPAL_INTIMERANGE(?1,?2)"));
//		this.registerFunction("F_OPAL_ISNUMERIC",
//				new SQLFunctionTemplate(StandardBasicTypes.NUMERIC_BOOLEAN, "F_OPAL_ISNUMERIC(?1)"));
//		this.registerFunction("F_OPAL_COMPARE_NUMERIC",
//				new SQLFunctionTemplate(StandardBasicTypes.NUMERIC_BOOLEAN, "F_OPAL_COMPARE_NUMERIC(?1,?2,?3)"));
//		// 注册JPA未识别类型
//		registerHibernateType(Types.NCHAR, StandardBasicTypes.CHARACTER.getName());
//		registerHibernateType(Types.NCHAR, 1, StandardBasicTypes.CHARACTER.getName());
//		registerHibernateType(Types.NCHAR, 255, StandardBasicTypes.STRING.getName());
//		registerHibernateType(Types.NVARCHAR, StandardBasicTypes.STRING.getName());
//		registerHibernateType(Types.LONGNVARCHAR, StandardBasicTypes.TEXT.getName());
//		registerHibernateType(Types.NCLOB, StandardBasicTypes.CLOB.getName());
//		registerHibernateType(Types.JAVA_OBJECT, "T_OPAL_DATE_OBJECT");
//	}
}
