server.port=8083


spring.datasource.url=**************************************************************************************************************************************************
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=mes_opal
spring.datasource.password=Mes_opal123
#主数据库连接信息
#spring.datasource.url=*********************************************************************************************************************************************************
#spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.username=mes_opal_mtenant
#spring.datasource.password=mes_opal_mtenant

#主数据库连接信息总部生产
#spring.datasource.url=**************************************************************************************************************************************************
#spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.username=mes_opal
#spring.datasource.password=Mes_opal123

### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl.job.admin.addresses=http://127.0.0.1:8081/xxl-job-admin

### 执行器通讯TOKEN [选填]：非空时启用；
xxl.job.accessToken=default_token

### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
xxl.job.executor.appname=xxl-job-executor-sample
### 调度中心部署根地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
xxl.job.executor.address=
### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
xxl.job.executor.ip=
### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
xxl.job.executor.port=9998
### xxl-job executor log-path
xxl.job.executor.logpath=/data/applogs/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logretentiondays=30

##在建表的时候，将默认的存储引擎切换为 InnoDB 用的
spring.jpa.database= MYSQL
spring.jpa.database-platform=com.pcitc.opal.oracle.dialect.CustomMysqlDialect
#mi
#jasypt.encryptor.password=mesopal
#hibernate.dialect.storage_engine =innodb


# HikariCP settings
# spring.datasource.hikari.*
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.minimum-idle=20
spring.datasource.hikari.maximum-pool-size=100
# 设置连接有效时间
spring.datasource.hikari.max-lifetime=500000
#连接测试查询
spring.datasource.hikari.connection-test-query=SELECT 1


spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.serialization.write-dates-as-timestamps=true
#spring.jpa
spring.jpa.hibernate.show_sql = true
spring.jpa.show_sql = true
spring.jpa.open-in-view= true
spring.jpa.properties.hibernate.proc.param_null_passing=true
hibernate.proc.param_null_passing=true
spring.jpa.hibernate.jdbc.batch_size = 30
spring.jpa.hibernate.cache.use_second_level_cache = false
spring.jpa.properties.hibernate.jdbc.batch_size=100
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates =true

# 企业id
comId=5
# 工艺变更单信息URL（测试）
craftUrl=http://**************:8080/IP/WebService/OpeAlarmCraftIndexService.asmx/GetCraftIndexApply
# 工艺变更单信息URL（生产）
#craftUrl=http://ptm.sinopec.com/IP/WebService/OpeAlarmCraftIndexService.asmx/GetCraftIndexApply
#craftUrl=http://**************:8811/IP/WebService/OpeAlarmCraftIndexService.asmx/GetCraftIndexApply
        
#工艺卡片限制URL（测试）
#craftLimitUrl =http://**************:8080/IP/WebService/OpeAlarmCraftIndexService.asmx/getCraftCardLimit
#工艺卡片限制URL（生产）
#craftLimitUrl =http://ptm.sinopec.com/IP/WebService/OpeAlarmCraftIndexService.asmx/getCraftCardLimit
craftLimitUrl =http://**************:8811/IP/WebService/OpeAlarmCraftIndexService.asmx/getCraftCardLimit

#AAA使用版本   old:老版本(镇海),new:新版本,promace:promace
aaa_version=new


#AAA 班组WebService地址(后缀不用带"/")
aaa.shiftcalendarsvc_address_base=http://**************:8080
#PORMACE 班组
promace.imp.shift.base.url=http://shift.wsm.qlsh.promace.sinopec.com

#工厂模型-根Url
#factorymodel.base.url=http://**************:30625/FactoryModelService/rents/MESTEST/
factorymodel.base.url=http://pm.wsm.qlsh.promace.sinopec.com
fm_bizCode=qlsh
fm_unit_type_code=plants
factorymodel.bizCode=${fm_bizCode}
fm_factoryTypeCode=1005
factorymodel.factoryTypeCode=${fm_factoryTypeCode}
fm_workshopTypeCode=1007
factorymodel.workshopTypeCode=${fm_workshopTypeCode}


#运行环境类型promace,other
runtime_type=other
#是否开启装置数据权限 1：开启，0：不开启
aaa_auth=1 

#更新历史数据相关
histroyDate.startTime=2023-11-08 00:00:00
histroyDate.endTime=2023-11-10 00:00:00
histroyDate.open=true