package com.pcitc.opal.ad.dao.imp;

import com.pcitc.opal.ad.dao.AlarmEventDelRepository;
import com.pcitc.opal.ad.dao.AlarmEventDelRepositoryCustom;
import com.pcitc.opal.ad.dao.AlarmEventRepositoryCustom;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.ad.pojo.AlarmEventDel;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.pojo.AlarmPointDelConfig;
import org.springframework.orm.jpa.EntityManagerHolder;

import javax.transaction.Transactional;
import java.util.List;

/**
 * @USER: chenbo
 * @DATE: 2023/2/1
 * @TIME: 10:18
 * @DESC:
 **/
public class AlarmEventDelRepositoryImpl extends BaseRepository<AlarmEventDel, Long> implements AlarmEventDelRepositoryCustom {
    @Override
    public void insertSelectAlarmEvent(List<AlarmPointDelConfig> alarmPointDelConfigs) {

        StringBuilder hql = new StringBuilder();

        hql.append("select * from AlarmEvent where 1=1");
        //开始过滤需要剔除的数据
        if (alarmPointDelConfigs.size() != 0){
            //查询剔除配置信息
            for (AlarmPointDelConfig a : alarmPointDelConfigs) {

                //过滤报警点
                hql.append(" and ( ap.alarmPointId not in (").append(a.getAlarmPointId()).append(") ");
                //过滤报警标识
                hql.append(" or af.alarmFlagId not in (").append(a.getAlarmFlagId()).append(") ");
                //过滤时间
                hql.append(" or ae.alarmTime not between '").append(org.apache.commons.lang.time.DateFormatUtils.format(a.getDelStartTime(), "yyyy-MM-dd HH:mm:ss")).append("' and '").append(org.apache.commons.lang.time.DateFormatUtils.format(a.getDelEndTime(), "yyyy-MM-dd HH:mm:ss")).append("' )");

            }


        }


    }

    @Transactional
    @Override
    public void saveBatch(List<AlarmEventDel> alarmEventDels) {

        int batchSize = 1000;

        for (int i = 0; i < alarmEventDels.size(); i++) {
            alarmEventDels.get(i).setEventId(null);
            getEntityManager().persist(alarmEventDels.get(i));
            if (i % batchSize == 0) {
                getEntityManager().flush();
                getEntityManager().clear();
            }
        }

        getEntityManager().flush();
        getEntityManager().clear();
    }


}
