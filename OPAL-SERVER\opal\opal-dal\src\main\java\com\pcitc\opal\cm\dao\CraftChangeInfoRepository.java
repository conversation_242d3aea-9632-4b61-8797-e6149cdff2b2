package com.pcitc.opal.cm.dao;

import com.pcitc.opal.cm.pojo.CraftChangeInfo;
import com.pcitc.opal.pm.dao.AlarmPointTagCompRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmPointTagComp;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * AlarmEventTypeComp实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmEventTypeCompRepository
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：AlarmEventTypeComp实体的Repository实现   
 */
public interface CraftChangeInfoRepository extends JpaRepository<CraftChangeInfo, Long>, CraftChangeInfoRepositoryCustom {

}
