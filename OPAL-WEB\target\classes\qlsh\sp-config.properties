##
# config of SP
##

#默认认证方式（根据实际情况配置）
sp.auth.method.default=TAMUsernamePassword
#获取 sp、 idp的 metadata 文件地址（根据实际情况配置）
sp.metadata.ts.download.url=https://ts.siam.sinopec.com
#SP应用节点ID（根据实际情况配置）
sp.metadata.url.entityId=http://opal.qlsh.promace.sinopec.com
#应用code,中文需要ASCII转码（根据实际情况配置）
sp.login.tsysAccount=\u4e2d\u56fd\u77f3\u5316\u9f50\u9c81\u77f3\u5316\u667a\u4e91\u5e94\u7528\u95e8\u6237\u7cfb\u7edf
#证书路径（根据实际情况配置）
sp.credential.keyStorePath=classpath:/qlsh/portal-qlsh-promace-sinopec-com.jks
#证书库密码（根据实际情况配置,需要加密）
sp.credential.keyStorePassword=fdR96f3lyHRRjLaNnhEbhA==
# 证书密码（根据实际情况配置，需要加密）
sp.credential.keyPassword=fdR96f3lyHRRjLaNnhEbhA==
#证书别名（根据实际情况配置）
sp.credential.keyAlias=portal.qlsh.promace.sinopec.com
#IDP应用节点ID（根据实际情况配置）
sp.saml2.idp.entityId=https://auth.siam.sinopec.com/idp
#metadata请求超时时间，单位毫秒（根据实际情况配置）
ts.metadata.requestTimeout=5000
ts.metadata.disregardSSLCertificate=true
#metadata最小自动更新时间，单位毫秒（根据实际情况配置）默认4小时
ts.metadata.minRefreshDelay=********
#metadata最大自动更新时间，单位毫秒（根据实际情况配置）
ts.metadata.maxRefreshDelay=14450000
# SP Key Store Type: jks
sp.credential.keyStoreType=jks



#appServerUrls:应用服务器url列表
#transparentJunction:值为true时websealUrls与appServerUrls配置项生效,值为false时websealUrls与appServerUrls配置项不生效
#配置示例:siam.sp.eai.junction.appServerUrls=[http://java.uat.sinopec.com:8081/sp/SSO/SAML2/POST],[https://eai.siam.sinopec.com:8080],[https://cheng.siam.sinopec.com:8080]
#配置示例:siam.sp.eai.junction.transparentJunction=true
#siam.sp.eai.junction.appServerUrls=
#siam.sp.eai.junction.transparentJunction=false
siam.sp.proxy.web.urls=[http://172.16.55.158/SSO/SAML2/POST],[http://opal.qlsh.promace.sinopec.com/SSO/SAML2/POST]
siam.sp.proxy.load.url=http://opal.qlsh.promace.sinopec.com
siam.sp.proxy.flag=true


#------------------------------------------------------------------------------------
#
# SAML SP JSP Error Handler
#
sp.jsp.error.handler.path=/error.jsp

#------------------------------------------------------------------------------------
#
# SSO Login Path
#
#
#SSO global logout after the redirec to login
#
sp.saml2.slo.redirectToLogin=true
sp.saml2.slo.requestPaths=/SSO/SLO/Redirect

#------------------
#sp.ParserPool
#
sp.ParserPool.maxPoolSize=100
sp.ParserPool.coalescing=true
sp.ParserPool.ignoreComments=true
sp.ParserPool.ignoreElementContentWhitespace=true
sp.ParserPool.namespaceAware=true


#sp.metadata.ts.download.url+sp.metadata.url+sp.metadata.url.entityId拼接成metadata下载地址
#sp.metadata.backUpPath、idp.metadata.backUpPath为metadata下载后的存放路径
sp.metadata.url=/ts/services/restful/topology/publisher/getEntityDescriptorByEntityID?entityID=
sp.metadata.backUpPath=classpath:sp-metadata.xml
idp.metadata.url=/ts/services/restful/topology/publisher/getIdPEntitiesDescriptor
idp.metadata.backUpPath=classpath:idp-metadata-all.xml


#------------------------------------------------------------------------------------
#
# SAML SP Local Logout parameter
#
sp.saml2.self.LLO.I18N.path=com.sinopec.siam.agent.messages.messages
sp.saml2.self.LLO.image.path=/images/login/success1.jpg

#------------------------------------------------------------------------------------
#
# Theme Of IdP Login Page 
#
sp.saml2.idp.themeOfIdPLoginPage=default

# authentication level config file
sp.auth.method.level.file=classpath:/com/sinopec/siam/agent/web/siam-sp-authen-level.xml


#版本信息
sinopec.siam.version=3.2
sinopec.siam.releasedate=2018-05-18