package com.pcitc.opal.ak.bll.entity;


import com.pcitc.opal.ad.pojo.AlarmEvent;

import javax.persistence.*;
import java.util.Date;

/*
 * 报警知识管理实体
 * 模块编号：pcitc_opal_bll_class_AlarmKnowlgManagmtEntity
 * 作	者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描	述：报警知识管理实体
 */
public class AlarmKnowlgManagmtEntity {

    /**
     * 报警知识管理ID
     */
    private Long alarmKnowlgManagmtId;

    /**
     * 报警事件ID
     */
    private Long eventId;

    /**
     * 原因
     */
    private String reason;

    /**
     * 处置方案
     */
    private String disposalScheme;

    /**
     * 影响
     */
    private String impact;
    /**
     * 报警时间
     */
    private Date alarmTime;
    /**
     * 创建时间
     */
    private Date crtDate;

    /**
     * 报警事件实体
     */
    private AlarmEvent alarmEvent;

    /**
     * 是否标红
     */
    private int isRed;

    public Long getAlarmKnowlgManagmtId() {
        return alarmKnowlgManagmtId;
    }

    public void setAlarmKnowlgManagmtId(Long alarmKnowlgManagmtId) {
        this.alarmKnowlgManagmtId = alarmKnowlgManagmtId;
    }

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getDisposalScheme() {
        return disposalScheme;
    }

    public void setDisposalScheme(String disposalScheme) {
        this.disposalScheme = disposalScheme;
    }

    public String getImpact() {
        return impact;
    }

    public void setImpact(String impact) {
        this.impact = impact;
    }

    public Date getCrtDate() {
        return crtDate;
    }

    public void setCrtDate(Date crtDate) {
        this.crtDate = crtDate;
    }

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public AlarmEvent getAlarmEvent() {
        return alarmEvent;
    }

    public void setAlarmEvent(AlarmEvent alarmEvent) {
        this.alarmEvent = alarmEvent;
    }

    public int getIsRed() {
        return isRed;
    }

    public void setIsRed(int isRed) {
        this.isRed = isRed;
    }
}
