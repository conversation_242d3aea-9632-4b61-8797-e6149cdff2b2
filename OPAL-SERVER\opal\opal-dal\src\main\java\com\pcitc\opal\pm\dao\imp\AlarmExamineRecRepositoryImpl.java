package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmExamineRecRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmExamineRec;
import org.apache.commons.lang.ArrayUtils;

import java.util.*;

/**
 * @USER: chenbo
 * @DATE: 2023/4/14
 * @TIME: 10:12
 * @DESC:
 **/
public class AlarmExamineRecRepositoryImpl extends BaseRepository<AlarmExamineRec, Long> implements AlarmExamineRecRepositoryCustom {
    @Override
    public PaginationBean<AlarmExamineRec> getAlarmExamineRec(String[] unitIds, Long[] prdtCellIds, String tag, Integer alarmFlagId, Integer priority, Date startTime, Date endTime, Integer alarmDuration, Integer examineStatus, Pagination page) {

        StringBuilder hql = new StringBuilder();

        HashMap<String, Object> param = new HashMap<>();


        hql.append("select aer from AlarmExamineRec aer")
                .append(" inner join aer.alarmRec ar")
                .append(" inner join ar.alarmPoint ap ")
                .append(" inner join ar.alarmFlag af")
                .append(" inner join ar.prdtCell pc ");


        //过滤装置
        hql.append(" where ar.unitCode in (:unitIds) ");
        param.put("unitIds", Arrays.asList(unitIds));


        switch (alarmDuration) {
            case 1:
                hql.append(" and TIMESTAMPDIFf(minute, ar.alarmTime, case when :examineStatus = 2 then  aer.recoveryTime else ifnull(ar.recoveryTime, :endTime) end) between 30 and 59 ");
                break;
            case 2:
                hql.append(" and TIMESTAMPDIFf(hour, ar.alarmTime, case when :examineStatus = 2 then  aer.recoveryTime else ifnull(ar.recoveryTime, :endTime) end) between 1 and 11 ");
                break;
            case 3:
                hql.append(" and TIMESTAMPDIFf(hour, ar.alarmTime, case when :examineStatus = 2 then  aer.recoveryTime else ifnull(ar.recoveryTime, :endTime) between 12 and 23 ");
                break;
            case 4:
                hql.append(" and TIMESTAMPDIFf(hour, ar.alarmTime, case when :examineStatus = 2 then  aer.recoveryTime else ifnull(ar.recoveryTime, :endTime) end) > 24 ");
                break;
        }


        CommonProperty commonProperty = new CommonProperty();
        hql.append(" and ar.companyId = :companyId ");
        param.put("companyId", commonProperty.getCompanyId());


        hql.append(" and aer.examineStatus = :examineStatus ");
        param.put("examineStatus", examineStatus);

        //过滤时间
        hql.append(" and ar.alarmTime between :startTime and :endTime ");
        param.put("startTime", startTime);
        param.put("endTime", endTime);

        //过滤生产单元
        if (ArrayUtils.isNotEmpty(prdtCellIds)){
            hql.append(" and ar.prdtCellId in (:prdtCellIds) ");
            param.put("prdtCellIds", Arrays.asList(prdtCellIds));
        }

        //过滤位号
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(tag)){
            hql.append(" and ar.tag like :tag ");
            param.put("tag", "%"+ tag + "%");
        }

        //过滤报警标识
        if (alarmFlagId != null && alarmFlagId != -1){
            hql.append(" and ar.alarmFlagId in (:alarmFlagId) ");
            param.put("alarmFlagId", Long.valueOf(alarmFlagId));
        }

        //过滤优先级
        if (priority != null && priority != -1){
            hql.append(" and ar.priority in (:priority) ");
            param.put("priority", priority);
        }


        hql.append(" order by ar.alarmTime desc ");


        return findAll(page, hql.toString(), param);

    }
}
