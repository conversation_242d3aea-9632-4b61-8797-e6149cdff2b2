<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.mapper.DcsCodeMapper">

    <resultMap id="BaseResultMap" type="com.pcitc.opal.pojo.DcsCode">
            <id property="dcsCodeId" column="dcs_code_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="inUse" column="in_use" jdbcType="BIGINT"/>
            <result property="sortNum" column="sort_num" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        dcs_code_id,name,in_use,
        sort_num
    </sql>
    <select id="selectAllToTenantDb" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_pm_dcscode
    </select>
</mapper>
