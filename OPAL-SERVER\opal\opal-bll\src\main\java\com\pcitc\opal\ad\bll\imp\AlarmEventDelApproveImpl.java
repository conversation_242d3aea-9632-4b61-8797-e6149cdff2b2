package com.pcitc.opal.ad.bll.imp;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pcitc.opal.ad.bll.AlarmEventDelApproveService;
import com.pcitc.opal.ad.bll.entity.AlarmEventDelApproveEntity;
import com.pcitc.opal.ad.dao.*;
import com.pcitc.opal.ad.pojo.*;
import com.pcitc.opal.ad.vo.AlarmEventDelApproveVO;
import com.pcitc.opal.ad.vo.FloodAlarmPointCountVO;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointDelConfigDTOEntity;
import com.pcitc.opal.pm.dao.AlarmPointRepository;
import com.pcitc.opal.pm.dao.EventTypeRepository;
import com.pcitc.opal.pm.dao.MeasUnitRepository;
import com.pcitc.opal.pm.dao.PrdtCellRepository;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import com.pcitc.opal.pm.pojo.EventType;
import com.pcitc.opal.pm.pojo.MeasUnit;
import com.pcitc.opal.pm.pojo.PrdtCell;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import javax.annotation.Resource;
import java.rmi.RemoteException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AlarmEventDelApproveImpl implements AlarmEventDelApproveService {

    @Resource
    private AlarmEventDelApproveRepository alarmEventDelApproveRepository;

    @Resource
    private BasicDataService basicDataService;

    @Resource
    private AlarmEventDelApproveService alarmEventDelApproveService;

    @Resource
    private AlarmEventDelRepository alarmEventDelRepository;

    @Resource
    private AlarmEventRepository alarmEventRepository;

    @Resource
    private AlarmRecRepository alarmRecRepository;

    @Resource
    private AlarmRecDelRepository alarmRecDelRepository;

    @Resource
    private PrdtCellRepository prdtCellRepository;
    @Resource
    private EventTypeRepository eventTypeRepository;
    @Resource
    private AlarmPointRepository alarmPointRepository;
    @Resource
    private AlarmFlagRepository alarmFlagRepository;

    @Autowired
    private AlarmEventDelApproveDAO alarmEventDelApproveDAO;

    @Override
    public PaginationBean<AlarmEventDelApproveEntity> getAlarmEventDelApprove(String[] unitCode, Integer[] priority,
                                                                              Long[] alarmFlagId, Integer monitorType,
                                                                              Date startTime, Date endTime, String alarmPointTag, Integer delStatus,
                                                                              Pagination page) {
        PaginationBean<AlarmEventDelApproveEntity> returnList = null;
        try {
            List<UnitEntity> unitListByIds = basicDataService.getUnitListByIds(unitCode, true);
            unitCode = unitListByIds.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
//            PaginationBean<AlarmEventDelApprove> alarmEventDelApprove = alarmEventDelApproveRepository
//                    .getAlarmEventDelApprove(unitCode, priority, alarmFlagId, monitorType, startTime, endTime,
//                            alarmPointTag, delStatus, page);


            Page pager = new Page(page.getPageNumber(),page.getPageSize());
            List<String> unitCodeList = null;
            if (unitCode != null) {
                unitCodeList = Arrays.asList(unitCode);
            }

            List<Integer> priorityList = null;
            if (priority != null) {
                priorityList = Arrays.asList(priority);
            }
            List<Long> alarmFlagIdList = null;
            if (alarmFlagId != null) {
                alarmFlagIdList = Arrays.asList(alarmFlagId);
            }
            IPage<AlarmEventDelApproveVO> alarmEventDelApprove = alarmEventDelApproveDAO.getAlarmEventDelApprove(unitCodeList, priorityList,
                    alarmFlagIdList, monitorType, startTime, endTime, alarmPointTag, delStatus, pager);
            returnList = new PaginationBean<>(page, alarmEventDelApprove.getTotal());

            List<AlarmEventDelApproveEntity> alarmEventDelApproveEntities = new ArrayList<>();
            for (AlarmEventDelApproveVO eventDelApprove : alarmEventDelApprove.getRecords()) {
                AlarmEventDelApproveEntity alarmEventDelApproveEntity = ObjectConverter.entityConverter(eventDelApprove, AlarmEventDelApproveEntity.class);
                alarmEventDelApproveEntities.add(alarmEventDelApproveEntity);
//                PrdtCell prdtCell = alarmEvent.getPrdtCell();
//                AlarmPoint alarmPoint = alarmEvent.getAlarmPoint();
//                AlarmFlag alarmFlag = alarmEvent.getAlarmFlag();
//                EventType eventType = alarmEvent.getEventType();
//                AlarmEventDelApproveEntity alarmEventDelApproveEntity = ObjectConverter.entityConverter(alarmEvent, AlarmEventDelApproveEntity.class);
//                // 填充装置简称
//                alarmEventDelApproveEntity.setUnitName(unitListByIds.stream().filter(u -> prdtCell.getUnitId().equals(u.getStdCode())).findFirst().orElse(new UnitEntity()).getSname());
//                // 填充生产单元简称
//                alarmEventDelApproveEntity.setPrdtCellName(prdtCell.getSname());
//                // 填充报警点位号
//                alarmEventDelApproveEntity.setAlarmPointTag(null != alarmPoint ? alarmPoint.getTag() : alarmEvent.getTag());
//                //报警点位置
//                alarmEventDelApproveEntity.setLocation(null != alarmPoint ? alarmPoint.getLocation() : "");
//                // 填充报警标识名称
//                alarmEventDelApproveEntity.setAlarmFlagName(null != alarmFlag ? alarmFlag.getName() : null);
//                // 填充事件类型
//                alarmEventDelApproveEntity.setEventTypeName(eventType.getName());
//                // 填充计量单位
//                MeasUnit measUnit = null != alarmPoint ? alarmPoint.getMeasUnit() : null;
                alarmEventDelApproveEntity.setMeasUnitName(eventDelApprove.getMeasUnitName() + "(" + eventDelApprove.getSign() + ")");
//                // 填充级别
//                alarmEventDelApproveEntity.setCraftRank(null != alarmPoint ? alarmPoint.getCraftRank() : null);
                alarmEventDelApproveEntity.setCraftRankName(alarmEventDelApproveEntity.getCraftRankName());
//                // 优先级名称
//                alarmEventDelApproveEntity.setPriorityName(alarmEventDelApproveEntity.getPriorityName());
//                alarmEventDelApproveEntity.setAlarmPointHH(alarmPoint.getAlarmPointHH());
//                alarmEventDelApproveEntity.setAlarmPointHI(alarmPoint.getAlarmPointHI());
//                alarmEventDelApproveEntity.setAlarmPointLL(alarmPoint.getAlarmPointLL());
//                alarmEventDelApproveEntity.setAlarmPointLO(alarmPoint.getAlarmPointLO());
//                // 限值处理
                if (alarmEventDelApproveEntity.getEventTypeId().toString().startsWith("10")) {
                    Long flag = alarmEventDelApproveEntity.getAlarmFlagId();
                    if (null != flag) {
                        if (flag == CommonEnum.AlarmFlagEnum.PVHH.getIndex() && alarmEventDelApproveEntity.getAlarmPointHH() != null) {
                            alarmEventDelApproveEntity.setLimitValue(alarmEventDelApproveEntity.getAlarmPointHH());
                        } else if (flag == CommonEnum.AlarmFlagEnum.PVHI.getIndex() && alarmEventDelApproveEntity.getAlarmPointHI() != null) {
                            alarmEventDelApproveEntity.setLimitValue(alarmEventDelApproveEntity.getAlarmPointHI());
                        } else if (flag == CommonEnum.AlarmFlagEnum.PVLL.getIndex() && alarmEventDelApproveEntity.getAlarmPointLL() != null) {
                            alarmEventDelApproveEntity.setLimitValue(alarmEventDelApproveEntity.getAlarmPointLL());
                        } else if (flag == CommonEnum.AlarmFlagEnum.PVLO.getIndex() && alarmEventDelApproveEntity.getAlarmPointLO() != null) {
                            alarmEventDelApproveEntity.setLimitValue(alarmEventDelApproveEntity.getAlarmPointLO());
                        }
                    }
                }
//                alarmEventDelApproveEntity.setAlarmEventDelApproveId(eventDelApprove.getAlarmEventDelApproveId());
//                alarmEventDelApproveEntity.setReason(eventDelApprove.getReason());
//                alarmEventDelApproveEntity.setDelStatus(eventDelApprove.getDelStatus());
                alarmEventDelApproveEntity.setDelStatusShow(AlarmPointDelConfigDTOEntity.getDelStatusShowDetail(eventDelApprove.getDelStatus()));
//                alarmEventDelApproveEntity.setCrtDate(eventDelApprove.getCrtDate());
//                alarmEventDelApproveEntity.setCrtUserName(eventDelApprove.getCrtUserName());
//                alarmEventDelApproveEntity.setAProTime(eventDelApprove.getAProTime());
//                alarmEventDelApproveEntity.setAProUserName(eventDelApprove.getAProUserName());
//                alarmEventDelApproveEntities.add(alarmEventDelApproveEntity);
            }
            returnList.setPageList(alarmEventDelApproveEntities);
        } catch (Exception e) {
            log.error("getAlarmEventDelApprove获取装置异常", e);
            throw new RuntimeException("getAlarmEventDelApprove获取装置异常", e);
        }
        return returnList;
    }

    @Override
    public String approvePass(Integer[] alarmEventDelApproveId) {

        List<AlarmEventDelApprove> alarmEventDelApproves = alarmEventDelApproveRepository.findAllById(Arrays.asList(alarmEventDelApproveId));

        CommonProperty commonProperty = new CommonProperty();

        for (AlarmEventDelApprove alarmEventDelApprove : alarmEventDelApproves) {
            int delDataStatus = 5;
            AlarmEvent alarmEvent = alarmEventDelApprove.getAlarmEvent();
            //对象转换
            AlarmEventDel alarmEventDel = new AlarmEventDel(alarmEvent);
            try {
                //保存到剔除表
                alarmEventDelRepository.save(alarmEventDel);
                //删除原数据
                alarmEventRepository.delete(alarmEvent);
            } catch (Exception e) {
                delDataStatus = CommonEnum.DelDataStatus.EventFail.getStatus();
            }

            //1001，剔除记录表
            if (CommonEnum.EventTypeEnum.ProcessEvent.getIndex().equals(alarmEvent.getEventTypeId())) {
                //查询rec
                AlarmRec alarmRecByEventId = alarmRecRepository.getAlarmRecByEventId(alarmEvent.getEventId());
                if (alarmRecByEventId != null) {
                    //对象转换
                    AlarmRecDel alarmRecDel = new AlarmRecDel(alarmRecByEventId);
                    try {
                        //保存到剔除表
                        alarmRecDelRepository.save(alarmRecDel);
                        //删除原数据
                        alarmRecRepository.delete(alarmRecByEventId);
                    } catch (Exception e) {
                        if (delDataStatus == CommonEnum.DelDataStatus.EventFail.getStatus()) {
                            delDataStatus = CommonEnum.DelDataStatus.DelFail.getStatus();
                        } else {
                            delDataStatus = CommonEnum.DelDataStatus.RecFail.getStatus();
                        }
                    }
                }
            }

            //赋值修改人信息
            try {
                CommonUtil.returnValue(alarmEventDelApprove, 2);
                alarmEventDelApprove.setAProUserName(commonProperty.getUserName());
            } catch (RemoteException ignored) {
            }

            // 赋值审批人信息
            alarmEventDelApprove.setAProTime(new Date());
            alarmEventDelApprove.setAProUserId(commonProperty.getUserId());

            //修改剔除状态
            alarmEventDelApprove.setDelStatus(2);
            //修改数据剔除状态
            alarmEventDelApprove.setDelDataStatus(delDataStatus);
            //保存数据
            alarmEventDelApproveRepository.save(alarmEventDelApprove);

        }

        return "审批已通过";
    }

    @Override
    public String reject(Integer[] alarmEventDelApproveId) {
        try {
            CommonProperty commonProperty = new CommonProperty();
            List<AlarmEventDelApprove> allById = alarmEventDelApproveRepository.findAllById(Arrays.asList(alarmEventDelApproveId));
            allById.forEach(a -> {
                try {
                    CommonUtil.returnValue(a, 2);
                    a.setAProUserName(commonProperty.getUserName());
                    a.setAProTime(new Date());
                    a.setAProUserId(commonProperty.getUserId());
                    a.setDelStatus(3);
                } catch (RemoteException ignored) {
                }

            });
            alarmEventDelApproveRepository.saveAll(allById);
        } catch (Exception e) {
            return "驳回失败";
        }
        return "驳回成功";
    }

    @Override
    public String updateReason(Integer alarmEventDelApproveService, String reason) {
        if (StringUtils.isEmpty(reason)) {
            return "剔除原因不能为空";
        }
        Optional<AlarmEventDelApprove> byId = alarmEventDelApproveRepository.findById(alarmEventDelApproveService);

        if (byId.isPresent()) {
            try {
                AlarmEventDelApprove alarmEventDelApprove = byId.get();
                alarmEventDelApprove.setReason(reason);
                alarmEventDelApproveRepository.save(alarmEventDelApprove);
            } catch (Exception e) {
                log.error("updateReason修改失败-{}", e.getMessage());
                return "修改失败";
            }
        }
        return "修改成功";
    }

    @Transactional
    @Override
    public String removeAlarmEventDelApprove(Integer[] alarmEventDelApproveId) {

        CommonResult commonResult = new CommonResult();

        List<AlarmEventDelApprove> lists = alarmEventDelApproveRepository.findAllById(Arrays.asList(alarmEventDelApproveId));

        // 过滤剔除状态为已驳回的进行删除
        List<AlarmEventDelApprove> remove = lists.stream().filter(x -> x.getDelStatus().equals(3)).collect(Collectors.toList());

        if (lists.size() == remove.size()) {
            commonResult.setMessage("删除成功");
        } else {
            commonResult.setMessage("部分删除成功，只能删除状态为已驳回的数据");
        }

        try {
            alarmEventDelApproveRepository.deleteInBatch(remove);
        } catch (Exception e) {
            log.error("removeAlarmEventDelApprove删除失败-{}", e.getMessage());
            throw new RuntimeException("删除失败");
        }

        return commonResult.getMessage();
    }


}
