package com.pcitc.opal.ae.bll;

import java.util.Date;
import java.util.List;

import com.pcitc.opal.aa.bll.entity.PeakAlarmRateEntity;
import com.pcitc.opal.ac.bll.entity.AlarmChangePlanEntity;
import com.pcitc.opal.ad.bll.entity.AlarmRecEntity;
import com.pcitc.opal.ad.bll.entity.AlarmRespondEntity;
import com.pcitc.opal.ad.bll.entity.UnRespondEntity;
import org.springframework.stereotype.Service;

import com.pcitc.opal.ad.bll.entity.AlarmEventViewExEntity;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

/*
 * 报警响应查询页面业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_AlarmRespondService
 * 作       者：dageng.sun
 * 创建时间：2017/10/24
 * 修改编号：1
 * 描       述：报警响应查询页面业务逻辑层接口 
 */
@Service
public interface AlarmRespondService {
	
	/**
	 * 报警响应查询页面
	 * 
	 * <AUTHOR> 2017-10-24
	 *
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 位号
	 * @param alarmFlagId 报警表示
	 * @param priority 优先级
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param isTimeResponse 是否及时响应
	 * @param workTeamIds 班组编号
	 * @param responseDuration 响应时长
	 * @param page 翻页实现类
	 * @return
	 * @throws Exception 
	 * @return PaginationBean<AlarmEventEntity>   AlarmEventEntity分页对象
	 */
	PaginationBean<AlarmRecEntity> getAlarmRespond(String[] unitCodes, Long[] prdtCellIds, String tag, Long alarmFlagId, Integer priority, Date beginTime, Date endTime,
												   Integer isTimeResponse, Long[] workTeamIds, Integer responseDuration, Pagination page)
            throws Exception;

	PaginationBean<AlarmRespondEntity> getAlarmRespondForInterface(String[] workUnitIds, Date startTime, Date endTime, Pagination page) throws Exception;

	List<UnRespondEntity> getAlarmRespondOfUnRespond(Date startTime, Date endTime, String[] unitIds) throws Exception;
}
