var getPrdtCellInPrdtCellCompUrl = OPAL.API.pmUrl + "/alarmPrdtCellComp/getPrdtCellInAlarmPrdtCellComp";
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var UnitPrdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var inUseUrl = OPAL.API.commUrl + "/getInUse";

var getAlarmPointCompForAdd = OPAL.API.pmUrl + "/alarmPointComp/getAlarmPointCompForAdd";
var getAlarmPointCompForDel = OPAL.API.pmUrl + "/alarmPointComp/getAlarmPointCompForDel";
var stopAlarmPointUrl = OPAL.API.pmUrl + "/alarmPoints/stopAlarmPoint";
var exportExcelUrl = OPAL.API.pmUrl + "/alarmPointComp/exportAlarmPointCompForAdd";
$(function () {
    var page = {
        init: function () {
            this.bindUi();
            this.bindTabClick();

            page.logic.initUnitTree();
            page.logic.initInUse();
            page.logic.initQueryTime();

            page.logic.initAddTable();
            page.logic.initDelTable();

            //默认查询数据
            page.logic.search();
        },
        bindUi: function () {
            $('#btnQuery').click(function () {
                page.logic.search();
            });
            $("#btnStop").click(function(){
                page.logic.stopAlarmPointAll();
            })
            $("#btnExport").click(function(){
                page.logic.exportExcel();
            })
        },
        /**
         * 页签切换事件
         */
        bindTabClick: function () {
            $("#navTab li").click(function () {
                var id = $(this).attr('id');
                if (id == "liAddType") {
                    $("#divDate").css("display","inline-block");
                    $("#divPrdtCell").css("display", "none");
                    $("#divInUse").css("display", "none");
                    $("#btnExport").css("display", "inline-block");
                    $("#btnStop").css("display", "none");
                } else if (id == "liDelType") {
                    $("#divPrdtCell").css("display", "inline-block");
                    $("#divInUse").css("display", "inline-block");
                    $("#divDate").css("display","none");
                    $("#btnExport").css("display", "none");
                    $("#btnStop").css("display", "inline-block");
                }
            });
        },
        data: {
            param: {}
        },
        logic: {
            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            search: function() {
                // if (!OPAL.util.checkDateIsValid()) {
                //     return false;
                // }
                var id = $("#navTab li.active").attr("id");
                page.data.param = OPAL.form.getData('searchForm');
                $("#btnQuery").attr('disabled',true);
                if (id == "liAddType") {
                    if(page.data.param.startTime !=''){
                        page.data.param.startTime = page.data.param.startTime+" 00:00:00";
                    }
                    if(page.data.param.endTime !=''){
                        page.data.param.endTime = page.data.param.endTime+" 00:00:00";
                    }
                    $("#addTable").bootstrapTable('refresh', {
                        "url": getAlarmPointCompForAdd,
                        "pageNumber": 1
                    });
                } else if (id == "liDelType") {
                    $("#delTable").bootstrapTable('refresh', {
                        "url": getAlarmPointCompForDel,
                        "pageNumber": 1
                    });
                }
            },
            /**
             * 初始化事件类型列表
             *
             * @param ctrlID 控件ID
             * <AUTHOR>
             */
            initAddTable: function () {
                OPAL.ui.initBootstrapTable2("addTable", {
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            var tableOption = $('#addTable').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            return (index + 1) + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "写入时间",
                        field: 'writeTime',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "装置",
                        field: 'unitSname',
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCell',
                        align: 'left',
                        width: '200px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "高高报值",
                        field: 'hh',
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "高报值",
                        field: 'ph',
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "低报值",
                        field: 'pl',
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "低低报值",
                        field: 'll',
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '100px',
                        formatter: page.logic.onActionRenderer
                    }],
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    }
                }, page.logic.queryParams,"btnQuery")
            },
            /**
             * 初始化报警点列表
             */
            initDelTable: function () {
                OPAL.ui.initBootstrapTable2("delTable", {
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center'
                    },{
                        title: "序号",
                        formatter: function (value, row, index) {
                            var tableOption = $('#delTable').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            return (index + 1) + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "装置",
                        field: 'unitSname',
                        rowspan: 1,
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellSname',
                        align: 'left',
                        width: '140px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "级别",
                        field: 'craftRankName',
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "位置",
                        field: 'location',
                        rowspan: 1,
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "工艺卡片值",
                        field: 'craftLimitValue',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "联锁值",
                        field: 'interlockLimitValue',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "是否启用",
                        field: 'inUseShow',
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '100px',
                        formatter: page.logic.onActionRendererStop
                    }],
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    }
                }, page.logic.queryParams,"btnQuery");
            }, 
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a name="TableEditor" class="btn-config"  href="javascript:window.page.logic.setAlarmPoint(\'' + rowData.unitId + '\', \'' + rowData.prdtCellId + '\',\''+
                    rowData.hh + '\',\''+ rowData.ph + '\',\''+rowData.pl + '\',\''+rowData.ll + '\',\''+rowData.tag+'\')">配置</a>'
                ]
            },
            onActionRendererStop: function () {
                var rowData = arguments[1];
                if(rowData.inUse == 1){
                    return [
                    '<a name="TableEditor" class="btn-config"  href="javascript:window.page.logic.stopAlarmPoint(\'' + rowData.alarmPointId + '\')">停用</a>'
                    ]
                }
                else{
                    return [
                    '<span name="TableEditor" class="">停用</span>'
                    ]
                }
                
            },
            stopAlarmPoint:function(id){
                var data = new Array();
                data.push(id);
                layer.confirm('确定停用吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: stopAlarmPointUrl,
                        async: false, //
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("操作成功！", {
                                    time: 1000
                                }, function () {
                                    $('#delTable').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            stopAlarmPointAll:function(){
                var idsArray = new Array();
                var rowsArray = $("#delTable").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmPointId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要停用的数据！");
                    return;
                }
                layer.confirm('确定停用吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: stopAlarmPointUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("操作成功！", {
                                    time: 1000
                                }, function () {
                                    $('#delTable').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect("unitIds", commonUnitTreeUrl, "id", "parentId", "sname", {
                    data: {
                        'enablePrivilege': true,
                    },
                    onCheck: function (node) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds");
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }

                    }
                }, false, function () {

                });
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function (unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', UnitPrdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化查询inUse
             */
            initInUse: function () {
                OPAL.ui.getCombobox("inUse", inUseUrl, {
                    selectValue: 1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            /**
             * 初始化查询时间
             */
            initQueryTime: function () {
                OPAL.util.getSysDateTime(function (date) {
                    var startTime = moment(date).format("YYYY-01-01");
                    //var endTime = moment(date).format("YYYY-MM-DD");
                    var endTime = moment(new Date(new Date().getTime() - 86400000)).format("YYYY-MM-DD");
                    laydate.render({
                        elem: '#startTime',
                        type: 'date',
                        trigger: 'click',
                        btns: ['clear', 'confirm'],
                        format: 'yyyy-MM-dd',
                        value: startTime,
                        // max: endTime,
                    });
                    laydate.render({
                        elem: '#endTime',
                        type: 'date',
                        trigger: 'click',
                        btns: ['clear', 'confirm'],
                        format: 'yyyy-MM-dd',
                        value: endTime,
                         max: endTime,
                    });
                    // $('#startTime').attr('maxDate', endTime);
                    // $('#endTime').attr('maxDate', endTime);
                });
            },
            /**
             * 报警点设置
             * @param alarmPoint
             */
            setAlarmPoint: function (unitId, prdtCellId, hh,ph,pl,ll, tag) {
                if (tag == '') {
                    layer.msg("报警点为空！");
                    return;
                }
                layer.open({
                    type: 2,
                    title: '报警点配置',
                    closeBtn: 1,
                    area: ['1000px', '81%'],
                    shadeClose: false,
                    offset: '30px',
                    content: '../AlarmPoint/AlarmPointAddOrEdit.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var setData = {
                            "unitId": unitId,
                            "prdtCellId": prdtCellId,
                            "tag": tag,
                            "pageMode": PageModelEnum.View,
                            "values":{"hh":hh,'ph':ph,'pl':pl,'ll':ll}
                        };
                        iframeWin.page.logic.setData(setData);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            $("#addTable").bootstrapTable('refresh', {
                                "url": getAlarmPointCompForAdd,
                                "pageNumber": 1
                            });
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#addTable').bootstrapTable('selectPage', 1);
                        }
                    }
                })
            },
            /**
             * 导出
             */
            exportExcel: function () {
                var titleArray = new Array();
                var tableTitle = new Array(
                    {'title':"装置",'field':"unitSname"},
                    {'title':"生产单元",'field':"prdtCell"},
                    {'title':"位号",'field':"tag"},
                    {'title':"位置",'field':"location"},
                    {'title':"PID图号",'field':"pid"},
                    {'title':"计量单位",'field':"measUnit"},
                    {'title':"高高报值",'field':"hh"},
                    {'title':"高报值",'field':"ph"},
                    {'title':"低报值",'field':"pl"},
                    {'title':"低低报值",'field':"ll"},
                    {'title':"是否虚表",'field':"virtualRealityFlagStr"},
                    {'title':"报警点类型",'field':"alarmPointTypeStr"},
                    {'title':"专业",'field':"monitorTypeStr"},
                    {'title':"仪表类型",'field':"instrmtTypeStr"},
                    {'title':"是否启用",'field':"inUseStr"},
                    {'title':"排序",'field':"sortNum"},
                    {'title':"备注",'field':"des"}
                );
                $.each(tableTitle, function (i, el) {
                    titleArray.push({
                        'key': el.field,
                        'value': el.title
                    })
                })
                var data = {};
                var formData = page.data.param;
                var pageSize = $('#addTable').bootstrapTable('getOptions').pageSize;
                var pageNumber = $('#addTable').bootstrapTable('getOptions').pageNumber;
                // data.titles = JSON.stringify(titleArray);
                data.pageSize = pageSize;
                data.pageNumber = pageNumber;
                data.unitIds = formData.unitIds;
                data.tag = formData.tag;
                data.startTime = formData.startTime+" 00:00:00";
                data.endTime = formData.endTime+" 00:00:00";
                var str = "";
                $.each(data, function (key, value) {
                    if (value == "null" || value == undefined || value == null) {
                        value = "";
                    }
                    str += "<input type=\"hidden\" name=\"" + key + "\" value=\"" + value + "\">";
                });
                str += "<input type=\"hidden\" name='titles' value=" + JSON.stringify(titleArray) + ">";
                $('#formExPort').html(str);
                $('#formExPort').attr('action', exportExcelUrl);
                $('#formExPort').submit();
            },
        }
    };
    page.init();
    window.page = page;
})