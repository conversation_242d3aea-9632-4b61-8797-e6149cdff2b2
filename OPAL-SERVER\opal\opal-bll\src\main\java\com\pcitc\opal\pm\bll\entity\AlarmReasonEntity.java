package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.pm.pojo.AlarmReason;
import lombok.Data;
import lombok.NoArgsConstructor;
import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class AlarmReasonEntity extends BaseResRep implements Serializable {

    private Long alarmReasonId;

    private Long reasonType;

    private String reasonTypeName;

    private String name;

    private Long inUse;

    private String inUserName;

    private Long sortNum;

    private String des;

    private Date crtDate;

    private String crtUserId;

    private String crtUserName;

    private Date mntDate;

    private String mntUserId;

    private String mntUserName;


    public AlarmReasonEntity(AlarmReason alarmReason) {
        alarmReasonId = alarmReason.getAlarmReasonId();
        reasonType = alarmReason.getReasonType();
        name = alarmReason.getName();
        inUse = alarmReason.getInUse();
        sortNum = alarmReason.getSortNum();
        des = alarmReason.getDes();
        crtDate = alarmReason.getCrtDate();
        crtUserId = alarmReason.getCrtUserId();
        crtUserName = alarmReason.getCrtUserName();
        mntDate = alarmReason.getMntDate();
        mntUserId = alarmReason.getMntUserId();
        mntUserName = alarmReason.getMntUserName();
    }
}
