package com.pcitc.opal.ad.bll;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.bll.entity.MobileMsgListEntity;
import com.pcitc.opal.ad.bll.entity.MonitoringDataEntity;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.ad.vo.AlarmEventTableVO;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.pm.bll.entity.EventTypeEntity;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.Date;
import java.util.List;

/*
 * 报警事件逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_AlarmEventService
 * 作       者：kun.zhao
 * 创建时间：2017/10/09
 * 修改编号：1
 * 描       述：报警事件逻辑层接口 
 */
@Service
public interface AlarmEventService {
	
	/**
     * 获取分页数据
     *
     * @param unitCodes     装置编码数组
     * @param prdtCellIds   生产单元ID数组
     * @param eventTypeIds  事件类型ID数组
     * @param workTeamIds   班组ID数组
     * @param alarmPointTag 报警点位号
     * @param alarmFlagIds  报警标识ID
     * @param priority      优先级
     * @param craftRank     级别
     * @param startTime     发生时间范围起始
     * @param endTime       发生时间范围结束
     * @param page          分页参数
     * @param isMatching
     * @return 报警事件实体集合
     * <AUTHOR> 2017-10-09
     */
	PaginationBean<AlarmEventEntity> getAlarmEvent(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds, Long[] workTeamIds
			, String alarmPointTag, Long[] alarmFlagIds, Integer priority, Integer craftRank, Date startTime, Date endTime, Pagination page, Integer isMatching) throws Exception;

	PaginationBean<AlarmEventTableVO> getAlarmEventP(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds, Long[] workTeamIds
			, String alarmPointTag, Long[] alarmFlagIds, Integer[] priority, Integer monitorType,Integer craftRank, Date startTime, Date endTime, Pagination page, Integer isMatching) throws Exception;

	List<AlarmEventTableVO> getAlarmEventExport(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds, Long[] workTeamIds
			, String alarmPointTag, Long[] alarmFlagIds, Integer[] priority, Integer monitorType,Integer craftRank, Date startTime, Date endTime, Integer isMatching) throws Exception;

	PaginationBean<AlarmEventTableVO> getAlarmEventRemoveP(String[] unitCodes, Long[] prdtCellIds, Long[] eventTypeIds, Long[] workTeamIds
			, String alarmPointTag, Long[] alarmFlagIds, Integer[] priority, Integer monitorType, Integer craftRank, Date startTime,
														   Date endTime, Pagination page, Integer isMatching,Integer delstatus) throws Exception;

	/**
	 * 获取全部事件类型数据
	 *
	 * <AUTHOR> 2017-10-10
	 * @return 事件类型树形实体集合
	 */
	List<EventTypeEntity> getAllEventType(Long id) throws Exception;

	/**
	 * 获取报警标识列表
	 * @param isAll
	 * @return
	 */
	List<DictionaryEntity> getAlarmFlagList(boolean isAll);


    void getSendMsgInfo(URL msgUrl) throws Exception;

    CommonResult getAlarmEventForInterface(String unitName, String prdtCellIds, Long[] eventTypeIds, /*Long[] workTeamIds*/String workUnitName, String alarmPointTag, Long[] alarmFlagId, Integer priority, Integer craftRank, Date startTime, Date endTime, String[] workUnitIds, Pagination page) throws Exception;

    List<MonitoringDataEntity> getMonitoringData() throws Exception;

	PaginationBean<MobileMsgListEntity> getAlarmMsgConfig(String[] unitCodes, Long[] prdtCellIds, String tag, Integer status, Date startSendTime, Date endSendTime, Pagination page) throws Exception;

    List<DictionaryEntity> getAlarmMsgConfigStatus();
	List<AlarmRec> getAlarmEventInfoByAlarmTime(String startDate, String end, Integer companyId);
	List<AlarmRec> getAlarmEventInfoByStartTime(String startDate,String end,Integer companyId);

    /**
	 * 根据事件id进行剔除数据
	 *
	 * @param eventIds 事件id
	 * @param reason 剔除原因
	 * @return 剔除结果
	 */
	String commitAlarmEvent(Long[] eventIds, String reason);

	String saveAlarmEvent(Long[] eventIds, String reason);
}
