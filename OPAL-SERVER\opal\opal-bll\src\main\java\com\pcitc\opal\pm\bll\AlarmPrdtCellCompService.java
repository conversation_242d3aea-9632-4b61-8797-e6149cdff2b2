package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.AlarmPrdtCellCompEntity;
import com.pcitc.opal.pm.dao.imp.DataServerMonDTO;
import com.pcitc.opal.pm.dao.imp.DataServerMonVO;
import com.pcitc.opal.pm.pojo.PrdtCell;
import org.springframework.stereotype.Service;

import java.util.List;

/*
 * 生产单元对照配置业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_AlarmPrdtCellCompService
 * 作	者：jiangtao.xue
 * 创建时间：2018/04/04
 * 修改编号：1
 * 描	述：生产单元对照配置业务逻辑层接口 
 */
@Service
public interface AlarmPrdtCellCompService {

	/**
	 * 新增数据
	 *
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompEntity 生产单元对照配置实体
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult addAlarmPrdtCellComp(AlarmPrdtCellCompEntity alarmPrdtCellCompEntity) throws Exception;

	/**
	 * 删除生产单元对照配置维护数据
	 *
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompIds 生产单元对照配置维护主键Id集合
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult deleteAlarmPrdtCellComp(Long[] alarmPrdtCellCompIds) throws Exception;

	/**
	 * 生产单元对照配置更新数据
	 *
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompEntity 生产单元对照配置实体
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult updateAlarmPrdtCellComp(AlarmPrdtCellCompEntity alarmPrdtCellCompEntity) throws Exception;

	/**
	 * 获取单条数据
	 *
	 * <AUTHOR> 2018-04-04
	 * @param alarmPrdtCellCompId 生产单元对照配置ID
	 * @return AlarmPrdtCellCompEntity 生产单元对照配置实体类
	 * @throws Exception
	 */
	AlarmPrdtCellCompEntity getSingleAlarmPrdtCellComp(Long alarmPrdtCellCompId) throws Exception;

	/**
	 * 获取分页数据
	 *
	 * <AUTHOR> 2018-04-04
	 * @param dcsCodeId DCS名称
	 * @param opcCodeId OPC名称
	 * @param prdtCellSource 源报警生产单元
	 * @param unitCodes 装置编码集合
	 * @param prdtCellIds   本系统生产单元
	 * @param inUse   是否使用
	 * @param page 翻页实现类
	 * @throws Exception 
	 * @return PaginationBean<AlarmPrdtCellCompEntity> 翻页对象
	 */
	PaginationBean<AlarmPrdtCellCompEntity> getAlarmPrdtCellComp(Long dcsCodeId, Long opcCodeId, String prdtCellSource, String[] unitCodes,Long[] prdtCellIds, Integer inUse, Pagination page) throws  Exception;
	/**
	 *根据生产单元名称获取源报警生产单元
	 *
	 * <AUTHOR> 2018-04-17
	 * @param prdtCellSource 源报警生产单元
	 * @throws Exception 
	 * @return AlarmPrdtCellCompEntity 实体
	 */
	AlarmPrdtCellCompEntity getPrdtCellInPrdtCellComp(String prdtCellSource,Long opcCode,Long dcsCode) throws Exception;

	List<DataServerMonVO> getDataServerMon(String unitCodes);

	List<PrdtCell> getPrdtCellByDcsName(Long dcsCodeId);
}
