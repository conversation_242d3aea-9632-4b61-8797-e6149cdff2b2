package com.pcitc.opal.ad.dao;

import java.util.Date;
import java.util.List;

import com.pcitc.opal.ad.pojo.AlarmEventViewEx;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

/*
 * AlarmEventViewEx视图的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmEventViewExRepositoryCustom
 * 作       者：dageng.sun
 * 创建时间：2017/10/25 
 * 修改编号：1
 * 描       述：AlarmEventViewEx视图的Repository的JPA自定义接口 
 */
public interface AlarmEventViewExRepositoryCustom {

	/**
	 * 报警响应查询页面
	 * 
	 * <AUTHOR> 2017-10-24
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 位号
	 * @param alarmFlagId 报警表示
	 * @param priority 优先级
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param isTimeResponse 是否及时响应
	 * @param dateRangeList 日期区间集合对象
	 * @param responseDuration 响应时长
	 * @param page 翻页实现类
	 * @return
	 * @throws Exception 
	 * @return PaginationBean<AlarmEventViewEx>   AlarmEventViewEx分页对象
	 */
	@SuppressWarnings("rawtypes")
	PaginationBean<AlarmEventViewEx> getAlarmRespond(String[] unitCodes, Long[] prdtCellIds, String tag, Long alarmFlagId, Integer priority, Date beginTime, Date endTime,
													 Integer isTimeResponse, List dateRangeList, Integer responseDuration, Pagination page)
            throws Exception;
	
	/**
	 * 报警响应及时的总数
	 * 
	 * <AUTHOR> 2017-10-25
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 位号
	 * @param alarmFlagId 报警表示
	 * @param priority 优先级
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param dateRangeList 班组时间集合
	 * @param responseDuration 响应时长
	 * @return
	 * @throws Exception 
	 * @return Long 
	 */
	@SuppressWarnings("rawtypes")
    Long getRespondTimeCount(String[] unitCodes,Long[] prdtCellIds, String tag,Long alarmFlagId,Integer priority,Date beginTime,Date endTime, List dateRangeList, Integer responseDuration)
            throws Exception;
	
	/**
	 * 符合条件的总数(不包含是否响应及时)
	 * 
	 * <AUTHOR> 2017-10-25
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 位号
	 * @param alarmFlagId 报警表示
	 * @param priority 优先级
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param dateRangeList 班组时间集合
	 * @param responseDuration 响应时长
	 * @return
	 * @throws Exception 
	 * @return Long 
	 */
	@SuppressWarnings("rawtypes")
    Long getRespondCount(String[] unitCodes,Long[] prdtCellIds,String tag,Long alarmFlagId,Integer priority,Date beginTime,Date endTime, List dateRangeList, Integer responseDuration)
            throws Exception;

	PaginationBean<AlarmEventViewEx> getAlarmRespondFor20S(String[] unitCodes, Long[] prdtCellIds, String tag, Long alarmFlagId, Integer priority, Date beginTime, Date endTime,
														   Integer isTimeResponse, List dateRangeList, Integer responseDuration, Pagination page) throws Exception;
}
