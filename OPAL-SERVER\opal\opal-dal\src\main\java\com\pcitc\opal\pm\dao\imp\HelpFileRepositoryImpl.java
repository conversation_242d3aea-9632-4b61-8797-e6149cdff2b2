package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.as.pojo.AlarmStdManagmt;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.HelpFileRepositoryCustom;
import com.pcitc.opal.pm.pojo.HelpFile;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.TypedQuery;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author:隋
 * 2019/9/19 0019
 */
public class HelpFileRepositoryImpl extends BaseRepository<HelpFile,Long> implements HelpFileRepositoryCustom {


    /**
     * 新增数据
     * @param helpFile
     * @return
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addHelpFile(HelpFile helpFile) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(helpFile);
            commonResult.setResult(helpFile);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }


    /**
     * 删除
     * @param helpFileId
     * @return
     */
    @Override
    public CommonResult deleteHelpFile(Long helpFileId) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            String hql = " from HelpFile t where t.helpFileId =:helpFileId";
            Map<String, Object> paramList = new HashMap<String, Object>();
            //List<Long> alarmStdManagmtIdsList = Arrays.asList(alarmStdManagmtIds);
            paramList.put("helpFileId", helpFileId);

            TypedQuery<HelpFile> query = getEntityManager().createQuery(hql, HelpFile.class);
            this.setParameterList(query, paramList);
            List<HelpFile> helpFileList = query.getResultList();
            helpFileList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 删除出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }


    /**
     * 获取帮助文档
     * @param page
     * @return
     */
    @Override
    public PaginationBean<HelpFile> getHelpFile(Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from HelpFile t where 1=1 order by t.upLoadTime desc ");

            Map<String, Object> paramList = new HashMap<String, Object>();
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
    }


    @Override
    public HelpFile getSingleHelpFile(Long helpFileId) {
        try {
            return getEntityManager().find(HelpFile.class,helpFileId);
        }catch (Exception e){
            throw e;
        }

    }

    @Override
    public CommonResult hrlpFileValidation(HelpFile helpFile) {
        CommonResult commonResult = new CommonResult();
        try {
            // “名称”唯一性校验，提示“此报警管理制度名称已存在！”。
            StringBuilder hql = new StringBuilder(
                    "from HelpFile t where t.fileName =:fileName and t.helpFileId<>:helpFileId");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("fileName", helpFile.getFileName());
            paramList.put("helpFileId", helpFile.getHelpFileId() == null ? 0 : helpFile.getHelpFileId());

            long index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("此报帮助文档名称已存在！");
            }
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }
}
