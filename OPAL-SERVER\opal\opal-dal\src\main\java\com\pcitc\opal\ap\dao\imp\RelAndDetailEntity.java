package com.pcitc.opal.ap.dao.imp;


/*
 * 报警点实体
 * 模块编号：pcitc_pojo_class_Group
 * 作       者：guoganxin
 * 创建时间：2023/04/16
 * 修改编号：1
 * 描       述：群组
 */
public class RelAndDetailEntity {
    public RelAndDetailEntity(Long apRuleUnitRelId, String alarmPushRuleName, Long priority, Integer alarmSpeciality, String des) {
        this.apRuleUnitRelId = apRuleUnitRelId;
        this.alarmPushRuleName = alarmPushRuleName;
        this.priority = priority;
        this.alarmSpeciality = alarmSpeciality;
        this.des = des;
    }

    /**
     * 报警推送规则装置关系ID
     */
    private Long apRuleUnitRelId;

    /**
     * 报警推送规则名称
     */
    private String alarmPushRuleName;

    /**
     * 优先级（1 紧急；2重要；3 一般）
     */
    private Long priority;

    /**
     * 报警专业（1 工艺、2 设备、3 安全、4 环保、5 质量、6 火灾）
     */
    private Integer alarmSpeciality;

    /**
     * 描述
     */
    private String des;

    public Long getApRuleUnitRelId() {
        return apRuleUnitRelId;
    }

    public void setApRuleUnitRelId(Long apRuleUnitRelId) {
        this.apRuleUnitRelId = apRuleUnitRelId;
    }

    public String getAlarmPushRuleName() {
        return alarmPushRuleName;
    }

    public void setAlarmPushRuleName(String alarmPushRuleName) {
        this.alarmPushRuleName = alarmPushRuleName;
    }

    public Long getPriority() {
        return priority;
    }

    public void setPriority(Long priority) {
        this.priority = priority;
    }

    public Integer getAlarmSpeciality() {
        return alarmSpeciality;
    }

    public void setAlarmSpeciality(Integer alarmSpeciality) {
        this.alarmSpeciality = alarmSpeciality;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
