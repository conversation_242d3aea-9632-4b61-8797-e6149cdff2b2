package com.pcitc.opal.common.bll.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;
import java.util.Date;

/*
 * 装置实体
 * 模块编号：pcitc_opal_common_class_UnitVO
 * 作    者：xuelei.wang
 * 创建时间：2018/06/26
 * 修改编号：1
 * 描    述：装置实体
 */
public class UnitVO extends BaseResRep implements Serializable {
    /**
     * 装置编码
     */
    private String plantCode;
    /**
     * 装置名称
     */
    private String plantName;
    /**
     * 装置简称
     */
    private String plantAlias;
    /**
     * 排序
     */
    private Integer sortNum;
    /**
     * 描述
     */
    private String des;
    /**
     * 是否启用
     */
    private Integer inUse;
    /**
     * 资产原值
     */
    private Double initialAssetValue;

    /**
     * 资产净值
     */
    private Double netAssetValue;
    /**
     * 容积
     */
    private Double capacity;
    /**
     * 装置类型编码
     */
    private String plantTypeCode;
    /**
     * 装置类型名称
     */
    private String plantTypeName;
    /**
     * 所属组织机构编码
     */
    private String orgCode;
    /**
     * 所属组织机构名称
     */
    private String orgName;
    /**
     * 所属组织机构简称
     */
    private String orgAlias;

    public String getPlantCode() {
        return plantCode;
    }

    public void setPlantCode(String plantCode) {
        this.plantCode = plantCode;
    }

    public String getPlantName() {
        return plantName;
    }

    public void setPlantName(String plantName) {
        this.plantName = plantName;
    }

    public String getPlantAlias(){
        return plantAlias;
    }

    public void setPlantAlias(String plantAlias){
        this.plantAlias = plantAlias;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public Integer getInUse() {
        return inUse;
    }

    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }

    public Double getInitialAssetValue() {
        return initialAssetValue;
    }

    public void setInitialAssetValue(Double initialAssetValue) {
        this.initialAssetValue = initialAssetValue;
    }

    public Double getNetAssetValue() {
        return netAssetValue;
    }

    public void setNetAssetValue(Double netAssetValue) {
        this.netAssetValue = netAssetValue;
    }

    public Double getCapacity() {
        return capacity;
    }

    public void setCapacity(Double capacity) {
        this.capacity = capacity;
    }

    public String getPlantTypeCode() {
        return plantTypeCode;
    }

    public void setPlantTypeCode(String plantTypeCode) {
        this.plantTypeCode = plantTypeCode;
    }

    public String getPlantTypeName() {
        return plantTypeName;
    }

    public void setPlantTypeName(String plantTypeName) {
        this.plantTypeName = plantTypeName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgAlias() {
        return orgAlias;
    }

    public void setOrgAlias(String orgAlias) {
        this.orgAlias = orgAlias;
    }
}
