package com.pcitc.opal.ad.dao;

import com.pcitc.opal.ad.pojo.AlarmEventView;

import java.util.Date;
import java.util.List;

/*
 * 事件类型每10分钟数据视图实体的Repository的JPA自定义接口
 * 模块编号：pcitc_opal_dal_interface_AlarmEventViewRepositoryCustom
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/19
 * 修改编号：1
 * 描    述：事件类型每10分钟数据视图实体的Repository的JPA自定义接口
 */
public interface AlarmEventViewRepositoryCustom {

	/**
	 *获取事件类型每10分钟数据集合
	 *
	 * <AUTHOR> 2017-10-19
	 * @param startTime     发生时间范围起始
	 * @param endTime       发生时间范围结束
	 * @return 报警事件类型每10分钟数据集合
	 */
	List<AlarmEventView> getAlarmEventViewData(Date startTime, Date endTime, String[] unitCodes);
	/**
	 * 根据装置或者生产单元和时间区间获取高频报警清单列表
	 *
	 * @param startTime 开始时间
	 * @param endTime   结束时间
	 * @param unitCodes  装置编码集合
	 * @param prdtIds   生产单元集合
	 * @return 高频报警清单列表
	 * <AUTHOR> 2017-11-15
	 */
	List<AlarmEventView> getFloodAlarmAnalysisList(Date startTime,Date endTime,String[] unitCodes,Long[] prdtIds);
}
