package com.pcitc.opal.pm.dao.imp;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;
import javax.persistence.TypedQuery;

import com.pcitc.opal.common.*;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.FactoryRepositoryCustom;
import com.pcitc.opal.pm.pojo.Factory;

/*
 * Factory实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_FactoryRepositoryImpl
 * 作       者：kun.zhao
 * 创建时间：2017/12/11
 * 修改编号：1
 * 描       述：Factory实体的Repository实现   
 */
public class FactoryRepositoryImpl extends BaseRepository<Factory, Long> implements FactoryRepositoryCustom {

	/**
	 * 校验
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryPO 工厂实体
	 * @return 返回结果信息类
	 */
	@Override
	public CommonResult factoryValidation(Factory factoryPO) {
		CommonResult commonResult = new CommonResult();
		try {
            // “名称”唯一性校验，提示“该工厂名称已存在！”;
            StringBuilder hql = new StringBuilder(
                    "from Factory t where t.name =:name and t.companyId =:companyId and t.factoryId<>:factoryId");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("name", factoryPO.getName());
            paramList.put("companyId", factoryPO.getCompanyId());
            paramList.put("factoryId", factoryPO.getFactoryId() == null ? 0 : factoryPO.getFactoryId());
            long index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("该工厂名称已存在！");
            }

            // “简称”唯一性校验，提示“该工厂简称已存在！”;
            hql = new StringBuilder(
                    "from Factory t where t.sname =:sname and t.companyId =:companyId and t.factoryId<>:factoryId");
            paramList = new HashMap<String, Object>();
            paramList.put("sname", factoryPO.getSname());
            paramList.put("companyId", factoryPO.getCompanyId());
            paramList.put("factoryId", factoryPO.getFactoryId() == null ? 0 : factoryPO.getFactoryId());
            index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("该工厂简称已存在！");
            }

            // “标准编码”唯一性校验，提示“该标准编码已存在！”;
            hql = new StringBuilder(
                    "from Factory t where t.stdCode =:stdCode and t.factoryId<>:factoryId");
            paramList = new HashMap<String, Object>();
            paramList.put("stdCode", factoryPO.getStdCode());
            paramList.put("factoryId", factoryPO.getFactoryId() == null ? 0 : factoryPO.getFactoryId());
            index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("该标准编码已存在！");
            }
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
	}
	
	/**
     * 新增工厂
     * 
     * <AUTHOR> 2017-12-11
     * @param factoryPO 工厂实体
     * @return 返回结果信息类
     */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addFactory(Factory factoryPO) {
		 // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(factoryPO);
            commonResult.setResult(factoryPO);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
	}
	
	/**
     * 删除工厂
     *
     * <AUTHOR> 2017-12-11
     * @param factoryIds 工厂ID集合
     * @return 返回结果信息类
     */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteFactory(Long[] factoryIds) {
		// 初始化消息结果类
        CommonResult commonResult = new CommonResult();

            String hql = " from Factory t where t.factoryId in (:factoryIds)";
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> factoryIdsList = Arrays.asList(factoryIds);
            paramList.put("factoryIds", factoryIdsList);

            TypedQuery<Factory> query = getEntityManager().createQuery(hql, Factory.class);
            this.setParameterList(query, paramList);
            List<Factory> factoryList = query.getResultList();
            factoryList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");

        // 返回消息结果对象
        return commonResult;
	}
	
	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryPO 工厂实体
	 * @return 返回结果信息类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult updateFactory(Factory factoryPO) {
		// 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(factoryPO);
            commonResult.setResult(factoryPO);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
	}

	
	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryId 工厂ID
	 * @return 工厂实体
	 */
	@Override
	public Factory getSingleFactory(Long factoryId) {
		try {
            return getEntityManager().find(Factory.class, factoryId);
        } catch (Exception ex) {
            throw ex;
        }
	}

	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryIds 工厂ID数组
	 * @return 工厂实体集合
	 */
	@Override
	public List<Factory> getFactory(Long[] factoryIds) {
		try {
            CommonProperty commonProperty = new CommonProperty();
            Integer companyId = commonProperty.getCompanyId();
            // 查询字符串
            String hql = "from Factory t where 1=1 ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (companyId != null){
                hql += " and t.companyId =:companyId";
                paramList.put("companyId", companyId.longValue());
            }
            if (ArrayUtils.isNotEmpty(factoryIds)) {
                hql += " and t.factoryId in (:factoryIds)";
                List<Long> factoryIdsList = Arrays.asList(factoryIds);
                paramList.put("factoryIds", factoryIdsList);
            } else {
                hql += " and t.inUse = :inUse ";
                paramList.put("inUse", CommonEnum.InUseEnum.Yes.getIndex());
            }
            TypedQuery<Factory> query = getEntityManager().createQuery(hql, Factory.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
	}

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param name    工厂名称/简称
	 * @param stdCode 标准编码
	 * @param inUse   是否启用
	 * @param page    分页参数
	 * @return 工厂实体（分页）
	 */
	@Override
	public PaginationBean<Factory> getFactory(Long companyId, String name, String stdCode, Integer inUse, Pagination page) {
		try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from Factory t where 1=1 ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            //
            if (null != companyId){
                hql.append(" and t.companyId = :companyId ");
                paramList.put("companyId",companyId);
            }
            // 名称/简称
            if (!StringUtils.isEmpty(name)) {
                hql.append(" and (t.name like :name escape '/' or t.sname like :name escape '/') ");
                paramList.put("name", "%" + this.sqlLikeReplace(name) + "%");
            }
            if (!StringUtils.isEmpty(stdCode)) {
                hql.append(" and (upper(t.stdCode) like upper(:stdCode) escape '/') ");
                paramList.put("stdCode", "%" + this.sqlLikeReplace(stdCode) + "%");
            }
            if (inUse != null) {
                hql.append(" and t.inUse = :inUse ");
                paramList.put("inUse", inUse);
            }
            hql.append(" order by t.sortNum, t.name, t.sname ");
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
	}

    @Override
    public List<Factory> getFactoryByCompanyId(Long conpanyId) {
        String hql = "from Factory where companyId = :companyId";
        Query query = getEntityManager().createQuery(hql);
        query.setParameter("companyId", conpanyId);
        return query.getResultList();
    }

}
