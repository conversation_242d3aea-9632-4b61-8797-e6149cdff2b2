var searchUrl = OPAL.API.aeUrl + '/alarmScreen';
var exportAlarmScreenUrl = OPAL.API.aeUrl + '/alarmScreen/exportAlarmScreen';
var alarmFlagListUrl = OPAL.API.commUrl + '/getAlarmFlagList';
var alarmPriorityListUrl = OPAL.API.commUrl + '/getAlarmPriorityList';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var workTeamUrl = OPAL.API.commUrl + "/getWorkTeam";
var isLoading = true;
$(function() {
    var page = {
        /**
         * 初始化
         */
        init: function() {
            this.bindUi();
            //扩展日期插件
            OPAL.util.extendDate();
            // 初始化 时间设置
            page.logic.initTime();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化表格
            page.logic.initTable();
            //初始化报警标识
            page.logic.initAlarmFlagList();
            //初始化优先级
            page.logic.initAlarmPriorityList();
            //初始化事件类型
            page.logic.initEventType();
            $('#workTeamIds').html("");
            $("#workTeamIds").prop('disabled', true);

            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("AlarmScreen");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
            //默认查询数据
            setTimeout(function () {
                if ($("#alarmFlagId").val()!=null&&$("#priority").val()!=null) {
                    page.logic.search();
                }
            }, 500);
        },
        bindUi: function() {
            //查询
            $('#search').click(function() {
                isLoading = false;
                page.logic.search();
            })
            // 导出
            $('#AlarmScreenExport').click(function() {
                page.logic.exportExcel();
            });
            $("#startTime").unbind("change");
            $("#endTime").unbind("change");
            $("#startTime").change(function() {
                page.logic.initWorkTeam();
            })
            $("#endTime").change(function() {
                page.logic.initWorkTeam();
            })
        },
        data: {
            // 设置查询参数
            param: {}
        },
        logic: {
            initTable: function() {
                OPAL.ui.initBootstrapTable2("table", {
                    striped: true, 
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1 ) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "屏蔽/取消屏蔽时间",
                        field: 'startTime',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    },  {
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "班组",
                        field: 'workTeamSName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "位号",
                        field: 'alarmPointTag',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "参数名称",
                        field: 'location',
                        rowspan: 1,
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "事件类型",
                        field: 'eventTypeName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "报警等级",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "优先级",
                        field: 'priorityName',
                        rowspan: 1,
                        align: 'center',
                        width: '60px'
                    }]
                }, page.logic.queryParams,"search")
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                };
                return $.extend(page.data.param,param);
            },
            /**
             * 初始化 时间
             */
            initTime: function() {
                OPAL.ui.initDateTimePeriodPicker({
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm:ss'
                },function(){
                    page.logic.initWorkTeam();
                })
            },
            /**
             * 设置参数
             */
            setParams: function() {
                page.data.param = OPAL.form.getData("searchForm");
                page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
            },
            /**
             * 搜索
             */
            search: function() {
                if (!OPAL.util.checkDateIsValid()) {
                    return false;
                }
                page.logic.setParams();
                $("#search").attr('disabled', true);
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function(nodes) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $("#workTeamIds").prop('disabled', false);
                            page.logic.initWorkTeam();
                            $('.textbox,.combo').css('background-color','');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('#workTeamIds').html("");
                            $("#workTeamIds").prop('disabled', true);
                            $('.textbox-disabled').css('background-color','rgb(235, 235, 228)');
                        }
                    }
                }, false);
            },
            /**
             * 初始化查询 报警标识
             */
            initAlarmFlagList: function() {
                OPAL.ui.getCombobox("alarmFlagId", alarmFlagListUrl, {
                    selectFirstRecord: true,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            /**
             * 初始化查询 优先级
             */
            initAlarmPriorityList: function() {
                OPAL.ui.getCombobox("priority", alarmPriorityListUrl, {
                    selectValue: -1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            initEventType: function() {
                var data = [
                    {key:-1,value:"全部"},
                    {key:3003,value:"屏蔽记录"},
                    {key:3004,value:"取消屏蔽记录"},
                ]
                OPAL.ui.getComboboxByData("eventTypeId", data, {
                    selectFirstRecord: true,
                }, null);
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function() {
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化班组选择
             */
            initWorkTeam: function() {
                var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                if (unitIds.length != 1) return;
                if ($("#startTime").val() == '' || $("#endTime").val() == ''){
                    $('#workTeamIds').html("");
                    $("#workTeamIds").prop('disabled', true);
                    return;
                }else {
                    $("#workTeamIds").prop('disabled', false);
                }
                OPAL.ui.getCombobox("workTeamIds", workTeamUrl, {
                    keyField: "workTeamId",
                    valueField: "workTeamSName",
                    selectFirstRecord: true,
                    mapManyValues: true, //是否一条记录匹配多个隐藏值
                    mapManyDataFieldName: 'workTeamIdList',
                    data: {
                        "startTime": $("#startTime").val(),
                        "endTime": $("#endTime").val(),
                        "unitId": unitIds[0],
                    }
                }, null);
            },
            /**
             * 导出
             */
            exportExcel: function() {
                var titleArray = new Array();
                var tableTitle = $('#table').bootstrapTable('getOptions').columns[0];
                $.each(tableTitle, function(i, el) {
                    if (i >= 1) {
                        titleArray.push({
                            'key': el.field,
                            'value': el.title
                        })
                    }
                })
                var data = {};
                var pageSize = $('#table').bootstrapTable('getOptions').pageSize;
                var pageNumber = $('#table').bootstrapTable('getOptions').pageNumber;
                data.titles = JSON.stringify(titleArray);
                data.pageSize = pageSize;
                data.pageNumber = pageNumber;
                page.logic.setParams();
                $.extend(data, page.data.param);
                $('#form').attr('action', exportAlarmScreenUrl);
                $('#titles').val(data.titles);
                $('#pageSize').val(data.pageSize);
                $('#pageNumber').val(data.pageNumber);
                $('#unitIdsExport').val(data.unitIds);
                $('#prdtCellIdsExport').val(data.prdtCellIds);
                $('#alarmPointTagForm').val(data.alarmPointTag);
                $('#alarmFlagIdExport').val(data.alarmFlagId);
                $('#priorityForm').val(data.priority);
                $('#startTimeForm').val(data.startTime);
                $('#endTimeForm').val(data.endTime);
                $('#workTeamIdsForm').val(data.workTeamIds);
                $("#eventTypeIdForm").val(data.eventTypeId);
                $('#form').submit();
            }

        }
    }
    page.init();
    window.page = page;
});