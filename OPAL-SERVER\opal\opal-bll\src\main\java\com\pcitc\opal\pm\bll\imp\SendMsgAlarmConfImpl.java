package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.aa.bll.imp.AlarmAssessFirstPageImpl;
import com.pcitc.opal.ad.dao.MobileMsgListRepository;
import com.pcitc.opal.ad.pojo.MobileMsgList;
import com.pcitc.opal.pm.bll.SendMsgAlarmConfService;
import com.pcitc.opal.pm.bll.SendMsgService;
import com.pcitc.opal.pm.bll.entity.SendMsgEntity;
import com.pcitc.opal.pm.dao.MobileListRepository;
import com.pcitc.opal.pm.pojo.AlarmRecVo;
import com.pcitc.opal.pm.pojo.MobileList;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


//@Component
@Service
public class SendMsgAlarmConfImpl implements SendMsgAlarmConfService {
    private static final Logger logger = LoggerFactory.getLogger(AlarmAssessFirstPageImpl.class);
    @Resource
    MobileListRepository mobileListRepository;

    @Resource
    MobileMsgListRepository mobileMsgListRepository;

    @Resource
    private SendMsgService sendMsgService;


    @Override
    public String saveMobileMsg(String content, AlarmRecVo alarmRecVo, Integer companyId) {

        StringBuilder result = new StringBuilder();

        ArrayList<Long> ids = new ArrayList<>();
        for (String s : alarmRecVo.getMobileListIds().split(",")) {
            ids.add(Long.parseLong(s));
        }
        //通过手机号id去查询手机号
        List<MobileList> mobileListByIds = mobileListRepository.getMobileListByIds(ids);

        //转换为手机号列表
        List<String> mobiles = mobileListByIds.stream().map(MobileList::getMobile).collect(Collectors.toList());
        Map<String, String> sendBatch = sendMsgService.sendBatch(mobiles, content);

        //开始插入报警短信记录表
        for (MobileList mobileListById : mobileListByIds) {
            MobileMsgList mobileMsgList = new MobileMsgList();
            mobileMsgList.setFactoryId(mobileListById.getFactoryId());
            mobileMsgList.setWorkshopId(mobileListById.getWorkshopId());
            mobileMsgList.setUnitCode(alarmRecVo.getStdCode());
            mobileMsgList.setPrdtCellId(alarmRecVo.getPrdtCellId());
            mobileMsgList.setTag(alarmRecVo.getTag());
            mobileMsgList.setAlarmFlagId(alarmRecVo.getAlarmFlagId());
            mobileMsgList.setAlarmTime(alarmRecVo.getAlarmTime());
            mobileMsgList.setName(mobileListById.getName());
            mobileMsgList.setMobile(mobileListById.getMobile());
            mobileMsgList.setContent(content);
            mobileMsgList.setCompanyId(companyId);
            //发送短信并且将返回结果放入对象中
            String send = sendBatch.get(mobileListById.getMobile());
            //1发送成功，2发送失败
            mobileMsgList.setStatus(send.contains("发送成功") ? 1 : 2);
            result.append(send).append("\n");
            mobileMsgList.setResult(send);
            mobileMsgList.setSendTime(new Date());
            mobileMsgListRepository.save(mobileMsgList);
        }
        return result.toString();
    }

    @Override
    public String saveMobileMsg(String content, SendMsgEntity sendMsgEntity, Integer companyId) {

        StringBuilder result = new StringBuilder();

        //转换为手机号列表
        List<MobileList> mobiles = sendMsgEntity.getMobiles();

        //取出手机号
        List<String> mob = mobiles.stream().map(MobileList::getMobile).collect(Collectors.toList());

        Map<String, String> sendBatch = sendMsgService.sendBatch(mob, content);

        //开始插入报警短信记录表
        for (MobileList mobileListById : mobiles) {
            MobileMsgList mobileMsgList = new MobileMsgList();
            mobileMsgList.setFactoryId(mobileListById.getFactoryId());
            mobileMsgList.setWorkshopId(mobileListById.getWorkshopId());
            mobileMsgList.setUnitCode(sendMsgEntity.getStdCode());
            mobileMsgList.setPrdtCellId(sendMsgEntity.getPrdtCellId());
            mobileMsgList.setTag(sendMsgEntity.getTag());
            mobileMsgList.setAlarmFlagId(sendMsgEntity.getAlarmFlagId());
            mobileMsgList.setAlarmTime(sendMsgEntity.getAlarmTime());
            mobileMsgList.setName(mobileListById.getName());
            mobileMsgList.setMobile(mobileListById.getMobile());
            mobileMsgList.setContent(content);
            mobileMsgList.setCompanyId(companyId);
            //发送短信并且将返回结果放入对象中
            String send = sendBatch.get(mobileListById.getMobile());
            //1发送成功，2发送失败
            mobileMsgList.setStatus(send.contains("发送成功") ? 1 : 2);
            result.append(send).append("\n");
            mobileMsgList.setResult(send);
            mobileMsgList.setSendTime(new Date());
            mobileMsgListRepository.save(mobileMsgList);
        }
        return result.toString();
    }

}
