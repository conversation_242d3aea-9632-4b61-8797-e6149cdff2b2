package com.pcitc.opal.ad.bll;

import com.pcitc.opal.ad.bll.entity.AlarmEventDelApproveEntity;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

import java.util.Date;

public interface AlarmEventDelApproveService {
    /**
     * 分页查询审批信息
     *
     * @param unitCode  装置
     * @param delStatus 审批状态
     * @param page      分页对象
     */
    PaginationBean<AlarmEventDelApproveEntity> getAlarmEventDelApprove(String[] unitCode, Integer[] priority,
                                                                       Long[] alarmFlagId, Integer monitorType,
                                                                       Date startTime, Date endTime, String alarmPointTag,
                                                                       Integer delStatus, Pagination page);

    /**
     * 审批
     *
     * @param alarmEventDelApproveId 主键id
     */
    String approvePass(Integer[] alarmEventDelApproveId);

    /**
     * 驳回
     *
     * @param alarmEventDelApproveId 主键id
     */
    String reject(Integer[] alarmEventDelApproveId);

    /**
     * 更新剔除原因
     *
     * @param alarmEventDelApproveId 主键id
     * @param reason                 原因
     */
    String updateReason(Integer alarmEventDelApproveId, String reason);

    /**
     * 删除剔除配置
     *
     * @param alarmEventDelApproveId 主键id
     */
    String removeAlarmEventDelApprove(Integer[] alarmEventDelApproveId);

}
