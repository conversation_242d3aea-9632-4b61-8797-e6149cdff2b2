package com.pcitc.opal.pm.bll.entity;
import com.pcitc.opal.common.bll.entity.BasicEntity;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <p>
 * 报警点分组明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-01
 */
@Data
public class AlarmPointDelConfigEntity extends BasicEntity{

    /**
     * 报警剔除配置ID
     */
    private Long alarmPointDelConfigId;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 装置编码
     */
    private String unitCode;

    /**
     * 报警点分组ID
     */
    private Long alarmPointGroupId;

    /**
     * 剔除开始时间
     */
    private Date delStartTime;

    /**
     * 剔除结束时间
     */
    private Date delEndTime;

    /**
     * 描述
     */
    private String des;

    /**
     * 剔除状态（0未提交；1已提交；2已通过；3已驳回）
     */
    private Integer delStatus;

    /**
     * 数据剔除状态 (0未剔除，1剔除中，2事件表剔除失败，3记录表剔除失败，4剔除失败，5数据已剔除)
     */
    private Integer delDataStatus;

}
