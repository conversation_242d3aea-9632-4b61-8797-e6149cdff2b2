package com.pcitc.opal.ae.bll;

import java.util.Date;

import org.springframework.stereotype.Service;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

/*
 * 报警坏点分布业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_AlarmBadpvDistributionService
 * 作       者：dageng.sun
 * 创建时间：2017/10/23
 * 修改编号：1
 * 描       述：报警坏点分布业务逻辑层接口 
 */
@Service
public interface AlarmBadpvDistributionService {
	
	/**
	 * 分页获取数据
	 * 
	 * <AUTHOR> 2017-10-23
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 位号
	 * @param priority 优先级
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @param workTeamIds 班组编号
	 * @param page 翻页实现类
	 * @throws Exception 
	 * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity分页对象
	 */
	@Deprecated
	 PaginationBean<AlarmEventEntity> getAlarmBadpvDistribution(String[] unitCodes, Long[] prdtCellIds, String tag, Integer priority, Date beginTime, Date endTime, Long[] workTeamIds, Pagination page)
            throws Exception;
	/**
	 * 获取分页数据
	 *
	 * <AUTHOR> 2018-11-19
	 * @param unitCodes       装置编码数组
	 * @param prdtCellIds   生产单元id数组
	 * @param tag           位号
	 * @param beginTime     报警事件的开始间
	 * @param endTime       报警事件的结束时间
	 * @param page          翻页实现类
	 * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity分页对象
	 * @throws Exception 
	 */
	PaginationBean<AlarmEventEntity> getAlarmBadpvDistribution(String[] unitCodes, Long[] prdtCellIds, String tag,
															   Date beginTime, Date endTime, Pagination page) throws Exception;
	/**
	 * 获取报警坏点详情分页数据
	 *
	 * <AUTHOR> 2018-11-19
	 * @param alarmPointId   报警点id
	 * @param beginTime     报警事件的开始间
	 * @param endTime       报警事件的结束时间
	 * @param page          翻页实现类
	 * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity分页对象
	 * @throws Exception 
	 */
	PaginationBean<AlarmEventEntity> getAlarmBadpvDistributionDtl(Long alarmPointId, Date beginTime, Date endTime,Pagination page) throws Exception;
}
