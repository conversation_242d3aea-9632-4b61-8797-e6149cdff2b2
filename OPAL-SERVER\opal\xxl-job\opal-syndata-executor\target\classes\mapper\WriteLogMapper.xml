<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.mapper.WriteLogMapper">

    <resultMap id="BaseResultMap" type="com.pcitc.opal.pojo.WriteLog">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="currentEventId" column="current_event_id" jdbcType="INTEGER"/>
            <result property="writeTime" column="write_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,current_event_id,write_time
    </sql>
    <select id="getMaxCurrentEventIdByEnterpriseDb" resultType="Integer">
    select max(current_event_id) current_event_id from t_write_log limit 1


    </select>


</mapper>
