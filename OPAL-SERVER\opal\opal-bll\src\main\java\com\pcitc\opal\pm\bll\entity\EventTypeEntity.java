package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 事件类型实体  
 * 模块编号：pcitc_opal_bll_class_EventTypeEntity
 * 作       者：kun.zhao
 * 创建时间：2017/10/09
 * 修改编号：1
 * 描       述：事件类型实体     
 */
public class EventTypeEntity extends BasicEntity {
	 
	/**
     *事件类型ID
     */
    private Long eventTypeId;
    
    /**
     *名称
     */
    private String name;

    /**
     *上级ID
     */
    private Long parentId;

    /**
     *排序
     */
    private Integer sortNum;

	public Long getEventTypeId() {
		return eventTypeId;
	}

	public void setEventTypeId(Long eventTypeId) {
		this.eventTypeId = eventTypeId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public Integer getSortNum() {
		return sortNum;
	}

	public void setSortNum(Integer sortNum) {
		this.sortNum = sortNum;
	}
    
}
