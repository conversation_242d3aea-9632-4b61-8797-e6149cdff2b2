var searchUrl = OPAL.API.afUrl+ '/relevantTagConfig/getRelevantTagConfigDtlAdd';
var saveUrl = OPAL.API.afUrl+ '/relevantTagConfig/addRelevantTagConfigDtl';
var UnitPrdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
window.pageLoadMode = PageLoadMode.None;
$(function () {
    var index = parent.layer.getFrameIndex(window.name);
    var page = {
        /**
         * 初始化
         */
        init: function () {
            //绑定事件
            this.bindUI();
            //初始化查询装置树
            
        },
        /**
         * 绑定事件
         */
        bindUI: function () {
            // 新增
            $('#confirmBtn').click(function () {
                page.logic.save();
            })
            //批量删除
            $('#closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
            //查询
            $('#searchBtn').click(function () {
                page.logic.search();
            })
        },
        data: {
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                page.logic.initUnitTree();
                //初始化表格
                page.logic.initTable();
                $("#relevantTagConfigId").val(data.relevantTagConfigId)
                $("#unitId").combotree('setValue', data['unitId']);
                page.logic.searchUnitPrdt(data["unitId"]);
                // $("#prdtCellId").combotree('setValue', data["prdtCellId"]);
                $("#unitId").combo('disable');
            },
            /**
             * 初始化表格
             */
            initTable: function () {
                OPAL.ui.initBootstrapTable("table",{
                    cache:false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "生产单元",
                        field: 'prdtCellSname',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "装置",
                        field: 'unitSname',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "计量单位",
                        field: 'measunitName',
                        rowspan: 1,
                        align: 'left',
                    },{
                        title: "描述",
                        field: 'des',
                        rowspan: 1,
                        align: 'left',
                    }],
                },page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param,param);
            },
            /**
             * 搜索
             */
            search: function () {
                page.data.param = OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber":1
                });
            },
            save: function() {
                var selectRows = $("#table").bootstrapTable("getSelections");
                if(selectRows.length == 0) {
                    layer.msg("请选择相关位号！");
                    return;
                }
                var arr = new Array();
                for(x in selectRows) {
                    arr.push(selectRows[x].alarmPointId);
                }
                var data = {
                    "relevantTagConfigId" : $("#relevantTagConfigId").val(),
                    "alarmPointIds": arr,
                }
                $.ajax({
                    url: saveUrl,
                    async: false,
                    type: "get",
                    // data: JSON.stringify(data),
                    data:data,
                    // contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！",{
                                time: 1000
                            },function() {
                                window.pageLoadMode = PageLoadMode.Refresh;
                                page.logic.closeLayer();
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        window.pageLoadMode = PageLoadMode.None;
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect("unitId", commonUnitTreeUrl, "id", "parentId", "sname", {
                    multiple: false,
                    onlyLeafCheck: true,
                    async:false,
                    data: {
                        'enablePrivilege': false
                    },
                }, false, function() {
                });
                $("#unitId").combotree("getValues");
            },

            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getCombobox("prdtCellIds", UnitPrdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    async: false,
                    selectFirstRecord: true,
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    },
                }, function(){
                    $("#prdtCellIds option:first").text('全部');
                }, function() {
                    $("#prdtCellIds option:first").text('全部');
                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
        }
    }
    page.init();
    window.page = page;
})