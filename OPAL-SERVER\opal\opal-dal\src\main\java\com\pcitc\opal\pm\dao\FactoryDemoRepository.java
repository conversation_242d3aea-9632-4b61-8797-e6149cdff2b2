package com.pcitc.opal.pm.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.pm.pojo.FactoryDemo;

/*
 * Factory实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_FactoryRepository
 * 作       者：dongsheng.zhao
 * 创建时间：2017/09/17
 * 修改编号：1
 * 描       述：Factory实体的Repository实现   
 */
public interface FactoryDemoRepository extends JpaRepository<FactoryDemo, Long>, FactoryDemoRepositoryCustom {

}
