package com.pcitc.opal.common;

import com.alibaba.fastjson.JSON;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.authenticateservice.old.IAuthenticateServiceProxy;
import com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.authenticateservice.old.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.rmi.RemoteException;
import java.util.Arrays;
import java.util.Date;
import java.util.Enumeration;

/*
 * 用户属性公共方法
 * 模块编号：pcitc_opal_common_class_CommonProperty
 * 作	者：dongsheng.zhao
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描	述：用户属性公共方法
 */
@Slf4j
public class CommonProperty {

    /**
     * 注入方式获取获取request
     */
    //@Autowired
    //private HttpServletRequest requestByCas;

    /**
     * 获取当前用户ID
     */
    private String userId;

    /**
     * 获取当前用户名称
     */
    private String userName;

    /**
     * 获取数据库服务器系统时间
     */
    private Date systemDateTime = new Date();

    /**
     * 获取用户code
     *
     * @return 用户code
     * <AUTHOR> 2017-12-21
     */

    public String getUserId() {
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        // 获取请求体 request
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        String uname = request.getHeader("username");
        if (StringUtils.isNotBlank(uname)) {
            log.info(" [header]: " + uname);
            return uname;
        } else {
            Object obj = requestAttributes.getAttribute("UserName", 0);
            String userId = null;
            if (null != obj) {
                userId = String.valueOf(obj);
                System.out.println("用户ID打印----------------------------------" + userId);
                return userId;
            }
            obj = requestAttributes.getAttribute("userName", 0);

            if (null != obj) {
                userId = String.valueOf(obj);
                System.out.println("用户ID打印----------------------------------" + userId);
                return userId;
            }
            obj = requestAttributes.getAttribute("loginName", 0);

            if (null != obj) {
                userId = String.valueOf(obj);
                System.out.println("用户ID打印----------------------------------" + userId);
                return userId;
            }
        }
        return "admin";
//        return "mes4test1";


//        if (StringUtils.isEmpty(userId) == false) {
//            return userId;
//        }
//        userId = String.valueOf(RequestContextHolder.currentRequestAttributes().getAttribute("UserName", 0));
//        if (StringUtils.isEmpty(userId)) {
//            userId = "admin";
//        }DBFactoryModelServiceImpl
//        return userId;
    }

    public Integer getCompanyId() {
        Object companyId = RequestContextHolder.currentRequestAttributes().getAttribute("companyId", 0);
        return (Integer) companyId;
//        return (Integer)24;
    }

    /**
     * 获取用户Name
     *
     * @return 用户Name
     * @throws RemoteException
     * <AUTHOR> 2017-12-21
     */
    public String getUserName() throws RemoteException {
        if (StringUtils.isEmpty(userName)) {
            String aaaVersion = ApplicationPropertiesReader.getValue("aaa_version");
            if ("old".equals(aaaVersion)) {
                IAuthenticateServiceProxy proxy = new IAuthenticateServiceProxy();
                User user = proxy.getUser(getUserId());
                if (user == null) {
                    userName = "默认用户";
                    return "默认用户";
                }
                userName = user.getName();
            } else if ("new".equals(aaaVersion)) {
                com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.authenticateservice.current.entity.User user = null;
                try {
                    com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.authenticateservice.current.IAuthenticateServiceProxy proxy = new com.pcitc.opal.webservice.appservice.pcitc.mes.ip.aaa.authenticateservice.current.IAuthenticateServiceProxy();
                    user = proxy.getUser(getUserId());
                } catch (RemoteException e) {
                    user = null;
                }
                if (user == null) {
                    userName = "默认用户";
                    return "默认用户";
                }
                userName = user.getName();
            } else if ("promace".equals(aaaVersion)) {
                userName = String.valueOf(RequestContextHolder.currentRequestAttributes().getAttribute("Name", 0));
                if (StringUtils.isEmpty(userName)) {
                    userName = "默认用户";
                    return "默认用户";
                }
            }
        }
        return userName;
    }

    /**
     * 获取cookie
     *
     * @param pageCode 页面编码
     * @return
     * <AUTHOR> 2018-08-17
     */
    public String getCookie(String pageCode) {
        String value = "";
        synchronized (String.class) {
            //cookieName = 页面编码+用户名
            String cookieName = pageCode + "-" + getUserId();
            //非注入方式获取获取request
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            final Cookie[] cookies = request.getCookies();
            if (cookies == null) {
                return "";
            }
            Cookie cookie = Arrays.stream(cookies).filter(x -> x.getName().equals(cookieName)).findFirst().orElse(null);
            if (cookie != null) {
                value = cookie.getValue();
            }
        }
        return value;
    }

    /**
     * 设置cookie
     *
     * @param pageCode 页面编码
     * @param unitIds  装置id集合
     * <AUTHOR> 2018-08-17
     */
    public void setCookie(String pageCode, String[] unitIds) throws UnsupportedEncodingException {
        if (StringUtils.isEmpty(pageCode)) {
            return;
        }
        synchronized (String.class) {
            //非注入方式获取获取request
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            //非注入方式获取获取response
            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();

            //cookieName = 页面编码+用户名
            String cookieName = pageCode + "-" + getUserId();
            System.out.println("cookieName打印----------------------------------" + cookieName);
            //cookieValue = 以#号分开
            String cookieValue = StringUtils.join(unitIds, "#");
            String host = request.getHeader("host");
            String domain = host.contains(":") ? host.substring(0, host.indexOf(":")) : host;
            Cookie cookie = new Cookie(cookieName, cookieValue);
            //cookie.setDomain(domain);
            cookie.setPath("/");//这个路径即该工程下都可以访问该cookie
            if (unitIds == null || unitIds.length == 0)
                cookie.setMaxAge(0);//删除cookie，如不设置，关闭浏览器cookie即消失，默认值为-1
            else
                cookie.setMaxAge(Integer.MAX_VALUE);//如不设置，关闭浏览器cookie即消失，默认值为-1
            response.addCookie(cookie);
        }
    }

    public Date getSystemDateTime() {
        return systemDateTime;
    }
}
