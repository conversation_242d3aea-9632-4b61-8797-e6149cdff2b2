package com.pcitc.opal.pm.dao;

import java.util.List;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.Unit;
import com.pcitc.opal.pm.pojo.Workshop;

/*
 * Workshop实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_WorkshopRepositoryCustom
 * 作       者：dageng.sun
 * 创建时间：2017/12/11
 * 修改编号：1
 * 描       述：Workshop实体的Repository的JPA自定义接口 
 */
public interface WorkshopRepositoryCustom {
	
	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopEntity 车间实体
	 * @return 
	 * @return CommonResult 消息结果类
	 */
	CommonResult addWorkshop(Workshop workshopEntity);
	
	/**
	 * 获取车间集合
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopIds 车间主键id集合
	 * @return 
	 * @return List<Workshop> Workshop集合
	 */
	List<Workshop> getWorkshop(Long[] workshopIds);
	
	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopIds 车间主键id集合
	 * @return 
	 * @return CommonResult 消息结果类
	 */
	CommonResult deleteWorkshop(Long[] workshopIds);
	
	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopEntity 车间实体
	 * @return 
	 * @return CommonResult 消息结果类
	 */
	CommonResult updateWorkshop(Workshop workshopEntity);
	
	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopId 车间主键id
	 * @return 
	 * @return Workshop 车间实体
	 */
	Workshop getSingleWorkshop(Long workshopId);
	
	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryId 工厂id
	 * @param workshopName 车间的名称或简称
	 * @param stdCode 标准编码
	 * @param inUse 是否启用
	 * @param page 分页对象
	 * @return 
	 * @return PaginationBean<Workshop> 翻页对象
	 */
	PaginationBean<Workshop> getWorkshop(Long factoryId, String workshopName, String stdCode, Integer inUse, Pagination page);
	
	/**
	 * 校验数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopEntity 车间实体
	 * @return 
	 * @return CommonResult 消息结果类
	 */
	CommonResult workshopValidation(Workshop workshopEntity);

	Workshop getWorkShopByStdCode(String stdCode);

	/**
	 * 根据工厂id查询车间
	 * @param factoryId 工厂id
	 * @return list
	 */
	List<Workshop> getWorkShopByFactoryId(List<Long> factoryId);
}
