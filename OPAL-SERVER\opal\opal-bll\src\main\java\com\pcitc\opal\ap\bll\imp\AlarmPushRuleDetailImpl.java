package com.pcitc.opal.ap.bll.imp;

import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.AlarmFlagRepository;
import com.pcitc.opal.ak.dao.AlarmKnowlgManagmtRepository;
import com.pcitc.opal.ap.bll.AlarmPushRuleDetailService;
import com.pcitc.opal.ap.bll.AlarmPushRuleService;
import com.pcitc.opal.ap.bll.entity.AlarmPushRuleDetailEntity;
import com.pcitc.opal.ap.dao.AlarmPushRuleDetailRepository;
import com.pcitc.opal.ap.dao.AlarmPushRuleRepository;
import com.pcitc.opal.ap.dao.GroupRepository;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleDetailEntityVO;
import com.pcitc.opal.ap.pojo.AlarmPushRule;
import com.pcitc.opal.ap.pojo.AlarmPushRuleDetail;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.CommonUtil;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.pm.dao.AlarmPointRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.Arrays;
import java.util.List;

/*
 * 报警制度管理业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmStdManagmtImpl
 * 作	者：kun.zhao
 * 创建时间：2018/02/28
 * 修改编号：1
 * 描    述：报警制度管理业务逻辑层实现类
 */
@Service
public class AlarmPushRuleDetailImpl implements AlarmPushRuleDetailService {

    @Autowired
    private AlarmPushRuleDetailRepository alarmPushRuleDetailRepository;
    @Autowired
    private AlarmPushRuleRepository alarmPushRuleRepository;
    @Autowired
    private GroupRepository groupRepository;


    @Override
    public List<AlarmPushRuleDetailEntityVO> getAlarmPushRuleDetail(Long apRuleDetailId) throws Exception {
        List<AlarmPushRuleDetailEntityVO> alarmPushRuleDetailList =alarmPushRuleDetailRepository.getAlarmPushRuleDetails(apRuleDetailId);
        return alarmPushRuleDetailList;
    }

    @Override
    public CommonResult addAlarmPushRuleDetail(AlarmPushRuleDetailEntity alarmPushRuleDetailEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmPushRuleDetail alarmPushRule = ObjectConverter.entityConverter(alarmPushRuleDetailEntity, AlarmPushRuleDetail.class);
        CommonUtil.returnValue(alarmPushRule, CommonEnum.PageModelEnum.NewAdd.getIndex());
        return alarmPushRuleDetailRepository.addAlarmPushRuleDetail(alarmPushRule);
    }

    @Override
    public CommonResult deleteAlarmPushRuleDetail(Long[] ids) throws Exception {
        return alarmPushRuleDetailRepository.deleteAlarmPushRuleDetail(Arrays.asList(ids));
    }

    @Override
    public CommonResult updateAlarmPushRuleDetail(AlarmPushRuleDetailEntity alarmPushRuleDetailEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmPushRuleDetail alarmPushRule = ObjectConverter.entityConverter(alarmPushRuleDetailEntity, AlarmPushRuleDetail.class);
        return alarmPushRuleDetailRepository.updateAlarmPushRuleDetail(alarmPushRule);
    }
}
