package com.pcitc.opal.aa.bll.imp;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pcitc.opal.aa.bll.AlarmAssessFirstPageService;
import com.pcitc.opal.aa.bll.entity.*;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.AlarmRecDAO;
import com.pcitc.opal.ad.dao.AlarmRecRepository;
import com.pcitc.opal.ad.vo.WorkshopResponseNumberVO;
import com.pcitc.opal.ad.vo.UnitResponseNumberVO;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.DbConfig;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.entity.SystRunParaConfEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.ParameterMode;
import javax.persistence.PersistenceContext;
import javax.persistence.StoredProcedureQuery;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.Collator;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/*
 * 评估首页业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmAssessFirstPageImpl
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/17
 * 修改编号：1
 * 描    述：评估首页业务逻辑层实现类
 */
@Service
@Component
public class AlarmAssessFirstPageImpl implements AlarmAssessFirstPageService {
    private static final Logger logger = LoggerFactory.getLogger(AlarmAssessFirstPageImpl.class);
    /**
     * 实例化数据访问层接口
     */
    @Autowired
    private BasicDataService basicDataService;

    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private DbConfig dbConfig;
    @Autowired
    private AlarmEventRepository eventRro;

    @Autowired
    private AlarmRecRepository alarmRecRepository;


    @Autowired
    private AlarmRecDAO alarmRecDAO;

    /**
     * 获取评估首页实体集合
     *
     * @param dateTime 发生时间
     * @return 评估首页实体集合
     * <AUTHOR> 2017-10-17
     */
    @Override
    public AlarmAssessFirstPageEntity getAlarmAssessFirstPage(Date dateTime)
            throws Exception {
        AlarmAssessFirstPageEntity alarmAssessFirstPageEntity = new AlarmAssessFirstPageEntity();
        List<UnitEntity> unitList = basicDataService.getUnitList(true);
        String[] unitIds1 = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
        String strUnitIds = StringUtils.join(unitIds1, ",");

        // region 公共数据
        List<DictionaryEntity> dateTimeList = basicDataService.getQueryStartAndEndDate(dateTime);
        Date startTime = (Date) (dateTimeList.get(0).getValue());
        Date endTime = (Date) (dateTimeList.get(1).getValue());
        Date newEndTime = endTime;
        Date lastWeekStartDate = (Date) (dateTimeList.get(2).getValue());
        Date lastWeekEndDate = (Date) (dateTimeList.get(3).getValue());
        String endFlag = String.valueOf(dateTimeList.get(5).getValue());
        if ("<".equals(endFlag)) {
            newEndTime = DateUtils.addSeconds(endTime, -1);
        }

        // endregion
        // region 网格列数据
//        if("oracle".equals(dbConfig.getDataBase())){
//            gridViewEntityList = getGridViewData(startTime,newEndTime,strUnitIds);
//        }else{
//            gridViewEntityList = getGridViewDataNew(startTime,newEndTime,strUnitIds);
//        }
        List<GridViewEntity> gridViewEntityList = getGridViewData(startTime, newEndTime, strUnitIds);
        List<GridViewEntity> top4GridViewEntityList = getTop4GridViewData(gridViewEntityList, lastWeekStartDate, lastWeekEndDate, true);

        // endregion

        // region 评估等级数据
        AssessLevelEntity assessLevelEntity = getAssessLevelData(gridViewEntityList);
        // endregion

        // 趋势图数据
        List<String> dateList = getVariationTrendDate(startTime, endTime);
        List<List<VariationTrendEntity>> variationTrendDataList = getVariationTrendData(dateList, top4GridViewEntityList, endTime);
        System.out.println(variationTrendDataList.size());
        //region 评估等级装置ID集合
        Map<Long, List<String>> unitIds = gridViewEntityList.stream().
                collect(Collectors.groupingBy(GridViewEntity::getAssessLevel, Collectors.mapping(GridViewEntity::getUnitId, Collectors.toList())));
        List<DictionaryEntity> dicUnitIds = new ArrayList<>();
        unitIds.entrySet().forEach(x -> {
            DictionaryEntity dic = new DictionaryEntity();
            dic.setKey(x.getKey());
            dic.setValue(x.getValue());
            dicUnitIds.add(dic);
        });
        //endregion

        alarmAssessFirstPageEntity.setGridViewEntityList(top4GridViewEntityList);
        alarmAssessFirstPageEntity.setAssessLevelEntity(assessLevelEntity);
        alarmAssessFirstPageEntity.setVariationTrendEntityList(variationTrendDataList);
        alarmAssessFirstPageEntity.setDateTimeList(dateTimeList);
        alarmAssessFirstPageEntity.setUnitIdList(dicUnitIds);
        alarmAssessFirstPageEntity.setVariationTrendDate(dateList);

        return alarmAssessFirstPageEntity;
    }

    /**
     * 获取评估首页实体集合
     *
     * @param startDate 发生时间
     * @param endDate   结束时间
     * @return 评估首页实体集合
     * <AUTHOR> 2017-10-17
     */
    @Override
    public AlarmAssessFirstPageEntity getAlarmAssessFirstPage(Date startDate, Date endDate, String[] unitStdCodes)
            throws Exception {
        AlarmAssessFirstPageEntity alarmAssessFirstPageEntity = new AlarmAssessFirstPageEntity();
        List<UnitEntity> unitList = basicDataService.getUnitList(true);
        String[] unitIds1 = unitList.stream().map(x -> x.getStdCode()).toArray(String[]::new);

        String strUnitIds = StringUtils.join(unitStdCodes, ",");
        // region 网格列数据
        List<GridViewEntity> gridViewEntityList = getGridViewDataPage(startDate, endDate, strUnitIds);
        //List<GridViewEntity> top4GridViewEntityList = getTop4GridViewData(gridViewEntityList, lastWeekStartDate, lastWeekEndDate, true);

        alarmAssessFirstPageEntity.setGridViewEntityList(gridViewEntityList);


        return alarmAssessFirstPageEntity;
    }

    /**
     * 获取评估首页实体集合————工艺参数报警率————平均报警数专用
     *
     * @param startDate 发生时间
     * @param endDate   结束时间
     * @return 评估首页实体集合
     * <AUTHOR> 2017-10-17
     */
    @Override
    public AlarmAssessFirstPageEntity getAlarmAssessFirstPageAve(Date startDate, Date endDate, String[] unitStdCodes)
            throws Exception {
        AlarmAssessFirstPageEntity alarmAssessFirstPageEntity = new AlarmAssessFirstPageEntity();
        List<UnitEntity> unitList = basicDataService.getUnitList(true);
        String[] unitIds1 = unitList.stream().map(x -> x.getStdCode()).toArray(String[]::new);

        String strUnitIds = StringUtils.join(unitStdCodes, ",");
        // region 网格列数据
        List<GridViewEntity> gridViewEntityList = getGridViewDataAve(startDate, endDate, strUnitIds);
        //List<GridViewEntity> top4GridViewEntityList = getTop4GridViewData(gridViewEntityList, lastWeekStartDate, lastWeekEndDate, true);

        alarmAssessFirstPageEntity.setGridViewEntityList(gridViewEntityList);


        return alarmAssessFirstPageEntity;
    }

    /**
     * 获取评估等级数据
     *
     * @param isAll 是否显示全部
     * @return 评估等级结果集
     * <AUTHOR> 2017-10-23
     */
    @Override
    public List<DictionaryEntity> getAssessLevel(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            if (isAll) {
                dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
            }

            //报警评估等级信息
            SystRunParaConfEntity systRunParaConfEntity = basicDataService.getSystRunParamByCode("AlarmAssessGrade");
            if (systRunParaConfEntity == null || org.apache.commons.lang3.StringUtils.isBlank(systRunParaConfEntity.getCode())) {
                throw new Exception("未配置报警评估等级信息参数");
            }
            int alarmAssessGradeValue = Integer.parseInt(systRunParaConfEntity.getParaValue());
            if (alarmAssessGradeValue == 1) {
                for (CommonEnum.AlarmLevelEnum enumItem : CommonEnum.AlarmLevelEnum.values()) {
                    dictionaryEntityArrayList.add(new DictionaryEntity(enumItem.getIndex(), enumItem.getName()));
                }
            } else {
                for (CommonEnum.AlarmLevelGradeEnum enumItem : CommonEnum.AlarmLevelGradeEnum.values()) {
                    dictionaryEntityArrayList.add(new DictionaryEntity(enumItem.getIndex(), enumItem.getName()));
                }
            }
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 查看等级评估详情
     *
     * @param dateJason        开始时间集合
     * @param assessLevelJason 评估等级装置id集合
     * @param assessLevel      评估等级
     * @return 等级评估详情
     * <AUTHOR> 2017-10-17
     */
    @SuppressWarnings("unchecked")
    @Override
    public AlarmLevelAssessDetailEntity getAlarmLevelAssessDetail(String dateJason, String assessLevelJason, Long assessLevel)
            throws Exception {

        AlarmLevelAssessDetailEntity alarmLevelAssessDetailEntity = new AlarmLevelAssessDetailEntity();

        //region 解析Jason字符串
        ObjectMapper objectMapper = new ObjectMapper();
        List<DictionaryEntity> dateList = objectMapper.readValue(dateJason, new TypeReference<List<DictionaryEntity>>() {
        });
        List<DictionaryEntity> assessLevelList = objectMapper.readValue(assessLevelJason, new TypeReference<List<DictionaryEntity>>() {
        });

        Date startTime = new Date((Long) dateList.get(0).getValue());
        Date endTime = new Date((Long) dateList.get(1).getValue());
        Date newEndTime = endTime;
        Date lastWeekStartDate = new Date((Long) dateList.get(2).getValue());
        Date lastWeekEndDate = new Date((Long) dateList.get(3).getValue());
        String endFlag = String.valueOf(dateList.get(5).getValue());
        if ("<".equals(endFlag)) {
            newEndTime = DateUtils.addSeconds(endTime, -1);
        }

        List<String> unitCodes = new ArrayList<>();
        String[] unitCodesArray = null;
        List<DictionaryEntity> alEntityList = new ArrayList<>();
        if (assessLevel != null) {
            alEntityList = assessLevelList.stream().filter(x -> Long.valueOf(String.valueOf(x.getKey())).equals(assessLevel)).collect(Collectors.toList());

            alEntityList.stream().forEach(x -> {
                unitCodes.addAll((List<String>) x.getValue());
            });
            unitCodesArray = new String[unitCodes.size()];
            int i = 0;
            for (String unitId : unitCodes) {
                unitCodesArray[i++] = new String(unitId);
            }
            ;
            //如果没有数据直接返回空数据
            if (unitCodesArray == null || unitCodesArray.length == 0) {
                return alarmLevelAssessDetailEntity;
            }
        } else {
            assessLevelList.stream().forEach(x -> {
                unitCodes.addAll((List<String>) x.getValue());
            });
        }

        //endregion

        String strIds = StringUtils.join(unitCodes, ",");
        List<GridViewEntity> gridViewEntityList = getGridViewData(startTime, newEndTime, strIds);
        gridViewEntityList = getTop4GridViewData(gridViewEntityList, lastWeekStartDate, lastWeekEndDate, false);

        List<GridViewEntity> selectAlarmEvent = new ArrayList<>();
        GridViewEntity gridViewEntity = gridViewEntityList.stream().findFirst().orElse(null);
        if (gridViewEntity != null) {
            selectAlarmEvent.add(gridViewEntity);
        }

        // 趋势图数据
        List<String> vtDateList = getVariationTrendDate(startTime, endTime);
        List<List<VariationTrendEntity>> variationTrendDataList = getVariationTrendData(vtDateList, gridViewEntityList, endTime);
        alarmLevelAssessDetailEntity.setGridViewEntityList(gridViewEntityList);
        alarmLevelAssessDetailEntity.setVariationTrendList(variationTrendDataList);
        alarmLevelAssessDetailEntity.setVariationTrendDate(vtDateList);

        return alarmLevelAssessDetailEntity;
    }

    /***
     * 获取装置的优化效果评估
     *
     * @param unitCode   装置编码
     * @param startTime 查询开始时间
     * @param endTime   查询结束时间
     * @return
     * <AUTHOR> 2017-11-22
     */
    @Override
    public GridViewEntity getUnitOptimizationEffectAssess(String unitCode, Date startTime, Date endTime) throws Exception {
        return getGridViewData(startTime, endTime, unitCode.toString() + ",").stream().findFirst().orElse(new GridViewEntity());
    }

    /**
     * 获取单个装置的趋势图
     *
     * @param dateJason 时间集合
     * @param unitCode  装置编码
     * @return 等级评估详情页面实体
     * <AUTHOR> 2017-10-24
     */
    @Override
    public AlarmLevelAssessDetailEntity getVariationTrend(String dateJason, String unitCode) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        List<DictionaryEntity> dateList = objectMapper.readValue(dateJason, new TypeReference<List<DictionaryEntity>>() {
        });

        Date startTime = new Date((Long) dateList.get(0).getValue());
        Date endTime = new Date((Long) dateList.get(1).getValue());
        Date newEndTime = endTime;
        String endFlag = String.valueOf(dateList.get(5).getValue());
        if ("<".equals(endFlag)) {
            newEndTime = DateUtils.addSeconds(endTime, -1);
        }

        List<GridViewEntity> gridViewEntityList = getGridViewData(startTime, newEndTime, unitCode);

        // 趋势图数据
        List<String> vtDateList = getVariationTrendDate(startTime, endTime);
        List<List<VariationTrendEntity>> variationTrendDataList = getVariationTrendData(vtDateList, gridViewEntityList, endTime);

        AlarmLevelAssessDetailEntity alarmLevelAssessDetailEntity = new AlarmLevelAssessDetailEntity();
        alarmLevelAssessDetailEntity.setVariationTrendList(variationTrendDataList);
        alarmLevelAssessDetailEntity.setVariationTrendDate(vtDateList);

        return alarmLevelAssessDetailEntity;
    }

    /**
     * 获取单个装置的趋势图
     *
     * @param startTime
     * @param endTime
     * @param unitId
     * @return
     * @throws Exception
     */
    @Override
    public AlarmLevelAssessDetailEntity getVariationTrend(Date startTime, Date endTime, String[] unitId) throws Exception {

        String unitIds = StringUtils.join(unitId, ",");

        List<GridViewEntity> gridViewEntityList = getGridViewDataAve(startTime, endTime, unitIds);
        // 趋势图数据
        List<String> vtDateList = getVariationTrendDate(startTime, endTime);
        List<List<VariationTrendEntity>> variationTrendDataList = getVariationTrendData(vtDateList, gridViewEntityList, startTime, endTime);
        AlarmLevelAssessDetailEntity alarmLevelAssessDetailEntity = new AlarmLevelAssessDetailEntity();
        alarmLevelAssessDetailEntity.setVariationTrendList(variationTrendDataList);
        alarmLevelAssessDetailEntity.setVariationTrendDate(vtDateList);

        return alarmLevelAssessDetailEntity;
    }

    List<GridViewEntity> getGridViewDataPage(Date startTime, Date endTime, String strCodes) throws Exception {
        List<String> asList = Arrays.asList(strCodes.split(","));


        List<GridViewEntity> gridViewEntityList = new ArrayList<>();

        String codes = "";
        int pageSize = 40;
        for (int i = 0; i < asList.size(); i++) {
            List<String> asList2 = asList.stream()
                    .skip(i * pageSize)
                    .limit(pageSize)
                    .collect(Collectors.toList());
            if (asList2.size() != 0) {
                codes = StringUtils.join(asList2, ',');
                List<GridViewEntity> gridViewEntityL = getGridViewData(startTime, endTime, codes);
                gridViewEntityList.addAll(gridViewEntityL);
            }
        }

//        String[] arrr = strCodes.split(",");
//        List<String> strCodesList =splitArr(arrr,30);
//
//        strCodesList.forEach(l->{
//            try {
//                List<GridViewEntity> gridViewEntityL = getGridViewData(startTime,endTime,l);
//                gridViewEntityList.addAll(gridViewEntityL);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        });
        return gridViewEntityList;
    }

    private List<String> splitArr(String[] array, int num) {
        int count = array.length % num == 0 ? array.length / num : array.length / num + 1;
        List<String> arrayList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            int index = i * num;
            List<String> list = new ArrayList<>();
            StringBuilder sb = new StringBuilder();
            int j = 0;
            while (j < num && index < array.length) {
                sb.append(array[index++]);
                sb.append(",");
                j++;
            }
            arrayList.add(sb.toString());
        }
        return arrayList;
    }


    // region 私有方法

    /**
     * 使用存储过程获取评估首页网格列数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param strCodes  装置ID集合
     * @return 评估首页网格列实体集合
     * <AUTHOR> 2017-10-24
     */
    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true, propagation = Propagation.REQUIRED)
    public List<GridViewEntity> getGridViewData(Date startTime, Date endTime, String strCodes) throws Exception {

        logger.error("传入参数：" + startTime + " " + endTime + " " + strCodes);

        List<GridViewEntity> gridViewEntityList = new ArrayList<>();
        StoredProcedureQuery query = entityManager.createStoredProcedureQuery("P_AA_GetGridViewData");
        query.registerStoredProcedureParameter("v_in_startTime", Date.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_endTime", Date.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_ids", String.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_companyId", Integer.class, ParameterMode.IN);
        if ("oracle".equals(dbConfig.getDataBase())) {
            query.registerStoredProcedureParameter("v_result", void.class, ParameterMode.REF_CURSOR);
        }
        query.setParameter("v_in_startTime", startTime);
        query.setParameter("v_in_endTime", endTime);
        query.setParameter("v_in_ids", strCodes);
        Integer companyId = new CommonProperty().getCompanyId();
        if (companyId != null) {
            query.setParameter("v_in_companyId", companyId);
        } else {
            query.setParameter("v_in_companyId", basicDataService.getCompanyIdByUnit(strCodes.split(",")));

        }


        List<Object[]> result = new ArrayList<>();
        try {
            result = query.getResultList();
        } catch (Exception e) {
            logger.error(e.getMessage());
            e.printStackTrace();
        }
        if (result != null && result.size() == 0) {
            return gridViewEntityList;
        }
        String[] unitCodes = result.stream().map(x -> x[0].toString()).toArray(String[]::new);
//        List<UnitEntity> unitEntityList = basicDataService.getUnitListByIds(unitCodes, false);
        SystRunParaConfEntity systRunParaConfEntity = null;
        try {
            systRunParaConfEntity = basicDataService.getSystRunParamByCode("AlarmAssessGrade");
        } catch (Exception e) {
            logger.error("查询系统参数AlarmAssessGrade异常" + e.getMessage());
        }

        //报警评估等级信息
        try {

            int alarmAssessGradeValue = systRunParaConfEntity == null ? 0 : Integer.parseInt(systRunParaConfEntity.getParaValue());

            for (Object[] element : result) {

                GridViewEntity entity = new GridViewEntity();
                entity.setUnitId(element[0].toString());
                entity.setUnitName(element[7].toString());
                entity.setAlarmPointId(Long.valueOf(element[1].toString()));
                entity.setSumTimes(element[2] == null ? 0 : Long.valueOf(element[2].toString()));
                entity.setAvgAlarmRate(getDoubleDecimals(element[3]));
                entity.setPeakAlarmRate(getDoubleDecimals(element[4]));
                entity.setExcitationRate(getDoubleDecimals(element[5]));
                entity.setAssessLevel(Long.valueOf(element[6].toString()));
                if (alarmAssessGradeValue == 1) {
                    entity.setAssessLevelName(CommonEnum.AlarmLevelEnum.getName(Integer.parseInt(element[6].toString())));
                } else {
                    entity.setAssessLevelName(CommonEnum.AlarmLevelGradeEnum.getName(Integer.parseInt(element[6].toString())));
                }
                gridViewEntityList.add(entity);
            }
            ;
        } catch (Exception ex) {
            logger.error("P_AA_GetGridViewData异常" + ex.getMessage());
            ex.printStackTrace();
        }
        return gridViewEntityList;
    }

    /**
     * 使用存储过程获取评估首页网格列数据————工艺参数报警率————平均报警数专用
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param strCodes  装置ID集合
     * @return 评估首页网格列实体集合
     * <AUTHOR> 2017-10-24
     */
    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true, propagation = Propagation.REQUIRED)
    public List<GridViewEntity> getGridViewDataAve(Date startTime, Date endTime, String strCodes) throws Exception {
        logger.error("传入参数：" + startTime + " " + endTime + " " + strCodes);
        //企业
        CommonProperty commonProperty = new CommonProperty();
//        if("mysql".equals(dbConfig.getDataBase())){
//            return this.getGridViewDataMysql(startTime, endTime, strCodes);
//        }
        List<GridViewEntity> gridViewEntityList = new ArrayList<>();
        StoredProcedureQuery query = entityManager.createStoredProcedureQuery("P_AA_GetGridViewData_Ave");
        query.registerStoredProcedureParameter("v_in_startTime", Date.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_endTime", Date.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_ids", String.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("v_in_companyId", Integer.class, ParameterMode.IN);
        if ("oracle".equals(dbConfig.getDataBase())) {
            query.registerStoredProcedureParameter("v_result", void.class, ParameterMode.REF_CURSOR);
        }
        query.setParameter("v_in_startTime", startTime);
        query.setParameter("v_in_endTime", endTime);
        query.setParameter("v_in_ids", strCodes);
        query.setParameter("v_in_companyId", commonProperty.getCompanyId());

        List<Object[]> result = new ArrayList<>();
        try {
            result = query.getResultList();
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        if (result != null && result.size() == 0) {
            return gridViewEntityList;
        }
//        String[] unitCodes = result.stream().map(x -> x[0].toString()).toArray(String[]::new);
//        List<UnitEntity> unitEntityList = basicDataService.getUnitListByIds(unitCodes, false);

        //报警评估等级信息
        try {
            SystRunParaConfEntity systRunParaConfEntity = basicDataService.getSystRunParamByCode("AlarmAssessGrade");
            if (systRunParaConfEntity == null || org.apache.commons.lang3.StringUtils.isBlank(systRunParaConfEntity.getCode())) {
                throw new Exception("未配置报警评估等级信息参数");
            }
            int alarmAssessGradeValue = Integer.parseInt(systRunParaConfEntity.getParaValue());

            result.stream().forEach((element) -> {
                GridViewEntity entity = new GridViewEntity();
                entity.setUnitId(element[0].toString());
                entity.setUnitName(element[7].toString());
                entity.setAlarmPointId(Long.valueOf(element[1].toString()));
                entity.setSumTimes(element[2] == null ? 0 : Long.valueOf(element[2].toString()));
                entity.setAvgAlarmRate(getDoubleDecimals(element[3]));
                entity.setPeakAlarmRate(getDoubleDecimals(element[4]));
                entity.setExcitationRate(getDoubleDecimals(element[5]));
                entity.setAssessLevel(Long.valueOf(element[6].toString()));
                if (alarmAssessGradeValue == 1) {
                    entity.setAssessLevelName(CommonEnum.AlarmLevelEnum.getName(Integer.valueOf(element[6].toString())));
                } else {
                    entity.setAssessLevelName(CommonEnum.AlarmLevelGradeEnum.getName(Integer.valueOf(element[6].toString())));
                }
                gridViewEntityList.add(entity);
            });
        } catch (Exception ex) {
            logger.error("P_AA_GetGridViewData_Ave发生异常" + ex.getMessage());
            ex.printStackTrace();
        }
        return gridViewEntityList;
    }

    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true, propagation = Propagation.REQUIRED)
    public List<GridViewEntity> getGridViewDataMysql(Date startTime, Date endTime, String strCodes) throws Exception {
        List<String> str = this.eventRro.getListCodes(startTime, endTime, strCodes);
        logger.error("传入参数：" + startTime + " " + endTime + " " + strCodes);
        List<GridViewEntity> gridViewEntityList = new ArrayList<>();
        if (str != null && str.size() > 0) {
            for (int i = 0, j = str.size(); i < j; i++) {
                System.out.println("----------------------------------------------------------------------------------------------------" + str.get(i));
                StoredProcedureQuery query = entityManager.createStoredProcedureQuery("P_AA_GetGridViewData_New");
                query.registerStoredProcedureParameter("v_in_startTime", Date.class, ParameterMode.IN);
                query.registerStoredProcedureParameter("v_in_endTime", Date.class, ParameterMode.IN);
                query.registerStoredProcedureParameter("v_in_ids", String.class, ParameterMode.IN);
                if ("oracle".equals(dbConfig.getDataBase())) {
                    query.registerStoredProcedureParameter("v_result", void.class, ParameterMode.REF_CURSOR);
                }
                query.setParameter("v_in_startTime", startTime);
                query.setParameter("v_in_endTime", endTime);
                query.setParameter("v_in_ids", str.get(i));


                List<Object[]> result = null;
                try {
                    result = query.getResultList();
                } catch (Exception e) {
                    logger.error(e.getMessage());
                }
                if (result != null && result.size() == 0) {
//                    return gridViewEntityList;
                    continue;
                }
                System.out.println(result);
                String[] unitCodes = result.stream().map(x -> x[0].toString()).toArray(String[]::new);
                logger.error("resultToStr" + unitCodes);
                List<UnitEntity> unitEntityList = basicDataService.getUnitListByIds(unitCodes, false);

                //报警评估等级信息
                try {
                    SystRunParaConfEntity systRunParaConfEntity = basicDataService.getSystRunParamByCode("AlarmAssessGrade");
                    if (systRunParaConfEntity == null || org.apache.commons.lang3.StringUtils.isBlank(systRunParaConfEntity.getCode())) {
                        throw new Exception("未配置报警评估等级信息参数");
                    }
                    int alarmAssessGradeValue = Integer.parseInt(systRunParaConfEntity.getParaValue());

                    result.stream().forEach((element) -> {
                        GridViewEntity entity = new GridViewEntity();
                        entity.setUnitId(element[0].toString());
                        entity.setUnitName(unitEntityList.stream().filter(x -> x.getStdCode().equals(entity.getUnitId())).findFirst().orElse(null).getSname());
                        entity.setAlarmPointId(Long.valueOf(element[1].toString()));
                        entity.setSumTimes(element[2] == null ? 0 : Long.valueOf(element[2].toString()));
                        entity.setAvgAlarmRate(getDoubleDecimals(element[3]));
                        entity.setPeakAlarmRate(getDoubleDecimals(element[4]));
                        entity.setExcitationRate(getDoubleDecimals(element[5]));
                        entity.setAssessLevel(Long.valueOf(element[6].toString()));
                        if (alarmAssessGradeValue == 1) {
                            entity.setAssessLevelName(CommonEnum.AlarmLevelEnum.getName(Integer.valueOf(element[6].toString())));
                        } else {
                            entity.setAssessLevelName(CommonEnum.AlarmLevelGradeEnum.getName(Integer.valueOf(element[6].toString())));
                        }
                        gridViewEntityList.add(entity);
                    });
                } catch (Exception ex) {
                }
            }
        }
        return gridViewEntityList;
    }


    //region 网格列方法

    /**
     * 获取前4条网格列数据，并和前7天数据对比
     *
     * @param gridViewEntityList 网格列数据集合
     * @param startTime          开始时间
     * @param endTime            结束时间
     * @param isTop4             是否获取前4条数据
     * @return 前4条网格列数据
     * <AUTHOR> 2017-10-18
     */
    private List<GridViewEntity> getTop4GridViewData(List<GridViewEntity> gridViewEntityList, Date startTime, Date endTime, boolean isTop4) throws Exception {
        // 获取前4条数据
        List<GridViewEntity> top4GridViewEntityList;
        if (gridViewEntityList.size() == 0) {
            return new ArrayList<GridViewEntity>();
        }
        if (gridViewEntityList.size() > 4 && isTop4) {
            top4GridViewEntityList = gridViewEntityList.subList(0, 4);
        } else {
            top4GridViewEntityList = gridViewEntityList;
        }
        String[] unitCodes = top4GridViewEntityList.stream().map(x -> x.getUnitId()).toArray(String[]::new);

        Date newEndTime = DateUtils.addSeconds(endTime, -1);
        ////前7天的网格列数据
        String strIds = StringUtils.join(unitCodes, ",");
        List<GridViewEntity> ago7DayGvDataList = getGridViewData(startTime, newEndTime, strIds);
//        if("oracle".equals(dbConfig.getDataBase())){
//            ago7DayGvDataList= getGridViewData(startTime,newEndTime,strIds);
//        }else{
//            ago7DayGvDataList= getGridViewDataNew(startTime,newEndTime,strIds);
//        }

        // 报警率对比值比较
        top4GridViewEntityList.stream().forEach(x -> {
            GridViewEntity gridViewEntity = ago7DayGvDataList.stream()
                    .filter(y -> y.getUnitId().equals(x.getUnitId()))
                    .findFirst().orElse(null);

            // region 平均报警率对比
            double avgAlarmRate = Double.valueOf(x.getAvgAlarmRate().toString()) - (gridViewEntity == null ? 0 : Double.valueOf(gridViewEntity.getAvgAlarmRate().toString()));
            if (avgAlarmRate == 0) {
                x.setAvgAlarmContrast(CommonEnum.StateComparisonEnum.Unchange.getIndex());
            } else if (avgAlarmRate < 0) {
                x.setAvgAlarmContrast(CommonEnum.StateComparisonEnum.Lower.getIndex());
            } else if (avgAlarmRate > 0) {
                x.setAvgAlarmContrast(CommonEnum.StateComparisonEnum.Increase.getIndex());
            }
            // endregion

            // region 峰值报警率对比
            double peakAlarmRate = Double.valueOf(x.getPeakAlarmRate().toString()) - (gridViewEntity == null ? 0 : Double.valueOf(gridViewEntity.getPeakAlarmRate().toString()));
            if (peakAlarmRate == 0) {
                x.setPeakAlarmContrast(CommonEnum.StateComparisonEnum.Unchange.getIndex());
            } else if (peakAlarmRate < 0) {
                x.setPeakAlarmContrast(CommonEnum.StateComparisonEnum.Lower.getIndex());
            } else if (peakAlarmRate > 0) {
                x.setPeakAlarmContrast(CommonEnum.StateComparisonEnum.Increase.getIndex());
            }
            // endregion

            // region 扰动率对比
            double excitationRate = Double.valueOf(x.getExcitationRate().toString()) - (gridViewEntity == null ? 0 : Double.valueOf(gridViewEntity.getExcitationRate().toString()));
            if (excitationRate == 0) {
                x.setExcitationContrast(CommonEnum.StateComparisonEnum.Unchange.getIndex());
            } else if (excitationRate < 0) {
                x.setExcitationContrast(CommonEnum.StateComparisonEnum.Lower.getIndex());
            } else if (excitationRate > 0) {
                x.setExcitationContrast(CommonEnum.StateComparisonEnum.Increase.getIndex());
            }
            // endregion
        });

        return top4GridViewEntityList;
    }

    //endregion

    /**
     * 获取评估等级数据
     *
     * @param gridViewEntityList 网格列数据集合
     * @return 评估等级数据
     * <AUTHOR> 2017-10-18
     */
    private AssessLevelEntity getAssessLevelData(List<GridViewEntity> gridViewEntityList) {
        Map<Long, Long> mapGroup = gridViewEntityList.stream().collect(
                Collectors.groupingBy(GridViewEntity::getAssessLevel, Collectors.counting()));

        AssessLevelEntity assessLevelEntity = new AssessLevelEntity();
        //报警评估等级信息

        Long totalAlarm = gridViewEntityList.stream().mapToLong(gridViewEntity -> gridViewEntity.getSumTimes()).sum();
        assessLevelEntity.setTotalAlarmNum(totalAlarm);
        mapGroup.forEach((x, y) -> {
            if (x.equals(Long.valueOf(CommonEnum.AlarmLevelEnum.Overloaded.getIndex()))) {
                assessLevelEntity.setLevel1(y);
            } else if (x.equals(Long.valueOf(CommonEnum.AlarmLevelEnum.Reactive.getIndex()))) {
                assessLevelEntity.setLevel2(y);
            } else if (x.equals(Long.valueOf(CommonEnum.AlarmLevelEnum.Stable.getIndex()))) {
                assessLevelEntity.setLevel3(y);
            } else if (x.equals(Long.valueOf(CommonEnum.AlarmLevelEnum.Robust.getIndex()))) {
                assessLevelEntity.setLevel4(y);
            } else if (x.equals(Long.valueOf(CommonEnum.AlarmLevelEnum.Predictive.getIndex()))) {
                assessLevelEntity.setLevel5(y);
            }
        });
        //总报警数、报警平均数
        if (assessLevelEntity.getTotalAlarmNum() != null) {
            String avgValue = String.valueOf((double) Math.round((assessLevelEntity.getTotalAlarmNum().doubleValue() / 7) * 100) / 100);
            assessLevelEntity.setAvgAlarmNum(avgValue);
        }
        return assessLevelEntity;
    }

    //region 趋势图方法

    /**
     * 获取趋势图数据
     *
     * @param dateList               统计10分钟的报警事件数据集合
     * @param top4GridViewEntityList 网格列前4条数据
     * @param endTime                结束时间
     * @return 趋势图数据
     * <AUTHOR> 2017-10-18
     */
    private List<List<VariationTrendEntity>> getVariationTrendData(List<String> dateList,
                                                                   List<GridViewEntity> top4GridViewEntityList, Date endTime) {

        DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
        DateFormat dateFormat2 = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

        List<List<VariationTrendEntity>> variationTrendDataList = new ArrayList<>();
        if (top4GridViewEntityList.size() == 0) {
            return variationTrendDataList;
        }
        String hourTime = " " + basicDataService.getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();
        List<String> unitCodes = Arrays.asList(top4GridViewEntityList.stream().map(x -> x.getUnitId()).toArray(String[]::new));
        String strIds = StringUtils.join(unitCodes, ",");
        List<List<GridViewEntity>> gridViewList = new ArrayList<>();
        dateList.forEach(x -> {
            Date newDate = null;
            Date endDate = null;
            try {
                newDate = dateFormat2.parse(x + hourTime);
                endDate = DateUtils.addDays(newDate, 1);
                if (dateFormat.format(newDate).equals(dateFormat.format(endTime)) && endDate.after(endTime)) {
                    endDate = endTime;
                } else {
                    endDate = DateUtils.addSeconds(endDate, -1);
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
            List<GridViewEntity> gvDataList = null;
            try {
                gvDataList = getGridViewData(newDate, endDate, strIds);
//                if("oracle".equals(dbConfig.getDataBase())){
//                    gvDataList = getGridViewData(newDate,endDate,strIds);
//                }else{
//                    gvDataList = getGridViewDataNew(newDate,endDate,strIds);
//                }

            } catch (Exception e) {
                e.printStackTrace();
            }
            gridViewList.add(gvDataList);
        });


        Map<String, List<Object>> sumMap = new LinkedHashMap<>();
        Map<String, List<Object>> avgMap = new LinkedHashMap<>();
        Map<String, List<Object>> peakMap = new LinkedHashMap<>();
        Map<String, List<Object>> excitationMap = new LinkedHashMap<>();
        top4GridViewEntityList.forEach(x -> {
            List<Object> sumList = new ArrayList<>();
            List<Object> avgList = new ArrayList<>();
            List<Object> peakList = new ArrayList<>();
            List<Object> excitationList = new ArrayList<>();
            gridViewList.forEach(y -> {
                GridViewEntity gvEntity = y.stream().filter(u -> x.getUnitId().equals(u.getUnitId())).findFirst().orElse(null);
                sumList.add(gvEntity == null ? 0 : gvEntity.getSumTimes());
                avgList.add(gvEntity == null ? 0 : ("0.00".equals(gvEntity.getAvgAlarmRate()) ? 0 : gvEntity.getAvgAlarmRate()));
                peakList.add(gvEntity == null ? 0 : ("0.00".equals(gvEntity.getPeakAlarmRate()) ? 0 : gvEntity.getPeakAlarmRate()));
                excitationList.add(gvEntity == null ? 0 : ("0.00".equals(gvEntity.getExcitationRate()) ? 0 : gvEntity.getExcitationRate()));
            });
            sumMap.put(x.getUnitId(), sumList);
            avgMap.put(x.getUnitId(), avgList);
            peakMap.put(x.getUnitId(), peakList);
            excitationMap.put(x.getUnitId(), excitationList);
        });
        getVtData(sumMap, top4GridViewEntityList, variationTrendDataList);
        getVtData(avgMap, top4GridViewEntityList, variationTrendDataList);
        getVtData(peakMap, top4GridViewEntityList, variationTrendDataList);
        getVtData(excitationMap, top4GridViewEntityList, variationTrendDataList);

        return variationTrendDataList;
    }

    private List<List<VariationTrendEntity>> getVariationTrendData(List<String> dateList,
                                                                   List<GridViewEntity> craftParaAlarmRateList, Date startTime, Date endTime) {

        DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
        DateFormat dateFormat2 = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

        String hourTime = " " + basicDataService.getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();

        List<String> unitCodes = Arrays.asList(craftParaAlarmRateList.stream().map(x -> x.getUnitId()).toArray(String[]::new));
        String strIds = StringUtils.join(unitCodes, ",");
        List<List<GridViewEntity>> gridViewList = new ArrayList<>();
        dateList.forEach(x -> {
            Date newDate = null;
            Date endDate = null;
            try {
                newDate = dateFormat2.parse(x + hourTime);
                endDate = DateUtils.addDays(newDate, 1);
                if (dateFormat.format(newDate).equals(dateFormat.format(endTime)) && endDate.after(endTime)) {
                    endDate = endTime;
                } else {
                    endDate = DateUtils.addSeconds(endDate, -1);
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
            List<GridViewEntity> gvDataList = null;
            try {
                gvDataList = getGridViewDataAve(newDate, endDate, strIds);
            } catch (Exception e) {
                e.printStackTrace();
            }

            gridViewList.add(gvDataList);
        });
        List<List<VariationTrendEntity>> variationTrendDataList = new ArrayList<>();

        Map<String, List<Object>> hourMap = new LinkedHashMap<>();
        Map<String, List<Object>> avgMap = new LinkedHashMap<>();
        Map<String, List<Object>> peakMap = new LinkedHashMap<>();
        Map<String, List<Object>> craftMap = new LinkedHashMap<>();
        craftParaAlarmRateList.forEach(x -> {
            List<Object> hourList = new ArrayList<>();
            List<Object> avgList = new ArrayList<>();
            List<Object> peakList = new ArrayList<>();
            List<Object> craftList = new ArrayList<>();
            gridViewList.forEach(y -> {
                GridViewEntity gvEntity = y.stream().filter(u -> x.getUnitId().equals(u.getUnitId())).findFirst().orElse(null);
                //hourList.add(gvEntity == null ? 0 : gvEntity.getAlarmAmount());
                avgList.add(gvEntity == null ? 0 : ("0.00".equals(gvEntity.getAvgAlarmRate()) ? 0 : gvEntity.getAvgAlarmRate()));
                peakList.add(gvEntity == null ? 0 : ("0.00".equals(gvEntity.getPeakAlarmRate()) ? 0 : gvEntity.getPeakAlarmRate()));
                // craftList.add(gvEntity == null ? 0 : (gvEntity.getCraftParaAlarmRate().equals("0.00") ? 0 : gvEntity.getCraftParaAlarmRate()));
            });
            //hourMap.put(x.getUnitCode(),hourList );
            avgMap.put(x.getUnitId(), avgList);
            peakMap.put(x.getUnitId(), peakList);
            //craftMap.put(x.getUnitCode(),craftList );
        });
        //getVtData(hourMap,craftParaAlarmRateList,variationTrendDataList);
        getVtData(avgMap, craftParaAlarmRateList, variationTrendDataList);
        getVtData(peakMap, craftParaAlarmRateList, variationTrendDataList);
        //getVtData(craftMap,craftParaAlarmRateList,variationTrendDataList);

        return variationTrendDataList;
    }

    /**
     * 拼接趋势图
     *
     * @param map
     * @param gvEntityList
     * @param variationTrendDataList
     */
    private void getVtData(Map<String, List<Object>> map, List<GridViewEntity> gvEntityList, List<List<VariationTrendEntity>> variationTrendDataList) {
        List<VariationTrendEntity> vtEntityList = new ArrayList<>();
        map.entrySet().forEach(y -> {
            GridViewEntity gvEntity = gvEntityList.stream().filter(x -> y.getKey().equals(x.getUnitId())).findFirst().orElse(null);
            VariationTrendEntity vtEntity = new VariationTrendEntity();
            vtEntity.setUnitId(gvEntity.getUnitId());
            vtEntity.setUnitName(gvEntity.getUnitName());
            vtEntity.setAlarmRate(y.getValue());
            vtEntityList.add(vtEntity);
        });
        variationTrendDataList.add(vtEntityList);
    }


    //endregion

    //region 时间间隔、x轴日期、天数时间差计算

    /**
     * 获取x轴时间
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 时间日期集合
     * <AUTHOR> 2017-10-23
     */
    private List<String> getVariationTrendDate(Date startTime, Date endTime) {
        List<String> dateList = new ArrayList<>();
        int diffDays = differentDays(startTime, endTime);
        DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");

        for (int i = 0; i < diffDays; i++) {
            Date newDate = DateUtils.addDays(startTime, i);
            String vtDate = dateFormat.format(newDate);
            dateList.add(vtDate);
        }
        return dateList;
    }

    /**
     * startDate比endDate多的天数
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 时间天数差
     * <AUTHOR> 2017-10-23
     */
    private int differentDays(Date startDate, Date endDate) {

        String hourTime = " " + basicDataService.getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();
        int diffDays = 0;
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(startDate);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(endDate);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        //不同一年
        if (year1 != year2) {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0) {
                    //闰年
                    timeDistance += 366;
                } else {
                    //不是闰年
                    timeDistance += 365;
                }
            }

            diffDays = timeDistance + (day2 - day1);
        } else    //同年
        {
            diffDays = day2 - day1;
        }

        try {
            //根据业务需要，如果结束时间大于现在日期的8点，则日期加一天
            DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
            DateFormat dateFormat2 = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            String strNow = dateFormat.format(new Date()) + hourTime;

            if ((endDate.getTime() - (dateFormat2.parse(strNow).getTime())) > 0) {
                diffDays++;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return diffDays;
    }

    /**
     * 转成两位小数，不够两位自动补齐
     *
     * @param source 传入数值
     * @return 返回转换后的值
     * <AUTHOR> 2017-10-23
     */
    private String getDoubleDecimals(Object source) {
        String result = "0";
        if (source == null || StringUtils.isEmpty(source.toString())) {
            return "0.00";
        }
        if (source.toString().contains(".")) {
            if (source.toString().split("\\.")[1].length() == 1) {
                result = source + "0";
            } else {
                result = source.toString();
            }
        } else {
            result = source.toString() + ".00";
        }
        return result;
    }
    //endregion

    // endregion

    /**
     * 获取 1.装置名称, 2.装置ID, 3.报警总数 ,4.滋扰报警数量,5.报警确认数,6.报警恢复数,7.报警搁置数列表
     *
     * @param startTime 查询开始时间
     * @param endTime   查询结束时间
     * @return 1.装置名称, 2.装置ID, 3.报警总数 ,4.滋扰报警数量,5.报警确认数,6.报警恢复数,7.报警搁置数列表
     * <AUTHOR> 2017-12-20
     */
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, List<StaticInfoEntity>> getStatisticsAlarmCount(Date startTime, Date endTime) throws Exception {
        Map<String, List<StaticInfoEntity>> map = new HashMap<String, List<StaticInfoEntity>>();
        List<Object[]> result = new ArrayList<>();
        //企业
        CommonProperty commonProperty = new CommonProperty();
        try {

            StoredProcedureQuery query = entityManager.createStoredProcedureQuery("P_AA_GETSTATISTICSALARM");
            query.registerStoredProcedureParameter("v_in_startDate", Date.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_endDate", Date.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_companyId", Integer.class, ParameterMode.IN);
            if ("oracle".equals(dbConfig.getDataBase())) {
                query.registerStoredProcedureParameter("v_results", void.class, ParameterMode.REF_CURSOR);
            }
            query.setParameter("v_in_startDate", startTime);
            query.setParameter("v_in_endDate", endTime);
            query.setParameter("v_in_companyId", commonProperty.getCompanyId());

            result = query.getResultList();

            List<AlarmStatisticEntity> list = new ArrayList<>();
            result.stream().forEach((element) -> {
                AlarmStatisticEntity entity = new AlarmStatisticEntity();
                entity.setUnitId(element[0].toString());
                entity.setAlarmTotalCount(Double.valueOf(element[1].toString()));
                entity.setDisturbance((Double.valueOf(element[2].toString()) / entity.getAlarmTotalCount()) * 100);
                entity.setConfigure((Double.valueOf(element[3].toString()) / entity.getAlarmTotalCount()) * 100);
                entity.setRecovery((Double.valueOf(element[4].toString()) / entity.getAlarmTotalCount()) * 100);
                entity.setHold((Double.valueOf(element[5].toString()) / entity.getAlarmTotalCount()) * 100);
                list.add(entity);
            });
            List<UnitEntity> unitList = basicDataService.getUnitList(true);
            List<String> unitCodes = Arrays.asList(unitList.stream().map(x -> x.getStdCode()).toArray(String[]::new));
            List<AlarmStatisticEntity> newAlarmList = list.stream().filter(x -> unitCodes.stream().anyMatch(item -> x.getUnitId().equals(item)))
                    .collect(Collectors.toCollection(ArrayList::new));

            newAlarmList.stream().forEach(item -> item.setName(unitList.stream().filter(u -> u.getStdCode().equals(item.getUnitId())).findFirst().orElse(new UnitEntity()).getSname()));
            Collator collatorChinese = Collator.getInstance(Locale.SIMPLIFIED_CHINESE);
            Collections.sort(newAlarmList, Comparator.comparing(AlarmStatisticEntity::getDisturbance, Comparator.reverseOrder()).thenComparing(AlarmStatisticEntity::getName, collatorChinese));
            List<StaticInfoEntity> disList = new ArrayList<>();
            int length = 3;
            if (newAlarmList.size() < 3) {
                length = newAlarmList.size();
            }
            newAlarmList.subList(0, length).forEach((item) -> {
                disList.add(new StaticInfoEntity(item.getName(), item.getUnitId(), String.format("%.2f", item.getDisturbance()) + "%"));
            });

            map.put("alarmDisturbance", disList);

            List<Object[]> responseValue = alarmRecRepository.getResponseValue(startTime, endTime, commonProperty.getCompanyId());
            if (CollectionUtils.isNotEmpty(responseValue)) {
                List<StaticInfoEntity> configList = new ArrayList<>();
                for (Object[] item : responseValue) {
                    StaticInfoEntity staticInfoEntity = new StaticInfoEntity();
                    staticInfoEntity.setUnitId(item[0].toString());
                    staticInfoEntity.setUnitName(item[1].toString());
                    staticInfoEntity.setValue(String.format("%.2f", item[2]) + "%");
                    configList.add(staticInfoEntity);
                }
                int size = 3;
                if (configList.size() < 3) {
                    size = configList.size();
                }
                map.put("alarmConfigure", configList.subList(0, size));
            } else {
                //保留以前的取数逻辑
                Collections.sort(newAlarmList, Comparator.comparing(AlarmStatisticEntity::getConfigure, Comparator.reverseOrder()).thenComparing(AlarmStatisticEntity::getName, collatorChinese));
                List<StaticInfoEntity> configList = new ArrayList<>();
                newAlarmList.subList(0, length).forEach((item) -> {
                    configList.add(new StaticInfoEntity(item.getName(), item.getUnitId(), String.format("%.2f", item.getConfigure()) + "%"));
                });
                map.put("alarmConfigure", configList);
            }

            Collections.sort(newAlarmList, Comparator.comparing(AlarmStatisticEntity::getHold, Comparator.reverseOrder()).thenComparing(AlarmStatisticEntity::getName, collatorChinese));
            List<StaticInfoEntity> holdList = new ArrayList<>();
            newAlarmList.subList(0, length).forEach((item) -> {
                holdList.add(new StaticInfoEntity(item.getName(), item.getUnitId(), String.format("%.2f", item.getHold()) + "%"));
            });
            map.put("alarmHold", holdList);

            Collections.sort(newAlarmList, Comparator.comparing(AlarmStatisticEntity::getRecovery, Comparator.reverseOrder()).thenComparing(AlarmStatisticEntity::getName, collatorChinese));
            List<StaticInfoEntity> recoveryList = new ArrayList<>();
            newAlarmList.subList(0, length).forEach((item) -> {
                recoveryList.add(new StaticInfoEntity(item.getName(), item.getUnitId(), String.format("%.2f", item.getRecovery()) + "%"));
            });
            map.put("alarmRecovery", recoveryList);

        } catch (Exception ex) {
            throw ex;
        }
        return map;
    }

    /**
     * 查看报警评估总览
     *
     * @param startTime
     * @param endTime
     * @param assessLevel 评估等级
     * @return 等级评估详情
     * <AUTHOR> 2019-12-20
     */
    @SuppressWarnings("unchecked")
    @Override
    public AlarmLevelAssessDetailEntity getAlarmAssessOverviewDetail(Date startTime, Date endTime, Long assessLevel)
            throws Exception {
        AlarmLevelAssessDetailEntity alarmLevelAssessDetailEntity = new AlarmLevelAssessDetailEntity();

        //region 解析时间和装置ID
        List<DictionaryEntity> dateTimeLists = new ArrayList<>();
        Map<String, Object> dateTimeMap = basicDataService.getSearchTimeByDate(startTime, endTime);
        dateTimeMap.entrySet().forEach(x -> {
            DictionaryEntity dic = new DictionaryEntity();
            dic.setKey(x.getKey());
            dic.setValue(x.getValue());
            dateTimeLists.add(dic);
        });

        startTime = (Date) (dateTimeLists.get(0).getValue());
        endTime = (Date) (dateTimeLists.get(1).getValue());
        Date newEndTime = endTime;
//        Date lastWeekStartDate = (Date) (dateTimeLists.get(2).getValue());
//        Date lastWeekEndDate = (Date) (dateTimeLists.get(3).getValue());
        String endFlag = String.valueOf(dateTimeLists.get(5).getValue());
        if ("<".equals(endFlag)) {
            newEndTime = DateUtils.addSeconds(endTime, -1);
        }
        //endregion

        //获取授权装置
        List<UnitEntity> unitList = basicDataService.getUnitList(true);
        String[] unitIds1 = unitList.stream().map(x -> x.getStdCode()).toArray(String[]::new);
        String strUnitIds = StringUtils.join(unitIds1, ",");
        List<GridViewEntity> gridViewEntityList = getGridViewData(startTime, newEndTime, strUnitIds);
        //gridViewEntityList = getTop4GridViewData(gridViewEntityList, lastWeekStartDate, lastWeekEndDate, false);

        if (null != assessLevel) {
            for (int i = gridViewEntityList.size(); i > 0; i--) {
                if (!gridViewEntityList.get(i - 1).getAssessLevel().equals(assessLevel)) {
                    gridViewEntityList.remove(i - 1);
                }

            }
        }
        List<GridViewEntity> selectAlarmEvent = new ArrayList<>();
        GridViewEntity gridViewEntity = gridViewEntityList.stream().findFirst().orElse(null);
        if (gridViewEntity != null) {
            selectAlarmEvent.add(gridViewEntity);
        }

        //region 评估等级装置ID集合
        Map<Long, List<String>> unitIds = gridViewEntityList.stream().
                collect(Collectors.groupingBy(GridViewEntity::getAssessLevel, Collectors.mapping(GridViewEntity::getUnitId, Collectors.toList())));
        List<DictionaryEntity> dicUnitIds = new ArrayList<>();
        unitIds.entrySet().forEach(x -> {
            DictionaryEntity dic = new DictionaryEntity();
            dic.setKey(x.getKey());
            dic.setValue(x.getValue());
            dicUnitIds.add(dic);
        });

        // 趋势图数据
        List<String> vtDateList = getVariationTrendDate(startTime, endTime);
        List<List<VariationTrendEntity>> variationTrendDataList = getVariationTrendData(vtDateList, gridViewEntityList, endTime);
        alarmLevelAssessDetailEntity.setGridViewEntityList(gridViewEntityList);
        alarmLevelAssessDetailEntity.setVariationTrendList(variationTrendDataList);
        alarmLevelAssessDetailEntity.setVariationTrendDate(vtDateList);
        alarmLevelAssessDetailEntity.setDateTimeList(dateTimeLists);
        alarmLevelAssessDetailEntity.setUnitIdList(dicUnitIds);

        return alarmLevelAssessDetailEntity;
    }


    @Override
    public List<PeakAlarmRateEntity> getPeakAlarmRate(Date startTime, Date endTime, String[] unitIds) throws Exception {

        List<UnitEntity> unitList = basicDataService.getUnitListByIds(unitIds, false);
        List<PeakAlarmRateEntity> returnList = new ArrayList<>();

        AlarmAssessFirstPageEntity alarmAssessFirstPage = getAlarmAssessFirstPage(startTime, endTime, unitIds);
        List<GridViewEntity> gridViewEntityList = alarmAssessFirstPage.getGridViewEntityList();
        for (GridViewEntity gridViewEntity : gridViewEntityList) {

            PeakAlarmRateEntity peakAlarmRateEntity = new PeakAlarmRateEntity();
            peakAlarmRateEntity.setUnitName(gridViewEntity.getUnitName());
            peakAlarmRateEntity.setUnitCode(gridViewEntity.getUnitId());
            String peakAlarmRateStr = null != gridViewEntity.getPeakAlarmRate() ? gridViewEntity.getPeakAlarmRate().toString() : "0";
            String substring = peakAlarmRateStr.substring(0, peakAlarmRateStr.length() - 3);
            Integer peakAlarmRateInt = Integer.parseInt(substring);
            peakAlarmRateEntity.setPeakAlarmRate(peakAlarmRateInt);

            returnList.add(peakAlarmRateEntity);
        }
        for (UnitEntity unitEntity : unitList) {
            if (!returnList.stream().filter(w -> w.getUnitCode().equals(unitEntity.getStdCode())).findAny().isPresent()) {
                PeakAlarmRateEntity peakAlarmRateEntity = new PeakAlarmRateEntity();
                peakAlarmRateEntity.setUnitName(unitEntity.getSname());
                peakAlarmRateEntity.setUnitCode(unitEntity.getStdCode());

                peakAlarmRateEntity.setPeakAlarmRate(0);

                returnList.add(peakAlarmRateEntity);
            }
        }
        return returnList;
    }

    @Override
    public List<UnitResponseNumberVO> getUnitAlarmConfirmRate(Date startDate, Date endDate) {
        List<UnitResponseNumberVO> voList = null;
        try {
            voList = alarmRecDAO.selectUnitResponseNumber(startDate, endDate);
            if (CollectionUtils.isNotEmpty(voList)) {
                for (UnitResponseNumberVO vo : voList) {
                    BigDecimal alarmNumber = vo.getAlarmNumber();
                    if (alarmNumber.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal responseNumber = vo.getResponseNumber();
                        BigDecimal responseRate = BigDecimal.ZERO;
                        if (responseNumber.compareTo(BigDecimal.ZERO) > 0) {
                            responseRate = responseNumber.divide(alarmNumber, 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal("100"));

                        }
                        vo.setResponseRateDec(responseRate);
                        vo.setResponseRate(String.format("%.2f", responseRate) + "%");
                        BigDecimal timelyResponseNumber = vo.getTimelyResponseNumber();
                        BigDecimal timelyResponse = BigDecimal.ZERO;
                        if (timelyResponseNumber.compareTo(BigDecimal.ZERO) > 0) {
                            timelyResponse = timelyResponseNumber.divide(alarmNumber, 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal("100"));
                        }
                        vo.setTimelyResponseRate(String.format("%.2f", timelyResponse) + "%");
                    }
                }
                voList = voList.stream().sorted(Comparator.comparing(UnitResponseNumberVO::getResponseRateDec, Comparator.reverseOrder())).collect(Collectors.toList());;
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return voList;
    }

    @Override
    public Page<UnitResponseNumberVO> getUnitAlarmStatistics(Date startDate, Date endDate, Pagination page) {
        Page<UnitResponseNumberVO> pageList = null;
        try {
            Page<UnitResponseNumberVO> pager = new Page<UnitResponseNumberVO>(page.getPageNumber(), page.getPageSize());
            pageList = alarmRecDAO.selectUnitResponseNumber(pager, startDate, endDate);
            List<UnitResponseNumberVO> voList = pageList.getRecords();
            if (CollectionUtils.isNotEmpty(voList)) {
                for (UnitResponseNumberVO vo : voList) {
                    BigDecimal alarmNumber = vo.getAlarmNumber();
                    if (alarmNumber.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal responseNumber = vo.getResponseNumber();
                        BigDecimal responseRate = BigDecimal.ZERO;
                        if (responseNumber.compareTo(BigDecimal.ZERO) > 0) {
                            responseRate = responseNumber.divide(alarmNumber, 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal("100"));
                        }
                        vo.setResponseRate(String.format("%.2f", responseRate) + "%");
                        BigDecimal timelyResponseNumber = vo.getTimelyResponseNumber();
                        BigDecimal timelyResponse = BigDecimal.ZERO;
                        if (timelyResponseNumber.compareTo(BigDecimal.ZERO) > 0) {
                            timelyResponse = timelyResponseNumber.divide(alarmNumber, 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal("100"));
                        }
                        vo.setTimelyResponseRate(String.format("%.2f", timelyResponse) + "%");
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return pageList;
    }

    @Override
    public List<WorkshopResponseNumberVO> getWorkshopAlarmConfirmRate(Date startDate, Date endDate) {
        List<WorkshopResponseNumberVO> voList = null;
        try {
            voList = alarmRecDAO.selectWorkshopResponseNumber(startDate, endDate);
            if (CollectionUtils.isNotEmpty(voList)) {
                for (WorkshopResponseNumberVO vo : voList) {
                    BigDecimal alarmNumber = vo.getAlarmNumber();
                    if (alarmNumber.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal responseNumber = vo.getResponseNumber();
                        BigDecimal responseRate = BigDecimal.ZERO;
                        if (responseNumber.compareTo(BigDecimal.ZERO) > 0) {
                            responseRate = responseNumber.divide(alarmNumber, 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal("100"));

                        }
                        vo.setResponseRateDec(responseRate);
                        vo.setResponseRate(String.format("%.2f", responseRate) + "%");
                        BigDecimal timelyResponseNumber = vo.getTimelyResponseNumber();
                        BigDecimal timelyResponse = BigDecimal.ZERO;
                        if (timelyResponseNumber.compareTo(BigDecimal.ZERO) > 0) {
                            timelyResponse = timelyResponseNumber.divide(alarmNumber, 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal("100"));
                        }
                        vo.setTimelyResponseRate(String.format("%.2f", timelyResponse) + "%");
                    }
                }
                voList = voList.stream().sorted(Comparator.comparing(WorkshopResponseNumberVO::getResponseRateDec, Comparator.reverseOrder())).collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return voList;
    }

    @Override
    public Page<WorkshopResponseNumberVO> getWorkshopAlarmStatistics(Date startDate, Date endDate, Pagination page) {
        Page<WorkshopResponseNumberVO> voPage = null;
        try {
            Page<WorkshopResponseNumberVO> pager = new Page<WorkshopResponseNumberVO>(page.getPageNumber(), page.getPageSize());
            voPage = alarmRecDAO.selectWorkshopResponseNumber(pager, startDate, endDate);
            List<WorkshopResponseNumberVO> voList = voPage.getRecords();
            if (CollectionUtils.isNotEmpty(voList)) {
                for (WorkshopResponseNumberVO vo : voList) {
                    BigDecimal alarmNumber = vo.getAlarmNumber();
                    if (alarmNumber.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal responseNumber = vo.getResponseNumber();
                        BigDecimal responseRate = BigDecimal.ZERO;
                        if (responseNumber.compareTo(BigDecimal.ZERO) > 0) {
                            responseRate = responseNumber.divide(alarmNumber, 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal("100"));
                        }
                        vo.setResponseRate(String.format("%.2f", responseRate) + "%");
                        BigDecimal timelyResponseNumber = vo.getTimelyResponseNumber();
                        BigDecimal timelyResponse = BigDecimal.ZERO;
                        if (timelyResponseNumber.compareTo(BigDecimal.ZERO) > 0) {
                            timelyResponse = timelyResponseNumber.divide(alarmNumber, 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal("100"));
                        }
                        vo.setTimelyResponseRate(String.format("%.2f", timelyResponse) + "%");
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return voPage;
    }
}
