package com.pcitc.opal.pm.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.pm.pojo.SystRunParaConf;

/*
 * 系统运行参数配置实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_SystRunParaConfRepository
 * 作       者：kun.zhao
 * 创建时间：2018/01/22
 * 修改编号：1
 * 描       述：系统运行参数配置实体的Repository实现   
 */
public interface SystRunParaConfRepository extends JpaRepository<SystRunParaConf, Long>, SystRunParaConfRepositoryCustom{

}
