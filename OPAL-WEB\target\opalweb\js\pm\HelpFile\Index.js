var addUrl = OPAL.API.pmUrl + '/helpFile/addHelpFile';
var delUrl = OPAL.API.pmUrl + '/helpFile/deleteHelpFile';
var searchUrl = OPAL.API.pmUrl + '/helpFile';
var downloadFileUrl = OPAL.API.pmUrl + '/helpFile/downloadFile';
var currentTimeUrl = OPAL.API.commUrl + "/getSysDateTime";
window.pageLoadMode = PageLoadMode.None;
$(function() {
    var page = {
        /**
         * 初始化
         */
        init: function() {
            //业务类型 1上传2查询
            BusinessType =page.logic.getQueryParam("BusinessType")
            if (BusinessType ==2){
                $("#HelpFileAdd").hide();
                $("#pageTitle").html("帮助文档")
            }
            //绑定事件
            this.bindUI();

            // 初始化表格
            page.logic.initTable();
            page.logic.search();
        },
        /**
         * 绑定事件
         */
        bindUI: function() {
            // 新增
            $('#HelpFileAdd').click(function() {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            })

        },
        data: {
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化表格
             */
            initTable: function() {
                if(BusinessType ==1){

                    OPAL.ui.initBootstrapTable("table", {
                        cache: false,
                        columns: [{
                            title: "操作",
                            field: 'event_cancel',
                            rowspan: 1,
                            align: 'center',
                            width: '90px',
                            formatter: page.logic.onActionRenderer
                        }, {
                            field: 'fileName',
                            title: '文档',
                            align: 'left',
                            width: '90px',
                            formatter: page.logic.onActionRenderer1
                        }, {
                            field: 'upLoadTime',
                            title: '上传时间',
                            align: 'center',
                            width: '140px'
                        }, {
                            field: 'upLoadUserName',
                            title: '上传人',
                            align: 'left',
                            width: '90px'
                        }],
                    }, page.logic.queryParams)
                }else if(BusinessType ==2){
                    OPAL.ui.initBootstrapTable("table", {
                        cache: false,
                        columns: [ {
                            field: 'fileName',
                            title: '文档',
                            align: 'left',
                            width: '90px',
                            formatter: page.logic.onActionRenderer1
                        }, {
                            field: 'upLoadTime',
                            title: '上传时间',
                            align: 'center',
                            width: '140px'
                        }, {
                            field: 'upLoadUserName',
                            title: '上传人',
                            align: 'left',
                            width: '90px'
                        }],
                    }, page.logic.queryParams)
                }
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function() {
                var rowData = arguments[1];
                return [
                    '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.helpFileId + '\')" >删除</a> '
                ]
            },
            /**
             * 触发下载
             * @returns {string[]}
             */
            onActionRenderer1: function() {
                var rowData = arguments[1];
                return [
                    '<a  name="DownLoad"  href="javascript:window.page.logic.download(\'' + rowData.fileId + '\',\'' + rowData.fileName + '\')" >'+rowData.fileName+'</a> '
                ]
            },

            /**
             * 单条删除
             */
            delSingle: function(id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function(result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function(index) {
                    layer.close(index)
                });
            },
            /**
             * 新增
             */
            add: function() {
                var pageMode = PageModelEnum.NewAdd;
                var title = "帮助文档";
                page.logic.detail(title, "", pageMode);
            },
            /**
             * 获取页面URL参数
             * @param  {name}
             */
            getQueryParam: function(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
                var r = window.location.search.substr(1).match(reg); //匹配目标参数
                if (r != null) return unescape(r[2]);
                return null; //返回参数值
            },

            /**
             * 帮助文档新增
             */
            detail: function(title, helpFileId, pageMode) {
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: '',
                    area: ['930px', '300px'],
                    shadeClose: false,
                    content: 'HelpFileAdd.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "helpFileId": helpFileId,
                            'title': title
                        };
                        iframeWin.page.logic.setPage(data);
                    },
                    end: function() {
                        if(window.isRefresh){
                            $('#table').bootstrapTable('selectPage', 1);
                        }
                    }
                })
            },
            /**
             * 搜索
             */
            search: function() {

                // page.data.param = OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            download: function(helpFileId,fileName) {
                $('#formExPort').attr('action', downloadFileUrl);
                $('#fileName1').val(fileName);
                $('#fileId1').val(helpFileId);
                $('#formExPort').submit();
            }


        }
    }
    page.init();
    window.page = page;
})