package com.pcitc.opal.pm.dao;

import java.util.List;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.PrdtCell;
import org.springframework.core.KotlinReflectionParameterNameDiscoverer;

/*
 * PrdtCell实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_PrdtCellRepositoryCustom
 * 作       者：zheng.yang
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描       述：PrdtCell实体的Repository的JPA自定义接口 
 */
public interface PrdtCellRepositoryCustom {

	/**
	 * 校验数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param prdtCell
	 *            生产单元实体
	 * @return 返回结果信息类
	 */
	CommonResult prdtCellValidation(PrdtCell prdtCell);

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param prdtCell
	 *            生产单元实体
	 * @return 返回结果信息类
	 */
	CommonResult addPrdtCell(PrdtCell prdtCell);

	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param prdtCellIds
	 *            生产单元ID集合
	 * @return 返回结果信息类
	 */
	CommonResult deletePrdtCell(Long[] prdtCellIds);

	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param prdtCell
	 * @return 返回结果信息类
	 */
	CommonResult updatePrdtCell(PrdtCell prdtCell);

	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param prdtCellId
	 *            生产单元ID
	 * @return 生产单元实体
	 */
	PrdtCell getSinglePrdtCell(Long prdtCellId);

	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param prdtCellIds
	 *            生产单元ID集合
	 * @return 生产单元实体集合
	 */
	List<PrdtCell> getPrdtCell(Long[] prdtCellIds);

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param unitCodes
	 *            装置编码集合
	 * @param name
	 *            生产单元名称
	 * @param inUse
	 *            是否启动
	 * @param page
	 *            分页参数
	 * @return 生产单元实体集合
	 */
	PaginationBean<PrdtCell> getPrdtCell(String[] unitCodes, String name, Integer inUse, Pagination page);

	List<PrdtCell> getPrdtCellByUnitCode(String[] unitCodes);


	List<PrdtCell> getPrdtCellByCompanyId(Long companyId);
}
