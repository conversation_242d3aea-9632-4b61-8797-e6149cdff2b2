OPAL.API.pmUrl = "{server.url}/opalservice/api/pm";
OPAL.API.commUrl = "{server.url}/opalservice/api/common";
OPAL.API.adUrl = "{server.url}/opalservice/api/ad";
OPAL.API.aaUrl = "{server.url}/opalservice/api/aa";
OPAL.API.aeUrl = "{server.url}/opalservice/api/ae";
OPAL.API.afUrl = "{server.url}/opalservice/api/af";
OPAL.API.acUrl = "{server.url}/opalservice/api/ac";
OPAL.API.asUrl = "{server.url}/opalservice/api/as";
OPAL.API.akUrl = "{server.url}/opalservice/api/ak";
OPAL.API.apUrl = "{server.url}/opalservice/api/ap";
OPAL.API.portalUrl = "{server.url}/opalservice/api/portal";
//
// OPAL.API.pmUrl = "http://127.0.0.1:8068/opalservice/api/pm";
// OPAL.API.commUrl = "http://127.0.0.1:8068/opalservice/api/common";
// OPAL.API.adUrl = "http://127.0.0.1:8068/opalservice/api/ad";
// OPAL.API.aaUrl = "http://127.0.0.1:8068/opalservice/api/aa";
// OPAL.API.aeUrl = "http://127.0.0.1:8068/opalservice/api/ae";
// OPAL.API.afUrl = "http://127.0.0.1:8068/opalservice/api/af";
// OPAL.API.acUrl = "http://127.0.0.1:8068/opalservice/api/ac";
// OPAL.API.asUrl = "http://127.0.0.1:8068/opalservice/api/as";
// OPAL.API.akUrl = "http://127.0.0.1:8068/opalservice/api/ak";
// OPAL.API.apUrl = "http://127.0.0.1:8068/opalservice/api/ap";
// OPAL.API.portalUrl = "http://127.0.0.1:8068/opalservice/api/portal";