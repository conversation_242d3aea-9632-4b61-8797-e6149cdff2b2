var initSendMsgUrl = OPAL.API.pmUrl + "/alarmMsgConfig/getInSendMsgList";     //是否发送报警短信
var searchUrl = OPAL.API.pmUrl + "/alarmPoints/getAlarmPointList";   //查询
var saveUrl = OPAL.API.pmUrl + '/alarmMsgConfig/updateAlarmMsgConfigForInUse';
var alarmFlagUrl = OPAL.API.pmUrl + '/alarmMsgConfig/getAlarmFlagList';
var addUrl = OPAL.API.pmUrl +'/alarmMsgConfigNew/addAlarmMsgConfigInfo';
var isRefresh = false;
window.pageLoadMode = PageLoadMode.None;
var pageIndex = 1;
var pageMode = PageModelEnum.NewAdd;
var alarmPointIds;

var mobileBookId;
var mobileBook;

$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var f =parent.layer.getFrameIndex(window.parent.name);
    var page = {
        /**
         * 初始化
         */
        init: function () {
            //绑定事件
            this.bindUI();
            //初始化查询是否发送报警短信
            page.logic.initSendMsg();
            //初始化表格
            page.logic.initTable();
            //初始化报警点
            page.logic.initAlarmFlag();
            //默认查询数据
            //page.logic.search();
        },
        /**
         * 绑定事件
         */
        bindUI: function () {
            //批量配置
            // $('#batchConfig').click(function () {
            //     page.logic.config();
            // });
            // $('#inUse').click(function () {
            //     page.logic.notOrInUse(1);
            // })
            // $('#notUse').click(function () {
            //     page.logic.notOrInUse(0);
            // })
            //查询
            $('#btnSearch').click(function () {
                page.logic.search();
            });
            $("#choice").click(function () {
                page.logic.choice();
            });
            $("#saveAddModal").click(function(){
                page.logic.addModal();
            });
            $('.closeBtn').click(function () {
                // page.logic.closeLayer(false);
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            });
        },
        data: {
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化表格
             */
            initTable: function () {
                // var results = JSON.parse(data);
                OPAL.ui.initBootstrapTable("table", {
                    cache: false,
                    columns: [
                        {
                            title: "装置",
                            field: 'unitSname',
                            rowspan: 1,
                            width: '150px',
                            align: 'left',
                        }, {
                            title: "生产单元",
                            field: 'prdtCellSname',
                            rowspan: 1,
                            width: '150px',
                            align: 'left',
                        },
                        {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }]
                },page.logic.queryParams);
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },


            setData: function (data) {
                pageMode = data.pageMode;
                $("#pageTitle").text(data.title);
                alarmPointIds = data.alarmPointIds.split(",");
                var param = {
                    "alarmPointIds":alarmPointIds
                }
                page.data.param = param;
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },

            /**
             * 编辑
             * @param prdtCellId
             */
            addModal:function () {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var checks = [];
                $("input[type='checkbox'][name='alarmFlag']:checked").each(function () {
                    checks.push($(this).val())
                });
                if($("#inSendMsg").prop("checked")){
                    if(!$("#mobileBook").val()){
                        layer.msg("请选择电话本！");
                        return
                    }
                }

                var data = OPAL.form.getData('AddOrEditModal');
                data.alarmPointIds = alarmPointIds;
                data.alarmFlagIds = checks;
                data.mobileBookId = mobileBookId;
                $.ajax({
                    url:addUrl,
                    async:false,
                    type:"put",
                    data:data,
                    dataType: "text",
                    success:function(result, XMLHttpRequest){
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！",{
                                time: 1000
                            },function() {
                                window.pageLoadMode = PageLoadMode.Refresh;
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }

                });
            },
            formValidate:function(){
                    OPAL.form.formValidate('AddOrEditModal',{
                        rules:{
                            timeInterval:{
                                required: true,
                                digits: true
                            }
                        },
                        messages:{
                            timeInterval: {
                                number: '请输入正确数值'
                            }
                        }
                    })
            },
            // /**
            //  * 搜索
            //  */
            // search: function () {
            //     page.data.param = OPAL.form.getData("searchForm");
            //     $("#table").bootstrapTable('refresh', {
            //         "url": searchUrl,
            //         "pageNumber": pageIndex
            //     });
            // },

            /**
             * 初始化是否发送报警短信
             */
            initSendMsg: function () {
                OPAL.ui.getCombobox("inSendMsg", initSendMsgUrl, {
                    selectValue: 1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            // 报警标识
            initAlarmFlag:function () {
                $.ajax({
                    url:alarmFlagUrl,
                    type:"get",
                    dataType: "json",
                    success: function (data) {
                        var results = $.ET.toObjectArr(data);
                        var _li = ""
                        for(var i = 0;i < results.length;i++){
                            _li+= '<li><input type="checkbox" value="'+results[i].key+'" name="alarmFlag" checked="checked" class="top-and-bottom-style"><span>'+results[i].value+'</span></li>';
                        }
                        $("#alarmIdent").html(_li);
                    }
                })
            },
            // 选择
            choice:function () {
                var title = "手机号选择";
                layer.open({
                    type: 2,
                    title: false,
                    closeBtn:0,
                    area: ['920px', '560px'],
                    shadeClose: false,
                    content: 'MobileList.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "mobileBookId": mobileBookId,
                            "mobileBook": mobileBook,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function() {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            getSelect:function (selects,mobileBookIds) {
                $("#mobileBook").val(selects.join(","));
                mobileBook = selects.join("#");
                mobileBookId = mobileBookIds;
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                window.parent.pageLoadMode = window.pageLoadMode;
                // parent.layer.close(index);
                parent.page.logic.closethis();
            }
        }
    }
    page.init();
    window.page = page;
});