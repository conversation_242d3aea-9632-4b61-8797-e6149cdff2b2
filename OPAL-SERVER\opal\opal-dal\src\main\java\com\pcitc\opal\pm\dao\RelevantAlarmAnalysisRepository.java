package com.pcitc.opal.pm.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.ad.pojo.AlarmEvent;

/*
 * RelevantAlarmAnalysis实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_RelevantAlarmAnalysisRepository
 * 作       者：dageng.sun
 * 创建时间：2018/08/03
 * 修改编号：1
 * 描       述：RelevantAlarmAnalysis实体的Repository实现   
 */
public interface RelevantAlarmAnalysisRepository extends JpaRepository<AlarmEvent, Long>, RelevantAlarmAnalysisRepositoryCustom {
	
}
