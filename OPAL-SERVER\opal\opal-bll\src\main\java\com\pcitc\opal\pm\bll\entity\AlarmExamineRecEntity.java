package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.CommonEnum;
import lombok.Data;
import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.util.Date;

/**
 * @USER: chenbo
 * @DATE: 2023/4/17
 * @TIME: 10:19
 * @DESC:
 **/
@Data
public class AlarmExamineRecEntity extends BaseResRep {

    /**
     * 导出序号
     */
    private Integer number;

    /**
     * 报警审查记录ID
     */
    private Long alarmExamineRecId;

    /**
     * 报警记录ID
     */
    private Long alarmRecId;

    /**
     * 原恢复时间
     */
    private Date oldRecoveryTime;

    /**
     * 原更新时间
     */
    private Date oldUpdateTime;

    /**
     * 原报警是否恢复(1是；0否)
     */
    private Integer isAck;

    /**
     * 实际恢复时间
     */
    private Date recoveryTime;

    /**
     * 原因分析
     */
    private String reasonAnly;

    /**
     * 上传附件ID
     */
    private String uplAttaId;

    /**
     * 上传附件名称
     */
    private String uplAttaName;

    /**
     * 审查状态（0未提交；1已提交；2已通过；3已驳回）
     */
    private int examineStatus;

    /**
     * 审查状态（0未提交；1已提交；2已通过；3已驳回）
     */
    private String examineStatusName;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 提交人ID
     */
    private String submitUserId;

    /**
     * 提交人名称
     */
    private String submitUserName;

    /**
     * 审批时间
     */
    private Date aproTime;

    /**
     * 审批人ID
     */
    private String aproUserId;

    /**
     * 审批人名称
     */
    private String aproUserName;

    /**
     * 报警时间
     */
    private Date alarmTime;

    /**
     * 装置名称
     */
    private String unitName;

    /**
     * 生产单元
     */
    private String prdName;

    /**
     * 位号
     */
    private String tag;
    /**
     * 参数表述
     */
    private String location;

    /**
     * 描述
     */
    private String des;

    /**
     * 报警标识
     */
    private String flagName;

    /**
     * 报警标识
     */
    private Integer alarmFlagId;

    /**
     * 优先级
     */
    private String priorityName;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 报警时长
     */
    private Double alarmDuration;


    public void setExamineStatus(int examineStatus) {
        this.examineStatus = examineStatus;
        this.examineStatusName = CommonEnum.ExamineStatus.getName(examineStatus);
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
        if (priority != null) {
            this.priorityName = CommonEnum.AlarmPriorityEnum.getName(priority);
        }
    }
}
