package com.pcitc.opal.aa.bll.entity;

import lombok.Data;

/*
 * 优先级评估统计数据实体
 * 模块编号：pcitc_opal_bll_class_AlarmPriorityAssessEntity
 * 作  　  者：kun.zhao
 * 创建时间：2017-11-21
 * 修改编号：1
 * 描       述：优先级评估统计数据实体
 */
@Data
public class AlarmPriorityAssessEntity {
	
   /**
    * 系统报警事件总数
    */
    private Integer totalAlarmEvents;
    
    /**
     * 系统紧急报警率
     */
    private String sysEmergencyAlarmRate;
    
    /**
     * 系统重要报警率
     */
    private String sysImportantAlarmRate;
    
    /**
     * 系统一般报警率
     */
    private String sysGeneralAlarmRate;

	/**
	 * 优先级重要所占比例
	 */
	private String importanceRatio;

	/**
	 * 优先级紧急所占比例
	 */
	private String emergencyRatio;

	/**
	 * 优先级一般所占比例
	 */
	private String normalRatio;
}
