package com.pcitc.opal.ap.bll.imp;

import com.pcitc.opal.ap.bll.AlarmPushRuleService;
import com.pcitc.opal.ap.bll.AlarmPushRuleUnitRelService;
import com.pcitc.opal.ap.bll.entity.AlarmPushRuleEntity;
import com.pcitc.opal.ap.bll.entity.AlarmPushRuleUnitRelEntity;
import com.pcitc.opal.ap.dao.*;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleDetailEntityVO;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleUnitRelDetailEntityVO;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleUnitRelEntityVO;
import com.pcitc.opal.ap.pojo.AlarmPushRule;
import com.pcitc.opal.ap.pojo.AlarmPushRuleUnitRel;
import com.pcitc.opal.common.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/*
 * 报警制度管理业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmStdManagmtImpl
 * 作	者：kun.zhao
 * 创建时间：2018/02/28
 * 修改编号：1
 * 描    述：报警制度管理业务逻辑层实现类
 */
@Service
public class AlarmPushRuleUnitRelImpl implements AlarmPushRuleUnitRelService {


    @Autowired
    private AlarmPushRuleUnitRelDetailRepository alarmPushRuleUnitRelDetailRepository;
    @Autowired
    private AlarmPushRuleUnitRelRepository alarmPushRuleUnitRelRepository;
    @Autowired
    private GroupRepository groupRepository;


    @Override
    public PaginationBean<AlarmPushRuleUnitRelEntityVO> getAlarmPushRuleUnitRel(String name, Integer companyId, Integer pushType,Integer speciality,Long priority, Pagination page) throws Exception {
        PaginationBean<AlarmPushRuleUnitRelEntityVO> alarmPointGroupConfigList = alarmPushRuleUnitRelRepository.getAlarmPushRuleUnitRelPage(name,companyId, pushType,speciality,priority, page);
        return alarmPointGroupConfigList;
    }

    @Override
    public CommonResult addAlarmPushRuleUnitRel(AlarmPushRuleUnitRelEntity alarmPushRuleUnitRelEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmPushRuleUnitRel alarmPushRule = ObjectConverter.entityConverter(alarmPushRuleUnitRelEntity, AlarmPushRuleUnitRel.class);
        CommonUtil.returnValue(alarmPushRule, CommonEnum.PageModelEnum.NewAdd.getIndex());
        return alarmPushRuleUnitRelRepository.addAlarmPushRuleUnitRel(alarmPushRule);
    }

    @Override
    public CommonResult deleteAlarmPushRuleUnitRel(Long[] ids) throws Exception {
        List<Long> idl = Arrays.asList(ids);
        List<Long> apIdl=new ArrayList<>();
        Pagination page =new Pagination();
        page.setPageSize(Integer.MAX_VALUE);
        page.setPageNumber(1);
        idl.forEach(id->{
            List<AlarmPushRuleUnitRelDetailEntityVO> vos= alarmPushRuleUnitRelDetailRepository.getAlarmPushRuleUnitRelDetails(id,page).getPageList();
            vos.forEach(v->{
                apIdl.add(v.getApRuleUnitRelDetailId());
            });
        });
        alarmPushRuleUnitRelDetailRepository.deleteAlarmPushRuleUnitRelDetail(apIdl);
        return alarmPushRuleUnitRelRepository.deleteAlarmPushRuleUnitRel(ids);
    }

    @Override
    public CommonResult updateAlarmPushRuleUnitRel(AlarmPushRuleUnitRelEntity alarmPushRuleUnitRelEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmPushRuleUnitRel alarmPushRule = ObjectConverter.entityConverter(alarmPushRuleUnitRelEntity, AlarmPushRuleUnitRel.class);
        CommonUtil.returnValue(alarmPushRule, CommonEnum.PageModelEnum.NewAdd.getIndex());
        return alarmPushRuleUnitRelRepository.updateAlarmPushRuleUnitRel(alarmPushRule);
    }
}
