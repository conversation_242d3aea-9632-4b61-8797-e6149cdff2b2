package com.pcitc.opal.aa.bll.entity;

import com.pcitc.opal.common.bll.entity.DictionaryEntity;

import java.util.List;

/*
 * 等级评估详情页面实体
 * 模块编号：pcitc_opal_bll_class_AlarmLevelAssessDetailEntity
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/24
 * 修改编号：1
 * 描    述：等级评估详情页面实体
 */
public class AlarmLevelAssessDetailEntity {
    /**
     * 网格列数据
     */
    private List<GridViewEntity> gridViewEntityList;
    /**
     * 变化趋势图数据
     */
    private List<List<VariationTrendEntity>> variationTrendList;

    /**
     * 变化趋势图中x轴的时间
     */
    private List<String> variationTrendDate;

    /**
     * 时间集合
     */
    private List<DictionaryEntity> dateTimeList;


    /**
     * 评估等级装置编码集合
     */
    private List<DictionaryEntity> unitIdList;


    public List<DictionaryEntity> getDateTimeList() {
        return dateTimeList;
    }

    public void setDateTimeList(List<DictionaryEntity> dateTimeList) {
        this.dateTimeList = dateTimeList;
    }

    public List<GridViewEntity> getGridViewEntityList() {
        return gridViewEntityList;
    }

    public void setGridViewEntityList(List<GridViewEntity> gridViewEntityList) {
        this.gridViewEntityList = gridViewEntityList;
    }

    public List<List<VariationTrendEntity>> getVariationTrendList() {
        return variationTrendList;
    }

    public void setVariationTrendList(List<List<VariationTrendEntity>> variationTrendList) {
        this.variationTrendList = variationTrendList;
    }

    public List<String> getVariationTrendDate() {
        return variationTrendDate;
    }

    public void setVariationTrendDate(List<String> variationTrendDate) {
        this.variationTrendDate = variationTrendDate;
    }

    public List<DictionaryEntity> getUnitIdList() {
        return unitIdList;
    }

    public void setUnitIdList(List<DictionaryEntity> unitIdList) {
        this.unitIdList = unitIdList;
    }
}
