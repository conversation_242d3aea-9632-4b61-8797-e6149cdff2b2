package com.pcitc.opal.pm.bll.entity;
import com.pcitc.opal.common.bll.entity.BasicEntity;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

/**
 * <p>
 * 报警点分组
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-01
 */
public class AlarmPointGroupEntity extends BasicEntity {


    /**
     * 报警点分组ID
     */
    private Long alarmPointGroupId;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 装置编码
     */
    private String unitCode;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 报警标识ID
     */
    private String alarmFlagId;

    /**
     * 描述
     */
    private String des;

    public Long getAlarmPointGroupId() {
        return alarmPointGroupId;
    }

    public void setAlarmPointGroupId(Long alarmPointGroupId) {
        this.alarmPointGroupId = alarmPointGroupId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(String alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
