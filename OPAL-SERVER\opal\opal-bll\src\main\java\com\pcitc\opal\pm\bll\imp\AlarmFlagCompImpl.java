package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.common.*;
import com.pcitc.opal.common.CommonEnum.PageModelEnum;
import com.pcitc.opal.pm.bll.AlarmFlagCompService;
import com.pcitc.opal.pm.bll.entity.*;
import com.pcitc.opal.pm.dao.AlarmFlagCompRepository;
import com.pcitc.opal.pm.pojo.AlarmFlagComp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.List;

/*
 * 报警标识对照业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmFlagCompImpl
 * 作	者：jiangtao.xue
 * 创建时间：2018/03/30
 * 修改编号：1
 * 描	述：报警标识对照业务逻辑层实现类
 */
@Service
@Component
public class AlarmFlagCompImpl implements AlarmFlagCompService {
	/**
	 * 实例化数据访问层接口
	 */
	@Autowired
	private AlarmFlagCompRepository alarmFlagCompRepository;

	/**
	 * 新增报警标识对照
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompEntity 报警标识对照实体
	 * @return 报警标识对照实体
	 * @throws Exception 
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addAlarmFlagComp(AlarmFlagCompEntity alarmFlagCompEntity) throws Exception {
		// 实体转换为持久层实体
		AlarmFlagComp alarmFlagComp = ObjectConverter.entityConverter(alarmFlagCompEntity, AlarmFlagComp.class);
		// 数据校验
		alarmFlagCompValidation(alarmFlagComp);
		// 赋值  创建人、创建名称、创建时间
		CommonUtil.returnValue(alarmFlagComp, PageModelEnum.NewAdd.getIndex());
		CommonResult commonResult = alarmFlagCompRepository.addAlarmFlagComp(alarmFlagComp);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false){
			throw new Exception(commonResult.getMessage());
		}
		return commonResult;
	}

	/**
	 * 删除报警标识对照维护数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompIds  报警标识对照维护主键Id集合
	 * @return 报警标识对照主键id集合
	 * @throws Exception 
	 */
	@Override
	public CommonResult deleteAlarmFlagComp(Long[] alarmFlagCompIds) throws Exception {
		// 判断ID集合是否可用
		if (alarmFlagCompIds == null || alarmFlagCompIds.length <= 0) {
			throw new Exception("没有需要删除的报警标识对照数据！");
		}
		List<AlarmFlagComp> anlyAlarmFlagCompList = alarmFlagCompRepository.getAlarmFlagComp(alarmFlagCompIds);
		if (anlyAlarmFlagCompList == null || anlyAlarmFlagCompList.isEmpty())
			return new CommonResult();
		Long[] anlyAlarmFlagCompIdList = anlyAlarmFlagCompList.stream().map(item -> item.getAlarmFlagCompId()).toArray(Long[]::new);
		// 调用DAL删除方法
		CommonResult commonResult = alarmFlagCompRepository.deleteAlarmFlagComp(anlyAlarmFlagCompIdList);

		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
		return commonResult;
	}

	/**
	 * 修改报警标识对照
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompEntity 报警标识对照实体类
	 * @return 报警标识对照实体类
	 * @throws Exception 
	 */
	@Override
	public CommonResult updateAlarmFlagComp(AlarmFlagCompEntity alarmFlagCompEntity) throws Exception {
		// 实体转换持久层实体
		AlarmFlagComp alarmFlagComp = ObjectConverter.entityConverter(alarmFlagCompEntity, AlarmFlagComp.class);
		// 校验
		alarmFlagCompValidation(alarmFlagComp);
		// 实体转换为持久层实体
		alarmFlagComp = alarmFlagCompRepository.getSingleAlarmFlagComp(alarmFlagCompEntity.getAlarmFlagCompId());
		CommonUtil.objectExchange(alarmFlagCompEntity, alarmFlagComp);
		// 赋值 修改人、修改名称、修改时间
		CommonUtil.returnValue(alarmFlagComp, PageModelEnum.Edit.getIndex());
		// 调用DAL更新方法
		CommonResult commonResult = alarmFlagCompRepository.updateAlarmFlagComp(alarmFlagComp);
		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
		return commonResult;
	}

	/**
	 * 通过报警标识对照ID获取单条数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompId 报警标识对照主键id
	 * @throws Exception
	 * @return 报警标识对照实体类
	 */
	@Override
	public AlarmFlagCompEntity getSingleAlarmFlagComp(Long alarmFlagCompId) throws Exception {
		AlarmFlagComp alarmFlagComp = alarmFlagCompRepository.getSingleAlarmFlagComp(alarmFlagCompId);
		AlarmFlagCompEntity afce = ObjectConverter.entityConverter(alarmFlagComp, AlarmFlagCompEntity.class);
		afce.setDcsName(alarmFlagComp.getDcsCode().getName());
		afce.setAlarmFlagName(alarmFlagComp.getAlarmFlag().getName());
		return afce;
	}

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param dcsCodeId DCS名称
	 * @param alarmFlagSource 源报警事件标识
	 * @param alarmFlagId 本系统报警标识
	 * @param inUse   是否使用
	 * @param page 翻页实现类
	 * @return 翻页对象
	 * @throws Exception 
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<AlarmFlagCompEntity> getAlarmFlagComp(Long dcsCodeId, String alarmFlagSource, Long alarmFlagId,Integer inUse, Pagination page) throws Exception {
		PaginationBean<AlarmFlagComp> listAlarmFlagComp = alarmFlagCompRepository.getAlarmFlagComp(dcsCodeId,alarmFlagSource,alarmFlagId,inUse, page);
		PaginationBean<AlarmFlagCompEntity> returnAlarmFlagComp = new PaginationBean<>(page,listAlarmFlagComp.getTotal());
		returnAlarmFlagComp.setPageList(ObjectConverter.listConverter(listAlarmFlagComp.getPageList(), AlarmFlagCompEntity.class));
		returnAlarmFlagComp.getPageList().stream().forEach(x->{
			AlarmFlagComp afc = listAlarmFlagComp.getPageList().stream().filter(y->y.getAlarmFlagCompId().equals(x.getAlarmFlagCompId())).findFirst().orElse(null);
			x.setDcsName(afc.getDcsCode().getName());
			x.setAlarmFlagName(afc.getAlarmFlag().getName());
		});
		return returnAlarmFlagComp;
	}

	//region 私有方法

	/**
	 * 校验
	 *
	 * <AUTHOR> 2018-03-30
	 * @param entity 报警标识对照实体
	 * @throws Exception
	 */
	private void alarmFlagCompValidation(AlarmFlagComp entity) throws Exception {
		CommonResult commonResult = null;
		// 实体不能为空
		if (entity == null) {
			throw new Exception("没有报警标识对照数据！");
		}
		// 调用DAL与数据库相关的校验
		commonResult = alarmFlagCompRepository.alarmFlagCompValidation(entity);

		// 如果失败，直接throw
		if (commonResult.getIsSuccess() == false)
			throw new Exception(commonResult.getMessage());
	}
	//endregion
}
