package com.pcitc.opal.pm.bll;

import org.springframework.stereotype.Service;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.FactoryDemoEntity;

/*
 * 工厂业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_FactoryService
 * 作       者：dongsheng.zhao
 * 创建时间：2017/09/17
 * 修改编号：1
 * 描       述：工厂业务逻辑层接口 
 */
@Service
public interface FactoryDemoService {

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-09-18
	 * @param factoryEntity
	 *            工厂实体
	 */
	void addFactory(FactoryDemoEntity factoryEntity) throws Exception;

	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-09-18
	 * @param factoryIds
	 *            工厂ID集合
	 */
	void deleteFactory(Long[] factoryIds) throws Exception;

	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-09-18
	 * @param factoryEntity
	 */
	void updateFactory(FactoryDemoEntity factoryEntity) throws Exception;

	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-09-18
	 * @param factoryId
	 *            工厂ID
	 * @return 工厂实体
	 */
	FactoryDemoEntity getSingleFactory(Long factoryId);

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-09-18
	 * @param name
	 *            工厂名称
	 * @param inUse
	 *            是否启动
	 * @param page
	 *            分页参数
	 * @return 工厂实体集合
	 * @throws Exception
	 */
	PaginationBean<FactoryDemoEntity> getFactory(String name, String stdCode, Integer inUse, Pagination page)
			throws Exception;
}
