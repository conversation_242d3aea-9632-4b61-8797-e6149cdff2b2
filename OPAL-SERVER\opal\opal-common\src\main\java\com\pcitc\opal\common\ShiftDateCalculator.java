package com.pcitc.opal.common;


import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/*
 * 班组查询日期时间计算器
 * 模块编号：pcitc_opal_common_class_ShiftDateCalculator
 * 作       者：xuelei.wang
 * 创建时间：2018/1/5
 * 修改编号：1
 * 描       述：班组查询日期时间计算器
 */
@SuppressWarnings("unused")
public class ShiftDateCalculator {

    /**
     * 构造函数
     *
     * @param endDate 查询结束日期时间
     * <AUTHOR>  2018-01-11
     */
    public ShiftDateCalculator(Date endDate) {
        this.nowTime = new Date();
        init(endDate);
    }

    /**
     * 构造函数
     *
     * @param startDate 查询开始日期时间
     * @param endDate   查询结束日期时间
     * <AUTHOR>  2018-01-11
     */
    public ShiftDateCalculator(Date startDate, Date endDate) {
        this(endDate);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);

        startDate = DateUtils.setHours(startDate, calendar.get(Calendar.HOUR_OF_DAY));
        startDate = DateUtils.setMinutes(startDate, calendar.get(Calendar.MINUTE));
        startDate = DateUtils.setSeconds(startDate, calendar.get(Calendar.SECOND));
        startTime = startDate;
    }

    /**
     * 构造函数
     *
     * @param startDate 查询开始日期时间
     * @param endDate   查询结束日期时间
     * @param endFlag   如果不为空,则传入的开始时间和结束时间为实际查询时间;如果为空,则需要根据开始时间和结束时间计算;
     * <AUTHOR>  2018-01-11
     */
    public ShiftDateCalculator(Date startDate, Date endDate, String endFlag) {
        this(startDate, endDate);
        if(!StringUtils.isEmpty(endFlag)){
            this.startTime=startDate;
            this.endTime=endDate;
            this.endFlag=endFlag;
            this.lastWeekStartTime=DateUtils.addDays(this.startTime,-7);
            this.lastWeekEndTime=DateUtils.addDays(this.endTime,-7);
        }
    }


    /**
     * 根据结束日期计算查询开始日期和结束日期
     *
     * @param endDate 结束日期时间
     * <AUTHOR>  2018-01-11
     */
    private void init(Date endDate) {
        try {
            String queryTime = CommonPropertiesReader.getValue("query.time");
            Date queryDate;

            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
            queryDate = sdf.parse(queryTime);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(queryDate);

            Date queryAddOneDay = DateUtils.addDays(endDate, 1);
            queryAddOneDay = DateUtils.setHours(queryAddOneDay, calendar.get(Calendar.HOUR_OF_DAY));
            queryAddOneDay = DateUtils.setMinutes(queryAddOneDay, calendar.get(Calendar.MINUTE));
            queryAddOneDay = DateUtils.setSeconds(queryAddOneDay, calendar.get(Calendar.SECOND));

            //查询日期+1 8:00:00 <= 当前系统时间
            if (queryAddOneDay.getTime() <= nowTime.getTime()) {//是历史数据,完整的7天
                startTime = DateUtils.addDays(queryAddOneDay, -7);
                endTime = queryAddOneDay;

                endTime = DateUtils.setHours(endTime, calendar.get(Calendar.HOUR));
                endTime = DateUtils.setMinutes(endTime, calendar.get(Calendar.MINUTE));
                endTime = DateUtils.setSeconds(endTime, calendar.get(Calendar.SECOND));

                startFlag = ">=";
                endFlag = "<";
            } else {  //是当天数据,数据不是完整的7天
                startTime = DateUtils.addDays(queryAddOneDay, -7);
                endTime = nowTime;

                startFlag = ">=";
                endFlag = "<=";
            }
            startTime = DateUtils.setHours(startTime, calendar.get(Calendar.HOUR_OF_DAY));
            startTime = DateUtils.setMinutes(startTime, calendar.get(Calendar.MINUTE));
            startTime = DateUtils.setSeconds(startTime, calendar.get(Calendar.SECOND));

            lastWeekStartTime = DateUtils.addDays(startTime, -7);
            lastWeekEndTime = DateUtils.addDays(endTime, -7);
        } catch (Exception ex) {

        }
    }

    /**
     * 当前系统时间
     */
    private Date nowTime;
    /***
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 获取一周之前开始时间
     */
    private Date lastWeekStartTime;
    /**
     * 获取一周之前结束时间
     */
    private Date lastWeekEndTime;
    /**
     * 开始标识符(>、>=)
     */
	private String startFlag;
    /**
     * 结束标识符(<、<=)
     */
    private String endFlag;

    /**
     * 获取查询开始时间
     *
     * @return
     * <AUTHOR>  2018-01-05
     */
    public Date getQueryStartTime() {
        return startTime;
    }

    /**
     * 获取查询结束时间
     *
     * @return
     * <AUTHOR>  2018-01-05
     */
    public Date getQueryEndTime() {
        if ("<".equals(endFlag)) {
            return DateUtils.addSeconds(endTime, -1);
        } else {
            return endTime;
        }
    }

    /**
     * 获取开始时间显示时间
     *
     * @return
     * <AUTHOR>  2018-01-05
     */
    public Date getDisplayStartTime() {
        return startTime;
    }

    /**
     * 获取结束时间显示时间
     *
     * @return
     * <AUTHOR>  2018-01-05
     */
    public Date getDisplayEndTime() {
        return endTime;
    }

    /**
     * 获取一周之前查询开始时间
     *
     * @return
     * <AUTHOR>  2018-01-05
     */
    public Date getLastWeekQueryStartTime() {
        return lastWeekStartTime;
    }

    /**
     * 获取一周之前查询结束时间
     *
     * @return
     * <AUTHOR>  2018-01-05
     */
    public Date getLastWeekQueryEndTime() {
        if ("<".equals(endFlag)) {
            return DateUtils.addSeconds(lastWeekEndTime, -1);
        } else {
            return lastWeekEndTime;
        }
    }

    /**
     * 获取一周之前显示开始时间
     *
     * @return
     * <AUTHOR>  2018-01-05
     */
    public Date getLastWeekDisplayStartTime() {
        return lastWeekStartTime;
    }

    /**
     * 获取一周之前显示结束时间
     *
     * @return
     * <AUTHOR>  2018-01-05
     */
    public Date getLastWeekDisplayEndTime() {
        return lastWeekEndTime;
    }

    /**
     * 获取系统服务器现在时间
     *
     * @return
     * <AUTHOR> 2018-01-05
     */
    public Date getSystemNowTime() {
        return nowTime;
    }
}
