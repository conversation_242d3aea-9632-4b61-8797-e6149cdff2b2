package com.pcitc.opal.common;

import org.apache.chemistry.opencmis.client.api.*;
import org.apache.chemistry.opencmis.client.runtime.SessionFactoryImpl;
import org.apache.chemistry.opencmis.commons.PropertyIds;
import org.apache.chemistry.opencmis.commons.SessionParameter;
import org.apache.chemistry.opencmis.commons.data.ContentStream;
import org.apache.chemistry.opencmis.commons.enums.BindingType;
import org.apache.chemistry.opencmis.commons.enums.IncludeRelationships;
import org.apache.chemistry.opencmis.commons.enums.VersioningState;
import org.apache.chemistry.opencmis.commons.exceptions.CmisObjectNotFoundException;
import org.apache.chemistry.opencmis.commons.impl.MimeTypes;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.util.*;

/*
 * 文件上传公用类
 * 模块编号： pcitc_opal_common_class_CMISClient
 * 作       者：xuelei.wang
 * 创建时间：2018/2/28
 * 修改编号：1
 * 描       述：文件上传公用类
 */
public class CMISClient {
    /**
     * 附件上传工具用户名
     */
    private String userName;
    /**
     * 附件上传工具密码
     */
    private String password;
    /**
     * 附件上传根目录
     */
    private String repositoryName;
    /**
     * 文件上传服务对话
     */
    private Session session;
    /**
     * 目录地址
     */
    private String repName;
    /**
     * 附件上传Browser地址
     */
    private String alfrescoBrowserUrl;

    public CMISClient() {
        userName = ApplicationPropertiesReader.getValue("alfresco_username");
        password = ApplicationPropertiesReader.getValue("alfresco_password");
        repositoryName = ApplicationPropertiesReader.getValue("alfresco_folder_name");
        alfrescoBrowserUrl = ApplicationPropertiesReader.getValue("alfresco_browser_url");

        SessionFactory sessionFactory = SessionFactoryImpl.newInstance();
        Map<String, String> parameters = new HashMap<>();
        parameters.put(SessionParameter.USER, userName);
        parameters.put(SessionParameter.PASSWORD, password);
        parameters.put(SessionParameter.BINDING_TYPE, BindingType.BROWSER.value());
        parameters.put(SessionParameter.BROWSER_URL,alfrescoBrowserUrl);
        parameters.put(SessionParameter.COMPRESSION, "true");
        parameters.put(SessionParameter.CACHE_TTL_OBJECTS, "0");
        List<Repository> repositories;
        try {
            repositories = sessionFactory.getRepositories(parameters);
        } catch (Exception e) {
            throw e;
        }
        Repository alfrescoRepository;
        if (repositories != null && repositories.size() > 0) {
            alfrescoRepository = repositories.get(0);
            session = alfrescoRepository.createSession();
        }
        repName = "/" + repositoryName;
    }

    /**
     * 文件上传
     *
     * @param relPath     目录,可为空
     * @param fileName    文件名称
     * @param inputStream 文件流
     * @return
     * <AUTHOR> 2018-2-28
     */
    public String uploadFile(String relPath, String fileName, InputStream inputStream) throws Exception {
        String[] fileNames= fileName.split("%00");
        String suffix = fileNames[0].substring(fileNames[0].lastIndexOf(".") + 1);
        List<String> fileTypeList=new ArrayList();
        fileTypeList.add("docx");
        fileTypeList.add("doc");
        fileTypeList.add("xsl");
        fileTypeList.add("xlsx");
        fileTypeList.add("jpeg");
        fileTypeList.add("jpg");
        fileTypeList.add("png");
        fileTypeList.add("pdf");
        fileTypeList.add("ppt");
        fileTypeList.add("pptx");
        if(!fileTypeList.contains(suffix)) {
            throw new Exception("此文件格式不支持上传，</br>只能上传docx、doc、xsl、xlsx、jpeg、jpg、png、pdf、ppt、pptx格式文件！");
        }

        String documentId = null;
        String newName = null;
        if (!StringUtils.isEmpty(fileName)) {
            String[] arrName = StringUtils.split(fileName, ".");
            if (arrName.length == 2) {
                newName = UUID.randomUUID().toString() + "." + arrName[1];
            }
            else{
                newName = UUID.randomUUID().toString();
            }
        } else {
            newName = UUID.randomUUID().toString();
        }
        if (session != null) {
            try {
                if (relPath != null && !relPath.equals("")) {
                    createFileFolder(relPath);
                }
                Folder root = (Folder) session.getObjectByPath(repName + (relPath == null ? "" : relPath));
                ContentStream contentStream = session.getObjectFactory().createContentStream(fileName, -1, MimeTypes.getMIMEType("application/octet-stream"), inputStream);
                if (contentStream != null) {
                    Map<String, Object> properties = new HashMap<>();
                    properties.put(PropertyIds.OBJECT_TYPE_ID, "cmis:document");
                    properties.put(PropertyIds.NAME, newName);
                    Document document = root.createDocument(properties, contentStream, VersioningState.MAJOR);
                    if (document != null && document.getId() != null) {
                        documentId = document.getId();
                    }
                }
            } catch (Exception e) {
                throw new Exception("文件上传失败:" + e.getMessage());
            } finally {
                inputStream.close();
            }

        }
        System.out.println(documentId);
        return documentId;
    }

    /**
     * 删除文件
     *
     * @param fileId 文件ID
     * <AUTHOR> 2018-2-28
     */
    public boolean deleteFile(String fileId) throws Exception {
        try {
            ItemIterable<CmisObject> cmisObjectItem = null;
            if (fileId != null) {
                OperationContext context = session.createOperationContext();
                context.setFilterString("cmis:name,cmis:creationDate");
                context.setCacheEnabled(false);
                context.setIncludeAcls(false);
                context.setIncludeAllowableActions(false);
                context.setIncludePathSegments(false);
                context.setIncludePolicies(false);
                context.setIncludeRelationships(IncludeRelationships.NONE);
                context.setRenditionFilterString("cmis:none");
                context.setOrderBy("cmis:creationDate");
                cmisObjectItem = session.queryObjects("cmis:document", "cmis:objectId ='" + fileId.split(";")[0] + "'", false, context);
            }
            if (cmisObjectItem != null) {
                for (CmisObject cmisObject : cmisObjectItem) {
                    cmisObject.delete();
                }
            }
        } catch (CmisObjectNotFoundException e) {
           return true;
        }
        catch (Exception e) {
            throw new Exception("文件删除失败:"+e.getMessage());
        }
        return true;
    }

    /**
     * 下载文件
     *
     * @param fileId 文件ID
     * @return
     * <AUTHOR> 2018-2-28
     */
    public InputStream downloadFile(String fileId) throws Exception {
        InputStream inputStream = null;
        try {
            Document doc = (Document) session.getObject(fileId);
            if (doc != null) {
                inputStream = doc.getContentStream().getStream();
            }
        } catch (CmisObjectNotFoundException e) {
            throw new Exception("文件下载失败:文件不存在!");
        }
        return inputStream;
    }

    /**
     * 创建上传文件的所属文件夹
     *
     * @param relPath 目录
     * @return
     * <AUTHOR> 2018-2-28
     */
    private void createFileFolder(String relPath) {
        if (relPath != null && relPath.equals("")) {
            String[] paths = relPath.split("/");
            String fileFolderPath = "";
            for (int i = 1; i < paths.length; i++) {
                try {
                    if (!fileFolderPath.equals("")) {
                        createFolder(fileFolderPath, paths[i]);
                    } else {
                        createFolder("", paths[i]);
                    }

                } catch (Exception e) {
                    continue;
                } finally {
                    fileFolderPath += "/" + paths[i];
                }
            }
        }
    }

    /**
     * 创建一个目录对象
     *
     * @param relPath    目录
     * @param folderName 文件夹名称
     * @return
     * <AUTHOR> 2018-2-28
     */
    public String createFolder(String folderName, String relPath) {
        String folderId = null;
        if (session != null) {
            try {
                if (relPath != null && !relPath.equals("")) {
                    createFileFolder(relPath);
                }
                String folderPath = repName + (relPath == null ? "" : relPath);
                Folder root = (Folder) session.getObjectByPath(folderPath);
                Map<String, String> newFolderProps = new HashMap<>();
                newFolderProps.put(PropertyIds.OBJECT_TYPE_ID, "cmis:folder");
                newFolderProps.put(PropertyIds.NAME, folderName);
                Folder newFolder = root.createFolder(newFolderProps);
                if (newFolder != null && newFolder.getId() != null) {
                    folderId = newFolder.getId();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return folderId;
    }
}
