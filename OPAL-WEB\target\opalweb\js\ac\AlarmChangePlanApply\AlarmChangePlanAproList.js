$(function() {
    var searchUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getAlarmChangePlanAproList';
    var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
    var getAlarmChangePlanApproStatusListUrl = OPAL.API.commUrl + "/getAlarmChangePlanApproStatusList";
    window.pageLoadMode = PageLoadMode.None;
    var isLoading = true;
    var page = {
        /**
         * 初始化
         */
        init: function() {
            //绑定事件
            this.bindUI();
            // 初始化 时间设置
            page.logic.initTime();
            //初始化表格
            page.logic.initTable();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化审核状态
            page.logic.initAlarmChangePlanApproStatusList();

            //装置赋值
            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("AlarmChangePlanAproList");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }

            page.logic.search();
        },
        /**
         * 绑定事件
         */
        bindUI: function() {
            //表格自适应页面拉伸宽度
            $(window).resize(function() {
                $('#table').bootstrapTable('resetView');
            });
            //查询
            $('#btnSearch').click(function() {
                page.logic.search();
            })
        },
        data: {
            // 设置查询参数
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化表格
             */
            initTable: function() {
                var columns = [{
                    title: "操作",
                    field: 'opr',
                    align: 'center',
                    width: '60px',
                    formatter: page.logic.onActionRenderer
                }, {
                    title: "序号",
                    formatter: function(value, row, index) {
                        var data = page.data.param;
                        var pageNumber = data.pageNumber;
                        var pageSize = data.pageSize;
                        return index + 1 + (pageNumber - 1) * pageSize;
                    },
                    align: 'center',
                    width: '30px'
                }, {
                    title: "处理状态",
                    field: 'auditStatusName',
                    align: 'center',
                    width: '70px',
                    formatter: function(value, row, index) {
                            var className;
                            switch(row.auditStatus){
                                case 0 :
                                    className ="wf-status-submitted";
                                    break;
                                case 1 :
                                    className ="wf-status-audited";
                                    break;
                            }
                            return ["<span class='wf-status "+ className+"'>"+ value +"</span>"]
                        }
                }, {
                    title: "申请单编号",
                    field: 'planCode',
                    align: 'center',
                    width: '80px'
                }, {
                    title: "车间",
                    field: 'workshopName',
                    align: 'left',
                    width: '70px'
                }, {
                    title: "装置",
                    field: 'unitName',
                    align: 'left',
                    width: '80px'
                }, {
                    title: "申请时间",
                    field: 'applyTime',
                    align: 'center',
                    width: '100px'
                }, {
                    title: '提交时间',
                    field: 'submitTime',
                    align: 'center',
                    width: '100px'
                }, {
                    title: "提交人",
                    field: 'submitUserName',
                    align: 'center',
                    width: '60px'
                }];
                OPAL.ui.initBootstrapTable("table", {
                    cache: false,
                    striped: true,
                    columns: columns,
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function() {
                var rowData = arguments[1];
                if(rowData.auditStatusName == "已审核") {
                    return [
                    '<span name="TableEditor">审核</span> &nbsp;&nbsp;&nbsp;' +
                    '<a name="DeTails" href="javascript:window.page.logic.seeDetail(\'' + rowData.planId + '\')">详情</a> '
                    ]
                }else {
                    return [
                    '<a name="TableEditor" href="javascript:window.page.logic.audit(\'' + rowData.planId + '\',\'' + rowData.taskId +'\')">审核</a> &nbsp;&nbsp;&nbsp;' +
                    '<a name="DeTails" href="javascript:window.page.logic.seeDetail(\'' + rowData.planId + '\')">详情</a> '
                    ]
                }
            },
            /**
             * 审核
             */
            audit: function(planId, taskId) {
                var pageMode = 3;
                var title = "审核信息";
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: false,
                    area: ['95%', '100%'],
                    // shadeClose: false,
                    content: 'AlarmChangePlanLinkInfo.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "title": title,
                            "planId": planId,
                            "taskId": taskId
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function() {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             *  详情
             */
            seeDetail: function(planId) {
                var pageMode = PageModelEnum.View;
                var title = "调整方案详情";
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: false,
                    area: ['65%', '99%'],
                    shadeClose: false,
                    content: 'AlarmChangePlanDtl.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "planId": planId,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function() {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 搜索
             */
            search: function() {
                if (!page.logic.checkDateIsValid()) {
                    return false;
                }
                page.data.param = OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {

                }, false);
            },
            initAlarmChangePlanApproStatusList: function() {
                OPAL.ui.getCombobox("status", getAlarmChangePlanApproStatusListUrl, {
                    selectValue: 0
                }, null);
            },
            /**
             * 初始化 时间
             */
            initTime: function() {
                OPAL.ui.initDateTimePeriodPicker({
                    type: 'date',
                    format: 'yyyy-MM-dd',
                    startValue: '',
                    endValue: ''
                })
            },
            checkDateIsValid: function() {
                if ($('#startTime').val() != '' && $('#endTime').val() != '') {
                    var startDate = OPAL.util.strToDate($('#startTime').val());
                    var endDate = OPAL.util.strToDate($('#endTime').val());
                    if ((endDate - startDate) < 0) {
                        layer.msg("开始时间须小于等于结束时间！");
                        return false;
                    }
                }
                return true;
            }
        }
    }
    page.init();
    window.page = page;
})