package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.HelpFile;

/*
 * 帮助文档实体的Repository的JPA自定义接口
 * 作    者：shufei.sui
 * 创建时间：2019/09/19
 * 修改编号：1
 * 描    述：帮助文档实体的Repository的JPA自定义接口
 */
public interface HelpFileRepositoryCustom {

    /**
     * 新增
     * @param helpFile
     * @return
     */
    CommonResult addHelpFile(HelpFile helpFile);

    /**
     * 删除
     * @param helpFileId
     * @return
     */
    CommonResult deleteHelpFile(Long helpFileId);

    /**
     * 获取帮助文档
     * @param page
     * @return
     */
    PaginationBean<HelpFile> getHelpFile(Pagination page);

    /**
     * 校验
     * @param helpFile
     * @return
     */
    CommonResult hrlpFileValidation(HelpFile helpFile);

    /**
     * 获取单条
     * @param helpFileId
     * @return
     */
    HelpFile getSingleHelpFile(Long helpFileId);
}
