var floodAlarmListUrl = OPAL.API.afUrl + '/floodAlarmAnalysis/getFloodAlarmList';
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var commonCasualMainUrl = OPAL.API.commUrl + "/getCausalAlarmAnalysis";
var commonAlarmOperateNumberUrl = OPAL.API.commUrl + "/getOperateAndAlarmNumberList";
var AlarmStatisticsUrl = OPAL.API.afUrl + '/floodAlarmAnalysis/getAlarmStatistics';//因果
var AlarmStatisticsExportUrl = OPAL.API.afUrl + '/floodAlarmAnalysis/getAlarmStatisticsExport';//因果

var displayName = "单元";
var floodAlarmChart;
var operNumChart;
var operNumChartOption;
var parameter = {};
var xAxis = {};
var hightLightRowIndex = -1;
var floodChartConfig;
var alarmPointId;
var alarmFlagId;
var queryHour = 8;
var isLoading = true;
var searchTypenow = ''
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            /**
             *绑定事件
             */
            this.bindUi();
            OPAL.util.extendDate();
            /**
             * 初始化日期时间选择控件组
             */
            OPAL.ui.initDateTimePeriodPicker();
            /**
             * 初始化装置数
             */
            page.logic.initUnitTree();
            page.logic.initFloodChart({});
            // page.logic.initCausalMainTable({});
            // page.logic.queryMostOperate();
            page.logic.initAlarmAndOperateTable({});

            page.logic.initOperateNumberChart();
            page.logic.initOpetateTable();
            page.logic.initTableDisplay({});
            OPAL.util.getQueryTime(function (data) {
                queryHour = moment(data, "HH:mm:ss").get('hour');
                if (queryHour == undefined) {
                    queryHour = 0;
                }
            });

            //装置赋值
            if (isLoading && (page.data.param.unitIds == null || page.data.param.unitIds == undefined || page.data.param.unitIds.length == 0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("FloodAlarmAnalysis");
                if (cookieValue != null && cookieValue != undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
            document.getElementById("btnSearch").click();
        },
        bindUi: function () {
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                floodAlarmChart.resize();
                operNumChart.resize();

            };
            /**
             * 导航切换
             */
            $('.myTab li').click(function () {
                var flag = $(this).attr('showFlag');
                if (flag == 'imgShow') {
                    $(this).find('img').attr('src', '../../../images/one1.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/tweo.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                } else if (flag == 'tableShow') {
                    $(this).find('img').attr('src', '../../../images/tweos.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                } else if (flag == 'unitShow') {
                    $(this).find('img').attr('src', '../../../images/treese.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/tweo.png');
                }
            })
            /**
             * 查询
             */
            $('#btnSearch').click(function () {
                if (OPAL.util.checkDateIsValid() == true) {
                    isLoading = false;
                    page.logic.initChartxAxisConfig();
                    page.logic.search();
                    $("#NowTimeSpan").html(OPAL.util.dateFormat(OPAL.util.strToDate($("#startTime").val()), "yyyy-MM-dd") + " 00:00:00 至" + OPAL.util.dateFormat(OPAL.util.strToDate($("#endTime").val()), "yyyy-MM-dd") + " 23:59:59");
                }
            })
            //表格自适应页面拉伸宽度
            $(window).resize(function () {
                $('#floodTable').bootstrapTable('resetView');
                $('#tableAlarmAndOperate').bootstrapTable('resetView');
                $('#MostAlarmOperateTable').bootstrapTable('resetView');
            });
            $('#MostAlarmOperateExport').click(function () {
                page.data.mostParam.titles = JSON.stringify(new Array(
                    {'value': "装置", 'key': "unitName"},
                    {'value': "生产单元", 'key': "prdtCellName"},
                    {'value': "位号", 'key': "tag"},
                    {'value': "位置描述", 'key': "location"},
                    {'value': "报警等级", 'key': "alarmFlag"},
                    {'value': "数量", 'key': "alarmCount"},
                    {'value': "时间点", 'key': "alarmTime"}
                ));
                var url = AlarmStatisticsExportUrl + "?" + $.param(page.data.mostParam);
                $('<form method="post" action="' + url + '"></form>').appendTo('body').submit().remove();
            })
        },
        data: {
            param: {},
            subParam: {},
            mostParam: {}
        },
        logic: {
            /***
             * 查询
             */
            search: function () {
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                var prdtIds = $("#prdtIds").val();
                if (prdtIds == "" || prdtIds == undefined || prdtIds.length == 0) {
                    displayName = "装置";
                } else {
                    displayName = "单元";
                }
                var data = $('#formSearch').serialize();
                page.logic.toggleDisplayName();
                $("#btnSearch").prop('disabled', true);
                $("#random").val(Math.random());
                $.ajax({
                    url: floodAlarmListUrl,
                    data: $('#formSearch').serialize(),
                    dataType: 'json',
                    success: function (data) {
                        page.logic.initFloodChart(data);
                        $("#totalCounts").text(data['counts']);
                        page.logic.initTableDisplay(data);
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }

                });
                $.ajax({
                    url: commonAlarmOperateNumberUrl,
                    data: $('#formSearch').serialize(),
                    dataType: 'json',
                    success: function (data) {
                        page.logic.initAlarmAndOperateTable(data);
                        page.logic.initOperateNumberChart(data);

                    },
                    error: function (data) {
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
                OPAL.util.getSearchTime({
                    startTime: $("#startTime").val(),
                    endTime: $("#endTime").val()
                }, function (data) {
                    var prdtIds = OPAL.ui.getComboMultipleSelect.getValues("prdtIds", false);
                    var unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                    var startTime = OPAL.util.dateFormat(OPAL.util.strToDate(data["startDate"]), "yyyy-MM-dd HH:mm:ss");
                    var endTime = OPAL.util.dateFormat(OPAL.util.strToDate(data["endDate"]), "yyyy-MM-dd HH:mm:ss");
                    page.data.param = {
                        unitIds: unitIds,
                        prdtIds: prdtIds,
                        startTime: startTime,
                        endTime: endTime,
                        now: Math.random(),
                    };
                    page.data.subParam = {
                        startTime: startTime,
                        endTime: endTime,
                        now: Math.random()
                    };
                    page.data.mostParam = {
                        unitIds: unitIds,
                        prdtIds: prdtIds,
                        startTime: startTime,
                        endTime: endTime,
                    };
                });
                $("#MostAlarmOperateTable").bootstrapTable('refresh', {
                    url: AlarmStatisticsUrl,
                    pageNumber: 1
                });
            },


            /**
             * 切换显示名称
             */
            toggleDisplayName: function () {
                $("#displayName").text("按" + displayName + "显示");
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function (node, checked) {
                        var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (unitIds != undefined && unitIds.length == 1) {
                            $("#prdtIds").combo('enable');
                            $('#prdtIds').combotree('setValues', []);
                            page.logic.initPrdtMultipleSelect(unitIds[0]);
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $('#prdtIds').combotree('setValues', []);
                            $("#prdtIds").combo('disable');
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    }
                }, false);
            },
            /**
             * 初始化生产单元下拉多选
             * @param unitId
             */
            initPrdtMultipleSelect: function (unitId) {
                OPAL.ui.getComboMultipleSelect('prdtIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function () {
                    $("#prdtIds").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化图表
             * @param data
             */
            initFloodChart: function (data) {

                if (floodAlarmChart && !floodAlarmChart.isDisposed()) {
                    floodAlarmChart.clear();
                    floodAlarmChart.dispose();
                }
                if (data == undefined || data['list'] == undefined || data['list'].length == 0) {
                    floodAlarmChart = OPAL.ui.chart.initEmptyChart('floodAlarmChart');
                    return;
                }
                var series = new Array();
                var option = {
                    legend: {
                        data: data['legend'],
                        left: 50,
                    },
                    grid: {
                        left: '2%',
                        right: '1%',
                        top: '11%',
                        height: '280px',
                        containLabel: true
                    },
                    xAxis: xAxis,
                    tooltip: {
                        trigger: 'item',
                        formatter: function (param) {
                            var floodName = page.logic.getFloodName(param);
                            return "时间:" + param.data[0] + "<br>" + param.seriesName + "&nbsp;&nbsp;" + floodName + "<br>报警数:" + param.data[1];
                        }
                    },
                    toolbox: {
                        right: '20px',
                        itemSize: 15,
                        lineStyle: {
                            color: ['#e5e5e5']
                        },

                        top: 7,
                        feature: {
                            dataZoom: {
                                yAxisIndex: 'none'
                            },
                            restore: {}
                        }
                    },
                    dataZoom: [{
                        type: 'inside',
                        throttle: 250
                    }],
                    yAxis: {
                        type: 'value',
                    },
                    series: series,
                    originalData: data['floodData']
                };
                if (data != undefined && data['list'] != undefined) {
                    for (var i = 0; i < data.list.length; i++) {
                        series.push({
                            name: data.list[i].name,
                            type: 'line',
                            data: data.list[i].data,
                            itemStyle: {
                                normal: {
                                    color: page.logic.getDisplayColor(data.list[i].name, data['mainTableEntityList'])
                                }
                            },
                            prdtId: data.list[i]['prdtId'],
                            unitId: data.list[i]['unitId'],
                            floodName: data.list[i]['floodName']
                        });
                    }
                }
                series.push(page.logic.getEmptySeriesData());
                floodAlarmChart = echarts.init(document.getElementById('floodAlarmChart'));
                floodAlarmChart.setOption(option);
                floodChartConfig = option;

                floodAlarmChart.on('click', function (param) {
                    var config = {};
                    config.endTime = param['value'][0];
                    config.startTime = moment(param["value"][0]).subtract(10, 'minute').format("YYYY-MM-DD HH:mm:ss");
                    // page.logic.hightLightAlarmAndOperateData(config);

                    var causalConfig;
                    for (var i = 0; i < floodChartConfig.series.length; i++) {
                        if (floodChartConfig.series[i]['name'] == param.seriesName) {
                            for (var j = 0; j < floodChartConfig.series[i]['data'].length; j++) {
                                if (floodChartConfig.series[i]['data'][j][0] == param.data[0] && floodChartConfig.series[i]['data'][j][1] == param.data[1]) {
                                    causalConfig = floodChartConfig.series[i]['data'];
                                    break;
                                }
                            }
                        }
                    }

                    if (causalConfig != undefined && causalConfig.length != 0) {
                        page.data.param.startTime = causalConfig[0][0];
                        page.data.param.endTime = causalConfig[causalConfig.length - 1][0];

                        page.data.param.unitIds = floodChartConfig.series[param.seriesIndex]['unitId'];
                        page.data.param.prdtIds = [floodChartConfig.series[param.seriesIndex]['prdtId']];
                        page.data.subParam.unitId = floodChartConfig.series[param.seriesIndex]['unitId'];
                        page.data.param.endTime = moment(page.data.param.endTime).add(10, 'minute').format("YYYY-MM-DD HH:mm:ss")
                        page.data.subParam.startTime = page.data.param.startTime
                        page.data.subParam.endTime = page.data.param.endTime;

                        // page.logic.queryMostOperate();
                        page.data.mostParam.unitIds = [floodChartConfig.series[param.seriesIndex]['unitId']];
                        page.data.mostParam.prdtIds = [floodChartConfig.series[param.seriesIndex]['prdtId']];
                        page.data.mostParam.startTime = page.data.param.startTime
                        page.data.mostParam.endTime = moment(page.data.param.endTime).add(10, 'minute').format("YYYY-MM-DD HH:mm:ss")
                        $("#MostAlarmOperateTable").bootstrapTable('refresh', {
                            url: AlarmStatisticsUrl,
                            pageNumber: 1
                        });

                    }
                });
            },
            /**
             * 获取填充Empty Series
             */
            getEmptySeriesData: function () {
                var dateType = $("#dateType").val();
                var startDateStr = $("#startTime").val();
                var endDateStr = $("#endTime").val();
                OPAL.util.getSearchTime({
                    'startTime': startDateStr,
                    'endTime': endDateStr
                }, function (data) {
                    if (data != undefined && data['startDate'] != undefined && data['endDate'] != undefined) {
                        startTimeStr = OPAL.util.dateFormat(OPAL.util.strToDate(data['startDate']), 'yyyy-MM-dd');
                        endTimeStr = OPAL.util.dateFormat(OPAL.util.strToDate(data['endDate']), 'yyyy-MM-dd');
                    }
                });
                var option = {
                    name: '',
                    type: 'line',
                    showSymbol: false,
                    data: [[startDateStr, ''], [endDateStr, '']],
                    itemStyle: {normal: {opacity: 0}},
                    lineStyle: {normal: {opacity: 0}}
                };
                if (dateType == "month") {
                    option.data = [[OPAL.util.dateFormat(OPAL.util.strToDate(startDateStr), "yyyy-MM-01"), ''], [OPAL.util.dateFormat(OPAL.util.strToDate(endDateStr), "yyyy-MM-01"), '']];
                } else if (dateType == "week") {
                    option.data = [[moment(startDateStr).format('MM-DD'), ''], [moment(endTimeStr).format('MM-DD'), '']];
                } else if (dateType == "day") {
                    option.data = [[moment(startDateStr).format('MM-DD'), ''], [moment(endTimeStr).format('MM-DD'), '']];
                } else if (dateType == "hour") {
                    option.data = [[moment(startDateStr).format('HH:mm'), ''], [moment(endTimeStr).format('HH:mm'), '']];
                }
                return option;
            },
            /***
             * 获取高频报警名称
             *
             * @param param
             * @param list
             * @returns {*}
             */
            getFloodName: function (param) {
                for (var i = 0; i < floodChartConfig.series.length; i++) {
                    if (floodChartConfig.series[i]['name'] == param.seriesName) {
                        for (var j = 0; j < floodChartConfig.series[i]['data'].length; j++) {
                            if (floodChartConfig.series[i]['data'][j][0] == param.data[0] && floodChartConfig.series[i]['data'][j][1] == param.data[1]) {
                                return floodChartConfig.series[i].floodName;
                            }
                        }
                    }
                }
            },
            /**
             * 获取显示的颜色
             * @param name
             * @param list
             * @returns {*}
             */
            getDisplayColor: function (name, list) {
                if (name == undefined || list == undefined) return "#348FE2";
                var index = 0;
                for (var i = 0; i < list.length; i++) {
                    if (list[i]['name'] == name) {
                        index = i;
                        break;
                    }
                }
                switch (index % 5) {
                    case 0:
                        return "#348FE2";
                    case 1:
                        return "#00ACAC";
                    case 2:
                        return "#FF5B57";
                    case 3:
                        return "#F6A630";
                    case 4:
                        return "#B17ED9";
                }
            },
            /**
             * 初始化图表x轴日期
             */
            initChartxAxisConfig: function () {
                OPAL.ui.chart.getxAxisTimeConfig(null, function (xAxisConfig, paramConfig) {
                    xAxis = xAxisConfig;
                    parameter = paramConfig;
                });
            },
            /**
             * 初始化操作/报警趋势图
             */
            initOperateNumberChart: function (data) {
                if (operNumChart && !operNumChart.isDisposed()) {
                    operNumChart.clear();
                    operNumChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    operNumChart = OPAL.ui.chart.initEmptyChart('chartAlarmAndOperate');
                    return;
                }
                operNumChart = echarts.init(document.getElementById('chartAlarmAndOperate'));
                var option = {
                    tooltip: {
                        position: ['30%', '20%'],
                        trigger: 'item',
                        axisPointer: {
                            type: 'shadow'

                        },
                        formatter: function (param) {
                            return option.series[param['seriesIndex']]['tooltip'][param['dataIndex']];
                        }
                    },
                    legend: {
                        itemWidth: 10,
                        itemHeight: 10,
                        top: 7,
                        left: 50,
                        data: [{
                            name: '操作数',
                            icon: 'bar',
                            textStyle: {
                                color: '#333'

                            }
                        }, {
                            name: '报警数',
                            icon: 'bar',
                            textStyle: {
                                color: '#333'
                            }
                        }]

                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        bottom: '1%',
                        top: '14%',
                        height: '220px',
                        containLabel: true
                    },
                    calculable: false,
                    xAxis: [{
                        type: 'category',
                        boundaryGap: false,
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            formatter: function (value, index) {
                                var type = $("#dateType").val();
                                switch (type) {
                                    case "hour":
                                        return moment(value).format("HH:mm");
                                    case "day":
                                        return moment(value).format("MM-DD");
                                    case "week":
                                        return moment(value).format("MM-DD");
                                    case "month":
                                        return moment(value).format("YYYY-MM");
                                    default:
                                        return value;
                                }
                            },
                            interval: 0,
                            rotate: 35
                        },
                        data: []
                    }],
                    yAxis: [{
                        type: 'value',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                    }],
                    series: [
                        {
                            name: '报警数',
                            type: 'line',
                            itemStyle: {
                                label: {
                                    show: false
                                },
                                normal: {
                                    color: '#489ae5',
                                    areaStyle: {
                                        color: '#FF5B57'
                                    }
                                }
                            },
                            markArea: {
                                itemStyle: {
                                    normal: {
                                        color: '#6b6d75',
                                        borderColor: '#6b6d75',
                                        opacity: 0.2
                                    }
                                },
                                silent: true,
                                data: [
                                    [{
                                        xAxis: ''
                                    }, {
                                        xAxis: ''
                                    }]
                                ]
                            },
                            data: [],
                        }, {
                            name: '操作数',
                            type: 'line',
                            smooth: true,
                            itemStyle: {
                                normal: {
                                    color: '#f6a630',
                                    areaStyle: {
                                        color: '#5BA5E8'
                                    }
                                }
                            },
                            data: []
                        }

                    ]
                };
                var dateType = $("#dateType").val();
                if (option.xAxis[0].data.length == 0 && data != undefined && data[0] != undefined && data[0].xaxis != undefined) {
                    var dateArr = data[0].xaxis;
                    var lastDate = dateArr[dateArr.length - 1];
                    var nextDate;
                    switch (dateType) {
                        case "month":
                            lastDate = lastDate + '-01';
                            nextDate = OPAL.util.strToDate(lastDate).dateAdd('m', 1);
                            nextDate = OPAL.util.dateFormat(nextDate, 'yyyy-MM');
                            break;
                        case "week":
                            nextDate = OPAL.util.strToDate(lastDate).dateAdd('w', 1);
                            nextDate = OPAL.util.dateFormat(nextDate, 'yyyy-MM-dd');
                            break;
                        case "day":
                            nextDate = OPAL.util.strToDate(lastDate).dateAdd('d', 1);
                            nextDate = OPAL.util.dateFormat(nextDate, 'yyyy-MM-dd');
                            break;
                        case "hour":
                            nextDate = OPAL.util.strToDate(lastDate).dateAdd('h', 1);
                            nextDate = OPAL.util.dateFormat(nextDate, 'yyyy-MM-dd HH:mm:ss');
                            break;
                    }
                    //data[0].xaxis.push(nextDate);
                    option.xAxis[0].data = data[0].xaxis;
                }
                if (data != null || data != undefined) {
                    for (var i = 0; i < data.length; i++) {
                        option.series.push({
                            name: data[i]['name'],
                            type: 'line',
                            smooth: true,
                            itemStyle: {
                                normal: {
                                    color: data[i]['name'] == "操作数" ? '#f6a630' : '#489ae5',
                                    areaStyle: {
                                        color: data[i]['name'] == "操作数" ? '#f6a630' : '#489ae5'
                                    }
                                }
                            },
                            data: data[i]['counts'],
                            tooltip: data[i]['tip'],
                            markArea: {
                                silent: true,
                                itemStyle: {
                                    normal: {
                                        color: '#6b6d75',
                                        borderColor: '#6b6d75',
                                        opacity: 0.2
                                    }
                                },
                                data: [
                                    [{
                                        xAxis: ''
                                    }, {
                                        xAxis: ''
                                    }]
                                ]
                            },
                        });
                    }
                }
                option['dateType'] = dateType;
                operNumChartOption = option;
                if (dateType == 'hour') {
                    if (operNumChartOption != undefined && operNumChartOption.xAxis != undefined && operNumChartOption.xAxis[0] != undefined && operNumChartOption.xAxis[0]['data'] != undefined) {
                        for (var k = 0; k < operNumChartOption.xAxis[0]['data'].length; k++) {
                            operNumChartOption.xAxis[0]['data'][k] = OPAL.util.dateFormat(OPAL.util.strToDate(operNumChartOption.xAxis[0]['data'][k]), "yyyy-MM-dd HH:00");
                        }
                    }
                }
                operNumChart.setOption(operNumChartOption);
            },
            /**
             * 初始化报警数和操作数表格
             * @param data
             */
            initAlarmAndOperateTable: function (data) {
                var option = {
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'timeStr',
                        title: '时间',
                        align: 'center',
                        formatter: function (value, row, index) {
                            return value;
                        }
                    }, {
                        field: 'operationCount',
                        title: '操作数',
                        align: 'right'
                    }, {
                        field: 'count',
                        title: '报警数',
                        align: 'right'
                    }],
                    sidePagination: "client",
                    queryParamsType: "undefined",
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    sidePagination: "client",
                    cache: false,
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    striped: true
                };
                $('#tableAlarmAndOperate').bootstrapTable(option);
                $("#tableAlarmAndOperate").bootstrapTable("removeAll");
                if (data != undefined && data[0] != undefined && data[0].list != undefined) {
                    $("#tableAlarmAndOperate").bootstrapTable("load", data[0].list);
                } else {
                    $("#tableAlarmAndOperate").bootstrapTable("load", []);
                }
                $('#tableAlarmAndOperate').bootstrapTable('refreshOptions', option);
            },
            /**
             * 初始化Table数据
             * @param data
             */
            initTableDisplay: function (data) {
                var title = displayName == "单元" ? "生产单元" : displayName;
                var options = {
                    detailView: true, //父子表
                    striped: true, //是否显示行间隔色
                    pagination: false, //是否显示分页（*）
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    height: 350,
                    pageSize: 20, //每页的记录行数（*）
                    pageList: [20, 40, 60], //可供选择的每页的行数（*）
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'name',
                        title: title,
                        align: 'left',
                    }, {
                        field: 'bloodAlarmCounts',
                        title: '次数',
                        align: 'right',
                    }, {
                        field: 'timePercent',
                        title: '时间百分比(%)',
                        align: 'right',

                    }, {
                        field: 'counts',
                        title: '报警数(高频)',
                        align: 'right',
                    }
                    ],
                    onExpandRow: function (index, row, detail) {
                        page.logic.initSubDisplay(index, row, detail)
                    }
                };
                $('#floodTable').bootstrapTable(options);
                $('#floodTable').bootstrapTable('refreshOptions', options);
                if (data['mainTableEntityList'] == undefined) {
                    data['mainTableEntityList'] = [];
                }
                $("#floodTable").bootstrapTable("load", data['mainTableEntityList']);
            },
            /**
             * 初始二级子表
             * @param index
             * @param row
             * @param detail
             */
            initSubDisplay: function (index, row, detail) {
                var floodTableMain = detail.html('<table></table>').find('table');
                $(floodTableMain).bootstrapTable({
                    data: row['subList'],
                    clickToSelect: true,
                    detailView: true, //父子表
                    sidePagination: "client",
                    pagination: false,
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    pageSize: 20, //每页的记录行数（*）
                    pageList: [20, 40, 60], //可供选择的每页的行数（*）
                    onClickRow: function (row, element) {
                        var trs = $("#floodTable  tr[class='detail-view']  tr");
                        for (var i = 0; i < trs.length; i++) {
                            $(trs[i]).removeClass("selectedd");
                        }
                        element.addClass("selectedd");
                        row['startTime'] = row['startTimeStr'];
                        row['endTime'] = row['endTimeStr'];
                        page.logic.hightLightAlarmAndOperateData(row);
                        page.data.param = {
                            startTime: row['startTimeStr'],
                            endTime: row['endTimeStr'],
                            now: Math.random(),
                            prdtIds: [row['prdtId']],
                            unitIds: [row['unitId']],
                            pageSize: 20,
                            pageNumber: 1,
                            sortOrder: 'asc',
                        };
                        page.data.subParam.unitId = row['unitId'];
                        page.data.subParam.startTime = page.data.param.startTime;
                        page.data.subParam.endTime = page.data.param.endTime;
                        $('#tableCausal').bootstrapTable('refresh', {
                            "url": commonCasualMainUrl,
                            "pageNumber": 1
                        })
                    },
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'floodName',
                        title: '名称',
                        align: 'left',
                    }, {
                        field: 'startTimeStr',
                        title: '开始时间',
                        align: 'center',
                    }, {
                        field: 'endTimeStr',
                        title: '结束时间',
                        align: 'center',
                    }, {
                        field: 'datePeriod',
                        title: '时间期间(分钟)',
                        align: 'right',
                    }, {
                        field: 'alarmCounts',
                        title: '报警数',
                        align: 'right',
                    }, {
                        field: 'alarmRate',
                        title: '峰值报警率',
                        align: 'right',
                    }
                    ],
                    onExpandRow: function (index, row, detail) {
                        page.logic.initThirdDisplay(index, row, detail)
                    }
                });
            },
            /**
             * 高亮操作数和报警数图表和列表
             * @param item
             */
            hightLightAlarmAndOperateData: function (item) {
                var queryStartTime;
                var queryEndTime;
                var pattern = "";
                //如果选择类型不是小时,则需要减去10个小时(从服务器获取)
                var queryHourTemp = queryHour;
                if (operNumChartOption['dateType'] == "hour") {
                    queryHourTemp = 0;
                } else {
                    item["startTime"] = moment(item["startTime"]).subtract(queryHourTemp, 'hour').format("YYYY-MM-DD HH:mm:ss");
                    item["endTime"] = moment(item["endTime"]).subtract(queryHourTemp, 'hour').format("YYYY-MM-DD HH:mm:ss");
                }

                switch (operNumChartOption['dateType']) {
                    case "hour":
                        item["startTime"] = OPAL.util.dateFormat(OPAL.util.strToDate(item["startTime"]), "yyyy-MM-dd HH:00");
                        queryStartTime = OPAL.util.dateFormat(OPAL.util.strToDate(item["startTime"]), "yyyy-MM-dd HH:00");
                        queryEndTime = OPAL.util.dateFormat(OPAL.util.strToDate(item["endTime"]), "yyyy-MM-dd HH:00");
                        pattern = "yyyy-MM-dd HH:00";
                        break;
                    case "day":
                        item["startTime"] = OPAL.util.dateFormat(OPAL.util.strToDate(item["startTime"]), "yyyy-MM-dd");
                        queryStartTime = OPAL.util.dateFormat(OPAL.util.strToDate(item["startTime"]), "yyyy-MM-dd");
                        queryEndTime = OPAL.util.dateFormat(OPAL.util.strToDate(item["endTime"]), "yyyy-MM-dd");
                        pattern = "yyyy-MM-dd";
                        break;
                    case "week":
                        queryStartTime = item["startTime"];
                        queryEndTime = item["endTime"];
                        item["startTime"] = OPAL.util.dateFormat(OPAL.util.strToDate(item["startTime"]), "yyyy-MM-dd");
                        pattern = "yyyy-MM-dd HH:mm:ss";
                        break;
                    case "month":
                        item["startTime"] = OPAL.util.dateFormat(OPAL.util.strToDate(item["startTime"]), "yyyy-MM");
                        queryStartTime = moment(item["startTime"]).format("YYYY-MM");
                        queryEndTime = moment(item["endTime"]).format("YYYY-MM");
                        pattern = "yyyy-MM";
                        break;
                }
                //高亮表格;
                var option = $('#tableAlarmAndOperate').bootstrapTable('getOptions');
                if (option != undefined && option['data'] != undefined) {
                    //如果是周的话需要进行特殊计算
                    var min = 0;
                    if (operNumChartOption['dateType'] == "week") {
                        for (var i = 0; i < option.data.length; i++) {
                            if (i == 0) {
                                min = moment(queryStartTime).valueOf() - moment(OPAL.util.dateFormat(OPAL.util.strToDate(option.data[i]['timeStr']), pattern)).valueOf();
                                hightLightRowIndex = 0;
                            }
                            if (option.data[i]['timeStr'] == undefined) continue;
                            var temp = (moment(queryStartTime).valueOf() - moment(OPAL.util.dateFormat(OPAL.util.strToDate(option.data[i]['timeStr']), pattern)).valueOf());
                            if (temp >= 0 && temp <= min) {
                                min = (moment(queryStartTime).valueOf() - moment(OPAL.util.dateFormat(OPAL.util.strToDate(option.data[i]['timeStr']), pattern)).valueOf());
                                hightLightRowIndex = i;
                            }
                        }
                    } else {
                        for (var i = 0; i < option.data.length; i++) {
                            if (option.data[i]['timeStr'] == undefined) continue;
                            if (moment(OPAL.util.dateFormat(OPAL.util.strToDate(option.data[i]['timeStr']), pattern)).valueOf() == moment(queryEndTime).valueOf()) {
                                hightLightRowIndex = i;
                                break;
                            }
                        }
                    }
                }
                if (hightLightRowIndex != -1) {
                    option.pageNumber = Math.ceil((hightLightRowIndex + 1) / option.pageSize);
                    option.rowStyle = function (row, index) {
                        if (index == hightLightRowIndex) {
                            return {
                                classes: "add-class-bg"
                            }
                        } else {
                            return {
                                classes: ''
                            }
                        }
                    }
                }
                $("#tableAlarmAndOperate").bootstrapTable('refreshOptions', option);
                hightLightRowIndex = -1;

                //高亮图表
                var startTimeStr;
                var endTimeStr;

                if (operNumChartOption != undefined && operNumChartOption.xAxis != undefined && operNumChartOption.xAxis[0] != undefined && operNumChartOption.xAxis[0]['data'] != undefined) {
                    for (var k = 0; k < operNumChartOption.xAxis[0]['data'].length; k++) {
                        if (moment(item["endTime"]).valueOf() >= moment(operNumChartOption.xAxis[0]['data'][k]).valueOf() && operNumChartOption.xAxis[0]['data'][k + 1] != undefined && moment(item["endTime"]).valueOf() < moment(operNumChartOption.xAxis[0]['data'][k + 1]).valueOf()) {
                            startTimeStr = operNumChartOption.xAxis[0]['data'][k];
                            endTimeStr = operNumChartOption.xAxis[0]['data'][k + 1];
                            break;
                        }
                    }
                }
                //处理特殊情况,如果是最后一个数据,则都为undefined
                if (startTimeStr == undefined && endTimeStr == undefined) {
                    endTimeStr = operNumChartOption.xAxis[0]['data'][operNumChartOption.xAxis[0]['data'].length - 1];
                    startTimeStr = operNumChartOption.xAxis[0]['data'][operNumChartOption.xAxis[0]['data'].length - 2];
                }
                if (operNumChartOption != undefined && operNumChartOption['series'] != undefined) {
                    for (var i = 0; i < operNumChartOption.series.length; i++) {
                        if (operNumChartOption.series[i] != undefined && operNumChartOption.series[i]['markArea'] != undefined && operNumChartOption.series[i]['markArea']['data'] != undefined && operNumChartOption.series[i]['markArea']['data'][0].length == 2) {
                            if (startTimeStr != undefined && endTimeStr != undefined) {
                                operNumChartOption.series[i]['markArea'].data[0][0]['xAxis'] = startTimeStr;
                                operNumChartOption.series[i]['markArea'].data[0][1]['xAxis'] = endTimeStr;
                            }
                        }
                    }
                    operNumChart.setOption(operNumChartOption);
                }
            },
            /**
             * 初始化三级子表
             * @param index
             * @param row
             * @param detail
             */
            initThirdDisplay: function (index, row, detail) {
                var floodTableMain = detail.html('<table></table>').find('table');
                $(floodTableMain).bootstrapTable({
                    data: row['thirdTable'],
                    clickToSelect: true,
                    sidePagination: "client",
                    pagination: false,
                    pageNumber: 1,
                    pageSize: 20,
                    pageList: [20, 40, 60],
                    detailView: false,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '3%'
                    }, {
                        field: 'startTimeStr',
                        title: '时间',
                        align: 'center',
                        width: '120px'
                    }, {
                        field: 'alarmCounts',
                        title: '报警数',
                        align: 'right',
                        width: '120px'
                    }
                    ],
                });
            },
            /**
             * 查询父表格参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) {
                var param = {
                    isCausalAlarmAnalysis: false,
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    now: Math.random()
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 查询子表格参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            subQueryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    alarmPointId: alarmPointId,
                    alarmFlagId: alarmFlagId,
                    now: Math.random()
                };
                return $.extend(page.data.subParam, param);
            },
            queryOperateParam: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.mostParam, param);
            },
            /**
             * 报警统计表格初始化
             */
            initOpetateTable: function () {
                OPAL.ui.initBootstrapTable("MostAlarmOperateTable", {
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '60px'
                    }, {
                        field: 'unitName',
                        title: '装置',
                        align: 'left',
                        width: '140px'
                    }, {
                        field: 'prdtCellName',
                        title: '生产单元',
                        align: 'left',
                        width: '140px'
                    }, {
                        field: 'tag',
                        title: '位号',
                        align: 'left',
                        width: '140px'
                    }, {
                        field: 'location',
                        title: '位置描述',
                        width: '250px',
                        align: 'left'
                    }, {
                        field: 'alarmFlag',
                        title: '报警等级',
                        align: 'center',
                        width: '80px'

                    }, {
                        field: 'monitorTypeStr',
                        title: '专业',
                        align: 'center',
                        width: '80px'

                    }, {
                        field: 'alarmCount',
                        title: '数量',
                        align: 'right',
                        width: '60px'
                    }, {
                        field: 'alarmTime',
                        title: '时间点',
                        width: '150px',
                        align: 'left'
                    }],
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    }
                }, page.logic.queryOperateParam);
            },
        }
    };
    page.init();
    window.page = page;
});