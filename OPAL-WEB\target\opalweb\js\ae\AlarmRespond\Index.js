 var searchUrl = OPAL.API.aeUrl + '/alarmRespond';
 var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
 var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
 var alarmFlagListUrl = OPAL.API.commUrl + '/getAlarmFlagList';
 var alarmPriorityListUrl = OPAL.API.commUrl + '/getAlarmPriorityList';
 var workTeamUrl = OPAL.API.commUrl + "/getWorkTeam";
 var exportAlarmRespondUrl = OPAL.API.aeUrl + '/alarmRespond/exportAlarmRespond';
 var isLoading = true;
 $(function() {
     var page = {
         /**
          * 初始化
          */
         init: function() {
             this.bindUi();
             //扩展日期插件
             OPAL.util.extendDate();
             // 初始化 时间设置
             page.logic.initTime();
             //初始化查询装置树
             page.logic.initUnitTree();
             //初始化表格
             page.logic.initTable();
             //初始化报警标识
             page.logic.initAlarmFlagList();
             //初始化优先级
             page.logic.initAlarmPriorityList();
             //初始化是否及时确认
             page.logic.initIsTimeResponse();
             $('#workTeamIds').html("");
             $("#workTeamIds").prop('disabled', true);

             if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                 var cookieValue = OPAL.util.getCookieByPageCode("AlarmRespond");
                 if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                     $('#unitIds').combotree('setValue', cookieValue);
                 }
             }
             //默认查询数据
             setTimeout(function () {
                 if ($("#alarmFlagId").val()!=null&&$("#priority").val()!=null
                     &&$("#isTimeResponse").val()!=null&&$("#responseDuration").val()!=null) {
                     page.logic.search();
                 }
             }, 500);
         },
         bindUi: function() {
             //查询
             $('#search').click(function() {
                 isLoading = false;
                 page.logic.search();
             })
             $('#alarmRespondExport').click(function() {
                 //1.提示是否继续
                 layer.confirm('数据量较大，导出时间可能较长，是否继续？', {
                     btn: ['是', '否'],
                     title: '提示',
                 }, function (index) {
                     layer.close(index);
                     page.logic.exportExcel();
                 }, function (index) {
                     layer.close(index);
                 });

             });
             $("#startTime").unbind("change");
             $("#endTime").unbind("change");
             $("#startTime").change(function() {
                 page.logic.initWorkTeam();
             })
             $("#endTime").change(function() {
                 page.logic.initWorkTeam();
             })
         },
         data: {
             // 设置查询参数
             param: {}
         },
         logic: {
             initTable: function() {
                 OPAL.ui.initBootstrapTable2("table", {
                     columns: [{
                         title: "序号",
                         field: 'index',
                         formatter: function(value, row, index) {
                             var data = page.data.param;
                             var pageNumber = data.pageNumber;
                             var pageSize = data.pageSize;
                             return index + 1 + (pageNumber - 1) * pageSize;
                         },
                         rowspan: 1,
                         align: 'center',
                         width: '80px'
                     }, {
                         title: "报警时间",
                         field: 'alarmTime',
                         rowspan: 1,
                         align: 'center',
                         width: '150px'
                     }, {
                         title: "确认时间",
                         field: 'responseTime',
                         rowspan: 1,
                         align: 'center',
                         width: '150px'
                     }, {
                         title: "装置",
                         field: 'unitName',
                         rowspan: 1,
                         align: 'left',
                         width: '120px'
                     }, {
                         title: "生产单元",
                         field: 'prdtCellName',
                         rowspan: 1,
                         align: 'left',
                         width: '120px'
                     }, {
                         title: "班组",
                         field: 'team',
                         rowspan: 1,
                         align: 'center',
                         width: '100px'
                     }, {
                         title: "位号",
                         field: 'tag',
                         rowspan: 1,
                         align: 'left',
                         width: '100px'
                     }, {
                         title: "参数名称",
                         field: 'location',
                         rowspan: 1,
                         align: 'left',
                         width: '150px'
                     }, {
                         title: "报警等级",
                         field: 'alarmFlagName',
                         rowspan: 1,
                         align: 'center',
                         width: '100px'
                     }, {
                         title: "优先级",
                         field: 'priorityName',
                         rowspan: 1,
                         align: 'center',
                         width: '60px'
                     }, {
                         title: "实际确认时长(秒)",
                         field: 'actualResponseDuration',
                         rowspan: 1,
                         align: 'right',
                         width: '120px'
                     }],
                     responseHandler: function(res) {
                         var item = {
                             "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                             "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                             "total": $.ET.getPageInfo(res)[0]["total"],
                             "rows": $.ET.toObjectArr(res)
                         };
                         var rate = '0.00%';
                         if ($.ET.toObjectArr(res).length > 0) {
                             rate = $.ET.toObjectArr(res)[0]['responseTimeRate'];
                         }
                         //确认及时率
                         $("#rate").html(rate);
                         return item;
                     },
                     rowStyle: function(row, index) {
                         var style = "";
                         if (row.actualResponseDuration > row.prescribedResponseDuration) {
                             style = 'redRow';
                         }
                         if (row.responseTime == '') {
                             style = 'redRow';
                         }
                         return {
                             classes: style
                         }
                     }
                 }, page.logic.queryParams,"search")
             },
             /**
              * 查询参数
              * @param params
              * @returns {{pageSize: *, pageNumber: *}}
              */
             queryParams: function(p) {
                 var param = {
                     pageSize: p.pageSize,
                     pageNumber: p.pageNumber,
                     sortOrder: p.sortOrder
                 };
                 return $.extend(page.data.param, param);
             },
             /**
              * 设置日期插件
              */
             initTime: function() {
                 OPAL.ui.initDateTimePeriodPicker({
                     format: 'yyyy-MM-dd HH:mm:ss',
                     type: 'datetime',
                 }, function() {
                     page.logic.initWorkTeam();
                 });
             },
             /**
              * 初始化装置树
              */
             initUnitTree: function() {
                 OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                     onChange: function(nodes) {
                         var nodeIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                         if (nodeIds.length == 1) {
                             $("#prdtCellIds").combo('enable');
                             $("#prdtCellIds").combotree('setValues', []);
                             page.logic.searchUnitPrdt(nodeIds[0]);
                             $("#workTeamIds").prop('disabled', false);
                             page.logic.initWorkTeam();
                             $('.textbox,.combo').css('background-color','');
                         } else {
                             $("#prdtCellIds").combotree('setValues', []);
                             $("#prdtCellIds").combo('disable');
                             $('#workTeamIds').html("");
                             $("#workTeamIds").prop('disabled', true);
                             $('.textbox-disabled').css('background-color','rgb(235, 235, 228)');
                         }
                     }
                 }, false, function() {
                     $("#searchPrdt").combotree("checkAllNodes");
                 });
             },
             /**
              * 搜索
              */
             search: function() {
                 page.logic.setData();
                 if (!OPAL.util.checkDateIsValid()) {
                     return false;
                 }
                 $("#search").attr('disabled', true);
                 $("#table").bootstrapTable('refresh', {
                     "url": searchUrl,
                     "pageNumber": 1
                 });
             },

             setData: function() {
                 page.data.param = OPAL.form.getData("searchForm");
             },
             /**
              * 初始化是否及时确认
              */
             initIsTimeResponse: function() {
                 OPAL.ui.getComboboxByData("isTimeResponse",[{'key':'-1','value':'全部'},{'key':'1','value':'是'},{'key':'2','value':'否'}], {
                     selectFirstRecord: true,
                 }, function(){
                     page.logic.initTimeResponseRate($("#isTimeResponse").val());
                 },function(value){
                     page.logic.initTimeResponseRate(value);
                 });
             },
             /**
              * 初始化确认时长
              */
             initTimeResponseRate: function(value) {
                 var dataList=new Array();
                 if(value==-1){
                     dataList.push({'key':'7','value':'已确认'});
                     dataList.push({'key':'-1','value':'全部'});
                     dataList.push({'key':'1','value':'<=30s'});
                     dataList.push({'key':'2','value':'30s～2min'});
                     dataList.push({'key':'3','value':'2min～5min'});
                     dataList.push({'key':'4','value':'＞5min'});
                     dataList.push({'key':'5','value':'未确认'});
                     dataList.push({'key':'6','value':'45s～2min'});
                 }
                 else if(value==1){
                     dataList.push({'key':'1','value':'<=30s'});
                 }
                 else if(value==2){
                     dataList.push({'key':'-1','value':'全部'});
                     dataList.push({'key':'2','value':'30s～2min'});
                     dataList.push({'key':'3','value':'2min～5min'});
                     dataList.push({'key':'4','value':'＞5min'});
                     dataList.push({'key':'5','value':'未确认'});
                     dataList.push({'key':'6','value':'45s～2min'});
                 }
                 OPAL.ui.getComboboxByData("responseDuration", dataList, {
                     selectFirstRecord: true
                 }, null);
             },
             /**
              * 初始化查询 报警标识
              */
             initAlarmFlagList: function() {
                 OPAL.ui.getCombobox("alarmFlagId", alarmFlagListUrl, {
                     selectFirstRecord: true,
                     data: {
                         'isAll': true
                     }
                 }, null);
             },
             /**
              * 初始化查询 优先级
              */
             initAlarmPriorityList: function() {
                 OPAL.ui.getCombobox("priority", alarmPriorityListUrl, {
                     selectValue: -1,
                     data: {
                         'isAll': true
                     }
                 }, null);
             },
             /**
              * 查询装置下的生产单元
              */
             searchUnitPrdt: function(unitId) {
                 OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                     keyField: "prdtCellId",
                     valueField: "sname",
                     data: {
                         "unitId": unitId,
                         'isAll': true
                     }
                 }, true, function() {
                     var treeView = $("#prdtCellIds").combotree('tree');
                     var nd = treeView.tree('find', -1);
                     if (nd != null) {
                         treeView.tree('update', {
                             target: nd.target,
                             text: '全选'
                         });
                     }
                     $("#prdtCellIds").combotree("checkAllNodes");
                 });
             },
             /**
              * 初始化班组选择
              */
             initWorkTeam: function() {
                 var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                 if (unitIds.length != 1) return;
                 if ($("#startTime").val() == '' || $("#endTime").val() == '') {
                     $('#workTeamIds').html("");
                     $("#workTeamIds").prop('disabled', true);
                     return;
                 } else {
                     $("#workTeamIds").prop('disabled', false);
                 }
                 OPAL.ui.getCombobox("workTeamIds", workTeamUrl, {
                     keyField: "workTeamId",
                     valueField: "workTeamSName",
                     selectFirstRecord: true,
                     mapManyValues: true, //是否一条记录匹配多个隐藏值
                     mapManyDataFieldName: 'workTeamIdList',
                     data: {
                         "startTime": $("#startTime").val(),
                         "endTime": $("#endTime").val(),
                         "unitId": unitIds[0],
                     }
                 }, null);

             },
             /**
              * 导出
              */
             exportExcel: function() {
                 var data = {};
                 var titleArray = new Array(); // 导出列名
                 var tableTitle = $('#table').bootstrapTable('getOptions').columns[0];
                 $.each(tableTitle, function(i, el) {
                     if (i >= 1) {
                         titleArray.push({
                             'key': el.field,
                             'value': el.title
                         })
                     }
                 })
                 data.titles = JSON.stringify(titleArray);
                 data.pageSize = $('#table').bootstrapTable('getOptions').pageSize;
                 data.pageNumber = $('#table').bootstrapTable('getOptions').pageNumber;
                 page.logic.setData();
                 $.extend(data, page.data.param);
                 $('#formExPort').attr('action', exportAlarmRespondUrl);
                 $('#titles').val(data.titles);
                 $('#pageSize').val(data.pageSize);
                 $('#pageNumber').val(data.pageNumber);
                 $('#unitIds1').val(data.unitIds);
                 $('#prdtCellIds1').val(data.prdtCellIds);
                 $('#tag1').val(data.tag);
                 $('#flag1').val(data.alarmFlagId);
                 $('#priorityExport').val(data.priority);
                 $("#responseInTimeExport").val(data.isTimeResponse);
                 $("#startDate").val(data.startTime);
                 $("#endDate").val(data.endTime);
                 $("#team").val(data.workTeamIds);
                 $('#formExPort').submit();
              }
         }
     };
     page.init();
     window.page = page;
 });