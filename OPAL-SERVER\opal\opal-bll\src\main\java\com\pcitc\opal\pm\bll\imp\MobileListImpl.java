package com.pcitc.opal.pm.bll.imp;

import com.pcitc.imp.common.exception.BusiException;
import com.pcitc.opal.common.CommonEnum.PageModelEnum;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.MobileListService;
import com.pcitc.opal.pm.bll.UnitService;
import com.pcitc.opal.pm.bll.WorkshopService;
import com.pcitc.opal.pm.bll.entity.DBFactoryEntity;
import com.pcitc.opal.pm.bll.entity.DBWorkshopEntity;
import com.pcitc.opal.pm.bll.entity.MobileListDTOEntity;
import com.pcitc.opal.pm.bll.entity.MobileListEntity;
import com.pcitc.opal.pm.dao.AlarmMobileConfRepository;
import com.pcitc.opal.pm.dao.AlarmMsgConfigRepository;
import com.pcitc.opal.pm.dao.MobileListRepository;
import com.pcitc.opal.pm.dao.imp.MobileListDTO;
import com.pcitc.opal.pm.pojo.AlarmMobileConf;
import com.pcitc.opal.pm.pojo.MobileList;
import com.pcitc.opal.pm.pojo.SendMsgAlarmConf;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/*
 * 电话本业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_mobileListImpl
 * 作       者：guoganxin
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：电话本业务逻辑层实现类
 */
@Service
@Component
public class MobileListImpl implements MobileListService {

    @Autowired
    private BasicDataService basicDataService;
    @Autowired
    private WorkshopService workshopService;
    @Autowired
    MobileListRepository mobileListRepository;
    @Autowired
    AlarmMobileConfRepository alarmMobileConfRepository;
    @Autowired
    AlarmMsgConfigRepository alarmMsgConfigRepository;
    @Autowired
    private UnitService unitService;

    //获取装置数据
    List<UnitEntity> unitList = null;
    List<DBFactoryEntity> factoryList = null;
    List<DBWorkshopEntity> workshopList = null;

    /**
     * 新增电话本
     * <p>
     *  * <AUTHOR> 2017-10-11
     *
     * @param mobileListEntity 电话本实体
     * @return 电话本实体
     * @throws Exception 
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addMobileList(MobileListEntity mobileListEntity) throws Exception {
        CommonResult commonResult = new CommonResult();
        List<String> delList = mobileListRepository.getDelList();
        String mobile = delList.stream().filter(u ->
                        u.equals(mobileListEntity.getMobile()))
                .findFirst().orElse(null);

        if (mobile != null) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage("手机号已存在！");
            throw new Exception(commonResult.getMessage());
        }
        MobileList mobileList = ObjectConverter.entityConverter(mobileListEntity, MobileList.class);
        // 赋值 创建人、创建名称、创建时间
        CommonUtil.returnValue(mobileList, PageModelEnum.NewAdd.getIndex());
        commonResult = mobileListRepository.addMobileList(mobileList);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false) {
            throw new Exception(commonResult.getMessage());
        }
        return commonResult;
    }

    /**
     * 删除电话本维护数据
     * <p>
     *  * <AUTHOR> 2017-10-11
     *
     * @param mobileListIds 电话本维护主键Id集合
     * @return 电话本主键id集合
     * @throws Exception 
     */
    @Override
    @CacheEvict(value = "AlarmAnalysis", allEntries = true) // 移除AlarmAnalysis下的所有缓存数据
    public CommonResult deleteMobileList(Long[] mobileListIds) throws Exception {
        List<AlarmMobileConf> alarmMobileConfList = alarmMobileConfRepository.getAlarmMobileConfMobileId(mobileListIds);
        CommonResult commonResult = new CommonResult();
        if (alarmMobileConfList.size() > 0) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage("存在相关联的数据，删除失败！");
            return commonResult;
        }
        for (Long mobileListId : mobileListIds) {
            List<SendMsgAlarmConf> alarmMsgConfList = alarmMsgConfigRepository.getSendMsgAlarmConfMobileId(mobileListId);
            if (alarmMsgConfList.size() > 0) {
                commonResult.setIsSuccess(false);
                commonResult.setMessage("存在相关联的数据，删除失败！");
                return commonResult;
            }
        }

        return mobileListRepository.deleteMobileList(mobileListIds);
    }

    /**
     * 修改电话本
     * <p>
     *  * <AUTHOR> 2017-10-11
     *
     * @param mobileListEntity 电话本实体类
     * @return 电话本实体类
     * @throws Exception 
     */
    @Override
    @CacheEvict(value = "AlarmAnalysis", allEntries = true) // 移除AlarmAnalysis下的所有缓存数据
    // @CacheUtil.CacheRemove(value = "AlarmAnalysis" , key= "'AlarmDetail_'+#mobileListEntity.mobileListId+'_*'" )
    public CommonResult updateMobileList(MobileListEntity mobileListEntity) throws Exception {

        // 实体转换为持久层实体// 实体转换持久层实体
        MobileList mobileListPO = ObjectConverter.entityConverter(mobileListEntity, MobileList.class);
        List<Long> ids = new ArrayList<>();
        ids.add(mobileListPO.getMobileListId());
        MobileList mobileList = mobileListRepository.getMobileListByIds(ids).get(0);
        mobileListPO.setCrtDate(mobileList.getCrtDate());
        mobileListPO.setCrtUserId(mobileList.getCrtUserId());
        mobileListPO.setCrtUserName(mobileList.getCrtUserName());
        // 实体转换为持久层实体
//        CommonUtil.objectExchange(mobileListEntity, mobileListPO);
        // 赋值 修改人、修改名称、修改时间
        CommonUtil.returnValue(mobileListPO, PageModelEnum.Edit.getIndex());
        // 调用DAL更新方法
        CommonResult commonResult = mobileListRepository.updateMobileList(mobileListPO);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 通过电话本ID获取单条数据
     * <p>
     *  * <AUTHOR> 2017-10-11
     *
     * @param mobileListId 电话本主键id
     * @throws Exception
     * @return 电话本实体类
     */
    @Override
    public MobileListEntity getSingleMobileList(Long mobileListId) throws Exception {
        List<Long> al = new ArrayList<>();
        al.add(mobileListId);
        List<MobileList> mobileList = mobileListRepository.getMobileListByIds(al);
        if (mobileList.size() > 0) {
            MobileListEntity ape = ObjectConverter.entityConverter(mobileList.get(0), MobileListEntity.class);
            return ape;
        }
        return null;
    }

    /**
     * 获取分页数据
     * <p>
     *  * <AUTHOR> 2017-10-11
     *
     * @param page 翻页实现类
     * @return 翻页对象
     * @throws Exception 
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<MobileListDTOEntity> getMobileList(Long factoryId, Long workshopId, String unitCode, String name, String mobile, Pagination page) throws Exception {
        PaginationBean<MobileListDTO> mobileListPage = mobileListRepository.getMobileListPage(factoryId, workshopId, unitCode, name, mobile, page);
        PaginationBean<MobileListDTOEntity> returnmobileList = new PaginationBean<MobileListDTOEntity>(page, (mobileListPage.getTotal()));
        returnmobileList.setPageList(ObjectConverter.listConverter(mobileListPage.getPageList(), MobileListDTOEntity.class));

        return returnmobileList;
    }

    @Override
    public List<MobileListDTOEntity> getMobileListExport(Long factoryId, Long workshopId, String unitCode, String name, String mobile) {
        List<MobileListDTO> mobileList = mobileListRepository.getMobileListExport(factoryId, workshopId, unitCode, name, mobile);
        List<MobileListDTOEntity> mobileEntityList = new ArrayList<>();
        mobileList.forEach(m -> {
            try {
                MobileListDTOEntity mobileListDTOEntity = ObjectConverter.entityConverter(m, MobileListDTOEntity.class);
                mobileEntityList.add(mobileListDTOEntity);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return mobileEntityList;
    }

    /**
     * 报警点维护数据导入
     *
     * @param inputStream Excel文件流
     * @return 导入成功信息
     * <AUTHOR> 2017-11-16
     */
    @SuppressWarnings("unchecked")
    public String importMobileList(InputStream inputStream) throws Exception {
        List<List<String[]>> allSheetContent = ExcelHelper.importAllSheetExcel(inputStream, 0, true);
        if (allSheetContent.size() == 0) {
            return "没有可导入的数据！";
        } else if (allSheetContent.size() == 1) {
            if (allSheetContent.get(0).size() < 2)
                return "没有可导入的数据！";
        } else if (allSheetContent.get(0).size() < 2 && allSheetContent.get(1).size() < 2)
            return "没有可导入的数据！";
        int sheet1Num = sheet1Import(allSheetContent.get(0));
        //sheet2 内容导入
        return String.format("sheet-1共导入%s条数据，导入成功！", sheet1Num);
    }

    /**
     * 导入sheet1数据
     *
     * @param excelContent excel内容
     * @return void
     * @throws BusiException 
     * <AUTHOR> 2017-10-11
     */
    @Transactional
    public int sheet1Import(List<String[]> excelContent) throws Exception {

        //读取excel文件
        List<MobileListEntity> apEntityList = new ArrayList<>();
        //校验字段
        String message = sheet1Verify(excelContent, apEntityList);
        if (!message.trim().isEmpty()) {
            throw new Exception(message);
        } else if (apEntityList.size() == 0) {
            return 0;
        } else {
            // 实体转换为持久层实体
            List<MobileList> mobileListList = ObjectConverter.listConverter(apEntityList, MobileList.class);

            for (MobileList mobileList : mobileListList) {

                // 赋值 创建人、创建名称、创建时间
                CommonUtil.returnValue(mobileList, PageModelEnum.NewAdd.getIndex());

                CommonResult commonResult = mobileListRepository.addMobileList(mobileList);
                // 如果失败，直接throw
                if (commonResult.getIsSuccess() == false)
                    throw new Exception(commonResult.getMessage());
            }
            return mobileListList.size();
        }
    }


    /**
     * 抽取sheet1数据
     *
     * @param excelContent excel导入内容
     * @param apEntityList 报警点实体集合
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-16
     */
    @SuppressWarnings("unchecked")
    private String sheet1Verify(List<String[]> excelContent, List<MobileListEntity> apEntityList) throws Exception {
        //没有可导入的数据！
        if (excelContent.size() < 2) {
            return "";
        }
        //region 校验标题
        String titles = "工厂、车间、装置、姓名、手机号";
        String[] titlesArr = titles.split("、");
        for (int i = 0; i < titlesArr.length; i++) {
            if (!titlesArr[i].equals(excelContent.get(0)[i])) {
                return "sheet-1 列" + titlesArr[i] + "列名不合法！";
            }
        }
        //获取装置数据
//        unitList = unitRepository.getUnit(null);
        unitList = basicDataService.getUnitList(false);
        if (this.unitList == null || this.unitList.isEmpty()) {
            throw new Exception("装置表没有数据");
        }
        factoryList = workshopService.getFactoryList(true);
        ;
//        //是否虚表
//        List<DictionaryEntity> vrfList = new ArrayList<>();
//        for (CommonEnum.VirtualRealityFlagEnum vrfEnum : CommonEnum.VirtualRealityFlagEnum.values()) {
//            vrfList.add(new DictionaryEntity(vrfEnum.getIndex(), vrfEnum.getName()));
//        }
//        //是否启用
//        List<DictionaryEntity> yonList = new ArrayList<>();
//        for (CommonEnum.InUseEnum inUseEnum : CommonEnum.InUseEnum.values()) {
//            yonList.add(new DictionaryEntity(inUseEnum.getIndex(), inUseEnum.getName()));
//        }
        //获取已存在手机号
        List<String> delList = mobileListRepository.getDelList();
        List<String> exportDelList = new ArrayList<>();
        for (int i = 1; i < excelContent.size(); i++) {
            MobileListEntity apEntity = new MobileListEntity();
            String[] itemContent = excelContent.get(i);
            for (int j = 0; j < itemContent.length; j++) {
                itemContent[j] = itemContent[j].replace("　", "").trim();
            }
            //工厂
            if (!itemContent[0].isEmpty()) {
                DBFactoryEntity factory = this.factoryList.stream().filter(u -> u.getSname()
                                .equals(itemContent[0]))
                        .findFirst().orElse(null);

                if (factory == null) {
                    return "sheet-1 行" + (i + 1) + "工厂不存在！";
                }
                apEntity.setFactoryId(factory.getFactoryId());
                //车间
                if (!itemContent[1].isEmpty()) {
                    workshopList = unitService.getWorkshopListByFactoryId(factory.getFactoryId(), true);
                    DBWorkshopEntity workshop = this.workshopList.stream().filter(u -> u.getSname()
                                    .equals(itemContent[1]))
                            .findFirst().orElse(null);
                    if (workshop == null) {
                        return "sheet-1 行" + (i + 1) + "工厂下的车间不存在！";
                    }
                    apEntity.setWorkshopId(workshop.getWorkshopId().intValue());
                }
            } else {
                return "sheet-1 行" + (i + 1) + "工厂不能为空！";
            }
            //region 检查数据必须录入和数据格式
            //region 装置
            if (!itemContent[2].isEmpty()) {
                UnitEntity unit = this.unitList.stream().filter(u -> u.getSname()
                                .equals(itemContent[2]))
                        .findFirst().orElse(null);

                if (unit == null) {
                    return "sheet-1 行" + (i + 1) + "装置不存在！";
                }
                apEntity.setUnitCode(unit.getStdCode());
            }
            //endregion

            //region 姓名
            if (itemContent[3].isEmpty()) {
                return "sheet-1 行" + (i + 1) + "姓名不能为空！";
            }
            if (itemContent[3].getBytes().length > 200) {
                return "sheet-1 行" + (i + 1) + "该电话本姓名长度超限！";
            }
            apEntity.setName(itemContent[3]);
            //region 手机号
            if (!itemContent[4].isEmpty()) {
                String del = delList.stream().filter(u ->
                                u.equals(itemContent[4]))
                        .findFirst().orElse(null);
                if (del != null) {
                    return "sheet-1 行" + (i + 1) + "手机号已存在！";
                }
                String exprotDel = exportDelList.stream().filter(u ->
                                u.equals(itemContent[4]))
                        .findFirst().orElse(null);
                if (exprotDel != null) {
                    return "sheet-1 行" + (i + 1) + "导入手机号重复！";
                }
                if (itemContent[4].length() != 11) {
                    return "sheet-1 行" + (i + 1) + "该电话本手机号不合法！";
                }
                for (int k = itemContent[4].length(); -k >= 0; ) {
                    int chr = itemContent[4].charAt(k);
                    if (chr < 48 || chr > 57) {
                        return "sheet-1 行" + (i + 1) + "该电话本手机号不合法！";
                    }
                }
            } else {
                return "sheet-1 行" + (i + 1) + "手机号不能为空！";
            }
            exportDelList.add(itemContent[4]);
            apEntity.setMobile(itemContent[4]);
            //endregion
            apEntityList.add(apEntity);
        }

        return "";
    }

}
