var pmUrl = OPAL.API.pmUrl;
var dcsCodeUrl = OPAL.API.commUrl + "/getDcsCodeList"; //DCS位号下拉框
var prdtCellList = OPAL.API.commUrl + '/getPrdtCellByDcs'; // 生产单元下拉框
var searchUrl = pmUrl + '/UnMatchAlarmPoint/getUnMatchAlarmPoints'; //查询
var getPrdtCellInPrdtCellCompUrl = OPAL.API.pmUrl + "/alarmPrdtCellComp/getPrdtCellInAlarmPrdtCellComp";
var exportUrl = pmUrl + '/UnMatchAlarmPoint/exportUnMatchAlarmPoints';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
$(function () {
    var page = {
        init: function () {
            this.bindUi();
            page.logic.initTable("tbEventType");
            //默认查询数据
            setTimeout(function () {
                if ($("#dcsCode").val()!=null) {
                    page.logic.search();
                }
            }, 500);
            //Dcs位号
            page.logic.initDcsCode();
            //初始化查询装置树
            page.logic.initUnitTree();

        },
        bindUi: function () {
            $('#export').click(function () {
                page.logic.exportExcel("tbEventType", exportUrl);
            });

            $('#btnQuery').click(function () {
                page.logic.search();
            });
        },
        data: {
            param: {}
        },
        logic: {
            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            search: function() {
                page.data.param = OPAL.form.getData('searchForm');
                $("#tbEventType").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 初始化事件类型列表
             *
             * @param ctrlID 控件ID
             * <AUTHOR>
             */
            initTable: function (ctrlID) {
                OPAL.ui.initBootstrapTable(ctrlID, {
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            var tableOption = $('#tbEventType').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            return (index + 1) + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "写入时间",
                        field: 'writeDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "DCS名称",
                        field: 'dcsName',
                        align: 'left',
                        width: '140px'
                    }, {
                        title: "装置",
                        field: 'unitName',
                        align: 'left',
                        rowspan: 1,
                        width: '100px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    },  {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '100px',
                        formatter: page.logic.onActionRenderer
                    }],
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    }
                }, page.logic.queryParams)
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor" class="btn-config"  href="javascript:window.page.logic.setAlarmPoint(\'' + rowData.unitCode + '\',\'' + rowData.prdtCellId + '\',\'' + rowData.tag + '\',\'' + rowData.opcCodeId + '\',\'' + rowData.dcsCode + '\')">配置</a>'
                ]
            },
            /**
             * 初始化DcsCode
             */
            initDcsCode: function () {
                OPAL.ui.getCombobox("dcsCode", dcsCodeUrl, {
                    keyField: "dcsCodeId",
                    valueField: "name",
                    data: {
                        isAll: true
                    },
                    selectValue: '-1',
                },function() {
                    $("#dcsCode option").eq(0).html("全部");
                }, function(data){
                    if (data) {
                        page.logic.initPrdtCell();
                    }
                });
            },
            /**
             * 初始化生产单元
             */
            initPrdtCell: function () {
                var tmp = $("#prdtCellId").val();
                OPAL.ui.getCombobox("prdtCellId", prdtCellList, {
                    keyField: "prdtCellId",
                    valueField: "name",
                    selectFirstRecord: true,
                    data: {
                        dcsCodeId: $("#dcsCode").val(),
                    }
                }, function () {
                    page.logic.echoSelect("prdtCellId", tmp);
                });
            },
            //下拉框回显
            echoSelect: function (id, originalValue) {
                $("#" + id).val(originalValue);
                if ($("#" + id).val() == '' || $("#" + id).val() == null) {
                    var arr = new Array();
                    $("#" + id + " option").each(function () {
                        arr.push($(this).val());
                    })
                    if (arr.length == 2) {
                        $("#" + id).val(arr[1]);
                    } else {
                        $("#" + id).val("-1");
                    }
                }
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                var checkTeamFlag = $("#checkTeam").is(":checked");//是否选班组
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function () {
                    },
                    multiple: checkTeamFlag?false:true,
                    onlyLeafCheck:checkTeamFlag ?true:false
                }, false);
            },
            /**
             * 报警点设置
             * @param alarmPoint
             */
            setAlarmPoint: function (unitId,prdtCell, tag,opcCode,dcsCode) {
                // if (tag == '') {
                //     layer.msg("报警点为空！");
                //     return;
                // }
                // if (reason == 2) {
                //     layer.msg("报警事件缓存表中生产单元与报警点中生产单元不一致!");
                //     return;
                // }
                layer.open({
                    type: 2,
                    title: '报警点配置',
                    closeBtn: 1,
                    area: ['1000px', '90%'],
                    shadeClose: false,
                    offset: '30px',
                    content: '../AlarmPoint/AlarmPointAddOrEdit.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var setData = {
                            "unitId": unitId,
                            "prdtCellId": prdtCell,
                            "tag": tag,
                            "pageMode": PageModelEnum.View
                        };
                        iframeWin.page.logic.setData(setData);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            $("#tbEventType").bootstrapTable('refresh', {
                                "url": searchUrl,
                                "pageNumber": 1
                            });
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#tbEventType').bootstrapTable('selectPage', 1);
                        }
                    }
                })
                // $.ajax({
                //     url: getPrdtCellInPrdtCellCompUrl,
                //     async: true,
                //     dataType: "JSON",
                //     data: {"prdtCellSource": prdtCell,"opcCode":opcCode,"dcsCode":dcsCode},
                //     contentType: "X-WWW-FORM-URLENCODED",
                //     type: 'GET',
                //     success: function (result) {
                //         var dataArr = $.ET.toObjectArr(result);
                        
                //     },
                //     error: function (result) {
                //         layer.msg("生产单元尚未配置，请先配置生产单元的对应规则！");
                //     }
                // })
            },
            /**
             * 导出Excel
             *
             * @param tableId   列表ID
             * @param actionURL 导出Excel表单Form ID
             * <AUTHOR> 2018-04-24
             */
            exportExcel: function (tableId, actionURL) {
                var titleArray = new Array();
                var tableTitle = $('#' + tableId).bootstrapTable('getOptions').columns[0];
                $.each(tableTitle, function (i, el) {
                    if(el.title!='序号'&&el.title!='操作'){
                        titleArray.push({
                            'key': el.field,
                            'value': el.title
                        });
                    }
                });
                var data = OPAL.form.getData('searchForm');
                data.titles = JSON.stringify(titleArray);
                page.logic.setExportExcelData('formExportExcel', data);

                $('#formExportExcel').attr('action', actionURL);
                $('#formExportExcel').submit();
            },
            /**
             * 设置Form Excel导出字段信息
             *
             * @param formId   form表单ID
             * @param dataObj  form表单对象数据
             * <AUTHOR>
            setExportExcelData: function (formId, dataObj) {
                var str = "";
                $.each(dataObj, function (key, value) {
                    if (value == "null" || value == undefined || value == null) {
                        value = "";
                    }
                    if (typeof value == 'object') {
                        $.each(value, function(m,n) {
                            str += "<input type=\"hidden\" name=" + key + "[] value=" + n + ">";
                        })
                    } else {
                        str += "<input type=\"hidden\" name=" + key + " value=" + value + ">";
                    }
                });
                $('#' + formId).html(str);
            }
        }
    };
    page.init();
    window.page = page;
})