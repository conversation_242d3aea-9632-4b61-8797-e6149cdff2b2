package com.pcitc.opal.pm.bll;


import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.entity.FactoryEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.common.bll.entity.WorkshopEntity;
import com.pcitc.opal.pm.bll.entity.UnitPersonEntity;
import com.pcitc.opal.pm.pojo.UnitPerson;
import org.springframework.data.domain.Pageable;

import java.util.List;


/**
 * 未分类数据查询业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_UnitPersonService
 * 作       者：zhipeng.guo
 * 创建时间：2018-12-17
 * 修改编号：1
 * 描       述：操作报警装置维护
 */

public interface UnitPersonService {




    /**
     * 调用promence接口查询封装装置的名称简称 车间简称
     * @param unitPerson  暂时只用到装置编码
     * @param UnitName   装置名称
     * @param workShopName  车间名称
     * <AUTHOR>
     * @return
     */
    List<UnitPersonEntity> query(UnitPerson unitPerson,String UnitName,String workShopName)throws Exception;

    /**
     * 调用promence分页接口查询封装装置的名称简称 车间简称
     * @param unitPerson  暂时只用到装置编码
     * @param UnitName   装置名称
     * @param workShopName  车间名称
     * @param pageNumber 当前页  从1开始
     * @param pageSize 数据量
     * <AUTHOR>
     * @return
     */
    PaginationBean <UnitPersonEntity> getInPage(UnitPerson unitPerson,String UnitName,String workShopName,Integer pageNumber,Integer pageSize) throws Exception;

    /**
     * 新增
     * @param unitPerson  包含人数和code
     * @return  校验结果或者保存成功
     * <AUTHOR>
     */
    CommonResult addUnitPerson(UnitPerson unitPerson)throws Exception;


    /**
     * 编辑装置人员信息
     * @param unitPerson  需要编辑的对象
     * <AUTHOR>
     * @throws Exception
     */
    CommonResult edditUnitPerson(UnitPerson unitPerson)throws Exception;

    /**
     * 根据车间编码获取车间编码下的装置，调用promence
     * @param workShopCode  车间编码
     * <AUTHOR>
     * @throws Exception
     */
    List<UnitEntity> getUnitListByWorkshopIds(String workShopCode)throws Exception;


    /**
     * 获取工厂集合调用promence
     * <AUTHOR>
     * @throws Exception
     */
    List<FactoryEntity> getFactoryList() throws Exception;

    /**
     * 根据工厂编码获取车间列表调用promence
     * <AUTHOR>
     * @throws Exception
     */
    List<WorkshopEntity> getWorkShop(String...factoryCode) throws Exception;

    /**
     * 根据ID物理删除人员装置表中的数据
     * @param unitPersonId  人员装置表ID
     * <AUTHOR>
     * @throws Exception
     */
    CommonResult deleteUnitPerson(Long...unitPersonId)throws Exception;

    /**
     * 获取单个装置人员信息
     * @param unitPersonId   装置人员ID
     * @return
     * @throws Exception
     */
    UnitPersonEntity getSingle(Long unitPersonId)throws Exception;

}
