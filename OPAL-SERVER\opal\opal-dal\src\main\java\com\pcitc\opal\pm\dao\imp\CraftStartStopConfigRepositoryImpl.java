package com.pcitc.opal.pm.dao.imp;


import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.CraftStartStopConfigRepositoryCustom;
import com.pcitc.opal.pm.pojo.CraftStartStopConfig;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.TypedQuery;
import java.util.*;


public class CraftStartStopConfigRepositoryImpl extends BaseRepository<CraftStartStopConfig, Long> implements CraftStartStopConfigRepositoryCustom {


    @Override
    public List<CraftStartStopConfig> selectByParams(CraftStartStopConfig config) {

        try {
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 查询字符串
            StringBuilder hql = new StringBuilder("from CraftStartStopConfig t where t.unitCode = :unitCode");
            hql.append(" and t.stopTime = :stopTime");
            paramList.put("unitCode", config.getUnitCode());
            paramList.put("stopTime", config.getStopTime());
            String prdtCode = config.getPrdtCode();
            if (StringUtils.isNoneBlank(prdtCode)) {
                hql.append(" and t.prdtCode = :prdtCode");
                paramList.put("prdtCode", prdtCode);
            }
            TypedQuery<CraftStartStopConfig> query = getEntityManager().createQuery(hql.toString(), CraftStartStopConfig.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }

    }


}
