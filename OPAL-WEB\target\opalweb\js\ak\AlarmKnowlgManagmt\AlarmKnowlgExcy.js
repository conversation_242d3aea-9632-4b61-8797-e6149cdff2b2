$(function() {
    var getAlarmKnowlgExcyDetailUrl = OPAL.API.akUrl + '/alarmKnowlgManagmt/getAlarmKnowlgExcyDetail';
    var getAlarmKnowlgManagmtUrl = OPAL.API.akUrl + '/alarmKnowlgManagmt/getAlarmKnowlgManagmt';
    var saveUrl = OPAL.API.akUrl + '/alarmKnowlgManagmt';
    var isRefresh = false;
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var page = {
        /**
         * 初始化
         */
        init: function() {
            //绑定事件
            this.bindUI();
        },
        /**
         * 绑定事件
         */
        bindUI: function() {
            /*关闭弹窗*/
            $('#closePage').click(function() {
                page.logic.closeLayer(false);
            });
            $('.closeBtn').click(function() {
                page.logic.closeLayer(false);
            });
            //保存
            $("#saveAddModal").click(function() {
                page.logic.save();
            })
        },
        data: {
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            initTable: function() {
                OPAL.ui.initBootstrapTable("alarmKnowlgManagmtTable", {
                    cache: false,
                    striped: true,
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '40px'
                    }, {
                        title: "原因",
                        field: 'reason',
                        rowspan: 1,
                        align: 'left',
                        width: '130px'
                    }, {
                        title: "处置方案",
                        field: 'disposalScheme',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "影响",
                        field: 'impact',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "报警时间",
                        field: 'alarmTime',
                        rowspan: 1,
                        align: 'center',
                        width: '135px'
                    }, {
                        title: "创建时间",
                        field: 'crtDate',
                        rowspan: 1,
                        align: 'center',
                        width: '135px'
                    }],
                    rowStyle: function(row, index) {
                         var style;
                         if(row.isRed == 1){
                             style = 'redRow'
                         }
                         return {
                             classes: style
                         }
                     }
                }, page.logic.queryParams)
            },
            queryParams: function(p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            search: function() {
                $("#alarmKnowlgManagmtTable").bootstrapTable('refresh', {
                    "url": getAlarmKnowlgManagmtUrl,
                    "pageNumber": 1
                });
            },
            setData: function(data) {
                $("input[name='eventId']").val(data.eventId);
                $.ajax({
                    url: getAlarmKnowlgExcyDetailUrl,
                    async: true,
                    data: data,
                    dataType: "json",
                    success: function(data) {
                        var obj = $.ET.toObjectArr(data)[0];
                        for (var key in obj) {
                            $("#" + key).html(obj[key]);
                            $("#" +key).attr("title", obj[key]);
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {}
                });
                //初始化表格
                page.logic.initTable();
                page.data.param = data;
                page.logic.search();
            },
            save: function() {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data = OPAL.form.getETCollectionData('AddOrEditModal');
                $.ajax({
                    url: saveUrl,
                    async: false,
                    type: "post",
                    data: JSON.stringify(data),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function(result, XMLHttpRequest) { //保存成功
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！");
                            $("#reason").val('');
                            $("#disposalScheme").val('');
                            $("#impact").val('');
                            page.logic.search();
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    },
                    error: function(result, jqXHR) { //保存失败
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            formValidate: function() {
                OPAL.form.formValidate('AddOrEditModal', {
                    rules: {
                        reason: {
                            required: true
                        },
                        disposalScheme: {
                            required: true
                        }
                    }
                })
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function(isRefresh) {
                parent.isRefresh = isRefresh;
                parent.layer.close(index);
            }
        }
    }
    page.init();
    window.page = page;
})