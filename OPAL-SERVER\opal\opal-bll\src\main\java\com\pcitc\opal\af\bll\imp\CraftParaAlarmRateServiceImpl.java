package com.pcitc.opal.af.bll.imp;

import com.pcitc.opal.aa.bll.AlarmAssessFirstPageService;
import com.pcitc.opal.aa.bll.entity.AlarmLevelAssessDetailEntity;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmRecDAO;
import com.pcitc.opal.ad.dao.AlarmRecRepository;
import com.pcitc.opal.ad.dao.imp.AlarmTagUnitVO;
import com.pcitc.opal.ad.entity.AlarmRecEntity;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.ad.vo.AlarmRecInfoParamVO;
import com.pcitc.opal.ad.vo.AlarmRecInfoVO;
import com.pcitc.opal.af.bll.CraftParaAlarmRateService;
import com.pcitc.opal.af.bll.entity.*;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.DateRangeEntity;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.common.bll.imp.NewAAAServiceImpl;
import com.pcitc.opal.pm.dao.CraftAlarmRateConfRepository;
import com.pcitc.opal.pm.dao.UnitRepository;
import com.pcitc.opal.pm.pojo.CraftAlarmRateConf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTPieSer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CraftParaAlarmRateServiceImpl implements CraftParaAlarmRateService {

    @Autowired
    private BasicDataService basicDataService;
    @Autowired
    private AlarmAssessFirstPageService alarmAssessFirstPageService;
    @Autowired
    private UnitRepository unitRepository;
    @Autowired
    private CraftAlarmRateConfRepository craftAlarmRateConfRepository;
    @Autowired
    private AlarmRecRepository alarmRecRepository;
    @Resource
    private ShiftService shiftService;

    @Autowired
    private AlarmRecDAO alarmRecDAO;

    /**
     * 获取页面-报警分析-工艺参数报警率网格列信息
     *
     * @param unitIds
     * @param startTime
     * @param newEndTime
     * @param workTeamIds
     * @return
     * @throws Exception
     */
    @Override
    public List<CraftParaAlarmRateEntity> getCraftParaAlarmRate(String[] unitIds, List<Integer> monitorType, Date startTime, Date newEndTime, Integer companyId, Long workTeamIds) {
        List<CraftParaAlarmRateEntity> craftParaAlarmRateList = null;
        try {
            List<UnitEntity> unitEntityList = basicDataService.getUnitListByIds(unitIds, true);
            List<String> unitCodes = unitEntityList.stream().map(UnitEntity::getStdCode).distinct().collect(Collectors.toList());
            AlarmRecInfoParamVO paramVO = new AlarmRecInfoParamVO();
            paramVO.setStartTime(startTime);
            paramVO.setEndTime(newEndTime);
            paramVO.setUnitCodes(unitCodes);
            paramVO.setMonitorTypeList(monitorType);
            List<AlarmRecInfoVO> alarmRecs = alarmRecDAO.selectInfoList(paramVO);

            craftParaAlarmRateList = getCraftParaAlarmRateList(unitEntityList, startTime, newEndTime, workTeamIds, true, alarmRecs);

            // 处理每个装置下个班组的详细信息
            if (workTeamIds == null) {
                for (CraftParaAlarmRateEntity entity : craftParaAlarmRateList) {
                    List<ShiftWorkTeamEntity> shiftWorkTeamList = shiftService.getShiftWorkTeamList(entity.getUnitid(), startTime, newEndTime);
                    // 过滤掉全部并且按照排序字段正序排列
                    List<ShiftWorkTeamEntity> teams = shiftWorkTeamList.stream().filter(x -> !x.getWorkTeamSName().equals("全部")).sorted(Comparator.comparingLong(ShiftWorkTeamEntity::getWorkTeamSortNum)).collect(Collectors.toList());
                    List<CraftParaAlarmRateEntity> teamDetail = new ArrayList<>();
                    // 循环每个班组查询数据
                    for (ShiftWorkTeamEntity team : teams) {
                        UnitEntity unitEntity = new UnitEntity();
                        unitEntity.setStdCode(entity.getUnitid());
                        // 过滤数据，只保留当前查询的装置数据
                        List<AlarmRecInfoVO> collect = alarmRecs.stream().filter(x -> x.getUnitCode().equals(unitEntity.getStdCode())).collect(Collectors.toList());
                        List<CraftParaAlarmRateEntity> workAlarmRateList = getCraftParaAlarmRateList(Arrays.asList(unitEntity), startTime, newEndTime, team.getWorkTeamId(), false, collect);
                        if (CollectionUtils.isNotEmpty(workAlarmRateList)) {
                            CraftParaAlarmRateEntity workAlarmRate = workAlarmRateList.get(0);
                            workAlarmRate.setUnitCode(team.getWorkTeamName());
                            workAlarmRate.setUnitid(team.getWorkTeamName());
                            teamDetail.add(workAlarmRate);
                        }
                    }
                    entity.setTeamDetail(teamDetail);
                }
            }
            //以上查询结果根据列“工艺参数报警率”升序排列展示在网格列表中；
            craftParaAlarmRateList.sort(Comparator.comparing(CraftParaAlarmRateEntity::getCraftParaAlarmRate));
        } catch (RuntimeException e) {
            log.error("工艺参数报警率查询异常---" + e.getMessage(), e);
        } catch (Exception e) {
            log.error("工艺参数报警率查询异常", e);
        }
        return craftParaAlarmRateList;
    }

    /**
     * 获取页面-报警分析-工艺参数报警率线形图信息
     *
     * @param unitIds
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    @Override
    public AlarmLevelAssessDetailEntity getCraftParaAlarmRateCurve(String[] unitIds, Date startTime, Date endTime) throws Exception {

        //List<CraftParaAlarmRateEntity> craftParaAlarmRateList = getCraftParaAlarmRateList(unitIds, startTime, endTime);
        List<DictionaryEntity> dateTimeList = basicDataService.getQueryStartAndEndDate(startTime, endTime);

        //如果查询时间大于今天八点，趋势图多展示一个点（八点到当前查询时间）
//        startTime = (Date) dateTimeList.get(0).getValue();
        endTime = (Date) dateTimeList.get(1).getValue();

        AlarmLevelAssessDetailEntity variationTrend = alarmAssessFirstPageService.getVariationTrend(startTime, endTime, unitIds);
        variationTrend.setDateTimeList(dateTimeList);
        return variationTrend;
    }


//    /**
//     * 获取单装置工艺参数报警率
//     */
//    private CraftParaAlarmRateEntity getCraftParaAlarmRateList(UnitEntity unitEntity, Date startTime, Date endTime, Long workTeamIds, boolean isCalculateDetails, List<AlarmRec> alarmRecs) {
//        ArrayList<UnitEntity> unitEntities = new ArrayList<>();
//        unitEntities.add(unitEntity);
//        // 过滤数据，只保留当前查询的装置数据
//        List<AlarmRec> collect = alarmRecs.stream().filter(x -> x.getUnitCode().equals(unitEntity.getStdCode())).collect(Collectors.toList());
//        return getCraftParaAlarmRateList(unitEntities, startTime, endTime, workTeamIds, isCalculateDetails, collect).get(0);
//    }


//    /**
//     * 计算工艺参数报警率以及详细信息
//     *
//     * @param unitEntityList     装置
//     * @param startTime          开始时间
//     * @param endTime            结束时间
//     * @param workTeamIds        班组id
//     * @param isCalculateDetails 是否获取各装置数据的详细信息
//     */
//    public List<CraftParaAlarmRateEntity> getCraftParaAlarmRateList(List<UnitEntity> unitEntityList, Date startTime, Date endTime, Long workTeamIds, boolean isCalculateDetails) {
//        String[] unitIds = unitEntityList.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
//        List<AlarmRec> alarmRecs = alarmRecRepository.getAlarmRec(startTime, endTime, unitIds);
//        return getCraftParaAlarmRateList(unitEntityList, startTime, endTime, workTeamIds, isCalculateDetails, alarmRecs);
//    }

    /**
     * 计算工艺参数报警率以及详细信息
     *
     * @param unitEntityList     装置
     * @param startTime          开始时间
     * @param endTime            结束时间
     * @param workTeamIds        班组id
     * @param isCalculateDetails 是否获取各装置数据的详细信息
     * @param alarmRecs          要处理的数据集合
     */
    private List<CraftParaAlarmRateEntity> getCraftParaAlarmRateList(List<UnitEntity> unitEntityList, Date startTime, Date endTime, Long workTeamIds, boolean isCalculateDetails, List<AlarmRecInfoVO> alarmRecs) {

        DecimalFormat df = new DecimalFormat("0.00");

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        String[] unitIds = unitEntityList.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);

        //获取得分配置信息
        List<CraftAlarmRateConf> coefficient = craftAlarmRateConfRepository.getAllCraftAlarmRateConf();
        //用于取出得分系数
        Map<Integer, CraftAlarmRateConf> rateConfMap = coefficient.stream().collect(Collectors.toMap(x -> x.getType().intValue(), x -> x, (existingValue, newValue) -> newValue));
        //平均报警数得分系数
        Double averageCoefficient = rateConfMap.get(CommonEnum.CraftAlarmRateConfTypeEnum.AverageCoefficient.getIndex()).getRatio();
        //24小时持续报警数得分系数
        Double hourCoefficient = rateConfMap.get(CommonEnum.CraftAlarmRateConfTypeEnum.HourCoefficient.getIndex()).getRatio();
        //峰值报警数得分系数
        Double peakCoefficient = rateConfMap.get(CommonEnum.CraftAlarmRateConfTypeEnum.PeakCoefficient.getIndex()).getRatio();
        //报警响应及时率得分系数
        Double alarmTimelyResponseRateCoefficient = rateConfMap.get(CommonEnum.CraftAlarmRateConfTypeEnum.AlarmTimelyResponseRateCoefficient.getIndex()).getRatio();
        //报警处置及时率得分系数
        Double alarmTimelyDisposalRateCoefficient = rateConfMap.get(CommonEnum.CraftAlarmRateConfTypeEnum.AlarmTimelyDisposalRateCoefficient.getIndex()).getRatio();

        //初始化所有装置数据
        Map<String, CraftParaAlarmRateEntity> newMap = initCraftParaAlarmRateMap(unitEntityList);


        //各类型得分
        Map<Integer, List<CraftAlarmRateConf>> coefficientmap = getCoefficientmap(coefficient);


//        List<AlarmRec> alarmRecs = alarmRecRepository.getAlarmRec(startTime, endTime, unitIds);

        //间隔时间，时平均报警用
        double intervalHours = 0D;
        //勾选班组时过滤数据
        if (workTeamIds != null) {
            try {
                List<ShiftWorkTeamEntity> shiftList = shiftService.getShiftList(unitIds[0], startTime, endTime, Collections.singletonList(workTeamIds));
                List<DateRangeEntity> dateRangeEntities = shiftList.stream().map(item -> new DateRangeEntity(item.getStartTime(), item.getEndTime())).collect(Collectors.toList());
                List<AlarmRecInfoVO> alarmRecsWorkTeam = new ArrayList<>();

                //过滤数据，只保留在班组范围之内的数据
                for (DateRangeEntity dateRangeEntity : dateRangeEntities) {
                    //计算时间区间内班组间隔时间
                    intervalHours += Math.ceil((Math.min(dateRangeEntity.getEndTime().getTime(), endTime.getTime()) - dateRangeEntity.getStartTime().getTime()) / 1000.0 / 60 / 60);
                    List<AlarmRecInfoVO> collect = alarmRecs.stream().filter(x -> x.getAlarmTime().getTime() >= dateRangeEntity.getStartTime().getTime() && x.getAlarmTime().getTime() <= dateRangeEntity.getEndTime().getTime()).collect(Collectors.toList());
                    alarmRecsWorkTeam.addAll(collect);
                }
                //替换掉原有数据
                alarmRecs = alarmRecsWorkTeam;
            } catch (Exception e) {
                throw new RuntimeException("工艺参数报警率班组获取异常", e);
            }

        } else {
            //计算从开始到结束间隔小时数
            intervalHours = Math.ceil((endTime.getTime() - startTime.getTime()) / 1000.0 / 60 / 60);
        }

        // 按照装置对数据进行分组
        Map<String, List<AlarmRecInfoVO>> unitMaps = alarmRecs.stream().collect(Collectors.groupingBy(AlarmRecInfoVO::getUnitCode));


        //将开始结束时间转换为LocalDateTime
        LocalDateTime startLocalDateTime = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime endLocalDateTime = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();


        //时平均报警数
        for (Map.Entry<String, List<AlarmRecInfoVO>> s : unitMaps.entrySet()) {
            CraftParaAlarmRateEntity entity = newMap.get(s.getKey());

            List<AlarmRecInfoVO> value = s.getValue();
            entity.setAvgAlarmRate(Double.valueOf(df.format(value.size() / intervalHours)));

            //--------------------------详细信息--------------------------
            if (isCalculateDetails) {
                LinkedHashMap<String, Double> avgAlarmRateDetail = new LinkedHashMap<>();
                //按照日期将数据分组数据
                LinkedHashMap<LocalDateTime, List<AlarmRecInfoVO>> splitDate = splitDate(startLocalDateTime, endLocalDateTime, value);
                for (Map.Entry<LocalDateTime, List<AlarmRecInfoVO>> maps : splitDate.entrySet()) {
                    String format = maps.getKey().format(formatter);
                    if (maps.getKey().plusDays(1).isAfter(endLocalDateTime)) {
                        //处理最后一天
                        double v = maps.getValue().size() / Math.ceil(Duration.between(maps.getKey(), endLocalDateTime).toMinutes() / 24.0);
                        avgAlarmRateDetail.put(format, Double.valueOf(df.format(v)));
                    } else {
                        avgAlarmRateDetail.put(format, Double.valueOf(df.format(maps.getValue().size() / 24.0)));
                    }
                }
                entity.setAvgAlarmRateDetail(avgAlarmRateDetail);
            }
        }


        //峰值报警率
        for (Map.Entry<String, List<AlarmRecInfoVO>> s : unitMaps.entrySet()) {

            CraftParaAlarmRateEntity entity = newMap.get(s.getKey());

            List<AlarmRecInfoVO> value = s.getValue();

            //将时间格式化并分组
            Map<Date, List<AlarmRecInfoVO>> collect = value.stream().collect(Collectors.groupingBy(x -> this.setHours(x.getAlarmTime())));

            //找出分组内报警数最多的集合数量
            int size = collect.values().stream().max(Comparator.comparingInt(List::size)).orElse(Collections.emptyList()).size();
            entity.setPeakAlarmRate((double) size);

            //--------------------------详细信息--------------------------
            if (isCalculateDetails) {
                LinkedHashMap<String, Integer> peakAlarmRateDetail = new LinkedHashMap<>();
                //先按照日期分组
                LinkedHashMap<LocalDateTime, List<AlarmRecInfoVO>> splitDate = splitDate(startLocalDateTime, endLocalDateTime, value);
                for (Map.Entry<LocalDateTime, List<AlarmRecInfoVO>> localDateTimeListEntry : splitDate.entrySet()) {
                    int size1 = localDateTimeListEntry.getValue().stream().collect(Collectors.groupingBy(x -> this.setHours(x.getAlarmTime()))).values().stream().max(Comparator.comparingInt(List::size)).orElse(Collections.emptyList()).size();
                    peakAlarmRateDetail.put(localDateTimeListEntry.getKey().format(formatter), size1);
                }
                entity.setPeakAlarmRateDetail(peakAlarmRateDetail);
            }
        }

        //按班组过滤不计算此字段
        //24小时持续报警数
        if (workTeamIds == null) {
            for (Map.Entry<String, List<AlarmRecInfoVO>> s : unitMaps.entrySet()) {
                CraftParaAlarmRateEntity entity = newMap.get(s.getKey());
                //恢复时间为空伏当前查询结束时间->过滤超过24小时的数据
                List<AlarmRecInfoVO> recs = s.getValue().stream().filter(x -> (x.getRecoveryTime() == null ? endTime : x.getRecoveryTime()).getTime() - x.getAlarmTime().getTime() > 1000 * 60 * 60 * 24).collect(Collectors.toList());
                entity.setAlarmAmount((double) recs.size());


                //--------------------------详细信息--------------------------
                if (isCalculateDetails) {
                    List<ContinuedAlarmEntity> rateDetail = getRateDetail(recs, 1, 1, endTime);
                    entity.setAlarmAmountDetail(rateDetail);
                }

            }
        }

        //报警响应及时率
        for (Map.Entry<String, List<AlarmRecInfoVO>> s : unitMaps.entrySet()) {
            CraftParaAlarmRateEntity entity = newMap.get(s.getKey());

            //响应时间为空伏当前查询结束时间->过滤小于30秒的数据
            List<AlarmRecInfoVO> recs = s.getValue().stream().filter(x -> (x.getResponseTime() == null ? endTime : x.getResponseTime()).getTime() - x.getAlarmTime().getTime() <= 1000 * 30).collect(Collectors.toList());

            if (s.getValue().isEmpty()) {
                entity.setAlarmTimelyResponseRate(100D);
            } else {
                entity.setAlarmTimelyResponseRate(Double.valueOf(df.format(recs.size() * 1.0 / s.getValue().size() * 100)));
            }

            //--------------------------详细信息--------------------------
            if (isCalculateDetails) {
                List<AlarmRecInfoVO> recs1 = s.getValue().stream().filter(x -> (x.getResponseTime() == null ? endTime : x.getResponseTime()).getTime() - x.getAlarmTime().getTime() > 1000 * 30).collect(Collectors.toList());
                List<ContinuedAlarmEntity> rateDetail = getRateDetail(recs1, 2, 3, endTime);
                entity.setAlarmTimelyResponseRateDetail(rateDetail);
            }

        }


        //报警处置及时率
        for (Map.Entry<String, List<AlarmRecInfoVO>> s : unitMaps.entrySet()) {
            CraftParaAlarmRateEntity entity = newMap.get(s.getKey());

            //恢复时间为空伏当前查询结束时间->过滤超过30分钟的数据
            List<AlarmRecInfoVO> recs = s.getValue().stream().filter(x -> (x.getRecoveryTime() == null ? endTime : x.getRecoveryTime()).getTime() - x.getAlarmTime().getTime() <= 1000 * 60 * 30).collect(Collectors.toList());

            if (s.getValue().isEmpty()) {
                entity.setAlarmTimelyDisposalRate(100D);
            } else {
                entity.setAlarmTimelyDisposalRate(Double.valueOf(df.format(recs.size() * 1.0 / s.getValue().size() * 100)));
            }

            //--------------------------详细信息--------------------------
            if (isCalculateDetails) {
                List<AlarmRecInfoVO> recs1 = s.getValue().stream().filter(x -> (x.getRecoveryTime() == null ? endTime : x.getRecoveryTime()).getTime() - x.getAlarmTime().getTime() > 1000 * 60 * 30).collect(Collectors.toList());
                List<ContinuedAlarmEntity> rateDetail = getRateDetail(recs1, 1, 2, endTime);
                entity.setAlarmTimelyDisposalRateDetail(rateDetail);
            }
        }

        List<CraftParaAlarmRateEntity> returnList = new ArrayList<>(newMap.values());

        Double avgCraft = 0D;
        Double avgAlarmRate = 0D;
        Double avgAlarmAmount = 0D;
        //计算工艺参数报警率
        for (CraftParaAlarmRateEntity entity : returnList) {
            Double craft = 0d;
            //是平均报警数
            craft += getScore(coefficientmap.get(CommonEnum.CraftAlarmRateConfTypeEnum.AverageScore.getIndex()), averageCoefficient, entity.getAvgAlarmRate());
            //24小时持续报警数
            craft += getScore(coefficientmap.get(CommonEnum.CraftAlarmRateConfTypeEnum.HourContinuousScore.getIndex()), hourCoefficient, entity.getAlarmAmount());
            //峰值报警数
            craft += getScore(coefficientmap.get(CommonEnum.CraftAlarmRateConfTypeEnum.MinutesScore.getIndex()), peakCoefficient, entity.getPeakAlarmRate());
            //响应及时率
            craft += getScore(coefficientmap.get(CommonEnum.CraftAlarmRateConfTypeEnum.AlarmTimelyResponseRateScore.getIndex()), alarmTimelyResponseRateCoefficient, entity.getAlarmTimelyResponseRate());
            //处置及时率
            craft += getScore(coefficientmap.get(CommonEnum.CraftAlarmRateConfTypeEnum.AlarmTimelyDisposalRateScore.getIndex()), alarmTimelyDisposalRateCoefficient, entity.getAlarmTimelyDisposalRate());

            avgCraft += craft;
            avgAlarmRate += entity.getAvgAlarmRate();
            avgAlarmAmount += entity.getAlarmAmount();
            entity.setCraftParaAlarmRate(craft);

        }
        Double avgAlarmTimelyResponseRate = 100D;
        Double avgAlarmTimelyDisposalRate = 100D;

        if (!alarmRecs.isEmpty()) {
            //计算所有装置响应及时率
            List<AlarmRecInfoVO> avgAlarmTimelyResponseRates = alarmRecs.stream().filter(x -> (x.getResponseTime() == null ? endTime : x.getResponseTime()).getTime() - x.getAlarmTime().getTime() <= 1000 * 30).collect(Collectors.toList());
            avgAlarmTimelyResponseRate = Double.valueOf(df.format(avgAlarmTimelyResponseRates.size() * 1.0 / alarmRecs.size() * 100));

            //计算所有装置处置及时率
            List<AlarmRecInfoVO> avgAlarmTimelyDisposalRates = alarmRecs.stream().filter(x -> (x.getRecoveryTime() == null ? endTime : x.getRecoveryTime()).getTime() - x.getAlarmTime().getTime() <= 1000 * 60 * 30).collect(Collectors.toList());
            avgAlarmTimelyDisposalRate = Double.valueOf(df.format(avgAlarmTimelyDisposalRates.size() * 1.0 / alarmRecs.size() * 100));
        }

        //计算平均工艺参数报警率
        if (returnList.isEmpty()) {
            avgCraft = 0D;
            avgAlarmRate = 0D;
        } else {
            avgCraft /= returnList.size();
            avgAlarmRate /= returnList.size();
        }

        //赋值合计列
        for (CraftParaAlarmRateEntity entity : returnList) {
            entity.setAvgCraftParaAlarmRate(Double.valueOf(df.format(avgCraft)));
            entity.setAvgAvgAlarmRate(avgAlarmRate);
            entity.setAvgAlarmAmount(avgAlarmAmount);
            entity.setEndTime(endLocalDateTime.format(formatter));
            entity.setAvgAlarmTimelyResponseRate(avgAlarmTimelyResponseRate);
            entity.setAvgAlarmTimelyDisposalRate(avgAlarmTimelyDisposalRate);
        }

        return returnList;
    }


    /**
     * 将传入数据按指定字段分组
     *
     * @param alarmRecs   recList
     * @param timeType    1:使用恢复时间参与计算，2：使用响应时间参与计算
     * @param betweenType 1:小时，2：分钟，3：秒（计算两个日期相差的时间）
     */
    public List<ContinuedAlarmEntity> getRateDetail(List<AlarmRecInfoVO> alarmRecs, int timeType, int betweenType, Date endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        List<ContinuedAlarmEntity> continuedAlarmEntities = new ArrayList<>();

        Map<String, List<AlarmRecInfoVO>> recs = alarmRecs.stream().collect(Collectors.groupingBy(x -> x.getAlarmFlagId() + x.getPrdtCellId() + x.getDes() + x.getTag() + x.getPriority() + x.getAlarmPointId()));
        for (Map.Entry<String, List<AlarmRecInfoVO>> maps : recs.entrySet()) {
            ContinuedAlarmEntity continuedAlarmEntity = new ContinuedAlarmEntity(maps.getValue().get(0));
            continuedAlarmEntity.setAlarmNumber(maps.getValue().size());

            //计算时间差值，如果所计算时间为空则使用查询结束时间代替
            List<ContinuedAlarmDetail> details = maps.getValue().stream().map(x -> {
                Date date = (timeType == 1 ? x.getRecoveryTime() : x.getResponseTime());
                if (date == null) {
                    date = endDate;
                }
                long timeLen = 0;
                if (betweenType == 1) {
                    timeLen = Duration.between(x.getAlarmTime().toInstant(), (date).toInstant()).toHours();
                } else if (betweenType == 2) {
                    timeLen = Duration.between(x.getAlarmTime().toInstant(), (date).toInstant()).toMinutes();
                } else if (betweenType == 3) {
                    timeLen = Duration.between(x.getAlarmTime().toInstant(), (date).toInstant()).getSeconds();
                }
                return new ContinuedAlarmDetail().setAlarmTime(sdf.format(x.getAlarmTime())).setRecoveryTime(x.getRecoveryTime() == null ? null : sdf.format(x.getRecoveryTime())).setResponseTime(x.getResponseTime() == null ? null : sdf.format(x.getResponseTime())).setTimeLen(timeLen + "");
            }).collect(Collectors.toList());
            continuedAlarmEntity.setDetails(details);
            continuedAlarmEntities.add(continuedAlarmEntity);
        }
        return continuedAlarmEntities;
    }


    /**
     * 把数据按照传入的开始结束时间进行分割，分割开始时间为每天的8:00:00
     *
     * @param start 开始时间
     * @param end   结束时间
     * @param recs  要分割的数据
     * @return map，key->，每个时间段的开始时间，value->时间段对呀数据
     */
    public LinkedHashMap<LocalDateTime, List<AlarmRecInfoVO>> splitDate(LocalDateTime start, LocalDateTime end, List<AlarmRecInfoVO> recs) {
        if (CollectionUtils.isEmpty(recs)) {
            return new LinkedHashMap<>();
        }
        List<LocalDateTime> timeIntervals = new ArrayList<>();
        LocalDateTime currentInterval = start.toLocalDate().atTime(LocalTime.of(0, 0, 0));
        while (currentInterval.isBefore(end)) {
            timeIntervals.add(currentInterval);
            currentInterval = currentInterval.plusDays(1);
        }
        Map<LocalDateTime, List<AlarmRecInfoVO>> collect = recs.stream().collect(Collectors.groupingBy(record -> findTimeInterval(record.getAlarmTime(), timeIntervals)));
        //填充数据为空的时间段
        for (LocalDateTime timeInterval : timeIntervals) {
            if (!collect.containsKey(timeInterval)) {
                collect.put(timeInterval, Collections.emptyList());
            }
        }
        //按照key排序
        return collect.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
    }


    /**
     * 通过传入的时间来确定当前时间属于哪个list时间区间
     *
     * @param dateTime      时间
     * @param timeIntervals List<LocalDateTime>
     */
    private static LocalDateTime findTimeInterval(Date dateTime, List<LocalDateTime> timeIntervals) {
        LocalDateTime localDateTime = dateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return timeIntervals.stream().filter(interval -> localDateTime.isAfter(interval.minusNanos(1)) && localDateTime.isBefore(interval.plusDays(1).minusNanos(1))).findFirst().orElse(null);
    }

    public Date setHours(Date date) {
        //不修改原有对象
        Date date1 = new Date(date.getTime());
        date1.setMinutes(date.getHours() / 10 * 10);
        date1.setSeconds(0);
        return date1;
    }

    /**
     * 初始化工艺参数报警率网格列数据
     *
     * @param unitEntityList 装置
     */
    public Map<String, CraftParaAlarmRateEntity> initCraftParaAlarmRateMap(List<UnitEntity> unitEntityList) {
        Map<String, CraftParaAlarmRateEntity> map = new HashMap<>();
        for (UnitEntity unit : unitEntityList) {
            CraftParaAlarmRateEntity craftParaAlarmRateEntity = new CraftParaAlarmRateEntity();
            craftParaAlarmRateEntity.setAlarmAmount(0D);
            craftParaAlarmRateEntity.setAvgAlarmRate(0d);
            craftParaAlarmRateEntity.setPeakAlarmRate(0d);
            craftParaAlarmRateEntity.setUnitCode(unit.getSname());
            craftParaAlarmRateEntity.setUnitid(unit.getStdCode());
            craftParaAlarmRateEntity.setAlarmTimelyResponseRate(100d);
            craftParaAlarmRateEntity.setAlarmTimelyDisposalRate(100d);
            craftParaAlarmRateEntity.setCraftParaAlarmRate(0d);
            craftParaAlarmRateEntity.setAvgAvgAlarmRate(0D);
            craftParaAlarmRateEntity.setAvgCraftParaAlarmRate(0D);
            craftParaAlarmRateEntity.setAvgAlarmTimelyResponseRate(0D);
            craftParaAlarmRateEntity.setAvgAlarmTimelyDisposalRate(0D);
            craftParaAlarmRateEntity.setAvgAlarmAmount(0D);
            map.put(unit.getStdCode(), craftParaAlarmRateEntity);
        }
        return map;
    }

    /**
     * 将获取到的得分配置按照类型转换为map
     *
     * @param coefficient 得分配置
     * @return Map<Integer, List < CraftAlarmRateConf>>
     */
    public Map<Integer, List<CraftAlarmRateConf>> getCoefficientmap(List<CraftAlarmRateConf> coefficient) {
        Map<Integer, List<CraftAlarmRateConf>> coefficientmap = new HashMap<>();

        //初始化map中的value
        coefficient.forEach(x -> coefficientmap.put(x.getType().intValue(), new ArrayList<CraftAlarmRateConf>()));

        for (CraftAlarmRateConf craftAlarmRateConf : coefficient) {
            if (CommonEnum.CraftAlarmRateConfTypeEnum.getName(craftAlarmRateConf.getType().intValue()) != null) {
                coefficientmap.get(craftAlarmRateConf.getType().intValue()).add(craftAlarmRateConf);
            }
        }
        return coefficientmap;
    }


    /**
     * 通过传入的配置以及值来计算当前数据的工艺参数报警率
     *
     * @param confList     配置
     * @param currentValue 当前值
     * @param coefficient  得分系数
     * @return 计算后的值
     */
    public Double getScore(List<CraftAlarmRateConf> confList, double coefficient, double currentValue) {
        double value = 0d;
        for (CraftAlarmRateConf craftAlarmRateConf : confList) {
            if (null != craftAlarmRateConf.getUpLimitValue() && craftAlarmRateConf.getDownLimitValue() < currentValue && craftAlarmRateConf.getUpLimitValue() >= currentValue) {
                value = craftAlarmRateConf.getScore() * coefficient;
                break;//计算分数成功后跳出循环
            } else if (null == craftAlarmRateConf.getUpLimitValue() && craftAlarmRateConf.getDownLimitValue() < currentValue) {
                value = craftAlarmRateConf.getScore() * coefficient;
                break;//计算分数成功后跳出循环
            }
        }
        return value;
    }

    @Override
    public List<ContinuedAlarmEntity> GetContinuedAlarm(String[] unitId, Date startTime, Date endTime) {
        try {
            Pagination pagination = new Pagination();
            pagination.setPageSize(Integer.MAX_VALUE);
            pagination.setPageNumber(1);
            PaginationBean<AlarmEventEntity> persistentAlarmAnalysis = basicDataService.getPersistentAlarmAnalysis(unitId, null, -1, -1, startTime, endTime, pagination);

            List<AlarmEventEntity> pageList = persistentAlarmAnalysis.getPageList();

            //分组
            Map<String, List<AlarmEventEntity>> collect = pageList.stream().collect(Collectors.groupingBy(AlarmEventEntity::getSplicing));


            List<ContinuedAlarmEntity> returnList = new ArrayList<>();
            for (Map.Entry<String, List<AlarmEventEntity>> map : collect.entrySet()) {

                ContinuedAlarmEntity continuedAlarmEntity = new ContinuedAlarmEntity();
                continuedAlarmEntity.setSplicing(map.getKey().split(","));
                continuedAlarmEntity.setAlarmNumber(map.getValue().size());
                List<ContinuedAlarmDetail> details = map.getValue().stream().map(x -> new ContinuedAlarmDetail().setAlarmTime(DateFormatUtils.format(x.getAlarmTime(), "yyyy-MM-dd HH:mm:ss")).setRecoveryTime(x.getRecoveryTime() == null ? "" : DateFormatUtils.format(x.getRecoveryTime(), "yyyy-MM-dd HH:mm:ss")).setTimeLen(x.getContinuousHour())).collect(Collectors.toList());

                //按照报警时间倒叙排列
                details.sort((o1, o2) -> LocalDateTime.parse(o2.getAlarmTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).compareTo(LocalDateTime.parse(o1.getAlarmTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
                continuedAlarmEntity.setDetails(details);

                returnList.add(continuedAlarmEntity);
            }

            return returnList;
        } catch (Exception e) {
            throw new RuntimeException("24小时持续报警查询发生异常--" + e);
        }
    }

    @Override
    public List<AlarmRecRate> getAlarmResponseRate(String[] unitId, Date startTime, Date endTime) {
        try {
            //时间与页面查询时间同步
            List<DictionaryEntity> dateTimeList = basicDataService.getQueryStartAndEndDate(startTime, endTime);
            endTime = (Date) dateTimeList.get(1).getValue();
        } catch (Exception e) {
            log.error("日期获取失败");
        }

        List<Object[]> alarmResponseRate = craftAlarmRateConfRepository.getAlarmResponseRate(unitId, startTime, endTime, CommonEnum.AlarmResponseAndAlarmHandlingDateTypeEnum.Second);
        return getAlarmRecRate(alarmResponseRate);
    }

    @Override
    public List<AlarmRecRate> getAlarmHandlingRate(String[] unitId, Date startTime, Date endTime) {
        try {
            //时间与页面查询时间同步
            List<DictionaryEntity> dateTimeList = basicDataService.getQueryStartAndEndDate(startTime, endTime);
            endTime = (Date) dateTimeList.get(1).getValue();
        } catch (Exception e) {
            log.error("日期获取失败");
        }

        List<Object[]> alarmResponseRate = craftAlarmRateConfRepository.getAlarmResponseRate(unitId, startTime, endTime, CommonEnum.AlarmResponseAndAlarmHandlingDateTypeEnum.Minute);
        return getAlarmRecRate(alarmResponseRate);
    }

    @Override
    public boolean isFilterWorkTeam(String[] unitId) {
        return unitRepository.isFilterWorkTeam(unitId);
    }

//    @Override
//    public List<AlarmStatsListEntity> getAlarmStatsPortal(Date startTime, Date endTime, Integer companyId, Integer monitorType) throws Exception {
//        try {
//
//            //region 解析时间和装置ID
//            List<DictionaryEntity> dateTimeLists = new ArrayList<>();
//            Map<String, Object> dateTimeMap = basicDataService.getSearchTimeByDate(startTime, endTime);
//            dateTimeMap.entrySet().forEach(x -> {
//                DictionaryEntity dic = new DictionaryEntity();
//                dic.setKey(x.getKey());
//                dic.setValue(x.getValue());
//                dateTimeLists.add(dic);
//            });
//
//            startTime = (Date) (dateTimeLists.get(0).getValue());
//            endTime = (Date) (dateTimeLists.get(1).getValue());
//            String endFlag = String.valueOf(dateTimeLists.get(5).getValue());
//            if (endFlag.equals("<")) {
//                endTime = DateUtils.addSeconds(endTime, -1);
//            }
//
//            List<UnitEntity> unitEntityList = basicDataService.getUnitList(true);
//
//
//            AlarmStatsListEntity craftParaAlarmRate = getAlarmStatsList(unitEntityList, startTime, endTime, companyId, monitorType);
//            List<AlarmStatsListEntity> craftParaAlarmRateList = new ArrayList<>();
//            craftParaAlarmRateList.add(craftParaAlarmRate);
//            return craftParaAlarmRateList;
//        } catch (Exception e) {
//            throw e;
//        }
//    }

//    @Override
//    public XWPFDocument exportAlarmStatsPortal(Date startTime, Date endTime, Integer monitorType) throws Exception {
//
//        List<UnitEntity> unitList = basicDataService.getUnitList(true);
//
//
//        log.info("首页查询时间" + DateHelper.format(startTime) + "   " + DateHelper.format(endTime));
//        Map<String, Object> searchTimeByDate = basicDataService.getSearchTimeByDate(startTime, endTime);
//        startTime = (Date) searchTimeByDate.get("startTime");
//        endTime = (Date) searchTimeByDate.get("endTime");
//        if (searchTimeByDate.get("endFlag").equals("<")) {
//            endTime = DateUtils.addSeconds(endTime, -1);
//        }
//        log.info("首页查询时间2" + DateHelper.format(startTime) + "   " + DateHelper.format(endTime));
//
//
//        Integer companyId = new CommonProperty().getCompanyId();
//        AlarmStatsListEntity alarmStatsPortal = getAlarmStatsList(unitList, startTime, endTime, companyId, monitorType);
//
//
//        // 创建XWPFDocument对象，表示Word文档
//        XWPFDocument document = new XWPFDocument();
//
//        //标题
//        createParagraph(document, "工艺报警情况通报", 22, true, ParagraphAlignment.CENTER);
//
//        //时间
//        String time = "（" + DateFormatUtils.format(startTime, "yyyy-MM-dd HH:mm:ss") + "至" + DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss") + "）";
//        createParagraph(document, time, 10, true, ParagraphAlignment.CENTER);
//
//        //工艺参数报警情况
//        createParagraph(document, "一、工艺参数报警情况", 16, true, ParagraphAlignment.LEFT);
//
//        //时平均报警数1.47，完成（未完成）≤12的指标，
//        String avgAlarmRate = "时平均报警数" + alarmStatsPortal.getAvgAvgAlarmRate();
//        if (alarmStatsPortal.getAvgAvgAlarmRate() <= 12) {
//            avgAlarmRate += "，完成≤12的指标";
//        } else {
//            avgAlarmRate += "，未完成≤12的指标";
//        }
////        createParagraph(document, avgAlarmRate, 12, false, ParagraphAlignment.LEFT);
//
//        //时均报警数后三名装置为气分（5.63）、2#抽提（4.76）和航煤加氢（2.93）；
//        List<PortalAlarmRateEntity> avgAlarmRateList = alarmStatsPortal.getAvgAlarmRateList().stream().sorted(Comparator.comparing(PortalAlarmRateEntity::getAvgAlarmRate).reversed()).collect(Collectors.toList());
//        StringBuilder avgAlarmRateListStr = new StringBuilder("时均报警数后三名装置为");
//        for (int i = 0; i < (Math.min(avgAlarmRateList.size(), 3)); i++) {
//            avgAlarmRateListStr.append(avgAlarmRateList.get(i).getUnitCode()).append("(").append(avgAlarmRateList.get(i).getAvgAlarmRate()).append(")");
//        }
////        createParagraph(document, avgAlarmRateListStr.toString(), 12, false, ParagraphAlignment.LEFT);
//
//
//        //报警响应及时率96.54%，完成（未完成）95%的指标；
//        String alarmTimelyResponseRate = "报警响应及时率" + alarmStatsPortal.getAvgAlarmTimelyResponseRate() + "%";
//        if (alarmStatsPortal.getAvgAlarmTimelyResponseRate() > 95) {
//            alarmTimelyResponseRate += "，完成95%的指标";
//        } else {
//            alarmTimelyResponseRate += "，未完成95%的指标";
//        }
////        createParagraph(document, alarmTimelyResponseRate, 12, false, ParagraphAlignment.LEFT);
//
//
//        //报警处置及时率99.22%，完成（未完成）95%的指标，
//        String alarmTimelyDisposalRate = "报警处置及时率" + alarmStatsPortal.getAvgAlarmTimelyDisposalRate() + "%";
//        if (alarmStatsPortal.getAvgAlarmTimelyDisposalRate() > 95) {
//            alarmTimelyDisposalRate += "，完成95%的指标";
//        } else {
//            alarmTimelyDisposalRate += "，未完成95%的指标";
//        }
////        createParagraph(document, alarmTimelyDisposalRate, 12, false, ParagraphAlignment.LEFT);
//
//
//        //处置及时率后三名装置为为2#PP（92.89）、加裂（95.79）和2#硫磺（96.04）。
//        List<PortalAlarmRateEntity> alarmTimelyDisposalRateList = alarmStatsPortal.getAvgAlarmTimelyDisposalList().stream().sorted(Comparator.comparing(PortalAlarmRateEntity::getAlarmTimelyDisposalRate)).collect(Collectors.toList());
//        StringBuilder alarmTimelyDisposalRateListStr = new StringBuilder("处置及时率后三名装置为");
//        for (int i = 0; i < (Math.min(alarmTimelyDisposalRateList.size(), 3)); i++) {
//            alarmTimelyDisposalRateListStr.append(alarmTimelyDisposalRateList.get(i).getUnitCode()).append("(").append(alarmTimelyDisposalRateList.get(i).getAlarmTimelyDisposalRate()).append(")");
//        }
//        createParagraph(document, avgAlarmRate + avgAlarmRateListStr.toString() + alarmTimelyResponseRate + alarmTimelyDisposalRate + alarmTimelyDisposalRateListStr.toString(), 12, false, ParagraphAlignment.LEFT);
//
//        //二、运行部各装置工艺参数报警率情况
//        createParagraph(document, "二、运行部各装置工艺参数报警率情况", 16, true, ParagraphAlignment.LEFT);
//
//        //各运行部报警次数分布情况 如下
//        createParagraph(document, "各运行部报警次数分布情况如下", 12, false, ParagraphAlignment.LEFT);
//

    ////        Map<String, List<UnitEntity>> collect = unitList.stream().collect(Collectors.groupingBy(UnitEntity::getWorkshopName));
//
//        //准备饼图数据
//        List<AlarmRec> alarmRecs = new ArrayList<>();
//        Map<String, List<AlarmRec>> collect1 = alarmRecs.stream().collect(Collectors.groupingBy(AlarmRec::getUnitCode));
//        Map<String, List<UnitEntity>> collect2 = unitList.stream().collect(Collectors.groupingBy(UnitEntity::getStdCode));
//        HashMap<String, Integer> chartData = new HashMap<>();
//        for (Map.Entry<String, List<AlarmRec>> map : collect1.entrySet()) {
//            List<UnitEntity> unitEntities = collect2.get(map.getKey());
//            if (chartData.containsKey(unitEntities.get(0).getWorkshopName())) {
//                Integer i = chartData.get(unitEntities.get(0).getWorkshopName());
//                chartData.put(unitEntities.get(0).getWorkshopName(), i + map.getValue().size());
//            } else {
//                chartData.put(unitEntities.get(0).getWorkshopName(), map.getValue().size());
//            }
//        }
//
//        createPieChartData(document, chartData.keySet(), chartData.values());
//
//
//        XWPFTable table = document.createTable(1, 5);
//        table.setTableAlignment(TableRowAlign.LEFT);
//        table.setWidthType(TableWidthType.AUTO);
//        XWPFTableRow row = table.getRow(0);
//        //装置	时平均报警数	报警确认及时率	报警处置及时率	工艺参数报警率
//        rowWithData(row, "C4C4C4", "装置", "时平均报警数", "报警确认及时率", "报警处置及时率", "工艺参数报警率");
//        alarmStatsPortal.getAvgAlarmRateList().stream().sorted(Comparator.comparing(PortalAlarmRateEntity::getCraftParaAlarmRate)).forEach(x -> {
//            XWPFTableRow row1 = table.createRow();
//            rowWithData(row1, null, x.getUnitCode(), x.getAvgAlarmRate() + "", x.getAlarmTimelyResponseRate() + "", x.getAlarmTimelyDisposalRate() + "", x.getCraftParaAlarmRate() + "");
//        });
//
//        //三、主要超标参数分析
//        createParagraph(document, "三、主要超标参数分析", 16, true, ParagraphAlignment.LEFT);
//
//
//        //公司前十个限制性报警参数分析
//        createParagraph(document, "公司前十个限制性报警参数分析", 12, false, ParagraphAlignment.CENTER);
//        //限制性报警
//        Map<String, List<AlarmRec>> collect3 = alarmRecs.stream().filter(x -> x.getAlarmFlagId().equals(1L) || x.getAlarmFlagId().equals(4L)).collect(Collectors.groupingBy(AlarmRec::getTag));
//        //按照数量倒叙排列
//        List<Map.Entry<String, List<AlarmRec>>> collect4 = collect3.entrySet().stream().sorted(Comparator.comparing(x -> x.getValue().size(), Comparator.reverseOrder())).collect(Collectors.toList());
//        //装置	生产单元	参数描述	位号	报警次数
//        XWPFTable table1 = document.createTable(1, 6);
//        table1.setTableAlignment(TableRowAlign.LEFT);
//        table1.setWidthType(TableWidthType.AUTO);
//        XWPFTableRow row2 = table1.getRow(0);
//        rowWithData(row2, "C4C4C4", "序号", "装置", "生产单元", "参数描述", "位号", "报警次数");
//        int num = 0;
//        for (int i = 0; i < Math.min(collect4.size(), 10); i++) {
//            Map.Entry<String, List<AlarmRec>> map = collect4.get(i);
//            List<AlarmRec> value = map.getValue();
//            XWPFTableRow row3 = table1.createRow();
//            rowWithData(row3, null, String.valueOf(++num), collect2.get(value.get(0).getUnitCode()).get(0).getSname(), value.get(0).getPrdtCell().getSname(), value.get(0).getDes(), value.get(0).getTag(), value.size() + "");
//        }
//
//        //上周前十个限制性报警参数治理情况
//        createParagraph(document, "上周前十个限制性报警参数治理情况", 12, false, ParagraphAlignment.CENTER);
//
//        String[] unitIds = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
//        //获取上周数据
//        Date oneWeekBeforeStartDate = (Date) searchTimeByDate.get("oneWeekBeforeStartDate");
//        Date oneWeekBeforeEndDate = (Date) searchTimeByDate.get("oneWeekBeforeEndDate");
//        List<AlarmRec> alarmRecByMonitor = alarmRecRepository.getAlarmRecByMonitor(oneWeekBeforeEndDate, oneWeekBeforeStartDate, unitIds, monitorType);
//        Map<String, List<AlarmRec>> collect5 = alarmRecByMonitor.stream().collect(Collectors.groupingBy(AlarmRec::getTag));
//        List<Map.Entry<String, List<AlarmRec>>> collect6 = collect5.entrySet().stream().sorted(Comparator.comparing(x -> x.getValue().size(), Comparator.reverseOrder())).collect(Collectors.toList());
//
//        //序号	装置	生产单元	参数描述	位号	上周报警次数	本周报警次数
//        XWPFTable table2 = document.createTable(1, 7);
//        table2.setTableAlignment(TableRowAlign.LEFT);
//        table2.setWidthType(TableWidthType.AUTO);
//        XWPFTableRow row4 = table2.getRow(0);
//        rowWithData(row4, "C4C4C4", "序号", "装置", "生产单元", "参数描述", "位号", "上周报警次数", "本周报警次数");
//        if (!collect6.isEmpty()) {
//            int num1 = 0;
//            for (int i = 0; i < Math.min(collect6.size(), 10); i++) {
//                Map.Entry<String, List<AlarmRec>> map = collect6.get(i);
//                List<AlarmRec> value = map.getValue();
//                XWPFTableRow row5 = table2.createRow();
//                rowWithData(row5, null, String.valueOf(++num1), collect2.get(value.get(0).getUnitCode()).get(0).getSname(), value.get(0).getPrdtCell().getSname(), value.get(0).getDes(), value.get(0).getTag(), value.size() + "", (collect3.get(map.getKey()) != null ? collect3.get(map.getKey()).size() : 0) + "");
//            }
//        }
//
//
//        //四、持续报警
//        createParagraph(document, "四、持续报警", 16, true, ParagraphAlignment.LEFT);
//
//
//        //二十四小时持续报警
//        Date finalEndTime = endTime;
//        List<AlarmRec> recs = alarmRecs.stream().filter(x -> (x.getRecoveryTime() == null ? finalEndTime : x.getRecoveryTime()).getTime() - x.getAlarmTime().getTime() > 1000 * 60 * 60 * 24).collect(Collectors.toList());
//        //提示性报警
//        List<AlarmRec> collect7 = recs.stream().filter(x -> x.getAlarmFlagId().equals(2L) || x.getAlarmFlagId().equals(3L)).collect(Collectors.toList());
//        //限制性报警
//        List<AlarmRec> collect8 = recs.stream().filter(x -> x.getAlarmFlagId().equals(1L) || x.getAlarmFlagId().equals(4L)).collect(Collectors.toList());
//
//        //本期超24小时持续报警0条，其中提示性（高低）0条，限制性高高低低0条。
//        createParagraph(document, "本期超24小时持续报警" + recs.size() + "条，其中提示性（高低）" + collect7.size() + "条，限制性高高低低" + collect8.size() + "条。", 12, false, ParagraphAlignment.LEFT);
//        if (!recs.isEmpty()) {
//            //序号 装置 DCS位号 报警描述	报警标识	报警时间	恢复时间	超标情况说明
//            XWPFTable table3 = document.createTable(1, 8);
//            table3.setTableAlignment(TableRowAlign.LEFT);
//            table3.setWidthType(TableWidthType.AUTO);
//            XWPFTableRow row6 = table3.getRow(0);
//            rowWithData(row6, "C4C4C4", "序号", "装置", "DCS位号", "报警描述", "报警标识", "报警时间", "恢复时间", "超标情况说明");
//            int num2 = 0;
//            for (AlarmRec rec : recs) {
//                XWPFTableRow row7 = table3.createRow();
//                rowWithData(row7, null, String.valueOf(++num2), collect2.get(rec.getUnitCode()).get(0).getSname(), rec.getTag(), rec.getDes(), CommonEnum.AlarmFlagEnum.getName(rec.getAlarmFlagId().intValue()), DateFormatUtils.format(rec.getAlarmTime(), "yyyy-MM-dd HH:mm:ss"), rec.getRecoveryTime() == null ? "" : DateFormatUtils.format(rec.getRecoveryTime(), "yyyy-MM-dd HH:mm:ss"), "");
//            }
//        }
//
//        log.info("首页导出成功");
//        return document;
//    }
    @Override
    public List<ContinuedAlarmDetailExportEntity> getAlarmAmountDetail(String[] unitCodes, Date startTime, Date endTime) throws Exception {
        List<UnitEntity> unitEntityList = basicDataService.getUnitListByIds(unitCodes, true);
        List<String> unitCodeList = unitEntityList.stream().map(UnitEntity::getStdCode).distinct().collect(Collectors.toList());
        AlarmRecInfoParamVO paramVO = new AlarmRecInfoParamVO();
        paramVO.setStartTime(startTime);
        paramVO.setEndTime(endTime);
        paramVO.setUnitCodes(unitCodeList);
        List<AlarmRecInfoVO> alarmRecs = alarmRecDAO.selectInfoList(paramVO);
        List<ContinuedAlarmDetailExportEntity> continuedAlarmEntities = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(alarmRecs)) {
            List<AlarmRecInfoVO> recs = alarmRecs.stream().filter(x -> (x.getRecoveryTime() == null ? endTime : x.getRecoveryTime()).getTime() - x.getAlarmTime().getTime() > 1000 * 60 * 60 * 24).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(recs)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                for (AlarmRecInfoVO rec : recs) {
                    ContinuedAlarmDetailExportEntity continuedAlarmEntity = new ContinuedAlarmDetailExportEntity();
                    continuedAlarmEntity.setUnitName(rec.getUnitName());
                    continuedAlarmEntity.setPrdtCellName(rec.getPrdtcellName());
                    continuedAlarmEntity.setAlarmPointTag(rec.getTag());
                    continuedAlarmEntity.setDes(rec.getDes());
                    continuedAlarmEntity.setAlarmFlagName(rec.getAlarmflagName());
                    continuedAlarmEntity.setMeasUnitName(rec.getMeasunitName() + "( " + rec.getMeasunitSign() + ")");
                    Integer priority = rec.getPriority();
                    continuedAlarmEntity.setPriority(priority == null ? "" : CommonEnum.AlarmPriorityEnum.getName(priority));
                    continuedAlarmEntity.setAlarmTime(sdf.format(rec.getAlarmTime()));
                    continuedAlarmEntity.setRecoveryTime(rec.getRecoveryTime() == null ? null : sdf.format(rec.getRecoveryTime()));
                    continuedAlarmEntity.setResponseTime(rec.getResponseTime() == null ? null : sdf.format(rec.getResponseTime()));
                    continuedAlarmEntity.setTimeLen(Duration.between(rec.getAlarmTime().toInstant(), (rec.getRecoveryTime() == null ? new Date() : rec.getRecoveryTime()).toInstant()).toHours() + "");
                    continuedAlarmEntities.add(continuedAlarmEntity);
                }
            }

        }
        return continuedAlarmEntities;
    }

    @Override
    public List<ContinuedAlarmDetailExportEntity> getAlarmTimelyResponseRateDetail(String[] unitCodes, Date startTime, Date endTime) throws Exception {
        List<UnitEntity> unitEntityList = basicDataService.getUnitListByIds(unitCodes, true);
        List<String> unitCodeList = unitEntityList.stream().map(UnitEntity::getStdCode).distinct().collect(Collectors.toList());
        AlarmRecInfoParamVO paramVO = new AlarmRecInfoParamVO();
        paramVO.setStartTime(startTime);
        paramVO.setEndTime(endTime);
        paramVO.setUnitCodes(unitCodeList);
        List<AlarmRecInfoVO> alarmRecs = alarmRecDAO.selectInfoList(paramVO);
        List<ContinuedAlarmDetailExportEntity> continuedAlarmEntities = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(alarmRecs)) {
            List<AlarmRecInfoVO> recs = alarmRecs.stream().filter(x -> (x.getResponseTime() == null ? endTime : x.getResponseTime()).getTime() - x.getAlarmTime().getTime() > 1000 * 30).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(recs)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                for (AlarmRecInfoVO rec : recs) {
                    ContinuedAlarmDetailExportEntity continuedAlarmEntity = new ContinuedAlarmDetailExportEntity();
                    continuedAlarmEntity.setUnitName(rec.getUnitName());
                    continuedAlarmEntity.setPrdtCellName(rec.getPrdtcellName());
                    continuedAlarmEntity.setAlarmPointTag(rec.getTag());
                    continuedAlarmEntity.setDes(rec.getDes());
                    continuedAlarmEntity.setAlarmFlagName(rec.getAlarmflagName());
                    continuedAlarmEntity.setMeasUnitName(rec.getMeasunitName() + "( " + rec.getMeasunitSign() + ")");
                    Integer priority = rec.getPriority();
                    continuedAlarmEntity.setPriority(priority == null ? "" : CommonEnum.AlarmPriorityEnum.getName(priority));
                    continuedAlarmEntity.setAlarmTime(sdf.format(rec.getAlarmTime()));
                    continuedAlarmEntity.setRecoveryTime(rec.getRecoveryTime() == null ? null : sdf.format(rec.getRecoveryTime()));
                    continuedAlarmEntity.setResponseTime(rec.getResponseTime() == null ? null : sdf.format(rec.getResponseTime()));
                    continuedAlarmEntity.setTimeLen(Duration.between(rec.getAlarmTime().toInstant(), (rec.getResponseTime() == null ? new Date() : rec.getResponseTime()).toInstant()).getSeconds() + "");
                    continuedAlarmEntities.add(continuedAlarmEntity);
                }
            }

        }
        return continuedAlarmEntities;
    }

    @Override
    public List<ContinuedAlarmDetailExportEntity> getAlarmTimelyDisposalRateDetail(String[] unitCodes, Date startTime, Date endTime) throws Exception {
        List<UnitEntity> unitEntityList = basicDataService.getUnitListByIds(unitCodes, true);
        List<String> unitCodeList = unitEntityList.stream().map(UnitEntity::getStdCode).distinct().collect(Collectors.toList());
        AlarmRecInfoParamVO paramVO = new AlarmRecInfoParamVO();
        paramVO.setStartTime(startTime);
        paramVO.setEndTime(endTime);
        paramVO.setUnitCodes(unitCodeList);
        List<AlarmRecInfoVO> alarmRecs = alarmRecDAO.selectInfoList(paramVO);
        List<ContinuedAlarmDetailExportEntity> continuedAlarmEntities = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(alarmRecs)) {
            List<AlarmRecInfoVO> recs = alarmRecs.stream().filter(x -> (x.getRecoveryTime() == null ? endTime : x.getRecoveryTime()).getTime() - x.getAlarmTime().getTime() > 1000 * 60 * 30).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(recs)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                for (AlarmRecInfoVO rec : recs) {
                    ContinuedAlarmDetailExportEntity continuedAlarmEntity = new ContinuedAlarmDetailExportEntity();
                    continuedAlarmEntity.setUnitName(rec.getUnitName());
                    continuedAlarmEntity.setPrdtCellName(rec.getPrdtcellName());
                    continuedAlarmEntity.setAlarmPointTag(rec.getTag());
                    continuedAlarmEntity.setDes(rec.getDes());
                    continuedAlarmEntity.setAlarmFlagName(rec.getAlarmflagName());
                    continuedAlarmEntity.setMeasUnitName(rec.getMeasunitName() + "( " + rec.getMeasunitSign() + ")");
                    Integer priority = rec.getPriority();
                    continuedAlarmEntity.setPriority(priority == null ? "" : CommonEnum.AlarmPriorityEnum.getName(priority));
                    continuedAlarmEntity.setAlarmTime(sdf.format(rec.getAlarmTime()));
                    continuedAlarmEntity.setRecoveryTime(rec.getRecoveryTime() == null ? null : sdf.format(rec.getRecoveryTime()));
                    continuedAlarmEntity.setResponseTime(rec.getResponseTime() == null ? null : sdf.format(rec.getResponseTime()));
                    continuedAlarmEntity.setTimeLen(Duration.between(rec.getAlarmTime().toInstant(), (rec.getRecoveryTime() == null ? new Date() : rec.getRecoveryTime()).toInstant()).toMinutes() + "");
                    continuedAlarmEntities.add(continuedAlarmEntity);
                }
            }

        }
        return continuedAlarmEntities;
    }

//    /**
//     * 填充表格指定行的列
//     *
//     * @param row    行
//     * @param rgbStr 颜色
//     * @param values 数据
//     */
//    private void rowWithData(XWPFTableRow row, String rgbStr, String... values) {
//        for (int i = 0; i < values.length; i++) {
//            XWPFTableCell cell = row.getCell(i);
//            if (StringUtils.isNotEmpty(rgbStr)) {
//                cell.setColor(rgbStr);
//            }
//            // 设置单元格文字水平居中
//            cell.getParagraphs().get(0).setAlignment(ParagraphAlignment.CENTER);
//
//            // 设置单元格文字垂直居中
//            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
//
//            cell.setText(values[i]);
//        }
//    }

//    /**
//     * 创建段落
//     *
//     * @param document
//     * @param text               文本
//     * @param fontSize           字体大小
//     * @param bold               是否加粗
//     * @param paragraphAlignment 段落对齐方式
//     */
//    private void createParagraph(XWPFDocument document, String text, int fontSize, boolean bold, ParagraphAlignment paragraphAlignment) {
//        XWPFParagraph paragraph = document.createParagraph();
//        paragraph.setSpacingBeforeLines(130);
//        paragraph.setSpacingAfterLines(130);
//        paragraph.setAlignment(paragraphAlignment);
//        XWPFRun run = paragraph.createRun();
//        run.setFontSize(fontSize);
//        run.setText(text);
//        run.setBold(bold);
//    }

    /**
     * 创建饼图
     */
//    private void createPieChartData(XWPFDocument document, Set<String> categories, Collection<Integer> values) throws IOException, InvalidFormatException {
//
//        XWPFChart chart = document.createChart(9 * Units.EMU_PER_CENTIMETER, 8 * Units.EMU_PER_CENTIMETER);
//        // 标题
//        chart.setTitleText("工艺报警次数分布");
//        // 标题是否覆盖图表
//        chart.setTitleOverlay(false);
//        chart.deleteLegend();
//        // 图例位置
////        XDDFChartLegend legend = chart.getOrAddLegend();
////        legend.setPosition(LegendPosition.RIGHT);
//
//        // 分类数据源
//        XDDFCategoryDataSource categoryDS = XDDFDataSourcesFactory.fromArray(categories.toArray(new String[0]));
//        // 值数据源
//        XDDFNumericalDataSource<Number> valuesDS = XDDFDataSourcesFactory.fromArray(values.toArray(new Integer[0]));
//
//        XDDFChartData data = chart.createData(ChartTypes.PIE, null, null);
//        // 设置为可变颜色
//        data.setVaryColors(true);
//        // 绑定数据源到图表
//        XDDFChartData.Series series = data.addSeries(categoryDS, valuesDS);
//
//        series.setShowLeaderLines(true);
//        // 隐藏系列名称
//        XDDFPieChartData.Series s = (XDDFPieChartData.Series) series;
//
//
//        CTPieSer ctPieSer = s.getCTPieSer();
//        if (ctPieSer.getDLbls().isSetShowSerName()) {
//            ctPieSer.getDLbls().getShowSerName().setVal(false);
//        } else {
//            ctPieSer.getDLbls().addNewShowSerName().setVal(false);
//        }
//
//        // 绘制
//        chart.plot(data);
//    }
//

    /**
     * 用于报警处置及时率报警响应及时率类型转换
     *
     * @param alarmResponseRate obj数组
     * @return AlarmRecRate实体
     */
    public List<AlarmRecRate> getAlarmRecRate(List<Object[]> alarmResponseRate) {
        ArrayList<AlarmRecRate> alarmRecRates = new ArrayList<>();

        for (Object[] o : alarmResponseRate) {
            AlarmRecRate alarmRecRate = new AlarmRecRate();
            alarmRecRate.setPrdtcellName(ObjectUtil.objectToString(o[0])).setTag(ObjectUtil.objectToString(o[1])).setDes(ObjectUtil.objectToString(o[2])).setFlagName(ObjectUtil.objectToString(o[3])).setPriority(ObjectUtil.objectToInteger(o[4])).setMeasUnitName(ObjectUtil.objectToString(o[5])).setAlarmTime(ObjectUtil.objectToDate(o[6])).setResponseTime(ObjectUtil.objectToDate(o[7])).setTimeDifferenceValue(ObjectUtil.objectToInteger(o[8]));
            alarmRecRates.add(alarmRecRate);
        }

        Map<String, List<AlarmRecRate>> collect = alarmRecRates.stream().collect(Collectors.groupingBy(AlarmRecRate::getSplicing));

        ArrayList<AlarmRecRate> returnList = new ArrayList<>();
        for (Map.Entry<String, List<AlarmRecRate>> map : collect.entrySet()) {
            AlarmRecRate alarmRecRate = new AlarmRecRate();
            alarmRecRate.setSplicing(map.getKey().split("/,/"));
            alarmRecRate.setNumber(map.getValue().size());

            //将对象转换为ContinuedAlarmDetail，然后按照报警时间倒叙排列
            List<ContinuedAlarmDetail> details = map.getValue().stream().map(x -> new ContinuedAlarmDetail().setAlarmTime(DateFormatUtils.format(x.getAlarmTime(), "yyyy-MM-dd HH:mm:ss")).setResponseTime(x.getResponseTime() == null ? "" : DateFormatUtils.format(x.getResponseTime(), "yyyy-MM-dd HH:mm:ss")).setTimeLen(x.getTimeDifferenceValue().toString())).sorted((o1, o2) -> LocalDateTime.parse(o2.getAlarmTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).compareTo(LocalDateTime.parse(o1.getAlarmTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))).collect(Collectors.toList());

            alarmRecRate.setDetails(details);
            returnList.add(alarmRecRate);
        }

        //按照数量倒排
        returnList.sort(Comparator.comparing(AlarmRecRate::getNumber, Comparator.reverseOrder()));
        return returnList;
    }


//    public AlarmStatsListEntity getAlarmStatsList(List<UnitEntity> unitEntityList, Date startTime, Date endTime, Integer companyId, Integer monitorType) throws Exception {
//        AlarmStatsListEntity alarmStatsLists = new AlarmStatsListEntity();
//        DecimalFormat df = new DecimalFormat("0.00");
//
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//
//        String[] unitIds = unitEntityList.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
//
//        //获取得分配置信息
//        List<CraftAlarmRateConf> coefficient = craftAlarmRateConfRepository.getAllCraftAlarmRateConf();
//
//
//        //用于取出得分系数
//        Map<Integer, CraftAlarmRateConf> rateConfMap = coefficient.stream().collect(Collectors.toMap(x -> x.getType().intValue(), x -> x, (existingValue, newValue) -> newValue));
//        //平均报警数得分系数
//        Double averageCoefficient = rateConfMap.get(CommonEnum.CraftAlarmRateConfTypeEnum.AverageCoefficient.getIndex()).getRatio();
//        //24小时持续报警数得分系数
//        Double hourCoefficient = rateConfMap.get(CommonEnum.CraftAlarmRateConfTypeEnum.HourCoefficient.getIndex()).getRatio();
//        //峰值报警数得分系数
//        Double peakCoefficient = rateConfMap.get(CommonEnum.CraftAlarmRateConfTypeEnum.PeakCoefficient.getIndex()).getRatio();
//        //报警响应及时率得分系数
//        Double alarmTimelyResponseRateCoefficient = rateConfMap.get(CommonEnum.CraftAlarmRateConfTypeEnum.AlarmTimelyResponseRateCoefficient.getIndex()).getRatio();
//        //报警处置及时率得分系数
//        Double alarmTimelyDisposalRateCoefficient = rateConfMap.get(CommonEnum.CraftAlarmRateConfTypeEnum.AlarmTimelyDisposalRateCoefficient.getIndex()).getRatio();
//
//        //初始化所有装置数据
//        Map<String, PortalAlarmRateEntity> newMap = initprotalAlarmRateMap(unitEntityList);
//
//
//        //各类型得分
//        Map<Integer, List<CraftAlarmRateConf>> coefficientmap = getCoefficientmap(coefficient);
//
//        //总报警
//        List<AlarmRecInfoVO> alarmRecs = alarmRecRepository.getAlarmRecByMonitor(startTime, endTime, unitIds, monitorType);
//        alarmStatsLists.setAlarmRecs(null);
//        //限制报警
//        List<AlarmRecInfoVO> alarmRecHHLLs = alarmRecs.stream().filter(x -> x.getAlarmFlagId().equals(Long.valueOf(1)) || x.getAlarmFlagId().equals(Long.valueOf(4))).collect(Collectors.toList());
//
//        // RT最频繁的报警位号
//        List<AlarmTagUnitVO> tagUnitVOList = alarmRecRepository.getTagUnitByMonitor(startTime, endTime, unitIds, monitorType);
//        alarmStatsLists.setTagUnitVOList(tagUnitVOList);
////-------------------------------------------
//
//        // 按照装置对数据进行分组
//        Map<String, List<AlarmRec>> unitMaps = alarmRecs.stream().collect(Collectors.groupingBy(AlarmRec::getUnitCode));
//        // 按照优先级对数据进行分组
//        Map<Integer, List<AlarmRec>> priorityMaps = alarmRecs.stream().collect(Collectors.groupingBy(AlarmRec::getPriority));
//        // RT按优先级分布
//        List<CodeNameValue> priorityPreList = new ArrayList<>();
//        for (Map.Entry<Integer, List<AlarmRec>> s : priorityMaps.entrySet()) {
//            CodeNameValue cnv = new CodeNameValue();
//            cnv.setCode(s.getKey());
//            cnv.setName(CommonEnum.AlarmPriorityEnum.getName(s.getKey()));
//            cnv.setValue(Long.valueOf(s.getValue().size()));
//            priorityPreList.add(cnv);
//        }
//        alarmStatsLists.setPriorityPreList(priorityPreList);
//
//
////-------------------------------------------
//
//        // RT按报警时长分布
//        alarmRecs.sort((o1, o2) -> {
//            Long a1 = (o1.getRecoveryTime() != null ? o1.getRecoveryTime() : endTime).getTime() - o1.getAlarmTime().getTime();
//            Long a2 = (o2.getRecoveryTime() != null ? o2.getRecoveryTime() : endTime).getTime() - o2.getAlarmTime().getTime();
//            return a1.compareTo(a2);
//        });
//        Long alarm30 = alarmRecs.stream().filter(o -> ((o.getRecoveryTime() != null ? o.getRecoveryTime() : endTime).getTime() - o.getAlarmTime().getTime()) <= 30).count();
//        Long alarm1800 = alarmRecs.stream().filter(o -> ((o.getRecoveryTime() != null ? o.getRecoveryTime() : endTime).getTime() - o.getAlarmTime().getTime()) <= 1800).count();
//        Long alarm86400 = alarmRecs.stream().filter(o -> ((o.getRecoveryTime() != null ? o.getRecoveryTime() : endTime).getTime() - o.getAlarmTime().getTime()) <= 86400).count();
//        List<CodeNameValue> durTime = new ArrayList<>();
//        durTime.add(new CodeNameValue("0-30s", alarm30));
//        durTime.add(new CodeNameValue("30s-30m", alarm1800 - alarm30));
//        durTime.add(new CodeNameValue("30m-24h", alarm86400 - alarm1800));
//        durTime.add(new CodeNameValue("24h", alarmRecs.size() - alarm86400));
//        alarmStatsLists.setDurTime(durTime);
////-------------------------------------------
//
//        //将开始结束时间转换为LocalDateTime
//        LocalDateTime startLocalDateTime = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
//        LocalDateTime endLocalDateTime = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
//        // RT报警总数
//        LinkedHashMap<LocalDateTime, List<AlarmRecInfoVO>> splitDate = splitDate(startLocalDateTime, endLocalDateTime, alarmRecs);
//        LinkedHashMap<LocalDateTime, List<AlarmRecInfoVO>> splitDateHHLLs = splitDate(startLocalDateTime, endLocalDateTime, alarmRecHHLLs);
//        List<CodeNameValue> splitDateList = new ArrayList<>();
//        List<CodeNameValue> splitDateHLList = new ArrayList<>();
//        for (Map.Entry<LocalDateTime, List<AlarmRecInfoVO>> s : splitDate.entrySet()) {
//            CodeNameValue cnv = new CodeNameValue(s.getKey().toLocalDate().toString().substring(5), Long.valueOf(s.getValue().size()));
//            splitDateList.add(cnv);
//        }
//        for (Map.Entry<LocalDateTime, List<AlarmRecInfoVO>> s : splitDateHHLLs.entrySet()) {
//            CodeNameValue cnvHL = new CodeNameValue(s.getKey().toLocalDate().toString().substring(5), Long.valueOf(s.getValue().size()));
//            splitDateHLList.add(cnvHL);
//        }
//        alarmStatsLists.setSplitDateList(splitDateList);
//        alarmStatsLists.setSplitDateHLList(splitDateHLList);
////-------------------------------------------
//
//        //计算从开始到结束间隔小时数
//        double house = Math.ceil(endTime.getTime() / 1000.0 / 60 / 60 - startTime.getTime() / 1000.0 / 60 / 60);
//        // RT时平均报警数
//        for (Map.Entry<String, List<AlarmRec>> s : unitMaps.entrySet()) {
//            PortalAlarmRateEntity entity = newMap.get(s.getKey());
//            List<AlarmRec> value = s.getValue();
//            entity.setAvgAlarmRate(Double.valueOf(df.format(value.size() / house)));
//
//        }
//        List<PortalAlarmRateEntity> avgAlarmRateList = newMap.values().stream().sorted(Comparator.comparing(PortalAlarmRateEntity::getAvgAlarmRate)).collect(Collectors.toList());
//        alarmStatsLists.setAvgAlarmRateList(avgAlarmRateList);
////-------------------------------------------
//
//        // RT24小时持续报警数
//        for (Map.Entry<String, List<AlarmRec>> s : unitMaps.entrySet()) {
//            PortalAlarmRateEntity entity = newMap.get(s.getKey());
//            //恢复时间为空伏当前查询结束时间->过滤超过24小时的数据
//            List<AlarmRec> recs = s.getValue().stream().filter(x -> (x.getRecoveryTime() == null ? endTime : x.getRecoveryTime()).getTime() - x.getAlarmTime().getTime() > 1000 * 60 * 60 * 24).collect(Collectors.toList());
//            entity.setAlarmAmount((double) recs.size());
//        }
//        List<PortalAlarmRateEntity> avgAlarmAmountList = newMap.values().stream().sorted(Comparator.comparing(PortalAlarmRateEntity::getAlarmAmount)).collect(Collectors.toList());
//        alarmStatsLists.setAvgAlarmAmountList(avgAlarmRateList);
////-------------------------------------------
//
//
//        // RT报警响应及时率
//        for (Map.Entry<String, List<AlarmRec>> s : unitMaps.entrySet()) {
//            PortalAlarmRateEntity entity = newMap.get(s.getKey());
//            //响应时间为空伏当前查询结束时间->过滤小于30秒的数据
//            List<AlarmRec> recs = s.getValue().stream().filter(x -> (x.getResponseTime() == null ? endTime : x.getResponseTime()).getTime() - x.getAlarmTime().getTime() <= 1000 * 30).collect(Collectors.toList());
//            entity.setAlarmTimelyResponseRate(Double.valueOf(df.format(recs.size() * 1.0 / s.getValue().size() * 100)));
//        }
//        List<PortalAlarmRateEntity> avgAlarmTimelyResponseList = newMap.values().stream().sorted(Comparator.comparing(PortalAlarmRateEntity::getAlarmTimelyResponseRate).reversed()).collect(Collectors.toList());
//        alarmStatsLists.setAvgAlarmTimelyResponseList(avgAlarmTimelyResponseList);
////-------------------------------------------
//
//
//        // RT报警处置及时率
//        for (Map.Entry<String, List<AlarmRec>> s : unitMaps.entrySet()) {
//            PortalAlarmRateEntity entity = newMap.get(s.getKey());
//            //恢复时间为空伏当前查询结束时间->过滤超过30分钟的数据
//            List<AlarmRec> recs = s.getValue().stream().filter(x -> (x.getRecoveryTime() == null ? endTime : x.getRecoveryTime()).getTime() - x.getAlarmTime().getTime() <= 1000 * 60 * 30).collect(Collectors.toList());
//            entity.setAlarmTimelyDisposalRate(Double.valueOf(df.format(recs.size() * 1.0 / s.getValue().size() * 100)));
//        }
//        List<PortalAlarmRateEntity> avgAlarmTimelyDisposalList = newMap.values().stream().sorted(Comparator.comparing(PortalAlarmRateEntity::getAlarmTimelyDisposalRate).reversed()).collect(Collectors.toList());
//        alarmStatsLists.setAvgAlarmTimelyDisposalList(avgAlarmTimelyDisposalList);
////-------------------------------------------
//
//        ArrayList<PortalAlarmRateEntity> returnList = new ArrayList<>(newMap.values());
//
//        Double avgCraft = 0D;
//        Double avgAlarmRate = 0D;
//        Double avgAlarmAmount = 0D;
//        // RT计算工艺参数报警率
//        for (PortalAlarmRateEntity entity : returnList) {
//            Double craft = 0d;
//            //时平均报警数
//            craft += getScore(coefficientmap.get(CommonEnum.CraftAlarmRateConfTypeEnum.AverageScore.getIndex()), averageCoefficient, entity.getAvgAlarmRate());
//            //24小时持续报警数
//            craft += getScore(coefficientmap.get(CommonEnum.CraftAlarmRateConfTypeEnum.HourContinuousScore.getIndex()), hourCoefficient, entity.getAlarmAmount());
//            //峰值报警数
//            craft += getScore(coefficientmap.get(CommonEnum.CraftAlarmRateConfTypeEnum.MinutesScore.getIndex()), peakCoefficient, entity.getPeakAlarmRate());
//            //响应及时率
//            craft += getScore(coefficientmap.get(CommonEnum.CraftAlarmRateConfTypeEnum.AlarmTimelyResponseRateScore.getIndex()), alarmTimelyResponseRateCoefficient, entity.getAlarmTimelyResponseRate());
//            //处置及时率
//            craft += getScore(coefficientmap.get(CommonEnum.CraftAlarmRateConfTypeEnum.AlarmTimelyDisposalRateScore.getIndex()), alarmTimelyDisposalRateCoefficient, entity.getAlarmTimelyDisposalRate());
//
//            avgCraft += craft;
//            avgAlarmRate += entity.getAvgAlarmRate();
//            avgAlarmAmount += entity.getAlarmAmount();
//            entity.setCraftParaAlarmRate(craft);
//        }
//        List<PortalAlarmRateEntity> craftParaAlarmRateList = newMap.values().stream().sorted(Comparator.comparing(PortalAlarmRateEntity::getCraftParaAlarmRate)).collect(Collectors.toList());
//        alarmStatsLists.setCraftParaAlarmRateList(craftParaAlarmRateList);
////-------------------------------------------
//
//        if (alarmRecs.size() != 0) {
//            // RT计算所有装置响应及时率
//            List<AlarmRec> avgAlarmTimelyResponseRates = alarmRecs.stream().filter(x -> (x.getResponseTime() == null ? endTime : x.getResponseTime()).getTime() - x.getAlarmTime().getTime() <= 1000 * 30).collect(Collectors.toList());
//            Double avgAlarmTimelyResponseRate = Double.valueOf(df.format(avgAlarmTimelyResponseRates.size() * 1.0 / alarmRecs.size() * 100));
//            alarmStatsLists.setAvgAlarmTimelyResponseRate(avgAlarmTimelyResponseRate);
//
//            // RT计算所有装置处置及时率
//            List<AlarmRec> avgAlarmTimelyDisposalRates = alarmRecs.stream().filter(x -> (x.getRecoveryTime() == null ? endTime : x.getRecoveryTime()).getTime() - x.getAlarmTime().getTime() <= 1000 * 60 * 30).collect(Collectors.toList());
//            Double avgAlarmTimelyDisposalRate = Double.valueOf(df.format(avgAlarmTimelyDisposalRates.size() * 1.0 / alarmRecs.size() * 100));
//            alarmStatsLists.setAvgAlarmTimelyDisposalRate(avgAlarmTimelyDisposalRate);
//        } else {
//            alarmStatsLists.setAvgAlarmTimelyResponseRate(0.00);
//            alarmStatsLists.setAvgAlarmTimelyDisposalRate(0.00);
//        }
//        if (!returnList.isEmpty()) {
//            avgCraft /= returnList.size();
//            avgAlarmRate /= returnList.size();
//            // RT计算时平均报警数
//            alarmStatsLists.setAvgAvgAlarmRate(Double.valueOf(df.format(avgAlarmRate)));
//
//            // RT计算平均工艺参数报警率
//            alarmStatsLists.setAvgCraftParaAlarmRate(Double.valueOf(df.format(avgCraft)));
//        } else {
//            alarmStatsLists.setAvgAvgAlarmRate(0D);
//            alarmStatsLists.setAvgCraftParaAlarmRate(0D);
//        }
//
//
////-------------------------------------------
//
//
//        return alarmStatsLists;
//    }

//    public Map<String, PortalAlarmRateEntity> initprotalAlarmRateMap(List<UnitEntity> unitEntityList) {
//        Map<String, PortalAlarmRateEntity> map = new HashMap<>();
//        for (UnitEntity unit : unitEntityList) {
//            PortalAlarmRateEntity craftParaAlarmRateEntity = new PortalAlarmRateEntity();
//            craftParaAlarmRateEntity.setAlarmAmount(0D);
//            craftParaAlarmRateEntity.setAvgAlarmRate(0d);
//            craftParaAlarmRateEntity.setPeakAlarmRate(0d);
//            craftParaAlarmRateEntity.setUnitCode(unit.getSname());
//            craftParaAlarmRateEntity.setUnitid(unit.getStdCode());
//            craftParaAlarmRateEntity.setAlarmTimelyResponseRate(0d);
//            craftParaAlarmRateEntity.setAlarmTimelyDisposalRate(0d);
//            craftParaAlarmRateEntity.setCraftParaAlarmRate(0d);
//            map.put(unit.getStdCode(), craftParaAlarmRateEntity);
//        }
//        return map;
//    }
}
