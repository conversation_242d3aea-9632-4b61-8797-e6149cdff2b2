package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.AlarmEventTypeCompEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointTagCompEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/*
 * 报警点位号对照业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmEventTypeCompService
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：报警点位号对照业务逻辑层接口
 */
@Service
public interface AlarmPointTagCompService {

	/**
	 * 通过企业ID查询相关报警点位号对照集合
	 * 
	 * <AUTHOR>  2018-03-30
	 * @param comId  企业ID
	 */
	List<AlarmPointTagCompEntity> getAlarmPointTagCompEntitys(Long comId)throws Exception;

	/**
	 * 通过企业ID去重/报警点位号对照
	 *
	 * <AUTHOR>  2018-03-30
	 */
	public List<Long> distinctByComId();
	}
