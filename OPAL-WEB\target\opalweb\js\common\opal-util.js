﻿//解决低版本IE ajax异常
$.support.cors = true;
$.ajaxSetup({ cache: false, crossDomain: true, xhrFields: { withCredentials: true } });

jQuery(document).bind("ajaxSend", function (event, request, settings) {
    var token = OPAL.util.getCookie("token");
    if (token) {
        var headers = settings.headers || {};
        headers["Authorization"] = "Bearer " + token;
        request.setRequestHeader("Authorization", "Bearer " + token);
        settings.headers = headers;
    }
});

//操作报警项目全局变量
var OPAL = {}
/**
 * 后端API路径相关
 */
OPAL.API = {};
/**
 * 表单相关
 */
OPAL.form = {};
/**
 * 常用相关
 */
OPAL.util = {};
/**
 *控件相关
 */
OPAL.ui = {};
/**
 * 图表控件相关
 */
OPAL.ui.chart = {};

/**
 * 初始化表格数据
 *
 * <AUTHOR> 2017-10-18
 * @param tableID  table表格的id
 * @param config      相关配置信息
 * @param queryParams 请求服务器数据时的传参配置
 */
OPAL.ui.initBootstrapTable = function (tableID, config, queryParams) {
    var _config = {
        method: 'get',
        url: '',
        cache: false,
        pagination: true, //启动分页
        pageSize: 10, // 每页显示的记录数
        pageNumber: 1, // 当前第几页
        pageList: [10, 20, 50, 100], // 记录数可选列表
        paginationPreText: '<', //上下翻页
        paginationNextText: '>',
        sidePagination: "server", // 表示服务端请求 后台分页
        //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder  
        //设置为limit可以获取limit, offset, search, sort, order  
        queryParamsType: "undefined",
        queryParams: queryParams,
        contentType: 'application/x-www-form-urlencoded',
        formatNoMatches: function () {
            return "";
        },
        responseHandler: function (res) {
            var item = {
                "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                "total": $.ET.getPageInfo(res)[0]["total"],
                "rows": $.ET.toObjectArr(res)
            };
            return item;
        },
        onLoadSuccess: function () {
            //设置鼠标浮动提示
            var tds = $('#' + tableID).find('tbody tr td');
            $.each(tds, function (i, el) {
                $(this).attr("title", $(this).text())
            })
        }
    };
    $.extend(true, _config, config);
    $('#' + tableID).bootstrapTable(_config);
    if ($().colResizable) {
        $('#' + tableID).colResizable({
            liveDrag: true
        });
    }
}


/**
 * 初始化表格数据（查询时将搜索按钮禁用）
 *
 * <AUTHOR> 2020-05-08
 * @param tableID  table表格的id
 * @param config      相关配置信息
 * @param queryParams 请求服务器数据时的传参配置
 * @param searchId  搜索按钮id
 */
OPAL.ui.initBootstrapTable2 = function (tableID, config, queryParams, searchId) {
    var _config = {
        method: 'get',
        url: '',
        cache: false,
        pagination: true, //启动分页
        pageSize: 10, // 每页显示的记录数
        pageNumber: 1, // 当前第几页
        pageList: [10, 20, 50, 100], // 记录数可选列表
        paginationPreText: '<', //上下翻页
        paginationNextText: '>',
        sidePagination: "server", // 表示服务端请求 后台分页
        //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder
        //设置为limit可以获取limit, offset, search, sort, order
        queryParamsType: "undefined",
        queryParams: queryParams,
        contentType: 'application/x-www-form-urlencoded',
        formatNoMatches: function () {
            return "";
        },
        responseHandler: function (res) {
            var item = {
                "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                "total": $.ET.getPageInfo(res)[0]["total"],
                "rows": $.ET.toObjectArr(res)
            };
            return item;
        },
        onLoadSuccess: function () {
            //设置鼠标浮动提示
            var tds = $('#' + tableID).find('tbody tr td');
            $.each(tds, function (i, el) {
                $(this).attr("title", $(this).text())
            })
            $('#' + searchId).attr('disabled', false);
        }
    };
    $.extend(true, _config, config);
    $('#' + tableID).bootstrapTable(_config);
    if ($().colResizable) {
        $('#' + tableID).colResizable({
            liveDrag: true
        });
    }
}
/**
 * 获取Combobox控件，统一调用
 *
 * <AUTHOR> 2017-09-29
 * @param ctrlId      Combobox 外层所在select控件ID
 * @param url         数据请求地址
 * @param config      相关配置信息
 * @param callback    回调函数
 * @param onChange    值改变函数
 */
OPAL.ui.getCombobox = function (ctrlId, url, config, callback, onChange) {
    var _config = {
        url: url,
        type: "get",
        async: false,
        dataType: "json",
        contentType: "application/x-www-form-urlencoded",
        keyField: "key",
        valueField: "value",
        selectValue: 1,
        selectFirstRecord: false,
        showAll: false,
        mapManyValues: false,//是否一条记录匹配多个隐藏值
        mapManyDataFieldName: 'workTeamIdList',
        data: {},
        isAll: false
    };
    $.extend(true, _config, config);
    $.ajax({
        url: _config.url,
        type: _config.type,
        async: _config.async || false,
        data: _config.data,
        dataType: _config.dataType,
        contentType: _config.contentType,
        timeout: 10000,
        retryLimit: 2,
        tryCount: 0,
        success: function (data) {
            var str = '';
            if (_config.isAll) {
                str += '<option value="" >全部</option>';
            }
            var dataArr = $.ET.toObjectArr(data);
            $.each(dataArr, function (i, el) {
                if (_config.selectFirstRecord == true && i == 0) {
                    if (_config.mapManyValues == true && _config.mapManyDataFieldName != undefined) {
                        _config.selectValue = JSON.parse(el[_config.mapManyDataFieldName]).join(",");
                    }
                    else {
                        _config.selectValue = el[_config.keyField];
                    }
                }
                if (_config.mapManyValues == true && _config.mapManyDataFieldName != undefined) {
                    str += '<option value="' + JSON.parse(el[_config.mapManyDataFieldName]).join(",") + '">' + el[_config.valueField] + '</option>';
                } else {
                    str += '<option value="' + el[_config.keyField] + '">' + el[_config.valueField] + '</option>';
                }
            });
            $('#' + ctrlId).html(str);
            $("#" + ctrlId).val(_config.selectValue);
            if (callback) {
                callback($('#' + ctrlId).val(), dataArr);
            }
            $("#" + ctrlId).change(function () {
                if (onChange != null) {
                    onChange($(this).children('option:selected').val());
                }
            });
            setTimeout(function () {
                $("#" + ctrlId).change();
            }, 0);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (textStatus == 'timeout') {
                this.tryCount++;
                if (this.tryCount <= this.retryLimit) {
                    $.ajax(this);
                    return;
                }
                return;
            } else {
                alert("加载Combobox出现了异常:" + errorThrown);
            }
        }
    });
};
/**
 * 获取Combobox控件，统一调用
 *
 * <AUTHOR> 2017-09-29
 * @param ctrlId      Combobox 外层所在select控件ID
 * @param data        数据集合
 * @param config      相关配置信息
 * @param callback    回调函数
 * @param onChange    值改变函数
 */
OPAL.ui.getComboboxByData = function (ctrlId, data, config, callback, onChange) {
    var _config = {
        keyField: "key",
        valueField: "value",
        selectValue: 1,
        selectFirstRecord: false,
    };
    $.extend(true, _config, config);
    var str = '';
    var dataArr = data;
    $.each(dataArr, function (i, el) {
        if (_config.selectFirstRecord == true && i == 0) {
            _config.selectValue = el[_config.keyField];
        }
        str += '<option value="' + el[_config.keyField] + '">' + el[_config.valueField] + '</option>';
    });
    $('#' + ctrlId).html(str);
    $("#" + ctrlId).val(_config.selectValue);
    if (callback) {
        callback();
    }
    $("#" + ctrlId).change(function () {
        if (onChange != null) {
            onChange($(this).children('option:selected').val());
        }
    });
    setTimeout(function () {
        $("#" + ctrlId).change();
    }, 0);
};
/**
 * 加载树控件Treeselect,统一调用（控件ID必须和valueField保持一致）
 *
 * <AUTHOR> 2017-09-29
 * @param ctrId    要绑定数据的控件ID
 * @param url      数据请求地址
 * @param id       树节点对应ID字段
 * @param parentId 树节点父节点对应字段
 * @param text     树节点显示字段
 * @param config   bootstrap treeview 初始化配置
 * @param async    是否异步
 * @param callback 回调函数
 */
OPAL.ui.getTreeSelect = function (ctrId, url, id, parentId, text, config, async, callback) {
    var _config = {
        data: "",
        highlighSelected: true,
        showIcon: true,
        expandAll: true,
        showCheckbox: false
    };
    $.extend(true, _config, config);
    $.ajax({
        url: url,
        async: async || false,
        data: "",
        dataType: "json",
        timeout: 5000,
        success: function (data) {
            _config.data = OPAL.util.toTreeData($.ET.toObjectArr(data), id, parentId, text);
            $('#' + ctrId).treeview(_config);
            if (callback) {
                callback();
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            alert("加载公共树出现了异常:" + errorThrown);
        }
    });
}
/**
 * 加载树控件Easy UI ComboTree,统一调用（控件ID必须和valueField保持一致）
 *
 * <AUTHOR> 2017-10-12
 * @param ctrId    要绑定数据的控件ID
 * @param url      数据请求地址
 * @param id       树节点对应ID字段
 * @param parentId 树节点父节点对应字段
 * @param text     树节点显示字段
 * @param config   Easy UI ComboTree初始化配置
 * @param async    是否异步
 * @param callback 回调函数
 */
OPAL.ui.getEasyUIComboTreeSelect = function (ctrId, url, id, parentId, text, config, async, callback) {
    var _config = {
        multiple: true,
        cascadeCheck: true,
        onlyLeafCheck: false,
        showParentNodeText: false,
        clickParentClosePanel: false,
        controlType: 'combotreeSelect',
        workShopModel: false,
        enableToolButton: true,
        originalData: [],
        data: {},
        onBeforeSelect: function (node) {
            if (_config.multiple) return false;
            else {
                if (!_config.onlyLeafCheck) return true;
                if (node.children != null)
                    return false;
                else
                    return true;
            }
        }
    };
    $.extend(true, _config, config);
    $.extend($.fn.combo.methods, {
        beforeSetText: function (target, text) {
            var treeView = $(target).combotree('tree');
            var options = $(target).combotree('options');
            if (!options.showParentNodeText) {
                var textArr = text.split($.fn.combo.defaults.separator);
                var nodes = treeView.tree('getChecked', ['checked', 'indeterminate', 'unchecked']);
                for (var i = 0; i < nodes.length; i++) {
                    if (nodes[i].attributes != undefined && nodes[i].attributes['hasChildren'] != undefined && nodes[i].attributes['hasChildren'] == true) {
                        if (textArr.indexOf(nodes[i].text) != -1)
                            textArr.splice(textArr.indexOf(nodes[i].text), 1);
                    }
                }
                if (textArr.indexOf('全部') != -1)
                    textArr.splice(textArr.indexOf('全部'), 1);
                if (textArr.indexOf('全选') != -1)
                    textArr.splice(textArr.indexOf('全选'), 1);
                text = textArr.join($.fn.combo.defaults.separator);
            }
            return text;
        },
        customAfterCheck: function (target, ids) {
            var treeView = $(target).combotree('tree');
            var options = $(target).combotree('options');
            if (options.controlType != 'combotreeSelect') return;
            if (!options.showParentNodeText) {
                for (var i = 0; i < ids.length; i++) {
                    var node = treeView.tree('find', ids[i]);
                    if (node != undefined && node.attributes != undefined && node.attributes['hasChildren'] != undefined && node.attributes['hasChildren'] == true) {
                        var hiddenNode = $("[type=hidden][name='" + target[0].id + "'][value='" + node.id + "']");
                        if (hiddenNode != undefined) hiddenNode.remove();
                    }
                }
            }
        },
        beforeHidePanel: function (target, node) {
            if (_config.clickParentClosePanel) return true;
            if (node.attributes != undefined && node.attributes['hasChildren'] != undefined && node.attributes['hasChildren'] == true) {
                return false;
            }
            return true;
        }
    });
    $.extend($.fn.combotree.methods, {
        /**
         * @param target
         * @param containParent 是否包含选中的父节点值
         * @returns {*}
         */
        getSelectValues: function (target, containParent) {
            if (containParent == undefined || containParent == null)
                containParent = false;
            var result = new Array();
            var treeView = $(target).combotree('tree');
            var nodes = treeView.tree('getChecked', ['checked', 'indeterminate']);
            for (var i = 0; i < nodes.length; i++) {
                if (containParent) {
                    result.push(nodes[i].id);
                } else {
                    if (nodes[i].attributes == undefined) {
                        result.push(nodes[i].id);
                    }
                }
            }
            return result;
        },
        setValue: function (target, value) {
            var tree = $(target).combotree('tree');
            if (value != undefined) {
                var result = new Array();
                if (value instanceof Array) {
                    for (var i = 0; i < value.length; i++) {
                        var node = tree.tree('find', value[i]);
                        if (node != undefined && node != null) {
                            result.push(value[i]);
                        }
                    }
                    $(target).combotree('setValues', result);
                } else {
                    var node = tree.tree('find', value);
                    if (node != undefined && node != null)
                        result.push(value);
                    $(target).combotree('setValues', result);
                }
            }
        },
        /**
         * 切换模式 true:显示车间;false:显示装置
         */
        toggleDisplayModel: function (target, model) {
            var comboTree = $(target);
            var option = comboTree.combotree('options');
            if (model == option.workShopModel) return;

            option.workShopModel = model;
            comboTree.combotree('setValues', []);
            var newData = new Array();
            if (model == true) {
                if (option.originalData == undefined || option.originalData == null) return;
                for (var i = 0; i < option.originalData.length; i++) {
                    //如果是车间显示模式
                    if (option.originalData[i]['type'] != 2) {
                        newData.push(option.originalData[i]);
                    }
                }
                option.data = OPAL.util.toEasyUITreeData(newData, 'id', 'parentId', 'sname');
            }
            else {
                option.data = OPAL.util.toEasyUITreeData(option.originalData, 'id', 'parentId', 'sname');
            }
            comboTree.combotree("loadData", option.data);
            if (model == true) {
                var tree = comboTree.combotree('tree');
                var nodes = tree.tree('getChecked', ['checked', 'indeterminate', 'unchecked']);
                for (var i = 0; i < nodes.length; i++) {
                    if (nodes[i].attributes != undefined && nodes[i].attributes['original'] != undefined && nodes[i].attributes['original']['type'] == 1) {
                        tree.tree('update', {
                            target: nodes[i].target,
                            id: nodes[i].attributes['original']['originalId']
                        });
                    }
                }
            }
        }
    });
    $.ajax({
        url: url,
        async: _config.async || false,
        data: _config.data,
        dataType: "json",
        timeout: 5000,
        retryLimit: 2,
        tryCount: 0,
        success: function (data) {
            _config.data = OPAL.util.toEasyUITreeData($.ET.toObjectArr(data), id, parentId, text);
            _config.originalData = $.ET.toObjectArr(data);
            $("#" + ctrId).combotree(_config);
            $("#" + ctrId).combotree("loadData", _config.data);
            if (callback) {
                callback();
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (textStatus == 'timeout') {
                this.tryCount++;
                if (this.tryCount <= this.retryLimit) {
                    $.ajax(this);
                    return;
                }
                return;
            } else {
                alert("加载Combobox出现了异常:" + errorThrown);
            }
        }
    });
}

OPAL.ui.defaultComboTree = function (ctrId) {
    var _config = {
        multiple: true,
        cascadeCheck: true,
        onlyLeafCheck: false,
        hasDownArrow: true,
        lines: false,
        animate: false,
        mapManyValues: false,//是否一条记录匹配多个隐藏值
        mapManyDataFieldName: 'workTeamCodeList',
        onBeforeSelect: function (node) {
            return true;
        },
        loadFilter: function (data, parent) {
            function forNodes(data, callback) {
                var nodes = [];
                for (var i = 0; i < data.length; i++) {
                    nodes.push(data[i]);
                }
                while (nodes.length) {
                    var node = nodes.shift();
                    if (callback(node) == false) {
                        return;
                    }
                    if (node.children) {
                        for (var i = node.children.length - 1; i >= 0; i--) {
                            nodes.unshift(node.children[i]);
                        }
                    }
                }
            }

            $(document.body).append("<style type=\"text/css\"> .easy-ui-icon-display {display:none; }</style>");
            forNodes(data, function (node) {
                node.iconCls = 'easy-ui-icon-display';
            });
            return data;
        },
        onBeforeCheck: function (node, selected) {
            var treeView = $("#" + ctrId).combotree('tree');
            var roots = treeView.tree('getRoots');
            if (roots.length <= 1) return;
            //1.如果是"全部"节点;
            if (node.id == -1) {
                if (selected) {
                    for (var i = 0; i < roots.length; i++) {
                        if (roots[i].id == -1)
                            continue;
                        treeView.tree('update', {
                            target: roots[i].target,
                            checked: true
                        });
                    }
                } else {
                    for (var i = 0; i < roots.length; i++) {
                        if (roots[i].id == -1)
                            continue;
                        treeView.tree('update', {
                            target: roots[i].target,
                            checked: false
                        });
                    }
                }

            } else {
                //2.1 获取"全部"节点
                var allNode;
                for (var i = 0; i < roots.length; i++) {
                    if (roots[i].id == -1) {
                        allNode = roots[i];
                        break;
                    }
                }
                var needCascade = true;
                //2.2处理级联选择
                for (var i = 0; i < roots.length; i++) {
                    if (roots[i].id == -1)
                        continue;
                    if (roots[i].id == node.id) continue;
                    if (roots[i].checked != selected) {
                        needCascade = false;
                        break;
                    }
                }
                if (needCascade) {
                    //2.3处理级联选中
                    if (selected) {
                        treeView.tree('update', {
                            target: allNode.target,
                            checked: true
                        });
                    } else {
                        treeView.tree('update', {
                            target: allNode.target,
                            checked: false
                        });
                    }
                } else {
                    //2.4处理取消全部选择
                    if (!selected) {
                        treeView.tree('update', {
                            target: allNode.target,
                            checked: false
                        });
                    }
                }
            }
            return true;
        }
    };
    var comboTree = $("#" + ctrId).combotree(_config);
    return comboTree;
}
/**
 * 加载树控件Easy UI ComboMultiple,统一调用（控件ID必须和valueField保持一致）
 *
 * <AUTHOR> 2017-10-12
 * @param ctrId    要绑定数据的控件ID
 * @param url      数据请求地址
 * @param id       树节点对应ID字段
 * @param text     树节点显示字段
 * @param config   Easy UI ComboTree初始化配置
 * @param async    是否异步
 * @param callback 回调函数
 */
OPAL.ui.getComboMultipleSelect = function (ctrId, url, config, async, callback) {
    var _config = {
        url: url,
        type: "get",
        async: true,
        dataType: "json",
        contentType: "application/x-www-form-urlencoded",
        keyField: "key",
        valueField: "value",
        selectValue: 1,
        data: {},
        treeviewConfig: {
            multiple: true,
            cascadeCheck: true,
            onlyLeafCheck: false,
            hasDownArrow: true,
            lines: false,
            animate: false,
            mapManyValues: false,//是否一条记录匹配多个隐藏值
            mapManyDataFieldName: 'workTeamCodeList',
            onBeforeSelect: function (node) {
                if (_config.treeviewConfig.multiple) return false;
                return true;
            },
            loadFilter: function (data, parent) {
                function forNodes(data, callback) {
                    var nodes = [];
                    for (var i = 0; i < data.length; i++) {
                        nodes.push(data[i]);
                    }
                    while (nodes.length) {
                        var node = nodes.shift();
                        if (callback(node) == false) {
                            return;
                        }
                        if (node.children) {
                            for (var i = node.children.length - 1; i >= 0; i--) {
                                nodes.unshift(node.children[i]);
                            }
                        }
                    }
                }

                $(document.body).append("<style type=\"text/css\"> .easy-ui-icon-display {display:none; }</style>");
                forNodes(data, function (node) {
                    node.iconCls = 'easy-ui-icon-display';
                });
                return data;
            },
            onBeforeCheck: function (node, selected) {
                var treeView = $("#" + ctrId).combotree('tree');
                var roots = treeView.tree('getRoots');
                if (roots.length <= 1) return;
                //1.如果是"全部"节点;
                if (node.id == -1) {
                    if (selected) {
                        for (var i = 0; i < roots.length; i++) {
                            if (roots[i].id == -1)
                                continue;
                            treeView.tree('update', {
                                target: roots[i].target,
                                checked: true
                            });
                        }
                    } else {
                        for (var i = 0; i < roots.length; i++) {
                            if (roots[i].id == -1)
                                continue;
                            treeView.tree('update', {
                                target: roots[i].target,
                                checked: false
                            });
                        }
                    }

                } else {
                    //2.1 获取"全部"节点
                    var allNode;
                    for (var i = 0; i < roots.length; i++) {
                        if (roots[i].id == -1) {
                            allNode = roots[i];
                            break;
                        }
                    }
                    var needCascade = true;
                    //2.2处理级联选择
                    for (var i = 0; i < roots.length; i++) {
                        if (roots[i].id == -1)
                            continue;
                        if (roots[i].id == node.id) continue;
                        if (roots[i].checked != selected) {
                            needCascade = false;
                            break;
                        }
                    }
                    if (needCascade) {
                        //2.3处理级联选中
                        if (selected) {
                            treeView.tree('update', {
                                target: allNode.target,
                                checked: true
                            });
                        } else {
                            treeView.tree('update', {
                                target: allNode.target,
                                checked: false
                            });
                        }
                    } else {
                        //2.4处理取消全部选择
                        if (!selected) {
                            treeView.tree('update', {
                                target: allNode.target,
                                checked: false
                            });
                        }
                    }
                }
                return true;
            }
        }
    };
    $.extend(true, _config, config);
    $.extend($.fn.combotree.methods, {
        /**
         * 隐藏节点
         *
         * @param jq
         * @param target
         * @returns {*}
         */
        hide: function (jq, target) {
            return jq.each(function () {
                $(target).hide();
            });
        },
        /**
         * 设置隐藏节点显示
         * @param jq
         * @param target
         * @returns {*}
         */
        unhide: function (jq, target) {
            return jq.each(function () {
                $(target).show();
            });
        },
        /**
         * 选中全部节点
         */
        checkAllNodes: function () {
            var treeView = $("#" + ctrId).combotree('tree');
            $("#" + ctrId).combotree("setValues", []);
            var roots = treeView.tree('getRoots');
            for (var i = 0; i < roots.length; i++) {
                treeView.tree('check', roots[i].target);
            }
        },
        /**
         * 选中第一条记录
         */
        selectFirstRecord: function () {
            var treeView = $("#" + ctrId).combotree('tree');
            $("#" + ctrId).combotree("setValues", []);
            var roots = treeView.tree('getRoots');
            if (roots.length > 0) {
                $("#" + ctrId).combotree("setValue", roots[0].id);
            }

        }
    });
    $.ajax({
        url: _config.url,
        type: _config.type,
        async: _config.async || false,
        data: _config.data,
        dataType: _config.dataType,
        contentType: _config.contentType,
        timeout: 5000,
        retryLimit: 2,
        tryCount: 0,
        success: function (data) {
            var dataArr = $.ET.toObjectArr(data);
            _config.data = OPAL.util.toMultipleSelectData(dataArr, _config.keyField, _config.valueField, _config.treeviewConfig.mapManyDataFieldName);
            var comboTree = $("#" + ctrId).combotree(_config.treeviewConfig);
            comboTree.combotree("loadData", _config.data);
            if (callback) {
                callback();
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (textStatus == 'timeout') {
                this.tryCount++;
                if (this.tryCount <= this.retryLimit) {
                    $.ajax(this);
                    return;
                }
                return;
            } else {
                alert("加载Combobox出现了异常:" + errorThrown);
            }
        }
    });
}
/**
 * 获取Easy UI下拉多选的值
 *
 * <AUTHOR> 2017-10-12
 * @param ctrId 下拉选择树控件ID
 * @param all 是否获取所有选中节点(true:返回所有节点;false:返回选中子级节点)
 */
OPAL.ui.getComboMultipleSelect.getValues = function (ctrId, all) {
    if (all == undefined || all == null)
        all = false;
    var selectValues = $("#" + ctrId).combotree("getValues");
    var mapManyValues = $("#" + ctrId).combotree("options")['mapManyValues'];
    if (mapManyValues == undefined) {
        mapManyValues = false;
    }
    if (all) {
        return selectValues;
    } else {
        selectValues = new Array();
        var treeView = $("#" + ctrId).combotree('tree');
        var checkedNodes = treeView.tree('getChecked', ['checked', 'indeterminate']);
        $.each(checkedNodes, function (i, e) {
            if (e.id != -1 && treeView.tree("isLeaf", e.target) == true) {
                if (mapManyValues && e["attributes"] != undefined) {
                    selectValues = selectValues.concat($.parseJSON(e["attributes"]));
                }
                else {
                    selectValues.push(e.id);
                }
            }
        });
        return selectValues;
    }
}
/***
 * <AUTHOR> 2017-10-27
 * 日期区间和时间类型选择公共方法
 * @param config
 * @param onChange 值改变事件
 */
OPAL.ui.initDateTimePeriodPicker = function (config, onChange) {
    if (config == undefined) config = {};
    OPAL.util.getShowTime(function (data) {
        var startDateStr = OPAL.util.dateFormat(OPAL.util.strToDate(data[0]["value"]), config['format'] == undefined ? "yyyy-MM-dd" : config['format']);
        var endDateStr = OPAL.util.dateFormat(OPAL.util.strToDate(data[1]["value"]), config['format'] == undefined ? "yyyy-MM-dd" : config['format']);
        //如果是日期时间格式,则不需要进行-1天计算
        if (config['format'] != undefined && config['format'] == "yyyy-MM-dd HH:mm:ss")
            endDateStr = OPAL.util.dateFormat(OPAL.util.strToDate(data[2]["value"]), config['format'] == undefined ? "yyyy-MM-dd" : config['format'])
        var _config = {
            startCtrId: "startTime",
            endCtrId: "endTime",
            dateTypeCtrId: "dateType",
            trigger: "click",
            btns: ['clear', 'confirm'],
            format: 'yyyy-MM-dd',
            type: 'date',
            startValue: startDateStr,
            startMax: endDateStr,
            endValue: endDateStr,
            endMax: endDateStr,
            onDatePeriodChange: function () {
                if ($("#" + _config.dateTypeCtrId) == undefined) return;
                var startTime = OPAL.util.strToDate($("#" + _config.startCtrId).val());
                var endTime = OPAL.util.strToDate($("#" + _config.endCtrId).val());
                var timeMiss = (endTime - startTime) / (1000 * 60 * 60 * 24);
                var monthStr = '<option value="month">月</option>';
                var weekStr = '<option value="week">周</option>';
                var dayStr = '<option value="day">天</option>';
                var hourStr = '<option value="hour">小时</option>';
                if (timeMiss >= 0) {
                    if (timeMiss > 90) {
                        $("#" + _config.dateTypeCtrId).empty();
                        $("#" + _config.dateTypeCtrId).html(monthStr);
                    } else if (timeMiss <= 90 && timeMiss > 30) {
                        $("#" + _config.dateTypeCtrId).empty();
                        $("#" + _config.dateTypeCtrId).html(monthStr + weekStr);
                    } else if (timeMiss > 6 && timeMiss <= 30) {
                        $("#" + _config.dateTypeCtrId).empty();
                        $("#" + _config.dateTypeCtrId).html(dayStr + weekStr);
                    } else if (timeMiss <= 6 && timeMiss >= 1) {
                        $("#" + _config.dateTypeCtrId).empty();
                        $("#" + _config.dateTypeCtrId).html(dayStr);
                    } else if (timeMiss == 0) {
                        $("#" + _config.dateTypeCtrId).empty();
                        $("#" + _config.dateTypeCtrId).html(hourStr + dayStr);
                    }
                }
            }
        };
        $.extend(true, _config, config);
        var start = laydate.render({
            elem: '#' + _config.startCtrId,
            type: _config.type,
            trigger: _config.trigger,
            btns: _config.btns,
            format: _config.format,
            max: _config.startMax,
            done: function (value, date) {
                $('#' + _config.startCtrId).val(value);
                _config.onDatePeriodChange();
                if (onChange != null)
                    onChange();
            },
        });
        $("#" + _config.startCtrId).val(_config.startValue);
        var end = laydate.render({
            elem: '#' + _config.endCtrId,
            type: _config.type,
            trigger: _config.trigger,
            btns: _config.btns,
            format: _config.format,
            //value: _config.endValue,
            max: _config.endMax,
            done: function (value, date) {
                $('#' + _config.endCtrId).val(value);
                _config.onDatePeriodChange();
                if (onChange != null)
                    onChange();
            },
        });
        $("#" + _config.endCtrId).val(_config.endValue);
        $("#" + _config.startCtrId).change(function () {
            _config.onDatePeriodChange();
        });
        $("#" + _config.endCtrId).change(function () {
            _config.onDatePeriodChange();
        });
        $("#" + _config.endCtrId).attr('maxDate', endDateStr);
    });
}
/***
 * <AUTHOR> 2017-11-6
 * 日期时间控件公共方法
 * @param config
 * @param onChange 值改变事件
 */
OPAL.ui.initDateTimePicker = function (config, onChange) {
    if (config == undefined) config = {};
    OPAL.util.getShowTime(function (data) {
        var endDateStr = OPAL.util.dateFormat(OPAL.util.strToDate(data[1]["value"]), config['format'] == undefined ? "yyyy-MM-dd" : config['format']);
        //如果是日期时间格式,则不需要进行-1天计算
        if (config['format'] != undefined && config['format'] == "yyyy-MM-dd HH:mm:ss")
            endDateStr = OPAL.util.dateFormat(OPAL.util.strToDate(data[2]["value"]), config['format'] == undefined ? "yyyy-MM-dd" : config['format'])
        var _config = {
            ctrId: 'startTime', //指定元素
            type: 'date',
            btns: ['clear', 'confirm'],
            trigger: 'click',
            format: 'yyyy-MM-dd', //日期格式
            value: endDateStr,
            max: endDateStr, //最大日期
        };
        $.extend(true, _config, config);
        var start = laydate.render({
            elem: '#' + _config.ctrId, //指定元素
            type: _config.type,
            btns: _config.btns,
            trigger: _config.trigger,
            format: _config.format, //日期格式
            value: endDateStr,
            max: endDateStr, //最大日期
            done: function () {
                if (onChange != null)
                    onChange();
            }
        });
    });
};
/**
 * EasyUI 日期区间选择
 * <AUTHOR> 2017-11-29
 * @param config
 * @param callback 值改变后的回调函数
 */
OPAL.ui.initEasyUIDatePeriodPicker = function (config, callback) {
    if (config == undefined) config = {};
    OPAL.util.getShowTime(function (data) {
        var startDateStr = OPAL.util.dateFormat(OPAL.util.strToDate(data[0]["value"]), "yyyy-MM-dd");
        var endDateStr = OPAL.util.dateFormat(OPAL.util.strToDate(data[1]["value"]), "yyyy-MM-dd");
        var endDate1 = OPAL.util.strToDate(data[1]["value"]);
        var _config = {
            startCtrId: "startTime",
            endCtrId: "endTime",
            dateTypeCtrId: "dateType",
            format: 'yyyy-MM-dd',
            onDatePeriodChange: function () {
                if ($("#" + _config.dateTypeCtrId) == undefined) return;
                var startTime = OPAL.util.strToDate($("#" + _config.startCtrId).val());
                var endTime = OPAL.util.strToDate($("#" + _config.endCtrId).val());
                var timeMiss = (endTime - startTime) / (1000 * 60 * 60 * 24);
                var monthStr = '<option value="month">月</option>';
                var weekStr = '<option value="week">周</option>';
                var dayStr = '<option value="day">天</option>';
                var hourStr = '<option value="hour">小时</option>';
                if (timeMiss >= 0) {
                    if (timeMiss > 30) {
                        $("#" + _config.dateTypeCtrId).empty();
                        $("#" + _config.dateTypeCtrId).html(monthStr + weekStr);
                    } else if (timeMiss <= 30 && timeMiss > 7) {
                        $("#" + _config.dateTypeCtrId).empty();
                        $("#" + _config.dateTypeCtrId).html(dayStr + weekStr);
                    } else if (timeMiss <= 7 && timeMiss > 1) {
                        $("#" + _config.dateTypeCtrId).empty();
                        $("#" + _config.dateTypeCtrId).html(dayStr);
                    } else {
                        $("#" + _config.dateTypeCtrId).empty();
                        $("#" + _config.dateTypeCtrId).html(hourStr);
                    }
                }
            }
        };
        $.extend(true, _config, config);
        //1.结束时间-开始时间<=365
        //2.结束时间不能小于开始时间
        $('#' + _config.startCtrId).datebox({
            value: startDateStr,
            onHidePanel: function () {
                if (callback != null)
                    callback();
            }
        }).datebox('calendar').calendar({
            validator: function (date) {
                var endDate = OPAL.util.strToDate($("#" + _config.endCtrId).val());
                var result = endDate - date;
                return result >= 0 && result <= 86400000 * 365 && endDate <= endDate1;
            }, onChange: function (newDate, oldDate) {
                if (oldDate == null) return;
                _config.onDatePeriodChange();
            }
        });
        $('#' + _config.endCtrId).datebox({
            value: endDateStr,
            onHidePanel: function () {
                if (callback != null)
                    callback();
            }
        }).datebox('calendar').calendar({
            validator: function (date) {
                var startDate = OPAL.util.strToDate($("#" + _config.startCtrId).val());
                var result = date - startDate;
                return result >= 0 && result <= 86400000 * 365 && date <= endDate1;
            }, onChange: function (newDate, oldDate) {
                if (oldDate == null) return;
                _config.onDatePeriodChange();
            }
        });

    });
};
/**
 * EasyUI 日期时间区间选择
 * <AUTHOR> 2017-11-29
 * @param config
 * @param callback 值改变后的回调函数
 */
OPAL.ui.initEasyUIDateTimePeriodPicker = function (config, callback) {
    if (config == undefined) config = {};
    OPAL.util.getShowTime(function (data) {
        var startDateStr = OPAL.util.dateFormat(OPAL.util.strToDate(data[0]["value"]), "yyyy-MM-dd HH:mm:ss");
        var endDateStr = OPAL.util.dateFormat(OPAL.util.strToDate(data[2]["value"]), "yyyy-MM-dd HH:mm:ss");
        var endDate1 = OPAL.util.strToDate(data[2]["value"]);
        var _config = {
            startCtrId: "startTime",
            endCtrId: "endTime",
            format: 'yyyy-MM-dd HH:mm:ss',
        };
        $.extend(true, _config, config);
        //1.结束时间-开始时间<=365
        //2.结束时间不能小于开始时间
        var startDateTimeBox = $('#' + _config.startCtrId).datetimebox({
            value: startDateStr,
            onHidePanel: function () {
                if (callback != null)
                    callback();
            }
        });
        startDateTimeBox.datetimebox('calendar').calendar({
            validator: function (date) {
                var endDate = OPAL.util.strToDate(OPAL.util.dateFormat(OPAL.util.strToDate($("#" + _config.endCtrId).val()), "yyyy-MM-dd"));
                var result = endDate - OPAL.util.strToDate(OPAL.util.dateFormat(date, "yyyy-MM-dd"));
                return result >= 0 && result <= 86400000 * 365 && endDate <= endDate1;
            }, onChange: function (newDate, oldDate) {
                if (oldDate == null) return;
                var startDate = OPAL.util.strToDate($("#" + _config.startCtrId).val());
                var endDate = OPAL.util.strToDate($("#" + _config.endCtrId).val());
                if (endDate - startDate <= 0) {
                    $("#" + _config.startCtrId).datetimebox("setValue", OPAL.util.dateFormat(oldDate, "yyyy-MM-dd HH:mm:ss"));
                    layer.msg("结束时间不能小于等于开始时间");
                }
            }
        });

        var endDateTimeBox = $('#' + _config.endCtrId).datetimebox({
            value: endDateStr,
            onHidePanel: function () {
                if (callback != null)
                    callback();
            }
        });
        endDateTimeBox.datetimebox('calendar').calendar({
            validator: function (date) {
                var startDate = OPAL.util.strToDate(OPAL.util.dateFormat(OPAL.util.strToDate($("#" + _config.startCtrId).val()), "yyyy-MM-dd"));
                var result = OPAL.util.strToDate(OPAL.util.dateFormat(date, "yyyy-MM-dd")) - startDate;
                return result >= 0 && result <= 86400000 * 365 && date <= endDate1;
            },
            onChange: function (newDate, oldDate) {
                if (oldDate == null) return;
                var startDate = OPAL.util.strToDate($("#" + _config.startCtrId).val());
                var endDate = OPAL.util.strToDate($("#" + _config.endCtrId).val());
                if (endDate - startDate <= 0) {
                    $("#" + _config.endCtrId).datetimebox("setValue", OPAL.util.dateFormat(oldDate, "yyyy-MM-dd HH:mm:ss"));
                    layer.msg("结束时间不能小于等于开始时间");
                }
            }
        });
    });

};
/***
 * <AUTHOR> 2017-11-29
 * @param config
 *                ctrId: 控件ID
 type: date|datetime,
 format: yyyy-MM-dd|yyyy-MM-dd HH:mm:ss
 * @param callback 值改变回调函数
 */
OPAL.ui.initEasyUIDateTimePicker = function (config, callback) {
    if (config == undefined) config = {};
    OPAL.util.getShowTime(function (data) {
        var endDateStr = OPAL.util.dateFormat(OPAL.util.strToDate(data[1]["value"]), config['format'] == undefined ? "yyyy-MM-dd" : config['format']);
        //如果是日期时间格式,则不需要进行-1天计算
        if (config['type'] != undefined && config['type'] == "datetime")
            endDateStr = OPAL.util.dateFormat(OPAL.util.strToDate(data[2]["value"]), "yyyy-MM-dd HH:mm:ss");
        var _config = {
            ctrId: 'startTime', //指定元素
            type: 'date',
            format: 'yyyy-MM-dd', //日期格式
        };
        $.extend(true, _config, config);
        if (_config.type == "date") {
            $('#' + _config.ctrId).datebox({
                value: endDateStr,
                onHidePanel: function () {
                    if (callback != null)
                        callback();
                }
            });
        } else {
            _config.format = "yyyy-MM-dd HH:mm:ss";
            $('#' + _config.ctrId).datetimebox({
                value: endDateStr
            }).datetimebox('calendar').calendar({
                onChange: function (newDate, oldDate) {
                    if (oldDate == null) return;
                    if (callback != null)
                        callback();
                }
            });
        }
    });
};
/**
 * 初始化Form表单元素数据
 *
 * <AUTHOR> 2017-09-29
 * @param formId     表单ID
 * @param dataArray  数据对象
 */
OPAL.form.setData = function (formId, dataArray) {
    if (dataArray == undefined || dataArray == null) return;
    $("*", document.forms[formId]).each(function (i, e) {
        var element = $("#" + e.id);
        if (element != undefined && element != null) {
            if (e.type == "radio") {
                if (dataArray[e.id] != undefined) {
                    $("#" + formId + "[value=" + dataArray[e.id] + "]").attr('checked', 'checked');
                }
                element.trigger('change');
            } else if (e.type == "checkbox") {
                if (dataArray[e.id] == InUseEnum.Yes) {
                    element.attr('checked', 'checked');
                } else {
                    element.removeAttr('checked');
                }
                element.trigger('change');
            } else {
                if (e.tagName == "SELECT") {
                    element.val(dataArray[e.id]);
                    element.trigger('change');
                }
                //特殊处理EasyUI ComboTree控件
                else if (e.tagName == "INPUT" && e['className'] != '' && e['className'] != undefined && e['className'].indexOf('easyui-combotree') != -1) {
                    setTimeout(function () {
                        $("#" + e.id).combotree('setValue', dataArray[e.id]);
                    }, 0);
                }
                else {
                    element.val(dataArray[e.id]);
                }
            }
        }
    });
};

/**
 * 获取表单要提交的值
 * <AUTHOR> 2018-02-01
 * @param formId  表单ID
 * @param containParentTreeNode 树形下拉多选是否包含父节点
 * @returns {{}}
 */
OPAL.form.getData = function (formId, containParentTreeNode) {
    if (containParentTreeNode == undefined || containParentTreeNode == null) {
        containParentTreeNode = false;
    }
    var result = {};
    var form = $("#" + formId);
    if (form == undefined || form == null) {
        return result;
    }
    $('input', form).each(function () {
        if (this.name != undefined && this.name != '' && $(this).val() != '') {
            result[this.name] = $(this).val();
        }
    });
    $('textarea', form).each(function () {
        if (this.name != undefined && this.name != ''&& $(this).val() != '') {
            result[this.name] = $(this).val();
        }
    });
    $('select', form).each(function () {
        if (this.name != undefined && this.name != ''&& $(this).val() != '') {
            result[this.name] = $(this).val();
        }
    });

    //处理下拉多选
    $('.easyui-combotree', form).each(function () {
        var opts = $(this).combotree("options");
        if (opts.multiple) {
            if (this.id != undefined && this.id != '') {
                result[this.id] = OPAL.ui.getComboMultipleSelect.getValues(this.id, containParentTreeNode);
            }
        }
    });
    //处理班组
    $('[workTeam="true"]', form).each(function () {
        var str = $(this).val();
        var arr = new Array();
        if (str != undefined && str != "") {
            var workTeamIds = str.split(",");
            if (workTeamIds != null && workTeamIds != undefined) {
                for (var i = 0; i < workTeamIds.length; i++) {
                    arr.push(workTeamIds[i]);
                }
                if (this.name != undefined && this.name != '') {
                    result[this.name] = arr;
                }
            }
        }
    });

    //特殊处理CheckBox
    var ckList = form.find("[type='checkbox']");
    if (ckList != undefined && ckList != null && ckList.length != 0) {
        for (var i = 0; i < ckList.length; i++) {
            if ($(ckList[i]).is(':checked')) {
                result[$(ckList[i]).attr('name')] = 1;
            } else {
                result[$(ckList[i]).attr('name')] = 0;
            }
        }
    }

    //处理随机时间
    result['now'] = Math.random();
    return result;
}
/**
 * 获取表单要提交的ET.Collection值
 * <AUTHOR> 2018-02-01
 * @param formId  表单ID
 * @returns {{}}
 */
OPAL.form.getETCollectionData = function (formId) {
    var formData = OPAL.form.getData(formId);
    var arr = new Array();
    arr.push(formData);
    return $.ET.toCollectionJson(arr);
}
/**
 * 表单校验
 *
 * <AUTHOR> 2017-10-18
 * @param formId  form表单的id
 * @param config  相关配置信息
 */
OPAL.form.formValidate = function (formId, config) {
    var _config = {
        errorPlacement: function (error, element) {
            element.closest('.form-group').find('.error-msg').append(error); //显示错误消息提示
        },
        //给未通过验证的元素进行处理
        highlight: function (element) {
            $(element).closest('.form-group').addClass('has-error has-feedback');
        },
        //验证通过的处理
        success: function (label) {
            label.closest('.form-group').removeClass('has-error');
        }
    };
    $.extend(true, _config, config);
    $("#" + formId).validate(_config)
};
/**
 * ajax表单异步提交
 *
 * <AUTHOR> 2017-10-26
 * @param option    参数
 * @param success 提交成功回调函数
 */
OPAL.form.ajaxSubmit = function (option, success) {
    var _option = {
        formId: "",
        url: "",
        dataType: "json",
        async: true,
        type: 'get',
        contentType: 'application/json;charset=utf-8',
    };
    $.extend(true, _option, option);
    $.ajax({
        url: _option.url,
        async: _option.async,
        data: $('#' + _option.formId).serialize(),
        dataType: _option.dataType,
        contentType: _option.contentType,
        type: _option.type,
        success: function (data) {
            if (data['collection'] != undefined) {
                if (success != null && success != undefined) {
                    success($.ET.toObjectArr(data));
                }
            } else {
                layer.msg(data.collection.error.message)
            }
        },
        error: function (data) {
            layer.msg(data.collection.error.message);
        }
    })
};
/**
 * ajax get 提交参数序列化
 *
 * <AUTHOR> 2017-11-8
 * @param dataArray    参数数组[{name:'',value:''},
 *                             {name:'',value:''}]模式
 * @returns id=1&name=1&name=2 格式
 */
OPAL.form.param = function (dataArray) {
    return $.param(dataArray);
};
/**
 * 设置Form Excel导出字段信息
 *
 * @param formId   form表单ID
 * @param dataObj  form表单对象数据
 * <AUTHOR> 2018-04-24
 */
OPAL.form.setExportExcelData = function (formId, dataObj) {
    var str = "";
    $.each(dataObj, function (key, value) {
        if (value == "null" || value == undefined || value == null) {
            value = "";
        }
        str += "<input type=\"hidden\" name='" + key + "' value='" + value + "'>";
    });
    $('#' + formId).html(str);
};
/**
 * <AUTHOR> 2017-10-21
 * 将日期对象转换成指定的字符串格式
 *
 * 举例:
 * new Date().pattern("yyyy-MM-dd hh:mm:ss.S")==> 2006-07-02 08:09:04.423
 * new Date().pattern("yyyy-MM-dd E HH:mm:ss") ==> 2009-03-10 二 20:09:04
 * new Date().pattern("yyyy-MM-dd EE hh:mm:ss") ==> 2009-03-10 周二 08:09:04
 * new Date().pattern("yyyy-MM-dd EEE hh:mm:ss") ==> 2009-03-10 星期二 08:09:04
 * new Date().pattern("yyyy-M-d h:m:s.S") ==> 2006-7-2 8:9:4.18
 * @param date 日期对象
 * @param fmt
 * @returns {*}
 */
OPAL.util.dateFormat = function (date, fmt) {
    if (!date instanceof Date) {
        return "参数需为日期对象!";
    }
    var o = {
        "M+": date.getMonth() + 1,
        "d+": date.getDate(),
        "h+": date.getHours() % 12 == 0 ? 12 : date.getHours() % 12,
        "H+": date.getHours(),
        "m+": date.getMinutes(),
        "s+": date.getSeconds(),
        "q+": Math.floor((date.getMonth() + 3) / 3),
        "S": date.getMilliseconds()
    };
    var week = {
        "0": "/u65e5",
        "1": "/u4e00",
        "2": "/u4e8c",
        "3": "/u4e09",
        "4": "/u56db",
        "5": "/u4e94",
        "6": "/u516d"
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    if (/(E+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, ((RegExp.$1.length > 1) ? (RegExp.$1.length > 2 ? "/u661f/u671f" : "/u5468") : "") + week[date.getDay() + ""]);
    }
    for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        }
    }
    return fmt;
}
/**
 * <AUTHOR> 2017-10-23
 * 字符串转日期对象
 * @param dateStr  日期时间字符串
 * @returns {Date} 日期对象
 */
OPAL.util.strToDate = function (dateStr) {
    if (dateStr == undefined) return dateStr;
    if ($.isNumeric(dateStr)) {
        return new Date(dateStr);
    }
    if (dateStr instanceof Date) return dateStr;
    if (dateStr instanceof Object) return dateStr;
    return new Date(dateStr.replace(/-/g, "/"));
}
/**
 * <AUTHOR> 2017-10-21
 * 获取查询时间
 * @param callback(查询时间)
 */
OPAL.util.getQueryTime = function (callback) {
    $.ajax({
        url: OPAL.API.commUrl + "/getQueryTime",
        dataType: "json",
        timeout: 5000,
        async: false,
        retryLimit: 2,
        tryCount: 0,
        success: function (data) {
            var dataArr = $.ET.toObjectArr(data);
            if (callback != null) {
                callback(dataArr[0]["value"]);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (textStatus == 'timeout') {
                this.tryCount++;
                if (this.tryCount <= this.retryLimit) {
                    $.ajax(this);
                    return;
                }
                return;
            } else {
                return errorThrown;
            }
        }
    });
}
/**
 * <AUTHOR> 2017-10-31
 * 获取查询开始日期和结束日期
 * @param callback(查询时间)
 */
OPAL.util.getSearchTime = function (config, callback) {
    var _config = {
        startTime: '2017-1-1',
        endTime: '2017-1-10'
    }
    $.extend(true, _config, config);
    $.ajax({
        url: OPAL.API.commUrl + "/getSearchTime",
        async: false,
        data: _config,
        dataType: "json",
        timeout: 5000,
        retryLimit: 2,
        tryCount: 0,
        success: function (data) {
            if (callback != null) {
                callback(data);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (textStatus == 'timeout') {
                this.tryCount++;
                if (this.tryCount <= this.retryLimit) {
                    $.ajax(this);
                    return;
                }
                return;
            } else {
                return errorThrown;
            }
        }
    });
}
/**
 * <AUTHOR> 2017-10-31
 * 获取查询开始日期和结束日期
 * @param callback(查询时间)
 */
OPAL.util.getShowTime = function (callback) {
    $.ajax({
        url: OPAL.API.commUrl + "/getShowTime",
        async: false,
        dataType: "json",
        timeout: 5000,
        retryLimit: 2,
        tryCount: 0,
        success: function (data) {
            var dataArr = $.ET.toObjectArr(data);
            if (callback != null) {
                callback(dataArr);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (textStatus == 'timeout') {
                this.tryCount++;
                if (this.tryCount <= this.retryLimit) {
                    $.ajax(this);
                    return;
                }
                return;
            } else {
                return errorThrown;
            }
        }
    });
};
/**
 *获取服务器当前时间
 *
 * <AUTHOR> 2017-12-01
 * @param callback
 */
OPAL.util.getSysDateTime = function (callback) {
    $.ajax({
        url: OPAL.API.commUrl + "/getSysDateTime",
        async: false,
        dataType: "json",
        timeout: 5000,
        retryLimit: 2,
        tryCount: 0,
        success: function (data) {
            var dataArr = $.ET.toObjectArr(data);
            if (callback != null && dataArr[0] != undefined && dataArr[0]["value"] != undefined) {
                callback(dataArr[0]["value"]);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (textStatus == 'timeout') {
                this.tryCount++;
                if (this.tryCount <= this.retryLimit) {
                    $.ajax(this);
                    return;
                }
                return;
            } else {
                return errorThrown;
            }
        }
    });
};
/**
 * 获取用户类型
 *
 * <AUTHOR> 2017-11-30
 * @param callback
 */
OPAL.util.getUserType = function (callback) {
    $.ajax({
        url: OPAL.API.commUrl + "/getSysUser",
        async: false,
        dataType: "json",
        timeout: 5000,
        retryLimit: 2,
        tryCount: 0,
        success: function (data) {
            var dataArr = $.ET.toObjectArr(data);
            if (callback != null) {
                callback(dataArr);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (textStatus == 'timeout') {
                this.tryCount++;
                if (this.tryCount <= this.retryLimit) {
                    $.ajax(this);
                    return;
                }
                return;
            } else {
                return errorThrown;
            }
        }
    });
}
/**
 * 日期计算（原型扩展或重载）
 * <AUTHOR> 2017-10-24
 * @param {strInterval} 日期类型：'y、m、d、h、n、s、w'
 * @param {Number} 数量
 * @type Date
 * @returns 计算后的日期
 */
OPAL.util.extendDate = function () {
    Date.prototype.dateAdd = function (strInterval, Number) {
        var dtTmp = this;
        switch (strInterval) {
            case 's':
                return new Date(Date.parse(dtTmp) + (1000 * Number));
            case 'n':
                return new Date(Date.parse(dtTmp) + (60000 * Number));
            case 'h':
                return new Date(Date.parse(dtTmp) + (3600000 * Number));
            case 'd':
                return new Date(Date.parse(dtTmp) + (86400000 * Number));
            case 'w':
                return new Date(Date.parse(dtTmp) + ((86400000 * 7) * Number));
            case 'q':
                return new Date(dtTmp.getFullYear(), (dtTmp.getMonth()) + Number * 3, dtTmp.getDate(), dtTmp.getHours(), dtTmp.getMinutes(), dtTmp.getSeconds());
            case 'm':
                return new Date(dtTmp.getFullYear(), (dtTmp.getMonth()) + Number, dtTmp.getDate(), dtTmp.getHours(), dtTmp.getMinutes(), dtTmp.getSeconds());
            case 'y':
                return new Date((dtTmp.getFullYear() + Number), dtTmp.getMonth(), dtTmp.getDate(), dtTmp.getHours(), dtTmp.getMinutes(), dtTmp.getSeconds());
        }
    }
}
/**
 * 数字验证,默认最多保留5位小数(正负数)默认最长20位
 *
 * <AUTHOR> 2017-10-10
 * @param value
 * @param config
 * @returns {boolean}
 */
OPAL.util.checkDecimalIsValid = function (value, config) {
    var _config = {
        //长度
        length: 20,
        //小数位数
        precision: 5
    }
    $.extend(true, _config, config);
    var numStr = value;
    if (numStr == "" || numStr == null) {
        return false;
    } else if (isNaN(numStr)) {
        return false;
    } else {
        numStr += "";
        if ((numStr + "").length > 20) {
            return false;
        }
        var floatStr = numStr.split(".");
        if (floatStr.length > 1) {
            if (floatStr[0].length < 1) {
                return false;
            }
            if (floatStr[1].length > 5) {
                return false;
            }
            if (floatStr[1].length < 1) {
                return false;
            }
        }
    }
    return true;
}
/**
 * List数据结构转换成Bootstrap TreeView数据
 *
 * <AUTHOR> 2017-09-29
 * @param dataArray  Array数组
 * @param id         树结构ID字段
 * @param parentId   树结构PID字段
 * @param text       树节点显示名称
 * @returns {Array}  Boootstrap TreeView所需格式数据
 */
OPAL.util.toTreeData = function (dataArray, id, parentId, text) {
    var result = new Array();
    for (var i = 0; i < dataArray.length; i++) {
        //查找所有顶级节点
        if (dataArray[i][parentId] == undefined || dataArray[i][parentId] == null || dataArray[i][parentId] == 0) {
            var node = {};
            node["text"] = dataArray[i][text];
            node["id"] = dataArray[i][id];
            node["state"] = {};
            node["state"]["expanded"] = false;
            var childNodes = findChildNodes(dataArray, dataArray[i][id], id, parentId, text);
            if (childNodes != null && childNodes.length != 0) {
                node["nodes"] = childNodes;
            }
            result.push(node);
        }
    }
    return result;
};
/**
 * List数据结构转换成EasyUI TreeView数据
 *
 * <AUTHOR> 2017-09-29
 * @param dataArray  Array数组
 * @param id         树结构ID字段
 * @param parentId   树结构PID字段
 * @param text       树节点显示名称
 * @returns {Array}  Boootstrap TreeView所需格式数据
 */
OPAL.util.toEasyUITreeData = function (dataArray, id, parentId, text) {
    var result = new Array();
    if (dataArray == undefined || dataArray == null) return result;
    for (var i = 0; i < dataArray.length; i++) {
        //查找所有顶级节点
        if (dataArray[i][parentId] == undefined || dataArray[i][parentId] == null || dataArray[i][parentId] == 0) {
            var node = {};
            node["text"] = dataArray[i][text];
            node["id"] = dataArray[i][id];
            var childNodes = findEasyUITreeChildNodes(dataArray, dataArray[i][id], id, parentId, text);
            if (childNodes != null && childNodes.length != 0) {
                node["children"] = childNodes;
                node["state"] = 'closed';
                node["attributes"] = {
                    'hasChildren': true,
                    'original': dataArray[i]
                };
            }
            else {
                node["attributes"] = {
                    'hasChildren': false,
                    'original': dataArray[i]
                };
            }
            result.push(node);
        }
    }
    return result;
};
/**
 * List数据结构转换成EasyUI 下拉多选数据
 *
 * <AUTHOR> 2017-10-12
 * @param dataArray     Array数组
 * @param id            树结构ID字段
 * @param text          树节点显示名称
 * @param dataListFieldName 值集合节点
 * @returns {Array}  Boootstrap TreeView所需格式数据
 */
OPAL.util.toMultipleSelectData = function (dataArray, id, text, dataListFieldName) {
    var result = new Array();
    for (var i = 0; i < dataArray.length; i++) {
        var node = {};
        node["text"] = dataArray[i][text];
        node["id"] = dataArray[i][id];
        if (dataListFieldName != undefined && dataListFieldName != '')
            node["attributes"] = dataArray[i][dataListFieldName];
        result.push(node);
    }
    return result;
};
/***
 * 通用图表X轴日期时间显示处理;
 *
 * <AUTHOR> 2017-11-10
 * @param config
 * @param callback(xAxisConfig,parameter),xAxisConfig:图表xAxis配置;parameter:按照日期分组格式化等信息
 */
OPAL.ui.chart.getxAxisTimeConfig = function (config, callback) {
    var _config = {
        formId: "formSearch",
        url: OPAL.API.commUrl + "/getChartDateShowPeriod",
        dateTypeId: "dateType",
    }
    $.extend(true, _config, config);
    OPAL.form.ajaxSubmit(_config, function (data) {
        var param = {};
        switch ($("#" + _config.dateTypeId).val()) {
            case "hour":
                param.minInterval = 1000 * 3600;
                param.maxInterval = 1000 * 3600;
                param.min = data[0]['value'];
                param.max = data[1]['value'];
                param.interval = 'hour';
                param.showMaxLabel = true;
                param.formatter = function (value, index) {
                    return moment(value).format("HH:mm");
                }
                param.pattern = "yyyyMMddHH";
                param.dateFormatPattern = "yyyy-MM-dd HH";
                break;
            case "day":
                param.minInterval = 1000 * 3600 * 24;
                param.maxInterval = 1000 * 3600 * 24;
                param.min = data[0]['value'];
                param.max = data[1]['value'];
                param.showMaxLabel = false;
                param.interval = 'day';
                param.formatter = function (value, index) {
                    return moment(value).format("MM-DD");
                }
                param.pattern = "yyyyMMdd";
                param.dateFormatPattern = "yyyy-MM-dd";
                break;
            case "week":
                param.minInterval = 1000 * 3600 * 24 * 7;
                param.maxInterval = 1000 * 3600 * 24 * 7;
                param.max = data[1]['value'];
                param.min = data[0]['value'];
                param.interval = 'week';
                param.showMaxLabel = false;
                param.formatter = function (value, index) {
                    return moment(value).format("MM-DD");
                }
                param.pattern = "yyyyMMdd";
                param.dateFormatPattern = "yyyy-MM-dd";
                break;
            case "month":
                param.minInterval = 1000 * 3600 * 24 * 31;
                param.maxInterval = 1000 * 3600 * 24 * 31;
                param.max = data[1]['value'];
                param.min = data[0]['value'];
                param.interval = 'month';
                param.showMaxLabel = false;
                param.formatter = function (value, index) {
                    return moment(value).format("YYYY-MM");
                }
                param.pattern = "yyyyMM";
                param.dateFormatPattern = "yyyy-MM";
                break;
        }
        ;
        var config = {
            type: 'time',
            minInterval: param.minInterval,
            maxInterval: param.maxInterval,
            min: param.min,
            max: param.max,
            interval: param.interval,
            axisLabel: {
                rotate: 35,
                interval: 0,
                showMaxLabel: param.showMaxLabel,
                textStyle: {
                    color: '#333',
                },
                formatter: param.formatter
            },
            splitLine: { //网格线
                show: true,
                lineStyle: {
                    color: ['#e5e5e5'],
                    type: 'solid'
                }
            }
        };
        if (callback != null) {
            callback(config, param);
        }
    });
};
/**
 * 图表无数据时显示空白图片公共方法
 *
 * @param ctrId 图表ID
 * @returns {*}
 * <AUTHOR> 2017-12-6
 */
OPAL.ui.chart.initEmptyChart = function (ctrId) {
    var option = {
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                axisTick: {
                    alignWithLabel: true
                },
            }
        ],
        yAxis: [
            {
                type: 'value'
            }
        ],
        series: [
            {
                type: 'line',
                markPoint: {
                    silent: true,
                    label: {
                        normal: {
                            show: false
                        }
                    },
                    data: [{
                        symbol: 'image://../../../images/eahrtsport.png',
                        symbolSize: [129, 118],
                        x: '50%',
                        y: '50%',
                    }]
                }
            }
        ]
    };
    var chart = echarts.init(document.getElementById(ctrId));
    chart.setOption(option);
    return chart;
}
/**
 * 校验时间控件选择时间是否合法;
 *
 * @param config
 * @returns {boolean}
 * <AUTHOR> 2017-11-30
 */
OPAL.util.checkDateIsValid = function (config) {
    var _config = {
        startCtrId: "startTime",
        endCtrId: "endTime",
    };
    $.extend(true, _config, config);
    var start = $("#" + _config.startCtrId);
    var end = $("#" + _config.endCtrId);
    var maxDateStr = $("#" + _config.endCtrId).attr("maxDate");

    if (maxDateStr == undefined || maxDateStr == null) {
        layer.msg("无法通过校验,控件未设置最大值属性!");
        return false;
    }
    if (start == undefined || end == undefined || start == null || end == null) {
        layer.msg("日期时间校验控件ID传递不正确");
        return false;
    }
    if (start.val() == "" || end.val() == "" || end.val() == undefined || start.val() == undefined) {
        layer.msg("开始时间和结束时间不能为空");
        return false;
    }
    var startDate = OPAL.util.strToDate(start.val());
    var endDate = OPAL.util.strToDate(end.val());
    var maxDate = OPAL.util.strToDate(maxDateStr);
    if ((maxDate - startDate) < 0) {
        layer.msg("开始时间须小于等于系统时间！");
        return false;
    }
    if ((maxDate - endDate) < 0) {
        layer.msg("结束时间须小于等于系统时间！");
        return false;
    }
    if ((endDate - startDate) < 0) {
        layer.msg("开始时间须小于等于结束时间！");
        return false;
    }
    else if ((endDate - startDate) > 86400000 * 365) {
        layer.msg("查询时间范围不能超过365天");
        return false;
    }
    return true;
}

/**
 * 获取子节点
 *
 * <AUTHOR> 2017-09-29
 * @param dataArray  数据
 * @param idValue    id值
 * @param id         id名称
 * @param parentId   父节点id
 * @param text       名称字段
 * @returns {Array}
 */
function findChildNodes(dataArray, idValue, id, parentId, text) {
    var workshopNodes = new Array();
    for (var k = 0; k < dataArray.length; k++) {
        //1.查找所有子节点
        if (dataArray[k][parentId] == idValue) {
            var node = {};
            node["text"] = dataArray[k][text];
            node["id"] = dataArray[k][id];
            var cNodes = findChildNodes(dataArray, dataArray[k][id], id, parentId, text);
            if (cNodes != null && cNodes.length != 0) {
                node["nodes"] = cNodes;
            }

            node["state"] = {};
            node["state"]["expanded"] = false;
            workshopNodes.push(node);
        }
    }
    return workshopNodes;
}

/**
 * 获取EasyUI Tree子节点
 *
 * <AUTHOR> 2017-10-12
 * @param dataArray  数据
 * @param idValue    id值
 * @param id         id名称
 * @param parentId   父节点id
 * @param text       名称字段
 * @returns {Array}
 */
function findEasyUITreeChildNodes(dataArray, idValue, id, parentId, text) {
    var workshopNodes = new Array();
    for (var k = 0; k < dataArray.length; k++) {
        //1.查找所有子节点
        if (dataArray[k][parentId] == idValue) {
            var node = {};
            node["text"] = dataArray[k][text];
            node["id"] = dataArray[k][id];
            var cNodes = findEasyUITreeChildNodes(dataArray, dataArray[k][id], id, parentId, text);
            if (cNodes != null && cNodes.length != 0) {
                node["children"] = cNodes;
                node["state"] = 'closed';
                node["attributes"] = {
                    'hasChildren': true,
                    'original': dataArray[k]
                };
            } else {
                node["attributes"] = {
                    'hasChildren': false,
                    'original': dataArray[k]
                };
            }
            workshopNodes.push(node);
        }
    }
    return workshopNodes;
}


/**
 * 获取cookie
 *
 * <AUTHOR> 2018-08-17
 * @param pageCode  页面编码
 * @returns 集合
 */
OPAL.util.getCookieByPageCode = function (pageCode) {
    var cookieValue = [];
    $.ajax({
        url: OPAL.API.commUrl + "/getCookie",
        async: false,//
        data: { pageCode: pageCode },
        type: 'get',//PUT DELETE POST
        dataType: "text",
        success: function (result) {
            if (result !== null && result !== undefined && result !== '')
                cookieValue = result.split('#');
        }
    });
    return cookieValue;
}

/*
*获取QueryString参数
 */
OPAL.util.getQueryParam = function (name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
    var r = window.location.search.substr(1).match(reg); //匹配目标参数
    if (r != null) return unescape(r[2]);
    return null; //返回参数值
}

/**
 * 获取指定的Cookie值
 * @param cookieName
 * @returns {*}
 */
OPAL.util.getCookie = function (cookieName) {
    if (document.cookie.length > 0) {
        c_start = document.cookie.indexOf(cookieName + "=")
        if (c_start != -1) {
            c_start = c_start + cookieName.length + 1
            c_end = document.cookie.indexOf(";", c_start)
            if (c_end == -1) c_end = document.cookie.length
            return unescape(document.cookie.substring(c_start, c_end))
        }
    }
    return ""
}

/**
 * 设置Form Excel导出字段信息
 *
 * @param formId   form表单ID
 * @param dataObj  form表单对象数据
 * <AUTHOR> 2018-04-24
 */
OPAL.ui.setExportExcelData = function (formId, dataObj) {
    var str = "";
    $.each(dataObj, function (key, value) {
        if (value == "null" || value == undefined || value == null) {
            value = "";
        }
        str += "<input type=\"hidden\" name='" + key + "' value='" + value + "'>";
    });
    $('#' + formId).html(str);
};