package com.pcitc.opal.af.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 相关性位号配置明细实体类
 * 模块编号：pcitc_opal_bll_class_RelevantTagConfigDtlEntity
 * 作    者：dageng.sun
 * 创建时间：2018-08-02 08:54:20
 * 修改编号：1
 * 描    述：相关性位号配置明细实体类
 */
public class RelevantTagConfigDtlEntity extends BasicEntity {

    /**
     * 相关性位号配置明细ID
     */
    private Long relevantTagConfigDtlId;

    /**
     * 相关性位号配置ID
     */
    private Long relevantTagConfigId;

    /**
     * 报警点ID
     */
    private Long alarmPointId;
    
    /**
     * 位号
     */
    private String tag;
    
    /**
	 * 装置名称简称
	 */
	private String unitSname;
	
	/**
     * 生产单元简称
     */
    private String prdtCellSname;
    
    /**
     * 描述
     */
    private String des;
    
    /**
     * 计量单位名称
     */
    private String measUnitName;

    public Long getRelevantTagConfigDtlId() {
        return relevantTagConfigDtlId;
    }

    public void setRelevantTagConfigDtlId(Long relevantTagConfigDtlId) {
        this.relevantTagConfigDtlId = relevantTagConfigDtlId;
    }

    public Long getRelevantTagConfigId() {
        return relevantTagConfigId;
    }

    public void setRelevantTagConfigId(Long relevantTagConfigId) {
        this.relevantTagConfigId = relevantTagConfigId;
    }

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public String getUnitSname() {
		return unitSname;
	}

	public void setUnitSname(String unitSname) {
		this.unitSname = unitSname;
	}

	public String getPrdtCellSname() {
		return prdtCellSname;
	}

	public void setPrdtCellSname(String prdtCellSname) {
		this.prdtCellSname = prdtCellSname;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}

	public String getMeasUnitName() {
		return measUnitName;
	}

	public void setMeasUnitName(String measUnitName) {
		this.measUnitName = measUnitName;
	}
}

