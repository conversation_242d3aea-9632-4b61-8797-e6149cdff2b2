var detailUrl = OPAL.API.aaUrl + '/alarmNumberAssess/getAlarmNumberDetail';
$(function() {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        /**
         * 初始化
         */
        init: function() {
            /**
             *绑定事件
             */
            this.bindUi();

            //page.logic.setData();
            page.logic.initTable();
        },
        bindUi: function() {
        },
        data: {
            // 设置查询参数
            param: {}
         },
        logic: {
            
            /**
             * 初始化数据
             */
            setData: function(data) {
                page.data.param = data;
                $("#tableDetail").bootstrapTable('refresh', {
                    "url": detailUrl,
                    "pageNumber":1
                });
            },
            initTable: function() {

                OPAL.ui.initBootstrapTable("tableDetail", {
                    pageSize:20,
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1 ) * data.pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'alarmTime',
                        title: '报警时间',
                        width: '150px',
                        align: 'center',
                        formatter: function(value, row, index) {
                            return OPAL.util.dateFormat(OPAL.util.strToDate(value), "yyyy-MM-dd HH:mm:ss");
                        }
                    }, {
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        field: 'prdtCellName',
                        title: '生产单元',
                        width: '120px'
                    }, {
                        field: 'alarmPointTag',
                        title: '位号',
                        width: '100px'

                    }, {
                        title: "级别",
                        field: 'craftRankName',
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "描述",
                        field: 'des',
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "当前值",
                        field: 'nowValue',
                        align: 'right',
                        width: '100px'
                    }]
                }, page.logic.queryParams)
            },
            queryParams: function (p) { // 设置查询参数
                var params = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    now: Math.random()
                };
                return $.extend(page.data.param,params);
            }
        }
    };
    page.init();
    window.page = page;


});
