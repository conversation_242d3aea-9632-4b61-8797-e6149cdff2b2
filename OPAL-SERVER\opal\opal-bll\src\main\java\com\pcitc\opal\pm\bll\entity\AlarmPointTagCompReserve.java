
package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;


/**
 * <p>
 * 报警点位号对照
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-31
 */
public class AlarmPointTagCompReserve  {


    private String StdCode;
    private String UnitName;
    private String ApplySn;
    private String DcsTagCode;
    private String Mtrl;
    private String Item;
    private String Sign;
    private String AlarmIdentification;
    private BigDecimal BeforeIndexValue;
    private BigDecimal AfterIndexValue;
    private Date SubmitTime;
    private Date RlsTime;

    public String getStdCode() {
        return StdCode;
    }

    public void setStdCode(String stdCode) {
        StdCode = stdCode;
    }

    public String getUnitName() {
        return UnitName;
    }

    public void setUnitName(String unitName) {
        UnitName = unitName;
    }

    public String getApplySn() {
        return ApplySn;
    }

    public void setApplySn(String applySn) {
        ApplySn = applySn;
    }

    public String getDcsTagCode() {
        return DcsTagCode;
    }

    public void setDcsTagCode(String dcsTagCode) {
        DcsTagCode = dcsTagCode;
    }

    public String getMtrl() {
        return Mtrl;
    }

    public void setMtrl(String mtrl) {
        Mtrl = mtrl;
    }

    public String getItem() {
        return Item;
    }

    public void setItem(String item) {
        Item = item;
    }

    public String getSign() {
        return Sign;
    }

    public void setSign(String sign) {
        Sign = sign;
    }

    public String getAlarmIdentification() {
        return AlarmIdentification;
    }

    public void setAlarmIdentification(String alarmIdentification) {
        AlarmIdentification = alarmIdentification;
    }

    public BigDecimal getBeforeIndexValue() {
        return BeforeIndexValue;
    }

    public void setBeforeIndexValue(BigDecimal beforeIndexValue) {
        BeforeIndexValue = beforeIndexValue;
    }

    public BigDecimal getAfterIndexValue() {
        return AfterIndexValue;
    }

    public void setAfterIndexValue(BigDecimal afterIndexValue) {
        AfterIndexValue = afterIndexValue;
    }

    public Date getSubmitTime() {
        return SubmitTime;
    }

    public void setSubmitTime(Date submitTime) {
        SubmitTime = submitTime;
    }

    public Date getRlsTime() {
        return RlsTime;
    }

    public void setRlsTime(Date rlsTime) {
        RlsTime = rlsTime;
    }
}
