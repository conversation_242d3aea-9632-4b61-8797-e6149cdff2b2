$(function () {
    var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";   //装置
    var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';   //生产单元
    var searchUrl = OPAL.API.pmUrl + "/alarmPointGroupConfig/getAlarmPonitList";//查询
    var addUrl = OPAL.API.pmUrl + "/alarmPointGroupConfig/group-dtl";//新增分组明细
    window.pageLoadMode = PageLoadMode.None;
    var unitId = '';
    var alarmPointGroupId = '';
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            //绑定事件
            this.bindUI();
            //初始化表格
            page.logic.initTable();
            //默认查询数据
            // page.logic.search();
            //初始化查询装置树
            page.logic.initUnitTree();
        },
        bindUI: function () {
            //查询
            $('#btnSearch').click(function () {
                page.logic.search();
            });
            //关弹层
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            });
            //批量确定
            $('#batchAdd').click(function () {
                page.logic.batchAdd();
            });

        },
        data: {
            param: {}
        },
        logic: {
            /**
             * 初始化编辑数据
             */
             setData: function (data) {
                unitId = data.unitId;
                alarmPointGroupId = data.alarmPointGroupId;
                //初始化生产单元
                page.logic.searchUnitPrdt(unitId);
            },
            /**
             * 初始化表格
             */
            initTable: function () {
                OPAL.ui.initBootstrapTable("table", {
                    pageSize:1000,
                    pageList: [1000],
                    pagination: false,
                    cache: false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellSname',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        width: '150px',
                        align: 'left',
                    }]
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: 1,
                    sortOrder: p.sortOrder,
                    inSendMsg: -1
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 搜索
             */
            search: function () {
                page.data.param = OPAL.form.getData("searchForm");
                page.data.param.unitIds = unitId;
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl
                    // "pageNumber": pageIndex
                });
            },
            getObj: function(arr) {
                return $.ET.toCollectionJson(arr);
            },
            /**
             * 批量新增
             */
            batchAdd: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections');
                $.each(rowsArray, function (i, el) {
                    idsArray.push({alarmPointId: el.alarmPointId,alarmPointGroupId: alarmPointGroupId});
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要配置的报警点！");
                    return;
                }
                let obj = page.logic.getObj(idsArray);
                $.ajax({
                    url: addUrl,
                    async: false,
                    type: "POST",
                    data: JSON.stringify(obj),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                        // parent.alarmPointId = [1,2,3]; // 传给父页面的值
                        page.logic.closeLayer(true);
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            
            /**
             * 初始化装置树
             */
             initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect("unitIds", commonUnitTreeUrl, "id", "parentId", "sname", {
                    multiple: false,
                    onlyLeafCheck: true,
                }, false, function() {});
                $("#unitIds").combotree("getValues");
            },
            // /**
            //  * 生产单元
            //  */
            searchUnitPrdt: function (unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    // $("#prdtCellIds").combotree("checkAllNodes");
                });
            },
        }
    }
    page.init();
    window.page = page;

})