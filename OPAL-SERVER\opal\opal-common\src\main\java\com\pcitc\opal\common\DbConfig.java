package com.pcitc.opal.common;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class DbConfig {
    @Value("${spring.datasource.driverClassName}")
    private String driverClassName;
    private String dataBase;

    public String getDriverClassName() {
        return driverClassName;
    }

    public void setDriverClassName(String driverClassName) {
        this.driverClassName = driverClassName;
    }

    public String getDataBase() {
        if("oracle.jdbc.OracleDriver".equals(this.driverClassName)){
            this.dataBase="oracle";
        }else{
            this.dataBase="mysql";
        }
        return dataBase;
    }

    public void setDataBase(String dataBase) {
        this.dataBase = dataBase;
    }
}
