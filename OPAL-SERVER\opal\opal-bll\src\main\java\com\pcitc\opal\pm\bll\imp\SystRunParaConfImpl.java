package com.pcitc.opal.pm.bll.imp;

import java.text.Collator;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.CommonUtil;
import com.pcitc.opal.pm.bll.SystRunParaConfService;
import com.pcitc.opal.pm.bll.entity.SystRunParaConfEntity;
import com.pcitc.opal.pm.dao.SystRunParaConfRepository;
import com.pcitc.opal.pm.pojo.SystRunParaConf;

import pcitc.imp.common.ettool.utils.ObjectConverter;

/*
 * 系统运行参数配置业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_SystRunParaConfImpl
 * 作       者：kun.zhao
 * 创建时间：2018/01/22
 * 修改编号：1
 * 描       述：系统运行参数配置业务逻辑层实现类
 */
@Service
public class SystRunParaConfImpl implements SystRunParaConfService {

	@Autowired
	private SystRunParaConfRepository systRunParaConfRepository;
	
	/**
	 * 批量更新系统运行参数数据
	 * 
	 * <AUTHOR> 2018-01-22
	 * @param systRunParaConfEntityList 系统运行参数配置实体集合
	 * @return	更新结果提示信息
	 * @throws Exception
	 */
	@Override
	@Transactional
	public CommonResult updateSystRunParaConfInBatch(List<SystRunParaConfEntity> systRunParaConfEntityList)
			throws Exception {
		CommonResult commonResult = new CommonResult();
		commonResult.setMessage("保存成功！");
		//查出数据库中原有数据实体
		Long[] systRunParaConfIds = systRunParaConfEntityList.stream().map(x -> x.getSystRunParaConfId()).distinct().toArray(Long[]::new);
		List<SystRunParaConf> anlySystRunParaConfList = systRunParaConfRepository.getSystRunParaConf(systRunParaConfIds);
		//过滤出修改过的实体并同步到数据库
		for (SystRunParaConfEntity systRunParaConfEntity : systRunParaConfEntityList) {
			if(systRunParaConfEntity.getParaValue() != null)
				systRunParaConfEntity.setParaValue(systRunParaConfEntity.getParaValue().trim());
			SystRunParaConf systRunParaConf = anlySystRunParaConfList.stream().filter(x -> x.getSystRunParaConfId().equals(systRunParaConfEntity.getSystRunParaConfId())).findFirst().orElse(new SystRunParaConf());
			if((systRunParaConfEntity.getParaValue() != null && !systRunParaConfEntity.getParaValue().equals(systRunParaConf.getParaValue())) || 
					(systRunParaConfEntity.getParaValue() == null && systRunParaConf.getParaValue() != null)){
				CommonUtil.objectExchange(systRunParaConfEntity,systRunParaConf);
				commonResult = systRunParaConfRepository.updateSystRunParaConf(systRunParaConf);
				if (commonResult.getIsSuccess() == false)
					throw new Exception(commonResult.getMessage());
			}
			
		}
		return commonResult;
	}

	/**
	 * 加载系统运行参数维护主数据
	 * 
	 * <AUTHOR> 2018-01-22
	 * @param name 名称
	 * @param code 编码
	 * @return	系统运行参数维护实体集合
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<SystRunParaConfEntity> getSystRunParaConf(String name, String code) throws Exception {
		List<SystRunParaConf> listSystRunParaConf = systRunParaConfRepository.getSystRunParaConf(name, code);
        List<SystRunParaConfEntity>  returnList = ObjectConverter.listConverter(listSystRunParaConf, SystRunParaConfEntity.class);
        returnList.sort(Comparator.comparing(SystRunParaConfEntity::getName, Collator.getInstance(Locale.CHINESE)));
        return returnList;
	}

	@Override
	public String getParaValueByCode(String code,Integer companyId) {
		return systRunParaConfRepository.findParaValueByCode(code, companyId);
	}

}
