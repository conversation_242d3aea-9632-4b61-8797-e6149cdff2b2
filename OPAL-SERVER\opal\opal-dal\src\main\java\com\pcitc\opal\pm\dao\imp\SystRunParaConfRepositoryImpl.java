package com.pcitc.opal.pm.dao.imp;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;
import javax.persistence.TypedQuery;

import com.pcitc.opal.common.CommonProperty;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.SystRunParaConfRepositoryCustom;
import com.pcitc.opal.pm.pojo.SystRunParaConf;

/*
 * 系统运行参数配置实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_SystRunParaConfRepositoryImpl
 * 作       者：kun.zhao
 * 创建时间：2018/01/22
 * 修改编号：1
 * 描       述：系统运行参数配置实体的Repository实现
 */
public class SystRunParaConfRepositoryImpl extends BaseRepository<SystRunParaConf, Long>
        implements SystRunParaConfRepositoryCustom {

    /**
     * 更新数据
     *
     * <AUTHOR> 2018-01-22
     * @param systRunParaConf 系统运行参数配置实体
     * @return 更新结果提示信息
     */
    @Override
    @Transactional
    public CommonResult updateSystRunParaConf(SystRunParaConf systRunParaConf) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(systRunParaConf);
            commonResult.setResult(systRunParaConf);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 获取多条数据
     *
     * <AUTHOR> 2018-01-22
     * @param systRunParaConfIds 系统运行参数配置Id数组
     * @return 系统运行参数配置实体集合
     */
    @Override
    public List<SystRunParaConf> getSystRunParaConf(Long[] systRunParaConfIds) {
        try {
            // 查询字符串
            String hql = "from SystRunParaConf t where t.companyId=:companyId ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (systRunParaConfIds.length > 0) {
                hql += " and t.systRunParaConfId in (:systRunParaConfIds)";
                List<Long> systRunParaConfIdsList = Arrays.asList(systRunParaConfIds);
                paramList.put("systRunParaConfIds", systRunParaConfIdsList);
            }
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            TypedQuery<SystRunParaConf> query = getEntityManager().createQuery(hql, SystRunParaConf.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 加载系统运行参数配置主数据
     *
     * <AUTHOR> 2018-01-22
     * @param name 名称
     * @param code 编码
     * @return	系统运行参数配置实体集合
     * @throws Exception
     */
    @Override
    public List<SystRunParaConf> getSystRunParaConf(String name, String code) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from SystRunParaConf t where 1=1 and t.companyId=:companyId");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 名称
            if (!StringUtils.isEmpty(name)) {
                hql.append(" and t.name like :name escape '/' ");
                paramList.put("name", "%" + this.sqlLikeReplace(name) + "%");
            }
            if (!StringUtils.isEmpty(code)) {
                hql.append(" and t.code like :code escape '/' ");
                paramList.put("code", "%" + this.sqlLikeReplace(code) + "%");
            }
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            // 调用基类方法查询返回结果
            TypedQuery<SystRunParaConf> query = getEntityManager().createQuery(hql.toString(), SystRunParaConf.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public String findParaValueByCode(String code,Integer companyId) {
        String sql =("select tps.para_value as v_para_value from T_PM_SystRunParaConf tps where tps.code = :code and tps.company_id=:companyId ");
        Map<String,Object> param = new HashMap<>();
        param.put("code",code);
        param.put("companyId",companyId);
        Query query = getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, param);
        List resultList = query.getResultList();
        if (CollectionUtils.isEmpty(resultList)){
            return "0";
        }
        return resultList.get(0).toString();
    }
}
