package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.DBFactoryEntity;
import com.pcitc.opal.pm.bll.entity.DBUnitEntity;
import com.pcitc.opal.pm.bll.entity.DBWorkshopEntity;
import com.pcitc.opal.pm.pojo.Unit;
import org.springframework.stereotype.Service;

import java.util.List;

/*
 * 装置业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_UnitService
 * 作       者：xuelei.wang
 * 创建时间：2017-12-11
 * 修改编号：1
 * 描       述：装置业务逻辑层接口
 */
@Service
public interface UnitService {

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param unitEntity
	 *            生产单元实体
	 */
	CommonResult add(DBUnitEntity unitEntity) throws Exception;

	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param unitIds
	 *            生产单元ID集合
	 */
	CommonResult delete(Long[] unitIds) throws Exception;

	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param unitEntity
	 */
	CommonResult update(DBUnitEntity unitEntity) throws Exception;

	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param unitId 装置ID
	 * @return 装置实体
	 */
	DBUnitEntity getSingle(Long unitId) throws Exception;

	/**
	 *获取装置分页数据
	 *
	 * @param factoryId   工厂ID
	 * @param workshopId  车间ID
	 * @param name        名称或简称
	 * @param stdCode     编码
	 * @param inUse       是否启用
	 * @param page        分页信息
	 * @return            装置分页数据
	 * <AUTHOR> 2017-12-11
	 */
	PaginationBean<DBUnitEntity> getUnitList(Long factoryId, Long workshopId, String name, String stdCode, Integer inUse, Pagination page) throws Exception;

	/**
	 * 获取工厂列表
	 * @param isAll  是否显示全部选项
	 * @return 工厂实体
	 */
    List<DBFactoryEntity> getFactoryList(boolean isAll) throws Exception;

	/**
	 * 根据工厂ID获取车间列表
	 * @param  factoryId 工厂ID
	 * @param isAll  是否显示全部
	 * @return 车间列表
	 * @throws Exception
	 */
	List<DBWorkshopEntity> getWorkshopListByFactoryId(Long factoryId, boolean isAll) throws Exception;

	Unit getUnitInfoByStdCode(String stdCode);
	CommonResult updateUnitInfoById(Unit unitEntity) throws Exception;
}
