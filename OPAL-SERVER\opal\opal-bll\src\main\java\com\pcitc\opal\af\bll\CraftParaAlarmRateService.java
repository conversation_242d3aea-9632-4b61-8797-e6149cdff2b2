package com.pcitc.opal.af.bll;

import com.pcitc.opal.aa.bll.entity.AlarmLevelAssessDetailEntity;
import com.pcitc.opal.af.bll.entity.*;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public interface CraftParaAlarmRateService {

    /**
     * 获取页面-报警分析-工艺参数报警率  网格列信息
     *
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param workTeamIds
     * @return
     * @throws Exception
     */
    List<CraftParaAlarmRateEntity> getCraftParaAlarmRate(String[] unitIds, List<Integer> monitorType, Date startTime,
                                                         Date endTime, Integer companyId, Long workTeamIds);

    /**
     * 获取页面-报警分析-工艺参数报警率  线形图信息
     *
     * @param unitIds
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    AlarmLevelAssessDetailEntity getCraftParaAlarmRateCurve(String[] unitIds, Date startTime, Date endTime)
            throws Exception;

    /**
     * 获取持续报警
     *
     * @param unitId    装置id
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    List<ContinuedAlarmEntity> GetContinuedAlarm(String[] unitId, Date startTime, Date endTime);

    /**
     * 报警响应及时率
     *
     * @param unitId    装置id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return AlarmRecRate
     */
    List<AlarmRecRate> getAlarmResponseRate(String[] unitId, Date startTime, Date endTime);

    /**
     * 报警处置及时率
     *
     * @param unitId    装置id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return AlarmRecRate
     */
    List<AlarmRecRate> getAlarmHandlingRate(String[] unitId, Date startTime, Date endTime);


    boolean isFilterWorkTeam(String[] unitId);

//    List<AlarmStatsListEntity> getAlarmStatsPortal(Date startTime, Date endTime, Integer companyId, Integer monitorType) throws Exception;

//    XWPFDocument exportAlarmStatsPortal(Date startTime, Date endTime, Integer monitorType) throws Exception;


    List<ContinuedAlarmDetailExportEntity> getAlarmAmountDetail(String[] unitCodes, Date startTime, Date endTime)
            throws Exception;


    List<ContinuedAlarmDetailExportEntity> getAlarmTimelyResponseRateDetail(String[] unitCodes, Date startTime,
                                                                            Date endTime) throws Exception;

    List<ContinuedAlarmDetailExportEntity> getAlarmTimelyDisposalRateDetail(String[] unitCodes, Date startTime,
                                                                            Date endTime) throws Exception;


}
