package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.*;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmPointGroupDtlRepositoryCustom;
import com.pcitc.opal.pm.dao.AlarmPointGroupRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import com.pcitc.opal.pm.pojo.AlarmPointGroup;
import com.pcitc.opal.pm.pojo.AlarmPointGroupDtl;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * AlarmPointGroupDtl实体的Repository实现 报警点分组明细
 * 模块编号：pcitc_opal_dal_class_AlarmPointGroupDtlRepositoryImpl
 * 作    者：guoganxin
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描    述：AlarmPoint实体的Repository实现
 */
public class AlarmPointGroupDtlRepositoryImpl extends BaseRepository<AlarmPointGroupDtl, Long> implements AlarmPointGroupDtlRepositoryCustom {

	@PersistenceContext
	EntityManager em;


	/**
	 * 新增报警点分组明细
	 *
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointEntity
	 *            报警点实体
	 * @return 消息结果类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addAlarmPointGroupDtl(List<AlarmPointGroupDtl> alarmPointEntity) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			alarmPointEntity.forEach(x -> {
				this.getEntityManager().persist(x);
			});
			commonResult.setResult(alarmPointEntity);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		return commonResult;
	}
	/**
	 * 更新报警点分组明细
	 *
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointEntity
	 *            报警点实体
	 * @return 消息结果类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult updateAlarmPointGroupDtl(AlarmPointGroupDtl alarmPointEntity) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			getEntityManager().merge(alarmPointEntity);
			commonResult.setResult(alarmPointEntity);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("更新成功！");
		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}
	/**
	 * 删除报警点分组明细实体
	 *
	 * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointGroupDtlIds
	 *            报警点ID集合
	 * @return 消息结果类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteAlarmPointGroupDtl(List<Long> alarmPointGroupDtlIds) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = " from AlarmPointGroupDtl t " +
					"where t.alarmPointGroupDtlId in (:alarmPointGroupDtlIds)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("alarmPointGroupDtlIds", alarmPointGroupDtlIds);
			TypedQuery<AlarmPointGroupDtl> query = getEntityManager().createQuery(hql, AlarmPointGroupDtl.class);
			this.setParameterList(query, paramList);
			List<AlarmPointGroupDtl> alarmPointList = query.getResultList();
			alarmPointList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}





	/**
	 * 获取单条数据
	 *
	  * <AUTHOR> 2018-04-23
	 * @param alarmPointTag  报警点位号
	 * @return AlarmPoint 报警点实体
	 */
	public AlarmPoint getSingleAlarmPointGroupDtlByTag(String alarmPointTag){
		try {
			StringBuilder hql = new StringBuilder("select ap from AlarmPoint ap ");
			hql.append("left join fetch ap.prdtCell pcs ");
			hql.append("left join fetch ap.alarmPointType apt ");
			hql.append("left join fetch ap.measUnit mu where ap.tag=:tag and ap.companyId=:companyId and pcs.companyId=:companyId ");
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("tag", alarmPointTag);
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			TypedQuery<AlarmPoint> query = getEntityManager().createQuery(hql.toString(), AlarmPoint.class);
			this.setParameterList(query, paramList);
			return query.getResultList().get(0);
		} catch (Exception ex) {
			throw ex;
		}
	}
	/**
	 * 获取报警点分组明细集合——————二级页面
	 *
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param alarmPointGroupId
	 *            报警点分组明细Ids集合
	 * @return 报警点分组明细集合
	 */
	@Override
	public List<AlarmPointGroupDtlDTO> getAlarmPointGroupDtls(Long alarmPointGroupId) {

		try {
			String hql ="select new com.pcitc.opal.pm.dao.imp.AlarmPointGroupDtlDTO(t.alarmPointGroupDtlId,c.sname,a.tag) from AlarmPointGroupDtl t "+
					"left join AlarmPoint a on t.alarmPointId=a.alarmPointId "+
					"left join PrdtCell c on c.prdtCellId=a.prdtCellId "+
					"where t.alarmPointGroupId = (:alarmPointGroupId) "+
					"order by c.sname ,a.tag ";

			Query query =this.getEntityManager().createQuery(hql);
			query.setParameter("alarmPointGroupId",alarmPointGroupId);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	public List<Long> getAlarmPointGroupDtlList(Long alarmPointGroupId) {
		try {
			String hql ="select t.alarmPointGroupDtlId from AlarmPointGroupDtl t "+
					"where t.alarmPointGroupId = (:alarmPointGroupId) ";

			Query query =this.getEntityManager().createQuery(hql);
			query.setParameter("alarmPointGroupId",alarmPointGroupId);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}


}
