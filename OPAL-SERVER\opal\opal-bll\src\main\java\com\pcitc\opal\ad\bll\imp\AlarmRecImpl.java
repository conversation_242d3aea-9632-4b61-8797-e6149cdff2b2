package com.pcitc.opal.ad.bll.imp;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pcitc.opal.ad.bll.AlarmRecService;
import com.pcitc.opal.ad.dao.AlarmRecDAO;
import com.pcitc.opal.ad.dao.AlarmRecRepository;
import com.pcitc.opal.ad.entity.AlarmRecEntity;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.ad.vo.*;
import com.pcitc.opal.bd.dao.MonLevelMapDAO;
import com.pcitc.opal.bd.entity.MonLevelMapEntity;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.pm.dao.AlarmPointRepository;
import com.pcitc.opal.pm.dao.CompanyDAO;
import com.pcitc.opal.pm.dao.CompanyRepository;
import com.pcitc.opal.pm.entity.CompanyEntity;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import com.pcitc.opal.pm.pojo.Company;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class AlarmRecImpl implements AlarmRecService {

    @Autowired
    private AlarmRecRepository alarmRecRepository;

    @Autowired
    private AlarmRecDAO alarmRecDAO;

    @Autowired
    private AlarmPointRepository alarmPointRepository;

    @Autowired
    private MonLevelMapDAO monLevelMapDAO;

    @Autowired
    private CompanyDAO companyDAO;

    @Value("${craft.mon.trendChart.url}")
    private String monTrendChartUrl;

    @Override
    public CommonResult removeRecoveryTimeIsNullByAlarmTime(String startDate, String end, Integer companyId) {
        return alarmRecRepository.removeRecoveryTimeIsNullByAlarmTime(startDate, end, companyId);
    }


    @Override
//    @Transactional
    public void addAlarmRecByInfo(List<AlarmRec> list) {

        this.alarmRecRepository.insertAlarmRecByInfo(list);

    }

    @Override
    public int getAlarmRecCountByStartTime(AlarmRec alarmRec, String companyId) {
        return this.alarmRecRepository.findAlarmRecCountByStartTime(alarmRec.getUnitCode(), alarmRec.getPrdtCellId(), alarmRec.getTag(), alarmRec.getAlarmFlagType(), alarmRec.getAlarmTime(), companyId);
    }

    @Override
    public int modifyAlarmRecRecoveryTime(AlarmRec alarmRec, String companyId) {
        int i = this.alarmRecRepository.updateAlarmRecRecoveryTime(alarmRec.getUnitCode(), alarmRec.getPrdtCellId(), alarmRec.getTag(), alarmRec.getAlarmFlagType(), alarmRec.getAlarmTime(), alarmRec.getRecoveryTime(), companyId);
        return i;
    }

    @Override
    public int modifyAlarmRecResponseTimeByLessAlarmTime(AlarmRec alarmRec, String companyId) {
        int i = this.alarmRecRepository.updateAlarmRecResponseTimeByLessAlarmTime(alarmRec.getUnitCode(), alarmRec.getPrdtCellId(), alarmRec.getTag(), alarmRec.getAlarmFlagType(), alarmRec.getAlarmTime(), alarmRec.getRecoveryTime(), companyId);
        return i;
    }

    @Override
    public Integer updateResponseTimeByRec(AlarmRec alarmRec, Integer companyId) {
        return alarmRecRepository.updateResponseTimeByRec(alarmRec, companyId);
    }

    @Override
    public List<UnitRecNumVO> getUnitRecNum() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        // 将时分秒,毫秒域清零
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        calendar.set(Calendar.MINUTE, 00);
        calendar.set(Calendar.SECOND, 00);
        Date endTime = calendar.getTime();

        calendar.add(Calendar.DATE, -1);
        Date startTime = calendar.getTime();
        List<UnitRecNumVO> unitRecNum = alarmRecDAO.getUnitRecNum(startTime, endTime);
        return unitRecNum;
    }

    @Override
    public List<PriorityRecNumVO> getPriorityRecNum() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        // 将时分秒,毫秒域清零
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        calendar.set(Calendar.MINUTE, 00);
        calendar.set(Calendar.SECOND, 00);
        Date startTime = calendar.getTime();
        List<PriorityRecNumVO> priorityRecNum = alarmRecDAO.getPriorityRecNum(startTime);
        if (CollectionUtils.isNotEmpty(priorityRecNum)) {
            priorityRecNum.forEach(x -> x.setPriorityStr(x.getPriority() !=null ?  CommonEnum.AlarmPriorityEnum.getName(x.getPriority()) : null));
        }
        return priorityRecNum;
    }

    @Override
    public List<UnitRecCurrentVO> getUnitRecCurrent() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        // 将时分秒,毫秒域清零
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        calendar.set(Calendar.MINUTE, 00);
        calendar.set(Calendar.SECOND, 00);
        Date startTime = calendar.getTime();
        List<UnitRecCurrentVO> unitRecCurrent = alarmRecDAO.getUnitRecCurrent(startTime);
        if (CollectionUtils.isNotEmpty(unitRecCurrent)) {
            unitRecCurrent.forEach(x -> x.setPriorityStr(x.getPriority() !=null ?  CommonEnum.AlarmPriorityEnum.getName(x.getPriority()) : null));
        }
        return unitRecCurrent;
    }

    @Override
    public List<PointRecNumVO> getPointRecNum() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        // 将时分秒,毫秒域清零
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        calendar.set(Calendar.MINUTE, 00);
        calendar.set(Calendar.SECOND, 00);
        Date endTime = calendar.getTime();

        calendar.add(Calendar.DATE, -1);
        Date startTime = calendar.getTime();
        List<PointRecNumVO> pointRecNum = alarmRecDAO.getPointRecNum(startTime, endTime);
        return pointRecNum;
    }

    @Override
    public List<MonitorRecNumVO> getMonitorRecNum() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        // 将时分秒,毫秒域清零
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        calendar.set(Calendar.MINUTE, 00);
        calendar.set(Calendar.SECOND, 00);
        Date startTime = calendar.getTime();
        List<MonitorRecNumVO> monitorRecNum = alarmRecDAO.getMonitorRecNum(startTime);
        monitorRecNum.forEach(x -> x.setMonitorTypeStr(CommonEnum.MonitorTypeEnum.getName(x.getMonitorType())));
        return monitorRecNum;
    }

    @Override
    public String getCraftOpeMonTrendChart(Long alarmRecId) {
        String result = null;
        try {
            AlarmRecEntity recEntity = alarmRecDAO.selectById(alarmRecId);
            Date alarmTime = recEntity.getAlarmTime();
            LocalDateTime startLocalDateTime = alarmTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            String startTime = startLocalDateTime.plusHours(-2).format(DateTimeFormatter.ofPattern("yyyy-MM-dd%20HH:mm:ss"));
            String endTime = startLocalDateTime.plusHours(2).format(DateTimeFormatter.ofPattern("yyyy-MM-dd%20HH:mm:ss"));
            Long alarmPointId = recEntity.getAlarmPointId();
            AlarmPoint alarmPoint = alarmPointRepository.findById(alarmPointId).get();
            Long alarmFlagId = recEntity.getAlarmFlagId();
            MonLevelMapEntity monLevelMap = new MonLevelMapEntity();
            monLevelMap.setAlarmFlagId(alarmFlagId);
            List<MonLevelMapEntity> monLevelMapEntities = monLevelMapDAO.selectList(Wrappers.query(monLevelMap));
            String rtdbTag = alarmPoint.getRtdbTag();
            Integer companyId = alarmPoint.getCompanyId();
            CompanyEntity companyEntity = companyDAO.selectById(companyId);
            String stdCode = companyEntity.getStdCode();
            String unitCode = recEntity.getUnitCode();
            if (CollectionUtils.isNotEmpty(monLevelMapEntities) && StringUtils.isNotBlank(rtdbTag)) {
                MonLevelMapEntity levelMap = monLevelMapEntities.get(0);
                Long monLevelId = levelMap.getMonLevelId();
                StringBuilder uri = new StringBuilder(monTrendChartUrl);
                uri.append("?companyCode=").append(stdCode);
                uri.append("&unitCode=").append(unitCode);
                uri.append("&unionTagCode=").append(rtdbTag);
                uri.append("&monLevelId=").append(monLevelId);
                uri.append("&startTime=").append(startTime);
                uri.append("&endTime=").append(endTime);
                CloseableHttpClient client = HttpClients.createDefault();
                log.info("获取工艺趋势图地址：{}", uri.toString());
                HttpGet get = new HttpGet(uri.toString());
                CloseableHttpResponse response = client.execute(get);
                HttpEntity message = response.getEntity();
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == 200) {
                    if (message != null) {
                        result = EntityUtils.toString(message);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    @Override
    public List<AlarmAnlyInfoVO> getAnlyByEvent(List<Long> eventIdList) {
        List<AlarmAnlyInfoVO> infoVOList = null;
        try {
            infoVOList = alarmRecDAO.selectAlarmAnlyByEvent(eventIdList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return infoVOList;
    }

    @Override
    public Object getAlarmRecMaxAlarmTime(Object[] o, String companyId) {
        String unitCode = toStr(o[1]);
        String prdtcellId = toStr(o[2]);
        String tag = toStr(o[3]);
        String alarmFlag = toStr(o[4]);
        Date startTime = (Date) o[6];
        Object obj = this.alarmRecRepository.findAlarmRecMaxAlarmTime(unitCode, prdtcellId, tag, alarmFlag, startTime, companyId);
        return obj;
    }

    @Override
    public int modifyResponseTimeByStartTime(Object[] o, String companyId) {
        String unitCode = toStr(o[1]);
        String prdtcellId = toStr(o[2]);
        String tag = toStr(o[3]);
        String alarmFlag = toStr(o[4]);
        Date startTime = (Date) o[6];
        Date recoveryTime = (Date) o[7];
        int i = this.alarmRecRepository.updateResponseTimeByStartTime(unitCode, prdtcellId, tag, alarmFlag, startTime, recoveryTime, companyId);
        return i;
    }

    @Override
    public int getCountByAlarmRec(AlarmRec alarmRec, String companyId) {
//        this.alarmRecRepository.fin
        return 0;
    }

    //Object转Long
    private Long toL(Object o) {
        return o != null ? new BigDecimal(o + "").longValue() : null;
    }

    //Object转String
    private String toStr(Object o) {
        return o == null ? "" : o.toString();
    }

}
