var pmUrl = OPAL.API.pmUrl;
var delUrl = OPAL.API.apUrl+'/PushRuleConf/delete';
var searchUrl = OPAL.API.apUrl + '/PushRuleConf/getAlarmPushRules';
var inUseUrl = OPAL.API.commUrl + "/getInUse";
var isRefresh = false;
window.pageLoadMode = PageLoadMode.None;
var BusinessType = 1;
var BusinessTypeTitle = '';
$(function() {
	var page = {
		//页面初始化	
		init : function() {
			this.bindUI();
            BusinessType = OPAL.util.getQueryParam('BusinessType');
            if (BusinessType == 1) {
                BusinessTypeTitle = '超时';
            } else {
                BusinessTypeTitle = '超限';
            }
            $(".alarm-public-title-dis").html(BusinessTypeTitle + '推送规则配置');
            //初始化表格
            page.logic.initTable();
            //默认查询数据
            page.logic.search();
		},
		//绑定事件和逻辑
		bindUI : function() {
			// 新增
            $('#btnAdd').click(function() {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            });
            //批量删除
            $('#btnDel').click(function() {
                page.logic.delAll();
            });
                //查询
            $('#searched').click(function() {
                page.logic.search();
            });
		},
		data: {
            // 设置查询参数
            param: {}
        },
        //定义业务逻辑方法
		logic : {
			/**
             * 初始化表格
             */
            initTable: function () {
                OPAL.ui.initBootstrapTable("table",{
                    cache:false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '90px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "推送规则",
                        field: 'name',
                        rowspan: 1,
                        align: 'left',
                        width: '180px'
                    }, {
                        title: "维护时间",
                        field: 'mntDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "维护人",
                        field: 'mntUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }]
                },page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    businessType: BusinessType,
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param,param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.edit(\''+ rowData.alarmPushRuleId +'\',\''+rowData.name+'\')">修改</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    '<a  name="TableDelete"  href="javascript:window.page.logic.view(\'' + rowData.alarmPushRuleId + '\',\''+ rowData.name +'\')" >详情</a> '
                ]
            },
            /**
             * 批量删除
             */
            delAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.alarmPushRuleId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        processData: false,
                        contentType: "application/json;charset=utf-8",
                        dataType: "text",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 新增
             */
            add: function () {
                var pageMode = PageModelEnum.NewAdd;
                var title = BusinessTypeTitle + "推送规则配置新增";
                page.logic.detail(title, "", pageMode,'');
            },
            /**
             * 编辑
             * @param ID
             */
            edit: function (ID, name) {
                var pageMode = PageModelEnum.Edit;
                var title = BusinessTypeTitle + "推送规则配置编辑";
                page.logic.detail(title, ID, pageMode,name);
            },
            /**
             * 详情
             * @param ID
             */
            view: function (ID, name) {
                var pageMode = PageModelEnum.View;
                var title = BusinessTypeTitle + "推送规则配置详情";
                page.logic.detail(title, ID, pageMode,name);
            },
            /**
             * 装置新增或者编辑详细页面
             */
            detail: function (title, ID, pageMode, name) {
                layer.open({
                    type: 2,
                    title: title,
                    closeBtn: 1,
                    area: ['1000px', '500px'],
                    shadeClose: false,
                    content: 'PushRuleAddOrEdit.html?'+ Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "alarmPushRuleId": ID,
                            'name': name,
                            'title': title,
                            'BusinessType': BusinessType
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                    	if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 搜索
             */
            search: function () {
                page.data.param = OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber":1
                });
            },
		}
	};
	page.init();
	window.page = page;
});