var addUrl = OPAL.API.pmUrl + '/systRunParaConf';
var searchUrl = OPAL.API.pmUrl + '/systRunParaConf';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var getAlarmChangePlanStatusListUrl = OPAL.API.commUrl + "/getAlarmChangePlanStatusList"
window.pageLoadMode = PageLoadMode.None;
$(function() {
    var page = {
        /**
         * 初始化
         */
        init: function() {
            $('#saveAddModal').prop('disabled', true);
            //绑定事件
            this.bindUI();
            //初始化表格
            page.logic.initTable();
            //默认查询数据
            page.logic.search();
        },
        /**
         * 绑定事件
         */
        bindUI: function() {
            // 新增
            $('#saveAddModal').click(function() {
                page.logic.save();
            })
            //查询
            $('#searched').click(function() {
                page.logic.search();
                $('#saveAddModal').prop('disabled',false);
            })
        },
        data: {
            // 设置查询参数
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化表格
             */
            initTable: function() {
                $("#table").bootstrapTable({
                    cache: false,
                    pagination: false,
                    striped: true,
                    sidePagination: "server",
                    queryParamsType: "undefined",
                    queryParams:  page.logic.queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    columns: [{
                        field: 'name',
                        title: '名称',
                        align: 'left',
                        width: '100px'
                    }, {
                        field: 'code',
                        title: '编码',
                        align: 'left',
                        width: '100px'
                    }, {
                        field: 'paraValue',
                        title: '参数值',
                        align: 'left',
                        width: '100px',
                        formatter: function(value, row, index) {
                            return "<input type='text' value='" + value + "' class='sys-run-num-box num'  maxlength='100'>"
                        }
                    }, {
                        field: 'des',
                        title: '描述',
                        align: 'left',
                        width: '100px'
                    }],

                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#table').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                })
                $('#table').colResizable({
                    liveDrag: true
                });
                    
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 搜索
             */
            search: function() {
                page.data.param = OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            save: function() {
                var data = $('#table').bootstrapTable('getData');
                if(data.length != 0){
                    var nums = $('.num');
                    $.each(nums, function(i, el) {
                        data[i].paraValue = $(el).val();
                    })
                    var arr = new Array();
                    $.each(data, function(i, el) {
                        arr.push(el);
                    })
                    var data = $.ET.toCollectionJson(arr);
                    $.ajax({
                        url: addUrl,
                        type: 'PUT',
                        async: true,
                        data: JSON.stringify(data),
                        dataType: "text",
                        processData: false,
                        contentType: "application/json;charset=utf-8",
                        success: function(result, XMLHttpRequest) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result);
                            } else {
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    });
                }else {
                    layer.msg('没有数据，不能保存！')
                }
                
            }
        }
    }
    page.init();
    window.page = page;
})
