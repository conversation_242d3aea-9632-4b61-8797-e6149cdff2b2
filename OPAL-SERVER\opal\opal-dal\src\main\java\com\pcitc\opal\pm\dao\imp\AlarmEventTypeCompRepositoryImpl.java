package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmEventTypeCompRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmEventTypeComp;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * AlarmEventTypeComp实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_AlarmEventTypeCompRepositoryImpl
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：AlarmEventTypeComp实体的Repository实现
 */
public class AlarmEventTypeCompRepositoryImpl extends BaseRepository<AlarmEventTypeComp, Long> implements AlarmEventTypeCompRepositoryCustom {

    /**
     * 唯一性校验
     *
     * @param alarmEventTypeCompEntity 报警事件类型对照实体
     * @return 查询返回信息类
     * <AUTHOR>   2018-03-30
     */
    @Override
    public CommonResult alarmEventTypeCompValidation(AlarmEventTypeComp alarmEventTypeCompEntity) {
        CommonResult commonResult = new CommonResult();
        try {
            //“源事件类型”和“源事件名称”做联合唯一校验
            StringBuilder hql = new StringBuilder("from AlarmEventTypeComp t where t.eventTypeSource=:eventTypeSource and t.eventNameSource=:eventNameSource and t.dcsCodeId=:name and t.alarmEventTypeCompId<>:alarmEventTypeCompId");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("eventTypeSource", alarmEventTypeCompEntity.getEventTypeSource());
            paramList.put("eventNameSource", alarmEventTypeCompEntity.getEventNameSource());
            paramList.put("alarmEventTypeCompId", alarmEventTypeCompEntity.getAlarmEventTypeCompId() == null ? 0 : alarmEventTypeCompEntity.getAlarmEventTypeCompId());
            paramList.put("name",alarmEventTypeCompEntity.getDcsCodeId());
            long index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                commonResult.setIsSuccess(false);
                commonResult.setMessage("该DCS下源事件名称已存在！");
                return commonResult;
            }
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
            commonResult.setResult(null);
        }
        return commonResult;
    }

    /**
     * 新增报警事件类型对照
     *
     * @param alarmEventTypeCompEntity 添加的实体
     * @return 消息结果类
     * <AUTHOR>    2018-03-30
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addAlarmEventTypeComp(AlarmEventTypeComp alarmEventTypeCompEntity) {
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(alarmEventTypeCompEntity);
            commonResult.setResult(alarmEventTypeCompEntity);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    /**
     * 删除报警事件类型对照
     *
     * @param alarmEventTypeCompIds 报警事件类型对照ID集合
     * @return 消息结果类
     * <AUTHOR> 2018-03-30
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult deleteAlarmEventTypeComp(Long[] alarmEventTypeCompIds) {
        CommonResult commonResult = new CommonResult();
        try {
            String hql = "from AlarmEventTypeComp t where t.alarmEventTypeCompId in (:alarmEventTypeCompIds)";
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> alarmEventTypeCompIdList = Arrays.asList(alarmEventTypeCompIds);
            paramList.put("alarmEventTypeCompIds", alarmEventTypeCompIdList);

            TypedQuery<AlarmEventTypeComp> query = getEntityManager().createQuery(hql, AlarmEventTypeComp.class);
            this.setParameterList(query, paramList);
            List<AlarmEventTypeComp> AlarmEventTypeCompList = query.getResultList();
            AlarmEventTypeCompList.forEach(x -> {
                this.getEntityManager().remove(x);
            });
            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    /**
     * 更新报警事件类型对照
     *
     * @param alarmEventTypeCompEntity 报警事件类型对照实体
     * @return 消息结果类
     * <AUTHOR>   2018-03-30
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult updateAlarmEventTypeComp(AlarmEventTypeComp alarmEventTypeCompEntity) {
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(alarmEventTypeCompEntity);
            commonResult.setResult(alarmEventTypeCompEntity);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    /**
     * 获取报警事件类型对照实体
     *
     * @param alarmEventTypeCompId 报警事件类型对照ID
     * @return 报警事件类型对照实体
     * <AUTHOR>  2018-03-30
     */
    @Override
    public AlarmEventTypeComp getSingleAlarmEventTypeComp(Long alarmEventTypeCompId) {
        return getEntityManager().find(AlarmEventTypeComp.class, alarmEventTypeCompId);
    }

    /**
     * 获取报警事件类型对照实体
     *
     * @param alarmEventTypeCompIds 报警事件类型对照ID集合
     * @return 报警事件类型对照实体集合
     * <AUTHOR> 2018-03-30
     */
    @Override
    public List<AlarmEventTypeComp> getAlarmEventTypeComp(Long[] alarmEventTypeCompIds) {
        try {
            // 查询字符串
            String hql = "from AlarmEventTypeComp t ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (alarmEventTypeCompIds.length > 0) {
                hql += " where t.alarmEventTypeCompId in (:alarmEventTypeCompIds)";
                List<Long> alarmEventTypeCompIdsList = Arrays.asList(alarmEventTypeCompIds);
                paramList.put("alarmEventTypeCompIds", alarmEventTypeCompIdsList);
            }
            TypedQuery<AlarmEventTypeComp> query = getEntityManager().createQuery(hql, AlarmEventTypeComp.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取报警事件类型对照实体（分页）
     *
     * @param dcsCodeId       DcsCodeID
     * @param eventTypeSource 源事件类型
     * @param eventNameSource 源事件名称
     * @param eventTypeIds    事件类型ID集合
     * @param inUse           是否启用
     * @param page            分页信息
     * @return 报警事件类型对照实体（分页）
     * <AUTHOR> 2018-03-30
     */
    @Override
    public PaginationBean<AlarmEventTypeComp> getAlarmEventTypeComp(Long dcsCodeId, String eventTypeSource, String eventNameSource, Long[] eventTypeIds, Integer inUse, Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from AlarmEventTypeComp t" +
                    " inner join fetch t.dcsCode dcs" +
                    " inner join fetch t.eventType eventType" +
                    " where 1=1 ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 源事件类型
            if (!StringUtils.isEmpty(eventTypeSource)) {
                hql.append("  and (t.eventTypeSource like :eventTypeSource)");
                paramList.put("eventTypeSource", "%" + this.sqlLikeReplace(eventTypeSource) + "%");
            }
            // 源事件名称
            if (!StringUtils.isEmpty(eventNameSource)) {
                hql.append("  and (t.eventNameSource like :eventNameSource)");
                paramList.put("eventNameSource", "%" + this.sqlLikeReplace(eventNameSource) + "%");
            }
            // 处理事件类型
            if (ArrayUtils.isNotEmpty(eventTypeIds)) {
                hql.append(" and t.eventTypeId in (:eventTypeIds) ");
                List<Long> eventTypeIdList = Arrays.asList(eventTypeIds);
                paramList.put("eventTypeIds", eventTypeIdList);
            }
            // 处理是否启用
            if (inUse != null && inUse != -1) {
                hql.append(" and t.inUse = :inUse ");
                paramList.put("inUse", inUse);
            }
            // 处理DcsCode
            if (dcsCodeId != -1) {
                hql.append(" and t.dcsCodeId=:dcsCodeId ");
                paramList.put("dcsCodeId", dcsCodeId);
            }
            hql.append(" order by t.dcsCode.name,t.eventTypeSource,t.eventNameSource asc ");
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }

    }

}
