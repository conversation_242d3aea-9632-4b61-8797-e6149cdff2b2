package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.pm.bll.entity.*;
import com.pcitc.opal.pm.pojo.AlarmPointGroup;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.List;

/*
 * 报警点分组业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmPointGroupService
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点分组业务逻辑层接口
 */
@Service
public interface AlarmPointGroupService {

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointGroupEntity 报警点实体
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult addAlarmPointGroup(AlarmPointGroupEntity alarmPointGroupEntity) throws Exception;
	
	/**
	 * 删除报警点分组数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointGroupIds 报警点分组主键Id集合
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult deleteAlarmPointGroup(Long[] alarmPointGroupIds) throws Exception;
	
	/**
	 * 报警点分组更新数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointGroupEntity 报警点分组实体
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult updateAlarmPointGroup(AlarmPointGroupEntity alarmPointGroupEntity) throws Exception;
	
	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointGroupId 报警点分组ID
	 * @return AlarmPointGroupEntity 报警点分组实体类
	 * @throws Exception 
	 */
	AlarmPointGroupConfigEntity getSingleAlarmPointGroup(Long alarmPointGroupId) throws Exception;
	
	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param unitCodes 装置编码
	 * @param groupName 分组名称
	 * @param page 翻页实现类
	 * @throws Exception 
	 * @return PaginationBean<AlarmPointEntity> 分页对象
	 */
	PaginationBean<AlarmPointGroupConfigEntity> getAlarmPointGroups(String[] unitCodes, String groupName,Long companyId, Pagination page) throws  Exception;


	/**
	 * 获取多条数据
	 *
	  * <AUTHOR> 2017-10-11
	 * @param unitCode 装置编码
	 * @return AlarmPointGroupEntity 报警点分组实体类
	 * @throws Exception
	 */
	List<AlarmPointGroup> getAlarmPointGroupsByUnit(String unitCode) throws Exception;
}
