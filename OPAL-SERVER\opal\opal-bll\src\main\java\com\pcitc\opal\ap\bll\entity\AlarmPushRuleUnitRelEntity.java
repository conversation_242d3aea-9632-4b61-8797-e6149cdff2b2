package com.pcitc.opal.ap.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 报警点实体
 * 模块编号：pcitc_pojo_class_Group
 * 作       者：guoganxin
 * 创建时间：2023/04/16
 * 修改编号：1
 * 描       述：群组
 */
public class AlarmPushRuleUnitRelEntity extends BasicEntity {
    /**
     * 报警推送规则装置关系ID
     */
    private Long apRuleUnitRelId;

    /**
     * 企业ID
     */
    private Integer companyId;

    /**
     * 报警推送规则ID
     */
    private Long alarmPushRuleId;


    /**
     * 优先级（1 紧急；2重要；3 一般）
     */
    private Long priority;

    /**
     * 报警专业（1 工艺、2 设备、3 安全、4 环保、5 质量、6 火灾）
     */
    private Integer alarmSpeciality;

    /**
     * 是否启用（1是；0否）
     */
    private Integer inUse;

    /**
     * 排序
     */
    private Integer sortNum;

    /**
     * 描述
     */
    private String des;

    public Long getApRuleUnitRelId() {
        return apRuleUnitRelId;
    }

    public void setApRuleUnitRelId(Long apRuleUnitRelId) {
        this.apRuleUnitRelId = apRuleUnitRelId;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Long getAlarmPushRuleId() {
        return alarmPushRuleId;
    }

    public void setAlarmPushRuleId(Long alarmPushRuleId) {
        this.alarmPushRuleId = alarmPushRuleId;
    }

    public Long getPriority() {
        return priority;
    }

    public void setPriority(Long priority) {
        this.priority = priority;
    }

    public Integer getAlarmSpeciality() {
        return alarmSpeciality;
    }

    public void setAlarmSpeciality(Integer alarmSpeciality) {
        this.alarmSpeciality = alarmSpeciality;
    }

    @Override
    public Integer getInUse() {
        return inUse;
    }

    @Override
    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
