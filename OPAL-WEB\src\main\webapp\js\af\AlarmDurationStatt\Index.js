/**
 * 报警时长统计页面
 * 功能：提供报警时长的统计分析，支持按车间和装置两种显示模式
 */

// API 接口配置
const API_URLS = {
    // 主要数据接口
    alarmDurationStatt: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotal',
    alarmDurStatt: OPAL.API.afUrl + '/alarmDurationStatt',
    alarmDurStattPage: OPAL.API.afUrl + '/alarmDurationStatt/page',
    alarmDurStattMain: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattMain',

    // 基础数据接口
    commonUnitTree: OPAL.API.commUrl + "/getAllUnit",
    priority: OPAL.API.afUrl + "/alarmDurationStatt/getAlarmPriorityList",
    getShowTime: OPAL.API.commUrl + '/getShowTime',
    inUse: OPAL.API.commUrl + "/getInUse",
    alarmFlagList: OPAL.API.adUrl + '/alarmEvents/getAlarmFlagList',

    // 车间显示相关接口
    totalWorkshop: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotalWorkshop',
    totalUnit: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotalUnit',
    totalPrdtcell: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattTotalPrdtcell',
    getMainCommon: OPAL.API.afUrl + '/alarmDurationStatt/getAlarmDurationStattMainCommon',

    // 导出接口
    exportAlarmDurationStatt: OPAL.API.afUrl + '/alarmDurationStatt/exportAlarmDurationStattMain',
    exportMainCommon: OPAL.API.afUrl + '/alarmDurationStatt/exportAlarmDurationStattMainCommon'
};

// 全局变量
let floodAlarmChart;
const tabChart = {
    floodAlarmChartcj: null,
    floodAlarmChartzz: null,
    floodAlarmChartdy: null,
};

// 状态变量
let isLoading = true;
let unitId;
let unit;
let alarmFlagId;
let tag;
let priority;
$(function () {
    const page = {
        /**
         * 页面初始化
         */
        init: function () {
            // 绑定UI事件
            this.bindUi();

            // 初始化基础组件
            this.initializeComponents();

            // 初始化数据
            this.initializeData();
        },

        /**
         * 初始化组件
         */
        initializeComponents: function () {
            // 日期扩展
            OPAL.util.extendDate();

            // 初始化各种下拉选择器
            page.logic.initPriority();
            page.logic.initUnitTree();
            page.logic.initAlarmFlagList();

            // 初始化图表和表格
            page.logic.initFloodChart(JSON.stringify(""));
            page.logic.initTableDisplay(JSON.stringify(""));
            page.logic.initOpetateTable();
            page.logic.initTableDisplaycj(JSON.stringify(""));
            page.logic.initOpetateTablecj();
        },

        /**
         * 初始化数据
         */
        initializeData: function () {
            // 根据日期时间计算开始时间和结束时间
            page.logic.getShowTime();

            // 装置赋值 - 从cookie恢复上次选择
            if (isLoading && (!page.data.param.unitIds || page.data.param.unitIds.length === 0)) {
                const cookieValue = OPAL.util.getCookieByPageCode("FloodAlarmAnalysis");
                if (cookieValue && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
        },
        /**
         * 绑定UI事件
         */
        bindUi: function () {
            // 窗口大小改变时重新调整图表和表格
            this.bindResizeEvent();

            // 绑定导航切换事件
            this.bindTabSwitchEvent();

            // 绑定查询按钮事件
            this.bindSearchEvent();

            // 绑定导出按钮事件
            this.bindExportEvent();
        },

        /**
         * 绑定窗口大小改变事件
         */
        bindResizeEvent: function () {
            window.onresize = function () {
                // 调整图表大小
                if (floodAlarmChart && !floodAlarmChart.isDisposed()) {
                    floodAlarmChart.resize();
                }

                // 调整车间模式图表大小
                Object.keys(tabChart).forEach(key => {
                    if (tabChart[key] && !tabChart[key].isDisposed()) {
                        tabChart[key].resize();
                    }
                });

                // 调整表格视图
                $('#floodTable').bootstrapTable('resetView');
                $('#MostAlarmOperateTable').bootstrapTable('resetView');
                $('#MostAlarmOperateTablecj').bootstrapTable('resetView');
            };
        },

        /**
         * 绑定标签切换事件
         */
        bindTabSwitchEvent: function () {
            $('.myTab li').click(function () {
                const flag = $(this).attr('showFlag');
                const imageMap = {
                    imgShow: {
                        current: '../../../images/one1.png',
                        siblings: ['../../../images/tweo.png', '../../../images/trees.png']
                    },
                    tableShow: {
                        current: '../../../images/tweos.png',
                        siblings: ['../../../images/one.png', '../../../images/trees.png']
                    },
                    unitShow: {
                        current: '../../../images/treese.png',
                        siblings: ['../../../images/one.png', '../../../images/tweo.png']
                    }
                };

                if (imageMap[flag]) {
                    $(this).find('img').attr('src', imageMap[flag].current);
                    $(this).siblings().each((index, sibling) => {
                        $(sibling).find('img').attr('src', imageMap[flag].siblings[index]);
                    });
                }
            });
        },

        /**
         * 绑定查询事件
         */
        bindSearchEvent: function () {
            $('#btnSearch').click(function () {
                if (OPAL.util.checkDateIsValid()) {
                    isLoading = false;
                    page.logic.search();
                }
            });
        },

        /**
         * 绑定导出事件
         */
        bindExportEvent: function () {
            $('#AlarmDurationStattExport').click(function() {
                layer.confirm('数据量较大，导出时间可能较长，是否继续？', {
                    btn: ['是', '否'],
                    title: '提示',
                }, function (index) {
                    layer.close(index);
                    page.logic.exportExcel();
                }, function (index) {
                    layer.close(index);
                });
            });
        },
        /**
         * 页面数据
         */
        data: {
            param: {},
            subParam: {},
            unitCodeList: [],
            unitCodes: [],
            prdtCellId: [],
            workshopCodes: [],
            click: ''
        },

        /**
         * 业务逻辑
         */
        logic: {
            /**
             * 初始化查询inUse（已注释，保留接口）
             */
            initInUse: function () {
                OPAL.ui.getCombobox("isElimination", API_URLS.inUse, {
                    selectValue: 1,
                    data: {
                        'isAll': false
                    }
                }, null);
            },

            /**
             * 执行查询
             */
            search: function () {
                // 重置点击状态
                page.data.click = '';

                // 进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;

                // 准备查询参数
                this.prepareSearchParams();

                // 禁用查询按钮防止重复点击
                $("#btnSearch").prop('disabled', true);

                // 根据显示模式执行不同的查询
                if ($("#checkShop").is(":checked")) {
                    this.searchWorkshopMode();
                } else {
                    this.searchDefaultMode();
                }
            },

            /**
             * 准备查询参数
             */
            prepareSearchParams: function () {
                page.data.param = OPAL.form.getData("searchForm");
                const unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                page.data.param.unitIds = unitIds;
                page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                page.data.subParam.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.subParam.endTime = OPAL.util.strToDate(page.data.param.endTime);
            },

            /**
             * 车间模式查询
             */
            searchWorkshopMode: function () {
                document.querySelector('.contentcj').style.display = 'block';
                document.querySelector('.contentmr').style.display = 'none';

                $.ajax({
                    url: API_URLS.totalWorkshop,
                    data: page.data.param,
                    dataType: 'json',
                    success: function (data) {
                        const result = $.ET.toObjectArr(data);
                        page.logic.initFloodChartCom(JSON.stringify(result), 'floodAlarmChartcj');
                    },
                    error: function () {
                        console.error('车间模式查询失败');
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
            },

            /**
             * 默认模式查询
             */
            searchDefaultMode: function () {
                document.querySelector('.contentcj').style.display = 'none';
                document.querySelector('.contentmr').style.display = 'block';

                $.ajax({
                    url: API_URLS.alarmDurationStatt,
                    data: page.data.param,
                    dataType: 'json',
                    success: function (data) {
                        const result = $.ET.toObjectArr(data);
                        page.logic.initFloodChart(JSON.stringify(result));
                        page.logic.initTableDisplay(JSON.stringify(result));
                    },
                    error: function () {
                        console.error('默认模式查询失败');
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
            },
            /**
             * 导出
             */
            exportExcel: function() {
                var titleArray = new Array();
                var id = ''
                if ($("#checkShop").is(":checked")) {
                    id = '#MostAlarmOperateTablecj'
                } else {
                    id = '#MostAlarmOperateTable'
                }
                var tableTitle = $(id).bootstrapTable('getOptions').columns[0];
                $.each(tableTitle, function(i, el) {
                    if (i >= 1) {
                        titleArray.push({
                            'key': el.field,
                            'value': el.title
                        })
                    }
                })
                var data = {};
                var pageSize = $(id).bootstrapTable('getOptions').pageSize;
                var pageNumber = $(id).bootstrapTable('getOptions').pageNumber;
                data.titles = JSON.stringify(titleArray);
                data.pageSize = pageSize;
                data.pageNumber = pageNumber;
                // page.logic.setParams();
                page.data.param = OPAL.form.getData("searchForm");
                $.extend(data, page.data.param);
                $('#titlesExport').val(data.titles);
                $('#pageSizeExport').val(data.pageSize);
                $('#pageNumberExport').val(data.pageNumber);
                $('#unitIdsExport').val(page.data.unitCodeList);
                $('#priorityFrom').val(data.priority);
                $('#startTimeForm').val(data.startTime);
                $('#endTimeForm').val(data.endTime);
                $('#alarmFlagIdForm').val(data.alarmFlagId);
                if ($("#checkShop").is(":checked")) {
                    $('#form').attr('action', exportnextMainCommon);
                    // $('#unitCodesFrom').val(page.data.unitCodes);
                    // $('#workshopCodesFrom').val(page.data.workshopCodes);
                    // $('#prdtCellIdFrom').val(page.data.prdtCellId);
                    // if (page.data.click == 'floodAlarmChartcj') {
                    //     $('#workshopCodesFrom').val([]);
                    //     $('#prdtCellIdFrom').val([]);
                    // } else if (page.data.click == 'floodAlarmChartzz') {
                    //     $('#prdtCellIdFrom').val([]);
                    // }
                } else {
                    $('#form').attr('action', exportAlarmDurationStattUrl);
                }
                $('#form').submit();
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', API_URLS.commonUnitTree, 'id', 'parentId', 'sname', {
                    onChange: function (/* node, checked */) {
                        const unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (unitIds && unitIds.length === 1) {
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    }
                }, false);
            },
            /**
             * 初始化图表
             * @param data
             */
            initFloodChart: function (data) {
                var results = JSON.parse(data);
                // page.logic.queryMostOperate();
                if (floodAlarmChart && !floodAlarmChart.isDisposed()) {
                    floodAlarmChart.clear();
                    floodAlarmChart.dispose();
                }
                if (results == undefined || results.length == 0) {
                    floodAlarmChart = OPAL.ui.chart.initEmptyChart('floodAlarmChart');
                    return;
                }
                var xAxis = [];
                var emergencyAlarmQuantity = [];
                var importantAlarmQuantity = [];
                var generalAlarmQuantity = [];
                var nullAlarmQuantity = [];
                unitId = results[0].unitId;
                unit = results[0].sname;
                for (var i = 0; i < results.length; i++) {
                    if (results[i].sname) {
                        xAxis.push(results[i].sname)
                    } else if (results[i].name) {
                        xAxis.push(results[i].name)
                    }
                    // emergencyAlarmQuantity.push($.parseJSON(results[i].emergencyAlarmQuantityEntity));
                    // importantAlarmQuantity.push($.parseJSON(results[i].importantAlarmQuantityEntity));
                    // generalAlarmQuantity.push($.parseJSON(results[i].generalAlarmQuantityEntity));
                    // nullAlarmQuantity.push($.parseJSON(results[i].nullAlarmQuantityEntity))
                    emergencyAlarmQuantity.push($.parseJSON(results[i].emergencyAlarmQuantity));
                    importantAlarmQuantity.push($.parseJSON(results[i].importantAlarmQuantity));
                    generalAlarmQuantity.push($.parseJSON(results[i].generalAlarmQuantity));
                    nullAlarmQuantity.push($.parseJSON(results[i].nullAlarmQuantity))
                }
                var option = {
                    color: ['#F4D312', '#FE6732', '#CD1515', '#C9C9C9'],
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        itemHeight: 8,
                        itemWidth: 18,
                        data: ['一般', '重要', '紧急', '空'],
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        top: '10%',
                        height: '270px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            show: true,
                            splitNumber: 5,
                            textStyle: {
                                fontSize: 12,
                                color: '#000'
                            },
                        },
                        data: xAxis,
                    }],
                    dataZoom: [{
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        // left: '9%',
                        bottom: -5,
                        start: 0,
                        end: 100 //初始化滚动条
                    }],
                    yAxis: {
                        splitLine: {
                            show: false
                        },
                        type: 'value',
                        name: "(分钟)"
                    },
                    series: [{
                        name: '一般',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: generalAlarmQuantity
                    },
                    {
                        name: '重要',
                        type: 'bar',
                        barWidth: '20px',
                        stack: 'sum',
                        data: importantAlarmQuantity,
                    },
                    {
                        name: '紧急',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: emergencyAlarmQuantity
                    },
                    {
                        name: '空',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: nullAlarmQuantity
                    }]
                };
                floodAlarmChart = echarts.init(document.getElementById('floodAlarmChart'));
                floodAlarmChart.setOption(option);
                page.data.param.unitIds = [unitId];
                page.data.unitCodeList=[unitId];
                floodChartConfig = option;
                floodAlarmChart.on('click', function (param) {
                    var indexs = param.dataIndex;
                    page.data.param.unitIds = [results[indexs].unitId];
                    unit = param.name;

                    page.logic.queryMostOperate();

                });
                page.logic.queryMostOperate();
            },

            // 展示空数据的图表
            EmptyChart: function() {
                tabChart.floodAlarmChartcj = OPAL.ui.chart.initEmptyChart('floodAlarmChartcj');
                tabChart.floodAlarmChartzz = OPAL.ui.chart.initEmptyChart('floodAlarmChartzz');
                tabChart.floodAlarmChartdy = OPAL.ui.chart.initEmptyChart('floodAlarmChartdy');
            },

            /**
             * 初始化通用图表（车间模式）
             * @param {string} data - 图表数据JSON字符串
             * @param {string} chartId - 图表容器ID
             */
            initFloodChartCom: function (data, chartId) {
                const results = JSON.parse(data);

                // 清理现有图表
                if (tabChart[chartId] && !tabChart[chartId].isDisposed()) {
                    tabChart[chartId].clear();
                    tabChart[chartId].dispose();
                }

                // 如果没有数据，显示空图表
                if (!results || results.length === 0) {
                    page.logic.EmptyChart();
                    return;
                }
                var xAxis = [];
                var emergencyAlarmQuantity = [];
                var importantAlarmQuantity = [];
                var generalAlarmQuantity = [];
                var nullAlarmQuantity = [];
                unitId = results[0].unitId;
                unit = results[0].sname;
                var nextparam = {
                    code: '',
                    num: 0
                }
                for (var i = 0; i < results.length; i++) {
                    if (results[i].sname) {
                        xAxis.push(results[i].sname)
                    } else if (results[i].name) {
                        xAxis.push(results[i].name)
                    }
                    emergencyAlarmQuantity.push($.parseJSON(results[i].emergencyAlarmQuantity));
                    importantAlarmQuantity.push($.parseJSON(results[i].importantAlarmQuantity));
                    generalAlarmQuantity.push($.parseJSON(results[i].generalAlarmQuantity));
                    nullAlarmQuantity.push($.parseJSON(results[i].nullAlarmQuantity))
                    if (nextparam.num < Math.abs($.parseJSON(results[i].emergencyAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].emergencyAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].importantAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].importantAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].generalAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].generalAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].nullAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].nullAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                }
                var option = {
                    color: ['#F4D312', '#FE6732', '#CD1515', '#C9C9C9'],
                    tooltip: {
                        trigger: 'axis',
                    },
                    legend: {
                        itemHeight: 8,
                        itemWidth: 18,
                        data: ['一般', '重要', '紧急', '空'],
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        top: '10%',
                        height: '270px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            show: true,
                            splitNumber: 5,
                            textStyle: {
                                fontSize: 12,
                                color: '#000'
                            },
                        },
                        data: xAxis,
                    }],
                    dataZoom: [{
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        // left: '9%',
                        bottom: -5,
                        start: 0,
                        end: 100 //初始化滚动条
                    }],
                    yAxis: {
                        splitLine: {
                            show: false
                        },
                        type: 'value',
                        name: "(分钟)"
                    },
                    series: [{
                        name: '一般',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: generalAlarmQuantity
                    },
                    {
                        name: '重要',
                        type: 'bar',
                        barWidth: '20px',
                        stack: 'sum',
                        data: importantAlarmQuantity,
                    },
                    {
                        name: '紧急',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: emergencyAlarmQuantity
                    },
                    {
                        name: '空',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: nullAlarmQuantity
                    }]
                };
                tabChart[chartId] = echarts.init(document.getElementById(chartId));
                tabChart[chartId].setOption(option);
                page.data.param.unitIds = [unitId];
                page.data.unitCodeList = [unitId];
                floodChartConfig = option;
                tabChart[chartId].on('click', function (param) {
                    const indexs = param.dataIndex;
                    page.data.click = chartId;
                    if (chartId === 'floodAlarmChartcj') {
                        page.data.param.workshopCodes = [results[indexs].code];
                        page.data.workshopCodes = [results[indexs].code];
                        page.logic.querynextChart('floodAlarmChartzz');
                    } else if (chartId === 'floodAlarmChartzz') {
                        page.data.param.unitCodes = [results[indexs].code];
                        page.data.unitCodes = [results[indexs].code];
                        page.logic.querynextChart('floodAlarmChartdy');
                    } else if (chartId === 'floodAlarmChartdy') {
                        page.data.param.prdtCellId = [results[indexs].code];
                        page.data.prdtCellId = [results[indexs].code];
                    }
                });

                if (chartId === 'floodAlarmChartcj') {
                    page.data.param.workshopCodes = [nextparam.code];
                    page.data.workshopCodes = [nextparam.code];
                    page.logic.querynextChart('floodAlarmChartzz');
                } else if (chartId === 'floodAlarmChartzz') {
                    page.data.param.unitCodes = [nextparam.code];
                    page.data.unitCodes = [nextparam.code];
                    page.logic.querynextChart('floodAlarmChartdy');
                } else if (chartId === 'floodAlarmChartdy') {
                    page.data.param.prdtCellId = [nextparam.code];
                    page.data.prdtCellId = [nextparam.code];
                }
            },

            /**
             * 获取下级图表数据
             * @param {string} chartId - 图表ID
             */
            querynextChart: function(chartId) {
                let url = '';
                if (chartId === 'floodAlarmChartzz') {
                    url = API_URLS.totalUnit;
                } else if (chartId === 'floodAlarmChartdy') {
                    url = API_URLS.totalPrdtcell;
                }

                $.ajax({
                    url: url,
                    data: page.data.param,
                    dataType: 'json',
                    success: function (data) {
                        const result = $.ET.toObjectArr(data);
                        page.logic.initFloodChartCom(JSON.stringify(result), chartId);
                        if (chartId === 'floodAlarmChartdy') {
                            page.logic.queryMostOperatecj();
                        }
                    },
                    error: function () {
                        console.error('获取图表数据失败:', chartId);
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
            },



            /**
             * 初始化Table数据
             * @param data
             */
            initTableDisplay: function (data) {
                var results = JSON.parse(data);
                var options = {
                    // detailView: true, //父子表
                    striped: true, //是否显示行间隔色
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    height: 350,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'sname',
                        title: '装置',
                        align: 'left',
                    }, {
                        field: 'emergencyAlarmQuantity',
                        title: '紧急时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.emergencyAlarmQuantity * 1 === 0) {
                                return '<a style="color: #333;">' + row.emergencyAlarmQuantity + '</a>';
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'1\')">' + row.emergencyAlarmQuantity + '</a>';
                            }
                        }
                    }, {
                        field: 'importantAlarmQuantity',
                        title: '重要时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.importantAlarmQuantity * 1 === 0) {
                                return '<a style="color: #333;">' + row.importantAlarmQuantity + '</a>';
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'2\')">' + row.importantAlarmQuantity + '</a>';
                            }
                        }
                    }, {
                        field: 'generalAlarmQuantity',
                        title: '一般时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.generalAlarmQuantity * 1 === 0) {
                                return '<a style="color: #333;">' + row.generalAlarmQuantity + '</a>';
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'3\')">' + row.generalAlarmQuantity + '</a>';
                            }
                        }
                    }, {
                        field: 'nullAlarmQuantity',
                        title: '优先级为空(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.nullAlarmQuantity * 1 === 0) {
                                return '<a style="color: #333;">' + row.nullAlarmQuantity + '</a>';
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'9\')">' + row.nullAlarmQuantity + '</a>';
                            }
                        }
                    },
                    {
                        field: 'totalAlarmQuantity',
                        title: '合计(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'-1\')">' + row.totalAlarmQuantity + '</a>';
                        }
                    }
                    ]
                };
                $('#floodTable').bootstrapTable(options);
                $('#floodTable').bootstrapTable('refreshOptions', options);
                if (results == undefined) {
                    results = [];
                }
                $("#floodTable").bootstrapTable("load", results);
            },
            /**
             * 初始化Table数据
             * @param data
             */
            initTableDisplaycj: function (data) {
                var results = JSON.parse(data);
                var options = {
                    // detailView: true, //父子表
                    striped: true, //是否显示行间隔色
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    height: 350,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'sname',
                        title: '装置',
                        align: 'left',
                    }, {
                        field: 'emergencyAlarmQuantity',
                        title: '紧急时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.emergencyAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.emergencyAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'1\')">' + row.emergencyAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'importantAlarmQuantity',
                        title: '重要时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.importantAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.importantAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'2\')">' + row.importantAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'generalAlarmQuantity',
                        title: '一般时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.generalAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.generalAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'3\')">' + row.generalAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'nullAlarmQuantity',
                        title: '优先级为空(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.nullAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.nullAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'9\')">' + row.nullAlarmQuantity + '</a>'
                            }
                        }
                    },
                    {
                        field: 'totalAlarmQuantity',
                        title: '合计(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',\'-1\')">' + row.totalAlarmQuantity + '</a>'
                        }
                    }
                    ]
                };
                $('#floodTable').bootstrapTable(options);
                $('#floodTable').bootstrapTable('refreshOptions', options);
                if (results == undefined) {
                    results = [];
                }
                $("#floodTable").bootstrapTable("load", results);
            },
            /**
             * 报警次数详情
             */
            showOperateDetail: function (unitName, unitId, priorityName) {
                layer.open({
                    type: 2,
                    title: '报警时长详情',
                    closeBtn: 1,
                    area: ['85%', '420px'],
                    shadeClose: false,
                    content: 'AlarmDurationDtl.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        let obj = {
                            unitName: unitName,
                            unitId: unitId,
                            priorityName: priorityName,
                            startTime: moment(page.data.param.startTime).format('YYYY-MM-DD HH:mm:ss'),
                            endTime: moment(page.data.param.endTime).format('YYYY-MM-DD HH:mm:ss'),
                            alarmFlagId: page.data.param.alarmFlagId
                        };
                        iframeWin.page.logic.setData(obj);
                    }
                });
            },
            /**
             * 设置日期插件
             */
            initTime: function () {
                var myDate = new Date();
                var start = laydate.render({
                    elem: '#startTime', //指定元素
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd', //日期格式
                    value: getStartTime,
                    max: getEndTime, //最大日期
                });
                var end = laydate.render({
                    elem: '#endTime',
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd',
                    value: getEndTime,
                    max: getEndTime,
                });
                $('#startTime').attr('maxDate', getEndTime)
                $('#endTime').attr('maxDate', getEndTime)
            },
            /**
             * 获取显示时间
             */
            getShowTime: function () {
                $.ajax({
                    url: API_URLS.getShowTime,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        const dataArr = $.ET.toObjectArr(result);
                        getStartTime = dataArr[0].value;
                        getEndTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');

                        // 设置时间插件
                        page.logic.initTime();
                        // 自动执行查询
                        document.getElementById("btnSearch").click();
                    },
                    error: function (result) {
                        const errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },

            /**
             * 初始化优先级选择器
             */
            initPriority: function () {
                OPAL.ui.getComboMultipleSelect('priority', API_URLS.priority, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    const treeView = $("#priority").combotree('tree');
                    const nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#priority").combotree("checkAllNodes");
                });
            },

            /**
             * 初始化报警标识选择器
             */
            initAlarmFlagList: function () {
                OPAL.ui.getComboMultipleSelect('alarmFlagId', API_URLS.alarmFlagList, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    const treeView = $("#alarmFlagId").combotree('tree');
                    const nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#alarmFlagId").combotree("checkAllNodes");
                });
            },
            /**
             * 加载下方表格数据
             */
            queryMostOperate: function () {
                $("#MostAlarmOperateTable").bootstrapTable('refresh', {
                    "url": API_URLS.alarmDurStattPage,
                    "pageNumber": 1
                });
            },
            //初始化下方表格
            initOpetateTable: function () {
                page.logic.initBootstrapTable("MostAlarmOperateTable", {
                    columns: [{
                        title: "序号", formatter: function (value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1) * data.pageSize;
                        }, rowspan: 1, align: 'center', width: '80px'
                    }, {
                        field: 'unitName', title: '装置', align: 'center', width: '180px'
                    }, {
                        field: 'prdtCellName', title: '生产单元', align: 'center', width: '180px'
                    }, {
                        field: 'location', title: '参数名称', align: 'center',
                    }, {
                        field: 'tag', title: '位号', align: 'center', width: '200px'
                    }, {
                        field: 'monitorTypeStr', title: '专业', align: 'center', width: '60px'
                    }, {
                        field: 'priorityName', title: "优先级", align: 'center', width: '60px'
                    }, {
                        field: 'alarmTime', title: '报警时间', align: 'center', width: '200px'
                    }, {
                        field: 'recoveryTime', title: '结束时间', align: 'center', width: '200px'
                    }, {
                        field: 'continuousHour', title: '时长(分钟)', align: 'center', width: '80px'
                    }, {
                        field: 'alarmFlagName', title: '报警等级', align: 'center', width: '80px'
                    }]
                    // onExpandRow: function (index, row, $detail) {
                    //     page.data.subParam.unitIds = row['unitCode'];
                    //     page.data.subParam.alarmFlagId = row['alarmFlagId'];
                    //     page.data.subParam.tag = row['tag'];
                    //     page.data.subParam.priority = row['priority'];
                    //     page.logic.initCausalSubTable(index, row, $detail);
                    // }
                }, page.logic.queryParams);
                var tableOption = $('#MostAlarmOperateTable').bootstrapTable('getOptions');
                //tableOption.pageList = [20];
                $("#MostAlarmOperateTable").bootstrapTable('refreshOptions', tableOption);
            },

            /**
             * 加载下方车间表格数据
             */
            queryMostOperatecj: function () {
                $("#MostAlarmOperateTablecj").bootstrapTable('refresh', {
                    "url": API_URLS.alarmDurStattPage,
                    "pageNumber": 1
                });
            },
            //初始化下方车间表格
            initOpetateTablecj: function () {
                page.logic.initBootstrapTablecj("MostAlarmOperateTablecj", {
                    // detailView: true,
                    // cache: false,
                    columns: [{
                        title: "序号", formatter: function (value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1) * data.pageSize;
                        }, rowspan: 1, align: 'center', width: '80px'
                    }, {
                        field: 'unitName', title: '装置', align: 'center', width: '180px'
                    }, {
                        field: 'prdtCellName', title: '生产单元', align: 'center', width: '180px'
                    }, {
                        field: 'location', title: '参数名称', align: 'center',
                    }, {
                        field: 'tag', title: '位号', align: 'center', width: '200px'
                    }, {
                        field: 'monitorTypeStr', title: '专业', align: 'center', width: '60px'
                    }, {
                        field: 'priorityName', title: "优先级", align: 'center', width: '60px'
                    }, {
                        field: 'alarmTime', title: '报警时间', align: 'center', width: '200px'
                    }, {
                        field: 'recoveryTime', title: '结束时间', align: 'center', width: '200px'
                    }, {
                        field: 'continuousHour', title: '时长(分钟)', align: 'center', width: '80px'
                    }, {
                        field: 'alarmFlagName', title: '报警等级', align: 'center', width: '80px'
                    }]/*,
                    onExpandRow: function (index, row, $detail) {
                        page.data.subParam.unitIds = row['unitCode'];
                        page.data.subParam.alarmFlagId = row['alarmFlagId'];
                        page.data.subParam.tag = row['tag'];
                        page.data.subParam.priority = row['priority'];
                        page.logic.initCausalSubTablecj(index, row, $detail);
                    }*/
                }, page.logic.queryParams);
                var tableOption = $('#MostAlarmOperateTablecj').bootstrapTable('getOptions');
                $("#MostAlarmOperateTablecj").bootstrapTable('refreshOptions', tableOption);
            },

            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                if (page.data.click == 'floodAlarmChartcj') {
                    param.unitCodes = []
                    param.prdtCellId = []
                } else if (page.data.click == 'floodAlarmChartzz') {
                    param.prdtCellId = []
                }
                return $.extend(page.data.param, param);
            },

            /**
             * 查询子表格参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            subQueryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    alarmFlagId: alarmFlagId,
                    // isElimination: $("#isElimination").val(),
                    tag: tag,
                    priority: priority,
                    now: Math.random()
                };
                return $.extend(page.data.subParam, param);
            },

            /**
             * 初始化二级列表
             */
            initCausalSubTable: function (index, row, $detail) {
                alarmFlagId = row.alarmFlagId;
                tag = row.tag;
                priority = row.priority;
                var subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                OPAL.ui.initBootstrapTable(subId, {
                    url: alarmDurStattUrl,
                    striped: true,
                    pagination: false,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        align: 'center',
                        width: '80'
                    }, {
                        field: 'alarmTime',
                        title: '报警时间',
                        align: 'center',
                    }, {
                        field: 'continuousHour',
                        title: '时长(分钟)',
                        align: 'right',
                    }, {
                        field: 'recoveryTime',
                        title: '恢复时间',
                        align: 'center',
                    }, {
                        field: 'eventTypeName',
                        title: '事件类型',
                        align: 'left',
                    }, {
                        field: 'monitorTypeStr',
                        title: '专业',
                        align: 'left',
                    }],
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            /*"pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],*/
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                }, page.logic.subQueryParams)
            },

            initBootstrapTable: function (tableID, config, queryParams) {
                var _config = {
                    method: 'get',
                    url: '',
                    cache: false,
                    pagination: true, //启动分页
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "server", // 表示服务端请求 后台分页
                    //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder
                    //设置为limit可以获取limit, offset, search, sort, order
                    queryParamsType: "undefined",
                    queryParams: queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#' + tableID).find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                };
                $.extend(true, _config, config);
                $('#' + tableID).bootstrapTable(_config);
            },

            initBootstrapTablecj: function (tableID, config, queryParams) {
                var _config = {
                    method: 'get',
                    url: '',
                    cache: false,
                    pagination: true, //启动分页
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "server", // 表示服务端请求 后台分页
                    //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder
                    //设置为limit可以获取limit, offset, search, sort, order
                    queryParamsType: "undefined",
                    queryParams: queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#' + tableID).find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                };
                $.extend(true, _config, config);
                $('#' + tableID).bootstrapTable(_config);
            },

            /**
             * 初始化二级列表
             */
            initCausalSubTablecj: function (index, row, $detail) {
                alarmFlagId = row.alarmFlagId;
                tag = row.tag;
                priority = row.priority;
                var subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                OPAL.ui.initBootstrapTable(subId, {
                    url: alarmDurStattUrl,
                    striped: true,
                    pagination: false,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        align: 'center',
                        width: '80'
                    }, {
                        field: 'alarmTime',
                        title: '报警时间',
                        align: 'center',
                    }, {
                        field: 'continuousHour',
                        title: '时长(分钟)',
                        align: 'right',
                    }, {
                        field: 'recoveryTime',
                        title: '结束时间',
                        align: 'center',
                    }, {
                        field: 'eventTypeName',
                        title: '事件类型',
                        align: 'left',
                    }, {
                        field: 'monitorTypeStr',
                        title: '专业',
                        align: 'left',
                    }],
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            /*"pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],*/
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                }, page.logic.subQueryParams)
            },
        }
    };
    page.init();
    window.page = page;
});