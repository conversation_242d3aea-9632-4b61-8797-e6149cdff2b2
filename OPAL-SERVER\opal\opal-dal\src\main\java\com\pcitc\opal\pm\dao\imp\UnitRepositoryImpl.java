package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.UnitRepositoryCustom;
import com.pcitc.opal.pm.pojo.Unit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Unit实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_UnitRepositoryImpl
 * 作       者：xuelei.wang
 * 创建时间：2017-12-11
 * 修改编号：1
 * 描       述：Unit实体的Repository实现
 */
@SuppressWarnings("all")
public class UnitRepositoryImpl extends BaseRepository<Unit, Long> implements UnitRepositoryCustom {
    private static final Logger logger = LoggerFactory.getLogger(UnitRepositoryImpl.class);

    /**
     * 装置唯一性校验
     *
     * @param unit 生产装置实体
     * @return 查询返回信息类
     * <AUTHOR> 2017-12-11
     */
    @Override
    public CommonResult unitValidation(Unit unit) {
        CommonResult commonResult = new CommonResult();
        CommonProperty commonProperty = new CommonProperty();
        try {
            //1.名称和车间ID联合唯一性校验
            StringBuilder jpql = new StringBuilder(
                    "from Unit t where t.name=:name and t.workshopId=:workshopId  and t.unitId<>:unitId and t.companyId=:companyId");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("name", unit.getName());
            paramList.put("workshopId", unit.getWorkshopId());
            paramList.put("unitId", unit.getUnitId()==null?0:unit.getUnitId());
            paramList.put("companyId",commonProperty.getCompanyId());

            long count = this.getCount(jpql.toString(), paramList);
            if (count > 0) {
                throw new Exception("该车间下此名称已存在！");
            }
            //2.简称和车间ID联合唯一性校验
            jpql = new StringBuilder(
                    "from Unit t where t.sname=:sname and t.workshopId=:workshopId and t.unitId<>:unitId and t.companyId=:companyId");
            paramList = new HashMap<String, Object>();
            paramList.put("sname", unit.getSname());
            paramList.put("workshopId", unit.getWorkshopId());
            paramList.put("unitId", unit.getUnitId());
            paramList.put("companyId",commonProperty.getCompanyId());

            count = this.getCount(jpql.toString(), paramList);
            if (count > 0) {
                throw new Exception("该车间下此简称已存在！");
            }
            //3.“标准编码”唯一性校验
            jpql = new StringBuilder(
                    "from Unit t where t.stdCode=:stdCode  and t.unitId<>:unitId and t.companyId=:companyId");
            paramList = new HashMap<String, Object>();
            paramList.put("stdCode", unit.getStdCode());
            paramList.put("unitId", unit.getUnitId()==null?0:unit.getUnitId());
            paramList.put("companyId",commonProperty.getCompanyId());
            count = this.getCount(jpql.toString(), paramList);
            if (count > 0) {
                throw new Exception("此标准编码已存在！");
            }
        } catch (Exception e) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(e.getMessage());
        }
        return commonResult;
    }

    /**
     * 新增装置
     *
     * @param unit 添加的实体
     * <AUTHOR> 2017-12-11
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addUnit(Unit unit) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(unit);
            commonResult.setResult(unit);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 删除装置
     *
     * @param unitIds 装置ID集合
     * @return 消息结果类
     * <AUTHOR> 2017-12-11
     */
    @Override
    public CommonResult deleteUnit(Long[] unitIds) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        CommonProperty commonProperty = new CommonProperty();
        try {
            String hql = "from Unit t where t.unitId in (:unitIds) and t.companyId=:companyId";
            Map<String, Object> paramList = new HashMap<>();
            List<Long> unitList = Arrays.asList(unitIds);
            paramList.put("unitIds", unitList);
            paramList.put("companyId",commonProperty.getCompanyId());

            TypedQuery<Unit> query = getEntityManager().createQuery(hql, Unit.class);
            this.setParameterList(query, paramList);
            List<Unit> unitsList = query.getResultList();
            unitsList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 更新生产装置
     *
     * @param unit 装置实体
     * @return 消息结果类
     * <AUTHOR> 2017-12-11
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult updateUnit(Unit unit) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(unit);
            commonResult.setResult(unit);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 获取装置单个实体
     *
     * @param unitId 装置ID
     * @return 装置实体
     * <AUTHOR> 2017-12-11
     */
    @Override
    public Unit getSingleUnit(Long unitId) {
        try {
            return getEntityManager().find(Unit.class, unitId);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取装置实体
     *
     * @param unitIds 装置ID集合
     * @return 装置实体集合
     * <AUTHOR> 2017-12-11
     */
    @Override
    public List<Unit> getUnit(Long[] unitIds) {
        try {
            // 查询字符串
            String hql = "from Unit t where t.companyId=:companyId ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (unitIds != null && unitIds.length > 0) {
                hql += " and t.unitId in (:unitIds)";
                List<Long> unitIdsList = Arrays.asList(unitIds);
                paramList.put("unitIds", unitIdsList);
            }
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            TypedQuery<Unit> query = getEntityManager().createQuery(hql, Unit.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取装置分页数据
     *
     * @param factoryId   工厂ID
     * @param workshopIds 车间ID集合
     * @param name        名称或简称
     * @param stdCode     编码
     * @param inUse       是否启用
     * @param page        分页信息
     * @return 装置分页数据
     * <AUTHOR> 2017-12-11
     */
    @Override
    public PaginationBean<Unit> getUnitList(Long factoryId, Long[] workshopIds, String name, String stdCode, Integer inUse, Pagination page) throws Exception {
        CommonProperty commonProperty = new CommonProperty();
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from Unit t inner join fetch t.workshop ws inner join fetch ws.factory f where 1=1 and t.companyId=:companyId");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 名称/简称
            if (!StringUtils.isEmpty(name)) {
                hql.append("  and (t.name like :name or t.sname like :name)");
                paramList.put("name", "%" + this.sqlLikeReplace(name) + "%");
            }
            if (workshopIds != null && workshopIds.length != 0) {
                hql.append("  and (t.workshopId in :workshopIds)");
                paramList.put("workshopIds", Arrays.asList(workshopIds));
            }
            if (!StringUtils.isEmpty(stdCode)) {
                hql.append("  and (upper(t.stdCode) like upper(:stdCode))");
                paramList.put("stdCode", "%" + this.sqlLikeReplace(stdCode) + "%");
            }
            if (inUse != null) {
                hql.append("  and (t.inUse =:inUse)");
                paramList.put("inUse", inUse);
            }
            paramList.put("companyId",commonProperty.getCompanyId());
            hql.append(" order by f.sortNum,f.sname,ws.sortNum,ws.sname,t.sortNum,t.name,t.sname asc");
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public Unit getUnitByStdCode(String unitId) {
        CommonProperty commonProperty = new CommonProperty();
        try {
            // 查询字符串
            String hql = "from Unit t where t.stdCode = :unitId and t.companyId=:companyId";
            Map<String, Object> paramList = new HashMap<String, Object>();

            paramList.put("unitId", unitId);
            paramList.put("companyId",commonProperty.getCompanyId());
            TypedQuery<Unit> query = getEntityManager().createQuery(hql, Unit.class);
            this.setParameterList(query, paramList);
            return query.getSingleResult();
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public Unit getUnitByStdCode(String unitId, Integer companyId) {
        try {
            // 查询字符串
            String hql = "from Unit t where t.stdCode = :unitId ";
            Map<String, Object> paramList = new HashMap<String, Object>();

            paramList.put("unitId", unitId);
            TypedQuery<Unit> query = getEntityManager().createQuery(hql, Unit.class);
            this.setParameterList(query, paramList);
            return query.getSingleResult();
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public Unit getUnitInfoByStdCode(String stdCode) {
        CommonProperty commonProperty = new CommonProperty();
        try {
            // 查询字符串
            String hql = "from Unit t where t.stdCode = :stdCode and t.companyId=:companyId";
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("stdCode", stdCode);
            paramList.put("companyId",commonProperty.getCompanyId());
            Query query = getEntityManager().createQuery(hql, Unit.class);
            this.setParameterList(query, paramList);
            if(query.getResultList().size()==0){
                return null;
            }else{
                Query q = getEntityManager().createQuery(hql, Unit.class);
                this.setParameterList(q, paramList);
                return (Unit) q.getSingleResult();
            }
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public List<Unit> getUnitByCompanyId(Long companyId) {
        String hql = "from Unit where companyId = :companyId";

        TypedQuery<Unit> query = getEntityManager().createQuery(hql, Unit.class);
        query.setParameter("companyId", (Math.toIntExact(companyId)));

        return query.getResultList();
    }

    @Override
    public boolean isFilterWorkTeam(String[] unitId) {
        String hql = "select count(1) from Unit where stdCode in (:unitId) group by shiftAreaId";


        TypedQuery<Long> query = getEntityManager().createQuery(hql, Long.class);
        query.setParameter("unitId", Arrays.asList(unitId));

        List<Long> resultList = query.getResultList();

        return resultList.size() == 1;
    }
}
