package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;
import com.pcitc.opal.pm.pojo.DcsCode;
import com.pcitc.opal.pm.pojo.PrdtCell;
import com.pcitc.opal.pm.pojo.Unit;

/*
 * 报警生产单元对照表实体
 * 模块编号：pcitc_opal_bll_class_AlarmPrdtCellCompEntity
 * 作	者：jiangtao.xue
 * 创建时间：2018/4/4
 * 修改编号：1
 * 描	述：报警生产单元对照表实体
 */
public class AlarmPrdtCellCompEntity extends BasicEntity {

    /**
     * 报警生产单元对照ID
     */
    private Long alarmPrdtCellCompId;

    /**
     * DCS编码ID
     */
    private Long dcsCodeId;
    /**
     * DCS名称
     */
    private String dcsName;

    /**
     * DCS编码ID
     */
    private Long opcCodeId;
    /**
     * DCS名称
     */
    private String opcName;

    /**
     * 源报警生产单元
     */
    private String prdtCellSource;

    /**
     * 装置编码
     */
    private String unitId;

    /**
     * 生产单元ID
     */
    private Long prdtCellId;

    /**
     * 装置简称
     */
    private String unitName;

    /**
     * 生产单元
     */
    private String prdtCellName;

    private Integer companyId;

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }
    public Long getOpcCodeId() {
        return opcCodeId;
    }

    public void setOpcCodeId(Long opcCodeId) {
        this.opcCodeId = opcCodeId;
    }

    public String getOpcName() {
        return opcName;
    }

    public void setOpcName(String opcName) {
        this.opcName = opcName;
    }

    public Long getAlarmPrdtCellCompId() {
        return alarmPrdtCellCompId;
    }

    public void setAlarmPrdtCellCompId(Long alarmPrdtCellCompId) {
        this.alarmPrdtCellCompId = alarmPrdtCellCompId;
    }

    public Long getDcsCodeId() {
        return dcsCodeId;
    }

    public void setDcsCodeId(Long dcsCodeId) {
        this.dcsCodeId = dcsCodeId;
    }

    public String getPrdtCellSource() {
        return prdtCellSource;
    }

    public void setPrdtCellSource(String prdtCellSource) {
        this.prdtCellSource = prdtCellSource;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public Long getPrdtCellId() {
        return prdtCellId;
    }

    public void setPrdtCellId(Long prdtCellId) {
        this.prdtCellId = prdtCellId;
    }

    public String getDcsName() {
        return dcsName;
    }

    public void setDcsName(String dcsName) {
        this.dcsName = dcsName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPrdtCellName() {
        return prdtCellName;
    }

    public void setPrdtCellName(String prdtCellName) {
        this.prdtCellName = prdtCellName;
    }
}
