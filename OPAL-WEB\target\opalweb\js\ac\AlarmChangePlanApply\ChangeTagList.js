$(function() {
    var alarmUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getMostFrequentAlarmTop20';
    var operateUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getMostFrequentOperateTop20';
    var persistentUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getPersistentAlarmAnalysisTop20';
    var shakeUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getShakeAlarmAnalysisTop20';
    var priorityUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getAlarmPriorityAssessTable';
    var tagQueryUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getAlarmPointView';
    var getAlarmIsoUrl = OPAL.API.commUrl + '/getAlarmIso';
    var getTimeUrl = OPAL.API.commUrl + '/getQueryStartAndEndDate';
    var alarmPriorityListUrl = OPAL.API.commUrl + '/getAlarmPriorityList';
    var getAlarmPriorityAssessStatisticDatavUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getAlarmPriorityAssessGraphical';
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var alarmChangeItemArr, param;
    var flag = false; //是否可点击圆环
    var standardEmergency, standardImportant, standardGeneral; //国际标准数据
    window.pageLoadMode = PageLoadMode.None;
    var startTimeStamp, endTimeStamp;
    var page = {
        /**
         * 初始化
         */
        init: function() {
            this.bindUI();
            /**
             * 日期扩展
             */
            OPAL.util.extendDate();
            /**
             * 初始化日期时间选择控件组
             */
            startTimeStamp = new Date().getTime();
            OPAL.ui.initDateTimePicker({
                "ctrId": "alarmTime"
            })
            endTimeStamp = new Date ().getTime();
            //表格初始化
            page.logic.initTable();
            //初始化优先级
            page.logic.initAlarmPriorityList();
            // 点击图表改变优先级
            page.logic.changePriorityTable();
            page.logic.initCharts();
            
        },
        bindUI: function() {
            //表格自适应页面拉伸宽度
            $(window).resize(function () {
                $('#alarmTable').bootstrapTable('resetView');
                $('#operateTable').bootstrapTable('resetView');
                $('#persistentTable').bootstrapTable('resetView');
                $('#shakeTable').bootstrapTable('resetView');
                $('#priorityTable').bootstrapTable('resetView');
            });
            $('#searched').click(function() {
                page.logic.search();
            })
            $('#tagBtn').click(function() {
                $('#tagDiv').show();
                $("#timeDiv").hide();
            })
            $('.ab').click(function() {
                $('#tagDiv').hide();
                $("#timeDiv").show();
            })
        },
        data: {
            // 设置查询参数
            param: {}
        },
        logic: {
            initTable: function() {
                page.logic.initAlarmTable();
                page.logic.initOperateTable();
                page.logic.initPersistentTable();
                page.logic.initShakeTable();
                page.logic.initPriorityTable();
                page.logic.initTagQueryTable();
            },
            //查询
            search: function() {
                $('.alarmClick').removeClass('alarm-priority-add-class');
                page.logic.getTime();
                if ($("#tagDiv").is(':hidden')) {
                    if ($("#alarmTime").val() == '') {
                        layer.msg('请输入时间!');
                        return;
                    }
                    flag = true;
                    page.data.param = {
                        'unitId': $('#unitIds').val(),
                        'endTime': $('#alarmTime').val()
                    }
                    $("#alarmTable").bootstrapTable('refresh', {
                        "url": alarmUrl,
                        "pageNumber": 1
                    });
                    $("#operateTable").bootstrapTable('refresh', {
                        "url": operateUrl,
                        "pageNumber": 1
                    });
                    $("#persistentTable").bootstrapTable('refresh', {
                        "url": persistentUrl,
                        "pageNumber": 1
                    });
                    $("#shakeTable").bootstrapTable('refresh', {
                        "url": shakeUrl,
                        "pageNumber": 1
                    });
                    page.logic.searchPriority("-1");
                    page.logic.initCharts(page.data.param);
                } else {
                    page.data.param = {
                        'unitId': $('#unitIds').val(),
                        'tag': $('#searchTag').val()
                    }
                    $("#tagQueryTable").bootstrapTable('refresh', {
                        "url": tagQueryUrl,
                        "pageNumber": 1
                    });
                }

            },
            setData: function(data) {
                param = data;
                $('#unitId').html(data.unitName);
                $('#planCode').html(data.planCode);
                $("#unitIds").val(data.unitId);
                setTimeout(function() {
                    page.logic.search();
                }, endTimeStamp-startTimeStamp);
            },
            searchPriority: function(priority) {
                var priorityData = page.data.param;
                priorityData.priority = priority;
                $.ajax({
                    url: priorityUrl,
                    async: true,
                    data: priorityData,
                    dataType: "JSON",
                    type: 'GET',
                    success: function(result) {
                        var res = $.ET.toObjectArr(result);
                        $("#priorityTable").bootstrapTable("removeAll");
                        if (result == null || result == undefined || result.length == 0) {
                            return;
                        }
                        $("#priorityTable").bootstrapTable("load", res);
                    }
                })
            },
            initAlarmTable: function() {
                OPAL.ui.initBootstrapTable("alarmTable", {
                    cache: false,
                    pagination: false,
                    columns: [{
                        title: "排名",
                        field: '',
                        formatter: function(value, row, index) {
                            var tableOption = $('#alarmTable').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            var value = (index + 1) + (pageNumber - 1) * pageSize;
                            if (value < 4) {
                                return "<span id='spanid' class='btn-tag-rank1'>" + value + "</span>";
                            } else {
                                return "<span id='spanid' class='btn-tag-rank'>" + value + "</span>";
                            }
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "报警数",
                        field: 'alarmCount',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "操作",
                        field: '',
                        rowspan: 1,
                        align: 'center',
                        formatter: page.logic.changedStatus,
                        width: '90px'
                    }],
                    responseHandler: function(res) {
                        var item = {
                            "rows": $.ET.toObjectArr(res)
                        }
                        return item;
                    },
                }, page.logic.queryParams)
            },
            initOperateTable: function() {
                OPAL.ui.initBootstrapTable("operateTable", {
                    cache: false,
                    pagination: false,
                    columns: [{
                        title: "排名",
                        field: '',
                        formatter: function(value, row, index) {
                            var tableOption = $('#operateTable').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            var value = (index + 1) + (pageNumber - 1) * pageSize;
                            if (value < 4) {
                                return "<span id='spanid' class='btn-tag-rank1'>" + value + "</span>";
                            } else {
                                return "<span id='spanid' class='btn-tag-rank'>" + value + "</span>";
                            }
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "操作数",
                        field: 'alarmCount',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "操作",
                        field: '',
                        rowspan: 1,
                        align: 'center',
                        formatter: page.logic.changedStatus,
                        width: '90px'
                    }],
                    responseHandler: function(res) {
                        var item = {
                            "rows": $.ET.toObjectArr(res)
                        }
                        return item;
                    },
                }, page.logic.queryParams)
            },
            initPersistentTable: function() {
                OPAL.ui.initBootstrapTable("persistentTable", {
                    cache: false,
                    pagination: false,
                    columns: [{
                        title: "排名",
                        field: '',
                        formatter: function(value, row, index) {
                            var tableOption = $('#persistentTable').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            var value = (index + 1) + (pageNumber - 1) * pageSize;
                            if (value < 4) {
                                return "<span id='spanid' class='btn-tag-rank1'>" + value + "</span>";
                            } else {
                                return "<span id='spanid' class='btn-tag-rank'>" + value + "</span>";
                            }
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "位号",
                        field: 'alarmPointTag',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "时长（小时）",
                        field: 'continuousHour',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "操作",
                        field: '',
                        rowspan: 1,
                        align: 'center',
                        formatter: page.logic.changedStatus,
                        width: '90px'
                    }],
                    responseHandler: function(res) {
                        var item = {
                            "rows": $.ET.toObjectArr(res)
                        }
                        return item;
                    },
                }, page.logic.queryParams)
            },
            initShakeTable: function() {
                OPAL.ui.initBootstrapTable("shakeTable", {
                    cache: false,
                    pagination: false,
                    columns: [{
                        title: "排名",
                        field: '',
                        formatter: function(value, row, index) {
                            var tableOption = $('#shakeTable').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            var value = (index + 1) + (pageNumber - 1) * pageSize;
                            if (value < 4) {
                                return "<span id='spanid' class='btn-tag-rank1'>" + value + "</span>";
                            } else {
                                return "<span id='spanid' class='btn-tag-rank'>" + value + "</span>";
                            }
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "报警数",
                        field: 'alarmCount',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "操作",
                        field: '',
                        rowspan: 1,
                        align: 'center',
                        formatter: page.logic.changedStatus,
                        width: '90px'
                    }],
                    responseHandler: function(res) {
                        var item = {
                            "rows": $.ET.toObjectArr(res)
                        }
                        return item;
                    },
                }, page.logic.queryParams)
            },
            initPriorityTable: function() {
                $("#priorityTable").bootstrapTable({
                    cache: false,
                    columns: [{
                        title: "序号",
                        field: '',
                        formatter: function(value, row, index) {
                            var value = index + 1;
                            return value;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "优先级",
                        field: 'priorityName',
                        rowspan: 1,
                        align: 'center',
                        width: '60px'
                    }, {
                        title: "报警数",
                        field: 'alarmCount',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "操作",
                        field: '',
                        rowspan: 1,
                        align: 'center',
                        formatter: page.logic.changedStatus,
                        width: '90px'
                    }],
                    formatNoMatches: function() {
                        return "";
                    },
                    formatLoadingMessage: function() {
                        return "";
                    },
                    sidePagination: "client",
                    cache: false,
                    pagination: true, //是否分页
                    pageSize: 10,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [10, 20, 50, 100],
                    striped: false
                });
            },
            initTagQueryTable: function() {
                OPAL.ui.initBootstrapTable("tagQueryTable", {
                    cache: false,
                    columns: [{
                        title: "序号",
                        field: '',
                        formatter: function(value, row, index) {
                            var tableOption = $('#tagQueryTable').bootstrapTable('getOptions');
                            var pageNumber = tableOption.pageNumber;
                            var pageSize = tableOption.pageSize;
                            var value = (index + 1) + (pageNumber - 1) * pageSize;
                            if (value < 4) {
                                return "<span id='spanid' class='btn-tag-rank1'>" + value + "</span>";
                            } else {
                                return "<span id='spanid' class='btn-tag-rank'>" + value + "</span>";
                            }
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '75px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "位置",
                        field: 'location',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "操作",
                        field: '',
                        rowspan: 1,
                        align: 'center',
                        formatter: page.logic.changedStatus,
                        width: '90px'
                    }],
                    responseHandler: function(res) {
                        var item = {
                            "rows": $.ET.toObjectArr(res),
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                        }
                        return item;
                    },
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             */
            queryParams: function(p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            //是否可调整
            changedStatus: function() {
                var row = arguments[1];
                if (row.changeStatus) {
                    return '<a name="TableEditor" class="btn-tag-style"  href="javascript:window.page.logic.edit(' + row.alarmPointId + ',' + row.alarmFlagId + ')">调整</a>';
                } else {
                    return '<span name="TableEditor" class="btn-alarm-change-style" >调整</span>';
                }
            },
            edit: function(alarmPointId, alarmFlagId) {
                var pageMode = PageModelEnum.NewAdd;
                layer.open({
                    type: 2,
                    title: '维护调整事项',
                    closeBtn: 1,
                    area: ['90%', '95%'],
                    offset: '30px',
                    shadeClose: false,
                    fixed: true,
                    content: 'AlarmChangeItemAddOrEdit.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var rowData = {
                            'planId': param.planId,
                            'alarmPointId': alarmPointId,
                            'alarmFlagId': alarmFlagId,
                            'pageMode': pageMode,
                            'unitId': $('#unitIds').val(),
                            'alarmTime': $('#alarmTime').val()
                        }
                        iframeWin.page.logic.setData(alarmChangeItemArr, rowData);
                    },
                    end: function() {
                        if (window.pageLoadMode = PageLoadMode.Refresh) {
                            page.logic.search();
                            pageLoadMode = PageLoadMode.None;
                            parent.pageLoadMode = PageLoadMode.Refresh;
                        }
                    }
                })
            },
            getTime: function() {
                var data = {
                    "endDate": $("#alarmTime").val()
                }
                $.ajax({
                    url: getTimeUrl,
                    data: data,
                    dataType: 'json',
                    type: 'get',
                    success: function(result) {
                        alarmChangeItemArr = $.ET.toObjectArr(result);
                        var obj = new Object();
                        $.each(alarmChangeItemArr, function(i, el) {
                            if (i != 4 && i != 5) {
                                el.value = OPAL.util.strToDate(el.value).getTime();
                            }
                        })
                    }
                })
            },
            /**
             * 初始化查询 优先级
             */
            initAlarmPriorityList: function() {
                $.ajax({
                    url: alarmPriorityListUrl,
                    async: false,
                    data: '',
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function(result) {
                        var res = $.ET.toObjectArr(result);
                        $('#datastyleBox').attr('dataId', res[0].key); //紧急
                        $('#datastyle1Box').attr('dataId', res[1].key); //重要
                        $('#datastyle2Box').attr('dataId', res[2].key); //一般
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            changePriorityTable: function() {
                $('.alarmClick').click(function() {
                    if (!$("#alarmTime").val() == '' && flag) {
                        $('.alarmClick').removeClass('alarm-priority-add-class');
                        $(this).addClass('alarm-priority-add-class');
                        var dataId = $(this).attr('dataId');
                        switch (dataId) {
                            case '1':
                                priorityId = 1;
                                break;
                            case '2':
                                priorityId = 2;
                                break;
                            case '3':
                                priorityId = 3;
                                break;
                            default:
                                priorityId = -1;
                        }
                        page.logic.searchPriority(priorityId);
                    }
                })
            },
            /**
             * 初始化图表
             */
            initCharts: function(data) {
                if (data != undefined) {
                    $.ajax({
                        url: getAlarmPriorityAssessStatisticDatavUrl,
                        async: false,
                        data: data,
                        dataType: "JSON",
                        contentType: "X-WWW-FORM-URLENCODED",
                        type: 'GET',
                        success: function(res) {
                            var result = $.ET.toObjectArr(res);
                            if (result.length != 0) {
                                $('#sysEmergency').find('h3').html(result[0].sysEmergencyAlarmRate);
                                $('#sysImportant').find('h3').html(result[0].sysImportantAlarmRate);
                                $('#sysGeneral').find('h3').html(result[0].sysGeneralAlarmRate);
                                $('#totalAlarmEvents').val(result[0].totalAlarmEvents);
                            } else {
                                $('#sysEmergency').find('h3').html('0.00%');
                                $('#sysImportant').find('h3').html('0.00%');
                                $('#sysGeneral').find('h3').html('0.00%');
                                $('#totalAlarmEvents').val('0');
                            }
                            var totalAlarmEvents = $('#totalAlarmEvents').val();
                            var sysEmergency = $('#sysEmergency').find('h3').html();
                            var sysImportant = $('#sysImportant').find('h3').html();
                            var sysGeneral = $('#sysGeneral').find('h3').html();

                            function toNumberPoint(percent) {
                                var str = percent.replace("%", "");
                                str = str / 100;
                                return Number(str);
                            }
                            page.logic.setCharts("紧急报警", "datastyle", toNumberPoint(sysEmergency) * totalAlarmEvents, toNumberPoint(standardEmergency) * 100, Number(totalAlarmEvents));
                            page.logic.setCharts("重要报警", "datastyle1", toNumberPoint(sysImportant) * totalAlarmEvents, toNumberPoint(standardImportant) * 100, Number(totalAlarmEvents));
                            page.logic.setCharts("一般报警", "datastyle2", toNumberPoint(sysGeneral) * totalAlarmEvents, toNumberPoint(standardGeneral) * 100, Number(totalAlarmEvents));
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                } else {
                    $.ajax({
                        url: getAlarmIsoUrl,
                        async: false,
                        data: '',
                        dataType: "JSON",
                        contentType: "X-WWW-FORM-URLENCODED",
                        type: 'GET',
                        success: function(result) {
                            var res = $.ET.toObjectArr(result);
                            standardEmergency = res[0].value;
                            standardImportant = res[1].value;
                            standardGeneral = res[2].value;
                            $('#sysEmergency').find('h3').html('0.00%');
                            $('#standardEmergency').find('h3').html(standardEmergency);
                            $('#sysImportant').find('h3').html('0.00%');
                            $('#standardImportant').find('h3').html(standardImportant);
                            $('#sysGeneral').find('h3').html('0.00%');
                            $('#standardGeneral').find('h3').html(standardGeneral);
                            $('#totalAlarmEvents').val('0');
                            var totalAlarmEvents = $('#totalAlarmEvents').val();
                            var sysEmergency = $('#sysEmergency').find('h3').html();
                            var sysImportant = $('#sysImportant').find('h3').html();
                            var sysGeneral = $('#sysGeneral').find('h3').html();

                            function toNumberPoint(percent) {
                                var str = percent.replace("%", "");
                                str = str / 100;
                                return Number(str);
                            }
                            page.logic.setCharts("紧急报警", "datastyle", toNumberPoint(sysEmergency) * totalAlarmEvents, toNumberPoint(standardEmergency) * 100, Number(totalAlarmEvents));
                            page.logic.setCharts("重要报警", "datastyle1", toNumberPoint(sysImportant) * totalAlarmEvents, toNumberPoint(standardImportant) * 100, Number(totalAlarmEvents));
                            page.logic.setCharts("一般报警", "datastyle2", toNumberPoint(sysGeneral) * totalAlarmEvents, toNumberPoint(standardGeneral) * 100, Number(totalAlarmEvents));
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }
            },
            setCharts: function(text, chartId, sysData, standData, total) {
                var myChart = echarts.init(document.getElementById(chartId));
                option = {
                    tooltip: {
                        show: false,
                    },
                    graphic: {
                        type: 'text',
                        left: 'center',
                        top: 'center',
                        style: {
                            text: text,
                            textAlign: 'center',
                            fill: '#313131',
                            width: 30,
                            height: 30
                        }
                    },
                    series: [{
                        type: 'pie',
                        radius: ['60%', '70%'],
                        color: ['#f59c1a', '#d9d9d9'],
                        hoverAnimation: false,
                        avoidLabelOverlap: false,
                        label: {
                            normal: {
                                show: true,
                                position: 'left'
                            },
                        },
                        labelLine: {
                            normal: {
                                show: false
                            }
                        },
                        data: [{
                            value: 0,
                        }, {
                            value: 1,
                        }],

                    }, {
                        type: 'pie',
                        radius: ['48%', '58%'],
                        color: ['#3590e2', '#d9d9d9'],
                        hoverAnimation: false,
                        avoidLabelOverlap: true,
                        label: {
                            normal: {
                                show: false,
                                position: 'right'
                            },
                        },
                        labelLine: {
                            normal: {
                                show: false,
                            }
                        },
                        data: [{
                            value: 0,
                        }, {
                            value: 1
                        }],
                    }]
                };
                myChart.setOption(option);
                if (total == 0) {
                    total = 1
                }
                myChart.setOption({
                    series: [{
                        data: [{
                            value: sysData,
                        }, {
                            value: (total - sysData),
                        }],
                    }, {
                        data: [{
                            value: standData,
                        }, {
                            value: (100 - standData)
                        }],
                    }]
                })
            },
        }
    };
    page.init();
    window.page = page;
});