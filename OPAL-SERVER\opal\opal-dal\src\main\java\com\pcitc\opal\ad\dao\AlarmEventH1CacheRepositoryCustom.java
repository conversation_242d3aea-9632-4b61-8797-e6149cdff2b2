package com.pcitc.opal.ad.dao;

import com.pcitc.opal.ad.pojo.AlarmEventCache;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.AlarmEventH1Cache;

import java.util.Date;
import java.util.List;

/*
 * AlarmEventH1Cache实体的Repository的JPA自定义接口
 * 模块编号：pcitc_opal_dal_interface_AlarmEventH1CacheRepositoryCustom
 * 作	者：zheng.yang
 * 创建时间：2017/08/01
 * 修改编号：1
 * 描	述：AlarmEventH1Cache实体的Repository的JPA自定义接口
 */
public interface AlarmEventH1CacheRepositoryCustom {

    /**
     * 获取未匹配计量单位列表
     *
     * @return 未匹配计量单位列表
     * <AUTHOR> 2019-08-01
     */
    List<AlarmEventH1Cache> getCacheMeasUnitList(Long[] dcsCodeIds, String measUnit, Long reson, Date startTime, Date endTime);
    /**
     * 获取未匹配优先级列表
     *
     * @param priority      优先级
     * @param dcsIds         DCSID集合
     * @param startTime      开始事件
     * @param endTime        结束事件
     * @return      未匹配优先级列表
     * <AUTHOR> 2019-08-01
     */
    List<AlarmEventH1Cache> getCachePriorityList(String priority, Long[] dcsIds, Date startTime, Date endTime);
    /**
     * 获取未匹配报警标识列表
     *
     * @param alarmFlag  报警标识
     * @param dcsIds         DCSID集合
     * @param startTime      开始事件
     * @param endTime        结束事件
     * @return      匹配报警标识列表
     * <AUTHOR> 2019-08-01
     */
    List<AlarmEventH1Cache> getCacheAlarmFlagList(String alarmFlag, Long[] dcsIds, Date startTime, Date endTime);

    /**
     * 获取未匹配报警点列表
     *
     *
     * @param alarmPoint 报警点
     * @param dcsIds         DCSID集合
     * @param startTime      开始事件
     * @param endTime        结束事件
     * @return      匹配报警点列表
     * <AUTHOR> 2019-08-01
     */
    List<AlarmEventH1Cache> getCacheAlarmPointList(String alarmPoint, Long[] dcsIds, Date startTime, Date endTime);

    /**
     * 获取未匹配报警点列表
     *
     *
     * @param alarmPoint 报警点
     * @param dcsIds         DCSID集合
     * @param reason         不一致原因
     * @param startTime      开始事件
     * @param endTime        结束事件
     * @return      匹配报警点列表
     * <AUTHOR> 2019-08-01
     */
    List<AlarmEventH1Cache> getCacheAlarmPointList(String alarmPoint, Long[] dcsIds, Long reason,Date startTime, Date endTime);
    /**
     * 获取未匹配生产单元列表
     *
     * @param prdtCell       生产单元
     * @param dcsIds         DCSID集合
     * @param startTime      开始事件
     * @param endTime        结束事件
     * @return      未匹配生产单元列表
     * <AUTHOR> 2019-08-01
     */
    List<AlarmEventH1Cache> getCachePrdtCellList(String prdtCell, Long[] dcsIds,Date startTime,Date endTime);

    /**
     * 获取未匹配事件名称列表
     *
     *
     * @param eventName
     * @param  eventType 事件名称
     * @param dcsIds         DCSID集合
     * @param startTime      开始事件
     * @param endTime        结束事件
     * @return      未匹配事件名称列表
     * <AUTHOR> 2019-08-01
     */
//    List<AlarmEventH1Cache> getCacheEventNameList(String eventName, String eventType, Long[] dcsIds, Date startTime, Date endTime);

    /**
     * 获取未匹配事件类型列表
     *
     *
     * @param eventType      事件类型
     * @param dcsIds         DCSID集合
     * @param startTime      开始事件
     * @param endTime        结束事件
     * @return      未匹配事件类型列表
     * <AUTHOR> 2019-08-01
     */
    List<AlarmEventH1Cache> getCacheEventTypeList(String eventType, Long[] dcsIds, Date startTime, Date endTime);

    /**
     * 获取未配置数据分页
     *
     * @param dcsCodeIds        DcsCodeID
     * @param prdtCell         生产单元
     * @param alarmPoint       报警点
     * @param alarmFlag        报警标识
     * @param eventTypeSource  源事件类型
     * @param eventNameSource  源事件名称
     * @param priority         优先级
     * @param startTime       查询开始时间
     * @param endTime         查询结束时间
     * @param page
     * @return                未匹配数据
     * @throws Exception
     * <AUTHOR> 2019-08-01
     */
    PaginationBean<AlarmEventH1Cache> getUnconfiguredDataList(Long[] dcsCodeIds,
                                                            String prdtCell,
                                                            String alarmPoint,
                                                            String alarmFlag,
                                                            String eventTypeSource,
                                                            String eventNameSource,
                                                            String priority,
                                                            Date startTime,
                                                            Date endTime,
                                                            Pagination page);

    /**
     * 获取计量单位未配置数据分页
     *
     * @param dcsCodeIds        DcsCodeID
     * @param measUnit         计量单位
     * @param reson            不一致原因
     * @param startTime       查询开始时间
     * @param endTime         查询结束时间
     * @param page
     * @return                未匹配数据
     * @throws Exception
     * <AUTHOR> 2019-08-01
     */
    PaginationBean getUnconfiguredMeasUnitList(Long[] dcsCodeIds,String measUnit,Long reson, Date startTime,Date endTime,Pagination page);

    /**
     * 获取报警点未分类数据分页
     *
     * @param dcsCodeIds        DcsCodeID
     * @param alarmPoint         报警点
     * @param reason            不一致原因
     * @param startTime       查询开始时间
     * @param endTime         查询结束时间
     * @param page
     * @return                未匹配数据
     * @throws Exception
     * <AUTHOR> 2019-08-01
     */
    PaginationBean getUnconfiguredAlarmPointList(Long[] dcsCodeIds,String alarmPoint,Long reason, Date startTime,Date endTime,Pagination page) throws Exception;
}
