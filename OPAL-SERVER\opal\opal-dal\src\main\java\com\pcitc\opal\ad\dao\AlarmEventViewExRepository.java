package com.pcitc.opal.ad.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.ad.pojo.AlarmEventViewEx;

/*
 * AlarmEventViewEx视图的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmEventViewExRepository
 * 作       者：dageng.sun
 * 创建时间：2017/10/25 
 * 修改编号：1
 * 描       述：AlarmEventViewEx视图的Repository实现   
 */
public interface AlarmEventViewExRepository extends JpaRepository<AlarmEventViewEx, Long>, AlarmEventViewExRepositoryCustom {

}
