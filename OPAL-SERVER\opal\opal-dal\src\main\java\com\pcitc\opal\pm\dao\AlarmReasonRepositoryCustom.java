package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.AlarmReason;

import java.util.List;

public interface AlarmReasonRepositoryCustom {

    PaginationBean<AlarmReason> getAlarmReason(Long reasonType, String name, Long inUse, Pagination page);

    CommonResult updateAlarmReason(AlarmReason alarmReason);

    boolean existsAlarmReason(Long reasonType, String name);

    List<AlarmReason> getReasonByType(Long reasonType);
}
