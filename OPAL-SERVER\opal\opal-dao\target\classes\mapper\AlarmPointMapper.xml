<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.pm.dao.AlarmPointDAO">
    <select id="selectDdzhModel" resultType="com.pcitc.opal.pm.dto.ModelDTO">
        select
        tag.tag              as tag,
        tag.des              as des,
        tag.instrmt_priority as instrmtPriority,
        tag.monitor_type     as monitorType,
        workshop.std_code    as workshopCode,
        workshop.sname        as workshopName,
        unit.std_code        as unitCode,
        unit.sname            as unitName,
        tag.rtdb_tag         as rtdbTag,
        prd.name             as prdtcellName,
        tag.location         as location,
        factory.std_code     as factoryCode,
        factory.sname         as factoryName
        from t_pm_alarmpoint tag
        left join t_pm_prdtcell prd on tag.prdtcell_id = prd.prdtcell_id
        left join t_pm_unit unit on prd.unit_code = unit.std_code
        left join t_pm_workshop workshop on unit.workshop_id = workshop.workshop_id
        left join t_pm_factory factory on workshop.factory_id = factory.factory_id
        where tag.in_use = 1
            <if test="dto.tag != null">
                and tag.tag = #{dto.tag}
            </if>
    </select>

    <select id="selectTable" resultType="com.pcitc.opal.pm.vo.AlarmPointTableVO">
        select ap.*,
               apt.name   as alarmPointTypeName,
               prd.sname  as prdtCellSname,
               unit.unit_id    as unitId,
               unit.sname as unitSname,
               mu.name    as measunitName,
               mu.sign as  signName,
        factory.sname         as factoryName,
        workshop.sname        as workshopName,
        ap.is_timeout as isTimeout
        from t_pm_alarmpoint ap
        left join t_pm_prdtcell prd on ap.prdtcell_id = prd.prdtcell_id
        left join t_pm_unit unit on prd.unit_code = unit.std_code
        left join t_pm_measunit mu on ap.measunit_id = mu.measunit_id
        left join t_pm_alarmpointtype apt on ap.alarm_point_type_id = apt.alarm_point_type_id
        left join t_pm_workshop workshop on unit.workshop_id = workshop.workshop_id
        left join t_pm_factory factory on workshop.factory_id = factory.factory_id
        where 1=1 and ap.virtual_flag = 0 and ap.company_Id = #{vo.companyId}
            <if test="vo.unitCodeList != null and vo.unitCodeList.size() > 0">
                and prd.unit_code in
                <foreach collection="vo.unitCodeList" item="unitCode" index="index" open="(" close=")" separator=",">
                    #{unitCode}
                </foreach>
            </if>
            <if test="vo.prdtCellIds != null and vo.prdtCellIds.size() > 0">
                and prd.prdtcell_id in
                <foreach collection="vo.prdtCellIds" item="prdtCellId" index="index" open="(" close=")" separator=",">
                    #{prdtCellId}
                </foreach>
            </if>
            <if test="vo.alarmPointTypeId != null and vo.alarmPointTypeId != -1">
                and ap.alarm_point_type_id = #{vo.alarmPointTypeId}
            </if>
            <if test="vo.tag != null">
                <bind name="tagLike" value="'%' + vo.tag + '%'"/>
                and ap.tag like #{tagLike}
            </if>
            <if test="vo.inUse != null and vo.inUse != -1">
                and ap.in_use = #{vo.inUse}
            </if>
            <if test="vo.instrmtPriority != null and vo.instrmtPriority != -1 and vo.instrmtPriority == 9">
                and ap.instrmt_priority is null
            </if>
            <if test="vo.instrmtPriority != null and vo.instrmtPriority != -1 and vo.instrmtPriority != 9">
                and ap.instrmt_priority = #{vo.instrmtPriority}
            </if>
    </select>
</mapper>
