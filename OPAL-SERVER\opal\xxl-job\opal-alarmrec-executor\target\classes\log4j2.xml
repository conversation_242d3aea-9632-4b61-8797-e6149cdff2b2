<?xml version="1.0" encoding="UTF-8"?>
<!-- Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，
 你会看到log4j2内部各种详细输出。可以设置成OFF(关闭)或Error(只输出错误信息)
-->
<Configuration status="OFF">
    <Properties>
        <Property name="fileName">../opal-alarmrec-log</Property>
        <Property name="backup">../opal-alarmrec-log/backup</Property>
    </Properties>
    <Appenders>
        <Console name="console" target="SYSTEM_OUT">
            <ThresholdFilter level="info" />
            <PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
        </Console>
        <RollingRandomAccessFile name="infoFile" fileName="${fileName}/alarmrec.log"
                                 filePattern="${backup}/$${date:yyyy-MM-dd}/alarmrec-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [%t] %-5level %logger{36} %L %M - %msg%xEx%n" />
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <Filters>
                <!-- 记录info级别以上信息,调试时可以设置成info debug等 -->
                <ThresholdFilter level="info"  />
            </Filters>
            <DefaultRolloverStrategy max="20"/>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="errorFile" fileName="${fileName}/alarmrec-error.log" immediateFlush="false"
                                 filePattern="${backup}/$${date:yyyy-MM-dd}/alarmrec-error-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [%t] %-5level %logger{36} %L %M - %msg%xEx%n" />
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY" />
            </Filters>
            <DefaultRolloverStrategy max="20"/>
            <DefaultRolloverStrategy max="20">
                <Delete basePath="${fileName}" maxDepth="2">
                    <IfFileName glob="*/*.log.gz" />
                    <IfLastModified age="10D" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
    </Appenders>
    <Loggers>
        <!-- AsyncRoot - 异步记录日志 - 需要LMAX Disruptor的支持 -->
        <AsyncRoot level="debug" additivity="false">
            <AppenderRef ref="console"/>
            <AppenderRef ref="infoFile"/>
            <AppenderRef ref="errorFile"/>
        </AsyncRoot>
    </Loggers>
</Configuration>
