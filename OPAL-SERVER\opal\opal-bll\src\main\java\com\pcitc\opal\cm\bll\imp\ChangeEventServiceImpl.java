package com.pcitc.opal.cm.bll.imp;

import com.pcitc.opal.cm.bll.ChangeEventService;
import com.pcitc.opal.cm.bll.entity.CraftChangeInfoEntity;
import com.pcitc.opal.cm.dao.ChangeEventRepository;
import com.pcitc.opal.cm.dao.imp.ChangeEventCondition;
import com.pcitc.opal.cm.pojo.ChangeEvent;
import com.pcitc.opal.cm.pojo.ChangeEventEntity;
import com.pcitc.opal.cm.pojo.ChangeMonitoringChartEntity;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.text.DateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/*
 * 事件类型对照配置业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmEventTypeCompServiceImpl
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：事件类型对照配置业务逻辑层实现类
 */
@Service
@Component
@Slf4j
public class ChangeEventServiceImpl implements ChangeEventService {


    @Autowired
    ChangeEventRepository changeEventRepository;

    @Resource
    private BasicDataService basicDataService;

    @Resource
    private ShiftService shiftService;

    @Override
    public List<ChangeEventCondition> getChangeEventInfo(Long comId) throws Exception {
        List<ChangeEventCondition> changeEventList = changeEventRepository.getChangeEventInfo(comId);

        return changeEventList;
    }

    @Override
    @Transactional
    public CommonResult updateChangeEventInfo(Long eventId, CraftChangeInfoEntity craftChangeInfo) throws Exception {

        ChangeEvent changeEvent = changeEventRepository.getChangeEventInfoById(eventId);

        ChangeEvent changeEvent1=new ChangeEvent();
        if (changeEvent!=null) {
            BeanUtils.copyProperties(changeEvent, changeEvent1);
        }
        if (craftChangeInfo!=null) {
            changeEvent1.setCraftChangeInfoId(craftChangeInfo.getCraftChangeInfoId().intValue());
            changeEvent1.setStatus(2);
        }else {
            changeEvent1.setStatus(1);
        }
        changeEvent1.setUpdateTime(new Date());



        return changeEventRepository.updateChangeEventInfo(changeEvent1);
    }

    @Override
    public List<ChangeMonitoringChartEntity> getChangeMonitoringChart(String[] unitIds, Long[] prdtCellIds, Long workTeamIds, String tag, Integer status, Date startTime, Date endTime) {
        //如果没有选择装置默认所有装置
        if (ArrayUtils.isEmpty(unitIds)){
            try {
                unitIds = basicDataService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
            } catch (Exception e) {
                log.error("获取所有装置发生异常" + e.getMessage());
            }
        }

        //定于排序规则《按照三个状态总和从大到小排序》
        Comparator<ChangeMonitoringChartEntity> comparator = (o1, o2) -> o2.getAllNum() - o1.getAllNum();

        List<ChangeMonitoringChartEntity> changeMonitoringChartEntities = changeEventRepository.selectStatusNumByUnitIds(unitIds, prdtCellIds, tag, status, startTime, endTime);
        //排序
        changeMonitoringChartEntities.sort(comparator);

        return changeMonitoringChartEntities;
    }

    @Override
    public PaginationBean<ChangeEventEntity> getChangeMonitoring(String[] unitIds, Long[] prdtCellIds, Long workTeamIds, String tag, Integer status, Date startTime, Date endTime, Pagination page) {
        //如果没有选择装置默认所有装置
        if (ArrayUtils.isEmpty(unitIds)){
            try {
                unitIds = basicDataService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
            } catch (Exception e) {
                log.error("获取所有装置发生异常" + e.getMessage());
            }
        }

        //用于存放班组的集合
        List<ShiftWorkTeamEntity> shiftList = new ArrayList<>();

        //获取班组
        //单选装置和班组时
        if (ArrayUtils.isNotEmpty(unitIds) && unitIds.length == 1 && workTeamIds != null){
            ArrayList<Long> workTeamIdList = new ArrayList<>();
            workTeamIdList.add(workTeamIds);
            try {
                shiftList = shiftService.getShiftList(unitIds[0], startTime, endTime, workTeamIdList);
            } catch (Exception e) {
                log.error("获取轮班域异常---{}",e.getMessage());
                e.printStackTrace();
            }

        }
        //没有选择班组时，直接根据装置获取班组
        if (workTeamIds == null){
            //
            try {
                shiftList = shiftService.getShiftWorkTeamList(Arrays.asList(unitIds), startTime, endTime);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        PaginationBean<ChangeEventEntity> pageBean = changeEventRepository.selectAllByUnitAndStartTime(unitIds, prdtCellIds, tag, status, startTime, endTime, page);

        List<ChangeEventEntity> pageList = pageBean.getPageList();

        List<ShiftWorkTeamEntity> finalShiftList = shiftList;
        //班组赋值
        pageList.forEach(x -> {
            x.setWorkerName(finalShiftList.parallelStream().filter(item -> x.getStartTime().getTime() >= item.getStartTime().getTime() && x.getStartTime().getTime() < item.getEndTime().getTime()).findFirst().orElse(new ShiftWorkTeamEntity()).getWorkTeamSName());
        });

//        //如果单选班组需要过滤数据中班组为空的数据
//        if (ArrayUtils.isNotEmpty(unitIds) && unitIds.length == 1 && workTeamIds != null){
//            pageList = pageList.stream().filter(x -> x.getWorkerName() != null).collect(Collectors.toList());
//        }

        pageBean.setPageList(pageList);

        return pageBean;


    }
}
