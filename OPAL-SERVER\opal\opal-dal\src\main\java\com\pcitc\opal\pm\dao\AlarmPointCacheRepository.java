package com.pcitc.opal.pm.dao;

import com.pcitc.opal.pm.pojo.AlarmPointCache;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * AlarmPointCache实体的Repository的JPA标准接口
 * 模块编号：pcitc_opal_dal_interface_AlarmPointCompRepository
 * 作    者：zheng.yang
 * 创建时间：2018/08/29
 * 修改编号：1
 * 描    述：AlarmPointCache实体的Repository实现
 */
public interface AlarmPointCacheRepository extends JpaRepository<AlarmPointCache,Long>, AlarmPointCacheRepositoryCustom {
}
