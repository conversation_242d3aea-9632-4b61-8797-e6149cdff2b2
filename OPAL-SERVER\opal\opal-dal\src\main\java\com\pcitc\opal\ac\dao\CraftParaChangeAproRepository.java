package com.pcitc.opal.ac.dao;

import com.pcitc.opal.ac.pojo.CraftParaChangeApro;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * CraftParaChangeApro实体的Repository的JPA标准接口
 * 模块编号： pcitc_opal_dal_interface_CraftParaChangeAproRepository
 * 作       者：zheng.yang
 * 创建时间：2019/4/15
 * 修改编号：1
 * 描       述：CraftParaChangeApro实体的Repository的JPA标准接口
 */
public interface CraftParaChangeAproRepository extends JpaRepository<CraftParaChangeApro,Long>, CraftParaChangeAproRepositoryCustom {
}
