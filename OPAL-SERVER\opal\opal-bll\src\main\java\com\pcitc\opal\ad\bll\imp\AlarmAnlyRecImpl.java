package com.pcitc.opal.ad.bll.imp;

import com.pcitc.opal.ad.bll.AlarmAnlyRecService;
import com.pcitc.opal.ad.bll.bo.AlarmAnlyRecExportRequestBO;
import com.pcitc.opal.ad.bll.bo.AlarmAnlyRecExportResponseBO;
import com.pcitc.opal.ad.dao.AlarmAnlyRecRepository;
import com.pcitc.opal.ad.dao.AlarmRecDAO;
import com.pcitc.opal.ad.dao.imp.AlarmAnlyRecVO;
import com.pcitc.opal.ad.dao.vo.AlarmRecAnlyVO;
import com.pcitc.opal.ad.pojo.AlarmAnlyRec;
import com.pcitc.opal.ad.vo.AlarmAnlyRecExportParamVO;
import com.pcitc.opal.ad.vo.AlarmAnlyRecExportVO;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@Component
public class AlarmAnlyRecImpl implements AlarmAnlyRecService {

    @Autowired
    private AlarmAnlyRecRepository alarmAnlyRecRepository;

    @Autowired
    private BasicDataService basicDataService;

    @Autowired
    private AlarmRecDAO alarmRecDAO;

    @Override
    public PaginationBean<AlarmRecAnlyVO> getAlarmAnlyRec(String[] unitIds, Long[] prdtCellIds, Long alarmFlagId,
                                                          String tag, Integer alarmStatus, Integer anlyStatus, Integer[] prioritys,
                                                          Date startTime, Date endTime, Integer[] monitorType, Pagination page) throws Exception {
        List<UnitEntity> units = null;
        if (unitIds == null) {
            units = basicDataService.getUnitList(true);
            unitIds = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.add(Calendar.DAY_OF_MONTH, -30);
        Date startTimeMonthAgo = calendar.getTime();
        return alarmAnlyRecRepository.getAlarmAnlyRec(unitIds, prdtCellIds, alarmFlagId, tag, alarmStatus, anlyStatus, prioritys,
                startTime, endTime, startTimeMonthAgo, monitorType, page);
    }

    @Override
    public List<AlarmRecAnlyVO> getAlarmAnlyRec(String[] unitIds, Long[] prdtCellIds, Long alarmFlagId, String tag, Integer alarmStatus, Integer anlyStatus, Integer[] prioritys, Date startTime, Date endTime, Integer[] monitorType) throws Exception {
        List<UnitEntity> units = null;
        if (unitIds == null) {
            units = basicDataService.getUnitList(true);
            unitIds = units.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.add(Calendar.DAY_OF_MONTH, -30);//5小时=18000秒
        Date startTimeMonthAgo = calendar.getTime();
        return alarmAnlyRecRepository.getAlarmAnlyRec(unitIds, prdtCellIds, alarmFlagId, tag, alarmStatus, anlyStatus, prioritys,
                startTime, endTime, startTimeMonthAgo, monitorType);
    }

    @Override
    public List<AlarmAnlyRecVO> getAlarmAnlyByRec(Long alarmRecId) throws Exception {
        return alarmAnlyRecRepository.getAlarmAnlyByRec(alarmRecId);
    }

    @Override
    public String addAlarmAnlyRec(Long alarmRecId, Long alarmAnlyRecId, Long reasonType, Long alarmReasonId, String des, Integer anlyStatus) throws Exception {
        CommonProperty commonProperty = new CommonProperty();
        String userId = commonProperty.getUserId();
        String userName = commonProperty.getUserName();
        if (alarmAnlyRecId != null) {
            AlarmAnlyRec ar = alarmAnlyRecRepository.getAlarmAnlyRecById(alarmAnlyRecId).get(0);
            if (alarmRecId != null) {
                ar.setAlarmRecId(alarmRecId);
            }
            if (reasonType != null) {
                ar.setReasonType(reasonType);
            }
            if (alarmReasonId != null) {
                ar.setAlarmReasonId(alarmReasonId);
            }
            if (des != null) {
                ar.setReasonDes(des);
            }
            ar.setAnlyStatus(anlyStatus);
            if (anlyStatus != 3) {
                ar.setSubmitUserId(userId);
                ar.setSubmitUserName(userName);
                ar.setSubmitTime(new Date());
            }
            CommonResult commonResult = alarmAnlyRecRepository.updateAlarmAnlyRec(ar);
            return commonResult.getMessage();
        }
//        if (anlyStatus == 3) {
//            List<AlarmAnlyRecVO> alarmAnlyByRec = alarmAnlyRecRepository.getAlarmAnlyByRec(alarmRecId);
//            for (AlarmAnlyRecVO vo : alarmAnlyByRec) {
//                AlarmAnlyRec ar = alarmAnlyRecRepository.getAlarmAnlyRecById(vo.getAlarmAnlyRecId()).get(0);
//                ar.setAnlyStatus(anlyStatus);
//                CommonResult commonResult = alarmAnlyRecRepository.updateAlarmAnlyRec(ar);
//            }
//        }
        AlarmAnlyRec ar = new AlarmAnlyRec();
        ar.setAlarmRecId(alarmRecId);
        ar.setReasonType(reasonType);
        ar.setAlarmReasonId(alarmReasonId);
        ar.setReasonDes(des);
        ar.setAnlyStatus(anlyStatus);
        ar.setCrtUserId(userId);
        ar.setCrtUserName(userName);
        ar.setCrtTime(new Date());
        ar.setSubmitTime(new Date());
        ar.setSubmitUserId(userId);
        ar.setSubmitUserName(userName);
        CommonResult commonResult = alarmAnlyRecRepository.addAlarmAnlyRec(ar);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        AlarmAnlyRec arc = (AlarmAnlyRec) commonResult.getResult();
        return arc.getAlarmAnlyRecId().toString();
    }

    @Override
    public String batchAddAnly(Long[] alarmRecIdList, Long reasonType, Long alarmReasonId, String des, Integer anlyStatus) throws Exception {
        if (alarmRecIdList == null || alarmRecIdList.length == 0) {
            throw new RuntimeException("报警记录为空");
        }
        Date now = new Date();
        CommonProperty commonProperty = new CommonProperty();
        String userId = commonProperty.getUserId();
        String userName = commonProperty.getUserName();
        for (Long alarmRecId : alarmRecIdList) {
            List<AlarmAnlyRecVO> alarmAnlyByRec = alarmAnlyRecRepository.getAlarmAnlyByRec(alarmRecId);
            AlarmAnlyRec ar = new AlarmAnlyRec();
            ar.setAlarmRecId(alarmRecId);
            ar.setReasonType(reasonType);
            ar.setAlarmReasonId(alarmReasonId);
            ar.setReasonDes(des);
            ar.setAnlyStatus(anlyStatus);
            ar.setCrtUserId(userId);
            ar.setCrtUserName(userName);
            ar.setCrtTime(now);
            if (anlyStatus == 2) {
                ar.setSubmitTime(now);
                ar.setSubmitUserId(userId);
                ar.setSubmitUserName(userName);
            }
            if (CollectionUtils.isNotEmpty(alarmAnlyByRec)) {
                AlarmAnlyRecVO anlyRecVO = alarmAnlyByRec.get(0);
                Long alarmAnlyRecId = anlyRecVO.getAlarmAnlyRecId();
                ar.setAlarmAnlyRecId(alarmAnlyRecId);
                alarmAnlyRecRepository.updateAlarmAnlyRec(ar);
            } else {
                alarmAnlyRecRepository.addAlarmAnlyRec(ar);
            }
        }
        return "1";
    }

    @Override
    public CommonResult updateAlarmAnlyRec(Long[] alarmAnlyRecIds, Integer anlyStatus) throws Exception {
        CommonProperty commonProperty = new CommonProperty();
        String userId = commonProperty.getUserId();
        String userName = commonProperty.getUserName();
        List<AlarmAnlyRec> ars = alarmAnlyRecRepository.getAlarmAnlyRecsById(alarmAnlyRecIds);
        ars.forEach(ar -> {
            ar.setAnlyStatus(anlyStatus);
            if (anlyStatus == 2) {
                ar.setSubmitUserId(userId);
                ar.setSubmitUserName(userName);
                ar.setSubmitTime(new Date());
            } else if (anlyStatus == 3) {
                ar.setConfirmUserId(userId);
                ar.setConfirmUserName(userName);
                ar.setConfirmTime(new Date());
            }
        });
        CommonResult commonResult = alarmAnlyRecRepository.updateAlarmAnlyRecs(ars);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    @Override
    public List<AlarmAnlyRecVO> getAnlyRecByEvent(List<Long> eventIds) {
        return Collections.emptyList();
    }

    @Override
    public List<AlarmAnlyRecExportResponseBO> getAnalyseRecExport(List<String> unitIds,
                                                                  List<Long> prdtCellIds,
                                                                  Long alarmFlagId,
                                                                  String tag,
                                                                  Integer alarmStatus,
                                                                  Integer anlyStatus,
                                                                  List<Integer> prioritys,
                                                                  Date startTime,
                                                                  Date endTime,
                                                                  List<Integer> monitorType) {
        List<AlarmAnlyRecExportResponseBO> responseBOList = new ArrayList<>();
        try {
            if (CollectionUtils.isEmpty(unitIds)) {
                List<UnitEntity> units = basicDataService.getUnitList(true);
                unitIds = units.stream().map(UnitEntity::getStdCode).distinct().collect(Collectors.toList());
            }
            AlarmAnlyRecExportParamVO paramVO = new AlarmAnlyRecExportParamVO();
            paramVO.setTag(tag);
            paramVO.setAlarmFlagId(alarmFlagId);
            paramVO.setAlarmStatus(alarmStatus);
            paramVO.setAnlyStatus(anlyStatus);
            paramVO.setStartTime(startTime);
            paramVO.setEndTime(endTime);
            paramVO.setPrioritys(prioritys);
            paramVO.setMonitorType(monitorType);
            paramVO.setPrdtCellIds(prdtCellIds);
            paramVO.setUnitIds(unitIds);
            List<AlarmAnlyRecExportVO> exportVOList = alarmRecDAO.selectAnalyseRecExport(paramVO);
            if (CollectionUtils.isNotEmpty(exportVOList)) {
                AlarmAnlyRecExportResponseBO responseBO = null;
                for (AlarmAnlyRecExportVO exportVO : exportVOList) {
                    responseBO = new AlarmAnlyRecExportResponseBO();
                    BeanUtils.copyProperties(exportVO, responseBO);
                    responseBO.setAnlyStatusName(CommonEnum.AnlyStatus.getName(exportVO.getAnlyStatus()));
                    responseBO.setPriorityName(CommonEnum.AlarmPriorityEnum.getName(exportVO.getPriority()));
                    responseBO.setMonitorTypeName(CommonEnum.MonitorTypeEnum.getName(exportVO.getMonitorType()));
                    responseBO.setReasonTypeName(CommonEnum.ReasonType.getName(exportVO.getReasonType()));
                    responseBOList.add(responseBO);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return responseBOList;
    }

}
