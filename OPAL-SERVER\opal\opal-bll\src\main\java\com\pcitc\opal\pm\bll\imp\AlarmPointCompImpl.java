package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.pm.bll.AlarmPointCompService;
import com.pcitc.opal.pm.bll.entity.AlarmPointCacheEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.dao.AlarmPointCacheRepository;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@Service
public class AlarmPointCompImpl implements AlarmPointCompService{

    @Autowired
    private AlarmPointCacheRepository repo;
    @Autowired
    private BasicDataService basicDataService;

    /**
     * 获取分页数据(新增)
     *
      * <AUTHOR> 2018-08-29
     * @param unitCodes 装置id集合
     * @param tag 位号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmPointEntity> 翻页对象
     */
    @Override
    public PaginationBean<AlarmPointCacheEntity> getAlarmPointCacheList(String[] unitCodes, String tag, Date startTime, Date endTime, Pagination page) throws Exception {
        if(StringUtils.isNotBlank(tag))
            tag = tag.trim();
        if(unitCodes != null && unitCodes.length == 0 ){
            unitCodes = null;
        }
        if (unitCodes==null || unitCodes.length == 0){
            List<UnitEntity> unitList = basicDataService.getUnitList(true);
            unitCodes = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
        }
        endTime = DateUtils.addDays(endTime, 1);
        PaginationBean<Object[]> alarmPointCacheList = repo.getAlarmPointCacheList(unitCodes, tag, startTime, endTime, page);
        PaginationBean<AlarmPointCacheEntity> returnList = new PaginationBean<AlarmPointCacheEntity>(page,alarmPointCacheList.getTotal());
        List<UnitEntity> unitList = basicDataService.getUnitList(true);
        for(Object[] o: alarmPointCacheList.getPageList()){
            AlarmPointCacheEntity entity = new AlarmPointCacheEntity();
            entity.setUnitId(o[0].toString());
            UnitEntity ue = unitList.stream().filter(ul -> entity.getUnitId().equals(ul.getStdCode())).findFirst().orElse(new UnitEntity());
            entity.setUnitSname(ue.getSname());
            entity.setPrdtCellId(toL(o[1]));
            entity.setPrdtCell(toStr(o[2]));
            entity.setWriteTime((Date)o[3]);
            entity.setOpcCode(toL(o[4]));
            entity.setTag(toStr(o[6]));
            entity.setHh(toD(o[7]));
            entity.setPh(toD(o[8]));
            entity.setPl(toD(o[9]));
            entity.setLl(toD(o[10]));
            returnList.getPageList().add(entity);
        }
        return returnList;
    }
    /**
     * 获取分页数据(删除)
     *
      * <AUTHOR> 2018-08-29
     * @param unitCodes 装置编码
     * @param prdtCellIds 生产单元id
     * @param tag 位号
     * @param inUse 是否启用
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmPointEntity> 翻页对象
     */
    @Override
    public PaginationBean<AlarmPointEntity> getAlarmPointCacheList(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inUse, Pagination page) throws Exception {
        try {
            if(StringUtils.isNotBlank(tag))
                tag = tag.trim();
            if(unitCodes != null && unitCodes.length == 0 ){
                unitCodes = null;
            }
            if (unitCodes==null || unitCodes.length == 0){
                List<UnitEntity> unitList = basicDataService.getUnitList(true);
                unitCodes = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
            }
            if (prdtCellIds!=null&&prdtCellIds.length==0){
                prdtCellIds=null;
            }
            PaginationBean<Object[]> alarmPointCacheList = repo.getAlarmPointCacheList(unitCodes, prdtCellIds, tag, inUse, page);
            List<UnitEntity> unitList = basicDataService.getUnitList(true);
            PaginationBean<AlarmPointEntity> returnList = new PaginationBean<AlarmPointEntity>(page,alarmPointCacheList.getTotal());
            for(Object[] o: alarmPointCacheList.getPageList()){
                AlarmPointEntity entity =new AlarmPointEntity();
                entity.setUnitId(o[0].toString());
                entity.setMonitorType(1);
                entity.setInstrmtType(1);
                entity.setVirtualRealityFlag(1);
                UnitEntity ue = unitList.stream().filter(ul -> entity.getUnitId().equals(ul.getStdCode())).findFirst().orElse(new UnitEntity());
                entity.setUnitSname(ue.getSname());
                entity.setPrdtCellSname(toStr(o[1]));
                entity.setTag(toStr(o[2]));
                entity.setCraftRank(Integer.parseInt(o[3]+""));
                entity.setCraftRankName(entity.getCraftRank()==1?"A":"B");
                entity.setLocation(toStr(o[4]));
                entity.setCraftUpLimitValue(toD(o[5]));
                entity.setCraftDownLimitValue(toD(o[6]));
                entity.setCraftUpLimitInclude(toI(o[7]));
                entity.setCraftDownLimitInclude(toI(o[8]));
                entity.setInterlockUpLimitValue(toD(o[9]));
                entity.setInterlockDownLimitValue(toD(o[10]));
                entity.setInterlockUpLimitInclude(toI(o[11]));
                entity.setInterlockDownLimitInclude(toI(o[12]));
                entity.setInUse(toI(o[13]));
                entity.setAlarmPointId(toL(o[14]));
                Double culv = entity.getCraftUpLimitValue();//工艺卡片上限值
                Double cdlv = entity.getCraftDownLimitValue();//工艺卡片下限值
                Integer culi = entity.getCraftUpLimitInclude();//工艺卡片上限值是否包含(1是；0否)
                Integer cdli = entity.getCraftDownLimitInclude();//工艺卡片下限值是否包含(1是；0否)
                Double iulv = entity.getInterlockUpLimitValue();//联锁上限值
                Double idlv = entity.getInterlockDownLimitValue();//联锁下限值
                Integer iuli = entity.getInterlockUpLimitInclude();//联锁上限值是否包含(1是；0否)
                Integer idli = entity.getInterlockDownLimitInclude();//联锁下限值是否包含(1是；0否)
                if (culv != null && cdlv != null) {
                    String culvStr = changeDouble(culv);
                    String cdlvStr = changeDouble(cdlv);
                    entity.setCraftLimitValue(cdlvStr + "～" + culvStr);
                } else if (culv != null && cdlv == null) {
                    if (culi != null && culi.intValue() == 1) {
                        String culvStr = changeDouble(culv);
                        entity.setCraftLimitValue("≤" + culvStr);
                    } else if (culi != null && culi.intValue() == 0) {
                        String culvStr = changeDouble(culv);
                        entity.setCraftLimitValue("<" + culvStr);
                    }
                } else if (culv == null && cdlv != null) {
                    if (cdli != null && cdli.intValue() == 1) {
                        String cdlvStr = changeDouble(cdlv);
                        entity.setCraftLimitValue("≥" + cdlvStr);
                    } else if (cdli != null && cdli.intValue() == 0) {
                        String cdlvStr = changeDouble(cdlv);
                        entity.setCraftLimitValue(">" + cdlvStr);
                    }
                } else if (culv == null && cdlv == null) {
                    entity.setCraftLimitValue("");
                }
                if (iulv != null && idlv != null) {
                    String iulvStr = changeDouble(iulv);
                    String idlvStr = changeDouble(idlv);
                    entity.setInterlockLimitValue(idlvStr + "～" + iulvStr);
                } else if (iulv != null && idlv == null) {
                    if (iuli.intValue() == 1) {
                        String iulvStr = changeDouble(iulv);
                        entity.setInterlockLimitValue("≤" + iulvStr);
                    } else if (iuli.intValue() == 0) {
                        String iulvStr = changeDouble(iulv);
                        entity.setInterlockLimitValue("<" + iulvStr);
                    }
                } else if (iulv == null && idlv != null) {
                    if (idli.intValue() == 1) {
                        String idlvStr = changeDouble(idlv);
                        entity.setInterlockLimitValue("≥" + idlvStr);
                    } else if (idli.intValue() == 0) {
                        String idlvStr = changeDouble(idlv);
                        entity.setInterlockLimitValue(">" + idlvStr);
                    }
                } else if (iulv == null && idlv == null) {
                    entity.setInterlockLimitValue("");
                }
                returnList.getPageList().add(entity);
            }
            return returnList;
        } catch (Exception e) {
            throw e;
        }
    }
    /**
     * 获取导出数据(新增)
     *
      * <AUTHOR> 2018-11-05
     * @param unitCodes 装置id集合
     * @param tag 位号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 翻页实现类
     * @throws Exception 
     * @return List<AlarmPointCacheEntity>
     */
    @Override
    public List<AlarmPointCacheEntity> getExportAlarmPointCompList(String[] unitCodes, String tag, Date startTime, Date endTime, Pagination page) throws Exception {
        page.setPageSize(Integer.MAX_VALUE);
        page.setPageNumber(1);
        List<AlarmPointCacheEntity> pageList = getAlarmPointCacheList(unitCodes, tag, startTime, endTime, page).getPageList();
        for(AlarmPointCacheEntity e: pageList){
            e.setVirtualRealityFlagStr("否");
            e.setAlarmPointTypeStr("-");
            e.setMonitorTypeStr("-");
            e.setInstrmtTypeStr("-");
            e.setInUseStr("是");
            e.setLocation("");
            e.setPid("");
            e.setMeasUnit("");
            e.setSortNum("");
            e.setDes("");
        }
        return pageList;
    }

    //Object转Long
    private Long toL(Object o) {
        return o != null ? new BigDecimal(o+"").longValue() : null;
    }
    //Object转String
    private String toStr(Object o ){
        return o==null ? "":o.toString();
    }
    //object转Double
    private Double toD(Object o){
        return o==null?null:new BigDecimal(o+"").doubleValue();
    }
    //object转Integer
    private Integer toI(Object o) {
        return o==null?null:new BigDecimal(o+"").intValue();
    }
    public String changeDouble(Double num) {
        if ((num + "").endsWith(".0")) {
            return num.intValue() + "";
        }
        return num + "";
    }
}
