package com.pcitc.opal.as.dao.imp;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.TypedQuery;

import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import com.pcitc.opal.as.dao.AlarmStdManagmtRepositoryCustom;
import com.pcitc.opal.as.pojo.AlarmStdManagmt;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import org.springframework.transaction.annotation.Propagation;


/*
 * 报警制度管理实体的Repository的JPA接口实现
 * 模块编号： pcitc_opal_dal_class_AlarmStdManagmtRepositoryImpl
 * 作       者：kun.zhao
 * 创建时间：2018/02/28
 * 修改编号：1
 * 描       述：报警制度管理实体的Repository的JPA接口实现
 */
public class AlarmStdManagmtRepositoryImpl extends BaseRepository<AlarmStdManagmt, Long>
		implements AlarmStdManagmtRepositoryCustom {
    /**
     * 校验数据
     *
     * <AUTHOR> 2018-03-01
     * @param alarmStdManagmt 报警制度管理实体
     * @return 返回结果信息类
     */
    @Override
    public CommonResult alarmStdManagmtValidation(AlarmStdManagmt alarmStdManagmt){
        CommonResult commonResult = new CommonResult();
        try {
            // “名称”唯一性校验，提示“此报警管理制度名称已存在！”。
            StringBuilder hql = new StringBuilder(
                    "from AlarmStdManagmt t where t.name =:name and t.alarmStdManagmtId<>:alarmStdManagmtId");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("name", alarmStdManagmt.getName());
            paramList.put("alarmStdManagmtId", alarmStdManagmt.getAlarmStdManagmtId() == null ? 0 : alarmStdManagmt.getAlarmStdManagmtId());

            long index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("此报警管理制度名称已存在！");
            }
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    /**
     * 新增数据
     *
     * <AUTHOR> 2018-03-01
     * @param alarmStdManagmt 报警制度管理实体
     * @return CommonResult 消息结果类
     */
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addAlarmStdManagmt(AlarmStdManagmt alarmStdManagmt){
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(alarmStdManagmt);
            commonResult.setResult(alarmStdManagmt);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

	/**
	 * 删除报警制度管理
	 *
	 * <AUTHOR> 2018-02-28
	 * @param alarmStdManagmtIds 报警制度管理Id数组
	 * @return 返回结果信息类
	 */
	@Override
	@Transactional
	public CommonResult deleteAlarmStdManagmt(Long[] alarmStdManagmtIds) {
		// 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            String hql = " from AlarmStdManagmt t where t.alarmStdManagmtId in (:alarmStdManagmtIds)";
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> alarmStdManagmtIdsList = Arrays.asList(alarmStdManagmtIds);
            paramList.put("alarmStdManagmtIds", alarmStdManagmtIdsList);

            TypedQuery<AlarmStdManagmt> query = getEntityManager().createQuery(hql, AlarmStdManagmt.class);
            this.setParameterList(query, paramList);
            List<AlarmStdManagmt> alarmStdManagmtList = query.getResultList();
            alarmStdManagmtList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 删除出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
	}
    /**
     * 更新数据
     *
     * <AUTHOR> 2018-03-01
     * @param alarmStdManagmt 报警制度管理实体
     * @return CommonResult 消息结果类
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult updateAlarmStdManagmt(AlarmStdManagmt alarmStdManagmt){
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(alarmStdManagmt);
            commonResult.setResult(alarmStdManagmt);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }
	/**
	 * 根据报警制度管理ID获取单条数据信息
	 * 
	 * <AUTHOR> 2018-02-28
	 * @param alarmStdManagmtId 报警制度管理ID
	 * @return 报警制度管理实体
	 * @throws Exception
	 */
	@Override
	public AlarmStdManagmt getSingleAlarmStdManagmt(Long alarmStdManagmtId) {
		try {
            return getEntityManager().find(AlarmStdManagmt.class, alarmStdManagmtId);
        } catch (Exception ex) {
            throw ex;
        }
	}
	
	/**
	 * 获取多条数据
	 *
	 * <AUTHOR> 2018-02-28
	 * @param alarmStdManagmtIds 报警制度管理ID
	 * @return 报警制度管理实体数组
	 */
	@Override
	public List<AlarmStdManagmt> getAlarmStdManagmt(Long[] alarmStdManagmtIds) {
		try {
            // 查询字符串
            String hql = "from AlarmStdManagmt t ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (alarmStdManagmtIds.length > 0) {
                hql += " where t.alarmStdManagmtId in (:alarmStdManagmtIds)";
                List<Long> alarmStdManagmtIdsList = Arrays.asList(alarmStdManagmtIds);
                paramList.put("alarmStdManagmtIds", alarmStdManagmtIdsList);
            }
            TypedQuery<AlarmStdManagmt> query = getEntityManager().createQuery(hql, AlarmStdManagmt.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
	}

	/**
	 * 获取报警制度管理分页数据
	 * 
	 * <AUTHOR> 2018-02-28
	 * @param name      名称
	 * @param catgr     分类
	 * @param startTime 上传时间范围起始
	 * @param endTime   上传时间范围结束
	 * @param page      分页对象
	 * @return 报警制度管理分页对象
	 * @throws Exception
	 */
	@Override
	public PaginationBean<AlarmStdManagmt> getAlarmStdManagmt(String name, Integer catgr, Date startTime, Date endTime,
			Pagination page) {
		
		try {
			// 查询字符串
            StringBuilder hql = new StringBuilder("from AlarmStdManagmt t where 1=1 ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 过滤名称
            if (StringUtils.isNotEmpty(name)) {
            	hql.append(" and (t.name like :name escape '/') ");
                paramList.put("name", "%" + this.sqlLikeReplace(name) + "%");
            }
            // 过滤分类
            if (catgr != null) {
                hql.append(" and t.catgr = :catgr ");
                paramList.put("catgr", catgr);
            }
            // 过滤上传时间
            if (startTime != null && endTime != null) {
                hql.append(" and t.uplTime between :startTime and :endTime ");
                paramList.put("startTime", startTime);
                paramList.put("endTime", endTime);
            }
            hql.append(" order by t.uplTime desc");
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString(), paramList);
		} catch (Exception ex) {
			throw ex;
		}
	}

}
