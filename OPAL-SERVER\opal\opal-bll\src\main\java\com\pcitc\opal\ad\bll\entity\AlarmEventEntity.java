package com.pcitc.opal.ad.bll.entity;

import java.util.Date;

import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 报警事件实体  
 * 模块编号：pcitc_opal_bll_class_AlarmEventEntity
 * 作       者：kun.zhao
 * 创建时间：2017/10/9
 * 修改编号：1
 * 描       述：报警事件实体    
 */
public class AlarmEventEntity extends BasicEntity {

    /**
     * 报警事件ID
     */
    private Long eventId;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 班组ID
     */
    private Long workTeamId;
    /**
     * 班组名称
     */
    private String workTeamName;
    /**
     * 持续时长
     */
    private String continuousHour;
    /**
     * 装置ID
     */
    private String unitCode;
    /**
     * 装置名称
     */
    private String unitName;

    /**
     * 生产单元名称
     */
    private Long prdtCellId;
    private String prdtCellName;

    /**
     * 事件类型ID
     */
    private Long eventTypeId;

    /**
     * 事件类型名称
     */
    private String eventTypeName;

    /**
     * 报警点位号
     */
    private String alarmPointTag;

    /**
     * 报警点位置
     */
    private String location;

    /**
     *报警点ID
     */
    private Long alarmPointId;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     * 报警标识名称
     */
    private String alarmFlagName;

    /**
     * 优先级(1紧急；2重要；3一般)
     */
    private Integer priority;

    /**
     * 优先级名称
     */
    @SuppressWarnings("unused")
	private String priorityName;

    /**
     * 发生时间起始
     */
    private Date startTime;

    /**
     * 发生时间结束
     */
    private Date endTime;

    /**
     * 报警时间
     */
    private Date alarmTime;

    /**
     * 先前值
     */
    private String previousValue;

    /**
     * 值
     */
    private String nowValue;

    /**
     * 限值
     */
    private Double limitValue;
    /**
     * 报警点高高报
     */
    private Double alarmPointHH;
    /**
     * 报警点高报
     */
    private Double alarmPointHI;
    /**
     * 报警点低报
     */
    private Double alarmPointLO;
    /**
     * 报警点低低报
     */
    private Double alarmPointLL;

    /**
     * 计量单位id
     */
    private String measUnitId;

    /**
     * 计量单位名称
     */
    private String measUnitName;

    /**
     * 描述
     */
    private String des;

    /**
     * 位号
     */
    private String tag;

    /**
     * 报警点位置
     */
    private String alarmPointLocation;
    /**
     * 级别(1A；2B)
     */
    private Integer craftRank;
    /**
     * 级别(1A；2B)
     */
    @SuppressWarnings("unused")
	private String craftRankName;
    /**
     * 发生时间起始
     */
    private String startTimeStr;
    /**
     * 处理时间（为空时默认赋当前时间）
     */
    private Date handleTime;

    /**
     * 处理时间（为空时不赋默认值）
     */
    private Date recoveryTime;
    /**
     * 班组简称
     */
    private String workTeamSName;
    /**
     * 班组编码
     */
    private String workTeamCode;
    /**
	 * 是否可以变更
	 */
	private boolean changeStatus;
    /**
     * 工艺卡片值
     */
    private String craftLimitValue;

    /**
     * 联锁值
     */
    private String interlockLimitValue;
    /**
     * 次数
     */
    private Long count;
    /**
     * 报警点说明
     */
    private String alarmPointExplain;

    /**
     * 总报警数
     */
    private Long totalAlarmNum;

    /**
     * 报警平均数
     */
    private String avgAlarmNum;

    public Date getRecoveryTime() {
        return recoveryTime;
    }

    public void setRecoveryTime(Date recoveryTime) {
        this.recoveryTime = recoveryTime;
    }

    /**
     *导出excel序号
     */
    private Integer index;

    /**
     * 监测类型（1物料;2能源;3质量;4:工艺;5:设备:;6:安全;7:其他;）
     */
    private Integer monitorType;

    /**
     * 监测类型Str（1:物料;2:能源;3:质量;4:工艺;5:设备;6:安全;7:其他;）
     */
    private String monitorTypeStr;

    public String getMonitorTypeStr() {
        return monitorTypeStr;
    }

    public Integer getMonitorType() {
        return monitorType;
    }


    public void setMonitorType(Integer monitorType) {
        this.monitorType = monitorType;
        this.monitorTypeStr =CommonEnum.MonitorTypeEnum.getName(monitorType);
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getPreviousValue() {
		return previousValue;
	}

	public void setPreviousValue(String previousValue) {
		this.previousValue = previousValue;
	}

	public String getNowValue() {
		return nowValue;
	}

	public void setNowValue(String nowValue) {
		this.nowValue = nowValue;
	}

	public Double getLimitValue() {
        return limitValue;
    }

    public void setLimitValue(Double limitValue) {
        this.limitValue = limitValue;
    }

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPrdtCellName() {
        return prdtCellName;
    }

    public void setPrdtCellName(String prdtCellName) {
        this.prdtCellName = prdtCellName;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public String getEventTypeName() {
        return eventTypeName;
    }

    public void setEventTypeName(String eventTypeName) {
        this.eventTypeName = eventTypeName;
    }

    public String getAlarmPointTag() {
        return alarmPointTag;
    }

    public void setAlarmPointTag(String alarmPointTag) {
        this.alarmPointTag = alarmPointTag;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public String getAlarmFlagName() {
        return alarmFlagName;
    }

    public void setAlarmFlagName(String alarmFlagName) {
        this.alarmFlagName = alarmFlagName;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getPriorityName() {
        if(this.priority == null) return "";
        return CommonEnum.AlarmPriorityEnum.getName(priority);
    }

    public void setPriorityName(String priorityName) {
        this.priorityName = priorityName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public String getMeasUnitId() {
        return measUnitId;
    }

    public void setMeasUnitId(String measUnitId) {
        this.measUnitId = measUnitId;
    }

    public String getMeasUnitName() {
        return measUnitName;
    }

    public void setMeasUnitName(String measUnitName) {
        this.measUnitName = measUnitName;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getContinuousHour() {
        return continuousHour;
    }

    public void setContinuousHour(String continuousHour) {
        this.continuousHour = continuousHour;
    }

    public String getAlarmPointLocation() {
        return alarmPointLocation;
    }

    public void setAlarmPointLocation(String alarmPointLocation) {
        this.alarmPointLocation = alarmPointLocation;
    }

    public Integer getCraftRank() {
        return craftRank;
    }

    public void setCraftRank(Integer craftRank) {
        this.craftRank = craftRank;
    }

    public String getCraftRankName() {
        if (craftRank == null) return "";
        return CommonEnum.CraftRankEnum.getName(craftRank);
    }

    public void setCraftRankName(String craftRankName) {
        this.craftRankName = craftRankName;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

	public String getStartTimeStr() {
		return startTimeStr;
	}

	public void setStartTimeStr(String startTimeStr) {
		this.startTimeStr = startTimeStr;
	}

	public Date getHandleTime() {
		return handleTime;
	}

	public void setHandleTime(Date handleTime) {
		this.handleTime = handleTime;
	}

    public String getWorkTeamSName() {
        return workTeamSName;
    }

    public void setWorkTeamSName(String workTeamSName) {
        this.workTeamSName = workTeamSName;
    }

    public String getWorkTeamCode() {
        return workTeamCode;
    }

    public void setWorkTeamCode(String workTeamCode) {
        this.workTeamCode = workTeamCode;
    }

    public Long getWorkTeamId() {
        return workTeamId;
    }

    public void setWorkTeamId(Long workTeamId) {
        this.workTeamId = workTeamId;
    }

    public String getWorkTeamName() {
        return workTeamName;
    }

    public void setWorkTeamName(String workTeamName) {
        this.workTeamName = workTeamName;
    }

	public boolean getChangeStatus() {
		return changeStatus;
	}

	public void setChangeStatus(boolean changeStatus) {
		this.changeStatus = changeStatus;
	}

	public Long getAlarmPointId() {
		return alarmPointId;
	}

	public void setAlarmPointId(Long alarmPointId) {
		this.alarmPointId = alarmPointId;
	}

    public String getCraftLimitValue() {
        return craftLimitValue;
    }

    public void setCraftLimitValue(String craftLimitValue) {
        this.craftLimitValue = craftLimitValue;
    }

    public String getInterlockLimitValue() {
        return interlockLimitValue;
    }

    public void setInterlockLimitValue(String interlockLimitValue) {
        this.interlockLimitValue = interlockLimitValue;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public Long getPrdtCellId() {
        return prdtCellId;
    }

    public void setPrdtCellId(Long prdtCellId) {
        this.prdtCellId = prdtCellId;
    }

    public String getAlarmPointExplain() {
        return alarmPointExplain;
    }

    public void setAlarmPointExplain(String alarmPointExplain) {
        this.alarmPointExplain = alarmPointExplain;
    }

    public Long getTotalAlarmNum() {
        return totalAlarmNum;
    }

    public void setTotalAlarmNum(Long totalAlarmNum) {
        this.totalAlarmNum = totalAlarmNum;
    }

    public String getAvgAlarmNum() {
        return avgAlarmNum;
    }

    public void setAvgAlarmNum(String avgAlarmNum) {
        this.avgAlarmNum = avgAlarmNum;
    }

    public Double getAlarmPointHH() {
        return alarmPointHH;
    }

    public void setAlarmPointHH(Double alarmPointHH) {
        this.alarmPointHH = alarmPointHH;
    }

    public Double getAlarmPointHI() {
        return alarmPointHI;
    }

    public void setAlarmPointHI(Double alarmPointHI) {
        this.alarmPointHI = alarmPointHI;
    }

    public Double getAlarmPointLO() {
        return alarmPointLO;
    }

    public void setAlarmPointLO(Double alarmPointLO) {
        this.alarmPointLO = alarmPointLO;
    }

    public Double getAlarmPointLL() {
        return alarmPointLL;
    }

    public void setAlarmPointLL(Double alarmPointLL) {
        this.alarmPointLL = alarmPointLL;
    }

    public boolean isChangeStatus() {
        return changeStatus;
    }

    public void setMonitorTypeStr(String monitorTypeStr) {
        this.monitorTypeStr = monitorTypeStr;
    }

    /**
     * 返回拼接好的字符串
     * @return string
     */
    public String getSplicing(){
        return unitName + "," + prdtCellName + "," + alarmPointTag + "," + des + "," + priority + "," + alarmFlagName + "," + measUnitName;
    }

}
