package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.dao.imp.AlarmPointGroupDtlDTO;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import com.pcitc.opal.pm.pojo.AlarmPointGroupDtl;

import java.util.List;

/*
 * AlarmPoint实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmPointRepositoryCustom
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：AlarmPoint实体的Repository的JPA自定义接口 
 */
public interface AlarmPointGroupDtlRepositoryCustom {



	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointEntity 报警点实体
	 * @return CommonResult 消息结果类
	 */
	CommonResult addAlarmPointGroupDtl(List<AlarmPointGroupDtl> alarmPointEntity);
	
	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointIds 报警点ID集合
	 * @return CommonResult 消息结果类
	 */
	CommonResult deleteAlarmPointGroupDtl(List<Long> alarmPointIds);
	
	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointEntity 报警点实体
	 * @return CommonResult 消息结果类
	 */
	CommonResult updateAlarmPointGroupDtl(AlarmPointGroupDtl alarmPointEntity);
	



	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2017-10-11
	 * @param alarmPointGroupId 分组ID
	 * @return List<AlarmPoint> 报警点实体集合
	 */
	List<AlarmPointGroupDtlDTO> getAlarmPointGroupDtls(Long alarmPointGroupId);

	/**
	 * 获取分组明细数据
	 *
	  * <AUTHOR> 2017-10-11
	 * @param alarmPointGroupId 分组编码
	 * @throws Exception 
	 * @return List
	 */
	List<Long> getAlarmPointGroupDtlList(Long alarmPointGroupId);

}
