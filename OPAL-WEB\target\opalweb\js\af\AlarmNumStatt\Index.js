var alarmNumStattUrl = alarmNumStattPriorityUrl;
var alarmNumStattPriorityUrl = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumStattStatisticData';
var alarmNumStattMonitorTypeUrl = OPAL.API.afUrl + '/alarmNumStatt/getUnitMonitorAlarmNumStat';
var exportUrl = OPAL.API.afUrl + '/alarmNumStatt/exportAlarmNumber';  // 导出
var exportMonitorTypeUrl = OPAL.API.afUrl + '/alarmNumStatt/exportMonitorTypeAlarmNumber';  // 导出

var alarmNumberUrl = OPAL.API.afUrl + "/alarmNumStatt/getAlarmNumberList";    // 报警数趋势图

var AlarmNumTop20SingleUrl = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumberAssessTop20Single';
var top20SingleExportExcelUrl = OPAL.API.afUrl + '/alarmNumStatt/top20SingleExportExcel';  // 导出最频繁20

// 按车间显示
var DataWorkshop = DataWorkshopPriority;   // 车间
var DataWorkshopMonitorType = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumStattStatisticDataWorkshopType';
var DataWorkshopPriority = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumStattStatisticDataWorkshop';

var DataUnit = DataUnitPriority;   // 装置
var DataUnitMonitorType = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumStattStatisticDataUnitType';
var DataUnitPriority = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumStattStatisticDataUnit';

var DataPrdtcell = DataPrdtcellPriority;   // 单元
var DataPrdtcellMonitorType = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumStattStatisticDataPrdtcellType';
var DataPrdtcellPriority = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumStattStatisticDataPrdtcell';

var AssessAll = AssessAllPriority;   // 下方列表
var AssessAllMonitorType = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumberAssessAllType';   // 下方列表
var AssessAllPriority = OPAL.API.afUrl + '/alarmNumStatt/getAlarmNumberAssessAll';   // 下方列表

var alarmDurationStatt = OPAL.API.afUrl + '/alarmDurationStatt';   // 二级列表
var allSingleExportExcel = OPAL.API.afUrl + '/alarmNumStatt/allSingleExportExcel';   // 导出
var alarmDurStattPageUrl = OPAL.API.afUrl + '/alarmDurationStatt/page';  //二级列表


var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";   //装置树
var monitorTypeUrl = OPAL.API.commUrl + "/getMonitorTypeList"; //专业
var getShowTimeUrl = OPAL.API.commUrl + '/getShowTime';
var priorityUrl = OPAL.API.afUrl + "/alarmDurationStatt/getAlarmPriorityList";    //优先级
var alarmFlagListUrl = OPAL.API.adUrl + '/alarmEvents/getAlarmFlagList';


var floodAlarmChart;
var floodChartConfig;
var mostAlarmOperateCharts;
var operNumChart;
var unitId;
var unitIds = [];
var unit;

tabChart = {
    floodAlarmChartcj: null,
    floodAlarmChartzz: null,
    floodAlarmChartdy: null,
}

var pageType;

$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            /**
             *绑定事件
             */
            this.bindUi();
            //日期扩展
            OPAL.util.extendDate();
            //根据日期时间计算开始时间和结束时间和一周之前的开始和结束时间
            page.logic.initPriority();
            //初始化装置数
            page.logic.initUnitTree();
            //初始化报警标识
            page.logic.initAlarmFlagList();
            //初始化时间条件
            page.logic.getShowTime();
            //初始化 专业
            page.logic.initMonitorType();

            // page.logic.initTableDisplaycj(JSON.stringify(""));
            page.logic.initOpetateTablecj({});

            // page.logic.initTableDisplay(JSON.stringify(""));
            page.logic.initMostAlarmOperateCharts();
            page.logic.initOpetateTable({});
            page.logic.initOperateNumberChart();
            page.logic.initAlarmAndOperateTable({});
        },
        bindUi: function () {
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                // floodAlarmChart.resize();
                mostAlarmOperateCharts.resize();
                operNumChart.resize();
                $('#floodTable').bootstrapTable('resetView');
                $('#MostAlarmOperateTable').bootstrapTable('resetView');
                $('#tableAlarmAndOperate').bootstrapTable('resetView');
            };
            /**
             * 导航切换
             */
            $('.myTab li').click(function () {
                var flag = $(this).attr('showFlag');
                var tableNum = $(this).attr('tableNum');
                if (flag == 'imgShow') {
                    $(this).find('img').attr('src', '../../../images/one1.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/tweo.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                    if (tableNum == '1') {
                        $("#expBtn").empty().removeClass("cursor_pointer");
                    }
                    if (tableNum == '3') {
                        $("#exportOftenBtn").empty().removeClass("cursor_pointer");
                    }
                } else if (flag == 'tableShow') {
                    $(this).find('img').attr('src', '../../../images/tweos.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                    if (tableNum == '1') {
                        $("#expBtn").html("&nbsp;&nbsp;&nbsp;&nbsp;导出").addClass("cursor_pointer");
                    }
                    if (tableNum == '3') {
                        $("#exportOftenBtn").html("&nbsp;&nbsp;&nbsp;&nbsp;导出").addClass("cursor_pointer");
                    }
                } else if (flag == 'unitShow') {
                    $(this).find('img').attr('src', '../../../images/treese.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/tweo.png');
                }
            })
            $("#btnTopOperate20").click(function () {
                $(this).removeClass("active").addClass("active").removeClass("btn-select").addClass("btn-select");
                page.logic.queryMostOperate();
            });
            /**
             * 查询
             */
            $('#btnSearch').click(function () {
                if (OPAL.util.checkDateIsValid() == true) {
                    page.logic.search();
                }
            })
            // 导出
            $("#expBtn").click(function () {
                if ($("#checkMonitorType").is(":checked")) {
                    page.logic.exportMonitorTypeExcel();
                } else {
                    page.logic.exportExcel();
                }
            })
            // 最频繁的报警导出
            $("#exportOftenBtn").click(function () {
                page.logic.exportTop20Excel();
            })
            // 车间导出
            $("#exportcjBtn").click(function () {
                page.logic.exportcjBtn();
            })
            // 车间导出
            $("#checkMonitorType").click(function () {
                page.logic.checkMonitorTypeClick();
            })
        },
        data: {
            param: {},
            subParam: {},
            row: null,
            click: '',
            top20Param: {}
        },
        logic: {
            checkMonitorTypeClick: function () {
                if ($("#checkMonitorType").is(":checked")) {
                    $('#MostAlarmOperateTablecj').bootstrapTable('destroy');
                    $('#floodTable').bootstrapTable('destroy');
                    page.logic.initOpetateMonitorTypeTablecj({});
                    page.logic.initMonitorTypeTableDisplay(JSON.stringify(""));
                    $('#priorityDiv').css('display', 'none');
                    $('#monitorTypeDiv').css('display', 'inline-block');
                } else {
                    $('#MostAlarmOperateTablecj').bootstrapTable('destroy');
                    $('#floodTable').bootstrapTable('destroy');
                    page.logic.initOpetateTablecj({});
                    page.logic.initTableDisplay(JSON.stringify(""));
                    $('#priorityDiv').css('display', 'inline-block');
                    $('#monitorTypeDiv').css('display', 'none');
                }
            },
            /***
             * 查询
             */
            search: function () {
                if ($("#checkMonitorType").is(":checked")) {
                    DataWorkshop = DataWorkshopMonitorType;
                    DataPrdtcell = DataPrdtcellMonitorType;
                    DataUnit = DataUnitMonitorType;
                    AssessAll = AssessAllMonitorType;
                    alarmNumStattUrl = alarmNumStattMonitorTypeUrl;
                } else {
                    DataWorkshop = DataWorkshopPriority;
                    DataPrdtcell = DataPrdtcellPriority;
                    DataUnit = DataUnitPriority;
                    AssessAll = AssessAllPriority;
                    alarmNumStattUrl = alarmNumStattPriorityUrl;
                }

                page.data.click = ''
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) {
                    return;
                }
                page.data.param = OPAL.form.getData("searchForm");
                var unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                page.data.param.unitIds = unitIds;
                page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);

                page.data.top20Param = OPAL.form.getData("searchForm");
                page.data.top20Param.topType = 20;
                page.data.top20Param.searchType = 2;

                $("#btnSearch").prop('disabled', true);
                $("#totals").hide();

                if ($("#checkShop").is(":checked")) {
                    document.querySelector('.contentcj').style.display = 'block'
                    document.querySelector('.ibox-tools').style.display = 'block'
                    document.querySelector('.contentmr').style.display = 'none'
                    $.ajax({
                        url: DataWorkshop,
                        data: page.data.param,
                        dataType: 'json',
                        success: function (data) {
                            var result = $.ET.toObjectArr(data);
                            if ($("#checkMonitorType").is(":checked")) {
                                page.logic.initMonitorTypeFloodChartCom(JSON.stringify(result), 'floodAlarmChartcj');
                            } else {
                                page.logic.initFloodChartCom(JSON.stringify(result), 'floodAlarmChartcj');
                            }
                        },
                        error: function () {
                            // $("#myModal").modal('hide');
                        },
                        complete: function () {
                            $("#btnSearch").prop('disabled', false);
                        }
                    });
                } else {
                    document.querySelector('.contentcj').style.display = 'none'
                    document.querySelector('.ibox-tools').style.display = 'none'
                    document.querySelector('.contentmr').style.display = 'block'
                    $.ajax({
                        url: alarmNumStattUrl,
                        data: page.data.param,
                        dataType: 'json',
                        success: function (data) {
                            var result = $.ET.toObjectArr(data);
                            if (result.length > 0) {
                                $("#totalNum").html(result[0].allTotalAlarmQuantity);
                                $("#totals").show();
                            }
                            if ($("#checkMonitorType").is(":checked")) {
                                page.logic.initMonitorTypeFloodChart(JSON.stringify(result));
                                page.logic.initMonitorTypeTableDisplay(JSON.stringify(result));
                            } else {
                                page.logic.initFloodChart(JSON.stringify(result));
                                page.logic.initTableDisplay(JSON.stringify(result));
                            }

                        },
                        complete: function () {
                            $("#btnSearch").prop('disabled', false);
                        }
                    });
                }
            },

            workshopSearch: function () {
                $('.contentcj').display('block');
                $('.ibox-tools').display('block');
                $('.contentmr').display('none');
                $.ajax({
                    url: DataWorkshop,
                    data: page.data.param,
                    dataType: 'json',
                    success: function (data) {
                        var result = $.ET.toObjectArr(data);
                        if ($("#checkMonitorType").is(":checked")) {
                            page.logic.initMonitorTypeFloodChartCom(JSON.stringify(result), 'floodAlarmChartcj');
                        } else {
                            page.logic.initFloodChartCom(JSON.stringify(result), 'floodAlarmChartcj');
                        }
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
            },


            // 导出报警数
            exportExcel: function () {
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                let obj = OPAL.form.getData("searchForm");
                var unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                obj.unitIds = unitIds;
                obj.startTime = OPAL.util.dateFormat(page.data.param.startTime, 'yyyy-MM-dd HH:mm:ss');
                obj.endTime = OPAL.util.dateFormat(page.data.param.endTime, 'yyyy-MM-dd HH:mm:ss');
                var searchData = obj;
                var titleArray = new Array(
                    {"value": "序号", 'key': "index"},
                    {'value': "装置", 'key': "sname"},
                    {'value': "紧急次数", 'key': "emergencyAlarmQuantity"},
                    {'value': "重要次数", 'key': "importantAlarmQuantity"},
                    {'value': "一般次数", 'key': "generalAlarmQuantity"},
                    {'value': "优先级为空次数", 'key': "nullAlarmQuantity"},
                    {'value': "合计", 'key': "totalAlarmQuantity"}
                );
                searchData.titles = JSON.stringify(titleArray);
                OPAL.ui.setExportExcelData("formExPort", searchData);
                $('#formExPort').attr('action', exportUrl);
                $('#formExPort').submit();
            },
            // 导出报警数
            exportMonitorTypeExcel: function () {
                //进行时间校验
                // if (!OPAL.util.checkDateIsValid()) return;
                let obj = page.data.param;
                // var unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                // obj.unitIds = unitIds;
                obj.startTime = OPAL.util.dateFormat(page.data.param.startTime, 'yyyy-MM-dd HH:mm:ss');
                obj.endTime = OPAL.util.dateFormat(page.data.param.endTime, 'yyyy-MM-dd HH:mm:ss');
                var titleArray = new Array(
                    {"value": "序号", 'key': "index"},
                    {'value': "装置", 'key': "sname"},
                    {'value': "工艺", 'key': "technology"},
                    {'value': "设备", 'key': "device"},
                    {'value': "安全", 'key': "safe"},
                    {'value': "生产", 'key': "other"},
                    {'value': "-", 'key': "nothing"},
                    {'value': "合计", 'key': "totalAlarmQuantity"}
                );
                obj.titles = JSON.stringify(titleArray);
                var url = exportMonitorTypeUrl + "?" + $.param(obj);
                window.location = url;
            },
            // 导出最频繁的报警TOP20
            exportTop20Excel: function () {
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                // page.logic.toggleDisplayName();
                let searchData = {};
                searchData.priority = page.data.param.priority;
                searchData.startTime = moment(page.data.param.startTime).format('YYYY-MM-DD HH:mm:ss');
                searchData.endTime = moment(page.data.param.endTime).format('YYYY-MM-DD HH:mm:ss');
                searchData.unitId = unitId;
                searchData.alarmFlagId = page.data.param.alarmFlagId;
                var titleArray = new Array(
                    {"value": "序号", 'key': "index"},
                    {'value': "装置", 'key': "unitName"},
                    {'value': "生产单元", 'key': "prdtCellName"},
                    {'value': "位置描述", 'key': "location"},
                    {'value': "位号", 'key': "tag"},
                    {'value': "报警等级", 'key': "alarmFlag"},
                    {'value': "报警数", 'key': "alarmCount"},
                    {'value': "优先级", 'key': "priorityName"},
                    {'value': "专业", 'key': "monitorTypeName"}
                );
                page.data.top20Param.titles = JSON.stringify(titleArray);
                page.data.top20Param.topType = 20;
                page.data.top20Param.searchType = 2;
                var url = top20SingleExportExcelUrl + "?" + $.param(page.data.top20Param);

                // $('#formExPort2').submit();
                window.location = url;
            },
            // 车间导出
            exportcjBtn: function () {
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                let obj = OPAL.form.getData("searchForm");
                var unitIds = OPAL.ui.getComboMultipleSelect.getValues("unitIds", false);
                obj.unitIds = unitIds;
                obj.startTime = OPAL.util.dateFormat(page.data.param.startTime, 'yyyy-MM-dd HH:mm:ss');
                obj.endTime = OPAL.util.dateFormat(page.data.param.endTime, 'yyyy-MM-dd HH:mm:ss');
                obj.topType = 20;
                obj.searchType = 2;
                var titleArray = new Array(
                    {"value": "序号", 'key': "index"},
                    {'value': "装置", 'key': "unitName"},
                    {'value': "生产单元", 'key': "prdtCellName"},
                    {'value': "位置描述", 'key': "location"},
                    {'value': "位号", 'key': "tag"},
                    {'value': "报警等级", 'key': "alarmFlag"},
                    {'value': "报警数", 'key': "alarmCount"},
                    {'value': "优先级", 'key': "priorityName"},
                    {'value': "百分比(%)", 'key': "percent"}
                );
                obj.workshopCodes = page.data.workshopCodes;
                obj.unitCodes = page.data.unitCodes;
                obj.prdtCellId = page.data.prdtCellId;
                if (page.data.click == 'floodAlarmChartcj') {
                    obj.unitCodes = []
                    obj.prdtCellId = []
                } else if (page.data.click == 'floodAlarmChartzz') {
                    obj.prdtCellId = []
                }
                var searchData = obj;
                searchData.titles = JSON.stringify(titleArray);
                OPAL.ui.setExportExcelData("formExPort3", searchData);
                $('#formExPort3').attr('action', allSingleExportExcel);
                $('#formExPort3').submit();
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function (node, checked) {
                        var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (unitIds != undefined && unitIds.length == 1) {
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    }
                }, false);
            },
            /**
             * 初始化图表
             * @param data
             */
            initFloodChart: function (data) {
                var results = JSON.parse(data);
                if (floodAlarmChart && !floodAlarmChart.isDisposed()) {
                    floodAlarmChart.clear();
                    floodAlarmChart.dispose();
                }
                if (results == undefined || results.length == 0) {
                    floodAlarmChart = OPAL.ui.chart.initEmptyChart('floodAlarmChart');
                    return;
                }
                var xAxis = [];
                var emergencyAlarmQuantity = [];
                var importantAlarmQuantity = [];
                var generalAlarmQuantity = [];
                var nullAlarmQuantity = [];
                unitId = results[0].unitId;
                unit = results[0].sname;
                for (var i = 0; i < results.length; i++) {
                    if (results[i].sname) {
                        xAxis.push(results[i].sname)
                    } else if (results[i].name) {
                        xAxis.push(results[i].name)
                    }
                    emergencyAlarmQuantity.push(results[i].emergencyAlarmQuantity);
                    importantAlarmQuantity.push(results[i].importantAlarmQuantity);
                    generalAlarmQuantity.push(results[i].generalAlarmQuantity);
                    nullAlarmQuantity.push(results[i].nullAlarmQuantity);
                }
                var option = {
                    color: ['#F4D312', '#FE6732', '#CD1515', '#C9C9C9'],
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            var colors = ['#F4D312', '#FE6732', '#CD1515', '#C9C9C9'];
                            var total = 0;
                            for (var i = 0; i < params.length; i++) {
                                total += parseInt(params[i].value);
                            }
                            var tips = '<div>' + params[0].name + '<span style="margin-left: 10px;font-weight: bolder">' + page.logic.ThreeBits(total) + '</span></div>';
                            for (var i = 0; i < params.length; i++) {
                                tips += '<div><span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + colors[i] + ';"></span>' + params[i].seriesName + '：' + page.logic.ThreeBits(params[i].value) + '</div>';
                                total += parseInt(params[i].value);
                            }
                            return tips;
                        }
                    },
                    legend: {
                        itemHeight: 8,
                        itemWidth: 18,
                        data: ['一般', '重要', '紧急', '空'],
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        top: '10%',
                        height: '270px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            show: true,
                            splitNumber: 5,
                            textStyle: {
                                fontSize: 12,
                                color: '#000'
                            },
                        },
                        data: xAxis,
                    }],
                    dataZoom: [{
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        // left: '9%',
                        bottom: -5,
                        start: 0,
                        end: 100 //初始化滚动条
                    }],
                    yAxis: {
                        splitLine: {
                            show: false
                        },
                        type: 'value'
                    },
                    series: [{
                        name: '一般',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: generalAlarmQuantity
                    }, {
                        name: '重要',
                        type: 'bar',
                        barWidth: '20px',
                        stack: 'sum',
                        data: importantAlarmQuantity,
                    }, {
                        name: '紧急',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: emergencyAlarmQuantity
                    }, {
                        name: '空',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: nullAlarmQuantity
                    }]
                };
                floodAlarmChart = echarts.init(document.getElementById('floodAlarmChart'));
                floodAlarmChart.setOption(option);
                floodChartConfig = option;
                floodAlarmChart.on('click', function (param) {
                    var indexs = param.dataIndex;
                    unitId = results[indexs].unitId;
                    page.data.top20Param.unitIds = [unitId];
                    unit = param.name;
                    page.logic.queryMostOperate();
                    page.logic.queryAlarmNum();

                });
                page.logic.queryMostOperate();
                page.logic.queryAlarmNum();
            },


            /**
             * 初始化图表
             * @param data
             */
            initMonitorTypeFloodChart: function (data) {
                var results = JSON.parse(data);
                if (floodAlarmChart && !floodAlarmChart.isDisposed()) {
                    floodAlarmChart.clear();
                    floodAlarmChart.dispose();
                }
                if (results == undefined || results.length == 0) {
                    floodAlarmChart = OPAL.ui.chart.initEmptyChart('floodAlarmChart');
                    return;
                }
                var xAxis = [];
                var technology = [];
                var device = [];
                var safe = [];
                var other = [];
                var nothing = [];
                unitId = results[0].unitId;
                unit = results[0].sname;
                for (var i = 0; i < results.length; i++) {
                    if (results[i].sname) {
                        xAxis.push(results[i].sname)
                    } else if (results[i].name) {
                        xAxis.push(results[i].name)
                    }
                    technology.push(results[i].technology);
                    device.push(results[i].device);
                    safe.push(results[i].safe);
                    other.push(results[i].other);
                    nothing.push(results[i].nothing);
                }
                var option = {
                    color: ['#F4D312', '#FE6732', '#CD1515', '#C9C9C9'],
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            var colors = ['#F4D312', '#FE6732', '#CD1515', '#C9C9C9'];
                            var total = 0;
                            for (var i = 0; i < params.length; i++) {
                                total += parseInt(params[i].value);
                            }
                            var tips = '<div>' + params[0].name + '<span style="margin-left: 10px;font-weight: bolder">' + page.logic.ThreeBits(total) + '</span></div>';
                            for (var i = 0; i < params.length; i++) {
                                tips += '<div><span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + colors[i] + ';"></span>' + params[i].seriesName + '：' + page.logic.ThreeBits(params[i].value) + '</div>';
                                total += parseInt(params[i].value);
                            }
                            return tips;
                        }
                    },
                    legend: {
                        itemHeight: 8,
                        itemWidth: 18,
                        data: ['工艺', '设备', '安全', '生产', '-'],
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        top: '10%',
                        height: '270px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            show: true,
                            splitNumber: 5,
                            textStyle: {
                                fontSize: 12,
                                color: '#000'
                            },
                        },
                        data: xAxis,
                    }],
                    dataZoom: [{
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        bottom: -5,
                        start: 0,
                        end: 100 //初始化滚动条
                    }],
                    yAxis: {
                        splitLine: {
                            show: false
                        },
                        type: 'value'
                    },
                    series: [{
                        name: '工艺',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: technology
                    }, {
                        name: '设备',
                        type: 'bar',
                        barWidth: '20px',
                        stack: 'sum',
                        data: device,
                    }, {
                        name: '安全',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: safe
                    }, {
                        name: '生产',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: other
                    }, {
                        name: '-',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: nothing
                    }]
                };
                floodAlarmChart = echarts.init(document.getElementById('floodAlarmChart'));
                floodAlarmChart.setOption(option);
                floodChartConfig = option;
                floodAlarmChart.on('click', function (param) {
                    var indexs = param.dataIndex;
                    unitId = results[indexs].unitId;
                    page.data.top20Param.unitIds = [unitId];
                    unit = param.name;
                    page.logic.queryMostOperate();
                    page.logic.queryAlarmNum();

                });
                page.logic.queryMostOperate();
                page.logic.queryAlarmNum();
            },

            // 展示空数据的图表
            EmptyChart: function () {
                tabChart.floodAlarmChartcj = OPAL.ui.chart.initEmptyChart('floodAlarmChartcj');
                tabChart.floodAlarmChartzz = OPAL.ui.chart.initEmptyChart('floodAlarmChartzz');
                tabChart.floodAlarmChartdy = OPAL.ui.chart.initEmptyChart('floodAlarmChartdy');
            },

            /**
             * 初始化图表
             * @param data
             */
            initFloodChartCom: function (data, Chartid, func) {
                var results = JSON.parse(data);
                // page.logic.queryMostOperate();
                if (tabChart[Chartid] && !tabChart[Chartid].isDisposed()) {
                    tabChart[Chartid].clear();
                    tabChart[Chartid].dispose();
                }
                if (results == undefined || results.length == 0) {
                    // tabChart[Chartid] = OPAL.ui.chart.initEmptyChart(Chartid);
                    page.logic.EmptyChart()
                    return;
                }
                var xAxis = [];
                var emergencyAlarmQuantity = [];
                var importantAlarmQuantity = [];
                var generalAlarmQuantity = [];
                var nullAlarmQuantity = [];
                unitId = results[0].unitId;
                unit = results[0].sname;
                var nextparam = {
                    code: '',
                    num: 0
                }
                for (var i = 0; i < results.length; i++) {
                    if (results[i].sname) {
                        xAxis.push(results[i].sname)
                    } else if (results[i].name) {
                        xAxis.push(results[i].name)
                    }
                    emergencyAlarmQuantity.push($.parseJSON(results[i].emergencyAlarmQuantity));
                    importantAlarmQuantity.push($.parseJSON(results[i].importantAlarmQuantity));
                    generalAlarmQuantity.push($.parseJSON(results[i].generalAlarmQuantity));
                    nullAlarmQuantity.push($.parseJSON(results[i].nullAlarmQuantity))
                    if (nextparam.num < Math.abs($.parseJSON(results[i].emergencyAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].emergencyAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].importantAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].importantAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].generalAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].generalAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].nullAlarmQuantity))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].nullAlarmQuantity))
                        nextparam.code = results[i].code
                    }
                }
                var option = {
                    color: ['#F4D312', '#FE6732', '#CD1515', '#C9C9C9'],
                    tooltip: {
                        trigger: 'axis',
                    },
                    legend: {
                        itemHeight: 8,
                        itemWidth: 18,
                        data: ['一般', '重要', '紧急', '空'],
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        top: '10%',
                        height: '270px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            show: true,
                            splitNumber: 5,
                            textStyle: {
                                fontSize: 12,
                                color: '#000'
                            },
                        },
                        data: xAxis,
                    }],
                    dataZoom: [{
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        // left: '9%',
                        bottom: -5,
                        start: 0,
                        end: 100 //初始化滚动条
                    }],
                    yAxis: {
                        splitLine: {
                            show: false
                        },
                        type: 'value',
                        name: "(次数)"
                    },
                    series: [{
                        name: '一般',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: generalAlarmQuantity
                    }, {
                        name: '重要',
                        type: 'bar',
                        barWidth: '20px',
                        stack: 'sum',
                        data: importantAlarmQuantity,
                    }, {
                        name: '紧急',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: emergencyAlarmQuantity
                    }, {
                        name: '空',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: nullAlarmQuantity
                    }]
                };
                tabChart[Chartid] = echarts.init(document.getElementById(Chartid));
                tabChart[Chartid].setOption(option);
                page.data.param.unitIds = [unitId];
                page.data.unitCodeList = [unitId];
                floodChartConfig = option;
                tabChart[Chartid].on('click', function (param) {
                    var indexs = param.dataIndex;
                    page.data.click = Chartid
                    if (Chartid == 'floodAlarmChartcj') {
                        page.data.param.workshopCodes = [results[indexs].code];
                        page.data.workshopCodes = [results[indexs].code]
                        page.logic.querynextChart('floodAlarmChartzz');
                    } else if (Chartid == 'floodAlarmChartzz') {
                        page.data.param.unitCodes = [results[indexs].code];
                        page.data.unitCodes = [results[indexs].code]
                        page.logic.querynextChart('floodAlarmChartdy');
                    } else if (Chartid == 'floodAlarmChartdy') {
                        page.data.param.prdtCellId = [results[indexs].code];
                        page.data.prdtCellId = [results[indexs].code];
                        page.logic.queryMostOperatecj();
                    }
                    // page.logic.queryMostOperate();
                });

                if (Chartid == 'floodAlarmChartcj') {
                    page.data.param.workshopCodes = [nextparam.code];
                    page.data.workshopCodes = [nextparam.code];
                    page.logic.querynextChart('floodAlarmChartzz');
                } else if (Chartid == 'floodAlarmChartzz') {
                    page.data.param.unitCodes = [nextparam.code];
                    page.data.unitCodes = [nextparam.code];
                    page.logic.querynextChart('floodAlarmChartdy');
                } else if (Chartid == 'floodAlarmChartdy') {
                    page.data.param.prdtCellId = [nextparam.code];
                    page.data.prdtCellId = [nextparam.code];
                    // page.logic.queryMostOperate();
                }
            },

            initMonitorTypeFloodChartCom: function (data, Chartid, func) {
                var results = JSON.parse(data);
                // page.logic.queryMostOperate();
                if (tabChart[Chartid] && !tabChart[Chartid].isDisposed()) {
                    tabChart[Chartid].clear();
                    tabChart[Chartid].dispose();
                }
                if (results == undefined || results.length == 0) {
                    // tabChart[Chartid] = OPAL.ui.chart.initEmptyChart(Chartid);
                    page.logic.EmptyChart()
                    return;
                }
                var xAxis = [];
                var technology = [];
                var device = [];
                var safe = [];
                var other = [];
                var nothing = [];
                unitId = results[0].unitId;
                unit = results[0].sname;
                var nextparam = {
                    code: '',
                    num: 0
                }
                for (var i = 0; i < results.length; i++) {
                    if (results[i].sname) {
                        xAxis.push(results[i].sname)
                    } else if (results[i].name) {
                        xAxis.push(results[i].name)
                    }
                    technology.push($.parseJSON(results[i].technology));
                    device.push($.parseJSON(results[i].device));
                    safe.push($.parseJSON(results[i].safe));
                    other.push($.parseJSON(results[i].other));
                    nothing.push($.parseJSON(results[i].nothing))
                    if (nextparam.num < Math.abs($.parseJSON(results[i].technology))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].technology))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].device))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].device))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].safe))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].safe))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].other))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].other))
                        nextparam.code = results[i].code
                    }
                    if (nextparam.num < Math.abs($.parseJSON(results[i].nothing))) {
                        nextparam.num = Math.abs($.parseJSON(results[i].nothing))
                        nextparam.code = results[i].code
                    }
                }
                var option = {
                    color: ['#F4D312', '#FE6732', '#CD1515', '#C9C9C9', '#A80826'],
                    tooltip: {
                        trigger: 'axis',
                    },
                    legend: {
                        itemHeight: 8,
                        itemWidth: 18,
                        data: ['工艺', '设备', '安全', '生产', '-'],
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        top: '10%',
                        height: '270px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            show: true,
                            splitNumber: 5,
                            textStyle: {
                                fontSize: 12,
                                color: '#000'
                            },
                        },
                        data: xAxis,
                    }],
                    dataZoom: [{
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        bottom: -5,
                        start: 0,
                        end: 100 //初始化滚动条
                    }],
                    yAxis: {
                        splitLine: {
                            show: false
                        },
                        type: 'value',
                        name: "(次数)"
                    },
                    series: [{
                        name: '工艺',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: technology
                    }, {
                        name: '设备',
                        type: 'bar',
                        barWidth: '20px',
                        stack: 'sum',
                        data: device,
                    }, {
                        name: '安全',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: safe
                    }, {
                        name: '生产',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: other
                    }, {
                        name: '-',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: nothing
                    }]
                };
                tabChart[Chartid] = echarts.init(document.getElementById(Chartid));
                tabChart[Chartid].setOption(option);
                page.data.param.unitIds = [unitId];
                page.data.unitCodeList = [unitId];
                floodChartConfig = option;
                tabChart[Chartid].on('click', function (param) {
                    var indexs = param.dataIndex;
                    page.data.click = Chartid
                    if (Chartid == 'floodAlarmChartcj') {
                        page.data.param.workshopCodes = [results[indexs].code];
                        page.data.workshopCodes = [results[indexs].code]
                        page.logic.querynextMonitorTypeChart('floodAlarmChartzz');
                    } else if (Chartid == 'floodAlarmChartzz') {
                        page.data.param.unitCodes = [results[indexs].code];
                        page.data.unitCodes = [results[indexs].code]
                        page.logic.querynextMonitorTypeChart('floodAlarmChartdy');
                    } else if (Chartid == 'floodAlarmChartdy') {
                        page.data.param.prdtCellId = [results[indexs].code];
                        page.data.prdtCellId = [results[indexs].code];
                        page.logic.queryMonitorTypeMostOperatecj();
                    }
                });

                if (Chartid == 'floodAlarmChartcj') {
                    page.data.param.workshopCodes = [nextparam.code];
                    page.data.workshopCodes = [nextparam.code];
                    page.logic.querynextMonitorTypeChart('floodAlarmChartzz');
                } else if (Chartid == 'floodAlarmChartzz') {
                    page.data.param.unitCodes = [nextparam.code];
                    page.data.unitCodes = [nextparam.code];
                    page.logic.querynextMonitorTypeChart('floodAlarmChartdy');
                } else if (Chartid == 'floodAlarmChartdy') {
                    page.data.param.prdtCellId = [nextparam.code];
                    page.data.prdtCellId = [nextparam.code];
                }
            },

            // 获取chart数据
            querynextChart: function (Chartid) {
                var url = ''
                if (Chartid == 'floodAlarmChartzz') {
                    url = DataUnit
                } else if (Chartid == 'floodAlarmChartdy') {
                    url = DataPrdtcell
                }
                $.ajax({
                    url: url,
                    data: page.data.param,
                    dataType: 'json',
                    success: function (data) {
                        var result = $.ET.toObjectArr(data);
                        page.logic.initFloodChartCom(JSON.stringify(result), Chartid);
                        if (Chartid == 'floodAlarmChartdy') {
                            page.logic.queryMostOperatecj();
                        }
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
            },

            // 获取chart数据
            querynextMonitorTypeChart: function (Chartid) {
                var url = ''
                if (Chartid == 'floodAlarmChartzz') {
                    url = DataUnit
                } else if (Chartid == 'floodAlarmChartdy') {
                    url = DataPrdtcell
                }
                $.ajax({
                    url: url,
                    data: page.data.param,
                    dataType: 'json',
                    success: function (data) {
                        var result = $.ET.toObjectArr(data);
                        page.logic.initMonitorTypeFloodChartCom(JSON.stringify(result), Chartid);
                        if (Chartid == 'floodAlarmChartdy') {
                            page.logic.queryMonitorTypeMostOperatecj();
                        }
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
            },
            /**
             * 加载 下方车间表格
             */
            queryMostOperatecj: function () {
                $("#MostAlarmOperateTablecj").bootstrapTable('refresh', {
                    "url": alarmDurStattPageUrl,
                    "pageNumber": 1
                });
            },

            queryMonitorTypeMostOperatecj: function () {
                $("#MostAlarmOperateTablecj").bootstrapTable('refresh', {
                    "url": alarmDurStattPageUrl,
                    "pageNumber": 1
                });
            },

            //初始化下方车间表格
            initOpetateTablecj: function () {
                page.logic.initBootstrapTablecj("MostAlarmOperateTablecj", {
         /*           detailView: true,
                    cache: false,*/
                    columns: [{
                        title: "序号", formatter: function (value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1) * data.pageSize;
                        }, rowspan: 1, align: 'center', width: '80px'
                    }, {
                        field: 'unitName', title: '装置', align: 'center', width: '180px'
                    }, {
                        field: 'prdtCellName', title: '生产单元', align: 'center', width: '180px'
                    }, {
                        field: 'location', title: '参数名称', align: 'center',
                    }, {
                        field: 'tag', title: '位号', align: 'center', width: '200px'
                    }, {
                        field: 'monitorTypeStr', title: '专业', align: 'center', width: '60px'
                    }, {
                        field: 'priorityName', title: "优先级", align: 'center', width: '60px'
                    }, {
                        field: 'alarmTime', title: '报警时间', align: 'center', width: '200px'
                    }, {
                        field: 'recoveryTime', title: '结束时间', align: 'center', width: '200px'
                    }, {
                        field: 'continuousHour', title: '时长(分钟)', align: 'center', width: '80px'
                    }, {
                        field: 'alarmFlagName', title: '报警等级', align: 'center', width: '80px'
                    }]/*,
                    onExpandRow: function (index, row, $detail) {
                        page.data.subParam.unitIds = row['unitCode'];
                        page.data.subParam.alarmFlagId = row['alarmFlagId'];
                        page.data.subParam.tag = row['tag'];
                        page.data.subParam.endTime = page.data.param.endTime;
                        page.data.subParam.startTime = page.data.param.startTime;
                        page.data.subParam.priority = row['priority'];
                        page.logic.initCausalSubTablecj(index, row, $detail);
                    }*/
                }, page.logic.queryParams);
                var tableOption = $('#MostAlarmOperateTablecj').bootstrapTable('getOptions');
                $("#MostAlarmOperateTablecj").bootstrapTable('refreshOptions', tableOption);
            },
            //初始化下方车间表格
            initOpetateMonitorTypeTablecj: function () {
                page.logic.initBootstrapTablecj("MostAlarmOperateTablecj", {
            /*        detailView: true,
                    cache: false,*/
                    columns: [{
                        title: "序号", formatter: function (value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1) * data.pageSize;
                        }, rowspan: 1, align: 'center', width: '80px'
                    }, {
                        field: 'unitName', title: '装置', align: 'center', width: '180px'
                    }, {
                        field: 'prdtCellName', title: '生产单元', align: 'center', width: '180px'
                    }, {
                        field: 'location', title: '参数名称', align: 'center',
                    }, {
                        field: 'tag', title: '位号', align: 'center', width: '200px'
                    }, {
                        field: 'monitorTypeStr', title: '专业', align: 'center', width: '60px'
                    }, {
                        field: 'priorityName', title: "优先级", align: 'center', width: '60px'
                    }, {
                        field: 'alarmTime', title: '报警时间', align: 'center', width: '200px'
                    }, {
                        field: 'recoveryTime', title: '结束时间', align: 'center', width: '200px'
                    }, {
                        field: 'continuousHour', title: '时长(分钟)', align: 'center', width: '80px'
                    }, {
                        field: 'alarmFlagName', title: '报警等级', align: 'center', width: '80px'
                    }]/*,
                    onExpandRow: function (index, row, $detail) {
                        page.data.subParam.unitIds = row['unitCode'];
                        page.data.subParam.alarmFlagId = row['alarmFlagId'];
                        page.data.subParam.tag = row['tag'];
                        page.data.subParam.endTime = page.data.param.endTime;
                        page.data.subParam.startTime = page.data.param.startTime;
                        page.data.subParam.priority = row['priority'];
                        page.logic.initCausalSubTablecj(index, row, $detail);
                    }*/
                }, page.logic.queryParams);
                var tableOption = $('#MostAlarmOperateTablecj').bootstrapTable('getOptions');
                $("#MostAlarmOperateTablecj").bootstrapTable('refreshOptions', tableOption);
            },

            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    topType: 20,
                    searchType: 2
                };
                param.unitCode = page.data.param.unitCodes;
                if (page.data.click == 'floodAlarmChartcj') {
                    param.unitCode = []
                    param.prdtCellId = []
                } else if (page.data.click == 'floodAlarmChartzz') {
                    param.prdtCellId = []
                }
                return $.extend(page.data.param, param);
            },

            initBootstrapTablecj: function (tableID, config, queryParams) {
                var _config = {
                    method: 'get',
                    url: '',
                    cache: false,
                    pagination: true, //启动分页
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "server", // 表示服务端请求 后台分页
                    //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder
                    //设置为limit可以获取limit, offset, search, sort, order
                    queryParamsType: "undefined",
                    queryParams: queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#' + tableID).find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                };
                $.extend(true, _config, config);
                $('#' + tableID).bootstrapTable(_config);
            },

            /**
             * 初始化Table数据
             * @param data
             */
            initTableDisplaycj: function (data) {
                var results = JSON.parse(data);
                var options = {
                    // detailView: true, //父子表
                    striped: true, //是否显示行间隔色
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    height: 350,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'sname',
                        title: '装置',
                        align: 'left',
                    }, {
                        field: 'emergencyAlarmQuantity',
                        title: '紧急时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.emergencyAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.emergencyAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',1)">' + row.emergencyAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'importantAlarmQuantity',
                        title: '重要时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.importantAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.importantAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',2)">' + row.importantAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'generalAlarmQuantity',
                        title: '一般时长(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.generalAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.generalAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',3)">' + row.generalAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'nullAlarmQuantity',
                        title: '优先级为空(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.nullAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.nullAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',9)">' + row.nullAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'totalAlarmQuantity',
                        title: '合计(分钟)',
                        align: 'right',
                        formatter: function (value, row, index) {
                            return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',-1)">' + row.totalAlarmQuantity + '</a>'
                        }
                    }]
                };
                $('#floodTable').bootstrapTable(options);
                $('#floodTable').bootstrapTable('refreshOptions', options);
                if (results == undefined) {
                    results = [];
                }
                $("#floodTable").bootstrapTable("load", results);
            },

            /**
             * 初始化车间二级列表
             */
            initCausalSubTablecj: function (index, row, $detail) {
                alarmFlagId = row.alarmFlagId;
                tag = row.tag;
                priority = row.priority;
                var subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                OPAL.ui.initBootstrapTable(subId, {
                    url: alarmDurationStatt,
                    striped: true,
                    pagination: false,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        align: 'center',
                        width: '80'
                    }, {
                        field: 'alarmTime',
                        title: '报警时间',
                        align: 'right',
                    }, {
                        field: 'responseTime',
                        title: '结束时间',
                        align: 'center',
                    }, {
                        field: 'monitorTypeStr',
                        title: '专业',
                        align: 'left',
                    }],
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                }, page.logic.subQueryParams)
            },

            /**
             * 查询子表格参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            subQueryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    alarmFlagId: alarmFlagId,
                    tag: tag,
                    priority: priority,
                    now: Math.random()
                };
                return $.extend(page.data.subParam, param);
            },


            ThreeBits: function (value) {
                var value = String(value);
                var temp = value.split('').reverse().join('').match(/(\d{1,3})/g);
                var result = (Number(value) < 0 ? "-" : "") + temp.join(',').split('').reverse().join('');
                return result;
            },


            /**
             * 初始化Table数据
             * @param data
             */
            initTableDisplay: function (data) {
                var results = JSON.parse(data);
                var options = {
                    // detailView: true, //父子表
                    striped: true, //是否显示行间隔色
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    height: 350,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'sname',
                        title: '装置',
                        align: 'left',
                    }, {
                        field: 'emergencyAlarmQuantity',
                        title: '紧急次数',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.emergencyAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.emergencyAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',1)">' + row.emergencyAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'importantAlarmQuantity',
                        title: '重要次数',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.importantAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.importantAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',2)">' + row.importantAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'generalAlarmQuantity',
                        title: '一般次数',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.generalAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.generalAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',3)">' + row.generalAlarmQuantity + '</a>'
                            }
                        }
                    }, {
                        field: 'nullAlarmQuantity',
                        title: '优先级为空次数',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.nullAlarmQuantity * 1 == 0) {
                                return '<a style="color: #333;">' + row.nullAlarmQuantity + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',9)">' + row.nullAlarmQuantity + '</a>'
                            }
                        }
                    },
                        {
                            field: 'totalAlarmQuantity',
                            title: '合计',
                            align: 'right',
                            formatter: function (value, row, index) {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',-1)">' + row.totalAlarmQuantity + '</a>'
                            }
                        }
                    ]
                };
                $('#floodTable').bootstrapTable(options);
                $('#floodTable').bootstrapTable('refreshOptions', options);
                if (results == undefined) {
                    results = [];
                }
                $("#floodTable").bootstrapTable("load", results);

                $("#floodTable").colResizable({
                    liveDrag: true
                });
            },
            /**
             * 初始化Table数据
             * @param data
             */
            initMonitorTypeTableDisplay: function (data) {
                var results = JSON.parse(data);
                var options = {
                    // detailView: true, //父子表
                    striped: true, //是否显示行间隔色
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    height: 350,
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'sname',
                        title: '装置',
                        align: 'left',
                    }, {
                        field: 'technology',
                        title: '工艺',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.technology * 1 == 0) {
                                return '<a style="color: #333;">' + row.technology + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',1)">' + row.technology + '</a>'
                            }
                        }
                    }, {
                        field: 'device',
                        title: '设备',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.device * 1 == 0) {
                                return '<a style="color: #333;">' + row.device + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',2)">' + row.device + '</a>'
                            }
                        }
                    }, {
                        field: 'safe',
                        title: '安全',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.safe * 1 == 0) {
                                return '<a style="color: #333;">' + row.safe + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',3)">' + row.safe + '</a>'
                            }
                        }
                    }, {
                        field: 'other',
                        title: '生产',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.other * 1 == 0) {
                                return '<a style="color: #333;">' + row.other + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',9)">' + row.other + '</a>'
                            }
                        }
                    }, {
                        field: 'nothing',
                        title: '-',
                        align: 'right',
                        formatter: function (value, row, index) {
                            if (row.nothing * 1 == 0) {
                                return '<a style="color: #333;">' + row.nothing + '</a>'
                            } else {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',9)">' + row.nothing + '</a>'
                            }
                        }
                    },
                        {
                            field: 'totalAlarmQuantity',
                            title: '合计',
                            align: 'right',
                            formatter: function (value, row, index) {
                                return '<a style="text-decoration: underline;color: #348fe2;cursor: pointer;" onclick="page.logic.showOperateDetail(\'' + row.sname + '\',\'' + row.unitId + '\',-1)">' + row.totalAlarmQuantity + '</a>'
                            }
                        }
                    ]
                };
                $('#floodTable').bootstrapTable(options);
                $('#floodTable').bootstrapTable('refreshOptions', options);
                if (results == undefined) {
                    results = [];
                }
                $("#floodTable").bootstrapTable("load", results);

                $("#floodTable").colResizable({
                    liveDrag: true
                });
            },
            /**
             * 报警次数详情
             */
            showOperateDetail: function (unitName, unitId, priorityName) {
                var detailPriority = [priorityName]
                if (priorityName == -1 && page.data.param.priority.length > 0) {
                    detailPriority = page.data.param.priority;
                }
                layer.open({
                    type: 2,
                    title: '报警次数详情',
                    closeBtn: 1,
                    area: ['85%', '420px'],
                    shadeClose: false,
                    content: 'AlarmNumStattDtl.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        let obj = {
                            unitName: unitName,
                            unitId: unitId,
                            priorityName: detailPriority,
                            startTime: moment(page.data.param.startTime).format('YYYY-MM-DD HH:mm:ss'),
                            endTime: moment(page.data.param.endTime).format('YYYY-MM-DD HH:mm:ss'),
                            alarmFlagId: page.data.param.alarmFlagId
                        };
                        iframeWin.page.logic.setData(obj);
                    }
                });
            },
            /**
             * 加载 最频繁操作 数据
             */
            queryMostOperate: function () {
                $.ajax({
                    url: AlarmNumTop20SingleUrl,
                    async: true,
                    data: page.data.top20Param,
                    dataType: "text",
                    type: 'GET',
                    success: function (result) {
                        var res = $.ET.toObjectArr(result);
                        if (res != null && res != undefined && res != '') {
                            page.logic.initMostAlarmOperateCharts(res);
                            page.logic.initMostAlarmOperateTable(res);
                        }
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            /**
             * 加载 报警数 数据
             */
            queryAlarmNum: function () {
                if (!unitId) {
                    return
                }
                $("#unit").html("（" + unit + "）");
                $.ajax({
                    url: alarmNumberUrl,
                    async: true,
                    data: {
                        "priority": page.data.param.priority,
                        "startTime": moment(page.data.param.startTime).format('YYYY-MM-DD HH:mm:ss'),
                        "endTime": moment(page.data.param.endTime).format('YYYY-MM-DD HH:mm:ss'),
                        "unitId": [unitId],
                        "alarmFlagId": page.data.param.alarmFlagId
                    },
                    type: 'GET',
                    success: function (result) {
                        if (result != null && result != undefined && result != '') {
                            page.logic.initOperateNumberChart(result);
                            page.logic.initAlarmAndOperateTable(result);
                        }
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            /**
             * 加载 最频繁操作 数据
             */
            initMostAlarmOperateCharts: function (data) {
                var res = data;
                if (typeof (res) == "string")
                    res = JSON.parse(data);
                if (mostAlarmOperateCharts && !mostAlarmOperateCharts.isDisposed()) {
                    mostAlarmOperateCharts.clear();
                    mostAlarmOperateCharts.dispose();
                }
                if (res == undefined || res == null || res.length == 0) {
                    mostAlarmOperateCharts = OPAL.ui.chart.initEmptyChart('shock-alarm-chart2');
                    return;
                }
                mostAlarmOperateCharts = echarts.init(document.getElementById('shock-alarm-chart2'));
                option = {
                    color: ['#00acac'],
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            var dataIndex = params[0].dataIndex;
                            var tips = "位号：" + res[dataIndex].tag + "<br/>" + "报警等级：" + res[dataIndex].alarmFlag + "<br/>" + "报警数：" + res[dataIndex].alarmCount;
                            return tips;
                        }
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        top: '7%',
                        height: '260px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        axisTick: {
                            alignWithLabel: true
                        },
                        axisPointer: {
                            show: true,
                            label: {
                                show: true,
                                formatter: function (param) {
                                    var dataIndex = param.seriesData[0].dataIndex;
                                    var tips = "位号：" + res[dataIndex].tag + "  报警等级：" + res[dataIndex].alarmFlag;
                                    return tips;
                                }
                            }
                        },
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 0,
                            rotate: 15,
                            textStyle: {
                                fontSize: 10
                            }
                        }
                    }],
                    yAxis: [{
                        type: 'value',
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid',
                                fontSize: 10
                            }
                        },
                    }],
                    series: [{
                        type: 'bar',
                        barWidth: '15',
                        data: []
                    }]
                };
                mostAlarmOperateCharts.setOption(option);
                var totalArray = new Array();
                var tagArray = new Array();
                for (var i = 0; i < res.length; i++) {
                    totalArray.push(res[i]["alarmCount"]);
                    tagArray.push(res[i]['tag']);
                }
                option.xAxis[0].data = tagArray;
                option.series[0].data = totalArray;
                mostAlarmOperateCharts.setOption(option);

            },
            //初始化最频繁报警列表
            initOpetateTable: function (data) {
                var options = {
                    // detailView: true, //父子表
                    striped: true, //是否显示行间隔色
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "client",
                    sortOrder: "asc", //排序方式
                    pageNumber: 1, //初始化加载第一页，默认第一页
                    height: 300,
                    columns: [{
                        title: "序号", formatter: function (value, row, index) {
                            return index + 1;
                        }, rowspan: 1, align: 'center', width: '80px'
                    }, {
                        field: 'unitName', title: '装置', align: 'left',
                    }, {
                        field: 'prdtCellName', title: '生产单元', align: 'left',
                    }, {
                        field: 'location', title: '位置描述', width: '150px', align: 'left'
                    }, {
                        field: 'tag', title: '位号', align: 'left',
                    }, {
                        field: 'alarmFlag', title: '报警等级', align: 'left',
                    }, {
                        field: 'alarmCount', title: '报警数', align: 'right',
                    }, {
                        field: 'priorityName', title: '优先级', align: 'center',
                    }, {
                        field: 'monitorTypeName', title: '专业', align: 'right',
                    }]
                };
                $('#MostAlarmOperateTable').bootstrapTable(options);
                $('#MostAlarmOperateTable').bootstrapTable('refreshOptions', options);
                if (data['mainTableEntityList'] == undefined) {
                    data['mainTableEntityList'] = [];
                }
                $("#MostAlarmOperateTable").bootstrapTable("load", data['mainTableEntityList']);

                $("#MostAlarmOperateTable").colResizable({
                    liveDrag: true
                });
            },
            // 刷新top20报警数列表
            initMostAlarmOperateTable: function (data) {
                var res = data;
                if (typeof (res) == "string")
                    res = JSON.parse(data);
                $("#MostAlarmOperateTable").bootstrapTable("removeAll");
                if (res == null || res == undefined || res.length == 0) {
                    return;
                }
                $("#MostAlarmOperateTable").bootstrapTable("load", res);
                $("#MostAlarmOperateTable").bootstrapTable("refresh");
                //设置鼠标浮动提示
                var tds = $('#MostAlarmOperateTable').find('tbody tr td');
                $.each(tds, function (i, el) {
                    $(this).attr("title", $(this).text())
                })
            },
            /**
             * 初始化操作/报警趋势图
             */
            initOperateNumberChart: function (data) {
                if (operNumChart && !operNumChart.isDisposed()) {
                    operNumChart.clear();
                    operNumChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    operNumChart = OPAL.ui.chart.initEmptyChart('chartAlarmAndOperate');
                    return;
                }
                operNumChart = echarts.init(document.getElementById('chartAlarmAndOperate'));
                var option = {
                    tooltip: {
                        position: ['30%', '20%'],
                        trigger: 'item',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: function (param) {
                            return option.series[param['seriesIndex']]['tooltip'][param['dataIndex']];
                        }
                    },
                    legend: {
                        itemWidth: 10,
                        itemHeight: 10,
                        top: 1,
                        left: 50,
                        data: [{
                            name: '报警数',
                            icon: 'bar',
                            textStyle: {
                                color: '#333'
                            }
                        }]
                    },
                    grid: {
                        left: '2%',
                        right: '1%',
                        top: '9%',
                        height: '230px',
                        bottom: "70px",
                        containLabel: true
                    },
                    calculable: false,
                    xAxis: [{
                        type: 'category',
                        boundaryGap: false,
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            formatter: function (value, index) {
                                return moment(value).format("MM-DD");
                            },
                            interval: 0,
                            rotate: 35
                        },
                        data: []
                    }],
                    dataZoom: [{
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        // left: '9%',

                        bottom: -5,
                        start: 0,
                        end: 100 //初始化滚动条
                    }],
                    yAxis: [{
                        type: 'value',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                    }],
                    series: [
                        {
                            name: '报警数',
                            type: 'line',
                            itemStyle: {
                                label: {
                                    show: false
                                },
                                normal: {
                                    color: '#489ae5',
                                    areaStyle: {
                                        color: '#489ae5'
                                    }
                                }
                            },
                            markArea: {
                                itemStyle: {
                                    normal: {
                                        color: '#6b6d75',
                                        borderColor: '#6b6d75',
                                        opacity: 0.2
                                    }
                                },
                                silent: true,
                                data: [
                                    [{
                                        xAxis: ''
                                    }, {
                                        xAxis: ''
                                    }]
                                ]
                            },
                            data: data[0]['counts'],
                            tooltip: data[0]['tip']
                        }
                    ]
                };
                if (option.xAxis[0].data.length == 0 && data != undefined && data[0] != undefined && data[0].xaxis != undefined) {
                    var dateArr = data[0].xaxis;
                    var lastDate = dateArr[dateArr.length - 1];
                    var nextDate;
                    nextDate = OPAL.util.strToDate(lastDate).dateAdd('d', 1);
                    nextDate = OPAL.util.dateFormat(nextDate, 'yyyy-MM-dd');
                    //data[0].xaxis.push(nextDate);
                    option.xAxis[0].data = data[0].xaxis;
                }
                operNumChartOption = option;
                operNumChart.setOption(operNumChartOption);
            },
            /**
             * 初始化报警数和操作数表格
             * @param data
             */
            initAlarmAndOperateTable: function (data) {
                var option = {
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'timeStr',
                        title: '时间',
                        align: 'center',
                        formatter: function (value, row, index) {
                            return value;
                        }
                    }, {
                        field: 'count',
                        title: '报警数',
                        align: 'right'
                    }, {
                        field: 'monitorTypeStr',
                        title: '专业',
                        align: 'left',
                    }],
                    sidePagination: "client",
                    queryParamsType: "undefined",
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    sidePagination: "client",
                    cache: false,
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    height: 300,
                    striped: true
                };
                $('#tableAlarmAndOperate').bootstrapTable(option);
                $("#tableAlarmAndOperate").bootstrapTable("removeAll");
                if (data != undefined && data[0] != undefined && data[0].list != undefined) {
                    $("#tableAlarmAndOperate").bootstrapTable("load", data[0].list);
                } else {
                    $("#tableAlarmAndOperate").bootstrapTable("load", []);
                }
                $('#tableAlarmAndOperate').bootstrapTable('refreshOptions', option);
                $("#tableAlarmAndOperate").colResizable({
                    liveDrag: true
                });
            },
            /**
             * 设置日期插件
             */
            initTime: function () {
                var myDate = new Date();
                var start = laydate.render({
                    elem: '#startTime', //指定元素
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd', //日期格式
                    value: getStartTime,
                    max: getEndTime, //最大日期
                });
                var end = laydate.render({
                    elem: '#endTime',
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd',
                    value: getEndTime,
                    max: getEndTime,
                });
                $('#startTime').attr('maxDate', getEndTime)
                $('#endTime').attr('maxDate', getEndTime)
            },
            getShowTime: function () {
                $.ajax({
                    url: getShowTimeUrl,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function (result) {
                        var dataArr = $.ET.toObjectArr(result);
                        getStartTime = dataArr[0].value;
                        getEndTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
                        //设置时间插件
                        page.logic.initTime();
                        document.getElementById("btnSearch").click();
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化查询 报警标识
             */
            initAlarmFlagList: function () {
                OPAL.ui.getComboMultipleSelect('alarmFlagId', alarmFlagListUrl, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#alarmFlagId").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#alarmFlagId").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化专业
             */
            initMonitorType: function () {
                OPAL.ui.getComboMultipleSelect('monitorType', monitorTypeUrl, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#monitorType").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#monitorType").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化优先级
             */
            initPriority: function () {
                OPAL.ui.getComboMultipleSelect('priority', priorityUrl, {
                    data: {
                        'isAll': true
                    }
                }, true, function () {
                    var treeView = $("#priority").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#priority").combotree("checkAllNodes");
                });
            },
        },
    }
    page.init();
    window.page = page;
});