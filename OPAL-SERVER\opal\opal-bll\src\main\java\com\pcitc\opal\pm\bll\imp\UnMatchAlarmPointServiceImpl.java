package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.UnMatchAlarmPointService;
import com.pcitc.opal.pm.dao.UnMatchAlarmPointRepository;
import com.pcitc.opal.pm.dao.imp.UnMatchAlarmPointEntityVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @USER: chenbo
 * @DATE: 2023/4/24
 * @TIME: 9:22
 * @DESC:
 **/
@Service
@Slf4j
public class UnMatchAlarmPointServiceImpl implements UnMatchAlarmPointService {

    @Autowired
    private UnMatchAlarmPointRepository unMatchAlarmPointRepository;

    @Resource
    BasicDataService basicDataService;


    @Override
    public PaginationBean<UnMatchAlarmPointEntityVO> getUnMatchAlarmPoint(String tag, Long prdtCellId, Long dcsCode, String[] unitIds, Pagination page) {

        List<String> unitList = null;
        try {
            unitList = basicDataService.getUnitListByIds(unitIds, true).stream().map(UnitEntity::getStdCode).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("未匹配报警点获取装置权限异常-{}", e.getMessage());
        }

        PaginationBean<UnMatchAlarmPointEntityVO> unMatchAlarmPointList = unMatchAlarmPointRepository.getUnMatchAlarmPoint(tag, prdtCellId, dcsCode, unitList, page);
        return unMatchAlarmPointList;
    }
}
