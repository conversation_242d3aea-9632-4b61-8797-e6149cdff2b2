var searchUrl = OPAL.API.acUrl + '/optimizationEffectAssess/getOptimizationEffectData';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var currentTimeUrl = OPAL.API.commUrl + "/getSysDateTime";
var getQueryTimeUrl = OPAL.API.commUrl + '/getQueryTime';
var getAlarmIsoUrl = OPAL.API.commUrl + '/getAlarmIso';
var queryTimeArray;
var standard, over, nowVal, ele, num, standardFlag, overFlag; //标准达标值 标准超标值 当前值 当前元素 标准值个数 标准达标值比较符号 标准超标值比较符号
var nowDate;
var afterMaxDate, beforeMaxDate; // 优化后时间插件最大值 优化前时间插件最大值
var alarmDayChart, alarmAvgChart, alarmMaxValueChart, alarmDisturbanceChart, alarmFloodChart, alarmShockChart, alarmDayContinuousChart;
var isLoading = true;
$(function() {
	var page = {
		/**
		 * 初始化
		 */
		init: function() {
			this.bindUi();
			//扩展日期插件
			OPAL.util.extendDate();
			//获取查询时间 10:00
			page.logic.getQueryTime();
			//初始化查询装置树
			page.logic.initUnitTree();
			//获取国际标准
			page.logic.getAlarmIso();
			//初始化图形
			page.logic.initChart();

            //装置赋值
            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("OptimizationEffectAssess");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitId').combotree('setValue', cookieValue[0]);
                }
            }
		},
		bindUi: function() {
			//查询
			$('#search').click(function() {
                isLoading=false;
				page.logic.search();
			})
			$('.myTab li').click(function() {
				var flag = $(this).attr('showFlag');
				if (flag == 'tableShow') {
					$(this).find('img').attr('src', '../../../images/one1.png');
					$($(this).siblings()[0]).find('img').attr('src', '../../../images/tweo.png');
					$($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
				} else if (flag == 'imgShow') {
					$(this).find('img').attr('src', '../../../images/tweos.png');
					$($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
					$($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
				}
			})
			$('.banner-content').flexslider({
				animation: "slide",
				direction: "horizontal",
				easing: "swing",
				animationLoop: true, // Boolean: 是否循环播放
				itemWidth: 1800, // Integer: slide 宽度，多个同时滚动时设置
				move: 4,
				slideshow: false,
				slideshowSpeed: 1000,
				animationSpeed: 1000,
				pauseOnHover: true,
				directionNav: true,
				keyboard: true,
				bounce: true,
			});
			$(".opt-chrome-compatible").hide();
		},
		data: {
			// 设置查询参数
			param: {}
		},
		logic: {
			//获取国际标准
			getAlarmIso: function() {
				$.ajax({
					data: '',
					url: getAlarmIsoUrl,
					async: false,
					dataType: "JSON",
					contentType: "X-WWW-FORM-URLENCODED",
					type: 'GET',
					success: function(data) {
						var data = $.ET.toObjectArr(data);
						page.logic.initAlarmIso(data);
					},
					error: function(result) {
						var errorResult = $.parseJSON(result.responseText);
						layer.msg(errorResult.collection.error.message);
					}
				})
			},
			//初始化国际标准
			initAlarmIso: function(data) {
				for (var i = 0; i < data.length; i++) {
					data[i]['value'] = data[i]['value'].replace(',', '<')
					$("#" + data[i]['key']).html(data[i]['value']);
				}
				$("#alarm_iso_emergency_bar").css('width', data[0]['value']);
				$("#alarm_iso_importance_bar").css('width', data[1]['value']);
				$("#alarm_iso_normal_bar").css('width', data[2]['value']);
			},
			initTable: function(data) {
				var optBefore = data['optBefore'];
				var optAfter = data['optAfter'];
				/**
				 * 管理等级评估
				 */
				var beforeAssessLevel = optBefore['assessLevel'];
				var afterAssessLevel = optAfter['assessLevel'];
				var imgSrc, alt;

				function getAssessLevelImgSrc(n, id) {
					switch (n) {
						case 1:
							imgSrc = '../../../images/time.png';
							alt = '操作人员无法准确的辨认和处理报警信息';
							break;
						case 2:
							imgSrc = '../../../images/time1.png';
							alt = '已报警信息得不到有效的控制';
							break;
						case 3:
							imgSrc = '../../../images/time2.png';
							alt = '报警信息得到一定的控制，报警峰值和平均值都趋于稳定';
							break;
						case 4:
							imgSrc = '../../../images/time3.png';
							alt = '干扰信息不再出现，报警信息被准确的捕捉';
							break;
						case 5:
							imgSrc = '../../../images/time4.png';
							alt = '操作人员可以提前发现报警并有足够的时间处理报警';
							break;
						default:
							imgSrc = '../../../images/time4.png';
							alt = '操作人员可以提前发现报警并有足够的时间处理报警';
					}
					$("#" + id).attr('src', imgSrc);
					$("#" + id).attr('title', alt);
				}
				getAssessLevelImgSrc(beforeAssessLevel, 'beforeAssessLevel');
				getAssessLevelImgSrc(afterAssessLevel, 'afterAssessLevel');

				if (optBefore['avgAlarmCount'] != null) {
					$("#beforeAvgAlarmCount").html(optBefore['avgAlarmCount']);
				}
				if (optAfter['avgAlarmCount'] != null) {
					$("#afterAvgAlarmCount").html(optAfter['avgAlarmCount']);
				}
				if (optBefore['maxAlarmCount'] != null) {
					$("#beforeMaxAlarmCount").html(optBefore['maxAlarmCount']);
				}
				if (optAfter['maxAlarmCount'] != null) {
					$("#afterMaxAlarmCount").html(optAfter['maxAlarmCount']);
				}
				if (optBefore['disturbanceAlarm'] != null) {
					$("#beforeDisturbanceAlarm").html(optBefore['disturbanceAlarm']);
				}
				if (optAfter['disturbanceAlarm'] != null) {
					$("#afterDisturbanceAlarm").html(optAfter['disturbanceAlarm']);
				}
				var n = $("#managementLevel").find('div').eq(0).find('span').length;
				for (var i = 0; i < n; i++) {
					var before = parseFloat($("#managementLevel").find('div').eq(0).find('span').eq(i).html());
					var after = parseFloat($("#managementLevel").find('div').eq(6).find('span').eq(i).html());
					var img = $(".opt-chrome-compatible").eq(i);
					if (after > before) {
						img.attr('src', '../../../images/uprat.png');
					} else if (after == before) {
						img.attr('src', '../../../images/rope.png');
					} else {
						img.attr('src', '../../../images/down.png');
					}
				}

				/**
				 * 优先级评估
				 */

				if (optBefore['systemPriority'][0]['value'] != null) {
					var n = optBefore['systemPriority'][0]['value'];
					$("#alarm_before_emergency").html(n);
					$("#alarm_before_emergency_bar").css('width', n);
				}
				if (optBefore['systemPriority'][1]['value'] != null) {
					var n = optBefore['systemPriority'][1]['value'];
					$("#alarm_before_importance").html(n);
					$("#alarm_before_importance_bar").css('width', n);
				}
				if (optBefore['systemPriority'][2]['value'] != null) {
					var n = optBefore['systemPriority'][2]['value'];
					$("#alarm_before_normal").html(n);
					$("#alarm_before_normal_bar").css('width', n);
				}
				if (optAfter['systemPriority'][0]['value'] != null) {
					var n = optAfter['systemPriority'][0]['value'];
					$("#alarm_after_emergency").html(n);
					$("#alarm_after_emergency_bar").css('width', n);
				}
				if (optAfter['systemPriority'][1]['value'] != null) {
					var n = optAfter['systemPriority'][1]['value'];
					$("#alarm_after_importance").html(n);
					$("#alarm_after_importance_bar").css('width', n);
				}
				if (optAfter['systemPriority'][2]['value'] != null) {
					var n = optAfter['systemPriority'][2]['value'];
					$("#alarm_after_normal").html(n);
					$("#alarm_after_normal_bar").css('width', n);
				}

				/**
				 * 重要指标对比
				 */

				if (optBefore['dayAlarmCount'] != null) {
					standard = $("#alarm_day_iso_best").html();
					over = $("#alarm_day_iso_over").html();
					nowVal = optBefore['dayAlarmCount'];
					ele = $("#dayAlarmCountBefore");
					page.logic.setTableValue(standard, over, nowVal, ele, 3, null, null);
				}
				if (optAfter['dayAlarmCount'] != null) {
					standard = $("#alarm_day_iso_best").html();
					over = $("#alarm_day_iso_over").html();
					nowVal = optAfter['dayAlarmCount'];
					ele = $("#dayAlarmCountAfter");
					page.logic.setTableValue(standard, over, nowVal, ele, 3, null, null);
				}
				if (optBefore['avgAlarmCount'] != null) {
					standard = $('#alarm_avg_iso_best').html();
					over = $('#alarm_avg_iso_over').html();
					nowVal = optBefore['avgAlarmCount'];
					ele = $("#avgAlarmCountBefore");
					page.logic.setTableValue(standard, over, nowVal, ele, 3, null, null);
				}
				if (optAfter['avgAlarmCount'] != null) {
					standard = $('#alarm_avg_iso_best').html();
					over = $('#alarm_avg_iso_over').html();
					nowVal = optAfter['avgAlarmCount'];
					ele = $("#avgAlarmCountAfter");
					page.logic.setTableValue(standard, over, nowVal, ele, 3, null, null);
				}
				if (optBefore['maxAlarmCount'] != null) {
					standard = $("#alarm_max_value_iso_standard").html();
					over = $("#alarm_max_value_iso_over").html();
					nowVal = optBefore['maxAlarmCount'];
					ele = $("#maxAlarmCountBefore");
					page.logic.setTableValue(standard, over, nowVal, ele, 2, '<=', '>');
				}
				if (optAfter['maxAlarmCount'] != null) {
					standard = $("#alarm_max_value_iso_standard").html();
					over = $("#alarm_max_value_iso_over").html();
					nowVal = optAfter['maxAlarmCount'];
					ele = $("#maxAlarmCountAfter");
					page.logic.setTableValue(standard, over, nowVal, ele, 2, '<=', '>');
				}
				if (optBefore['disturbanceAlarm'] != null) {
					standard = $("#alarm_disturbance_iso_standard").html();
					over = $("#alarm_disturbance_iso_over").html();
					nowVal = optBefore['disturbanceAlarm'];
					ele = $("#disturbanceAlarmBefore");
					page.logic.setTableValue(standard, over, nowVal, ele, 2, '<=', '>');
				}
				if (optAfter['disturbanceAlarm'] != null) {
					standard = $("#alarm_disturbance_iso_standard").html();
					over = $("#alarm_disturbance_iso_over").html();
					nowVal = optAfter['disturbanceAlarm'];
					ele = $("#disturbanceAlarmAfter");
					page.logic.setTableValue(standard, over, nowVal, ele, 2, '<=', '>');
				}
				if (optBefore['floodAlarm'] != null) {
					standard = $("#alarm_flood_iso_standard").html();
					over = $("#alarm_flood_iso_over").html();
					nowVal = optBefore['floodAlarm'];
					ele = $("#floodAlarmBefore");
					page.logic.setTableValue(standard, over, nowVal, ele, 2, '<', '>=');
				}
				if (optAfter['floodAlarm'] != null) {
					standard = $("#alarm_flood_iso_standard").html();
					over = $("#alarm_flood_iso_over").html();
					nowVal = optAfter['floodAlarm'];
					ele = $("#floodAlarmAfter");
					page.logic.setTableValue(standard, over, nowVal, ele, 2, '<', '>=');
				}
				if (optBefore['shockAlarmCount'] != null) {
					standard = $("#alarm_shock_iso_standard").html();
					over = $("#alarm_shock_iso_over").html();
					nowVal = optBefore['shockAlarmCount'];
					ele = $("#shockAlarmCountBefore");
					page.logic.setTableValue(standard, over, nowVal, ele, 2, '==', '>');
				}
				if (optAfter['shockAlarmCount'] != null) {
					standard = $("#alarm_shock_iso_standard").html();
					over = $("#alarm_shock_iso_over").html();
					nowVal = optAfter['shockAlarmCount'];
					ele = $("#shockAlarmCountAfter");
					page.logic.setTableValue(standard, over, nowVal, ele, 2, '==', '>');
				}
				if (optBefore['dayContinuousAlarmCount'] != null) {
					standard = $("#alarm_day_continuous_standard").html();
					over = $("#alarm_day_continuous_over").html();
					nowVal = optBefore['dayContinuousAlarmCount'];
					ele = $("#dayContinuousAlarmCountBefore");
					page.logic.setTableValue(standard, over, nowVal, ele, 2, '<', '>=');
				}
				if (optAfter['dayContinuousAlarmCount'] != null) {
					standard = $("#alarm_day_continuous_standard").html();
					over = $("#alarm_day_continuous_over").html();
					nowVal = optAfter['dayContinuousAlarmCount'];
					ele = $("#dayContinuousAlarmCountAfter");
					page.logic.setTableValue(standard, over, nowVal, ele, 2, '<', '>=');
				}

				//变化趋势
				for (var i = 0; i < $("#table tbody tr").length; i++) {
					var after = parseFloat($('#table').find('tbody').find('tr').eq(i).find('td').eq(4).find('.opt-bg-dis1').find('span').eq(1).html());
					var before = parseFloat($('#table').find('tbody').find('tr').eq(i).find('td').eq(3).find('.opt-bg-dis1').find('span').eq(1).html());
					var img = $('#table tbody').find('tr').eq(i).find('td').eq(5).find('img');
					if (after > before) {
						img.attr('src', '../../../images/dowep1.png');
					} else if (after == before) {
						img.attr('src', '../../../images/lipot.png');
					} else {
						img.attr('src', '../../../images/upload1.png');
					}
				}
			},
			/**
			 * 重要指标对比表格优化前,优化后赋值
			 */
			setTableValue: function(s, o, v, e, n, sFlag, oFlag) { //标准达标值 标准超标值 当前值 当前元素 标准值个数 标准达标值比较符号 标准超标值比较符号
				e.html(v);
				if (n == 3) {
					if (parseFloat(v) < parseFloat(s)) {
						e.prev().html('最佳的');
						e.parent().prev().attr('src', '../../../images/bsmile.png');
					} else if (parseFloat(v) >= parseFloat(o)) {
						e.prev().html('超标');
						e.parent().prev().attr('src', '../../../images/bsmile2.png');
					} else if (parseFloat(v) < parseFloat(o) && parseFloat(v) >= parseFloat(s)) {
						e.prev().html('达标');
						e.parent().prev().attr('src', '../../../images/bsmile1.png');
					}
				} else if (n == 2) {
					if (sFlag == '<=') {
						if (parseFloat(v) <= parseFloat(s)) {
							e.prev().html('达标');
							e.parent().prev().attr('src', '../../../images/bsmile1.png');
						} else if (parseFloat(v) > parseFloat(o)) {
							e.prev().html('超标');
							e.parent().prev().attr('src', '../../../images/bsmile2.png');
						}
					} else if (sFlag == '<') {
						if (parseFloat(v) < parseFloat(s)) {
							e.prev().html('达标');
							e.parent().prev().attr('src', '../../../images/bsmile1.png');
						} else if (parseFloat(v) >= parseFloat(o)) {
							e.prev().html('超标');
							e.parent().prev().attr('src', '../../../images/bsmile2.png');
						}
					} else if (sFlag == '==') {
						if (parseFloat(v) == parseFloat(s)) {
							e.prev().html('达标');
							e.parent().prev().attr('src', '../../../images/bsmile1.png');
						} else if (parseFloat(v) > parseFloat(o)) {
							e.prev().html('超标');
							e.parent().prev().attr('src', '../../../images/bsmile2.png');
						}
					}
				}
				standard = over = nowVal = ele = num = standardFlag = overFlag = undefined;
			},
			/**
			 * 获得固定的时间点
			 */
			getQueryTime: function() {
				OPAL.util.getQueryTime(function(queryTime) {
					queryTimeArray = queryTime.split(':');
					page.logic.initTime();
				});
			},
			/**
			 * 设置日期插件
			 */
			initTime: function() {

				$.ajax({
					url: currentTimeUrl,
					async: false,
					dataType: "JSON",
					contentType: "X-WWW-FORM-URLENCODED",
					type: 'GET',
					success: function(result) {
						var dataArr = $.ET.toObjectArr(result);
						nowDate = dataArr[0]['value'];
					},
					error: function(result) {
						var errorResult = $.parseJSON(result.responseText);
						layer.msg(errorResult.collection.error.message);
					}
				})
				var nowHour = parseInt(nowDate.split(' ')[1].split(':')[0]); //当前系统时间的小时
				var queryHour = parseInt(queryTimeArray[0]); //查询时间的小时
				if (nowHour >= queryHour) {
					afterMaxDate = OPAL.util.strToDate(nowDate.split(' ')[0]).dateAdd('d', -1);
				} else {
					afterMaxDate = OPAL.util.strToDate(nowDate.split(' ')[0]).dateAdd('d', -2);
				}
				beforeMaxDate = afterMaxDate.dateAdd('d', -7);
				afterMaxDate = OPAL.util.dateFormat(afterMaxDate, 'yyyy-MM-dd');
				beforeMaxDate = OPAL.util.dateFormat(beforeMaxDate, 'yyyy-MM-dd');
				before = laydate.render({
					elem: '#optBeforeDateTime', //指定元素
					type: 'date',
					trigger: 'click',
					btns: ['clear', 'confirm'],
					format: 'yyyy-MM-dd', //日期格式
					max: beforeMaxDate

				});
				after = laydate.render({
					elem: '#optAfterDateTime', //指定元素
					type: 'date',
					trigger: 'click',
					btns: ['clear', 'confirm'],
					format: 'yyyy-MM-dd', //日期格式
					max: afterMaxDate

				});
			},
			/**
			 * 初始化装置树
			 */
			initUnitTree: function() {
				OPAL.ui.getEasyUIComboTreeSelect("unitId", commonUnitTreeUrl, "id", "parentId", "sname", {
					multiple: false,
					onlyLeafCheck: true
				}, false, function() {});
			},
			/**
			 * 搜索
			 */
			search: function() {
				var unitId = $("#unitId").val();
				if (unitId == '') {
					layer.msg('请选择装置！');
					return;
				}
				var optBeforeDateTime = $('#optBeforeDateTime').val();
				var optAfterDateTime = $('#optAfterDateTime').val();
				var flag = page.logic.checkTime(optBeforeDateTime, optAfterDateTime);
				if (!flag) {
					return false;
				}
				$("#search").attr('disabled', true);
				$.ajax({
					data: $("#formSearch").serialize(),
					url: searchUrl,
					async: true,
					dataType: "JSON",
					contentType: "X-WWW-FORM-URLENCODED",
					type: 'GET',
					success: function(data) {
						page.logic.initTable(data);
						page.logic.initChart(data);
						$("#search").attr('disabled', false);
						$(".opt-chrome-compatible").show();
					},
					error: function(result) {
						var errorResult = $.parseJSON(result.responseText);
						layer.msg(errorResult.collection.error.message);
						$("#search").prop('disabled', false);
					}
				})
			},
			/**
			 * 时间校验
			 */
			checkTime: function(optBeforeDateTime, optAfterDateTime) {
				var flag = true;
				var timeDifference = (OPAL.util.strToDate(optAfterDateTime).getTime() - OPAL.util.strToDate(optBeforeDateTime).getTime()) / (1000 * 60 * 60 * 24)
				if (OPAL.util.strToDate(optAfterDateTime) > OPAL.util.strToDate(afterMaxDate)) {
					layer.msg('优化后日期须选择不超过' + afterMaxDate + '的日期！')
					flag = false;
				}
				if (timeDifference < 7) {
					layer.msg('优化前与优化后的日期需间隔大于等于7天！')
					flag = false;
				}
				if (OPAL.util.strToDate(optBeforeDateTime) > OPAL.util.strToDate(optAfterDateTime)) {
					layer.msg('优化前日期不能大于优化后日期！')
					flag = false;
				}
				if (optBeforeDateTime == '' || optAfterDateTime == '') {
					layer.msg('优化前日期和优化后日期不能为空！')
					flag = false;
				}
				return flag;
				//请选择昨天
			},
			/**
			 * 初始化chart
			 */
			initChart: function(data) {
				page.logic.initAlarmDayChart(data);
				page.logic.initAlarmAvgChart(data);
				page.logic.initAlarmMaxValueChart(data);
				page.logic.initAlarmDisturbanceChart(data);
				page.logic.initAlarmFloodChart(data);
				page.logic.initAlarmShockChart(data);
				page.logic.initAlarmDayContinuousChart(data);
			},
			/**
			 * 设置chartOption
			 */
			setChartOption: function(data, key, option) {
				var dataArr = new Array();
				dataArr.push('');
				dataArr.push(data['optBefore'][key]);
				dataArr.push(data['optAfter'][key]);

				var beforeCoordArr = new Array();
				beforeCoordArr.push('1');
				beforeCoordArr.push(data['optBefore'][key]);

				var afterCoordArr = new Array();
				afterCoordArr.push('2');
				afterCoordArr.push(data['optAfter'][key]);

				option.series[0]['data'] = dataArr;
				var beforeValue = parseFloat(data['optBefore'][key]); //优化前值
				var afterValue = parseFloat(data['optAfter'][key]); //优化后值
				if (option.series[0]['markLine']['data'].length == 5) { //y轴3段
					option.series[0]['markLine']['data'][3][1]['coord'] = beforeCoordArr;
					option.series[0]['markLine']['data'][4][1]['coord'] = afterCoordArr;
					option.series[0]['markLine']['data'][3][1]['name'] = '优化前：' + data['optBefore'][key] + '\n';
					option.series[0]['markLine']['data'][4][1]['name'] = '优化后：' + data['optAfter'][key] + '\n';
					//y轴设置最大值
					var max = parseFloat(data['optAfter'][key]) > parseFloat(data['optBefore'][key]) ? parseFloat(data['optAfter'][key]) : parseFloat(data['optBefore'][key]);
					if (max > option['series'][0]['markLine']['data'][2][1]['coord'][1]) {
						var arr = new Array();
						arr.push('0');
						arr.push(max);
						option['series'][0]['markLine']['data'][2][1]['coord'] = arr;
						option['yAxis'][0]['max'] = max;
					}
				} else if (option.series[0]['markLine']['data'].length == 4) { //y轴2段
					option.series[0]['markLine']['data'][2][1]['coord'] = beforeCoordArr;
					option.series[0]['markLine']['data'][3][1]['coord'] = afterCoordArr;
					option.series[0]['markLine']['data'][2][1]['name'] = '优化前：' + data['optBefore'][key] + '\n';;
					option.series[0]['markLine']['data'][3][1]['name'] = '优化后：' + data['optAfter'][key] + '\n';;
					//y轴设置最大值
					var max = parseFloat(data['optAfter'][key]) > parseFloat(data['optBefore'][key]) ? parseFloat(data['optAfter'][key]) : parseFloat(data['optBefore'][key]);
					if (max > option['series'][0]['markLine']['data'][1][1]['coord'][1]) {
						var arr = new Array();
						arr.push('0');
						arr.push(max);
						option['series'][0]['markLine']['data'][1][1]['coord'] = arr;
						option['yAxis'][0]['max'] = max;
					}

				} else if (option.series[0]['markLine']['data'].length == 3) { //y轴1段
					option.series[0]['markLine']['data'][1][1]['coord'] = beforeCoordArr;
					option.series[0]['markLine']['data'][2][1]['coord'] = afterCoordArr;
					option.series[0]['markLine']['data'][1][1]['name'] = '优化前：' + data['optBefore'][key] + '\n';;
					option.series[0]['markLine']['data'][2][1]['name'] = '优化后：' + data['optAfter'][key] + '\n';;
					//y轴设置最大值
					var max = parseFloat(data['optAfter'][key]) > parseFloat(data['optBefore'][key]) ? parseFloat(data['optAfter'][key]) : parseFloat(data['optBefore'][key]);
					if (max > option['series'][0]['markLine']['data'][0][1]['coord'][1]) {
						var arr = new Array();
						arr.push('0');
						arr.push(max);
						option['series'][0]['markLine']['data'][0][1]['coord'] = arr;
						option['yAxis'][0]['max'] = max;
					}
				}
			},
			/**
			 * 设置优化前优化后markline颜色
			 * @param {[type]} option 
			 * @param {[type]} data    元数据
			 * @param {[type]} key    
			 * @param {[type]} flag    判断符号  < <= 
			 */
			setMarkLintColor: function(option, data, key, flag) {
				var beforeVal = parseFloat(data['optBefore'][key]);
				var afterVal = parseFloat(data['optAfter'][key]);
				var split1 = option.series[0]['markLine']['data'][0][1]['coord'][1];
				var split2 = option.series[0]['markLine']['data'][1][1]['coord'][1]
				if (flag == null) {
					if (beforeVal < split1) {
						option.series[0]['markLine']['data'][3][0]['lineStyle']['normal']['color'] = '#5dc6f6';
					} else if (beforeVal >= split1 && beforeVal < split2) {
						option.series[0]['markLine']['data'][3][0]['lineStyle']['normal']['color'] = '#6dcf6f';
					} else if (beforeVal > split2) {
						option.series[0]['markLine']['data'][3][0]['lineStyle']['normal']['color'] = '#ff5050';
					}
					if (afterVal < split1) {
						option.series[0]['markLine']['data'][4][0]['lineStyle']['normal']['color'] = '#5dc6f6';
					} else if (afterVal >= split1 && afterVal < split2) {
						option.series[0]['markLine']['data'][4][0]['lineStyle']['normal']['color'] = '#6dcf6f';
					} else if (afterVal >= split2) {
						option.series[0]['markLine']['data'][4][0]['lineStyle']['normal']['color'] = '#ff5050';
					}
				} else if (flag == "<=") {
					if (beforeVal <= split1) {
						option.series[0]['markLine']['data'][2][0]['lineStyle']['normal']['color'] = '#6dcf6f';
					} else if (beforeVal > split1) {
						option.series[0]['markLine']['data'][2][0]['lineStyle']['normal']['color'] = '#ff5050';
					}
					if (afterVal <= split1) {
						option.series[0]['markLine']['data'][3][0]['lineStyle']['normal']['color'] = '#6dcf6f';
					} else if (afterVal > split1) {
						option.series[0]['markLine']['data'][3][0]['lineStyle']['normal']['color'] = '#ff5050';
					}
				} else if (flag == "<") {
					if (beforeVal < split1) {
						option.series[0]['markLine']['data'][2][0]['lineStyle']['normal']['color'] = '#6dcf6f';
					} else if (beforeVal >= split1) {
						option.series[0]['markLine']['data'][2][0]['lineStyle']['normal']['color'] = '#ff5050';
					}
					if (afterVal < split1) {
						option.series[0]['markLine']['data'][3][0]['lineStyle']['normal']['color'] = '#6dcf6f';
					} else if (afterVal >= split1) {
						option.series[0]['markLine']['data'][3][0]['lineStyle']['normal']['color'] = '#ff5050';
					}
				} else if (flag == "==") {
					if (beforeVal == 0) {
						option.series[0]['markLine']['data'][1][0]['lineStyle']['normal']['color'] = '#6dcf6f';
					} else if (beforeVal > 0) {
						option.series[0]['markLine']['data'][1][0]['lineStyle']['normal']['color'] = '#ff5050';
					}
					if (afterVal == 0) {
						option.series[0]['markLine']['data'][2][0]['lineStyle']['normal']['color'] = '#6dcf6f';
					} else if (afterVal > 0) {
						option.series[0]['markLine']['data'][2][0]['lineStyle']['normal']['color'] = '#ff5050';
					}
				}

			},
			/*
			 *每天的报警数(个)
			 */
			initAlarmDayChart: function(data) {
				if (alarmDayChart && !alarmDayChart.isDisposed()) {
					alarmDayChart.clear();
					alarmDayChart.dispose();
				}
				if (data == null || data == undefined || data.length == 0) {
					alarmDayChart = OPAL.ui.chart.initEmptyChart('alarmDayChart');
					return;
				}

				alarmDayChart = echarts.init(document.getElementById('alarmDayChart'));
				var option1 = {
					title: {
						text: '每天的报警数(个)',
						left: '45%',
						bottom: '0',
						textStyle: { //标题内容的样式
							color: '#333',
							fontStyle: 'normal',
							fontWeight: "normal",
							fontFamily: "san-serif", //主题文字字体，默认微软雅黑
							fontSize: 12 //主题文字字体大小，
						},
					},
					grid: {
						left: '1%',
						right: '2%',
						top: '14%',
						height: '80%',
						containLabel: true
					},

					xAxis: [{
						show: false, //隐藏x轴
						type: 'category',
						data: [0, 1, 2, 3],
						boundaryGap: false,
						axisTick: {
							alignWithLabel: true
						},
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					yAxis: [{
						type: 'value',
						max: 450,
						splitNumber: 3,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					series: [{
						type: 'line',
						smooth: true,
						symbolSize: 8,
						itemStyle: {
							normal: {
								color: '#dbdbdb',
							}
						},
						markLine: {
							data: [
								[{
										symbol: 'none',
										coord: ['0', 0],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#5dc6f6'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 150],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#5dc6f6',
											}
										}
									},

								],
								[{
										symbol: 'none',
										coord: ['0', 150],
										lineStyle: {
											normal: {
												width: '2',
												color: '#6dcf6f',
												type: 'solid',

											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 300],
										lineStyle: {
											normal: {
												color: '#6dcf6f',
												width: '2',
												type: 'solid',
											}
										}
									},

								],
								[{
										symbol: 'none',
										coord: ['0', 300],
										lineStyle: {
											normal: {
												width: '2',
												color: '#ff5050',
												type: 'solid',
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 450],
										lineStyle: {
											normal: {
												width: '2',
												color: '#ff5050',
												type: 'solid',
											}
										}
									},

								],
								[{

										symbol: 'none',
										coord: ['1', 0],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									}, {
										name: '优化前',
										symbol: 'circle',
										coord: ['1', 450],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									},

								],
								[{

										symbol: 'none',
										color: '#333',
										coord: ['2', 0],
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									}, {
										name: '优化后',
										symbol: 'circle',
										coord: ['2', 135],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									},

								]
							]
						},

						areaStyle: {
							normal: {
								color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
									offset: 0,
									color: 'rgba(234,234,234,0.5)' // 0% 处的颜色
								}, {
									offset: 1,
									color: '#fff' // 100% 处的颜色
								}], false)
							}
						},
						data: ['', '300', '135'],
					}]
				};
				page.logic.setChartOption(data, 'dayAlarmCount', option1);
				page.logic.setMarkLintColor(option1, data, 'dayAlarmCount', null);
				alarmDayChart.setOption(option1);
			},

			/**
			 * 平均报警率（个）
			 */
			initAlarmAvgChart: function(data) {
				if (alarmAvgChart && !alarmAvgChart.isDisposed()) {
					alarmAvgChart.clear();
					alarmAvgChart.dispose();
				}
				if (data == null || data == undefined || data.length == 0) {
					alarmAvgChart = OPAL.ui.chart.initEmptyChart('alarmAvgChart');
					return;
				}
				alarmAvgChart = echarts.init(document.getElementById('alarmAvgChart'));
				var option2 = {
					title: {
						text: '平均报警率(个)',
						left: '45%',
						bottom: '0',
						textStyle: { //标题内容的样式
							color: '#333',
							fontStyle: 'normal',
							fontWeight: "normal",
							fontFamily: "san-serif", //主题文字字体，默认微软雅黑
							fontSize: 12 //主题文字字体大小，
						},
					},
					grid: {
						left: '1%',
						right: '2%',
						top: '14%',
						height: '80%',
						containLabel: true
					},

					xAxis: [{
						show: false, //隐藏x轴
						type: 'category',
						data: [0, 1, 2, 3],
						boundaryGap: false,
						axisTick: {
							alignWithLabel: true
						},
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					yAxis: [{
						type: 'value',
						splitNumber: 3,
						max: 3,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					series: [{
						type: 'line',
						smooth: true,
						symbolSize: 8,
						itemStyle: {
							normal: {
								color: '#dbdbdb',
							}
						},
						markLine: {
							data: [
								[{
										symbol: 'none',
										coord: ['0', 0],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#5dc6f6'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 1],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#5dc6f6',
											}
										}
									},

								],
								[{
										symbol: 'none',
										name: '',
										coord: ['0', 1],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#6dcf6f'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 2],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#6dcf6f'
											}
										}
									},

								],
								[{
										symbol: 'none',
										name: '',
										coord: ['0', 2],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#ff5050'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 3],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#ff5050'
											}
										}
									},

								],

								[{
										symbol: 'none',
										coord: ['1', 0],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									}, {
										name: '优化前',
										symbol: 'circle',
										coord: ['1', 3],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									},

								],
								[{
										symbol: 'none',
										color: '#333',
										coord: ['2', 0],
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									}, {
										name: '优化后',
										symbol: 'circle',
										coord: ['2', 1],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									},

								]
							]
						},

						areaStyle: {
							normal: {
								color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
									offset: 0,
									color: 'rgba(234,234,234,0.5)' // 0% 处的颜色
								}, {
									offset: 1,
									color: '#fff' // 100% 处的颜色
								}], false)
							}
						},
						data: ['', '3', '1'],
					}]
				};
				page.logic.setChartOption(data, 'avgAlarmCount', option2);
				page.logic.setMarkLintColor(option2, data, 'avgAlarmCount', null);
				alarmAvgChart.setOption(option2);
			},
			/**
			 * 峰值报警率（个）
			 */
			initAlarmMaxValueChart: function(data) {
				if (alarmMaxValueChart && !alarmMaxValueChart.isDisposed()) {
					alarmMaxValueChart.clear();
					alarmMaxValueChart.dispose();
				}
				if (data == null || data == undefined || data.length == 0) {
					alarmMaxValueChart = OPAL.ui.chart.initEmptyChart('alarmMaxValueChart');
					return;
				}
				alarmMaxValueChart = echarts.init(document.getElementById('alarmMaxValueChart'));
				var option3 = {
					title: {
						text: '峰值报警率(个)',
						left: '45%',
						bottom: '0',
						textStyle: { //标题内容的样式
							color: '#333',
							fontStyle: 'normal',
							fontWeight: "normal",
							fontFamily: "san-serif", //主题文字字体，默认微软雅黑
							fontSize: 12 //主题文字字体大小，
						},
					},
					grid: {
						left: '1%',
						right: '2%',
						top: '14%',
						height: '80%',
						containLabel: true
					},

					xAxis: [{
						show: false, //隐藏x轴
						type: 'category',
						data: [0, 1, 2, 3],
						boundaryGap: false,
						axisTick: {
							alignWithLabel: true
						},
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					yAxis: [{
						type: 'value',
						splitNumber: 2,
						max: 20,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					series: [{
						type: 'line',
						smooth: true,
						symbolSize: 8,
						itemStyle: {
							normal: {
								color: '#dbdbdb',
							}
						},
						markLine: {
							data: [

								[{
										symbol: 'none',
										name: '',
										coord: ['0', 0],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#6dcf6f'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 10],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#6dcf6f'
											}
										}
									},

								],
								[{
										symbol: 'none',
										name: '',
										coord: ['0', 10],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#ff5050'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 20],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#ff5050'
											}
										}
									},

								],

								[{
										symbol: 'none',
										coord: ['1', 0],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									}, {
										name: '优化前',
										symbol: 'circle',
										coord: ['1', 10],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									},

								],
								[{
										symbol: 'none',
										color: '#333',
										coord: ['2', 0],
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									}, {
										name: '优化后',
										symbol: 'circle',
										coord: ['2', 16],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									},

								]
							]
						},

						areaStyle: {
							normal: {
								color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
									offset: 0,
									color: 'rgba(234,234,234,0.5)' // 0% 处的颜色
								}, {
									offset: 1,
									color: '#fff' // 100% 处的颜色
								}], false)
							}
						},
						data: ['', '10', '16'],
					}]
				};
				page.logic.setChartOption(data, 'maxAlarmCount', option3);
				page.logic.setMarkLintColor(option3, data, 'maxAlarmCount', '<=');
				alarmMaxValueChart.setOption(option3);
			},
			/**
			 * 扰动率（%）
			 */
			initAlarmDisturbanceChart: function(data) {
				if (alarmDisturbanceChart && !alarmDisturbanceChart.isDisposed()) {
					alarmDisturbanceChart.clear();
					alarmDisturbanceChart.dispose();
				}
				if (data == null || data == undefined || data.length == 0) {
					alarmDisturbanceChart = OPAL.ui.chart.initEmptyChart('alarmDisturbanceChart');
					return;
				}
				alarmDisturbanceChart = echarts.init(document.getElementById('alarmDisturbanceChart'));
				var option4 = {
					title: {
						text: '扰动率(%)',
						left: '50%',
						bottom: '0',
						textStyle: { //标题内容的样式
							color: '#333',
							fontStyle: 'normal',
							fontWeight: "normal",
							fontFamily: "san-serif", //主题文字字体，默认微软雅黑
							fontSize: 12 //主题文字字体大小，
						},
					},
					grid: {
						left: '1%',
						right: '2%',
						top: '14%',
						height: '80%',
						containLabel: true
					},

					xAxis: [{
						show: false, //隐藏x轴
						type: 'category',
						data: [0, 1, 2, 3],
						boundaryGap: false,
						axisTick: {
							alignWithLabel: true
						},
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					yAxis: [{
						name: '(%)',
						type: 'value',
						splitNumber: 2,
						max: 4,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					series: [{
						type: 'line',
						smooth: true,
						symbolSize: 8,
						itemStyle: {
							normal: {
								color: '#dbdbdb',
							}
						},
						markLine: {
							data: [

								[{
										symbol: 'none',
										name: '',
										coord: ['0', 0],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#6dcf6f'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 2],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#6dcf6f'
											}
										}
									},

								],
								[{
										symbol: 'none',
										name: '',
										coord: ['0', 2],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#ff5050'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 4],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#ff5050'
											}
										}
									},

								],

								[{

										symbol: 'none',
										coord: ['1', 0],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									}, {
										name: '优化前',
										symbol: 'circle',
										coord: ['1', 1],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									},

								],
								[{
										symbol: 'none',
										color: '#333',
										coord: ['2', 0],
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									}, {
										name: '优化后',
										symbol: 'circle',
										coord: ['2', 1],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									},

								]
							]
						},
						areaStyle: {
							normal: {
								color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
									offset: 0,
									color: 'rgba(234,234,234,0.5)' // 0% 处的颜色
								}, {
									offset: 1,
									color: '#fff' // 100% 处的颜色
								}], false)
							}
						},
						data: ['', '1', '1'],
					}]
				};
				page.logic.setChartOption(data, 'disturbanceAlarm', option4);
				page.logic.setMarkLintColor(option4, data, 'disturbanceAlarm', '<=');
				alarmDisturbanceChart.setOption(option4);
			},
			/**
			 * 高频报警时间百分比（%）
			 */
			initAlarmFloodChart: function(data) {
				if (alarmFloodChart && !alarmFloodChart.isDisposed()) {
					alarmFloodChart.clear();
					alarmFloodChart.dispose();
				}
				if (data == null || data == undefined || data.length == 0) {
					alarmFloodChart = OPAL.ui.chart.initEmptyChart('alarmFloodChart');
					return;
				}
				alarmFloodChart = echarts.init(document.getElementById('alarmFloodChart'));
				var option5 = {
					title: {
						text: '高频报警报警时间百分比(%)',
						left: '35%',
						bottom: '0',
						textStyle: { //标题内容的样式
							color: '#333',
							fontStyle: 'normal',
							fontWeight: "normal",
							fontFamily: "san-serif", //主题文字字体，默认微软雅黑
							fontSize: 12 //主题文字字体大小，
						},

					},
					grid: {
						left: '1%',
						right: '2%',
						top: '14%',
						height: '80%',
						containLabel: true
					},

					xAxis: [{
						show: false, //隐藏x轴
						type: 'category',
						data: [0, 1, 2, 3],
						boundaryGap: false,
						axisTick: {
							alignWithLabel: true
						},
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					yAxis: [{
						name: '(%)',
						type: 'value',
						splitNumber: 2,
						max: 2,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					series: [{
						type: 'line',
						smooth: true,
						symbolSize: 8,
						itemStyle: {
							normal: {
								color: '#dbdbdb',
							}
						},
						markLine: {
							data: [

								[{
										symbol: 'none',
										name: '',
										coord: ['0', 0],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#6dcf6f'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 1],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#6dcf6f'
											}
										}
									},

								],
								[{
										symbol: 'none',
										name: '',
										coord: ['0', 1],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#ff5050'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 2],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#ff5050'
											}
										}
									},

								],

								[{
										symbol: 'none',
										coord: ['1', 0],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									}, {
										name: '优化前',
										symbol: 'circle',
										coord: ['1', 2.7],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									},

								],
								[{
										symbol: 'none',
										color: '#333',
										coord: ['2', 0],
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									}, {
										name: '优化后',
										symbol: 'circle',
										coord: ['2', 0.05],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									},

								]
							]
						},
						areaStyle: {
							normal: {
								color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
									offset: 0,
									color: 'rgba(234,234,234,0.5)' // 0% 处的颜色
								}, {
									offset: 1,
									color: '#fff' // 100% 处的颜色
								}], false)
							}
						},
						data: ['', '2.7', '0.05'],
					}]
				};
				page.logic.setChartOption(data, 'floodAlarm', option5);
				page.logic.setMarkLintColor(option5, data, 'floodAlarm', '<');

				alarmFloodChart.setOption(option5);
			},
			/**
			 * 震荡报警（个）
			 */
			initAlarmShockChart: function(data) {
				if (alarmShockChart && !alarmShockChart.isDisposed()) {
					alarmShockChart.clear();
					alarmShockChart.dispose();
				}
				if (data == null || data == undefined || data.length == 0) {
					alarmShockChart = OPAL.ui.chart.initEmptyChart('alarmShockChart');
					return;
				}
				alarmShockChart = echarts.init(document.getElementById('alarmShockChart'));
				var option6 = {
					title: {
						text: '震荡报警(个)',
						left: '45%',
						bottom: '0',
						textStyle: { //标题内容的样式
							color: '#333',
							fontStyle: 'normal',
							fontWeight: "normal",
							fontFamily: "san-serif", //主题文字字体，默认微软雅黑
							fontSize: 12 //主题文字字体大小，
						},
					},
					grid: {
						left: '1%',
						right: '2%',
						top: '14%',
						height: '80%',
						containLabel: true
					},

					xAxis: [{
						show: false, //隐藏x轴
						type: 'category',
						data: [0, 1, 2, 3],
						boundaryGap: false,
						axisTick: {
							alignWithLabel: true
						},
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					yAxis: [{
						type: 'value',
						max: 300,
						splitNumber: 3,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					series: [{
						type: 'line',
						smooth: true,
						symbolSize: 8,
						itemStyle: {
							normal: {
								color: '#dbdbdb',
							}
						},
						markLine: {
							data: [
								[{
										symbol: 'none',
										name: '',
										coord: ['0', 0],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#ff5050'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 300],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#ff5050'
											}
										}
									},

								],

								[{
										symbol: 'none',
										coord: ['1', 0],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									}, {
										name: '优化前',
										symbol: 'circle',
										coord: ['1', 300],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									},

								],
								[{
										symbol: 'none',
										color: '#333',
										coord: ['2', 0],
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									}, {
										name: '优化后',
										symbol: 'circle',
										coord: ['2', 1],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									},

								]
							]
						},

						areaStyle: {
							normal: {
								color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
									offset: 0,
									color: 'rgba(234,234,234,0.5)' // 0% 处的颜色
								}, {
									offset: 1,
									color: '#fff' // 100% 处的颜色
								}], false)
							}
						},
						data: ['', '300', '1'],
					}]
				};
				page.logic.setChartOption(data, 'shockAlarmCount', option6);
				page.logic.setMarkLintColor(option6, data, 'shockAlarmCount', '==');
				// var key = 'shockAlarmCount';
				// var max = parseFloat(data['optAfter'][key]) > parseFloat(data['optBefore'][key]) ? data['optAfter'][key] : data['optBefore'][key];
				// var arr = new Array();
				// arr.push('0');
				// arr.push(max);
				// option6['series'][0]['markLine']['data'][0][1]['coord'] = arr;
				// option6['yAxis'][0]['max'] = max;
				alarmShockChart.setOption(option6);
			},
			/**
			 * 每天持续报警（个）
			 */
			initAlarmDayContinuousChart: function(data) {
				if (alarmDayContinuousChart && !alarmDayContinuousChart.isDisposed()) {
					alarmDayContinuousChart.clear();
					alarmDayContinuousChart.dispose();
				}
				if (data == null || data == undefined || data.length == 0) {
					alarmDayContinuousChart = OPAL.ui.chart.initEmptyChart('alarmDayContinuousChart');
					return;
				}
				alarmDayContinuousChart = echarts.init(document.getElementById('alarmDayContinuousChart'));
				var option7 = {
					title: {
						text: '每天持续报警(个)',
						left: '45%',
						bottom: '0',
						textStyle: { //标题内容的样式
							color: '#333',
							fontStyle: 'normal',
							fontWeight: "normal",
							fontFamily: "san-serif", //主题文字字体，默认微软雅黑
							fontSize: 12 //主题文字字体大小，
						},

					},
					grid: {
						left: '1%',
						right: '2%',
						top: '14%',
						height: '80%',
						containLabel: true
					},

					xAxis: [{
						show: false, //隐藏x轴
						type: 'category',
						data: [0, 1, 2, 3],
						boundaryGap: false,
						axisTick: {
							alignWithLabel: true
						},
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					yAxis: [{
						type: 'value',
						splitNumber: 2,
						max: 10,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					series: [{
						type: 'line',
						smooth: true,
						symbolSize: 8,
						itemStyle: {
							normal: {
								color: '#dbdbdb',
							}
						},
						markLine: {
							data: [

								[{
										symbol: 'none',
										name: '',
										coord: ['0', 0],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#6dcf6f'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 5],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#6dcf6f'
											}
										}
									},

								],
								[{
										symbol: 'none',
										name: '',
										coord: ['0', 5],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#ff5050'
											}
										}
									}, {
										symbol: 'none',
										coord: ['0', 10],
										lineStyle: {
											normal: {
												width: '2',
												type: 'solid',
												color: '#ff5050'
											}
										}
									},

								],

								[{
										symbol: 'none',
										coord: ['1', 0],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									}, {
										name: '优化前',
										symbol: 'circle',
										coord: ['1', 15],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#ff5151'
											}
										}
									},

								],
								[{
										symbol: 'none',
										color: '#333',
										coord: ['2', 0],
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									}, {
										name: '优化后',
										symbol: 'circle',
										coord: ['2', 4],
										color: '#333',
										lineStyle: {
											normal: {
												color: '#6dcf6f'
											}
										}
									},

								]
							]
						},
						areaStyle: {
							normal: {
								color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
									offset: 0,
									color: 'rgba(234,234,234,0.5)' // 0% 处的颜色
								}, {
									offset: 1,
									color: '#fff' // 100% 处的颜色
								}], false)
							}
						},
						data: ['', '15', '4'],
					}]
				};
				page.logic.setChartOption(data, 'dayContinuousAlarmCount', option7);
				page.logic.setMarkLintColor(option7, data, 'dayContinuousAlarmCount', '<');
				alarmDayContinuousChart.setOption(option7);
			}
		}
	};
	page.init();
	window.page = page;
});