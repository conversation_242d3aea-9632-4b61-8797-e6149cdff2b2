$(function () {
    var unitAlarmPageUrl = OPAL.API.aaUrl + '/alarmAssessFirstPages/getUnitAlarmPage';
    var workshopAlarmPageUrl = OPAL.API.aaUrl + '/alarmAssessFirstPages/getWorkshopAlarmPage';
    var unitExportUrl = OPAL.API.aaUrl + '/alarmAssessFirstPages/exportUnitAlarm';
    var workshopExportUrl = OPAL.API.aaUrl + '/alarmAssessFirstPages/exportWorkshopAlarm';
    window.pageLoadMode = PageLoadMode.Refresh;
    var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    var page = {
        data: {
            // 设置查询参数
            param: {}
        },
        init: function () {
            this.bindUI();
        },
        bindUI: function () {
            /*关闭弹窗*/
            $('#closePage').click(function () {
                page.logic.closeLayer(true);
            });

            $('#exportBtn').click(function () {
                page.logic.alarmResponseExport();
            });

        },
        logic: {
            alarmResponseExport: function () {
                var exportParam = {};
                var url = '';
                $.extend(exportParam, page.data.param);
                if (page.data.param.queryType === 1) {
                    exportParam.titles = JSON.stringify(new Array(
                        {
                            'key': 'workshopName',
                            'value': '车间'
                        }, {
                            'key': 'responseRate',
                            'value': '报警确认率'
                        }, {
                            'key': 'timelyResponseRate',
                            'value': '报警及时确认率'
                        }
                    ));
                    url = workshopExportUrl + "?" + $.param(exportParam);
                } else {
                    exportParam.titles = JSON.stringify(new Array(
                        {
                            'key': 'unitName',
                            'value': '装置'
                        }, {
                            'key': 'responseRate',
                            'value': '报警确认率'
                        }, {
                            'key': 'timelyResponseRate',
                            'value': '报警及时确认率'
                        }
                    ));
                    url = unitExportUrl + "?" + $.param(exportParam);
                }
                window.location = url;
            },
            /**
             * 初始化表格
             */
            initTable: function () {
                var columns = [{
                    title: "序号",
                    formatter: function (value, row, index) {
                        var data = page.data.param;
                        var pageNumber = data.pageNumber;
                        var pageSize = data.pageSize;
                        return index + 1 + (pageNumber - 1) * pageSize;
                    },
                    align: 'center'
                }];
                var searchUrl = '';
                if (page.data.param.queryType === 1) {
                    searchUrl = workshopAlarmPageUrl;
                    columns.push({
                        field: 'workshopName',
                        title: '车间',
                        align: 'center',
                    });
                } else {
                    searchUrl = unitAlarmPageUrl;
                    columns.push({
                        field: 'unitName',
                        title: '装置',
                        align: 'center',
                    });
                }
                columns.push({
                    field: 'responseRate',
                    title: '报警确认率',
                    align: 'center',
                }, {
                    field: 'timelyResponseRate',
                    title: '报警及时确认率',
                    align: 'center',
                })
                OPAL.ui.initBootstrapTable("table", {
                    columns: columns,
                    url: searchUrl
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                page.data.param = data
                page.logic.initTable();
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                parent.layer.close(index);
            }
        }
    }
    page.init();
    window.page = page;
})