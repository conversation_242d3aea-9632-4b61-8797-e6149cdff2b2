package com.pcitc.opal.ac.dao;

import java.util.List;

import com.pcitc.opal.ac.pojo.AlarmChangePlanDetail;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

/*
 * AlarmChangePlanDetail实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_AlarmChangePlanRepositoryDetailCustom
 * 作       者：xuelei.wang
 * 创建时间：2018/1/19
 * 修改编号：1
 * 描       述：AlarmChangePlanDetail实体的Repository的JPA自定义接口
 */
public interface AlarmChangePlanDetailRepositoryCustom {

    /**
     * 新增报警变更方案明细
     *
     * @param alarmChangePlanDetail 报警变更方案明细
     * @return 返回结果信息类
     * <AUTHOR> 2018-01-22
     */
    CommonResult addAlarmChangePlanDetail(AlarmChangePlanDetail alarmChangePlanDetail);

    /**
     * 删除报警变更方案明细实体
     *
     * @param alarmChangePlanDetailIds 报警变更方案明细Id数组
     * @return 返回结果信息类
     * <AUTHOR> 2018-01-19
     */
    CommonResult deleteAlarmChangePlanDetail(Long[] alarmChangePlanDetailIds);

    /**
     * 修改报警变更方案明细
     *
     * @param alarmChangePlanDetail 报警变更方案明细
     * @return 返回结果信息类
     * <AUTHOR> 2018-01-22
     */
    CommonResult updateAlarmChangePlanDetail(AlarmChangePlanDetail alarmChangePlanDetail);

    /**
     * 通过报警变更方案Id获取多条数据
     *
     * @param alarmChangePlanIds 报警变更方案Id数组
     * @return 报警变更方案明细实体集合
     * <AUTHOR> 2018-01-19
     */
    List<AlarmChangePlanDetail> getalarmChangePlanDetailByPlanIds(Long[] alarmChangePlanIds);

    /**
     * 根据报警变更方案Id、报警点Id、报警标识Id获取报警变更方案明细数据
     *
     * @param planId       报警变更方案ID
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @return AlarmChangePlanDetail 报警变更方案明细
     * <AUTHOR> 2018-01-22
     */
    List<AlarmChangePlanDetail> getChangeDetailByPointFlag(Long planId, Long alarmPointId, Long alarmFlagId);

    /**
     * 根据变更方案ID获取变更方案详情分页信息
     *
     * @param planId 变更方案ID
     * @param page   分页信息
     * @return       方案详情列表
     * <AUTHOR> 2018-01-22
     */
    PaginationBean<AlarmChangePlanDetail> getAlarmChangePlanDetail(Long planId, Pagination page);

    /**
     * 根据planDetailId集合获取变更方案详情列表
     *
     * @param planDetailIds
     * @return
     * <AUTHOR> 2018-01-22
     */
    List<AlarmChangePlanDetail> getByIds(Long[] planDetailIds);
    
}
