package com.pcitc.opal.as.dao;

import com.pcitc.opal.as.pojo.AlarmStdManagmt;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

import java.util.Date;
import java.util.List;

/*
 * 报警制度管理实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_AlarmStdManagmtRepositoryCustom
 * 作    者：kun.zhao
 * 创建时间：2018/2/28
 * 修改编号：1
 * 描    述：报警制度管理实体的Repository的JPA自定义接口
 */
public interface AlarmStdManagmtRepositoryCustom {

    /**
     * 校验数据
     *
     * <AUTHOR> 2018-03-01
     * @param alarmStdManagmt 报警制度管理实体
     * @return 返回结果信息类
     */
    CommonResult alarmStdManagmtValidation(AlarmStdManagmt alarmStdManagmt);

    /**
     * 新增数据
     *
     * <AUTHOR> 2018-03-01
     * @param alarmStdManagmt 报警制度管理实体
     * @return CommonResult 消息结果类
     */
    CommonResult addAlarmStdManagmt(AlarmStdManagmt alarmStdManagmt);

    /**
	 * 删除报警制度管理
	 *
	 * @param alarmStdManagmtIds 
	 * @return 返回结果信息类
	 */
	CommonResult deleteAlarmStdManagmt(Long[] alarmStdManagmtIds);

	/**
	 * 更新数据
	 *
	 * <AUTHOR> 2018-03-01
	 * @param alarmStdManagmt 报警制度管理实体
	 * @return CommonResult 消息结果类
	 */
	CommonResult updateAlarmStdManagmt(AlarmStdManagmt alarmStdManagmt);

	/**
	 * 根据报警制度管理ID获取单条数据信息
	 * 
	 * <AUTHOR> 2018-02-28
	 * @param alarmStdManagmtId 报警制度管理ID
	 * @return 报警制度管理实体
	 * @throws Exception
	 */
	AlarmStdManagmt getSingleAlarmStdManagmt(Long alarmStdManagmtId);
	
	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2018-02-28
	 * @param alarmStdManagmtIds 报警制度管理ID
	 * @return 报警制度管理实体数组
	 */
	List<AlarmStdManagmt> getAlarmStdManagmt(Long[] alarmStdManagmtIds);

	/**
	 * 获取报警制度管理分页数据
	 * 
	 * <AUTHOR> 2018-02-28
	 * @param name      名称
	 * @param catgr     分类
	 * @param startTime 上传时间范围起始
	 * @param endTime   上传时间范围结束
	 * @param page      分页对象
	 * @return 报警制度管理分页对象
	 * @throws Exception
	 */
	PaginationBean<AlarmStdManagmt> getAlarmStdManagmt(String name, Integer catgr, Date startTime, Date endTime,
			Pagination page);
	
	
}
