package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 系统运行参数配置Entity实体
 * 模块编号：pcitc_opal_bll_class_SystRunParaConfEntity
 * 作       者：kun.zhao
 * 创建时间：2018/01/22
 * 修改编号：1
 * 描       述：系统运行参数配置Entity实体
 */
public class SystRunParaConfEntity extends BasicEntity {
	
	/**
     * 系统运行参数配置ID
     */
    private Long systRunParaConfId;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 参数值
     */
    private String paraValue;

    /**
     * 描述
     */
    private String des;

	/**
	 * 企业ID
	 */
	//企业
	private Integer companyId;

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}
	public Long getSystRunParaConfId() {
		return systRunParaConfId;
	}

	public void setSystRunParaConfId(Long systRunParaConfId) {
		this.systRunParaConfId = systRunParaConfId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getParaValue() {
		return paraValue;
	}

	public void setParaValue(String paraValue) {
		this.paraValue = paraValue;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}
    
}
