package com.pcitc.opal.ad.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 报警记录写入日志
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_record_writelog")
public class RecordWriteLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "log_id", type = IdType.AUTO)
    private Long logId;

    /**
     * 下次开始执行的event_id
     */
    @TableField("current_id")
    private Long currentId;

    /**
     * 开始执行的event_id
     */
    @TableField("before_id")
    private Long beforeId;

    /**
     * 开始执行时间
     */
    @TableField("execute_time")
    private Date executeTime;

    /**
     * 异常信息
     */
    @TableField("error")
    private String error;

    /**
     * 是否历史
     */
    @TableField("is_history")
    private Integer isHistory;

    /**
     * 完成时间
     */
    @TableField("complete_time")
    private Date completeTime;

    /**
     * 企业ID
     */
    @TableField("company_id")
    private Long companyId;


}
