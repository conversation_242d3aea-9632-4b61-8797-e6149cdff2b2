package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.pm.bll.entity.AlarmReasonEntity;

import java.util.ArrayList;

/**
 * @USER: chenbo
 * @DATE: 2023/6/9
 * @TIME: 15:06
 * @DESC:
 **/
public interface AlarmReasonService {


    /**
     * 分页查询
     *
     * @param reasonType 原因类型
     * @param name       名称
     * @param inUse      是否启用
     * @param page       分页对象
     */
    PaginationBean<AlarmReasonEntity> getAlarmReason(Long reasonType, String name, Long inUse, Pagination page);


    /**
     * 新增报警原因
     *
     * @param reasonType 原因类型
     * @param name       原因名称
     * @param inUse      是否启用
     * @param sortNum    排序
     * @param des        描述
     */
    CommonResult addAlarmReason(Long reasonType, String name, Long inUse, Long sortNum, String des);


    /**
     * 更新报警原因
     *
     * @param alarmReasonId id
     * @param reasonType    原因类型
     * @param name          原因名称
     * @param inUse         是否启用
     * @param sortNum       排序
     * @param des           描述
     */
    CommonResult updateAlarmReason(Long alarmReasonId, Long reasonType, String name, Long inUse, Long sortNum, String des);

    /**
     * 删除报警原因
     *
     * @param alarmReasonId id
     */
    CommonResult deleteAlarmReason(Long[] alarmReasonId);


    /**
     * 获取 原因类型
     */
    ArrayList<DictionaryEntity> getReasonType();

    String getReasonByType(Long reasonType);
}
