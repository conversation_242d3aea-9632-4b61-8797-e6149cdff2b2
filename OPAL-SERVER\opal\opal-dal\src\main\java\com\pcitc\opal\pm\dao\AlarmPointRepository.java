package com.pcitc.opal.pm.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.pm.pojo.AlarmPoint;

/*
 * AlarmPoint实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmPointRepository
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：AlarmPoint实体的Repository实现   
 */
public interface AlarmPointRepository extends JpaRepository<AlarmPoint, Long>, AlarmPointRepositoryCustom {

}
