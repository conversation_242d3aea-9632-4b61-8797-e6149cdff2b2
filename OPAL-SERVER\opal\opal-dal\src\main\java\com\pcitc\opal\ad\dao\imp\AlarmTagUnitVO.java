package com.pcitc.opal.ad.dao.imp;

import lombok.Data;

import java.util.Date;

/*
 * 报警次数统计统计数据实体
 * 作  　  者：shufei.sui
 * 创建时间：2019/09/26
 * 修改编号：1
 * 描       述：报警次数统计统计数据实体
 */
@Data
public class AlarmTagUnitVO {
    public AlarmTagUnitVO(String tag, String unitCode, String unitName, Long count) {
        this.tag = tag;
        this.unitCode = unitCode;
        this.unitName = unitName;
        this.count = count;
    }

    /**
     * 位号
     */
    private String tag;
    /**
     * 装置code
     */
    private String unitCode;
    /**
     * 装置名称
     */
    private String unitName;

    private Long count;

}
