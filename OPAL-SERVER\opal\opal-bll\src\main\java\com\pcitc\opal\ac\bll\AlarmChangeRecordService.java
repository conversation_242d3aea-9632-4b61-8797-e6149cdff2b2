package com.pcitc.opal.ac.bll;

import com.pcitc.opal.ac.bll.entity.AlarmChangeRecordEntity;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import org.springframework.stereotype.Service;

import java.util.Date;

/*
 * 变更记录逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmChangeRecordService
 * 作	者：kun.zhao
 * 创建时间：2017/11/10
 * 修改编号：1
 * 描	述：变更记录逻辑层接口
 */
@Service
public interface AlarmChangeRecordService {

	/**
	 * 获得变更记录实体
	 *
	 * <AUTHOR> 2017-11-10
	 * @param unitCodes	    装置编码数组
	 * @param prdtCellIds   生产单元ID数组
	 * @param workTeamIds   班组ID数组
	 * @param startTime	        时间范围起始
	 * @param endTime	        时间范围结束
	 * @param page		        分页实体
	 * @return 变更记录
	 * @throws Exception
	 */
	PaginationBean<AlarmChangeRecordEntity> getAlarmChangeRecord(String[] unitCodes, Long[] prdtCellIds, Long[] workTeamIds,
			Date startTime, Date endTime, Pagination page, Integer AlarmChangeRecordBusinessType) throws Exception;

}
