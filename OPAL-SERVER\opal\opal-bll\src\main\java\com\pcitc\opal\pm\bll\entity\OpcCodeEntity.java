package com.pcitc.opal.pm.bll.entity;

/*
 * Opc编码表
 * 模块编号：pcitc_opal_bll_class_OpcCodeEntity
 * 作    者：jiangtao.xue
 * 创建时间：2018-08-23 08:47:57
 * 修改编号：1
 * 描    述：Opc编码表
 */
public class OpcCodeEntity {

    /**
     * OPC编码ID
     */
    private Long opcCodeId;

    /**
     * 名称
     */
    private String name;

    /**
     * 是否启用（1是；0否）
     */
    private Integer inUse;

    /**
     * 排序
     */
    private Integer sortNum;
    /**
    **
            * 企业ID
     */
    //企业
    private Integer companyId;

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Long getOpcCodeId() {
        return opcCodeId;
    }

    public void setOpcCodeId(Long opcCodeId) {
        this.opcCodeId = opcCodeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getInUse() {
        return inUse;
    }

    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }
}

