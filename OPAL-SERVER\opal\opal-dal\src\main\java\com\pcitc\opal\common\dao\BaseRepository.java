package com.pcitc.opal.common.dao;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.pojo.DateRange;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

/*
 * Repository基类
 * 模块编号：pcitc_opal_dal_class_BaseRepository
 * 作       者：dongsheng.zhao
 * 创建时间：2017/09/17
 * 修改编号：1
 * 描       述：Repository基类
 */
@SuppressWarnings("Duplicates")
@NoRepositoryBean
@Slf4j
public class BaseRepository<T, ID extends Serializable> {

    private Class<T> entityClass;
    private EntityManager entityManager;
    @Resource
    private RedisTemplate<String,Integer> redisTemplate;

    @Value("${cache.timeout:5}")
    private Integer cacheTimeout;

    /**
     * 注入EntityManager，同时实例化SimpleJpaRepository
     *
     * @param entityManager
     * <AUTHOR> 2017-09-18
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @PersistenceContext
    public void setEntityManager(EntityManager entityManager) {
        this.entityManager = entityManager;

        Type genType = getClass().getGenericSuperclass();
        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();
        entityClass = (Class) params[0];
    }

    /**
     * 获取EntityManager，操作jpa api的入口
     *
     * @return EntityManager
     * <AUTHOR> 2017-09-18
     */
    public EntityManager getEntityManager() {
        return entityManager;
    }

    /**
     * 获取分页数据
     *
     * @param page      分页参数
     * @param jpql      hql语句
     * @param paramList 参数列表
     * @return 分页数据
     * <AUTHOR> 2017-09-18
     */
    public PaginationBean<T> findAll(Pagination page, String jpql, Map<String, Object> paramList) {
        Integer count = null;
        
        StringBuilder key = new StringBuilder("count:" + entityClass.getSimpleName() + ":");

        //根据sql语句和参数计算hash值
        key.append(jpql.hashCode() + paramList.hashCode());

        Integer integer = redisTemplate.opsForValue().get(key.toString());

        if (integer != null) {
            count = integer;
            log.info("使用缓存查询count");
        } else {
            count = getCount(jpql, paramList).intValue();
            redisTemplate.boundValueOps(key.toString()).set(count);
            log.info("使用数据库查询count");
            //设置过期时间
            redisTemplate.expire(key.toString(), cacheTimeout, TimeUnit.MINUTES);
        }
        PaginationBean<T> resultList = new PaginationBean<T>(page, Long.valueOf(count));
        TypedQuery<T> query = this.entityManager.createQuery(jpql, entityClass);
        this.setParameterList(query, paramList);
        query.setFirstResult(resultList.getBeginIndex()).setMaxResults(resultList.getPageSize());
        resultList.setPageList(query.getResultList());
        return resultList;
    }


    /**
     * 使用原生sql获取分页数据
     *
     * @param jpql      hql语句
     * @param paramList 参数列表
     * @return 分页数据
     * <AUTHOR>
     */
    public PaginationBean<Object[]> findAllNativeQuery(Pagination page, String jpql, Map<String, Object> paramList) {
        Integer count = null;

        StringBuilder key = new StringBuilder("count:" + entityClass.getSimpleName() + ":");

        //根据sql语句和参数计算hash值
        key.append(jpql.hashCode() + paramList.hashCode());

        Integer integer = redisTemplate.opsForValue().get(key.toString());

        if (integer != null) {
            count = integer;
            log.info("使用缓存查询count");
        } else {
            count = getCountNativeQuery(jpql, paramList).intValue();
            redisTemplate.boundValueOps(key.toString()).set(count);
            //设置过期时间
            redisTemplate.expire(key.toString(), cacheTimeout, TimeUnit.MINUTES);
            log.info("使用数据库查询count");
        }

        PaginationBean<Object[]> resultList = new PaginationBean<Object[]>(page, Long.valueOf(count));
        Query query = this.entityManager.createNativeQuery(jpql);
        this.setParameterList(query, paramList);
        query.setFirstResult(resultList.getBeginIndex()).setMaxResults(resultList.getPageSize());
        resultList.setPageList(query.getResultList());
        return resultList;
    }

    /**
     * 获取分页数据
     *
     * @param entityManager  EntityManager
     * @param page          分页参数
     * @param total         总条数
     * @param jpql          hql语句
     * @param paramList     参数列表
     * @param obj           返回类型对象.class
     * @return 分页数据
     * <AUTHOR> 2022-12-12
     */
    public PaginationBean<T> findCusTomAll(EntityManager entityManager,Pagination page,Long total, String jpql, Map<String, Object> paramList,Class obj) {
        PaginationBean<T> resultList = new PaginationBean<T>(page, total);
        TypedQuery<T> query = entityManager.createQuery(jpql, obj);
        this.setParameterList(query, paramList);
        query.setFirstResult(resultList.getBeginIndex()).setMaxResults(resultList.getPageSize());
        resultList.setPageList(query.getResultList());
        return resultList;
    }
    /**
     * 获取分页条数
     *
     * @param jpql          hql语句
     * @param paramList     参数列表
     * @return 分页条数
     * <AUTHOR> 2022-12-12
     */
    public Integer findCusCount(String jpql, Map<String, Object> paramList){
        Integer count = null;
        StringBuilder key = new StringBuilder("count:" + entityClass.getSimpleName() + ":");

        //根据sql语句和参数计算hash值
        key.append(jpql.hashCode() + paramList.hashCode());

        Integer integer = redisTemplate.opsForValue().get(key.toString());

        if (integer != null) {
            count = integer;
            log.info("使用缓存查询count");
        } else {
            count = getCount(jpql, paramList).intValue();
            redisTemplate.boundValueOps(key.toString()).set(count);
            log.info("使用数据库查询count");
            //设置过期时间
            redisTemplate.expire(key.toString(), cacheTimeout, TimeUnit.MINUTES);
        }
        return count;
    }

    /**
     * 使用原生sql获取分页数据的总量
     *
     * @param jpql      查询语句
     * @param paramList 参数列表
     * @return 数据条数
     * <AUTHOR>
     */
    protected Long getCountNativeQuery(String jpql, Map<String, Object> paramList) {
        String countHql = CountSqlBuilder.toCountHql(jpql);
        Query query = this.entityManager.createNativeQuery(countHql);
        this.setParameterList(query, paramList);
        Object singleResult = query.getSingleResult();
        return Long.valueOf(singleResult.toString());
    }




    /**
     * 批量保存班组时间段
     *
     * @param rangeList 日期时间段列表
     * <AUTHOR> 2017-11-29
     */
    @Transactional
    public void saveDateRangeList(List<DateRange> rangeList) {
        for (DateRange range : rangeList) {
            this.getEntityManager().persist(range);
        }
    }

    /**
     * 设置Query的参数
     *
     * @param query     Query查询对象
     * @param paramList 参数列表
     * <AUTHOR> 2017-09-18
     */
    protected void setParameterList(Query query, Map<String, Object> paramList) {
        for (Entry<String, Object> pair : paramList.entrySet()) {
            query.setParameter(pair.getKey(), pair.getValue());
        }
    }

    /**
     * 获取分页数据的总量
     *
     * @param jpql      查询语句
     * @param paramList 参数列表
     * @return 数据条数
     * <AUTHOR> 2017-09-18
     */
    protected Long getCount(String jpql, Map<String, Object> paramList) {
        String countHql = CountHqlBuilder.toCountHql(jpql);
        TypedQuery<Long> query = this.entityManager.createQuery(countHql, Long.class);
        this.setParameterList(query, paramList);
        return query.getSingleResult();
    }

    /**
     * 处理Like语句
     *
     * @param name sql字符串
     * @return 处理完的字符串
     * <AUTHOR> 2017-09-18
     */
    protected String sqlLikeReplace(String name) {
        if (StringUtils.isEmpty(name))
            return "";
        return name.replace("/", "//").replace("%", "/%").replace("_", "/_");
    }

    protected String sqlLikeReplaceNew(String name) {
        if (StringUtils.isEmpty(name))
            return "";
        return name.replace("/", "\\/").replace("%", "/%").replace("_", "\\_");
    }
    /**
     * 根据装置获取企业id
     * @param unit 装置id集合
     * @return
     */
    public Integer getCompanyIdByUnit(String[] unit) {
        String sql = "select company_id from t_pm_unit where std_code in (:unit) and company_id is not null limit 1";

        Query nativeQuery = entityManager.createNativeQuery(sql);
        nativeQuery.setParameter("unit", Arrays.asList(unit));
        List resultList = nativeQuery.getResultList();

        if (CollectionUtils.isNotEmpty(resultList)){
            return Integer.valueOf(resultList.get(0).toString());
        }else {
            return null;
        }
    }

}