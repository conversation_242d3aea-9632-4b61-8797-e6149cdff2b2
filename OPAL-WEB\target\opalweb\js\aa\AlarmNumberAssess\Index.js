var numberAssessUrl = OPAL.API.aaUrl + '/alarmNumberAssess';
var singleUnitUrl = OPAL.API.aaUrl + '/alarmNumberAssess/getAlarmNumberUnit';
var operateNumberUrl = OPAL.API.aaUrl + '/alarmNumberAssess/getAlarmNumberAssessTrendEntity';
var operateNumberSingleUrl = OPAL.API.aaUrl + '/alarmNumberAssess/getAlarmNumberAssessTrendEntitySingle';
var numberTopUrl = OPAL.API.aaUrl + '/alarmNumberAssess/getAlarmNumberAssessTop20';
var numberTopSingleUrl = OPAL.API.aaUrl + '/alarmNumberAssess/getAlarmNumberAssessTop20Single';
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var exportUrl = OPAL.API.aaUrl + '/alarmNumberAssess/exportExcel';
var exportAlarmAssessUrl = OPAL.API.aaUrl + '/alarmNumberAssess/exportAlarmNumberAssess';
var exportAlarmAssessSingleUrl = OPAL.API.aaUrl + '/alarmNumberAssess/exportAlarmOperateAssessTop20Single';
var workTeamUrl = OPAL.API.commUrl + "/getWorkTeam";
var numberChart;//报警数图表
var mostFrequentNumberChart;//最频繁报警图表
var unitChart;//单装置图表
var operNumChart;//操作/报警 图表
var option;
var displayName = "单元";
var idsArray = new Array();
var idsArray1 = new Array(); //单装置
var xaxisArr; //x轴
var highLightColor = '#6b6d75'; //高亮颜色
var barIndex = new Array();//报警数图表  点击柱子交互时使用的变量
var barCorlor; //报警数图表  点击柱子交互时使用的变量
var dateArray;// 最频繁报警 详情 使用
var topCount = 20; //最频繁报警  选中（top20 or top10 ）
var isLoading = true;
var alarmNumberDetailData; //按班组查询报警数据（按班组分组）
var alarmNumberTop20Data=[]; //按班组查询报警数据 （根据报警数排序数据）
var chartAlarmNumId = ''; // 点击选择的报警数图表id
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            /**
             *绑定事件
             */
            this.bindUi();
            /**
             * 日期扩展
             */
            OPAL.util.extendDate();
            /**
             * 初始化日期时间选择控件组
             */
            OPAL.ui.initDateTimePeriodPicker({
                'dateTypeCtrId': 'timeGranularity'
            },function() {
                var startTime = OPAL.util.strToDate($("#startTime").val());
                var endTime = OPAL.util.strToDate($("#endTime").val());
                var timeMiss = (endTime - startTime) / (1000 * 60 * 60 * 24);
                if ($("#checkTeam").is(":checked") && timeMiss == 0) {
                    $("#timeGranularity").empty();
                    $("#timeGranularity").html('<option value="day">天</option>');
                }
                page.logic.initWorkTeam();
            });
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                mostFrequentNumberChart.resize();
                numberChart.resize();
                unitChart.resize();
                operNumChart.resize();
                mostFrequentNumberChart.resize();
            };
            /**
             * 初始化装置数
             */
            page.logic.initUnitTree();
            /**
             * 初始化表格
             */
            page.logic.initTable();
            // 初始化报警数图
            page.logic.initNumberChart();

            page.logic.initMostFrequentNumberChart();

            page.logic.initOperateNumberChart();

            page.logic.initOperateShowShow();
            //初始化按车间显示
            page.logic.showPrdtCellSelect();

            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("AlarmNumberAssess");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                    page.logic.search();
                }
            }
        },
        bindUi: function () {
            /**
             * 导航切换
             */
            $('.myTab li').click(function () {
                var flag = $(this).attr('showFlag');
                var num = $(this).attr('tableNum');
                if (flag == 'imgShow') {
                    $(this).find('img').attr('src', '../../../images/one1.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/tweo.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png');
                    if (num == '1') {
                        $("#expBtn").empty().removeClass("cursor_pointer");
                    }
                    if (num == '3') {
                        $("#exportOftenBtn").empty().removeClass("cursor_pointer");
                    }
                } else if (flag == 'tableShow') {
                    $(this).find('img').attr('src', '../../../images/tweos.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png');
                    if (num == '1') {
                        if ($("#checkTeam").is(":checked") || $("#checkShop").is(":checked")) {
                            $("#expBtn").empty().removeClass("cursor_pointer");
                        } else {
                            $("#expBtn").html("&nbsp;&nbsp;&nbsp;&nbsp;导出").addClass("cursor_pointer");
                        }
                    }
                    if (num == '3') {
                        if ($("#checkTeam").is(":checked") || $("#checkShop").is(":checked")) {
                            $("#exportOftenBtn").empty().removeClass("cursor_pointer");
                        } else {
                            $("#exportOftenBtn").html("&nbsp;&nbsp;&nbsp;&nbsp;导出").addClass("cursor_pointer");
                        }
                    }
                    // if($("#checkTeam").is(":checked")){
                    //     $("#expBtn").empty().removeClass("cursor_pointer");
                    // }else{
                    //     $("#expBtn").html("&nbsp;&nbsp;&nbsp;&nbsp;导出").addClass("cursor_pointer");
                    // }
                } else if (flag == 'unitShow') {
                    $(this).find('img').attr('src', '../../../images/treese.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/tweo.png');
                    if (num == '1') {
                        $("#expBtn").empty().removeClass("cursor_pointer");
                    }
                    if (num == '3') {
                        $("#exportOftenBtn").empty().removeClass("cursor_pointer");
                    }
                }
            });
            /**
             * Top20和Top10切换
             */
            $("#btnTop10").click(function () {
                $(this).removeClass("active").addClass("active").removeClass("btn-select").addClass("btn-select");
                $("#btnTop20").removeClass("active").removeClass("btn-select").removeClass("btn-unselect").addClass("btn-unselect");
                topCount = 10;
                $("#topType").val(topCount);
                page.logic.reloadMostTopData();
            });
            $("#btnTop20").click(function () {
                $(this).removeClass("active").addClass("active").removeClass("btn-select").addClass("btn-select");
                $("#btnTop10").removeClass("active").removeClass("btn-select").removeClass("btn-unselect").addClass("btn-unselect");
                topCount = 20;
                $("#topType").val(topCount);
                page.logic.reloadMostTopData();
            });
            //表格自适应页面拉伸宽度
            $(window).resize(function () {
                $('#tableNumber').bootstrapTable('resetView');
                $('#tableTop20').bootstrapTable('resetView');
                $('#tableOperateNumber').bootstrapTable('resetView');
            });
            /**
             * 查询
             */
            $('#btnSearch').click(function () {
                isLoading=false;
                chartAlarmNumId = '';
                $("#NowTimeSpan").html(OPAL.util.dateFormat(OPAL.util.strToDate($("#startTime").val()), "yyyy-MM-dd") + " 00:00:00 至" + OPAL.util.dateFormat(OPAL.util.strToDate($("#endTime").val()), "yyyy-MM-dd")+" 23:59:59");
                page.logic.search();
            });
            /**
             * 按车间显示按钮
             */
            $("#checkShop").click(function () {
                $("#expBtn").html("&nbsp;&nbsp;&nbsp;&nbsp;导出").addClass("cursor_pointer");
                $("#exportOftenBtn").html("&nbsp;&nbsp;&nbsp;&nbsp;导出").addClass("cursor_pointer");
                page.logic.showPrdtCellSelect();
            })
            //按班组显示按钮
            $("#checkTeam").click(function() {
                $("#expBtn").html("&nbsp;&nbsp;&nbsp;&nbsp;导出").addClass("cursor_pointer");
                $("#exportOftenBtn").html("&nbsp;&nbsp;&nbsp;&nbsp;导出").addClass("cursor_pointer");
                page.logic.showTeamSelect();
            })

            $("#expBtn").click(function() {
                page.logic.exportExcel();
            })
            // 最频繁的报警导出
            $("#exportOftenBtn").click(function() {
                page.logic.exportTop20Excel();
            })
            
        },
        data: {
            param: {},
            type: -1
        },
        logic: {
            // 导出报警数
            exportExcel: function () {
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                var prdtIds = $("#prdtIds").val();
                if ($("#checkShop").is(":checked")) {
                    if ($("#unitIds").val() == '') {
                        layer.msg('请选择车间！');
                        return;
                    }
                    displayName = "车间";
                } else if($("#checkTeam").is(":checked")) {
                    if ($("#team").val() == '' ||$("#team").val() == null) {
                        layer.msg('请选择班组！');
                        return;
                    }
                } else {
                    if ($("#unitIds").val() == '') {
                        layer.msg('请选择装置！');
                        return;
                    }
                    if (prdtIds == "" || prdtIds == undefined || prdtIds.length == 0) {
                        displayName = "装置";
                    } else {
                        displayName = "单元";
                    }
                }
                page.logic.toggleDisplayName();
                var searchData = OPAL.form.getData('formSearch');
                if(searchData.checkShop == 0) {
                    delete searchData.checkShop;
                }
                if(searchData.checkTeam == 0) {
                    delete searchData.checkTeam;
                }
                var titleArray = new Array(
                    {"value":"序号",'key':"index"},
                    {'value':"时间",'key':"alarmTime"},
                    {'value':displayName == "单元" ? "生产单元" : displayName,'key':"name"},
                    {'value':"报警数",'key':"alarmCount"}
                );
                searchData.titles = JSON.stringify(titleArray);
                OPAL.ui.setExportExcelData("formExPort",searchData);
                $('#formExPort').attr('action', exportUrl);
                $('#formExPort').submit();
            },
            // 导出最频繁的报警TOP20
            exportTop20Excel: function () {
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                var prdtIds = $("#prdtIds").val();
                if ($("#checkShop").is(":checked")) {
                    if ($("#unitIds").val() == '') {
                        layer.msg('请选择车间！');
                        return;
                    }
                    displayName = "车间";
                } else if($("#checkTeam").is(":checked")) {
                    if ($("#team").val() == '' ||$("#team").val() == null) {
                        layer.msg('请选择班组！');
                        return;
                    }
                } else {
                    if ($("#unitIds").val() == '') {
                        layer.msg('请选择装置！');
                        return;
                    }
                    if (prdtIds == "" || prdtIds == undefined || prdtIds.length == 0) {
                        displayName = "装置";
                    } else {
                        displayName = "单元";
                    }
                }
                page.logic.toggleDisplayName();
                let searchData;
                let url = '';
                if (!chartAlarmNumId) { // 导出初始化数据
                    searchData = OPAL.form.getData('formSearch');
                    url = exportAlarmAssessUrl;
                } else {  // 导出报警数图表对应id数据
                    searchData = JSON.parse(JSON.stringify(page.data.param));
                    url = exportAlarmAssessSingleUrl;
                }
                if(searchData.checkShop == 0) {
                    delete searchData.checkShop;
                }
                if(searchData.checkTeam == 0) {
                    delete searchData.checkTeam;
                }
                var titleArray = new Array(
                    {"value":"序号",'key':"index"},
                    {'value':"位号",'key':"tag"},
                    {'value':"报警标识",'key':"alarmFlag"},
                    {'value':"报警数",'key':"alarmCount"},
                    {'value':"百分比(%)",'key':"percent"},
                    {'value':"优先级",'key':"priorityName"},
                    {'value':"装置",'key':"unitName"},
                    {'value':"生产单元",'key':"prdtCellName"},
                    {'value':"位置描述",'key':"location"}
                );
                searchData.titles = JSON.stringify(titleArray);
                OPAL.ui.setExportExcelData("formExPort2",searchData);
                $('#formExPort2').attr('action', url);
                $('#formExPort2').submit();
            },
            search: function () {
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                $("#operNumSpan").html('');
                $("#AlarmTop20Span").html('');
                // $("#alarmAvg").html('0');
                $("#alarmTotal").html('0');
                var prdtIds = $("#prdtIds").val();
                if ($("#checkShop").is(":checked")) {
                    if ($("#unitIds").val() == '') {
                        layer.msg('请选择车间！');
                        return;
                    }
                    displayName = "车间";
                } else {
                    if ($("#unitIds").val() == '') {
                        layer.msg('请选择装置！');
                        return;
                    }
                    if (prdtIds == "" || prdtIds == undefined || prdtIds.length == 0) {
                        displayName = "装置";
                    } else {
                        displayName = "单元";
                    }
                    if($("#checkTeam").is(":checked") && ($("#team").val() == '' ||$("#team").val() == null)) {
                        layer.msg('请选择班组！');
                        return;
                    } 
                }
                page.logic.toggleDisplayName();
                $("#btnSearch").attr('disabled', true);
                //报警数图表
                $.ajax({
                    url: numberAssessUrl,
                    async: true,
                    data: $('#formSearch').serialize(),
                    dataType: 'json',
                    type: 'get',
                    success: function (data) {
                        if (data.length > 0) {
                            $("#alarmTotal").html(data[0]['sum']);
                            // $("#alarmAvg").html(data[0]['avg']);
                            idsArray = new Array();
                            for (var i = 0; i < data.length; i++) {
                                idsArray.push(data[i].id);
                            }
                        }
                        page.logic.initNumberTable(data);
                        page.logic.initNumberChart(data);
                        if($("#checkTeam").is(":checked") && data != null && data != undefined && data.length > 0) {
                            alarmNumberTop20Data = data[0].shiftWorkTeamEntityList;
                            page.logic.initMostFrequentNumberChart(alarmNumberTop20Data);
                            page.logic.initTop20Table(alarmNumberTop20Data);
                        }
                    },
                    error: function (data) {
                    },
                    complete: function () {
                        $("#btnSearch").attr('disabled', false);
                    }

                });
                if(!$("#checkTeam").is(":checked")) {
                    //单装置图表
                    $.ajax({
                        url: singleUnitUrl,
                        async: true,
                        data: $('#formSearch').serialize(),
                        dataType: 'json',
                        type: 'get',
                        success: function (data) {
                            idsArray1 = new Array();
                            for (var i = 0; i < data.length; i++) {
                                idsArray1.push(data[i].id);
                            }
                            page.logic.initOperateShowShow(data);
                        },
                        error: function (data) {
                        },
                        complete: function () {
                            $("#btnSearch").prop('disabled', false);
                        }

                    });
                }

                //报警数/操作数图表
                $.ajax({
                    url: operateNumberUrl,
                    async: true,
                    data: $('#formSearch').serialize(),
                    dataType: 'json',
                    type: 'get',
                    success: function (data) {
                        page.logic.initOperateNumberTable(data);
                        page.logic.initOperateNumberChart(data, null);
                    },
                    error: function (data) {
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }

                });
                if(!$("#checkTeam").is(":checked")){
                    //最频繁数量图表
                    page.logic.querySearchData($('#formSearch').serializeArray()); 
                }
                
            },
            /**
             * 查询数据(获取最频繁的报警数据)
             */
            querySearchData: function (queryData) {
                page.data.param = queryData;
                page.data.type = 1;
                $.ajax({
                    url: numberTopUrl,
                    async: true,
                    data: $.param(queryData),
                    dataType: 'json',
                    type: 'get',
                    success: function (data) {
                        var data = $.ET.toObjectArr(data);
                        page.logic.initMostFrequentNumberChart(data);
                        page.logic.initTop20Table(data);
                    },
                    error: function (data) {
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }

                });
            },
            /**
             * 重新加载数据
             */
            reloadMostTopData: function () {
                if($("#checkTeam").is(":checked")) {
                    page.logic.initMostFrequentNumberChart(alarmNumberTop20Data);
                    page.logic.initTop20Table(alarmNumberTop20Data);
                } else {
                    if (page.data.param == undefined || page.data.type == -1) return;
                    var exist = false;
                    if (page.data.param instanceof Array) {
                        for (var i = 0; i < page.data.param.length; i++) {
                            if (page.data.param[i]['name'] == 'topType') {
                                page.data.param[i]['value'] = topCount;
                                exist = true;
                                break;
                            }
                        }
                        if (exist == false) {
                            page.data.param.push({'name': 'topType', 'value': topCount});
                        }
                    } else if(page.data.param instanceof Object){
                        page.data.param.topType=topCount;
                    }
                    if (page.data.type == 1) {
                        page.logic.querySearchData(page.data.param);
                    } else if (page.data.type == 2) {
                        page.logic.queryChartClickData(page.data.param);
                    }
                }
                
            },
            /**
             * 加载表格点击数据 (点击报警数图形、表格)
             * @param data
             */
            queryChartClickData: function (data) {
                data.topType = topCount;
                page.data.param = data;
                page.data.type = 2;
                chartAlarmNumId = data.id;
                $.ajax({
                    url: numberTopSingleUrl,
                    async: true,
                    data: data,
                    dataType: 'json',
                    type: 'get',
                    success: function (data) {
                        var data = $.ET.toObjectArr(data);
                        page.logic.initMostFrequentNumberChart(data);
                        page.logic.initTop20Table(data);
                    },
                    error: function (data) {
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }

                });
            },
            /**
             * 初始化操作数图表
             * @param data
             */
            initNumberChart: function (data) {
                if (numberChart && !numberChart.isDisposed()) {
                    numberChart.clear();
                    numberChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    numberChart = OPAL.ui.chart.initEmptyChart('numberChart');
                    return;
                }
                numberChart = echarts.init(document.getElementById('numberChart'));
                option = {
                    color: ['#6699CC', '#669999', '#CC99CC', '#66CCCC', '#9999CC'],
                    legend: {
                        data: [],
                        left: 50,
                        right: 100,
                        icon: 'bar',
                        itemWidth: 10, //设置icon大小
                        itemHeight: 10, //设置icon大小
                        // top:2
                    },
                    grid: {
                        left: '1%',
                        right: '3%',
                        // bottom: '1%',
                        top: '18%',
                        height: '200px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 0,
                            textStyle: {
                                color: '#333',
                            },
                            formatter: function (value, index) {
                                if (value.length > 10) {
                                    value = value.substring(11, 16);
                                }
                                if ($("#timeGranularity").val() == 'day' || $("#timeGranularity").val() == 'week') {
                                    value = value.substring(5, 11);
                                }
                                return value;
                            }
                        }
                    }],
                    tooltip: {
                        trigger: 'item',
                        formatter: function (param) {
                            return option.series[param['seriesIndex']]['tooltip'][param['dataIndex']];
                        }
                    },
                    yAxis: [{
                        type: 'value',
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 'auto',
                            textStyle: {
                                color: '#333',
                                fontSize: 8,
                                fontStyle: 'normal'
                            }
                        }
                    }],
                    series: [],
                    tableData: [],
                    dataZoom: [{
                        orient: "horizontal",
                        show: true,
                        start: 0,
                        end: 100,
                        height: 20,
                        bottom: 2,
                    }, {
                        type: 'slider',
                        yAxisIndex: 0,
                        filterMode: 'none',
                        width: 20,


                    }]
                };
                if($("#checkTeam").is(":checked")) {
                    for(var i = 0; i<data.length;i++){
                        if(option.legend.data.length == 0){
                            option.legend.data = data[i]['legend'];
                        }
                        if (option.xAxis[0].data.length == 0) {
                            xaxisArr = data[i]['xaxis'];
                            option.xAxis[0].data = data[i]['xaxis'];
                        }
                        var customData = new Array();
                        for (var j = 0; j < data[i]['counts'].length; j++) {
                            customData.push({
                                value: data[i]['counts'][j] == "0" ? undefined : data[i]['counts'][j],
                                itemStyle: {
                                    normal: {
                                        //color: data[i]['color']
                                    }
                                }
                            });
                        }
                        option.series.push({
                            name: data[i]['barNames'][0],
                            type: 'bar',
                            barMaxWidth: '30',
                            data: customData,
                            tooltip: data[i]['tip']
                        });
                    }
                    
                }else{
                    option.color = data[0]['color'];
                    for (var i = 0; i < data.length; i++) {
                        option.legend.data.push({
                            name: data[i]['name'],
                            icon: 'bar',
                        });
                        if (option.xAxis[0].data.length == 0) {
                            xaxisArr = data[i]['xaxis'];
                            option.xAxis[0].data = data[i]['xaxis'];
                        }
                        var customData = new Array();
                        for (var j = 0; j < data[i]['counts'].length; j++) {
                            customData.push({
                                value: data[i]['counts'][j] == "0" ? undefined : data[i]['counts'][j],
                                itemStyle: {
                                    normal: {
                                        //color: data[i]['color']
                                    }
                                }
                            });
                        }
                        option.series.push({
                            name: data[i]['name'],
                            type: 'bar',
                            barMaxWidth: '30',
                            data: customData,
                            tooltip: data[i]['tip']
                        });
                    }
                    option.tableData = data[0]['list'];
                    option.legend.data = data[0]['legend'];
                    page.logic.chartClick(numberChart);
                }
                numberChart.setOption(option);
                
            },
            setNumberChart: function (params, queryData, isShadow) {

                var searchType = $("#checkShop").is(':checked') == true ? 1 : searchType = $("#prdtIds").val() == '' ? 2 : 3;
                var data = {
                    startTime: $("#startTime").val(),
                    endTime: $("#endTime").val(),
                    timeGranularity: $("#timeGranularity").val(),
                    id: queryData['id'],
                    searchType: searchType
                };
                var data1 = {
                    startTime: queryData['alarmTime'],
                    endTime: queryData['endTime']
                }
                dateArray = data1;
                $.ajax({
                    url: operateNumberSingleUrl,
                    async: true,
                    data: data,
                    dataType: 'json',
                    type: 'get',
                    success: function (data) {
                        page.logic.initOperateNumberTable(data);
                        if (isShadow == false) {
                            page.logic.initOperateNumberChart(data, null);
                        } else {
                            page.logic.initOperateNumberChart(data, params.name == '' ? null : params.dataIndex);
                        }

                    },
                    error: function (data) {
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }

                });
                page.logic.queryChartClickData($.extend(data, data1));
            },
            chartClick: function (myChart) {
                // 点击图表 改变颜色
                myChart.on('click', function (params) {
                    var titleHTML = params.seriesName;
                    $("#operNumSpan").html("（" + titleHTML + '）');
                    $("#AlarmTop20Span").html("（" + titleHTML + '）');
                    var i = params.seriesIndex;
                    var queryData;
                    for (var i = 0; i < option.tableData.length; i++) {
                        if (params['seriesName'] == option.tableData[i]['name'] && params['value'] == option.tableData[i]['alarmCount']) {
                            queryData = option.tableData[i];
                        }
                    }
                    page.logic.setNumberChart(params, queryData);
                    page.logic.changChartColor(myChart, params, i);

                })
            },
            changChartColor: function (myChart, params, i) {
                var seriesArr = option.series;
                if (barIndex[2] != $('#formSearch').serialize()) {
                    barIndex = new Array();
                }
                if (barIndex.length > 0) {
                    seriesArr[barIndex[0]].data[barIndex[1]].itemStyle.normal.color = barCorlor;
                    seriesArr[barIndex[0]].data[barIndex[1]].itemStyle.normal.opacity = 1;
                }
                for (var j = 0; j < seriesArr.length; j++) {
                    for (var k = 0; k < seriesArr[j].data.length; k++) {
                    }
                    if (j == params.seriesIndex) {
                        barIndex = new Array();
                        barIndex.push(j);
                        barIndex.push(params.dataIndex);
                        var data = $('#formSearch').serialize();
                        barIndex.push(data);
                        barCorlor = seriesArr[j].data[params.dataIndex].itemStyle.normal.color;
                        seriesArr[j].data[params.dataIndex].itemStyle.normal.color = highLightColor;
                        seriesArr[j].data[params.dataIndex].itemStyle.normal.opacity = 0.2;
                    }
                }
                myChart.setOption(option);
            },
            /**
             * 按照装置显示
             */
            initOperateShowShow: function (data) {
                if (unitChart && !unitChart.isDisposed()) {
                    unitChart.clear();
                    unitChart.dispose();
                }
                $("#totalCounts").text("0次");
                if (data == null || data == undefined || data.length == 0) {
                    unitChart = OPAL.ui.chart.initEmptyChart('unitChart');
                    return;
                } else {
                    if(data[0].xaxis == null || data[0].xaxis == undefined ||data[0].xaxis.length == 0) {
                        unitChart = OPAL.ui.chart.initEmptyChart('unitChart');
                        return;
                    }
                }
                
                unitChart = echarts.init(document.getElementById('unitChart'));
                var option = {
                    color: ['#3398DB'],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'line'
                        }
                    },
                    grid: {
                        left: '1%',
                        right: '3%',
                        bottom: '1%',
                        top: '16%',
                        height: '240px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        splitLine: {
                            show: true,
                            interval:0,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisTick: {
                            interval: 0,
                            alignWithLabel: true
                        }
                    }],
                    yAxis: [{
                        type: 'value',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                    }],
                    tooltip: {
                        trigger: 'item',
                        formatter: function (param) {
                            return option.series[param['seriesIndex']]['tooltip'][param['dataIndex']];
                        }
                    },
                    series: [{
                        name: '',
                        type: 'bar',
                        barMaxWidth: '15px',
                        itemStyle: {
                            normal: {
                                color: '#348fe2'
                            }
                        },
                        data: []
                    }],
                    tableData: [],
                    dataZoom: [{
                        type: 'slider',
                        yAxisIndex: 0,
                        filterMode: 'none',
                        width: 20
                    }]
                };
                var dataArray = new Array();
                var nameArray = new Array();
                var tipArray = new Array();
                var totalCounts = 0;
                if (data != null || data != undefined) {
                    if($("#checkShop").is(":checked")){
                        for (var i = 0; i < data.length; i++) {
                            totalCounts = totalCounts + parseFloat(data[i]["counts"][0]);
                            dataArray.push(data[i]["counts"][0]);
                            nameArray.push(data[i]["name"]);
                            tipArray.push(data[i]['tip']);
                        }
                    }else{
                        dataArray = data[0]["counts"];
                        nameArray = data[0]["xaxis"];
                        tipArray = data[0]["tip"];
                        for(var i=0; i< data[0]["counts"].length;i++){
                            totalCounts += parseFloat(data[0]["counts"][i]);
                        }
                    }
                }
                option.series[0].data = dataArray;
                option.xAxis[0].data = nameArray;
                option.series[0].tooltip = tipArray;
                unitChart.setOption(option);
                page.logic.singleChartClick(unitChart, option);

                if (option.tableData.length == 0 && data != undefined && data != null && data.length != 0 && data != '') {
                    option.tableData = data[0]['list'];
                }
                $("#totalCounts").text(totalCounts + "次");
            },
            singleChartClick: function (myChart, optionS) {
                // 点击图表 改变颜色
                myChart.on('click', function (params) {
                    var queryData;
                    var index;
                    var titleHTML = params.name;
                    $("#operNumSpan").html('（' + titleHTML + '）');
                    $("#AlarmTop20Span").html('（' + titleHTML + '）');
                    for (var i = 0; i < optionS.tableData.length; i++) {
                        if (params['name'] == optionS.tableData[i]['name']) {
                            queryData = optionS.tableData[i];
                            index = i;
                        }
                    }
                    page.logic.setNumberChart(params, queryData, false);
                    page.logic.singleChangChartColor(myChart, params, index, optionS);
                })
            },
            singleChangChartColor: function (myChart, params, i, optionS) {
                optionS.series[0].itemStyle = {
                    normal: {
                        color: function (params) {
                            if (params.dataIndex == i) {
                                return 'rgba(107,109,117,0.2)';
                            } else {
                                return "#348fe2";
                            }
                        }
                    }
                }
                myChart.setOption(optionS);
            },
            /**
             * 初始化操作/报警趋势图
             */
            initOperateNumberChart: function (data, index, tableSetData) {
                if (operNumChart && !operNumChart.isDisposed()) {
                    operNumChart.clear();
                    operNumChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    operNumChart = OPAL.ui.chart.initEmptyChart('ChartOperateNumber');
                    return;
                }
                operNumChart = echarts.init(document.getElementById('ChartOperateNumber'));
                var option = {
                    tooltip: {
                        position: ['30%', '20%'],
                        trigger: 'item',
                        axisPointer: { // 坐标轴指示器，坐标轴触发有效
                            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'

                        },
                        formatter: function (param) {
                            return option.series[param['seriesIndex']]['tooltip'][param['dataIndex']];
                        }
                    },
                    legend: {
                        itemWidth: 10, //设置icon大小
                        itemHeight: 10, //设置icon大小
                        top: 7,
                        left: 50,

                        data: [{
                            name: '操作数',
                            icon: 'bar',
                            textStyle: {
                                color: '#333',

                            }
                        }, {
                            name: '报警数',
                            icon: 'bar',
                            textStyle: {
                                color: '#333',

                            }
                        }]

                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        bottom: '1%',
                        top: '14%',
                        height: '220px',
                        containLabel: true
                    },
                    calculable: false,
                    xAxis: [{
                        type: 'category',
                        boundaryGap: false,
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },

                        axisLabel: {
                            interval: 0,
                            rotate: 55,
                            formatter: function (value, index) {
                                if (value.length > 10) {
                                    value = value.substring(11, 16);
                                }
                                if ($("#timeGranularity").val() == 'day' || $("#timeGranularity").val() == 'week') {
                                    value = value.substring(5, 11);
                                }
                                return value;
                            }
                        },
                        data: []
                    }],
                    yAxis: [{
                        type: 'value',
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                    }],
                    series: [

                        {
                            name: '报警数',
                            type: 'line',
                            //          smooth:true,
                            itemStyle: {
                                label: {
                                    show: false
                                },
                                normal: {
                                    color: '#489ae5',
                                    areaStyle: {
                                        color: '#FF5B57'
                                    }
                                }
                            },
                            markArea: {
                                silent: true,
                                data: [
                                    [{
                                        xAxis: ''
                                    }, {
                                        xAxis: ''
                                    }]
                                ]
                            },
                            data: [],
                        }, {
                            name: '操作数',
                            type: 'line',
                            smooth: true,
                            itemStyle: {
                                normal: {
                                    color: '#f6a630',
                                    areaStyle: {
                                        color: '#5BA5E8'
                                    }
                                }
                            },
                            data: []
                        }

                    ]
                };
                var timeGranularity = $("#timeGranularity").val(); //时间粒度
                if (option.xAxis[0].data.length == 0 && data != undefined && data[0] != undefined && data[0].xaxis != undefined) {
                    var dateArr = data[0].xaxis; //x轴
                    var lastDate = dateArr[dateArr.length - 1];
                    var nextDate; //x轴尾部补1个刻度值
                    switch (timeGranularity) {
                        case "month":
                            lastDate = lastDate + '-01';
                            nextDate = OPAL.util.strToDate(lastDate).dateAdd('m', 1);
                            nextDate = OPAL.util.dateFormat(nextDate, 'yyyy-MM');
                            break;
                        case "week":
                            nextDate = OPAL.util.strToDate(lastDate).dateAdd('w', 1);
                            nextDate = OPAL.util.dateFormat(nextDate, 'yyyy-MM-dd');
                            break;
                        case "day":
                            nextDate = OPAL.util.strToDate(lastDate).dateAdd('d', 1);
                            nextDate = OPAL.util.dateFormat(nextDate, 'yyyy-MM-dd');
                            break;
                        case "hour":
                            nextDate = OPAL.util.strToDate(lastDate).dateAdd('h', 1);
                            nextDate = OPAL.util.dateFormat(nextDate, 'yyyy-MM-dd HH:mm:ss');
                            break;
                    }
                    //data[0].xaxis.push(nextDate);
                    option.xAxis[0].data = data[0].xaxis;
                }
                if (data != null || data != undefined) {
                    if (tableSetData == undefined) {
                        if (index != null) {
                            var markAreaX1, markAreaX2; //markarea  范围坐标
                            markAreaX1 = xaxisArr[index];
                            switch (timeGranularity) {
                                case "month":
                                    markAreaX1 = markAreaX1 + '-01';
                                    markAreaX2 = OPAL.util.strToDate(markAreaX1).dateAdd('m', 1);
                                    markAreaX2 = OPAL.util.dateFormat(markAreaX2, 'yyyy-MM');
                                    break;
                                case "week":
                                    markAreaX2 = OPAL.util.strToDate(markAreaX1).dateAdd('w', 1);
                                    markAreaX2 = OPAL.util.dateFormat(markAreaX2, 'yyyy-MM-dd');
                                    break;
                                case "day":
                                    markAreaX2 = OPAL.util.strToDate(markAreaX1).dateAdd('d', 1);
                                    markAreaX2 = OPAL.util.dateFormat(markAreaX2, 'yyyy-MM-dd');
                                    break;
                                case "hour":
                                    markAreaX2 = OPAL.util.strToDate(markAreaX1).dateAdd('h', 1);
                                    markAreaX2 = OPAL.util.dateFormat(markAreaX2, 'yyyy-MM-dd HH:mm:ss');
                                    break;
                            }
                            markAreaX1 = xaxisArr[index];
                        } else if (index == null) {
                            markAreaX1 = '';
                            markAreaX2 = '';
                        }

                    }
                    for (var i = 0; i < data.length; i++) {
                        if (tableSetData == undefined) {
                            option.series.push({
                                name: data[i]['name'],
                                type: 'line',
                                smooth: true,
                                itemStyle: {
                                    normal: {
                                        color: data[i]['name'] == "操作数" ? '#f6a630' : '#489ae5',
                                        areaStyle: {
                                            color: data[i]['name'] == "操作数" ? '#f6a630' : '#489ae5'
                                        }
                                    }
                                },
                                data: data[i]['counts'],
                                tooltip: data[i]['tip'],
                                markArea: {
                                    silent: true,
                                    data: [
                                        [{
                                            xAxis: markAreaX1
                                        }, {
                                            xAxis: markAreaX2
                                        }]
                                    ],
                                    itemStyle: {
                                        normal: {
                                            color: highLightColor,
                                            borderColor: highLightColor,
                                            opacity: 0.15
                                        }
                                    }
                                },
                            });
                        } else if (tableSetData != undefined) {
                            var beginIndex; //markArea 开始索引(x轴值)
                            var endIndex; //markArea 结束索引(x轴值)

                            switch (timeGranularity) {
                                case "month":
                                    beginIndex = tableSetData['startTime'].substring(0, 7);
                                    endIndex = OPAL.util.strToDate(tableSetData['startTime']).dateAdd('m', 1);
                                    endIndex = OPAL.util.dateFormat(endIndex, 'yyyy-MM');
                                    break;
                                case "week":
                                    beginIndex = tableSetData['startTime'].substring(0, 10);
                                    endIndex = OPAL.util.strToDate(tableSetData['startTime']).dateAdd('w', 1);
                                    endIndex = OPAL.util.dateFormat(endIndex, 'yyyy-MM-dd');
                                    break;
                                case "day":
                                    beginIndex = tableSetData['startTime'].substring(0, 10);
                                    endIndex = OPAL.util.strToDate(tableSetData['startTime']).dateAdd('d', 1);
                                    endIndex = OPAL.util.dateFormat(endIndex, 'yyyy-MM-dd');
                                    break;
                                case "hour":
                                    beginIndex = tableSetData['startTime'];
                                    endIndex = OPAL.util.strToDate(tableSetData['startTime']).dateAdd('h', 1);
                                    endIndex = OPAL.util.dateFormat(endIndex, 'yyyy-MM-dd HH:mm:ss');
                                    break;
                            }
                            option.series.push({
                                name: data[i]['name'],
                                type: 'line',
                                smooth: true,
                                itemStyle: {
                                    normal: {
                                        color: data[i]['name'] == "操作数" ? '#f6a630' : '#489ae5',
                                        areaStyle: {
                                            color: data[i]['name'] == "操作数" ? '#f6a630' : '#489ae5'
                                        }
                                    }
                                },
                                data: data[i]['counts'],
                                tooltip: data[i]['tip'],
                                markArea: {
                                    silent: true,
                                    data: [
                                        [{
                                            xAxis: beginIndex
                                        }, {
                                            xAxis: endIndex
                                        }]
                                    ],
                                    itemStyle: {
                                        normal: {
                                            color: highLightColor,
                                            borderColor: highLightColor,
                                            opacity: 0.15
                                        }
                                    }
                                },
                            });
                        }

                    }
                }
                operNumChart.setOption(option);
            },
            /**
             * 初始化最频繁操作报警数Top20
             * @param data
             */
            initMostFrequentNumberChart: function (data) {
                if (mostFrequentNumberChart && !mostFrequentNumberChart.isDisposed()) {
                    mostFrequentNumberChart.clear();
                    mostFrequentNumberChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    mostFrequentNumberChart = OPAL.ui.chart.initEmptyChart('mostFrequentNumber');
                    return;
                }
                mostFrequentNumberChart = echarts.init(document.getElementById('mostFrequentNumber'));
                var option = {
                    grid: {
                        left: '1%',
                        right: '1%',
                        bottom: '1%',
                        top: '15%',
                        height: '220px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        axisTick: {
                            alignWithLabel: true
                        },
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 0,
                            rotate: 40
                        },
                        flagName: []
                    }],
                    yAxis: [{
                        type: 'value',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        }

                    }],
                    tooltip: {
                        position: ['30%', '20%'],
                        color: ['#3398DB'],
                        trigger: 'axis',
                        axisPointer: {
                            type: ''
                        },
                        formatter: function (params, index) {
                            if($("#checkTeam").is(":checked")){
                                return option.series[0]['tooltip'][params[0]['dataIndex']];
                            } else {
                                var dataIndex = params[0].dataIndex;
                                var flag = option.xAxis[0].flagName[dataIndex];
                                var str = "位号:" + params[0]['name'] + "&nbsp;&nbsp;报警标识:" + flag + "<br>报警数:" + params[0]['data'];
                                return str;
                            }    
                        }
                    },
                    series: [{
                        type: 'bar',
                        barMaxWidth: '15',
                        itemStyle: {
                            normal: {
                                color: '#348fe2'
                            }
                        },
                        data: []
                    }]
                };
                var totalArray = new Array();
                var tagArray = new Array();
                var flagArray = new Array();
                var flagIdArray = new Array();
                if($("#checkTeam").is(":checked")){
                    var len = data.length < topCount ? data.length :topCount;
                    var tooltipArr = [];
                    for (var i = 0; i < len; i++) {
                        var shiftDate = OPAL.util.dateFormat(new Date(data[i].shiftDate),"yyyy-MM-dd");
                        var startTime = OPAL.util.dateFormat(new Date(data[i].startTime),"yyyy-MM-dd HH:mm:ss");
                        var endTime = OPAL.util.dateFormat(new Date(data[i].endTime),"yyyy-MM-dd HH:mm:ss");

                        totalArray.push(data[i]["shiftSortNum"]);
                        tagArray.push(shiftDate+data[i].shiftSName);
                        tooltipArr.push("从"+startTime+"至"+endTime+"<br>"+data[i].workTeamSName+":"+data[i].shiftSortNum);
                    }
                    option.xAxis[0].axisLabel.rotate=60;
                    option.series[0].tooltip = tooltipArr;
                } else {
                    if (data != null || data != undefined) {
                        for (var i = 0; i < data.length; i++) {
                            totalArray.push(data[i]["alarmCount"]);
                            tagArray.push(data[i]['tag']);
                            flagArray.push(data[i]['alarmFlag']);
                            flagIdArray.push(data[i]['alarmFlagId']);
                        }
                        option.xAxis[0].flagName = flagArray;   
                    }
                    
                    /**
                     * 最频繁的报警详情信息
                     */
                    mostFrequentNumberChart.on("click", function (params) {
                        layer.open({
                            type: 2,
                            title: '',
                            closeBtn: 0,
                            area: ['90%', '75%'],
                            shadeClose: false,
                            content: 'AlarmDetail.html?' + Math.random(),
                            success: function (layero, index) {
                                var body = layer.getChildFrame('body', index);
                                var iframeWin = window[layero.find('iframe')[0]['name']];
                                var data = {
                                    "alarmPointTag": tagArray[params.dataIndex],
                                    "alarmFlagId": flagIdArray[params.dataIndex],
                                    'startTime': $("#AlarmTop20Span").html() == '' ? $("#startTime").val() : dateArray.startTime,
                                    'endTime': $("#AlarmTop20Span").html() == '' ? $("#endTime").val() : dateArray.endTime,
                                };
                                iframeWin.page.logic.setData(data);
                            }
                        });
                    })
                }
                option.xAxis[0].data = tagArray;
                option.series[0].data = totalArray;
                mostFrequentNumberChart.setOption(option);
            },
            /**
             * 初始化表格
             */
            initTable: function () {
                //最频繁报警数量表格
                $('#tableTop20').bootstrapTable({
                    columns:[{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        field: 'tag',
                        title: '位号',
                        width: '140px',
                        align: 'left'
                    }, {
                        field: 'alarmFlag',
                        title: '报警标识',
                        width: '80px',
                        align: 'center'
                    }, {
                        field: 'alarmCount',
                        title: '报警数',
                        width: '80px',
                        align: 'right'
                    }, {
                        field: 'percent',
                        title: '百分比（%）',
                        width: '80px',
                        align: 'right'
                    }, {
                        field: 'priorityName',
                        title: '优先级',
                        width: '60px',
                        align: 'center'
                    }, {
                        field: 'unitName',
                        title: '装置',
                        width: '140px',
                        align: 'left'
                    }, {
                        field: 'prdtCellName',
                        title: '生产单元',
                        width: '140px',
                        align: 'left'
                    }, {
                        field: 'location',
                        title: '位置描述',
                        width: '200px',
                        align: 'left'
                    }],
                    url: '',
                    pagination: true,
                    pageSize: 5,
                    pageList: [5, 10, 20, 50, 100],
                    paginationPreText: '<',
                    paginationNextText: '>',
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    showColumns: false,
                    onLoadSuccess: function (data) {
                        //设置鼠标浮动提示
                        var tds = $('#tableTop20').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                });

                $('#tableTop20').colResizable({
                    liveDrag: true
                });

                //装置列表
                $('#tableNumber').bootstrapTable({
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'alarmTime',
                        title: '时间',
                        align: 'center',
                        // 'data-resizable':'true',
                        width: '200px'
                    }, {
                        field: 'name',
                        title: '名称',
                        width: '200px'
                    }, {
                        field: 'endTime',
                        title: '结束时间',
                        visible: false,
                        width: '200px'
                    }, {
                        field: 'alarmCount',
                        title: '报警数',
                        align: 'right',
                        width: '200px',
                        formatter: function (value, row, index) {
                            return "<a style='text-decoration: underline;color: #348fe2;cursor: pointer;' onclick='javascript:page.logic.detail(\"" + row.id + "\",\"" + row.alarmTime + "\",\"" + row.endTime + "\")'>" + value + "</a>";
                        }
                    }],
                    queryParamsType: "undefined",
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    sidePagination: "client",
                    cache: false,
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    onClickRow: function (row) {
                        page.logic.setTable(row);
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#tableNumber').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                });
                $('#tableNumber').colResizable({
                    liveDrag: true
                });

                //报警/操作表格
                $('#tableOperateNumber').bootstrapTable({
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'timeStr',
                        title: '时间',
                        align: 'center'
                    }, {
                        field: 'operationCount',
                        title: '操作数',
                        align: 'right'
                    }, {
                        field: 'count',
                        title: '报警数',
                        align: 'right'
                    }],
                    queryParamsType: "undefined",
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    sidePagination: "client",
                    cache: false,
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    striped: true
                });
                $('#tableOperateNumber').colResizable({
                    liveDrag: true
                });

            },
            setTable: function (row) {
                var tableOption = $('#tableNumber').bootstrapTable('getOptions');
                var rowData = row;
                tableOption.rowStyle = function (row, index) {
                    var style = "";
                    if (rowData.rowIndex == row.rowIndex) {
                        style = 'selectedd';
                    }
                    return {
                        classes: style
                    }
                }
                $("#tableNumber").bootstrapTable('refreshOptions', tableOption);

                $("#operNumSpan").html('（' + row.name + '）');
                $("#AlarmTop20Span").html('（' + row.name + '）');
                var searchType = $("#checkShop").is(':checked') == true ? 1 : searchType = $("#prdtIds").val() == '' ? 2 : 3;
                var data = {
                    startTime: $("#startTime").val(),
                    endTime: $("#endTime").val(),
                    timeGranularity: $("#timeGranularity").val(),
                    id: row.id,
                    searchType: searchType
                };
                var data1 = {
                    startTime: row.alarmTime,
                    endTime: row.endTime
                };
                dateArray = data1;
                $.ajax({
                    url: operateNumberSingleUrl,
                    async: true,
                    data: data,
                    dataType: 'json',
                    type: 'get',
                    success: function (data) {
                        page.logic.initOperateNumberTable(data);
                        page.logic.initOperateNumberChart(data, null, data1);
                    },
                    error: function (data) {
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }

                });
                page.logic.queryChartClickData($.extend(data, data1));
            },
            /**
             * 初始化报警数列表
             */
            initNumberTable: function (data) {
                $("#tableNumber").bootstrapTable("removeAll");
                if (data == null || data == undefined || data.length == 0) {
                    return;
                }
                if(!$("#checkTeam").is(":checked")){
                    for (var i = 0; i < data[0].list.length; i++) {
                        data[0].list[i].rowIndex = i;
                    }
                    $("#tableNumber").bootstrapTable("load", data[0].list);
                    //$("#tableNumber").bootstrapTable("refresh");
                    $('#tableNumber').bootstrapTable('refreshOptions', {
                        columns: [{
                            title: "序号",
                            formatter: function (value, row, index) {
                                return index + 1;
                            },
                            rowspan: 1,
                            align: 'center',
                            width: '80px'
                        }, {
                            title: "index",
                            field: "index",
                            visible: false
                        }, {
                            field: 'alarmTime',
                            title: '时间',
                            align: 'center'
                        }, {
                            field: 'name',
                            title: displayName == "单元" ? "生产单元" : displayName,
                        }, {
                            field: 'endTime',
                            title: '结束时间',
                            visible: false

                        }, {
                            field: 'alarmCount',
                            title: '报警数',
                            align: 'right',
                            formatter: function (value, row, index) {
                                return "<a style='text-decoration: underline;color: #348fe2;cursor: pointer;' onclick='javascript:page.logic.detail(\"" + row.id + "\",\"" + row.alarmTime + "\",\"" + row.endTime + "\")'>" + value + "</a>";
                            }
                        }],
                        queryParamsType: "undefined",
                        formatNoMatches: function () {
                            return "";
                        },
                        formatLoadingMessage: function () {
                            return "";
                        },
                        sidePagination: "client",
                        cache: false,
                        pagination: true,
                        pageSize: 5,
                        paginationPreText: '<',
                        paginationNextText: '>',
                        showColumns: false,
                        pageList: [5, 10, 20, 50, 100],
                        onClickRow: function (row) {
                            page.logic.setTable(row);
                        }
                    });
                } else {
                    alarmNumberDetailData = data[0].groupworkTeamList;//二级列表数据
                    $("#tableNumber").bootstrapTable("load", data[0].list);
                    $('#tableNumber').bootstrapTable('refreshOptions', {
                        columns: [{
                            field: 'name',
                            title: "装置",
                            align: 'left'
                        },{
                            field: 'workTeamName',
                            title: "班组",
                            align: 'center'
                        }, {
                            field: 'alarmTime',
                            title: '开始日期',
                            align: 'center'
                        }, {
                            field: 'endTime',
                            title: '结束日期',
                            align: 'center'
                        }, {
                            field: 'alarmCount',
                            title: '报警数',
                            align: 'right'
                        }],
                        queryParamsType: "undefined",
                        formatNoMatches: function () {
                            return "";
                        },
                        formatLoadingMessage: function () {
                            return "";
                        },
                        sidePagination: "client",
                        detailView: true,
                        cache: false,
                        pagination: true,
                        pageSize: 5,
                        paginationPreText: '<',
                        paginationNextText: '>',
                        showColumns: false,
                        pageList: [5, 10, 20, 50, 100],
                        onClickRow: function (row) {
                        },
                        onExpandRow: function(index, row, $detail) {
                            page.logic.initAlarmNumberDetail(index, row, $detail);
                        },
                    });
                }
                
                
                var tds = $('#tableNumber').find('tbody tr td');
                $.each(tds, function (i, el) {
                    $(this).attr("title", $(this).text());
                })
            },
            initAlarmNumberDetail:function(index, row, $detail) {
                var subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                $("#"+subId).bootstrapTable({
                    columns: [{
                        field: 'shiftDate',
                        title: "日期",
                        align: 'center',
                        formatter:function(value, index){
                            return OPAL.util.dateFormat(new Date(value),"yyyy-MM-dd");
                        }
                    }, {
                        field: 'shiftSName',
                        title: "班次",
                        align: 'center'
                    }, {
                        field: 'workTeamSName',
                        title: '班组',
                        align: 'center'
                    }, {
                        field: 'shiftSortNum',
                        title: '报警数',
                        align: 'right'
                    }],
                    url: '',
                    cache: false,
                    pagination: true, //启动分页
                    pageSize: 10, // 每页显示的记录数
                    pageNumber: 1, // 当前第几页
                    pageList: [10, 20, 50, 100], // 记录数可选列表
                    paginationPreText: '<', //上下翻页
                    paginationNextText: '>',
                    sidePagination: "client", // 表示服务端请求 后台分页
                    //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder  
                    //设置为limit可以获取limit, offset, search, sort, order  
                    queryParamsType: "undefined",
                    formatNoMatches: function () {
                        return "";
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#' + tableID).find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                })
                var loadData=[];
                for(x in alarmNumberDetailData){
                    if(x == row.workTeamName){
                        loadData = alarmNumberDetailData[x];
                    }
                }
                $("#"+subId).bootstrapTable("load", loadData);
            },
            /**
             * 初始化操作报警数表格
             */
            initOperateNumberTable: function (data) {
                $("#tableOperateNumber").bootstrapTable("removeAll");
                if (data == null || data == undefined || data.length == 0) {
                    return;
                }
                $("#tableOperateNumber").bootstrapTable("load", data[0].list);
                $("#tableOperateNumber").bootstrapTable("refresh");
                var tds = $('#tableOperateNumber').find('tbody tr td');
                $.each(tds, function (i, el) {
                    $(this).attr("title", $(this).text());
                })
            },
            /**
             * 初始化最频繁数量Top20 列表
             */
            initTop20Table: function (data) {
                var data =JSON.parse(JSON.stringify(data));
                $("#tableTop20").bootstrapTable("removeAll");
                if (data == null || data == undefined || data.length == 0) {
                    return;
                }
                var columns = [];
                if($("#checkTeam").is(":checked")){
                    columns = [{
                        field: 'shiftDate',
                        title: '日期',
                        width: '100px',
                        align: 'center',
                        formatter:function(value, row, index) {
                            return OPAL.util.dateFormat(new Date(value),"yyyy-MM-dd");
                        }
                    }, {
                        field: 'shiftSName',
                        title: '班次',
                        width: '100px',
                        align: 'center'
                    }, {
                        field: 'workTeamSName',
                        title: '班组',
                        width: '80px',
                        align: 'center'
                    }, {
                        field: 'shiftSortNum',
                        title: '报警数',
                        width: '80px',
                        align: 'right'
                    }]
                } else {
                    columns = [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '60px'
                    }, {
                        field: 'tag',
                        title: '位号',
                        width: '120px',
                        align: 'left'
                    }, {
                        field: 'alarmFlag',
                        title: '报警标识',
                        width: '80px',
                        align: 'center'
                    }, {
                        field: 'alarmCount',
                        title: '报警数',
                        width: '60px',
                        align: 'right'
                    }, {
                        field: 'percent',
                        title: '百分比（%）',
                        width: '90px',
                        align: 'right'
                    }, {
                        field: 'priorityName',
                        title: '优先级',
                        width: '60px',
                        align: 'center'
                    }, {
                        field: 'unitName',
                        title: '装置',
                        width: '140px',
                        align: 'left'
                    }, {
                        field: 'prdtCellName',
                        title: '生产单元',
                        width: '140px',
                        align: 'left'
                    }, {
                        field: 'location',
                        title: '位置描述',
                        width: '220px',
                        align: 'left'
                    }]
                }
                $('#tableTop20').bootstrapTable("refreshOptions",{
                    columns:columns,
                    // url: '',
                    pagination: true,
                    pageSize: 5,
                    pageList: [5, 10, 20, 50, 100],
                    paginationPreText: '<',
                    paginationNextText: '>',
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    showColumns: false,
                    onLoadSuccess: function (data) {
                        //设置鼠标浮动提示
                        var tds = $('#tableTop20').find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                });
                var loadData = data.length<topCount?data:data.slice(0,topCount);
                $("#tableTop20").bootstrapTable("load", loadData);
                $("#tableTop20").bootstrapTable("refresh");
                var tds = $('#tableTop20').find('tbody tr td');
                $.each(tds, function (i, el) {
                    $(this).attr("title", $(this).text());
                })
            },
            /**
             * 切换显示名称
             */
            toggleDisplayName: function () {
                $("#displayName").text("按" + displayName + "显示");
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                var checkTeamFlag = $("#checkTeam").is(":checked");//是否选班组
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function () {
                        var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds',checkTeamFlag?true:false);
                        if (unitIds != undefined && unitIds.length == 1) {
                            $("#prdtIds").combo('enable');
                            $("#prdtIds").combotree('setValues', []);
                            if(!$("#checkTeam").is(":checked"))
                            page.logic.searchUnitPrdt(unitIds[0]);
                            $('.textbox,.combo').css('background-color', '');
                            if($("#checkTeam").is(":checked"))
                            page.logic.initWorkTeam();
                        } else {
                            $("#prdtIds").combotree('setValues', []);
                            $("#prdtIds").combo('disable');
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    },
                    multiple: checkTeamFlag?false:true,
                    onlyLeafCheck:checkTeamFlag ?true:false
                }, false);
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect('prdtIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function() {
                    var treeView = $("#prdtIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtIds").combotree("checkAllNodes");
                });
            },
            /**
              * 初始化班组选择
              */
             initWorkTeam: function() {
                var checkTeamFlag = $("#checkTeam").is(":checked");//是否选班组
                if(!checkTeamFlag) return;
                 var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds',true);
                 if (unitIds.length != 1) return;
                 if ($("#startTime").val() == '' || $("#endTime").val() == '') {
                     $('#team').html("");
                     $("#team").prop('disabled', true);
                     return;
                 } else {
                     $("#team").prop('disabled', false);
                 }
                 OPAL.ui.getComboMultipleSelect('team', workTeamUrl, {
                    keyField: "workTeamId",
                    valueField: "workTeamSName",
                    data: {
                        "startTime": $("#startTime").val() +" 00:00:00",
                         "endTime": $("#endTime").val() + " 23:59:59",
                         "unitId": unitIds[0]
                    },
                    async:false
                }, true, function() {
                    var treeView = $("#team").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#team").combotree("checkAllNodes");
                });

             },
            /**
             * 报警数详情
             */
            detail: function (id, alarmTime, endTime) {
                var searchType = $("#checkShop").is(':checked') == true ? 1 : searchType = $("#prdtIds").val() == '' ? 2 : 3;
                layer.open({
                    type: 2,
                    title: '报警数详情',
                    closeBtn: 1,
                    area: ['85%', '420px'],
                    shadeClose: false,
                    content: 'Detail.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "id": id,
                            "searchType": searchType,
                            'startTime': alarmTime,
                            'endTime': endTime
                        };
                        iframeWin.page.logic.setData(data);
                    }
                });
            },
            /**
             * 切换工厂 按车间显示
             *
             */
            showPrdtCellSelect: function () {
                var startTime = OPAL.util.strToDate($("#startTime").val());
                var endTime = OPAL.util.strToDate($("#endTime").val());
                var timeMiss = (endTime - startTime) / (1000 * 60 * 60 * 24);
                if ($("#checkShop").is(':checked')) {
                    $("#prdtIdsSpan").hide();
                    $("#prdtIds").hide();
                    $("#prdtSpan").hide();
                    $("#unitSpan").html('车间：');
                    $("#prdtIds").combo('disable');//生产单元不可用
                    $("#prdtIds").combo('setValues',[]);//生产单元选中值清空
                    $("#prdtIds").combo('clear');//生产单元选项清空
                    $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');//样式置灰（不可用）
                    $("#checkTeam").attr("checked",false);
                    $("#teamDiv").hide();
                    page.logic.initUnitTree();
                    $("#unitIds").combotree('toggleDisplayModel', true);
                    if(timeMiss == 0) {
                        $("#timeGranularity").empty();
                        $("#timeGranularity").html('<option value="hour">小时</option><option value="day">天</option>');
                    }
                    $("#unitLi").show();
                    $("#exportOftenBtn").empty().removeClass("cursor_pointer");
                    $("#expBtn").empty().removeClass("cursor_pointer");
                } else {
                    $("#prdtIdsSpan").show();
                    $("#prdtSpan").show();
                    $("#unitSpan").html('装置：');
                    page.logic.initUnitTree();
                    $("#unitIds").combotree('toggleDisplayModel', false);
                    let className1 = $('.tab-con-1 .active a .alarm-number-span-dis').html();
                    let className3 = $('.tab-con-3 .active a .alarm-number-span-dis').html();
                    if (className1 != '列表显示') {
                        $("#expBtn").empty().removeClass("cursor_pointer");
                    }
                    if (className3 != '列表显示') {
                        $("#exportOftenBtn").empty().removeClass("cursor_pointer");
                    }
                }
            },
            //按班组显示
            showTeamSelect: function () {
                var startTime = OPAL.util.strToDate($("#startTime").val());
                var endTime = OPAL.util.strToDate($("#endTime").val());
                var timeMiss = (endTime - startTime) / (1000 * 60 * 60 * 24);
                if($("#checkTeam").is(":checked")){
                    $("#teamDiv").show(); //显示班组组件
                    $("#checkShop").attr("checked",false);//按车间显示按钮去掉
                    $("#prdtIdsSpan").hide();//生产单元组件隐藏
                    $("#prdtIds").hide();//生产单元组件隐藏
                    $("#prdtSpan").hide();//生产单元组件隐藏
                    $("#prdtIds").combo('disable');//生产单元不可用
                    $("#prdtIds").combo('setValues',[]);//生产单元选中值清空
                    $("#prdtIds").combo('clear');//生产单元选项清空
                    $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');//样式置灰（不可用）
                    $("#unitSpan").html('装置：');//装置和车间共用一个组件  名称替换成装置
                    $("#expBtn").empty().removeClass("cursor_pointer"); //去掉导出按钮
                    page.logic.initUnitTree();
                    $("#unitIds").combotree('toggleDisplayModel', false);//扩展方法  切换模式 true:显示车间;false:显示装置
                    if(timeMiss == 0){
                        $("#timeGranularity").empty();
                        $("#timeGranularity").html('<option value="day">天</option>');
                    }
                    $("#unitLi").hide();
                    $("#exportOftenBtn").empty().removeClass("cursor_pointer");
                    $("#expBtn").empty().removeClass("cursor_pointer");
                } else {
                    $("#teamDiv").hide();
                    $("#prdtIdsSpan").show();
                    $("#prdtSpan").show();
                    page.logic.initUnitTree();
                    if(timeMiss == 0) {
                        $("#timeGranularity").empty();
                        $("#timeGranularity").html('<option value="hour">小时</option><option value="day">天</option>');
                    }
                    $("#unitLi").show();
                    let className1 = $('.tab-con-1 .active a .alarm-number-span-dis').html();
                    let className3 = $('.tab-con-3 .active a .alarm-number-span-dis').html();
                    if (className1 != '列表显示') {
                        $("#expBtn").empty().removeClass("cursor_pointer");
                    }
                    if (className3 != '列表显示') {
                        $("#exportOftenBtn").empty().removeClass("cursor_pointer");
                    }
                }
            }
        }
    };
    page.init();
    window.page = page;
});

