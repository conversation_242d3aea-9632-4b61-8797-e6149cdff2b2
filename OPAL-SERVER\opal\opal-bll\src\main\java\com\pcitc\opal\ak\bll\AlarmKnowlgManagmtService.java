package com.pcitc.opal.ak.bll;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ak.bll.entity.AlarmKnowlgExcyDetailEntity;
import com.pcitc.opal.ak.bll.entity.AlarmKnowlgManagmtEntity;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import org.springframework.stereotype.Service;

import java.util.Date;

/*
 * 报警知识管理业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmKnowlgManagmtService
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理业务逻辑层接口
 */
@Service
public interface AlarmKnowlgManagmtService {

    /**
     * 加载报警知识维护主数据
     *
     * <AUTHOR> 2018-03-09
     * @param unitCodes		    装置编码数组
     * @param prdtCellIds	    生产单元ID数组
     * @param alarmPointTag	报警点位号
     * @param alarmFlagId	    报警标识ID
     * @param startTime		发生时间范围起始
     * @param endTime		    发生时间范围结束
     * @param page			    分页参数
     * @return 报警知识数据
     * @throws Exception
     */
    PaginationBean<AlarmEventEntity> getAlarmEvent(String[] unitCodes, Long[] prdtCellIds, String alarmPointTag,
                                                   Long alarmFlagId, Date startTime, Date endTime, Pagination page) throws Exception;

    /**
     * 新增报警知识维护数据
     *
     * <AUTHOR> 2018-03-09
     * @param akmEntity 报警知识实体
     * @throws Exception
     * @return CommonResult 消息结果类
     */
    CommonResult addAlarmKnowlgManagmt(AlarmKnowlgManagmtEntity akmEntity) throws Exception;

    /**
     * 加载报警知识维护详情数据
     *
     * @param eventId       报警事件id
     * @param alarmPointId  报警点id
     * @param alarmFlagId   报警标识id
     * @return 报警知识维护数据
     * @throws Exception
     * <AUTHOR> 2018-03-09
     */
    AlarmKnowlgExcyDetailEntity getAlarmKnowlgExcyDetail(Long eventId, Long alarmPointId, Long alarmFlagId) throws Exception;

    /**
     * 加载报警知识维护主数据
     *
     * @param eventId       报警事件id
     * @param alarmPointId  报警点id
     * @param alarmFlagId   报警标识id
     * @param page   分页信息
     * @return 报警知识维护数据集合
     * @throws Exception
     * <AUTHOR> 2018-03-09
     */
    PaginationBean<AlarmKnowlgManagmtEntity> getAlarmKnowlgManagmt(Long eventId,Long alarmPointId,Long alarmFlagId, Pagination page) throws Exception;
}
