package com.pcitc.opal.pm.dao;

import com.pcitc.opal.pm.pojo.Company;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * Company实体的Repository的JPA标准接口
 * 模块编号：pcitc_opal_dal_interface_CompanyRepository
 * 作       者：shufei.sui
 * 创建时间：2021/02/22
 * 修改编号：1
 * 描       述：Company实体的Repository实现
 */
public interface CompanyRepository extends JpaRepository<Company, Long>, CompanyRepositoryCustom {
}
