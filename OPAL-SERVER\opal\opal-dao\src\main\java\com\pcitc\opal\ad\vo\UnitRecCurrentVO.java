package com.pcitc.opal.ad.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UnitRecCurrentVO implements Serializable {

    /**
     * 报警时间
     */
    private Date alarmTime;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 优先级名
     */
    private String priorityStr;
    /**
     * 装置编码
     */
    private String unitCode;
    /**
     * 装置名称
     */
    private String unitName;

}
