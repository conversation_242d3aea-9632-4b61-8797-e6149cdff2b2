package com.pcitc.opal.common.bll.entity;

/*
 * 装置实体
 * 模块编号：pcitc_opal_bll_class_UnitEntity
 * 作    者：xuelei.wang
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描   述：装置实体
 */
public class UnitEntity extends BasicEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 简称
     */
    private String sname;

    /**
     * 排序
     */
    private Integer sortNum;

    /**
     * 描述
     */
    private String des;
    /**
     * 编码
     */
    private String stdCode;
    /**
     * 车间编码
     */
    private String workshopCode;
    /**
     * 车间名称
     */
    private String workshopName;
    /**
     * 车间简称称
     */
    private String workshopSname;

    public String getWorkshopSname() {
        return workshopSname;
    }

    public void setWorkshopSname(String workshopSname) {
        this.workshopSname = workshopSname;
    }

    public String getWorkshopCode() {
        return workshopCode;
    }

    public void setWorkshopCode(String workshopCode) {
        this.workshopCode = workshopCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSname() {
        return sname;
    }

    public void setSname(String sname) {
        this.sname = sname;
    }

    public String getStdCode() {
        return stdCode;
    }

    public void setStdCode(String stdCode) {
        this.stdCode = stdCode;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getWorkshopName() {
        return workshopName;
    }

    public void setWorkshopName(String workshopName) {
        this.workshopName = workshopName;
    }
}