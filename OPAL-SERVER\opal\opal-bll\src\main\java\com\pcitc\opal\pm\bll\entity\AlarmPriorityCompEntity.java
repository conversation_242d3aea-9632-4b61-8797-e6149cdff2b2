package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 报警优先级对照实体
 * 模块编号：pcitc_opal_bll_class_AlarmPriorityCompEntity
 * 作       者：zheng.yang
 * 创建时间：2017/03/30
 * 修改编号：1
 * 描       述：报警优先级对照实体
 */
public class AlarmPriorityCompEntity extends BasicEntity {
    /**
     * 报警优先级对照ID
     */
    private Long alarmPriorityCompId;

    /**
     * DCS编码ID
     */
    private Long dcsCodeId;

    /**
     * 源报警优先级
     */
    private String prioritySource;

    /**
     * 本系统优先级(1紧急；2重要；3一般)
     */
    private Integer priority;

    /**
     * 本系统优先级名称
     */
    private String priorityName;

    /**
     * dcs编码表
     */
    private String dcsName;

    public Long getAlarmPriorityCompId() {
        return alarmPriorityCompId;
    }

    public void setAlarmPriorityCompId(Long alarmPriorityCompId) {
        this.alarmPriorityCompId = alarmPriorityCompId;
    }

    public Long getDcsCodeId() {
        return dcsCodeId;
    }

    public void setDcsCodeId(Long dcsCodeId) {
        this.dcsCodeId = dcsCodeId;
    }

    public String getPrioritySource() {
        return prioritySource;
    }

    public void setPrioritySource(String prioritySource) {
        this.prioritySource = prioritySource;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getDcsName() {
        return dcsName;
    }

    public void setDcsName(String dcsName) {
        this.dcsName = dcsName;
    }

    public String getPriorityName() {
        return priorityName;
    }

    public void setPriorityName(String priorityName) {
        this.priorityName = priorityName;
    }
}
