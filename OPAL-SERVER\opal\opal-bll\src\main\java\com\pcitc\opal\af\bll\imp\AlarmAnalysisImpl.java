package com.pcitc.opal.af.bll.imp;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pcitc.opal.aa.bll.entity.AlarmAnalysisOperateEntity;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.AlarmFlagRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.ad.pojo.AlarmFlag;
import com.pcitc.opal.af.bll.AlarmAnalysisService;
import com.pcitc.opal.af.bll.entity.AlarmDetailEntity;
import com.pcitc.opal.af.bll.entity.OperateDetailEntity;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.DateHelper;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import com.pcitc.opal.pm.dao.AlarmPointRepository;
import com.pcitc.opal.pm.pojo.AlarmPoint;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import pcitc.imp.common.ettool.utils.ObjectConverter;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;


/*
 * 报警分析业务实现类
 * 模块编号：pcitc_opal_bll_class_bll
 * 作    者：xuelei.wang
 * 创建时间：2017/10/30
 * 修改编号：1
 * 描    述：报警分析业务实现类
 */

@Service
@CacheConfig(cacheNames = "AlarmAnalysis")
@SuppressWarnings({"unused","unchecked","rawtypes"})
public class AlarmAnalysisImpl implements AlarmAnalysisService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private AlarmEventRepository repository;

    @Autowired
    private AlarmPointRepository alarmPointRepository;

    @Autowired
    private AlarmFlagRepository alarmFlagRepository;

    @Autowired
    private BasicDataService basicDataService;

    @Autowired
    private ShiftService shiftService;
    /**
     * 查看报警分析-操作详情
     *
     * <AUTHOR> 2017-10-30
     * @param dateJason    日期集合
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @return 操作详情数据
     */
	@Override
    public OperateDetailEntity getOperateDetail(String dateJason, Long alarmPointId, Long alarmFlagId) throws IOException {
        //region 解析Jason字符串
        ObjectMapper objectMapper = new ObjectMapper();
        List<DictionaryEntity> dateList = objectMapper.readValue(dateJason, new TypeReference<List<DictionaryEntity>>() {
        });

        Date startTime = new Date((Long) dateList.get(0).getValue());
        Date endTime = new Date((Long) dateList.get(1).getValue());
        Date newEndTime = endTime;
        String endFlag = String.valueOf(dateList.get(5).getValue());
        if (endFlag.equals("<")) {
            newEndTime = DateUtils.addSeconds(endTime, -1);
        }
        //endregion

        AlarmFlag alarmFlag = alarmFlagRepository.getSingleAlarmFlag(alarmFlagId);
        AlarmPoint alarmPoint = alarmPointRepository.getSingleAlarmPoint(alarmPointId);

        String hourTime = " " + basicDataService.getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();
		List alarmEventObjList = repository.getChartOperateDetail(startTime, newEndTime, alarmPointId, alarmFlagId, hourTime);
        List<AlarmAnalysisOperateEntity> alarmEventList = new ArrayList<>();

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ParsePosition pos = new ParsePosition(8);
        alarmEventObjList.forEach(x -> {
            AlarmAnalysisOperateEntity aaoEntity = new AlarmAnalysisOperateEntity(
                    Long.valueOf(((Object[]) x)[0].toString()),
                    Long.valueOf(((Object[]) x)[1].toString()),
                    Long.valueOf(((Object[]) x)[2].toString()),
                    DateHelper.parseDate(((Object[]) x)[3].toString())
            );
            alarmEventList.add(aaoEntity);
        });

        List<String> positionDateList = getVariationTrendDate(startTime, endTime);
        List<Long> alarmTimes = getChartData(alarmEventList, positionDateList);

        OperateDetailEntity operateDetailEntity = new OperateDetailEntity();
        operateDetailEntity.setAlarmFlagName(alarmFlag.getName());
        operateDetailEntity.setLocation(alarmPoint.getLocation());
        operateDetailEntity.setPrdtCellSname(alarmPoint.getPrdtCell().getSname());
        operateDetailEntity.setTag(alarmPoint.getTag());
        operateDetailEntity.setOperatorTimes(alarmTimes);
        operateDetailEntity.setPositionDate(positionDateList);
        return operateDetailEntity;
    }


    /**
     * 查看报警分析-报警详情
     *
     * <AUTHOR> 2017-11-08
     * @param dateJason    日期集合
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @return 报警详情数据
     */
    public AlarmDetailEntity getAlarmDetail(String dateJason, Long alarmPointId, Long alarmFlagId) throws Exception {
        AlarmDetailEntity alarmDetailEntity = new AlarmDetailEntity();
        //现柱状图数据
        List nowHistogramData = new ArrayList();

        //region 解析Jason字符串
        ObjectMapper objectMapper = new ObjectMapper();
        List<DictionaryEntity> dateList = objectMapper.readValue(dateJason, new TypeReference<List<DictionaryEntity>>() {
        });

        Date startTime = new Date((Long) dateList.get(0).getValue());
        Date endTime = new Date((Long) dateList.get(1).getValue());
        Date newEndTime = endTime;
        String endFlag = String.valueOf(dateList.get(5).getValue());
        if (endFlag.equals("<")) {
            newEndTime = DateUtils.addSeconds(endTime, -1);
        }

        //endregion

        AlarmFlag alarmFlag = alarmFlagRepository.getSingleAlarmFlag(alarmFlagId);
        AlarmPoint alarmPoint = alarmPointRepository.getSingleAlarmPoint(alarmPointId);
        String hourTime = " " + basicDataService.getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();

        //平均值，最大值，最小值
        List alarmAvgEventList = repository.getAlarmAvgData(startTime, newEndTime, alarmPointId, alarmFlagId);
        Object avgValue = ((Object[]) alarmAvgEventList.get(0))[0];
        Object maxValue = ((Object[]) alarmAvgEventList.get(0))[1];
        Object minValue = ((Object[]) alarmAvgEventList.get(0))[2];

        //报警总时长
        List alarmDurationList = repository.getAlarmDurationData(startTime, newEndTime, alarmPointId, alarmFlagId);
        //报警次数
        List<Long> alarmTimesList = repository.getAlarmTimesData(startTime, newEndTime, alarmPointId, alarmFlagId);
        //确认次数
        List confirmTimesList = repository.getConfirmTimesData(startTime, newEndTime, alarmPointId, alarmFlagId);
        Long confirmTimes =confirmTimesList.get(0)==null? 0 : Long.valueOf(confirmTimesList.get(0).toString());
        //确认总时长
        List<BigDecimal> confirmDurationList = repository.getConfirmDurationData(startTime, newEndTime, alarmPointId, alarmFlagId);
        //平均确认时间=确认总时长/确认次数，单位为小时，保留两位小数；
        //BigDecimal bd = confirmDurationList.get(0);
        //System.out.println("---------------------------"+bd.doubleValue());
        Double avgConfirm = confirmTimes == 0 ? 0 : Double.valueOf(String.format("%.2f", confirmDurationList.get(0).doubleValue()/ confirmTimes * 1.0));

        //获取柱状图数据
        //原柱状图数据
        List oriHistogramData = repository.getHistogramData(startTime, newEndTime, alarmPointId, alarmFlagId, hourTime, avgValue == null ? null : Double.valueOf(avgValue.toString()), true);
        //折线图数据
        List<DictionaryEntity> lineChartMap = new ArrayList<>();

        //获取折线图数据
        List lineChartData = repository.getLineChartData(startTime, newEndTime, alarmPointId, alarmFlagId);
        lineChartData.stream().forEach(x -> {
            DictionaryEntity de = new DictionaryEntity();
            if(((Object[]) x)[0].toString() !=null && !"".equals(((Object[]) x)[0].toString())){
                de.setKey(((Object[]) x)[1].toString());
                de.setValue(Double.valueOf(((Object[]) x)[0].toString()));
            }
            lineChartMap.add(de);
        });

        //获取x轴日期跨度
        List<String> positionDateList = getVariationTrendDate(startTime, endTime);
        List<DictionaryEntity> oriList = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");

        if (alarmFlagId == CommonEnum.AlarmFlagEnum.PVHH.getIndex()
                || alarmFlagId == CommonEnum.AlarmFlagEnum.PVHI.getIndex()
                || alarmFlagId == CommonEnum.AlarmFlagEnum.PVLO.getIndex()
                || alarmFlagId == CommonEnum.AlarmFlagEnum.PVLL.getIndex()) {

            if (avgValue==null||maxValue==null||minValue==null){
                alarmDetailEntity.setTag(alarmPoint.getTag());
                alarmDetailEntity.setAlarmFlagName(alarmFlag.getName());
                alarmDetailEntity.setPrdtCellSname(alarmPoint.getPrdtCell().getSname());
                alarmDetailEntity.setLocation(alarmPoint.getLocation());
                return alarmDetailEntity;
                //throw new Exception("报警事件表中值不正确！");
            }
            //限值
            Object limitValue = getLimitValue(alarmPoint, alarmFlagId);
            //现报警数
            int alarmEventList = repository.getNowAlrmTimesData(startTime, newEndTime, alarmPointId, alarmFlagId, Double.valueOf(((Object[]) alarmAvgEventList.get(0))[0].toString()));
            //减少比例 =（“报警次数”-“现报警数”）/“报警次数”*100%，保留2位小数；
            Double reduceRate = alarmTimesList.get(0) == 0 ? 0 : Double.valueOf(String.format("%.2f", ((alarmTimesList.get(0) - alarmEventList) / (alarmTimesList.get(0) * 1.0)) * 100));

            alarmDetailEntity.setNowAlarmTimes(alarmEventList);
            alarmDetailEntity.setReduceRate(reduceRate);
            alarmDetailEntity.setLimitValue(limitValue);

            //现柱状图数据
            nowHistogramData = repository.getHistogramData(startTime, newEndTime, alarmPointId, alarmFlagId, hourTime, avgValue == null ? null : Double.valueOf(avgValue.toString()), false);

            List finalNowHistogramData = nowHistogramData;
            positionDateList.forEach(x -> {
                DictionaryEntity oriEntity = new DictionaryEntity();
                DictionaryEntity itemEntity = new DictionaryEntity();
                Object oriItem = oriHistogramData.stream().filter(u -> dateFormat.format((Date) ((Object[]) u)[1]).equals(x)).findFirst().orElse(null);
                Object nowItem = finalNowHistogramData.stream().filter(u -> dateFormat.format((Date) ((Object[]) u)[1]).equals(x)).findFirst().orElse(null);
                Double oriValue = oriItem == null ? 0 : Double.valueOf(((Object[]) oriItem)[0].toString());
                Double nowValue = nowItem == null ? 0 : Double.valueOf(((Object[]) nowItem)[0].toString());
                itemEntity.setKey(oriValue);
                itemEntity.setValue(nowValue);

                oriEntity.setKey(x);
                oriEntity.setValue(itemEntity);

                oriList.add(oriEntity);
            });

        } else {
            positionDateList.forEach(x -> {
                DictionaryEntity oriEntity = new DictionaryEntity();
                DictionaryEntity itemEntity = new DictionaryEntity();
                Object oriItem = oriHistogramData.stream().filter(u -> dateFormat.format((Date) ((Object[]) u)[1]).equals(x)).findFirst().orElse(null);
                Double oriValue = oriItem == null ? 0 : Double.valueOf(((Object[]) oriItem)[0].toString());
                itemEntity.setKey(0);
                itemEntity.setValue(oriValue);

                oriEntity.setKey(x);
                oriEntity.setValue(itemEntity);
                oriList.add(oriEntity);
            });
        }

        //工艺卡片上限值
        DictionaryEntity upLimitValue = new DictionaryEntity();
        upLimitValue.setKey(alarmPoint.getCraftUpLimitInclude());
        upLimitValue.setValue(alarmPoint.getCraftUpLimitValue());
        if (upLimitValue.getKey()==null||upLimitValue.getValue()==null){
            upLimitValue = new DictionaryEntity();
        }

        //工艺卡片下限值
        DictionaryEntity downLimitValue = new DictionaryEntity();
        downLimitValue.setKey(alarmPoint.getCraftDownLimitInclude());
        downLimitValue.setValue(alarmPoint.getCraftDownLimitValue());
        if (downLimitValue.getKey()==null||downLimitValue.getValue()==null){
            downLimitValue = new DictionaryEntity();
        }

        alarmDetailEntity.setTag(alarmPoint.getTag());
        alarmDetailEntity.setAlarmFlag(alarmFlag.getAlarmFlagId());
        alarmDetailEntity.setAlarmFlagName(alarmFlag.getName());
        alarmDetailEntity.setPrdtCellSname(alarmPoint.getPrdtCell().getSname());
        alarmDetailEntity.setLocation(alarmPoint.getLocation());
        alarmDetailEntity.setAlarmDuration(alarmDurationList.get(0)==null?null:Double.valueOf(alarmDurationList.get(0).toString()));
        alarmDetailEntity.setAlarmTimes(alarmTimesList.get(0));
        alarmDetailEntity.setConfirmTimes(confirmTimes);
        alarmDetailEntity.setAvgConfirmTime(avgConfirm);
        alarmDetailEntity.setAlarmValue(avgValue == null ? null : Double.valueOf(avgValue.toString()));
        alarmDetailEntity.setUnitFlag(alarmPoint.getMeasUnit().getSign());
        alarmDetailEntity.setMaxValue(maxValue == null ? null : Double.valueOf(maxValue.toString()));
        alarmDetailEntity.setMinValue(minValue == null ? null : Double.valueOf(minValue.toString()));
        alarmDetailEntity.setUpLimitValue(upLimitValue);
        alarmDetailEntity.setDownLimitValue(downLimitValue);
        alarmDetailEntity.setHistogramData(oriList);
        alarmDetailEntity.setLineChartData(lineChartMap);

        return alarmDetailEntity;
    }

    /**
     * 查看报警分析-报警详情报警值变更
     *
     * <AUTHOR> 2017-11-09
     * @param dateJason    日期集合
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @param alarmValue   报警值变更
     * @return 报警详情数据
     */
    public AlarmDetailEntity getChangeAlarmDetail(String dateJason, Long alarmPointId, Long alarmFlagId, Double alarmValue) throws IOException {

        //region 解析Jason字符串
        ObjectMapper objectMapper = new ObjectMapper();
        List<DictionaryEntity> dateList = objectMapper.readValue(dateJason, new TypeReference<List<DictionaryEntity>>() {
        });

        Date startTime = new Date((Long) dateList.get(0).getValue());
        Date endTime = new Date((Long) dateList.get(1).getValue());
        Date newEndTime = endTime;
        String endFlag = String.valueOf(dateList.get(5).getValue());
        if (endFlag.equals("<")) {
            newEndTime = DateUtils.addSeconds(endTime, -1);
        }
        //endregion
        String hourTime = " " + basicDataService.getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();

        //现报警数
        int alarmEventList = repository.getNowAlrmTimesData(startTime, newEndTime, alarmPointId, alarmFlagId, alarmValue);
        //报警次数
        List<Long> alarmTimesList = repository.getAlarmTimesData(startTime, newEndTime, alarmPointId, alarmFlagId);
        //减少比例 =（“报警次数”-“现报警数”）/“报警次数”*100%，保留2位小数；
        Double reduceRate = alarmTimesList.get(0) == 0 ? 0 : Double.valueOf(String.format("%.2f", ((alarmTimesList.get(0) - alarmEventList) / (alarmTimesList.get(0) * 1.0)) * 100));
        //现柱状图数据
        List nowHistogramData = repository.getHistogramData(startTime, newEndTime, alarmPointId, alarmFlagId, hourTime, alarmValue, false);

        //获取x轴日期跨度
        List<String> positionDateList = getVariationTrendDate(startTime, endTime);
        List<DictionaryEntity> oriList = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");

        positionDateList.forEach(x -> {
            DictionaryEntity oriEntity = new DictionaryEntity();
            DictionaryEntity itemEntity = new DictionaryEntity();
            Object nowItem = nowHistogramData.stream().filter(u -> dateFormat.format((Date) ((Object[]) u)[1]).equals(x)).findFirst().orElse(null);
            Double nowValue = nowItem == null ? 0 : Double.valueOf(((Object[]) nowItem)[0].toString());
            itemEntity.setKey(0);
            itemEntity.setValue(nowValue);
            oriEntity.setKey(x);
            oriEntity.setValue(itemEntity);
            oriList.add(oriEntity);
        });

        AlarmDetailEntity alarmDetailEntity = new AlarmDetailEntity();
        alarmDetailEntity.setNowAlarmTimes(alarmEventList);
        alarmDetailEntity.setReduceRate(reduceRate);
        alarmDetailEntity.setHistogramData(oriList);
        return alarmDetailEntity;
    }

    /**
     * 查看报警分析-操作详情网格列数据
     *
     * <AUTHOR> 2017-10-30
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param endFlag      标志
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @param page         分页对象
     * @return 操作详情数据
     */
    @Override
    public PaginationBean<AlarmEventEntity> getGridOperateDetail(Date startTime, Date endTime, String endFlag, Long alarmPointId, Long alarmFlagId, Pagination page) throws Exception {

        Date newEndTime = endTime;
        if (endFlag.equals("<")) {
            newEndTime = DateUtils.addSeconds(endTime, -1);
        }

        PaginationBean<AlarmEvent> pageEntity = repository.getOperateDetail(startTime, newEndTime, alarmPointId, alarmFlagId, page);
        PaginationBean<AlarmEventEntity> returnList = new PaginationBean<>(page, pageEntity.getTotal());
        returnList.setPageList(ObjectConverter.listConverter(pageEntity.getPageList(), AlarmEventEntity.class));
        returnList.getPageList().stream().forEach(x -> {
            AlarmEvent alarmEvent = pageEntity.getPageList().stream().filter(u -> u.getEventId().equals(x.getEventId())).findFirst().orElse(new AlarmEvent());
            x.setPriorityName(CommonEnum.AlarmPriorityEnum.getName(x.getPriority()));
            x.setEventTypeName(alarmEvent.getEventType().getName());
        });
        return returnList;
    }

    /**
     * 根据装置ID和时间区间获取持续的报警事件列表
     *
     * <AUTHOR> 2017-10-30
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCode    装置Id
     * @param page      分页信息
     * @return 持续的报警事件列表
     */
    public PaginationBean<AlarmEventEntity> getContinuousAlarmList(Date startTime, Date endTime, String unitCode, Pagination page) throws Exception {

        PaginationBean<AlarmEventEntity> returnList = basicDataService.getPersistentAlarmAnalysis(new String[]{unitCode}, null, -1, -1, startTime, endTime, page);
        for (int i = 0; i < returnList.getPageList().size(); i++) {
            returnList.getPageList().get(i).setTag(returnList.getPageList().get(i).getAlarmPointTag());
        }
        return returnList;
    }

    /**
     * 根据装置ID和时间区间获取搁置的报警事件列表
     *
     * <AUTHOR> 2017-10-30
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCode    装置Id
     * @param page      分页信息
     * @return 搁置的报警事件列表
     */
    public PaginationBean<AlarmEventEntity> getShelvedAlarmList(Date startTime, Date endTime, String unitCode, Pagination page) throws Exception {

        PaginationBean<AlarmEvent> alarmList = repository.getShelveAlarmAnalysis(new String[]{unitCode}, null, null, startTime, endTime,null, page);

        PaginationBean<AlarmEventEntity> returnList = new PaginationBean<>(page, alarmList.getTotal());
        returnList.setPageList(ObjectConverter.listConverter(alarmList.getPageList(), AlarmEventEntity.class));
        List<ShiftWorkTeamEntity> shiftWorkList=new ArrayList<>();
        Date minDate=returnList.getPageList().stream().reduce((item1,item2)->item1.getStartTime().getTime()<item2.getStartTime().getTime()?item1:item2).orElse(new AlarmEventEntity()).getStartTime();
        Date maxDate=returnList.getPageList().stream().reduce((item1,item2)->item1.getStartTime().getTime()>item2.getStartTime().getTime()?item1:item2).orElse(new AlarmEventEntity()).getStartTime();
        if(minDate!=null&&maxDate!=null) {
            shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCode!=null?new String[]{unitCode}:new String[]{}),minDate, maxDate);
        }
        for (int i = 0; i < returnList.getPageList().size(); i++) {
            returnList.getPageList().get(i).setTag(alarmList.getPageList().get(i).getAlarmPoint().getTag());
            returnList.getPageList().get(i).setAlarmFlagName(alarmList.getPageList().get(i).getAlarmFlag().getName());
            int finalI = i;
            returnList.getPageList().get(i).setWorkTeamSName(shiftWorkList.stream().filter(item->returnList.getPageList().get(finalI).getStartTime().getTime()>=item.getStartTime().getTime()&&returnList.getPageList().get(finalI).getStartTime().getTime()<item.getEndTime().getTime()).findFirst().orElse(new ShiftWorkTeamEntity()).getWorkTeamSName());
        }
        return returnList;
    }

    /**
     * 根据装置ID和时间区间获取屏蔽的报警事件列表
     *
     * <AUTHOR> 2017-10-30
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCode    装置Id
     * @param page      分页信息
     * @return 屏蔽的报警事件列表
     */
    public PaginationBean<AlarmEventEntity> getSuppressedAlarmList(Date startTime, Date endTime, String unitCode, Pagination page) throws Exception {

        PaginationBean<AlarmEvent> alarmList = repository.getShieldAlarmAnalysis(new String[]{unitCode}, null, null,new Long[]{3003L,3004L}, startTime, endTime,null, page);
        PaginationBean<AlarmEventEntity> returnList = new PaginationBean<>(page, alarmList.getTotal());
        returnList.setPageList(ObjectConverter.listConverter(alarmList.getPageList(), AlarmEventEntity.class));
        List<ShiftWorkTeamEntity> shiftWorkList=new ArrayList<>();
        Date minDate=returnList.getPageList().stream().reduce((item1,item2)->item1.getStartTime().getTime()<item2.getStartTime().getTime()?item1:item2).orElse(new AlarmEventEntity()).getStartTime();
        Date maxDate=returnList.getPageList().stream().reduce((item1,item2)->item1.getStartTime().getTime()>item2.getStartTime().getTime()?item1:item2).orElse(new AlarmEventEntity()).getStartTime();
        if(minDate!=null&&maxDate!=null) {
            shiftWorkList = shiftService.getShiftWorkTeamList(Arrays.asList(unitCode!=null?new String[]{unitCode}:new String[]{}),minDate, maxDate);
        }
        for (int i = 0; i < returnList.getPageList().size(); i++) {
            returnList.getPageList().get(i).setTag(alarmList.getPageList().get(i).getAlarmPoint().getTag());
            returnList.getPageList().get(i).setAlarmFlagName(alarmList.getPageList().get(i).getAlarmFlag().getName());
            int finalI = i;
            returnList.getPageList().get(i).setWorkTeamSName(shiftWorkList.stream().filter(item->returnList.getPageList().get(finalI).getStartTime().getTime()>=item.getStartTime().getTime()&&returnList.getPageList().get(finalI).getStartTime().getTime()<item.getEndTime().getTime()).findFirst().orElse(new ShiftWorkTeamEntity()).getWorkTeamSName());
            returnList.getPageList().get(i).setEventTypeName(alarmList.getPageList().get(i).getEventType().getName());
        }
        return returnList;
    }

    /**
     * 获取x轴时间
     *
     * <AUTHOR> 2017-10-23
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 时间日期集合
     */
    private List<String> getVariationTrendDate(Date startTime, Date endTime) {
        List<String> dateList = new ArrayList<>();
        int diffDays = differentDays(startTime, endTime);
        DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");

        for (int i = 0; i < diffDays; i++) {
            Date newDate = DateUtils.addDays(startTime, i);
            String vtDate = dateFormat.format(newDate);
            dateList.add(vtDate);
        }
        return dateList;
    }

    /**
     * startDate比endDate多的天数
     *
     * <AUTHOR> 2017-10-23
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 时间天数差
     */
    private int differentDays(Date startDate, Date endDate) {
        String hourTime = " " + basicDataService.getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();
        int diffDays = 0;
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(startDate);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(endDate);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2)   //不同一年
        {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0)    //闰年
                {
                    timeDistance += 366;
                } else    //不是闰年
                {
                    timeDistance += 365;
                }
            }

            diffDays = timeDistance + (day2 - day1);
        } else    //同年
        {
            diffDays = day2 - day1;
        }

        try {
            //根据业务需要，如果结束时间大于现在日期的8点，则日期加一天
            DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
            DateFormat dateFormat2 = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            String strNow = dateFormat.format(new Date()) + hourTime;

            if ((endDate.getTime() - (dateFormat2.parse(strNow).getTime())) > 0) {
                diffDays++;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return diffDays;
    }

    /**
     * 获取柱状图数据
     *
     * <AUTHOR> 2017-10-30
     * @param alarmEventList
     * @param positionDateList
     * @return
     */
    private List<Long> getChartData(List<AlarmAnalysisOperateEntity> alarmEventList, List<String> positionDateList) {
        List<Long> alarmTimes = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
        for (int i = 0; i < positionDateList.size(); i++) {
            try {
                Long count = 0L;
                Date newDate = dateFormat.parse(positionDateList.get(i));
                AlarmAnalysisOperateEntity aaoEntity = alarmEventList.stream().filter(x -> x.getStartDate().getTime() == newDate.getTime()).findFirst().orElse(null);
                if (aaoEntity != null) {
                    count = aaoEntity.getTimes();
                }
                alarmTimes.add(count);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return alarmTimes;
    }

    /**
     * 获取限值
     *
     * <AUTHOR> 2017-10-30
     * @param alarmPoint  报警点对象
     * @param alarmFlagId 报警标识
     * @return
     */
    private Object getLimitValue(AlarmPoint alarmPoint, Long alarmFlagId) {
        Object limitValue = 0;
        switch (Integer.valueOf(alarmFlagId.toString())) {
            case 1:
                limitValue = alarmPoint.getAlarmPointHH();
                break;
            case 2:
                limitValue = alarmPoint.getAlarmPointHI();
                break;
            case 3:
                limitValue = alarmPoint.getAlarmPointLO();
                break;
            case 4:
                limitValue = alarmPoint.getAlarmPointLL();
                break;
        }
        return limitValue;
    }
}
