package com.pcitc.opal.ae.bll;

import java.util.Date;

import org.springframework.stereotype.Service;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

/*
 * 报警屏蔽逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_AlarmScreenService
 * 作       者：kun.zhao
 * 创建时间：2017/10/23
 * 修改编号：1
 * 描       述：报警屏蔽逻辑层接口 
 */
@Service
public interface AlarmScreenService {
	
	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-10-23
	 * @param unitCodes		装置编码数组
	 * @param prdtCellIds	生产单元ID数组
	 * @param workTeamIds	班组ID数组
	 * @param alarmPointTag 报警点位号
	 * @param alarmFlagId	报警标识ID
	 * @param priority		优先级
	 * @param startTime		发生时间范围起始
	 * @param endTime		发生时间范围结束
	 * @param page			分页参数
	 * @return 报警屏蔽事件数据
	 * @throws Exception
	 */
	PaginationBean<AlarmEventEntity> getAlarmEvent(String[] unitCodes, Long[] prdtCellIds, Long[] workTeamIds, String alarmPointTag,
			Long alarmFlagId,Long eventTypeId, Integer priority, Date startTime, Date endTime, Pagination page) throws Exception;

}
