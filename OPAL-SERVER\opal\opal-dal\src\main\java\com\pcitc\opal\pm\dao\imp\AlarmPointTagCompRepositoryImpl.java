package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmEventTypeCompRepositoryCustom;
import com.pcitc.opal.pm.dao.AlarmPointTagCompRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmEventTypeComp;
import com.pcitc.opal.pm.pojo.AlarmPointTagComp;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/*
 * AlarmEventTypeComp实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_AlarmEventTypeCompRepositoryImpl
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：AlarmEventTypeComp实体的Repository实现
 */
public class AlarmPointTagCompRepositoryImpl extends BaseRepository<AlarmPointTagComp, Long> implements AlarmPointTagCompRepositoryCustom {


    @Override
    public List<AlarmPointTagComp> getAlarmPointTagCompEntitys(Long comId) {
        List<AlarmPointTagComp> alarmPointTagCompList =new ArrayList<>();
        try {
            StringBuilder hql = new StringBuilder(
                    "from AlarmPointTagComp t where t.sysType =:type and t.companyId =:com");
            Query query=this.getEntityManager().createQuery(hql.toString());
            query.setParameter("type",1L);
            query.setParameter("com",comId);
            alarmPointTagCompList = query.getResultList();
            return alarmPointTagCompList;
        }catch (Exception ex){
            return alarmPointTagCompList;
        }
    }

    @Override
    public List<Long> distinctByComId() {
        List<Long> comIdList =new ArrayList<>();
        try {
            StringBuilder hql = new StringBuilder(
                    "select t.companyId from AlarmPointTagComp t group by t.companyId");
            Query query = this.getEntityManager().createQuery(hql.toString());
            comIdList = query.getResultList();
            return comIdList;
        }catch (Exception ex){
            return comIdList;
        }
    }

}
