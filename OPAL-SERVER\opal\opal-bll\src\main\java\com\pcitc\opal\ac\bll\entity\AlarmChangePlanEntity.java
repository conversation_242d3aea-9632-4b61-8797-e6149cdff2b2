
package com.pcitc.opal.ac.bll.entity;

import java.util.Date;

import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 报警变更方案Entity实体
 * 模块编号：pcitc_opal_bll_class_AlarmChangePlanEntity
 * 作       者：kun.zhao
 * 创建时间：2018/01/19
 * 修改编号：1
 * 描       述：报警变更方案Entity实体
 */
public class AlarmChangePlanEntity extends BasicEntity {
	
	/**
     * 报警变更方案ID
     */
    private Long planId;

    /**
     * 流程实例ID
     */
    private String flowCaseId;

    /**
     * 流程模板ID
     */
    private String flowTempId;

    /**
     * 装置编码
     */
    private String unitId;
    
    /**
     * 装置名称
     */
    private String unitName;

    /**
     * 车间编码
     */
    private String workshopId;
    
    /**
     * 车间名称
     */
    private String workshopName;
    
    /**
     * 申请单编号
     */
    private String planCode;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 提交人ID
     */
    private String submitUserId;

    /**
     * 提交人名称
     */
    private String submitUserName;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 状态（0未提交；1已驳回；2已提交；3已审核；4已下发；5已完成）
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    @SuppressWarnings("unused")
	private String statusName;

    /**
     * 显示状态
     */
    private Integer showStatus;
    
    /**
     * 显示状态名称
     */
    @SuppressWarnings("unused")
	private String showStatusName;
    
    /**
     * 变更方案业务类型
     */
    private Integer businessType;

	/**
	 * 任务id
	 */
	private String taskId;

    /**
     * 审批状态（0待审核，1已审核）
     */
    @SuppressWarnings("unused")
    private Integer auditStatus;

	/**
	 * 审批状态名称
	 */
	@SuppressWarnings("unused")
	private String auditStatusName;

	public Long getPlanId() {
		return planId;
	}

	public void setPlanId(Long planId) {
		this.planId = planId;
	}

	public String getFlowCaseId() {
		return flowCaseId;
	}

	public void setFlowCaseId(String flowCaseId) {
		this.flowCaseId = flowCaseId;
	}

	public String getFlowTempId() {
		return flowTempId;
	}

	public void setFlowTempId(String flowTempId) {
		this.flowTempId = flowTempId;
	}

	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getWorkshopId() {
		return workshopId;
	}

	public void setWorkshopId(String workshopId) {
		this.workshopId = workshopId;
	}

	public String getWorkshopName() {
		return workshopName;
	}

	public void setWorkshopName(String workshopName) {
		this.workshopName = workshopName;
	}

	public String getPlanCode() {
		return planCode;
	}

	public void setPlanCode(String planCode) {
		this.planCode = planCode;
	}

	public Date getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}

	public Date getSubmitTime() {
		return submitTime;
	}

	public void setSubmitTime(Date submitTime) {
		this.submitTime = submitTime;
	}

	public String getSubmitUserId() {
		return submitUserId;
	}

	public void setSubmitUserId(String submitUserId) {
		this.submitUserId = submitUserId;
	}

	public String getSubmitUserName() {
		return submitUserName;
	}

	public void setSubmitUserName(String submitUserName) {
		this.submitUserName = submitUserName;
	}

	public String getApplyReason() {
		return applyReason;
	}

	public void setApplyReason(String applyReason) {
		this.applyReason = applyReason;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getStatusName() {
		if (status == null) {
            return "";
        }
		return CommonEnum.AlarmChangePlanStatusEnum.getName(status);
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}

	public Integer getShowStatus() {
		return showStatus;
	}

	public void setShowStatus(Integer showStatus) {
		this.showStatus = showStatus;
	}

	public String getShowStatusName() {
		if (showStatus == null) {
            return "";
        }
		if(CommonEnum.AlarmChangeBizTypeEnum.Apply.getIndex() == businessType)
			return CommonEnum.AlarmChangeBizApplyEnum.getName(showStatus);
		else if(CommonEnum.AlarmChangeBizTypeEnum.Issue.getIndex() == businessType)
			return CommonEnum.AlarmChangeBizIssueEnum.getName(showStatus);
		else
			return CommonEnum.AlarmChangeBizConfirmEnum.getName(showStatus);
	}

	public void setShowStatusName(String showStatusName) {
		this.showStatusName = showStatusName;
	}

	public Integer getBusinessType() {
		return businessType;
	}

	public void setBusinessType(Integer businessType) {
		this.businessType = businessType;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAuditStatusName() {
		return auditStatusName;
	}

	public void setAuditStatusName(String auditStatusName) {
		this.auditStatusName = auditStatusName;
	}
}