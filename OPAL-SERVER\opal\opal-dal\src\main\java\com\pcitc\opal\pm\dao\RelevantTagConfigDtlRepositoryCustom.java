package com.pcitc.opal.pm.dao;

import java.util.List;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.RelevantTagConfigDtl;

/*
 * RelevantTagConfigDtl实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_RelevantTagConfigDtlRepositoryCustom
 * 作       者：dageng.sun
 * 创建时间：2018/8/2
 * 修改编号：1
 * 描       述：RelevantTagConfigDtl实体的Repository的JPA自定义接口 
 */
public interface RelevantTagConfigDtlRepositoryCustom {
	
	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigDtlIds 相关性位号配置明细主键id集合
	 * @return CommonResult 返回消息结果类
	 */
	CommonResult deleteRelevantTagConfigDtl(Long[] relevantTagConfigDtlIds);
	
	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigDtlIds 相关性位号配置明细主键Id集合
	 * @return List<RelevantTagConfigDtl> 返回RelevantTagConfigDtl实体类集合
	 */
	List<RelevantTagConfigDtl> getRelevantTagConfigDtl(Long[] relevantTagConfigDtlIds);
	
	/**
	 * 根据参数“相关性位号配置ID”查询<相关性位号配置明细>数据
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 相关性位号配置ID
	 * @param page 分页对象
	 * @return PaginationBean<RelevantTagConfigDtl> 返回RelevantTagConfigDtl实体类分页对象
	 */
	PaginationBean<RelevantTagConfigDtl> getRelevantTagConfigDtl(Long relevantTagConfigId, Pagination page);
	
	/**
	 * 根据参数“相关性位号配置ID”查询<相关性位号配置明细>数据
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 相关性位号配置ID
	 * @return List<RelevantTagConfigDtl> 返回RelevantTagConfigDtl实体类集合对象
	 */
	List<RelevantTagConfigDtl> getRelevantTagConfigDtl(Long relevantTagConfigId);
	
	/**
	 * 数据校验
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 关性位号配置ID
	 * @param alarmPointIds 报警点ID数组
	 * @return CommonResult 返回消息结果类
	 */
	CommonResult relevantTagConfigDtlValidation(Long relevantTagConfigId, Long[] alarmPointIds);
	
	/**
	 * 保存<相关性位号配置明细>数据
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param rtcd 相关性位号配置明细实体类
	 * @return CommonResult 返回消息结果类
	 */
	CommonResult addRelevantTagConfigDtl(RelevantTagConfigDtl rtcd);
	
}
