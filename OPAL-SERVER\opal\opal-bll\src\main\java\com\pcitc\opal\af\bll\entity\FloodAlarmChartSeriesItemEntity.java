package com.pcitc.opal.af.bll.entity;

import java.io.Serializable;

/*
 * 高频报警分析图表展示实体
 * 模块编号：pcitc_opal_bll_class_FloodAlarmChartSeriesItemEntity
 * 作  　者：xuelei.wang
 * 创建时间：2017-11-16
 * 修改编号：1
 * 描    述：高频报警分析图表展示实体
 */
@SuppressWarnings("serial")
public class FloodAlarmChartSeriesItemEntity implements Serializable {
    /**
     * 名称
     */
    private String name;
    /**
     * 类型
     */
    private String type;
    /**
     * 装置编码
     */
    private String unitId;
    /**
     * 单元ID
     */
    private Long prdtId;
    /**
     * 数据
     */
    private Object data;
    /***
     * 高频报警名称
     */
    private String floodName;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getFloodName() {
        return floodName;
    }

    public void setFloodName(String floodName) {
        this.floodName = floodName;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public Long getPrdtId() {
        return prdtId;
    }

    public void setPrdtId(Long prdtId) {
        this.prdtId = prdtId;
    }
}
