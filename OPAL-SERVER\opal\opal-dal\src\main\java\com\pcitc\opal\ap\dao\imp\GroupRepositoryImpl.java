package com.pcitc.opal.ap.dao.imp;

import com.pcitc.opal.ap.dao.GroupRepositoryCustom;
import com.pcitc.opal.ap.pojo.AlarmPushRule;
import com.pcitc.opal.ap.pojo.Group;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.dao.BaseRepository;
import org.springframework.util.StringUtils;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/*
 * 报警知识管理实体的Repository的JPA接口实现
 * 模块编号： pcitc_opal_dal_class_AlarmKnowlgManagmtRepositoryImpl
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理实体的Repository的JPA接口实现
 */
public class GroupRepositoryImpl extends BaseRepository<Group, Long>
		implements GroupRepositoryCustom {


	@Override
	public List<Group> getGroupNames() {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("from Group t where 1=1 and t.inUse=1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			//企业
			CommonProperty commonProperty = new CommonProperty();
			Integer companyId = commonProperty.getCompanyId();
			if (null != companyId){
				hql.append(" and t.companyId = :companyId ");
				paramList.put("companyId",companyId);
			}
			hql.append(" order by t.sortNum,t.name");
			Query query = getEntityManager().createQuery(hql.toString());
			this.setParameterList(query, paramList);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	@Override
	public List<Group> getGroupDetail(Long groupId) {

		String hql = "from Group gp inner join gp.groupUserRelations gur inner join gur.mobileList" +
				" where gp.inUse = 1 and gur.inUse = 1 and gp.groupId = :groupId";


		Query query = getEntityManager().createQuery(hql);

		query.setParameter("groupId", groupId);


		return query.getResultList();
	}
}
