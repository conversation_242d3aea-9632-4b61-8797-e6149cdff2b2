package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.ad.dao.AlarmEventDelRepository;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.AlarmRecDelRepository;
import com.pcitc.opal.ad.dao.AlarmRecRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.ad.pojo.AlarmEventDel;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.ad.pojo.AlarmRecDel;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.AlarmPointDelConfigService;
import com.pcitc.opal.pm.bll.entity.AlarmPointDelConfigDTOEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointDelConfigEntity;
import com.pcitc.opal.pm.dao.AlarmPointDelConfigRepository;
import com.pcitc.opal.pm.dao.imp.AlarmPointDelConfigDTO;
import com.pcitc.opal.pm.pojo.AlarmPointDelConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/*
 * 报警点分组逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmPointGroupImpl
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点分组逻辑层实现类
 */
@Service
@Component
@Slf4j
public class AlarmPointDelConfigImpl implements AlarmPointDelConfigService, CommandLineRunner {
    @Resource
    AlarmPointDelConfigRepository alarmPointDelConfigRepository;

    @Resource
    private AlarmEventDelRepository alarmEventDelRepository;

    @Resource
    private AlarmEventRepository alarmEventRepository;

    @Resource
    private AlarmRecRepository alarmRecRepository;

    @Resource
    private AlarmRecDelRepository alarmRecDelRepository;

    @Resource
    BasicDataService basicDataService;



    /**
     * 创建带有延迟队列的线程池
     */
    private static final ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(3);



    @Override
    public PaginationBean<AlarmPointDelConfigDTOEntity> getAlarmPointDelConfigPage(String[] unitCodes, String groupName, Date startTime, Date endTime, Integer inUse, Pagination page, Integer delStatus) throws Exception {

        //根据权限获取装置
        unitCodes = basicDataService.getUnitListByIds(unitCodes, true).stream().map(UnitEntity::getStdCode).toArray(String[]::new);


        PaginationBean<AlarmPointDelConfigDTO> alarmPointDelConfigDTOList =
                alarmPointDelConfigRepository.getAlarmPointDelConfigPage(unitCodes,groupName,startTime,endTime,inUse, page, delStatus);
        PaginationBean<AlarmPointDelConfigDTOEntity> returnAlarmPointDel = new PaginationBean<AlarmPointDelConfigDTOEntity>(page,alarmPointDelConfigDTOList.getTotal());
        List<AlarmPointDelConfigDTOEntity> entityList=ObjectConverter.listConverter(alarmPointDelConfigDTOList.getPageList(), AlarmPointDelConfigDTOEntity.class);
        entityList.forEach(l->{
            l.setInUseShow(l.getInUse()>0?"是":"否");
            l.setDelStatusShow(AlarmPointDelConfigDTOEntity.getDelStatusShowDetail(l.getDelStatus()));
            l.setDelDataStatusShow(AlarmPointDelConfigDTOEntity.getDelDataStatusShowDetail(l.getDelDataStatus()));
        });
        returnAlarmPointDel.setPageList(entityList);
        return returnAlarmPointDel;
    }

    @Override
    public AlarmPointDelConfigDTOEntity getSingleAlarmPointDel(Long alarmPointDelConfigId) throws Exception {
        AlarmPointDelConfigDTO alarmPointDelConfig = alarmPointDelConfigRepository.getSingleAlarmPointDel(alarmPointDelConfigId);
        AlarmPointDelConfigDTOEntity l =ObjectConverter.entityConverter(alarmPointDelConfig, AlarmPointDelConfigDTOEntity.class);
        l.setInUseShow(l.getInUse()>0?"是":"否");
        l.setDelStatusShow(AlarmPointDelConfigDTOEntity.getDelStatusShowDetail(l.getDelStatus()));
        l.setDelDataStatusShow(AlarmPointDelConfigDTOEntity.getDelDataStatusShowDetail(l.getDelDataStatus()));
        return l;

    }

    @Override
    public CommonResult deleteAlarmPointDel(Long[] alarmPointDelConfigIds){

        CommonResult commonResult = new CommonResult();
        if (ArrayUtils.isEmpty(alarmPointDelConfigIds)){
            commonResult.setMessage("请选择要删除的数据！");
        }
        Integer result = alarmPointDelConfigRepository.deleteAlarmPointDel(Arrays.asList(alarmPointDelConfigIds));

        if (result == alarmPointDelConfigIds.length) {
            commonResult.setMessage("删除成功");
        } else if (result > 0) {
            commonResult.setMessage("部分删除成功，只能删除状态为未提交和已驳回的数据！");
        } else {
            commonResult.setMessage("只能删除状态为未提交和已驳回的数据！");
        }
        return commonResult;
    }

    @Override
    public CommonResult addAlarmPointDel(AlarmPointDelConfigEntity alarmPointDelConfigEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmPointDelConfig alarmPointDelConfig =new AlarmPointDelConfig(
                alarmPointDelConfigEntity.getAlarmPointDelConfigId(),
                alarmPointDelConfigEntity.getUnitCode(),
                alarmPointDelConfigEntity.getAlarmPointGroupId(),
                alarmPointDelConfigEntity.getDelStartTime(),
                alarmPointDelConfigEntity.getDelEndTime(),
                alarmPointDelConfigEntity.getDes(),
                alarmPointDelConfigEntity.getInUse(),
                alarmPointDelConfigEntity.getDelStatus(),
                0
        );
        CommonProperty commonProperty = new CommonProperty();
        alarmPointDelConfig.setMntUserId(commonProperty.getUserId());
        alarmPointDelConfig.setMntUserName(commonProperty.getUserName());
        alarmPointDelConfig.setMntDate(commonProperty.getSystemDateTime());

        alarmPointDelConfig.setCrtUserId(commonProperty.getUserId());
        alarmPointDelConfig.setCrtUserName (commonProperty.getUserName());
        alarmPointDelConfig.setCrtDate(commonProperty.getSystemDateTime());

        alarmPointDelConfig.setCompanyId(Long.valueOf(commonProperty.getCompanyId()));
        return alarmPointDelConfigRepository.addAlarmPointDel(alarmPointDelConfig);
    }

    @Override
    public CommonResult updateAlarmPointDel(AlarmPointDelConfigEntity alarmPointDelConfigEntity) throws Exception {

        // 实体转换为持久层实体
        AlarmPointDelConfig alarmPointDelConfig =new AlarmPointDelConfig(
                alarmPointDelConfigEntity.getAlarmPointDelConfigId(),
                alarmPointDelConfigEntity.getUnitCode(),
                alarmPointDelConfigEntity.getAlarmPointGroupId(),
                alarmPointDelConfigEntity.getDelStartTime(),
                alarmPointDelConfigEntity.getDelEndTime(),
                alarmPointDelConfigEntity.getDes(),
                alarmPointDelConfigEntity.getInUse(),
                alarmPointDelConfigEntity.getDelStatus(),
                alarmPointDelConfigEntity.getDelDataStatus()
        );
        CommonProperty commonProperty = new CommonProperty();
        alarmPointDelConfig.setMntUserId(commonProperty.getUserId());
        alarmPointDelConfig.setMntUserName(commonProperty.getUserName());
        alarmPointDelConfig.setMntDate(commonProperty.getSystemDateTime());
        alarmPointDelConfig.setCrtUserId(commonProperty.getUserId());
        alarmPointDelConfig.setCrtUserName (commonProperty.getUserName());
        alarmPointDelConfig.setCrtDate(commonProperty.getSystemDateTime());
        alarmPointDelConfig.setCompanyId(Long.valueOf(commonProperty.getCompanyId()));
        return alarmPointDelConfigRepository.updateAlarmPointDel(alarmPointDelConfig);
    }

    @Override
    public CommonResult approvePass(Integer[] alarmPointDelConfigId) {

        CommonResult commonResult = new CommonResult();


        if (ArrayUtils.isEmpty(alarmPointDelConfigId)){
            commonResult.setMessage("请选择要审批通过的数据！");
            return commonResult;
        }

        Integer result = alarmPointDelConfigRepository.updateDelStatusByAlarmPointDelConfig(alarmPointDelConfigId, 2);

        if (result == alarmPointDelConfigId.length){
            commonResult.setMessage("已通过");

            //将状态修改为剔除中
            alarmPointDelConfigRepository.updateDelDataStatusById(alarmPointDelConfigId, 1);


            Integer companyId = new CommonProperty().getCompanyId();
            //延迟五分钟执行剔除
            scheduledExecutorService.schedule(() -> {
                delAlarmRecAndAlarmEvent(alarmPointDelConfigId, companyId, null);
            }, 5, TimeUnit.MINUTES);

            //剔除通过，将数据迁移到剔除表中

        } else if (result > 0) {
            commonResult.setMessage("部分通过，剔除状态为已提交的数据才可审批！");
        }else {
            commonResult.setMessage("剔除状态为已提交的数据才可审批！");
        }
        return commonResult;
    }

    @Override
    public CommonResult reject(Integer[] alarmPointDelConfigId) {

        CommonResult commonResult = new CommonResult();


        if (ArrayUtils.isEmpty(alarmPointDelConfigId)){
            commonResult.setMessage("请选择要驳回的数据！");
            return commonResult;
        }

        Integer result = alarmPointDelConfigRepository.updateDelStatusByAlarmPointDelConfig(alarmPointDelConfigId, 3);

        if (result == alarmPointDelConfigId.length){
            commonResult.setMessage("已驳回");
        } else if (result > 0) {
            commonResult.setMessage("部分驳回，剔除状态为已提交的数据才可驳回！");
        }else {
            commonResult.setMessage("剔除状态为已提交的数据才可驳回！");
        }
        return commonResult;
    }



    @Override
    public void delAlarmRecAndAlarmEvent(Integer[] alarmPointDelConfigId, Integer companyId, List<AlarmPointDelConfig> alarmPointDelConfigs){

        if (CollectionUtils.isEmpty(alarmPointDelConfigs)) {
            alarmPointDelConfigs = alarmPointDelConfigRepository.selectAlarmPointDelConfigDetailById(alarmPointDelConfigId, companyId);
        } else if (ArrayUtils.isEmpty(alarmPointDelConfigId)) {
            alarmPointDelConfigId = alarmPointDelConfigs.stream().map(AlarmPointDelConfig::getAlarmPointDelConfigId).map(x -> Integer.valueOf(x.toString())).collect(Collectors.toList()).toArray(new Integer[alarmPointDelConfigs.size()]);
        }

        //标记事件表剔除状态
        boolean eventStatus = true;

        //标记记录表剔除状态
        boolean recStatus = true;

        log.info("AlarmEvent剔除开始");

        try {
            try {
                List<AlarmEvent> alarmEvents = alarmEventRepository.selectDelAlarmEvent(alarmPointDelConfigs);
                log.info("AlarmEvent剔除数量{}", alarmEvents.size());
                if (alarmEvents.size() > 0) {
                    List<AlarmEventDel> alarmEventDels = alarmEvents.stream().map(AlarmEventDel::new).collect(Collectors.toList());
                    alarmEventDelRepository.saveBatch(alarmEventDels);

                    List<Long> ids = alarmEvents.stream().map(AlarmEvent::getEventId).collect(Collectors.toList());
                    alarmEventRepository.deleteBatch(ids, 1000);
                }
            } catch (Exception e) {
                eventStatus = false;
                log.error("事件表剔除失败，剔除配置表id-{}，异常信息{}", Arrays.toString(alarmPointDelConfigId), e.getMessage());
                e.printStackTrace();

            }
            log.info("AlarmEvent剔除结束");

            log.info("AlarmRec剔除开始");
            try {
                List<AlarmRec> alarmRecs = alarmRecRepository.selectDelAlarmRec(alarmPointDelConfigs);
                log.info("AlarmRec剔除数量{}", alarmRecs.size());
                if (alarmRecs.size() > 0) {
                    List<AlarmRecDel> alarmRecDels = alarmRecs.stream().map(AlarmRecDel::new).collect(Collectors.toList());
                    alarmRecDelRepository.saveBatch(alarmRecDels);

                    List<Long> ids = alarmRecs.stream().map(AlarmRec::getAlarmRecId).collect(Collectors.toList());
                    alarmRecRepository.deleteBatch(ids, 1000);
                }
            } catch (Exception e) {
                recStatus = false;
                log.error("记录表剔除失败，剔除配置表id-{}，异常信息{}", Arrays.toString(alarmPointDelConfigId), e.getMessage());
                e.printStackTrace();

            }
            log.info("AlarmRec剔除结束");
        } finally {

            if (eventStatus && recStatus) {
                //剔除成功，更新状态
                alarmPointDelConfigRepository.updateDelDataStatusById(alarmPointDelConfigId, 5);
            } else if (!eventStatus && !recStatus) {
                //全部剔除失败
                alarmPointDelConfigRepository.updateDelDataStatusById(alarmPointDelConfigId, 4);
            } else if (!eventStatus) {
                //事件表剔除失败
                alarmPointDelConfigRepository.updateDelDataStatusById(alarmPointDelConfigId, 2);
            } else {
                //记录表剔除失败
                alarmPointDelConfigRepository.updateDelDataStatusById(alarmPointDelConfigId, 3);
            }
        }
    }

    /**
     * 项目启动之后执行（扫描剔除失败的任务，重新执行剔除）
     */
    @Override
    public void run(String... args){

        boolean isScanDel = Boolean.parseBoolean(CommonPropertiesReader.getValue("isScanDel"));

        if (isScanDel) {
            try {
                //查询剔除失败的数据，查询语句加锁，for update
                List<AlarmPointDelConfig> alarmPointDelConfigs = alarmPointDelConfigRepository.selectDelDataFailure();

                if (alarmPointDelConfigs.size() == 0){
                    log.info("未查询到剔除失败的配置");
                }else {
                    log.error("查询到剔除失败的配置数据-{}，开始执行剔除", alarmPointDelConfigs.toString());
                    delAlarmRecAndAlarmEvent(null, null, alarmPointDelConfigs);
                }
            } catch (Exception e){
                log.error("启动时扫描剔除失败");
                log.error(e.getMessage());
                e.printStackTrace();
            }
        }

    }
}
