package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 企业实体
 * 模块编号：pcitc_opal_bll_class_Company
 * 作    者：shufei.sui
 * 创建时间：2021-02-22 10:03:53
 * 修改编号：1
 * 描    述：企业实体
 */
public class DBCompanyEntity extends BasicEntity {
    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 名称
     */
    private String name;

    /**
     * 简称
     */
    private String sname;

    /**
     * 标准编码
     */
    private String stdCode;

    /**
     * 是否启用（1是；0否）
     */
    //private Integer inUse;

    /**
     * 排序
     */
    private Integer sortNum;

    /**
     * 描述
     */
    private String des;


    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getName(){
        return name;
    }

    public void setName(String name){
        this.name = name;
    }

    public String getSname(){
        return sname;
    }

    public void setSname(String sname){
        this.sname = sname;
    }

    public String getStdCode(){
        return stdCode;
    }

    public void setStdCode(String stdCode){
        this.stdCode = stdCode;
    }

   /* public Integer getInUse(){
        return inUse;
    }

    public void setInUse(Integer inUse){
        this.inUse = inUse;
    }*/

    public Integer getSortNum(){
        return sortNum;
    }

    public void setSortNum(Integer sortNum){
        this.sortNum = sortNum;
    }

    public String getDes(){
        return des;
    }

    public void setDes(String des){
        this.des = des;
    }
}
