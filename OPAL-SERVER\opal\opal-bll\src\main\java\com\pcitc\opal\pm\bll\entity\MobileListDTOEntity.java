package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 电话本
 * 模块编号：pcitc_pojo_class_MobileList
 * 作    者：guoganxin
 * 创建时间：2020-05-11 10:14:49
 * 修改编号：1
 * 描    述：电话本
 */
public class MobileListDTOEntity extends BasicEntity {


    /**
     * 电话本ID
     */
    private Long mobileListId;

    /**
     * 工厂名称
     */
    private String factoryName;

    /**
     * 车间名称
     */
    private String workshopName;

    /**
     * 装置名称
     */
    private String unitName;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    public Long getMobileListId() {
        return mobileListId;
    }

    public void setMobileListId(Long mobileListId) {
        this.mobileListId = mobileListId;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    public String getWorkshopName() {
        return workshopName;
    }

    public void setWorkshopName(String workshopName) {
        this.workshopName = workshopName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}

