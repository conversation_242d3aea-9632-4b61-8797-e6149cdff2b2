$(function() {
    var searchUrl = OPAL.API.akUrl + '/alarmKnowlgManagmt';
    var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
    var alarmFlagListUrl = OPAL.API.commUrl + '/getAlarmFlagList';
    var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
    var currentTimeUrl = OPAL.API.commUrl + "/getSysDateTime";
    var isRefresh = false;
    window.pageLoadMode = PageLoadMode.None;
    var isLoading = true;
    var page = {
        /**
         * 初始化
         */
        init: function() {
            //绑定事件
            this.bindUI();
            //初始化查询装置树
            page.logic.initUnitTree();
            // 初始化 时间设置
            page.logic.initTime();
            //初始化报警标识
            page.logic.initAlarmFlagList();
            //初始化表格
            page.logic.initTable();

            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("AlarmKnowlgManagmt");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
            page.logic.search();
        },
        /**
         * 绑定事件
         */
        bindUI: function() {
            //查询
            $('#search').click(function() {
                isLoading = false;
                page.logic.search();
            })
        },
        data: {
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化表格
             */
            initTable: function() {
                OPAL.ui.initBootstrapTable("table", {
                    cache: false,
                    columns: [{
                        title: "操作",
                        field: '',
                        rowspan: 1,
                        align: 'center',
                        width: '60px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "序号",
                        formatter: function(value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "报警时间",
                        field: 'alarmTime',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'left',
                        width: '180px'
                    }, {
                        title: "位号",
                        field: 'alarmPointTag',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '70px'
                    }]
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function() {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.detail(' + rowData.eventId + ',' + rowData.alarmPointId +',' + rowData.alarmFlagId +')">知识维护</a>'
                ]
            },
            /**
             * 装置新增或者编辑详细页面
             */
            detail: function(alarmEventId, alarmPointId, alarmFlagId) {
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: 0,
                    area: ['800px', '81%'],
                    shadeClose: false,
                    content: 'AlarmKnowlgExcy.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "eventId" : alarmEventId,
                            "alarmPointId" : alarmPointId,
                            "alarmFlagId" : alarmFlagId
                        };
                         iframeWin.page.logic.setData(data);
                    },
                    end: function() {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 搜索
             */
            search: function() {
                if(!page.logic.checkDateIsValid()) return;
                //page.logic.detail();
                page.data.param = OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber":1
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    data: {
                        'enablePrivilege': true
                    },
                    onChange: function(nodes) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    }
                }, false);
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function() {
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化查询 报警标识
             */
            initAlarmFlagList: function() {
                OPAL.ui.getCombobox("alarmFlagId", alarmFlagListUrl, {
                    selectFirstRecord: true,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            /**
             * 初始化 时间
             */
            initTime: function() {
                var endTimeVal;
                $.ajax({
                    url: currentTimeUrl,
                    async: false,
                    dataType: "json",
                    success: function(data) {
                        var dataArr = $.ET.toObjectArr(data);
                        endTimeVal = dataArr[0].value;
                    },
                    error: function(jqXHR, textStatus, errorThrown) {}
                });
                OPAL.ui.initDateTimePeriodPicker({
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm:ss',
                    startValue: moment().get('years') + "-01-01 00:00:00",
                    startMax: moment(endTimeVal).format('YYYY-MM-DD HH:mm:ss'),
                    endValue: moment(endTimeVal).format('YYYY-MM-DD HH:mm:ss'),
                    endMax:moment(endTimeVal).format('YYYY-MM-DD HH:mm:ss')
                })
            },
            /**
             * 校验时间
             */
            checkDateIsValid: function () {
                var startTime = OPAL.util.strToDate($('#startTime').val());
                var endTime = OPAL.util.strToDate($('#endTime').val());
                if ($('#startTime').val() == "" || $('#endTime').val() == "" || $('#startTime').val() == undefined || $('#endTime').val() == undefined) {
                    layer.msg("开始时间和结束时间不能为空！");
                    return false;
                } else if ((endTime - startTime) < 0) {
                    layer.msg("报警开始时间不能大于结束时间！");
                    return false;
                } else if ((endTime - startTime) > (1000*60*60*24*365)) {
                    layer.msg("查询时间范围不能超过365天！");
                    return false;
                }
                return true;
            }
        }
    }
    page.init();
    window.page = page;
})