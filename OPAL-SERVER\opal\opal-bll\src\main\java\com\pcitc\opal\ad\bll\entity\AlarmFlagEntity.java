package com.pcitc.opal.ad.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 报警标识实体  
 * 模块编号：pcitc_opal_bll_class_AlarmFlagEntity
 * 作       者：kun.zhao
 * 创建时间：2017/10/19
 * 修改编号：1
 * 描       述：报警标识实体    
 */
public class AlarmFlagEntity extends BasicEntity {
	
	/**
     * 报警标识ID
     */
	private Long alarmFlagId;
	
	/**
     * 名称
     */
	private String name;
	
	/**
     * 排序
     */
	private Integer sortNum;

	public Long getAlarmFlagId() {
		return alarmFlagId;
	}

	public void setAlarmFlagId(Long alarmFlagId) {
		this.alarmFlagId = alarmFlagId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getSortNum() {
		return sortNum;
	}

	public void setSortNum(Integer sortNum) {
		this.sortNum = sortNum;
	}
	
}
