$(function() {
    var alarmFlagCompUrl = OPAL.API.pmUrl + "/alarmFlagComp";
    var dcsUrl = OPAL.API.commUrl + "/getDcsCodeList";
    var alarmFlagUrl = OPAL.API.commUrl + "/getAllAlarmFlagList";
    var inUseUrl = OPAL.API.commUrl + "/getInUse";
    var isRefresh = false;
    window.pageLoadMode = PageLoadMode.None;
    var page = {
        /**
         * 初始化
         */
        init: function() {
            //绑定事件
            this.bindUI();
            //初始化dcs
            page.logic.initDcs();
            //初始化优先级
            page.logic.initAlarmFlag();
            //初始化表格
            page.logic.initTable();
            //初始化查询是否启用
            page.logic.initInUse();
            //默认查询数据
            page.logic.search();
        },
        /**
         * 绑定事件
         */
        bindUI: function() {
            // 新增
            $('#alarmFlagCompAdd').click(function() {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            })
            //批量删除
            $('#alarmFlagCompAddDel').click(function() {
                page.logic.delAll();
            })
            //查询
            $('#search').click(function() {
                page.logic.search();
            })
        },
        data: {
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化表格
             */
            initTable: function() {
                OPAL.ui.initBootstrapTable("table", {
                    cache: false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '110px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "DCS名称",
                        field: 'dcsName',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "源报警标识",
                        field: 'alarmFlagSource',
                        rowspan: 1,
                        align: 'left',
                    }, {
                        title: "本系统报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'left',
                    },{
                        title: "是否启用",
                        field: 'inUseShow',
                        rowspan: 1,
                        align: 'center'
                    }]
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function() {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.edit(\'' + rowData.alarmFlagCompId + '\')">编辑</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.alarmFlagCompId + '\')" >删除</a> '
                ]
            },
            /**
             * 批量删除
             */
            delAll: function() {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections');
                $.each(rowsArray, function(i, el) {
                    idsArray.push(el.alarmFlagCompId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    $.ajax({
                        url: alarmFlagCompUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function(result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function(index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function(id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    $.ajax({
                        url: alarmFlagCompUrl,
                        async: false,
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function(result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function(index) {
                    layer.close(index)
                });
            },
            /**
             * 新增
             */
            add: function() {
                var pageMode = PageModelEnum.NewAdd;
                var title = "报警标识对照配置新增";
                page.logic.detail(title, "", pageMode);
            },
            /**
             * 编辑
             * @param prdtCellId
             */
            edit: function(alarmFlagCompId) {
                var pageMode = PageModelEnum.Edit;
                var title = "报警标识对照配置编辑";
                page.logic.detail(title, alarmFlagCompId, pageMode);
            },
            /**
             * 装置新增或者编辑详细页面
             */
            detail: function(title, alarmFlagCompId, pageMode) {
                layer.open({
                    type: 2,
                    title: title,
                    closeBtn: 1,
                    area: ['500px', '380px'],
                    shadeClose: false,
                    content: 'AlarmFlagCompAddOrEdit.html?'+ Math.random(),
                    success: function(layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "alarmFlagCompId": alarmFlagCompId,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function() {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 搜索
             */
            search: function() {
                page.data.param = OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": alarmFlagCompUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 初始化dcs
             */
            initDcs: function() {
                OPAL.ui.getCombobox("dcsCodeId", dcsUrl, {
                    keyField: "dcsCodeId",
                    valueField: "name",
                    selectFirstRecord: true,
                    data: {
                        isAll: true
                    }
                }, null);
            },
            initAlarmFlag: function() {
                    OPAL.ui.getCombobox("alarmFlagId", alarmFlagUrl, {
                    selectFirstRecord: true,
                    data: {
                        isAll: true
                    }
                }, null);
            },
            /**
             * 初始化查询inUse
             */
            initInUse: function () {
                OPAL.ui.getCombobox("inUse", inUseUrl, {
                    data: {
                        isAll: true
                    }
                }, null);
            }

        }
    }
    page.init();
    window.page = page;
})