package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 报警剔除配置
 * @TableName t_pm_alarmpointdelconfig
 */
@Data
public class AlarmPointDelConfigDTOEntity extends BasicEntity {

    /**
     * 报警剔除配置ID
     */
    private Long alarmPointDelConfigId;


    /**
     * 装置编码
     */
    private String unitCode;

    /**
     * 装置编码
     */
    private String unitName;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 报警点分组ID
     */
    private Long alarmPointGroupId;

    /**
     * 剔除开始时间
     */
    private Date delStartTime;

    /**
     * 剔除结束时间
     */
    private Date delEndTime;

    /**
     *是否启用（1是；0否）
     */
    private Integer inUse;


    /**
     * 创建时间
     */
    private Date crtDate;

    /**
     * 维护时间
     */
    private Date mntDate;


    /**
     * 创建人名称
     */
    private String crtUserName;

    /**
     * 最后维护人名称
     */
    private String mntUserName;

    /**
     * 描述
     */
    private String des;

    /**
     * 剔除状态
     */
    private Integer delStatus;

    /**
     * 剔除状态
     */
    private String delStatusShow;

    /**
     * 数据剔除状态 (0未剔除，1剔除中，2事件表剔除失败，3记录表剔除失败，4剔除失败，5数据已剔除)
     */
    private Integer delDataStatus;

    /**
     * 数据剔除状态
     */
    private String delDataStatusShow;

    private Date aProTime;

    private String aProUserName;

    public static String getDelStatusShowDetail(Integer delStatus){
        if (delStatus == null){
            return "";
        }
        switch (delStatus){
            case 0:
                return "未提交";
            case 1:
                return "已提交";
            case 2:
                return "已通过";
            case 3:
                return "已驳回";
            default:return "未知状态";
        }
    }

    public static String getDelDataStatusShowDetail(Integer delDataStatus){
        if (delDataStatus == null){
            return "";
        }
        switch (delDataStatus){
            case 0:
                return "未剔除";
            case 1:
                return "剔除中";
            case 2:
                return "事件表剔除失败";
            case 3:
                return "记录表剔除失败";
            case 4:
                return "剔除失败";
            case 5:
                return "数据已剔除";
            default:return "未知状态";
        }
    }
}