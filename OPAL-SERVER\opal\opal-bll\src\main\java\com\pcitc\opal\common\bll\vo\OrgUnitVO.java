package com.pcitc.opal.common.bll.vo;

import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;
import java.util.Date;

/*
 * 机构单元
 * 模块编号：pcitc_iol_vo_class_OrgUnitVO
 * 作    者：dongsheng.zhao
 * 创建时间：2018-07-30
 * 修改编号：1
 * 描    述：机构单元
 */
public class OrgUnitVO extends BaseResRep implements Serializable {

    private Long orgUnitId;

    private String orgUnitCode;

    private String orgUnitName;

    private String orgUnitAlia;

    private String englishName;

    private Long orgUnitTypeId;

    private String contract;

    private Long orgId;

    private Long parentId;

    private Long rootId;

    private String crtUserId;

    private String crtUserName;

    private Date crtUserDate;

    private String mntUserId;

    private String mntUserName;

    private Date mntUserDate;

    private Integer version;

    private Integer enabled;

    private Integer orderId;

    private String des;

    public Long getOrgUnitId() {
        return orgUnitId;
    }

    public void setOrgUnitId(Long orgUnitId) {
        this.orgUnitId = orgUnitId;
    }

    public String getOrgUnitCode() {
        return orgUnitCode;
    }

    public void setOrgUnitCode(String orgUnitCode) {
        this.orgUnitCode = orgUnitCode;
    }

    public String getOrgUnitName() {
        return orgUnitName;
    }

    public void setOrgUnitName(String orgUnitName) {
        this.orgUnitName = orgUnitName;
    }

    public String getOrgUnitAlia() {
        return orgUnitAlia;
    }

    public void setOrgUnitAlia(String orgUnitAlia) {
        this.orgUnitAlia = orgUnitAlia;
    }

    public Long getOrgUnitTypeId() {
        return orgUnitTypeId;
    }

    public void setOrgUnitTypeId(Long orgUnitTypeId) {
        this.orgUnitTypeId = orgUnitTypeId;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getRootId() {
        return rootId;
    }

    public void setRootId(Long rootId) {
        this.rootId = rootId;
    }

    public String getCrtUserId() {
        return crtUserId;
    }

    public void setCrtUserId(String crtUserId) {
        this.crtUserId = crtUserId;
    }

    public String getCrtUserName() {
        return crtUserName;
    }

    public void setCrtUserName(String crtUserName) {
        this.crtUserName = crtUserName;
    }

    public Date getCrtUserDate() {
        return crtUserDate;
    }

    public void setCrtUserDate(Date crtUserDate) {
        this.crtUserDate = crtUserDate;
    }

    public String getMntUserId() {
        return mntUserId;
    }

    public void setMntUserId(String mntUserId) {
        this.mntUserId = mntUserId;
    }

    public String getMntUserName() {
        return mntUserName;
    }

    public void setMntUserName(String mntUserName) {
        this.mntUserName = mntUserName;
    }

    public Date getMntUserDate() {
        return mntUserDate;
    }

    public void setMntUserDate(Date mntUserDate) {
        this.mntUserDate = mntUserDate;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
