package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.MobileListRepositoryCustom;
import com.pcitc.opal.pm.pojo.MobileList;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MobileListRepositoryImpl extends BaseRepository<MobileList, Long> implements MobileListRepositoryCustom {

    @Override
    public PaginationBean<MobileListDTO> getMobileListPage(Long factoryId, Long workshopId, String unitCode, String name, String mobile, Pagination page) {
        try {
            String hql = "select new com.pcitc.opal.pm.dao.imp.MobileListDTO\n" +
                    "(t.mobileListId,f.sname,w.sname,u.sname,t.name,t.mobile,t.crtDate,t.crtUserName,t.mntDate,t.mntUserName)\n" +
                    "from MobileList t \n" +
                    "left join t.factory f \n" +
                    "left join t.workshop w\n" +
                    "left join t.unit u \n" +
                    "	where 1=1 \n";
            Map<String, Object> paramList = new HashMap<String, Object>();

            if (factoryId != null) {
                hql += "	and t.factoryId =(:factoryId) \n";
                paramList.put("factoryId", factoryId);
                if (workshopId != null) {
                    hql += "	and t.workshopId =(:workshopId) \n";
                    paramList.put("workshopId", workshopId);
                    if (unitCode != null && unitCode != "") {
                        hql += "	and t.unitCode =(:unitCode) \n";
                        paramList.put("unitCode", unitCode);
                    }
//                    else {
//                        hql+="	and t.unitCode is null \n";
//                    }
                }
//
            }

            if (name != null && name != "") {
                hql += (" and upper(t.name) like upper(:name) escape '/' ");
                paramList.put("name", "%" + this.sqlLikeReplace(name) + "%");
            }
            if (mobile != null && mobile != "") {
                hql += (" and upper(t.mobile) like upper(:mobile) escape '/' ");
                paramList.put("mobile", "%" + this.sqlLikeReplace(mobile) + "%");
            }
            //List.count
            Query query = this.getEntityManager().createQuery(hql);
            this.setParameterList(query, paramList);
            Long count = Long.valueOf(query.getResultList().size());
            //重写分页
//            page.setPageSize(10);
//            page.setPageNumber(1);
//            page.setTotal(0);
            BaseRepository br = new BaseRepository();
            PaginationBean<MobileListDTO> bean = br.findCusTomAll(this.getEntityManager(), page, count, hql, paramList, MobileListDTO.class);
            return bean;
        } catch (Exception ex) {
            throw ex;
        }
    }


    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult deleteMobileList(Long[] mobileIds) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            String hql = " from MobileList t " +
                    "where 1=1 " +
                    "and t.mobileListId in (:mobileListId)";
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> mobileListIdList = Arrays.asList(mobileIds);
            paramList.put("mobileListId", mobileListIdList);
            TypedQuery<MobileList> query = getEntityManager().createQuery(hql, MobileList.class);
            this.setParameterList(query, paramList);
            List<MobileList> mobileListList = query.getResultList();
            mobileListList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addMobileList(MobileList mobileList) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        String del = mobileList.getMobile();
        String name = mobileList.getName();
        int len = name.length();

        try {
            if (name.getBytes().length > 200) {
                commonResult.setIsSuccess(false);
                commonResult.setMessage("该电话本姓名长度超限！");
                return commonResult;
            }
            if (del.length() != 11) {
                commonResult.setMessage("该电话本手机号不合法！");
                return commonResult;
            }
            for (int k = del.length(); k-- > 0; ) {
                int chr = del.charAt(k);
                if (chr < 48 || chr > 57) {
                    commonResult.setMessage("该电话本手机号不合法！");
                    return commonResult;
                }
            }
            this.getEntityManager().persist(mobileList);
            commonResult.setResult(mobileList);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult updateMobileList(MobileList mobileList) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        String del = mobileList.getMobile();
        try {
            if (del.length() != 11) {
                commonResult.setMessage("该电话本手机号不合法！");
                return commonResult;
            }
            for (int k = del.length(); -k >= 0; ) {
                int chr = del.charAt(k);
                if (chr < 48 || chr > 57) {
                    commonResult.setMessage("该电话本手机号不合法！");
                    return commonResult;
                }
            }
            getEntityManager().merge(mobileList);
            commonResult.setResult(mobileList);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    @Override
    public List<String> getDelList() {
        try {
            String hql = "select t.mobile from MobileList t\n";
            Map<String, Object> paramList = new HashMap<String, Object>();

            //List.count
            Query query = this.getEntityManager().createQuery(hql);
            this.setParameterList(query, paramList);
            List<String> resultList = query.getResultList();
            return resultList;
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public List<MobileListDTO> getMobileListExport(Long factoryId, Long workshopId, String unitCode, String name, String mobile) {
        try {
            String hql = "select new com.pcitc.opal.pm.dao.imp.MobileListDTO\n" +
                    "(t.mobileListId,f.sname,w.sname,u.sname,t.name,t.mobile,t.crtDate,t.crtUserName,t.mntDate,t.mntUserName)\n" +
                    "from MobileList t \n" +
                    "left join t.factory f \n" +
                    "left join t.workshop w\n" +
                    "left join t.unit u \n" +
                    "	where 1=1 \n";
            Map<String, Object> paramList = new HashMap<String, Object>();

            if (factoryId != null) {
                hql += "	and t.factoryId =(:factoryId) \n";
                paramList.put("factoryId", factoryId);
                if (workshopId != null) {
                    hql += "	and t.workshopId =(:workshopId) \n";
                    paramList.put("workshopId", workshopId);
                    if (unitCode != null) {
                        hql += "	and t.unitCode =(:unitCode) \n";
                        paramList.put("unitCode", unitCode);
                    } else {
                        hql += "	and t.unitCode is null \n";
                    }
                } else {
                    hql += "	and t.workshopId is null \n";
                    hql += "	and t.unitCode is null \n";
                }
            }

            if (name != null && name != "") {
                hql += (" and upper(t.name) like upper(:name) escape '/' ");
                paramList.put("name", "%" + this.sqlLikeReplace(name) + "%");
            }
            if (mobile != null && mobile != "") {
                hql += (" and upper(t.mobile) like upper(:mobile) escape '/' ");
                paramList.put("mobile", "%" + this.sqlLikeReplace(mobile) + "%");
            }
            //List.count
            Query query = this.getEntityManager().createQuery(hql);
            this.setParameterList(query, paramList);
            List<MobileListDTO> resultList = query.getResultList();
            return resultList;
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public List<MobileList> getMobileList(Long factoryIds, Long workshopIds, String unitIds, String name, String phoneNum) {
        try {
            StringBuilder hql = new StringBuilder(" from MobileList ml inner join fetch ml.factory f left join fetch ml.workshop w where 1=1 ");
            Map<String, Object> paramList = new HashMap<String, Object>();

            if (null != factoryIds ) {
                hql.append(" and ml.factoryId =:factoryIds");
                paramList.put("factoryIds", factoryIds);
            }
            if (null != workshopIds ) {
                hql.append(" and ml.workshopId =:workshopIds ");
                //paramList.put("workshopIds", Arrays.asList(workshopIds));
                paramList.put("workshopIds", workshopIds);
            }
            if (null != unitIds && !"".equals(unitIds)) {
                hql.append(" and ml.unitCode =:unitIds ");
                paramList.put("unitIds", unitIds);
            }
            if (null != name){
                hql.append(" and ml.name like :name ");
                paramList.put("name","%"+ this.sqlLikeReplace(name) +"%");
            }
            if (null != phoneNum){
                hql.append("  and ml.mobile like :mobile ");
                paramList.put("mobile", "%" + this.sqlLikeReplace(phoneNum) + "%");
            }

            hql.append(" order by f.sortNum,f.sname,w.sortNum,w.sname,ml.name,ml.mobile");
            TypedQuery<MobileList> query = getEntityManager().createQuery(hql.toString(), MobileList.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public List<MobileList> getMobileListByIds(List<Long> mobileIds) {
        try {
            StringBuilder hql = new StringBuilder(" from MobileList ml  where ml.mobileListId in (:mobileIds) ");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("mobileIds", mobileIds);

            TypedQuery<MobileList> query = getEntityManager().createQuery(hql.toString(), MobileList.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }
}
