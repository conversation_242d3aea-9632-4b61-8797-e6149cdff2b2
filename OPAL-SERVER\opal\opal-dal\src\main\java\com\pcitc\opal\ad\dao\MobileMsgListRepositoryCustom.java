package com.pcitc.opal.ad.dao;

import com.pcitc.opal.ad.pojo.MobileMsgList;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

import java.util.Date;

public interface MobileMsgListRepositoryCustom {
    CommonResult add(MobileMsgList mobileMsgList);

    PaginationBean<MobileMsgList> getAlarmMsgConfig(String[] unitCodes, Long[] prdtCellIds, String tag, Integer status, Date startSendTime, Date endSendTime, Pagination page);
}
