package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.UnitPersonRepositoryCustom;
import com.pcitc.opal.pm.pojo.UnitPerson;

import javax.persistence.Query;

import java.util.*;

/*
 * 装置人员信息实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_UnitPersonRepositoryImpl
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/18
 * 修改编号：1
 * 描    述：装置人员信息实体的Repository实现
 */
public class UnitPersonRepositoryImpl extends BaseRepository<UnitPerson, Long> implements UnitPersonRepositoryCustom {

    /**
     * 校验数据
     *
     * <AUTHOR> 2018-12-19
     * @param unitPerson 装置人员实体
     * @return     返回结果信息类
     */
    @Override
    public CommonResult unitValidation(UnitPerson unitPerson) {
        CommonResult commonResult = new CommonResult();
        try {
            //“装置编码”唯一性校验，提示“此装置编码已存在！”
            StringBuilder jpql = new StringBuilder(
                    "from UnitPerson t where t.unitId=:unitId and t.unitPersonId<>:unitPersonId ");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("unitId", unitPerson.getUnitId());
            paramList.put("unitPersonId", unitPerson.getUnitPersonId()==null?0:unitPerson.getUnitPersonId());

            long count = this.getCount(jpql.toString(), paramList);
            if (count > 0) {
                throw new Exception("此装置编码已存在！");
            }
        } catch (Exception e) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(e.getMessage());
        }
        return commonResult;
    }

    /**
     * 获取所有已经启用的装置人员信息集合
     *
     * @return 已经启用的装置人员信息集合
     * <AUTHOR> 2017-10-18
     */
    @Override
    public List<UnitPerson> getUnitPerson() {
        List<UnitPerson> unitEntityList = new ArrayList<UnitPerson>();
        try {
            //获取所有已启用的装置人员信息列表
            String eventTypeHql = "from UnitPerson e where e.inUse=:inUse ";
            unitEntityList = this.getEntityManager().createQuery(eventTypeHql, UnitPerson.class).setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex()).getResultList();
        } catch (Exception ex) {
            throw ex;
        }
        return unitEntityList;
    }

    /**
     * 保存装置人员信息表
     *
     * @param unitPerson
     * <AUTHOR> 2017-12-11
     */
    @Override
    public CommonResult saveUnitPerson(UnitPerson unitPerson) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(unitPerson);
            commonResult.setResult(unitPerson);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    /**
     * 获取单个装置人员信息表
     *
     * @param unitPersonId
     * @return
     * <AUTHOR> 2017-12-11
     */
    @Override
    public UnitPerson getSingleUnitPerson(Long unitPersonId) {
        try {
           return getEntityManager().find(UnitPerson.class,unitPersonId);
        } catch (Exception ex) {
            throw ex;
        }
    }
    /**
     * 根据装置ID获取装置人员信息
     *
     * @param unitCode 装置编码
     * @return
     * <AUTHOR> 2017-12-11
     */
    @Override
    public UnitPerson getUnitPersonByUnitId(String unitCode) {
        try {
            String hql = "from UnitPerson u where u.unitId=:unitId";
            return this.getEntityManager().createQuery(hql, UnitPerson.class).setParameter("unitId", unitCode).getResultList().stream().findFirst().orElse(new UnitPerson());
        } catch (Exception ex) {
            throw ex;
        }
    }
    /**
     * 更新装置人员信息
     *
     * @param unitPerson 装置人员信息
     * @return
     * <AUTHOR> 2017-12-11
     */
    @Override
    public CommonResult updateUnitPerson(UnitPerson unitPerson) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(unitPerson);
            commonResult.setResult(unitPerson);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }
    /**
     * 根据装置ID删除装置人员信息
     *
     * @param unitCodes 装置编码集合
     * @return
     * <AUTHOR> 2017-12-11
     */
    @SuppressWarnings("unchecked")
	@Override
    public CommonResult deleteByUnitId(String[] unitCodes) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        if(unitCodes==null){
            commonResult.setIsSuccess(false);
            return commonResult;
        }
        try {
            String hql = " from UnitPerson u where u.unitId in (:unitCodes)";
            Map<String, Object> paramList = new HashMap<>();

            paramList.put("unitCodes", Arrays.asList(unitCodes));

            Query query = getEntityManager().createQuery(hql, UnitPerson.class);
            this.setParameterList(query, paramList);
            List<UnitPerson> unitPersonList = query.getResultList();
            unitPersonList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

}
