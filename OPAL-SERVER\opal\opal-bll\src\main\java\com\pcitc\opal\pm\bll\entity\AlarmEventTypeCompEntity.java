package com.pcitc.opal.pm.bll.entity;


import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 事件类型对照配置Entity
 * 模块编号：pcitc_opal_bll_class_AlarmEventTypeCompEntity
 * 作    者：xuelei.wang
 * 创建时间：2018-03-30 08:47:57
 * 修改编号：1
 * 描    述：事件类型对照配置Entity
 */
public class AlarmEventTypeCompEntity extends BasicEntity {

    /**
     * 事件类型对照配置ID
     */
    private Long alarmEventTypeCompId;

    /**
     * DCS编码ID
     */
    private Long dcsCodeId;
    /**
     * DCS名称
     */
    private String dcsName;
    /**
     * 源事件类型
     */
    private String eventTypeSource;

    /**
     * 源事件名称
     */
    private String eventNameSource;

    /**
     * 事件类型ID
     */
    private Long eventTypeId;
    /**
     * 事件名称
     */
    private String eventTypeName;

    public Long getDcsCodeId() {
        return dcsCodeId;
    }

    public void setDcsCodeId(Long dcsCodeId) {
        this.dcsCodeId = dcsCodeId;
    }

    public String getEventTypeSource() {
        return eventTypeSource;
    }

    public void setEventTypeSource(String eventTypeSource) {
        this.eventTypeSource = eventTypeSource;
    }

    public String getEventNameSource() {
        return eventNameSource;
    }

    public void setEventNameSource(String eventNameSource) {
        this.eventNameSource = eventNameSource;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public Long getAlarmEventTypeCompId() {
        return alarmEventTypeCompId;
    }

    public void setAlarmEventTypeCompId(Long alarmEventTypeCompId) {
        this.alarmEventTypeCompId = alarmEventTypeCompId;
    }

    public String getDcsName() {
        return dcsName;
    }

    public void setDcsName(String dcsName) {
        this.dcsName = dcsName;
    }

    public String getEventTypeName() {
        return eventTypeName;
    }

    public void setEventTypeName(String eventTypeName) {
        this.eventTypeName = eventTypeName;
    }
}

