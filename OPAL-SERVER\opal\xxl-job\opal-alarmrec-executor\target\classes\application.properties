server.port=8083
spring.jpa.show-sql=true

#主数据库连接信息
spring.datasource.url=**************************************************************************************************************************************************
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=Mes_opal123

### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl.job.admin.addresses=http://**************:8080/xxl-job-admin

### 执行器通讯TOKEN [选填]：非空时启用；
xxl.job.accessToken=default_token

### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
xxl.job.executor.appname=xxl-job-executor-sample
### 调度中心部署根地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
xxl.job.executor.address=
### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
xxl.job.executor.ip=
### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
xxl.job.executor.port=9998
### xxl-job executor log-path
xxl.job.executor.logpath=/data/applogs/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logRetentionDays=30



#AAA使用版本   old:老版本(镇海),new:新版本,promace:promace
aaa_version=new


#AAA 班组WebService地址(后缀不用带"/")
aaa.shiftcalendarsvc_address_base=http://**************:8080
#PORMACE 班组
promace.imp.shift.base.url=http://shift.wsm.qlsh.promace.sinopec.com

#工厂模型-根Url
#factorymodel.base.url=http://**************:30625/FactoryModelService/rents/MESTEST/
factorymodel.base.url=http://pm.wsm.qlsh.promace.sinopec.com
fm_bizCode=qlsh
fm_unit_type_code=plants
factorymodel.bizCode=${fm_bizCode}
fm_factoryTypeCode=1005
factorymodel.factoryTypeCode=${fm_factoryTypeCode}
fm_workshopTypeCode=1007
factorymodel.workshopTypeCode=${fm_workshopTypeCode}


#运行环境类型promace,other
runtime_type=other
#是否开启装置数据权限 1：开启，0：不开启
aaa_auth=1 
# 获取工艺报警记录
# 测试
#alarm.info.url=http://**************:8080/IP/WebService/OpeAlarmCraftIndexService.asmx/getAlarmInfo
# 正式
alarm.info.url=http://**************:8811/IP/WebService/OpeAlarmCraftIndexService.asmx/getAlarmInfo
# 获取工艺装置停工信息
# 测试
#unit.stop.url=http://**************:8080/IP/WebService/OpeAlarmCraftIndexService.asmx/getUnitStopRec
# 正式
unit.stop.url=http://**************:8811/IP/WebService/OpeAlarmCraftIndexService.asmx/getUnitStopRec

ddzh.push.model.uri=https://newiplant.tjsh.sinopec.com/ddzh/warn/api/other/pushapi/model

ddzh.push.record.uri=https://newiplant.tjsh.sinopec.com/ddzh/warn/api/other/pushapi/record