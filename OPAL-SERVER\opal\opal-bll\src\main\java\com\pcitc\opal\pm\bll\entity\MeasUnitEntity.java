package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;


/*
 * 计量单位
 * 模块编号：pcitc_opal_bll_class_MeasUnitEntity
 * 作       者：jiangtao.xue
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描       述：计量单位
 */
public class MeasUnitEntity extends BasicEntity {
	
	/**
	 * 计量单位ID
	 */
	private Long measUnitId;
	
	/**
	 * 名称
	 */
	private String name;

	/**
	 * 符号
	 */
	private String sign;
 
	/**
	 * 排序
	 */
	private Integer sortNum;
	
	/**
	 * 描述
	 */
	private String des;

	public Long getMeasUnitId() {
		return measUnitId;
	}

	public void setMeasUnitId(Long measUnitId) {
		this.measUnitId = measUnitId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}
	
	public Integer getSortNum() {
		return sortNum;
	}

	public void setSortNum(Integer sortNum) {
		this.sortNum = sortNum;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}
}
