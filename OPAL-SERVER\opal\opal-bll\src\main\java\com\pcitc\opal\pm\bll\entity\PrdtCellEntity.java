package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 生产单元实体  
 * 模块编号：pcitc_opal_bll_class_PrdtCellEntity
 * 作       者：zheng.yang
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描       述：生产单元实体    
 */
public class PrdtCellEntity extends BasicEntity {

	private Long prdtCellId;

	private String unitId;

	/**
	 * 装置名称
	 */
	private String unitName;

	private String name;

	private String sname;

	private Integer sortNum;

	private String des;
	private Integer companyId;

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	public Long getPrdtCellId() {
		return prdtCellId;
	}

	public void setPrdtCellId(Long prdtCellId) {
		this.prdtCellId = prdtCellId;
	}

	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSname() {
		return sname;
	}

	public void setSname(String sname) {
		this.sname = sname;
	}

	public Integer getSortNum() {
		return sortNum;
	}

	public void setSortNum(Integer sortNum) {
		this.sortNum = sortNum;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}

}
