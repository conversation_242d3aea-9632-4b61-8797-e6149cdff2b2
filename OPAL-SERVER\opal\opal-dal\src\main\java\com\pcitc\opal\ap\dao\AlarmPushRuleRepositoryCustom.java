package com.pcitc.opal.ap.dao;

import com.pcitc.opal.ap.pojo.AlarmPushRule;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.AlarmPointGroup;

import java.util.List;

/*
 * 报警知识管理实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_AlarmKnowlgManagmtRepositoryCustom
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理实体的Repository的JPA自定义接口
 */
public interface AlarmPushRuleRepositoryCustom {

    PaginationBean<AlarmPushRule> getAlarmPushRulePage(String name, Integer companyId,  Integer pushType, Pagination page);

    CommonResult addAlarmPushRule(AlarmPushRule alarmPushRule);

    CommonResult deleteAlarmPushRule(Long[] ids);

    CommonResult updateAlarmPushRule(AlarmPushRule alarmPushRule);

    List<AlarmPushRule> findAllNameRule(Integer pushType);


    /**
     * 查询已启用的报警推送配置
     *
     * @param companyId 企业id
     */
    List<AlarmPushRule> getAlarmPushRule(Integer companyId);
}
