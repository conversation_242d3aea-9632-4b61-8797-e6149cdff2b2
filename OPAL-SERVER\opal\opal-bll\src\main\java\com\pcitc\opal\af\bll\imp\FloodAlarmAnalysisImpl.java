package com.pcitc.opal.af.bll.imp;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pcitc.opal.aa.bll.entity.AlarmNumberAssessDataEntity;
import com.pcitc.opal.ad.bll.entity.AlarmEventViewEntity;
import com.pcitc.opal.ad.dao.AlarmEventDAO;
import com.pcitc.opal.ad.dao.AlarmEventViewRepository;
import com.pcitc.opal.ad.vo.FloodAlarmPointCountVO;
import com.pcitc.opal.af.bll.FloodAlarmAnalysisService;
import com.pcitc.opal.af.bll.entity.*;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.ShiftDateCalculator;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.pm.bll.entity.PrdtCellEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.entity.UnitPersonEntity;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/*
 * 高频报警分析业务逻辑层接口实现
 * 模块编号：pcitc_opal_bll_class_FloodAlarmAnalysisImpl
 * 作  　者：xuelei.wang
 * 创建时间：2017-11-15
 * 修改编号：1
 * 描    述：高频报警分析业务逻辑层接口实现
 */
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class FloodAlarmAnalysisImpl implements FloodAlarmAnalysisService {

    @Autowired
    private BasicDataService basicDataService;
    @Autowired
    private AlarmEventViewRepository alarmEventViewRepository;
    private long index = 1L;
    private List<UnitPersonEntity> unitPersonEntityList = new ArrayList<>();
    /**
     * 时间格式化
     */
    private static final String FULL_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    private AlarmEventDAO alarmEventDAO;

    /**
     * 根据装置或者生产单元和时间区间获取高频报警清单列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCodes 装置ID集合
     * @param prdtIds   生产单元集合
     * @param dateType  日期类型(day|month|week|hour)
     * @return 高频报警清单列表
     * <AUTHOR> 2017-11-15
     */
    @Override
    public FloodAlarmChartOptionEntity getFloodAlarmList(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, String dateType) throws Exception {
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime);
        CommonEnum.EquipmentTypeEnum equipmentTypeEnum;
        List<UnitEntity> unitList = basicDataService.getUnitList(true);

        if (unitCodes != null && unitCodes.length == 0) unitCodes = null;
        if (prdtIds != null && prdtIds.length == 0) prdtIds = null;

        if (prdtIds != null) {
            equipmentTypeEnum = CommonEnum.EquipmentTypeEnum.PrdtCell;
        } else {
            equipmentTypeEnum = CommonEnum.EquipmentTypeEnum.Unit;
        }

        try {
            //如果装置没有数据,则默认查询全部装置
            if (unitCodes == null || unitCodes.length == 0) {
                unitCodes = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
            }

            if (unitCodes != null)
                unitPersonEntityList = basicDataService.getUnitPersonListByUnitIds(Arrays.asList(unitCodes));
            //1.查询按10分钟间隔的且报警次数大于等于4的所有的数据;
            List<AlarmEventViewEntity> alarmEventList = ObjectConverter.listConverter(alarmEventViewRepository.getFloodAlarmAnalysisList(shiftDateCalculator.getQueryStartTime(), shiftDateCalculator.getQueryEndTime(), unitCodes, prdtIds), AlarmEventViewEntity.class);
            Map<Object, List<AlarmEventViewEntity>> groupMap = new HashMap<>();
            //2.处理装置或者单元名称显示
            if (CommonEnum.EquipmentTypeEnum.PrdtCell.equals(equipmentTypeEnum)) {
                List<PrdtCellEntity> prdtCellEntityList = basicDataService.getAllPrdtCellList();
                alarmEventList.stream().forEach((item) -> {
                    item.setUnitSname(prdtCellEntityList.stream().filter(p ->
                            p.getPrdtCellId().equals(item.getPrdtCellId())).findFirst().orElse(new PrdtCellEntity()).getSname());
                });
                groupMap = alarmEventList.stream().collect(Collectors.groupingBy(AlarmEventViewEntity::getPrdtCellId));
            } else if (CommonEnum.EquipmentTypeEnum.Unit.equals(equipmentTypeEnum)) {
                alarmEventList.stream().forEach(item -> {
                    item.setUnitSname(unitList.stream().filter(u ->
                            u.getStdCode().equals(item.getUnitId())).findFirst().orElse(new UnitEntity()).getSname());
                });
                groupMap = alarmEventList.stream().collect(Collectors.groupingBy(AlarmEventViewEntity::getUnitId));
            }
            List<FloodAlarmChartEntity> resultList = new ArrayList<>();
            //3.进行高频报警分组归类;
            List keys = new ArrayList(groupMap.keySet());
            for (int i = 0; i < keys.size(); i++) {
                index = 1L;
                List<AlarmEventViewEntity> itemList = groupMap.get(keys.get(i));
                getFloodAlarmList(itemList, resultList);
            }

            //4.处理按照不同日期显示图标展示;
            FloodAlarmChartOptionEntity resultEntity = getChartData(resultList, shiftDateCalculator.getDisplayStartTime(), shiftDateCalculator.getDisplayEndTime(), dateType);

            //5.计算列表主表数据;
            Date finalStartTime = shiftDateCalculator.getDisplayStartTime();
            Date finalEndTime = shiftDateCalculator.getDisplayEndTime();
            resultList.stream().forEach(item -> {
                FloodMainTableEntity mainEntity = new FloodMainTableEntity();

                if (CommonEnum.EquipmentTypeEnum.PrdtCell.equals(equipmentTypeEnum)) {
                    mainEntity.setId(item.getList().stream().findFirst().orElse(new FloodAlarmAnalysisEntity()).getViewEntity().getPrdtCellId().toString());
                } else if (CommonEnum.EquipmentTypeEnum.Unit.equals(equipmentTypeEnum)) {
                    mainEntity.setId(item.getList().stream().findFirst().orElse(new FloodAlarmAnalysisEntity()).getViewEntity().getUnitId());
                }
                mainEntity.setName(item.getName());
                if (item.getName() != null && item.getName() != "") {
                    mainEntity.setCounts(getTimes(item.getName(), resultList));
                    mainEntity.setBloodAlarmCounts(getFloodTimes(item.getName(), resultList));
                    mainEntity.setTimePercent(getTimePercent(getFloodTimesCount(item.getName(), resultList), finalStartTime, finalEndTime));
                    if (resultEntity.getMainTableEntityList().stream().filter(re -> re.getName().equals(mainEntity.getName())).count() == 0)
                        resultEntity.getMainTableEntityList().add(mainEntity);
                    //6.计算装置或者单元下的二级列表
                    mainEntity.setSubList(getSubList(mainEntity.getId(), item.getName(), resultList));
                }
            });
            resultEntity.setFloodData(resultList);
            index = 1L;
            return resultEntity;
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public Page<FloodAlarmPointCountVO> getAlarmStatistics(List<String> unitCodes, Date startTime, Date endTime, List<Long> prdtIds, Pagination page) throws Exception {
        //如果装置没有数据,则默认查询全部装置
        if (CollectionUtils.isEmpty(unitCodes)) {
            List<UnitEntity> unitList = basicDataService.getUnitList(true);
            unitCodes = unitList.stream().map(UnitEntity::getStdCode).collect(Collectors.toList());
        }
        Page<FloodAlarmPointCountVO> pager = new Page<FloodAlarmPointCountVO>(page.getPageNumber(),page.getPageSize());
        Page<FloodAlarmPointCountVO> vos =  alarmEventDAO.selectFloodAlarmPointCount(pager, unitCodes, prdtIds, startTime, endTime);
        return  vos;
    }

    @Override
    public List<FloodAlarmPointCountVO> getAlarmStatistics(List<String> unitCodes, Date startTime, Date endTime, List<Long> prdtIds) throws Exception {
        //如果装置没有数据,则默认查询全部装置
        if (CollectionUtils.isEmpty(unitCodes)) {
            List<UnitEntity> unitList = basicDataService.getUnitList(true);
            unitCodes = unitList.stream().map(UnitEntity::getStdCode).collect(Collectors.toList());
        }
        List<FloodAlarmPointCountVO> vos =  alarmEventDAO.selectFloodAlarmPointCount(unitCodes, prdtIds, startTime, endTime);
        vos.forEach(t -> t.setMonitorTypeStr(CommonEnum.MonitorTypeEnum.getName(t.getMonitorType())));
        return  vos;
    }

    /**
     * 获取高频报警二级列表是数据
     *
     * @param id
     * @param name
     * @param resultList
     * @return 高频报警二级列表数据
     * <AUTHOR> 2017-12-7
     */
    private List<FloodSubTableEntity> getSubList(String id, String name, List<FloodAlarmChartEntity> resultList) {
        List<FloodSubTableEntity> returnList = new ArrayList<>();
        //2)二级列表为各个高频报警的情况，显示为“序号”+“名称”+“开始时间”+“结束时间”+“时间期间（分钟）”+“报警数”+“峰值报警率”。
        //a>“名称”显示本次高频报警的次序号。例如“Flood1”
        //b>“开始时间”，显示格式为YYYY-MM-DD hh-mm-ss；本次高频报警的开始时间。
        //c>“结束时间”显示格式为YYYY-MM-DD hh-mm-ss；本次高频报警的结束时间。
        //d>“时间期间”=高频报警结束时间-高频报警开始时间，转为为分钟。
        //e>“报警数”为本次高频报警所有时间间隔（n个10分钟，n>=1）报警数之和。
        //f>“峰值报警率”本次高频报警所有时间间隔（n个10分钟，n>=1）内计算每个10分钟的报警个数，取得报警数最大的值即为峰值报警率。
        resultList.stream().filter(item -> item.getName().equals(name)).forEach(entity -> {
            FloodSubTableEntity subEntity = new FloodSubTableEntity();
            subEntity.setId(id);
            subEntity.setPrdtId(entity.getList().stream().findFirst().orElse(new FloodAlarmAnalysisEntity()).getViewEntity().getPrdtCellId());
            subEntity.setUnitId(entity.getList().stream().findFirst().orElse(new FloodAlarmAnalysisEntity()).getViewEntity().getUnitId());
            subEntity.setName(entity.getName());
            subEntity.setFloodName(entity.getFloodName());
            subEntity.setStartTime(entity.getList().stream().reduce((item1, item2) -> item1.getViewEntity().getAlarmTime().getTime() < item2.getViewEntity().getAlarmTime().getTime() ? item1 : item2).orElse(new FloodAlarmAnalysisEntity()).getViewEntity().getAlarmTime());
            subEntity.setEndTime(entity.getList().stream().reduce((item1, item2) -> item1.getViewEntity().getAlarmTime().getTime() > item2.getViewEntity().getAlarmTime().getTime() ? item1 : item2).orElse(new FloodAlarmAnalysisEntity()).getViewEntity().getAlarmTime());
            subEntity.setEndTime(DateUtils.addMinutes(subEntity.getEndTime(), 10));
            Long size = (subEntity.getEndTime().getTime() - subEntity.getStartTime().getTime()) / (1000 * 60);
            subEntity.setDatePeriod(size.equals(0L) ? 10L : size);
            subEntity.setAlarmCounts(entity.getList().stream().mapToLong(item -> item.getViewEntity().getAlarmTimes()).sum());
            Long max = entity.getList().stream().mapToLong(item -> item.getViewEntity().getAlarmTimes()).max().orElse(0L);
            String unitCode = entity.getList().stream().findFirst().orElse(new FloodAlarmAnalysisEntity()).getViewEntity().getUnitId();
            try {
                Long operNum = unitPersonEntityList.stream().filter(item -> unitCode.equals(item.getUnitId())).findFirst().orElse(new UnitPersonEntity()).getOperNum();
                if (operNum == null || operNum == 0L) {
                    operNum = 1L;
                }
                DecimalFormat decimalFormat = new DecimalFormat("#0.00");
                subEntity.setAlarmRate(decimalFormat.format(max / (operNum * 1.000)));
            } catch (Exception e) {
                e.printStackTrace();
            }
            returnList.add(subEntity);

            //9.计算其下的三级列表
            /// (3)三级列表为每次高频报警各个十分钟的报警详情，显示为“序号”+“时间”+“报警数”。
            /// a>“时间”为每次高频报警各个10分钟的开始时间。
            /// b>“报警数”为每次高频报警各个10分钟的报警数。
            entity.getList().stream().forEach(en -> {
                FloodThirdTableEntity thirdEntity = new FloodThirdTableEntity();
                thirdEntity.setStartTime(en.getViewEntity().getAlarmTime());
                thirdEntity.setAlarmCounts(en.getViewEntity().getAlarmTimes());
                subEntity.getThirdTable().add(thirdEntity);
            });
            subEntity.setThirdTable(subEntity.getThirdTable().stream().sorted(Comparator.comparing(FloodThirdTableEntity::getStartTime)).collect(Collectors.toList()));
        });

        return returnList;
    }

    /**
     * 获取时间百分比(%)
     *
     * @param bloodAlarmCounts
     * @param startTime
     * @param endTime
     * @return 时间百分比字符串
     * <AUTHOR> 2017-12-7
     */
    private String getTimePercent(Long bloodAlarmCounts, Date startTime, Date endTime) {
        return String.format("%.2f", ((bloodAlarmCounts * 10 * 60) / ((endTime.getTime() - startTime.getTime()) / 1000.0)) * 100);
    }

    /**
     * 获取高频报警数
     *
     * @param name       名称
     * @param resultList 高频报警集合
     * @return 高频报警数量
     * <AUTHOR> 2017-12-7
     */
    private Long getFloodTimes(String name, List<FloodAlarmChartEntity> resultList) {
        return (long) resultList.stream().filter((item) -> item.getName().equals(name)).collect(Collectors.toList()).size();
    }

    /**
     * 获取高频报警时间段数
     *
     * @param name       名称
     * @param resultList 高频报警集合
     * @return 高频报警数量
     * <AUTHOR> 2017-12-7
     */
    private Long getFloodTimesCount(String name, List<FloodAlarmChartEntity> resultList) {
        return resultList.stream().filter((item) -> item.getName().equals(name)).collect(Collectors.toList()).stream().mapToLong(e -> e.getList().size()).sum();
    }

    /**
     * 获取一次高频报警的报警总次数
     *
     * @param name
     * @param resultList
     * @return
     * <AUTHOR> 2017-12-7
     */
    private Long getTimes(String name, List<FloodAlarmChartEntity> resultList) {

        return resultList.stream().filter((item) -> item.getName().equals(name)).collect(Collectors.summingLong(item -> item.getList().stream().mapToLong(a -> a.getViewEntity().getAlarmTimes()).sum()));

    }


    /***
     * 递归获取高频报警列表
     *
     * @param alarmEventList
     * @param resultList
     * @throws Exception
     * <AUTHOR> 2017-11-16
     */
    private void getFloodAlarmList(List<AlarmEventViewEntity> alarmEventList, List<FloodAlarmChartEntity> resultList) throws Exception {
        if (alarmEventList.size() != 0) {
            //1.顺序遍历,找出第一个报警次数大于10的记录;
            for (int i = 0; i < alarmEventList.size(); i++) {
                //如果报警数量大于10
                if (alarmEventList.get(i).getAlarmTimes() > 10) {
                    List<AlarmEventViewEntity> floodList = new ArrayList<>();
                    floodList.add(alarmEventList.get(i));
                    List<AlarmEventViewEntity> subList = alarmEventList.subList(getIndex(i, alarmEventList), alarmEventList.size());
                    List<AlarmEventViewEntity> returnList = getFloodAlarmPeriod(alarmEventList.get(i), subList, floodList);
                    List<FloodAlarmAnalysisEntity> floodEntityList = new ArrayList<>();
                    for (int j = 0; j < floodList.size(); j++) {
                        FloodAlarmAnalysisEntity floodEntity = new FloodAlarmAnalysisEntity();
                        if ((j + 1) < floodList.size()) {
                            //设置连线点
                            floodEntity.setNextEntity(ObjectConverter.entityConverter(floodList.get(j + 1), AlarmEventViewEntity.class));
                        }
                        floodEntity.setViewEntity(ObjectConverter.entityConverter(floodList.get(j), AlarmEventViewEntity.class));
                        floodEntityList.add(floodEntity);
                    }
                    if (floodEntityList.size() != 0)
                        resultList.add(new FloodAlarmChartEntity(floodEntityList, getFloodName(), getUnitOrPrdtCellName(floodEntityList)));

                    //如果剩余的list集合大于0,则继续寻找下一个高频报警;
                    if (returnList.size() > 0)
                        getFloodAlarmList(returnList, resultList);
                    //结束一次高频报警寻找;
                    return;
                }
            }
        }
    }

    /**
     * 获取装置或者单元名称
     *
     * @param floodEntityList
     * @return
     * <AUTHOR> 2017-12-7
     */
    private String getUnitOrPrdtCellName(List<FloodAlarmAnalysisEntity> floodEntityList) {
        return floodEntityList.stream().findFirst().orElse(new FloodAlarmAnalysisEntity()).getViewEntity().getUnitSname();
    }

    /***
     * 获取截取List的Index
     *
     * @param index
     * @param list
     * @return
     * <AUTHOR> 2017-11-16
     */
    private int getIndex(int index, List<AlarmEventViewEntity> list) {
        if ((index + 1) <= list.size()) return index + 1;
        return index;
    }

    /**
     * 获取一个高频报警区间
     *
     * @param list
     * @param floodList
     * @return 高频报警一个区间结果集合
     * <AUTHOR> 2017-11-16
     */
    private List<AlarmEventViewEntity> getFloodAlarmPeriod(AlarmEventViewEntity last, List<AlarmEventViewEntity> list, List<AlarmEventViewEntity> floodList) {
        for (int i = 0; i < list.size(); i++) {
            if (((list.get(i).getAlarmTime().getTime() - last.getAlarmTime().getTime()) > 1000 * 60 * 10)) {
                return list.subList(i, list.size());
            } else {
                //如果报警数量大于等于5
                if (list.get(i).getAlarmTimes() >= 5) {
                    floodList.add(list.get(i));
                } else {
                    return list.subList(getIndex(i, list), list.size());
                }
            }
        }
        //如果找到最后一个还没找到小于5的,则寻找高频报警结束;
        return new ArrayList<>();
    }

    /**
     * 计算高频报警名称
     *
     * @return 返回高频报警名称
     * <AUTHOR> 2017-11-16
     */
    private String getFloodName() {
        return "Flood" + index++;
    }

    /**
     * 获取高频报警图表格式数据
     *
     * @param list      高频报警数据
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param dateType  日期类型
     * @return
     * <AUTHOR> 2017-12-7
     */
    private FloodAlarmChartOptionEntity getChartData(List<FloodAlarmChartEntity> list, Date startTime, Date endTime, String dateType) {
        FloodAlarmChartOptionEntity optionEntity = new FloodAlarmChartOptionEntity();
        //1.处理x轴日期时间显示
        List<DictionaryEntity> dateList = basicDataService.getChartDateShowPeriod(startTime, endTime, dateType);
        optionEntity.getXaxis().addAll(dateList);
        for (FloodAlarmChartEntity item : list) {
            //2.处理legend
            if (!optionEntity.getLegend().contains(item.getName()))
                optionEntity.getLegend().add(item.getName());

            //3.处理series
            FloodAlarmChartSeriesItemEntity seriesItem = new FloodAlarmChartSeriesItemEntity();
            seriesItem.setName(item.getName());
            seriesItem.setType("line");
            seriesItem.setData(getSeriesItemData(item));
            seriesItem.setFloodName(item.getFloodName());
            seriesItem.setUnitId(item.getList().stream().findFirst().orElse(new FloodAlarmAnalysisEntity()).getViewEntity().getUnitId());
            seriesItem.setPrdtId(item.getList().stream().findFirst().orElse(new FloodAlarmAnalysisEntity()).getViewEntity().getPrdtCellId());
            optionEntity.getList().add(seriesItem);
        }
        //4.计算高频报警总次数
        optionEntity.setCounts((long) list.size());
        return optionEntity;
    }

    /**
     * 按天分组获取Series Item数据;
     *
     * @param entity
     * @return
     * <AUTHOR> 2017-12-7
     */
    private JSONArray getSeriesItemData(FloodAlarmChartEntity entity) {
        JSONArray jsonArray = new JSONArray();
        for (FloodAlarmAnalysisEntity en : entity.getList()) {
            jsonArray.add(new Object[]{DateFormatUtils.format(en.getViewEntity().getAlarmTime(), FULL_DATETIME_PATTERN), en.getViewEntity().getAlarmTimes()});
        }
        return jsonArray;
    }
}
