package com.pcitc.opal.ad.bll;

import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.ad.vo.*;
import com.pcitc.opal.common.CommonResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface AlarmRecService {
    /**
     * 删除响应时间为空的数据
     *
     * @param startDate 开始时间
     * @param end       结束时间
     * @param companyId 企业id
     */
    CommonResult removeRecoveryTimeIsNullByAlarmTime(String startDate, String end, Integer companyId);

    void addAlarmRecByInfo(List<AlarmRec> list);

    int getAlarmRecCountByStartTime(AlarmRec alarmRec, String companyId);

    int modifyAlarmRecRecoveryTime(AlarmRec alarmRec, String companyId);

    Object getAlarmRecMaxAlarmTime(Object[] o, String companyId);

    int modifyResponseTimeByStartTime(Object[] o, String companyId);

    int getCountByAlarmRec(AlarmRec alarmRec, String companyId);


    /**
     * 根据条件更新记录表的响应时间（报警时间小于传入时间）
     *
     * @param alarmRec
     * @param companyId
     * @return
     */
    public int modifyAlarmRecResponseTimeByLessAlarmTime(AlarmRec alarmRec, String companyId);


    /**
     * 更新响应时间
     */
    Integer updateResponseTimeByRec(AlarmRec alarmRec, Integer companyId);


    List<UnitRecNumVO> getUnitRecNum();

    List<PriorityRecNumVO> getPriorityRecNum();

    List<UnitRecCurrentVO> getUnitRecCurrent();

    List<PointRecNumVO> getPointRecNum();

    List<MonitorRecNumVO> getMonitorRecNum();

    String getCraftOpeMonTrendChart(Long alarmRecId);


    List<AlarmAnlyInfoVO> getAnlyByEvent(List<Long> eventIdList);
}
