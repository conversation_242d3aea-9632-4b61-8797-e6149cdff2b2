package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.ad.dao.AlarmEventH1CacheRepository;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.pm.bll.UnclaimDataService;
import com.pcitc.opal.pm.bll.entity.AlarmEventCacheEntity;
import com.pcitc.opal.pm.bll.entity.DcsCodeEntity;
import com.pcitc.opal.pm.dao.UnclaimDataRepository;
import com.pcitc.opal.pm.pojo.AlarmEventCache;
import com.pcitc.opal.common.*;

import com.pcitc.opal.pm.pojo.AlarmEventH1Cache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/*
 * 未分类数据查询业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_UnclaimDataServiceImpl
 * 作       者：xuelei.wang
 * 创建时间：2018-04-16
 * 修改编号：1
 * 描       述：未分类数据查询业务逻辑层实现类
 */
@Component
public class UnclaimDataServiceImpl implements UnclaimDataService {

    /**
     * 实例化数据访问层接口
     */
    @Autowired
    private UnclaimDataRepository repo;
    @Autowired
    private AlarmEventH1CacheRepository h1repo;
    @Autowired
    private BasicDataService basicDataService;

    /**
     * 获取未匹配计量单位列表
     *
     * @return 未匹配计量单位列表
     * <AUTHOR> 2018-04-19
     */
    @Override
    public List<AlarmEventCacheEntity> getCacheMeasUnitList(Long[] dcsCodeIds,String measUnit, Long reason, Date startTime, Date endTime) throws Exception {
        List<AlarmEventCacheEntity> returnList = new ArrayList<>();
        if(dcsCodeIds[0].longValue() == 1201 || dcsCodeIds[0].longValue() == 1202) {
            List<AlarmEventH1Cache> eventCacheList = h1repo.getCacheMeasUnitList(dcsCodeIds, measUnit,reason, startTime, endTime);
            if (eventCacheList.size() >= 1) {
                AlarmEventH1Cache entity = new AlarmEventH1Cache();
                entity.setParameter("-1");
                entity.setMeasUnit("全部");
                eventCacheList.add(0, entity);
            }
            returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
        } else {
            List<AlarmEventCache> eventCacheList = repo.getCacheMeasUnitList(dcsCodeIds, measUnit,reason, startTime, endTime);
            if (eventCacheList.size() >= 1) {
                AlarmEventCache entity = new AlarmEventCache();
                entity.setParameter("-1");
                entity.setMeasUnit("全部");
                eventCacheList.add(0, entity);
            }
            returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
        }
        return returnList;

    }

    /**
     * 获取未匹配优先级列表
     *
     * @param priority  优先级
     * @param dcsIds    DCSID集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 未匹配优先级列表
     * <AUTHOR> 2018-04-17
     */
    @Override
    public List<AlarmEventCacheEntity> getCachePriorityList(String priority, Long[] dcsIds, Date startTime, Date endTime) throws Exception {
        List<AlarmEventCacheEntity> returnList = new ArrayList<>();
        if(dcsIds[0].longValue() == 1201 || dcsIds[0].longValue() == 1202) {
            List<AlarmEventH1Cache> eventCacheList = h1repo.getCachePriorityList(priority,dcsIds,startTime,endTime);
            processShowAllH1(eventCacheList);
            returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
        } else {
            List<AlarmEventCache> eventCacheList = repo.getCachePriorityList(priority,dcsIds,startTime,endTime);
            processShowAll(eventCacheList);
            returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
        }
        return returnList;

    }

    /**
     * 获取未匹配报警标识列表
     *
     * @param alarmFlag  报警标识
     * @param dcsIds    DCSID集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 未匹配报警标识列表
     * <AUTHOR> 2018-04-17
     */
    @Override
    public List<AlarmEventCacheEntity> getCacheAlarmFlagList(String alarmFlag, Long[] dcsIds, Date startTime, Date endTime) throws Exception {
        List<AlarmEventCacheEntity> returnList = new ArrayList();
        if(dcsIds[0].longValue() == 1201 || dcsIds[0].longValue() == 1202) {
            List<AlarmEventH1Cache> eventCacheList = h1repo.getCacheAlarmFlagList(alarmFlag,dcsIds,startTime,endTime);
            processShowAllH1(eventCacheList);
            returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
        } else {
            List<AlarmEventCache> eventCacheList = repo.getCacheAlarmFlagList(alarmFlag,dcsIds,startTime,endTime);
            processShowAll(eventCacheList);
            returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
        }
        return returnList;
    }

    /**
     * 获取未匹配报警点列表
     *
     *
     * @param alarmPoint 报警点
     * @param dcsIds    DCSID集合
     * @param reason    不一致原因
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 未匹配报警点列表
     * <AUTHOR> 2018-04-26
     */
    public List<AlarmEventCacheEntity> getCacheAlarmPointList(String alarmPoint, Long[] dcsIds,Long reason, Date startTime, Date endTime) throws Exception{
        List<AlarmEventCacheEntity> returnList = new ArrayList();
        if(dcsIds[0].longValue() == 1201 || dcsIds[0].longValue() == 1202) {
            List<AlarmEventH1Cache> eventCacheList = h1repo.getCacheAlarmPointList(alarmPoint,dcsIds,startTime,endTime);
            processShowAllH1(eventCacheList);
            returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
        } else {
            List<AlarmEventCache> eventCacheList = repo.getCacheAlarmPointList(alarmPoint,dcsIds,reason,startTime,endTime);
            processShowAll(eventCacheList);
            returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
        }
        return returnList;

    }

    /**
     * 获取未匹配生产单元列表
     *
     * @param prdtCell  生产单元
     * @param dcsIds    DCSID集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 未匹配生产单元列表
     * <AUTHOR> 2018-04-17
     */
    @Override
    public List<AlarmEventCacheEntity> getCachePrdtCellList(String prdtCell,Long[] dcsIds, Date startTime, Date endTime) throws Exception {
        List<AlarmEventCacheEntity> returnList = new ArrayList();
        if(dcsIds[0].longValue() == 1201 || dcsIds[0].longValue() == 1202) {
            List<AlarmEventH1Cache> eventCacheList = h1repo.getCachePrdtCellList(prdtCell, dcsIds, startTime, endTime);
            processShowAllH1(eventCacheList);
            returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
        } else {
            List<AlarmEventCache> eventCacheList = repo.getCachePrdtCellList(prdtCell, dcsIds, startTime, endTime);
            processShowAll(eventCacheList);
            returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
        }
        return returnList;
    }

    /**
     * 获取未匹配事件名称列表
     *
     *
     * @param eventName 事件名称
     * @param eventType 事件名称
     * @param dcsIds    DCSID集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 未匹配事件名称列表
     * <AUTHOR> 2018-04-17
     */
    @Override
    public List<AlarmEventCacheEntity> getCacheEventNameList(String eventName, String eventType, Long[] dcsIds, Date startTime, Date endTime) throws Exception {
        List<AlarmEventCache> eventCacheList = repo.getCacheEventNameList(eventName,eventType,dcsIds,startTime,endTime);
        if (eventCacheList.size() >1) {
            AlarmEventCache entity = new AlarmEventCache();
            entity.setParameter("-1");
            entity.setDes("全部");
            eventCacheList.add(0, entity);
        }
        List<AlarmEventCacheEntity> returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
        return returnList;
    }

    /**
     * 获取未匹配事件类型列表
     *
     *
     * @param eventType 事件类型
     * @param dcsIds    DCSID集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 未匹配事件类型列表
     * <AUTHOR> 2018-04-17
     */
    @Override
    public List<AlarmEventCacheEntity> getCacheEventTypeList(String eventType, Long[] dcsIds, Date startTime, Date endTime) throws Exception {
        List<AlarmEventCacheEntity> returnList = new ArrayList();
        if(dcsIds[0].longValue() == 1201 || dcsIds[0].longValue() == 1202) {
            List<AlarmEventH1Cache> eventCacheList = h1repo.getCacheEventTypeList(eventType,dcsIds,startTime,endTime);
            processShowAllH1(eventCacheList);
            returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
        } else {
            List<AlarmEventCache> eventCacheList = repo.getCacheEventTypeList(eventType,dcsIds,startTime,endTime);
            processShowAll(eventCacheList);
            returnList = ObjectConverter.listConverter(eventCacheList, AlarmEventCacheEntity.class);
            return returnList;
        }
        return returnList;
    }

    /**
     * 获取未配置数据分页
     *
     * @param dcsCodeIds        DcsCodeID
     * @param prdtCell         生产单元
     * @param alarmPoint       报警点
     * @param alarmFlag        报警标识
     * @param eventTypeSource  源事件类型
     * @param eventNameSource  源事件名称
     * @param priority         优先级
     * @param startTime       查询开始时间
     * @param endTime         查询结束时间
     * @param page
     * @return                未匹配数据
     * @throws Exception
     * <AUTHOR>  2018-04-16
     */
    @Override
    public PaginationBean<AlarmEventCacheEntity> getUnconfiguredDataList(Long[] dcsCodeIds, String prdtCell, String alarmPoint, String alarmFlag, String eventTypeSource, String eventNameSource, String priority, Date startTime, Date endTime, Pagination page) throws Exception {
        try {
            if(dcsCodeIds[0].longValue() == 1201 || dcsCodeIds[0].longValue() == 1201) {
                PaginationBean<AlarmEventH1Cache> queryList = h1repo.getUnconfiguredDataList(dcsCodeIds, prdtCell, alarmPoint, alarmFlag, eventTypeSource, eventNameSource, priority, startTime, endTime, page);
                PaginationBean<AlarmEventCacheEntity> returnList = new PaginationBean<>(page,
                        queryList.getTotal());
                returnList.setPageList(ObjectConverter.listConverter(queryList.getPageList(), AlarmEventCacheEntity.class));
                processDcsH1(queryList.getPageList(), returnList.getPageList());
                return returnList;
            }else {
                PaginationBean<AlarmEventCache> queryList = repo.getUnconfiguredDataList(dcsCodeIds, prdtCell, alarmPoint, alarmFlag, eventTypeSource, eventNameSource, priority, startTime, endTime, page);
                PaginationBean<AlarmEventCacheEntity> returnList = new PaginationBean<>(page,
                        queryList.getTotal());
                returnList.setPageList(ObjectConverter.listConverter(queryList.getPageList(), AlarmEventCacheEntity.class));
                processDcs(queryList.getPageList(), returnList.getPageList());
                return returnList;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 获取计量单位未配置数据分页
     *
     * @param dcsCodeIds        DcsCodeID
     * @param measUnit         计量单位
     * @param reson            不一致原因
     * @param startTime       查询开始时间
     * @param endTime         查询结束时间
     * @param page
     * @return                未匹配数据
     * @throws Exception
     * <AUTHOR>  2018-04-19
     */
    @Override
    public PaginationBean<AlarmEventCacheEntity> getUnconfiguredMeasUnitList(
            Long[] dcsCodeIds, String measUnit, Long reson, Date startTime,
            Date endTime, Pagination page) throws Exception {
        try {
            PaginationBean<AlarmEventCacheEntity> returnList = new PaginationBean<>();
            if(dcsCodeIds[0].longValue() == 1201 || dcsCodeIds[0].longValue() == 1201) {
                PaginationBean queryList = h1repo.getUnconfiguredMeasUnitList(dcsCodeIds, measUnit, reson, startTime, endTime, page);
                returnList = new PaginationBean<>(page,queryList.getTotal());
                List<AlarmEventCacheEntity> alarmEventCacheEntities = convertList((List<Object[]>) queryList.getPageList());
                returnList.setPageList(alarmEventCacheEntities);
            }else {
                PaginationBean queryList = repo.getUnconfiguredMeasUnitList(dcsCodeIds, measUnit, reson, startTime, endTime, page);
                returnList = new PaginationBean<>(page,queryList.getTotal());
                List<AlarmEventCacheEntity> alarmEventCacheEntities = convertList((List<Object[]>) queryList.getPageList());
                returnList.setPageList(alarmEventCacheEntities);
            }
            return returnList;
        } catch (Exception e) {
            throw e;
        }
    }



    /**
     * 获取报警点未分类数据分页
     *
     * @param dcsCodeIds        DcsCodeID
     * @param alarmPoint         报警点
     * @param reason            不一致原因
     * @param startTime       查询开始时间
     * @param endTime         查询结束时间
     * @param page
     * @return                未匹配数据
     * @throws Exception
     * <AUTHOR>  2018-04-26
     */
    @Override
    public PaginationBean<AlarmEventCacheEntity> getUnconfiguredAlarmPointList(
            Long[] dcsCodeIds, String alarmPoint, Long reason, Date startTime,
            Date endTime, Pagination page) throws Exception {
        try {
            PaginationBean<AlarmEventCacheEntity> returnList = new PaginationBean<>();
            if(dcsCodeIds[0].longValue() == 1201 || dcsCodeIds[0].longValue() == 1201) {
                PaginationBean queryList = h1repo.getUnconfiguredAlarmPointList(dcsCodeIds, alarmPoint, reason, startTime, endTime, page);
                returnList = new PaginationBean<>(page,queryList.getTotal());
                List<AlarmEventCacheEntity> alarmEventCacheEntities = convertList2((List<Object[]>) queryList.getPageList());
                returnList.setPageList(alarmEventCacheEntities);
            }else {
                PaginationBean queryList = repo.getUnconfiguredAlarmPointList(dcsCodeIds, alarmPoint, reason, startTime, endTime, page);
                returnList = new PaginationBean<>(page,queryList.getTotal());
                List<AlarmEventCacheEntity> alarmEventCacheEntities = convertList2((List<Object[]>) queryList.getPageList());
                returnList.setPageList(alarmEventCacheEntities);
            }
            return returnList;
        } catch (Exception e) {
            throw e;
        }
    }
    //Object转Long
    private Long toL(Object o) {
        return o != null ? new BigDecimal(o+"").longValue() : null;
    }
    //Object转String
    private String toStr(Object o ){
        return o==null ? "":o.toString();
    }

    /**
     * 处理DCS名称
     *
     * @param queryList  POJO数据源列表
     * @param returnList 返回数据列表
     * <AUTHOR> 2018-04-17
     */
    private void processDcs(List<AlarmEventCache> queryList, List<AlarmEventCacheEntity> returnList) {
        AlarmEventCache cache;
        for (AlarmEventCacheEntity entity : returnList) {
            cache = queryList.stream().filter(item -> item.getEventId().equals(entity.getEventId())).findFirst().orElse(new AlarmEventCache());
            if (cache.getDcs() != null) {
                entity.setDcsName(cache.getDcs().getName());
            }
        }
    }

    /**
     * 处理DCS名称
     *
     * @param queryList  POJO数据源列表
     * @param returnList 返回数据列表
     * <AUTHOR> 2018-04-17
     */
    private void processDcsH1(List<AlarmEventH1Cache> queryList, List<AlarmEventCacheEntity> returnList) {
        AlarmEventH1Cache cache;
        for (AlarmEventCacheEntity entity : returnList) {
            cache = queryList.stream().filter(item -> item.getEventId().equals(entity.getEventId())).findFirst().orElse(new AlarmEventH1Cache());
            if (cache.getDcs() != null) {
                entity.setDcsName(cache.getDcs().getName());
            }
        }
    }

    /**
     * 处理是否显示全部
     *
     * @param eventCacheList
     * <AUTHOR> 2018-04-17
     */
    private void processShowAll(List<AlarmEventCache> eventCacheList){
        if (eventCacheList.size() >= 1) {
            AlarmEventCache entity = new AlarmEventCache();
            entity.setParameter("-1");
            entity.setDes("全部");
            eventCacheList.add(0, entity);
        }
    }

    /**
     * 处理是否显示全部
     *
     * @param eventCacheList
     * <AUTHOR> 2018-04-17
     */
    private void processShowAllH1(List<AlarmEventH1Cache> eventCacheList){
        if (eventCacheList.size() >= 1) {
            AlarmEventH1Cache entity = new AlarmEventH1Cache();
            entity.setParameter("-1");
            entity.setDes("全部");
            eventCacheList.add(0, entity);
        }
    }

    private List<AlarmEventCacheEntity> convertList(List<Object[]> sourceList) {
        List<AlarmEventCacheEntity> targetList = new ArrayList<>();
        for(int i=0;i<sourceList.size();i++) {
            Object[] o = sourceList.get(i);
            //AlarmEventCacheEntity entity = new AlarmEventCacheEntity((Long) o[0], (Date) o[1], (Long) o[2], (String) o[3], (String) o[4], (String) o[5], (String) o[6], (String) o[7], (String) o[8], (String) o[9], (String) o[10], (Date) o[11], (Date) o[12], (Integer) o[13], (String) o[14], (String) o[15]);
            //Long eventId0, Date writeTime1, Long dcsCode2, String eventType3, String eventName4, String prdtCell5, String alarmPoint6, String des7, String alarmFlag8, String priority9, String measUnit10, Date startTime11, Date alarmTime12, Integer reson13, String dcsName14, String priorityName15
            AlarmEventCacheEntity entity = new AlarmEventCacheEntity();
            entity.setEventId(toL(o[0]));
            entity.setWriteTime((Date)o[1]);
            entity.setDcsCode(toL(o[2]));
            entity.setEventType(toStr(o[3]));
            entity.setEventName(toStr(o[4]));
            entity.setPrdtCell(toStr(o[5]));
            entity.setAlarmPoint(toStr(o[6]));
            entity.setDes(toStr(o[7]));
            entity.setAlarmFlag(toStr(o[8]));
            entity.setPriority(toStr(o[9]));
            entity.setMeasUnit(toStr(o[10]));
            entity.setStartTime((Date) o[11]);
            entity.setAlarmTime((Date)o[12]);
            entity.setReson(toL(o[13]));
            entity.setDcsName(toStr(o[14]));
            targetList.add(entity);
        }
        return targetList;
    }

    private List<AlarmEventCacheEntity> convertList2(List<Object[]> sourceList) {
        List<AlarmEventCacheEntity> targetList = new ArrayList<>();
        for(int i=0;i<sourceList.size();i++) {
            Object[] o = sourceList.get(i);
            //AlarmEventCacheEntity entity = new AlarmEventCacheEntity((Long) o[0], (Date) o[1], (Long) o[2], (String) o[3], (String) o[4], (String) o[5], (String) o[6], (String) o[7], (String) o[8], (String) o[9], (String) o[10], (Date) o[11], (Date) o[12], (Integer) o[13], (String) o[14], (String) o[15]);
            //Long eventId0, Date writeTime1, Long dcsCode2, String eventType3, String eventName4, String prdtCell5, String alarmPoint6, String des7, String alarmFlag8, String priority9, String measUnit10, Date startTime11, Date alarmTime12, Integer reson13, String dcsName14, String priorityName15
            AlarmEventCacheEntity entity = new AlarmEventCacheEntity();
            entity.setEventId(toL(o[0]));
            entity.setWriteTime((Date)o[1]);
            entity.setDcsCode(toL(o[2]));
            entity.setEventType(toStr(o[3]));
            entity.setEventName(toStr(o[4]));
            entity.setPrdtCell(toStr(o[5]));
            entity.setAlarmPoint(toStr(o[6]));
            entity.setDes(toStr(o[7]));
            entity.setStartTime((Date) o[8]);
            entity.setReson(toL(o[9]));
            entity.setDcsName(toStr(o[11]));
            entity.setOpcCode(toL(o[10]));
            targetList.add(entity);
        }
        return targetList;
    }
}
