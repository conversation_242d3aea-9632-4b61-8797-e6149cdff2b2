var pmUrl = OPAL.API.pmUrl;
var delUrl = pmUrl + '/unitPerson';
var searchUrl = pmUrl + '/unitPerson';
var isRefresh = false;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            //绑定事件
            this.bindUI();
            //初始化表格
            page.logic.initTable();
            page.logic.search();
        },
        /**
         * 绑定事件
         */
        bindUI: function () {
            // 新增
            $('#unitAdd').click(function () {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            })
            //批量删除
            $('#unitDel').click(function () {
                page.logic.delAll();
            })
            //查询
            $('#btnSearch').click(function () {
                page.logic.search();
            })
        },
        data: {
            // 设置查询参数
            param: {}
        },
        /**
         * 方法
         */
        logic: {


            /**
             * 初始化表格
             */
            initTable: function () {
                OPAL.ui.initBootstrapTable("table", {
                    cache:false,
                    //分页只显示条数
                    //onlyInfoPagination:true,
                    columns: [{
                        field: 'unitPersonId',
                        rowspan: 1,
                        visible: false
                    },{
                        field: 'pageList',
                        rowspan: 1,
                        visible: false
                    },{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '90px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "车间",
                        field: 'workShopSname',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "名称",
                        field: 'name',
                        rowspan: 1,
                        align: 'left',
                        width: '180px'
                    }, {
                        title: "简称",
                        field: 'sName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "编码",
                        field: 'unitId',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    },{
                        title: "操作工人数",
                        field: 'operNum',
                        rowspan: 1,
                        align: 'right',
                        width: '80px'
                    }, {
                        title: "创建时间",
                        field: 'crtDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "创建人",
                        field: 'crtUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "维护时间",
                        field: 'mntDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "维护人",
                        field: 'mntUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }]
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.edit(\'' + rowData.unitPersonId + '\')">编辑</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.unitPersonId + '\')" >删除</a> '
                ]
            },
            /**
             * 批量删除
             */
            delAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.unitPersonId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function (id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 新增
             */
            add: function () {
                var pageMode = PageModelEnum.NewAdd;
                var title = "操作报警装置新增";
                page.logic.detail(title, null, pageMode);
            },
            /**
             * 编辑
             * @param factoryId
             */
            edit: function (unitPersonId) {
                var pageMode = PageModelEnum.Edit;
                var title = "操作报警装置编辑";
                page.logic.detail(title, unitPersonId, pageMode);
            },
            /**
             * 装置新增或者编辑详细页面
             */
            detail: function (title, unitPersonId, pageMode) {
                    layer.open({
                    type: 2,
                    title: '',
                    closeBtn: false,
                    area: ['800px', '520px'],
                    shadeClose: false,
                    content: 'UnitPersonAddOrEdit.html?'+ Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "unitPersonId": unitPersonId,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (isRefresh == true) {
                            if (pageMode == PageModelEnum.Edit)
                                page.logic.search();
                            else if (pageMode == PageModelEnum.NewAdd)
                                $('#table').bootstrapTable('selectPage', 1);
                        }
                    }
                })
            },
            /**
             * 搜索
             */
            search: function () {
                page.data.param =OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
        }

    }
    page.init();
    window.page = page;
})