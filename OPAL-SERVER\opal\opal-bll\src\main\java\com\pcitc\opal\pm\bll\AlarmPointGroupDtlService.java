package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.AlarmPointEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointGroupConfigEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointGroupDtlEntity;
import com.pcitc.opal.pm.bll.entity.AlarmPointGroupEntity;
import com.pcitc.opal.pm.dao.imp.AlarmPointGroupDtlDTO;
import com.pcitc.opal.pm.pojo.AlarmPointGroupDtl;
import org.springframework.stereotype.Service;

import java.util.List;

/*
 * 报警点分组业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmPointGroupService
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点分组业务逻辑层接口
 */
@Service
public interface AlarmPointGroupDtlService {

    /**
     * 获取分页数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPointGroupId 分组编码
     * @throws Exception 
     * @return PaginationBean<AlarmPointEntity> 翻页对象
     */
    List<AlarmPointGroupDtlDTO> getAlarmPointGroupDtls(Long alarmPointGroupId);

    /**
     * 删除报警点分组明细数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPointGroupDtlIds 报警点分组主键Id集合
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult deleteAlarmPointGroupDtl(List<Long>  alarmPointGroupDtlIds) throws Exception;

    /**
     * 新增报警点分组明细数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPointGroupDtlEntity 报警点分组明细对象
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult addAlarmPointGroupDtl(List<AlarmPointGroupDtlEntity> alarmPointGroupDtlEntity) throws Exception;
}
