package com.pcitc.opal.ad.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_AD_Alarmeventdelapprove")
public class AlarmEventDelApproveEntity {
    /**
     * 报警剔除配置ID
     */

    @TableId(value = "Alarm_Event_Del_Approve_Id", type = IdType.AUTO)
    private Integer alarmEventDelApproveId;

    /**
     * 企业ID
     */
    @TableField(value = "Company_Id")
    private Integer companyId;

    /**
     * 装置编码
     */
    @TableField(value = "Unit_Code")
    private String unitCode;

    /**
     * 事件id
     */
    @TableField(value = "event_id")
    private Long eventId;

    /**
     * 剔除原因
     */
    @TableField(value = "Reason")
    private String reason;

    /**
     * 剔除状态（0未提交；1已提交；2已通过；3已驳回）
     */
    @TableField(value = "DEL_STATUS")
    private Integer delStatus;

    /**
     * 审批时间
     */
    @TableField(value = "APRO_TIME")
    private Date aProTime;

    /**
     * 审批人ID
     */
    @TableField(value = "APRO_USER_ID")
    private String aProUserId;

    /**
     * 审批人名称
     */
    @TableField(value = "APRO_USER_NAME")
    private String aProUserName;

    /**
     * 数据剔除状态：0未剔除，1剔除中，2事件表剔除失败，3记录表剔除失败，4剔除失败，5数据已剔除
     */
    @TableField(value = "del_data_status")
    private Integer delDataStatus;
    
}
