package com.pcitc.opal.ad.bll.entity;

import java.util.Date;

public class MonitoringDataEntity {

    /**
     * DCS名称
     */
    private String dcsCodeName;
    /**
     * 装置编码
     */
    private String unitCodeName;
    /**
     * 生产单元名称
     */
    private String prdtcellName;
    /**
     * opc名称
     */
    private String opcName;
    /**
     * 异常时间（startTime）
     */
    private Date startTime;
    /**
     * 断开时长（分钟）
     */
    private String duration;

    public String getDcsCodeName() {
        return dcsCodeName;
    }

    public void setDcsCodeName(String dcsCodeName) {
        this.dcsCodeName = dcsCodeName;
    }

    public String getUnitCodeName() {
        return unitCodeName;
    }

    public void setUnitCodeName(String unitCodeName) {
        this.unitCodeName = unitCodeName;
    }

    public String getPrdtcellName() {
        return prdtcellName;
    }

    public void setPrdtcellName(String prdtcellName) {
        this.prdtcellName = prdtcellName;
    }

    public String getOpcName() {
        return opcName;
    }

    public void setOpcName(String opcName) {
        this.opcName = opcName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }
}
