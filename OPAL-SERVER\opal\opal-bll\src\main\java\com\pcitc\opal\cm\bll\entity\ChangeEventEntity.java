package com.pcitc.opal.cm.bll.entity;
import com.pcitc.opal.ad.pojo.AlarmFlag;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import com.pcitc.opal.pm.pojo.EventType;
import com.pcitc.opal.pm.pojo.PrdtCell;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/*
 * 报警事件实体
 * 模块编号：pcitc_pojo_class_AlarmEvent
 * 作       者：xuelei.wang
 * 创建时间：2017/09/30
 * 修改编号：1
 * 描       述：报警事件实体
 */
public class ChangeEventEntity {
    /**
     * 变更事件ID
     */
    private Long changeEventId;

    /**
     *装置编码
     */
    private String unitCode;

    /**
     *生产单元ID
     */
    private Long prdtCellId;

    /**
     *DCS编码(缓存表)
     */
    private String dcsCode;

    /**
     * 事件类型ID
     */
    private Long eventTypeId;

    /**
     *报警点ID
     */
    private Long alarmPointId;

    /**
     *位号(缓存表)
     */
    private String tag;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     *报警标识(缓存表)
     */
    private String alarmFlagType;

    /**
     *发生时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;

    /**
     *报警时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date alarmTime;

    /**
     *优先级(1紧急；2重要；3一般)
     */
    private Integer priority;

    /**
     *优先级(缓存表)
     */
    private String priorityCache;

    /**
     *先前值
     */
    private String previousValue;

    /**
     *值
     */
    private String nowValue;

    /**
     *限值
     */
    private Double limitValue;

    /**
     *是否搁置(1是；0否)
     */
    private Integer inShelved;

    /**
     *是否屏蔽（1是；0否）
     */
    private Integer inSuppressed;

    /**
     *操作人
     */
    private String operator;

    /**
     *描述
     */
    private String des;

    /**
     *参数
     */
    private String parameter;

    /**
     * 写入时间
     */
    private Date writeTime;

    /**
     *生产单元
     */
    private PrdtCell prdtCell;

    /**
     *事件类型
     */
    private EventType eventType;

    /**
     *报警点
     */
    private AlarmPoint alarmPoint;

    /**
     *报警标识
     */
    private AlarmFlag alarmFlag;


    /**
     * 企业ID
     */
    private Integer companyId;

    /**
     * 工艺变更信息Id
     */
    private Integer craftChangeInfoId;

    /**
     * 状态(1合法；2非法；3未知)
     */
    private Integer status;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Long getChangeEventId() {
        return changeEventId;
    }

    public void setChangeEventId(Long changeEventId) {
        this.changeEventId = changeEventId;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Long getPrdtCellId() {
        return prdtCellId;
    }

    public void setPrdtCellId(Long prdtCellId) {
        this.prdtCellId = prdtCellId;
    }

    public String getDcsCode() {
        return dcsCode;
    }

    public void setDcsCode(String dcsCode) {
        this.dcsCode = dcsCode;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public String getAlarmFlagType() {
        return alarmFlagType;
    }

    public void setAlarmFlagType(String alarmFlagType) {
        this.alarmFlagType = alarmFlagType;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getPriorityCache() {
        return priorityCache;
    }

    public void setPriorityCache(String priorityCache) {
        this.priorityCache = priorityCache;
    }

    public String getPreviousValue() {
        return previousValue;
    }

    public void setPreviousValue(String previousValue) {
        this.previousValue = previousValue;
    }

    public String getNowValue() {
        return nowValue;
    }

    public void setNowValue(String nowValue) {
        this.nowValue = nowValue;
    }

    public Double getLimitValue() {
        return limitValue;
    }

    public void setLimitValue(Double limitValue) {
        this.limitValue = limitValue;
    }

    public Integer getInShelved() {
        return inShelved;
    }

    public void setInShelved(Integer inShelved) {
        this.inShelved = inShelved;
    }

    public Integer getInSuppressed() {
        return inSuppressed;
    }

    public void setInSuppressed(Integer inSuppressed) {
        this.inSuppressed = inSuppressed;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getParameter() {
        return parameter;
    }

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    public Date getWriteTime() {
        return writeTime;
    }

    public void setWriteTime(Date writeTime) {
        this.writeTime = writeTime;
    }

    public PrdtCell getPrdtCell() {
        return prdtCell;
    }

    public void setPrdtCell(PrdtCell prdtCell) {
        this.prdtCell = prdtCell;
    }

    public EventType getEventType() {
        return eventType;
    }

    public void setEventType(EventType eventType) {
        this.eventType = eventType;
    }

    public AlarmPoint getAlarmPoint() {
        return alarmPoint;
    }

    public void setAlarmPoint(AlarmPoint alarmPoint) {
        this.alarmPoint = alarmPoint;
    }

    public AlarmFlag getAlarmFlag() {
        return alarmFlag;
    }

    public void setAlarmFlag(AlarmFlag alarmFlag) {
        this.alarmFlag = alarmFlag;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getCraftChangeInfoId() {
        return craftChangeInfoId;
    }

    public void setCraftChangeInfoId(Integer craftChangeInfoId) {
        this.craftChangeInfoId = craftChangeInfoId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
