<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.mapper.AlarmPriorityCompMapper">

    <resultMap id="BaseResultMap" type="com.pcitc.opal.pojo.AlarmPriorityComp">
            <id property="alarmprioritycompId" column="alarmprioritycomp_id" jdbcType="BIGINT"/>
            <result property="dcsCodeId" column="dcs_code_id" jdbcType="BIGINT"/>
            <result property="prioritySource" column="priority_source" jdbcType="VARCHAR"/>
            <result property="priority" column="priority" jdbcType="BIGINT"/>
            <result property="inUse" column="in_use" jdbcType="BIGINT"/>
            <result property="crtDate" column="crt_date" jdbcType="TIMESTAMP"/>
            <result property="mntDate" column="mnt_date" jdbcType="TIMESTAMP"/>
            <result property="crtUserId" column="crt_user_id" jdbcType="VARCHAR"/>
            <result property="mntUserId" column="mnt_user_id" jdbcType="VARCHAR"/>
            <result property="crtUserName" column="crt_user_name" jdbcType="VARCHAR"/>
            <result property="mntUserName" column="mnt_user_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        alarmprioritycomp_id,dcs_code_id,priority_source,
        priority,in_use,crt_date,
        mnt_date,crt_user_id,mnt_user_id,
        crt_user_name,mnt_user_name
    </sql>
    <select id="selectAllTenantBuDb" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_ad_alarmprioritycomp
    </select>
</mapper>
