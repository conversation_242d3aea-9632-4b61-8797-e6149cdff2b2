package com.pcitc.opal.aa.bll.entity;

import java.io.Serializable;

/*
 * 统计值数据实体
 * 模块编号：pcitc_opal_bll_class_StaticInfoEntity
 * 作    者：xuelei.wang
 * 创建时间：2017/10/17
 * 修改编号：1
 * 描    述：统计值数据实体
 */
@SuppressWarnings("serial")
public class StaticInfoEntity implements Serializable {
    public StaticInfoEntity(String unitName, String unitId, String value) {
        this.unitName=unitName;
        this.unitId = unitId;
        this.value=value;
    }

    public StaticInfoEntity() {
    }

    /**
     * 装置ID
     */
    private String unitId;
    /**
     * 装置名称
     */
    private String unitName;
    /**
     * 统计数值
     */
    private String value;

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
