package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.pm.pojo.MobileList;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @USER: chenbo
 * @DATE: 2023/6/27
 * @TIME: 15:46
 * @DESC: 发送短信所需配置
 **/
@Data
public class SendMsgEntity {

    private String stdCode;


    /**
     *生产单元ID
     */
    private Long prdtCellId;


    /**
     * 位号
     */
    private String tag;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     *报警时间
     */
    private Date alarmTime;

    /**
     * 手机号
     */
    private List<MobileList> mobiles;
}
