var pmUrl = OPAL.API.pmUrl;
var delUrl = pmUrl+'/factory';
var searchUrl = pmUrl + '/factory';
var inUseUrl = OPAL.API.commUrl + "/getInUse";
var companyUrl = pmUrl+'/company/getCompanyList';
var isRefresh = false;
window.pageLoadMode = PageLoadMode.None;
$(function() {
	var page = {
		//页面初始化	
		init : function() {
			this.bindUI();
			//初始化查询是否启用
            page.logic.initInUse();
            //初始化查询企业
            page.logic.initCompany();
            //初始化表格
            page.logic.initTable();
            //默认查询数据
            page.logic.search();
		},
		//绑定事件和逻辑
		bindUI : function() {
			// 新增
            $('#FactoryAdd').click(function() {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            });
            //批量删除
            $('#FactoryDel').click(function() {
                page.logic.delAll();
            });
                //查询
            $('#searched').click(function() {
                page.logic.search();
            });
		},
		data: {
            // 设置查询参数
            param: {}
        },
        //定义业务逻辑方法
		logic : {
			/**
             * 初始化表格
             */
            initTable: function () {
                OPAL.ui.initBootstrapTable("table",{
                    cache:false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '90px',
                        "data-resizable":"true",
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "企业",
                        field: 'companyName',
                        rowspan: 1,
                        align: 'left',
                        width: '180px'
                    }, {
                        title: "名称",
                        field: 'name',
                        rowspan: 1,
                        align: 'left',
                        width: '180px'
                    }, {
                        title: "简称",
                        field: 'sname',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "标准编码",
                        field: 'stdCode',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "是否启用",
                        field: 'inUseShow',
                        rowspan: 1,
                        align: 'center',
                        width: '70px'
                    }, {
                        title: "创建时间",
                        field: 'crtDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "创建人",
                        field: 'crtUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "维护时间",
                        field: 'mntDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "维护人",
                        field: 'mntUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "排序",
                        field: 'sortNum',
                        rowspan: 1,
                        align: 'right',
                        width: '70px'
                    }, {
                        title: "描述",
                        field: 'des',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }]
                },page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param,param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.edit(\'' + rowData.factoryId + '\')">编辑</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.factoryId + '\')" >删除</a> '
                ]
            },
            /**
             * 批量删除
             */
            delAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.factoryId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function (id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false, //
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg("删除成功！", {
                                    time: 1000
                                }, function() {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 新增
             */
            add: function () {
                var pageMode = PageModelEnum.NewAdd;
                var title = "工厂新增";
                page.logic.detail(title, "", pageMode);
            },
            /**
             * 编辑
             * @param factoryId
             */
            edit: function (factoryId) {
                var pageMode = PageModelEnum.Edit;
                var title = "工厂编辑";
                page.logic.detail(title, factoryId, pageMode);
            },
            /**
             * 装置新增或者编辑详细页面
             */
            detail: function (title, factoryId, pageMode) {
                layer.open({
                    type: 2,
                    title: title,
                    closeBtn: 1,
                    area: ['800px', '380px'],
                    shadeClose: false,
                    content: 'FactoryAddOrEdit.html?'+ Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "factoryId": factoryId,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                    	if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#table').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 搜索
             */
            search: function () {
                page.data.param = OPAL.form.getData("searchForm");
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber":1
                });
            },
			/**
             * 初始化查询inUse
             */
            initInUse: function() {
                OPAL.ui.getCombobox("inUse", inUseUrl, {
                    selectValue: 1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            /**
             * 初始化查询企业
             */
            initCompany: function() {
                OPAL.ui.getCombobox("companyId", companyUrl, {
                    keyField: "companyId",
                    valueField: "sname",
                    //selectValue: -1,
                    //selectedIndex:0,
                    selectFirstRecord: true,
                    data: {
                        'isAll': true
                    }
                }, null);
            },

		}
	};
	page.init();
	window.page = page;
});