<?xml version="1.0" encoding="UTF-8"?>
<configuration status="DEBUG">

    <properties>
        <!--日志格式-->
        <property name="PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %X{TRACE_ID} [%thread] %-5level %logger[%L] - %m%n"/>
        <!--日志编码-->
        <property name="CHARSET" value="utf-8"/>
        <!--单个日志文件大小-->
        <property name="MAX_FILE_SIZE" value="200MB"/>
        <!--日志保存时间-->
        <property name="MAX_HISTORY" value="P30D"/>
        <!--日志根路径-->
        <property name="BASE_LOG_PATH" value="../../opal-logs"/>
        <!--日志应用名，例如拼接完整的日志路径：/data/logs/app/app-info.log-->
        <property name="SERVER_NAME" value="opalservice"/>
    </properties>


    <appenders>
        <Console name="CONSOLE" target="SYSTEM_OUT">
            <PatternLayout pattern="${PATTERN}" charset="${CHARSET}"/>
        </Console>

        <RollingRandomAccessFile name="FILE-INFO"
                                 fileName="${BASE_LOG_PATH}/${SERVER_NAME}/${SERVER_NAME}-info.log"
                                 filePattern="${BASE_LOG_PATH}/${SERVER_NAME}/${SERVER_NAME}-info.%d{yyyy-MM-dd}-%i.log">
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${PATTERN}" charset="${CHARSET}"/>
            <Policies>
                <!--每天滚动一次-->
                <TimeBasedTriggeringPolicy interval="1"/>
                <!--或者日志达到10KB 滚动一次-->
                <SizeBasedTriggeringPolicy size="10MB"/>
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="FILE-ERROR"
                                 fileName="${BASE_LOG_PATH}/${SERVER_NAME}/${SERVER_NAME}-error.log"
                                 filePattern="${BASE_LOG_PATH}/${SERVER_NAME}/${SERVER_NAME}-error.%d{yyyy-MM-dd}-%i.log">
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${PATTERN}" charset="${CHARSET}"/>
            <Policies>
                <!--每天滚动一次-->
                <TimeBasedTriggeringPolicy interval="1"/>
                <!--或者日志达到10KB 滚动一次-->
                <SizeBasedTriggeringPolicy size="10MB"/>
            </Policies>
        </RollingRandomAccessFile>
    </appenders>

    <loggers>
        <AsyncRoot level="DEBUG" includeLocation="true" >
<!--            <AppenderRef ref="CONSOLE"/>-->
            <AppenderRef ref="FILE-INFO"/>
            <AppenderRef ref="FILE-ERROR"/>
        </AsyncRoot>
    </loggers>
</configuration>