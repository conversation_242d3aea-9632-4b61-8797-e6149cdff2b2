package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmMsgConfigRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmMsgConfig;
import com.pcitc.opal.pm.pojo.SendMsgAlarmConf;

import javax.persistence.TypedQuery;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AlarmMsgConfigRepositoryImpl extends BaseRepository<AlarmMsgConfig, Long> implements AlarmMsgConfigRepositoryCustom {
    @Override
    public List<SendMsgAlarmConf> getSendMsgAlarmConfMobileId(Long mobileListId) {

        try {
            String hqlConf = " from SendMsgAlarmConf f " +
                    "where 1=1 " +
                    "and instr(f.mobileListIds,(:mobileListId))>0";
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("mobileListId", String.valueOf(mobileListId));
            TypedQuery<SendMsgAlarmConf> query = getEntityManager().createQuery(hqlConf, SendMsgAlarmConf.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception e) {
            throw e;
        }
    }


}
