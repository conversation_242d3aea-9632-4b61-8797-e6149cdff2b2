$(function () {
    var chartUrl = OPAL.API.adUrl + '/alarmAnly/trendChart?alarmRecId=';
    var avgAlarmRateCharts;
    var index = parent.layer.getFrameIndex(window.name);
    var page = {
        init: function () {
            this.bindUI();
        },
        bindUI: function () {
            /*关闭弹窗*/
            $('#closeChart').click(function () {
                page.logic.closeLayer(true);
            });
        },
        logic: {
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                $.ajax({
                    url: chartUrl + data.alarmRecId,
                    type: "get",
                    async: false,
                    dataType: "json",
                    success: function (data) {
                        if(data != null && data.code === 1) {
                            avgAlarmRateCharts = echarts.init(document.getElementById('chart'));
                            var myChart = {
                                id: "chart-Container",
                                title: {
                                    text: data.data.text,
                                    subtext: data.data.subtext
                                },
                                legend: data.data.legend,
                                xAxis: data.data.xAxis,
                                yAxis: data.data.yAxis,
                                series: data.data.series
                            };
                            myChart.legend.top = 30;
                            myChart.legend.right = 10;
                            avgAlarmRateCharts.setOption(myChart);
                        } else {
                            $('#chart').html("")
                        }

                    }
                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                parent.layer.close(index);
            }
        }

    }
    page.init();
    window.page = page;
})