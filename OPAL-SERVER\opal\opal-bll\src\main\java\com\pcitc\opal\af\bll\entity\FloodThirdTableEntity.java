package com.pcitc.opal.af.bll.entity;

import org.apache.commons.lang.time.DateFormatUtils;

import java.io.Serializable;
import java.util.Date;

/*
 * 高频报警分析三级列表展示实体
 * 模块编号：pcitc_opal_bll_class_FloodThirdTableEntity
 * 作  　者：xuelei.wang
 * 创建时间：2017-11-17
 * 修改编号：1
 * 描    述：高频报警分析三级列表展示实体
 */
@SuppressWarnings({"serial","unused"})
public class FloodThirdTableEntity implements Serializable {
    public FloodThirdTableEntity(Date startTime, Long alarmCounts) {
        this.startTime = startTime;
        this.alarmCounts = alarmCounts;
    }

    public FloodThirdTableEntity() {
    }

    /**
     * 高频报警每个十分钟的开始时间
     */
    private Date startTime;
    /**
     * 开始时间字符串
     */
	private String startTimeStr;
    /**
     * 高频报警每个十分钟的报警数
     */
    private Long alarmCounts;

    public Long getAlarmCounts() {
        return alarmCounts;
    }

    public void setAlarmCounts(Long alarmCounts) {
        this.alarmCounts = alarmCounts;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getStartTimeStr() {
        return DateFormatUtils.format(startTime,"yyyy-MM-dd HH:mm:ss");
    }

    public void setStartTimeStr(String startTimeStr) {
        this.startTimeStr = startTimeStr;
    }
}