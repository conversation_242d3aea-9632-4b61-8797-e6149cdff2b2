package com.pcitc.opal.ad.dao;

import com.pcitc.opal.ad.pojo.AlarmEventCache;
import com.pcitc.opal.common.CommonResult;

import java.util.List;

/*
 * AlarmEventCache实体的Repository的JPA自定义接口
 * 模块编号：pcitc_opal_dal_interface_AlarmEventCacheRepositoryCustom
 * 作	者：zheng.yang
 * 创建时间：2017/03/14
 * 修改编号：1
 * 描	述：AlarmEventCache实体的Repository的JPA自定义接口
 */
public interface AlarmEventCacheRepositoryCustom {

    /**
     * 新增数据
     *
      * <AUTHOR> 2019-03-14
     * @param alarmEventCache 报警缓存表实体
     * @return 
     * @return CommonResult 消息结果类
     */
    CommonResult addAlarmEventCache(List<AlarmEventCache> alarmEventCache);
}
