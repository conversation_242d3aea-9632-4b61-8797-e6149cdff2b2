package com.pcitc.opal.ap.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

import javax.persistence.Column;

/*
 * 报警点实体
 * 模块编号：pcitc_pojo_class_Group
 * 作       者：guoganxin
 * 创建时间：2023/04/16
 * 修改编号：1
 * 描       述：群组
 */
public class AlarmPushRuleUnitRelDetailEntity extends BasicEntity {
    /**
     * 报警推送规则装置关系明细ID
     */
    private Long apRuleUnitRelDetailId;

    /**
     * 报警推送规则装置关系ID
     */
    private Long apRuleUnitRelId;


    /**
     * 装置编码
     */
    private String unitCode;

    /**
     * 是否启用（1是；0否）
     */
    private Integer inUse;

    /**
     * 排序
     */
    private Integer sortNum;

    /**
     * 描述
     */
    private String des;


    public Long getApRuleUnitRelDetailId() {
        return apRuleUnitRelDetailId;
    }

    public void setApRuleUnitRelDetailId(Long apRuleUnitRelDetailId) {
        this.apRuleUnitRelDetailId = apRuleUnitRelDetailId;
    }

    public Long getApRuleUnitRelId() {
        return apRuleUnitRelId;
    }

    public void setApRuleUnitRelId(Long apRuleUnitRelId) {
        this.apRuleUnitRelId = apRuleUnitRelId;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    @Override
    public Integer getInUse() {
        return inUse;
    }

    @Override
    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
