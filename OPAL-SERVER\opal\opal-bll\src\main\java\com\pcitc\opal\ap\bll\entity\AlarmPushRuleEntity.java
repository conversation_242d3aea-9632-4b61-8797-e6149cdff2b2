package com.pcitc.opal.ap.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;
import com.pcitc.opal.common.pojo.BasicInfo;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

/*
 * 报警点实体
 * 模块编号：pcitc_pojo_class_Group
 * 作       者：guoganxin
 * 创建时间：2023/04/16
 * 修改编号：1
 * 描       述：群组
 */
public class AlarmPushRuleEntity extends BasicEntity {
    /**
     * 报警推送规则ID
     */
    private Long alarmPushRuleId;

    /**
     * 企业ID
     */
    private Integer companyId;

    /**
     * 名称
     */
    private String name;

    /**
     * 推送类型（1 超时推送、2 超限推送）
     */
    private Integer pushType;

    /**
     * 是否启用（1是；0否）
     */
    private Integer inUse;

    /**
     * 排序
     */
    private Integer sortNum;

    /**
     * 描述
     */
    private String des;

    public Long getAlarmPushRuleId() {
        return alarmPushRuleId;
    }

    public void setAlarmPushRuleId(Long alarmPushRuleId) {
        this.alarmPushRuleId = alarmPushRuleId;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getPushType() {
        return pushType;
    }

    public void setPushType(Integer pushType) {
        this.pushType = pushType;
    }

    public Integer getInUse() {
        return inUse;
    }

    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
