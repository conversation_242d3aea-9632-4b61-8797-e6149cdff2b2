package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.MobileListDTOEntity;
import com.pcitc.opal.pm.bll.entity.MobileListEntity;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.List;

/*
 * 电话本业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_mobileListService
 * 作       者：guoganxin
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：电话本业务逻辑层接口
 */
@Service
public interface MobileListService {

	/**
	 * 新增数据
	 * <p>
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param mobileListEntity 电话本实体
	 * @return CommonResult 消息结果类
	 * @throws Exception 
	 */
	CommonResult addMobileList(MobileListEntity mobileListEntity) throws Exception;

	/**
	 * 删除电话本维护数据
	 * <p>
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param mobileListIds 电话本维护主键Id集合
	 * @return CommonResult 消息结果类
	 * @throws Exception 
	 */
	CommonResult deleteMobileList(Long[] mobileListIds) throws Exception;

	/**
	 * 电话本更新数据
	 * <p>
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param mobileListEntity 电话本实体
	 * @return CommonResult 消息结果类
	 * @throws Exception 
	 */
	CommonResult updateMobileList(MobileListEntity mobileListEntity) throws Exception;

	/**
	 * 获取单条数据
	 * <p>
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param mobileListId 电话本ID
	 * @return mobileListEntity 电话本实体类
	 * @throws Exception
	 */
	MobileListEntity getSingleMobileList(Long mobileListId) throws Exception;

	/**
	 * 获取分页数据
	 * <p>
	 *  * <AUTHOR> 2017-10-11
	 *
	 * @param page 翻页实现类
	 * @return PaginationBean<mobileListEntity> 翻页对象
	 * @throws Exception 
	 */
	PaginationBean<MobileListDTOEntity> getMobileList(Long factoryIds, Long workshopIds, String unitCodes, String name, String mobile, Pagination page) throws Exception;


	/**
	 * 电话本维护数据导入
	 *
	 * @param inputStream Excel文件流
	 * @return String 返回字符串对象
	 * <AUTHOR> 2017-11-16
	 */
	String importMobileList(InputStream inputStream) throws Exception;


	public List<MobileListDTOEntity> getMobileListExport(Long factoryId, Long workshopId, String unitCode, String name, String mobile);
}
