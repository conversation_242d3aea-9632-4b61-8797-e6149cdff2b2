package com.pcitc.opal.common.bll.imp;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pcitc.opal.bd.dao.CraftParamCompareDAO;
import com.pcitc.opal.bd.entity.CraftParamCompareEntity;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.bll.CraftStartStopDispositionService;
import com.pcitc.opal.common.bll.vo.CraftStartStopDispositionVO;
import com.pcitc.opal.common.bll.vo.PointStatusDispositionVO;
import com.pcitc.opal.pm.dao.AlarmPointDAO;
import com.pcitc.opal.pm.dao.CraftStartStopConfigRepository;
import com.pcitc.opal.pm.entity.AlarmPointEntity;
import com.pcitc.opal.pm.pojo.CraftStartStopConfig;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CraftStartStopDispositionServiceImpl implements CraftStartStopDispositionService {

    private static final Logger log = LoggerFactory.getLogger(CraftStartStopDispositionServiceImpl.class);

    @Autowired
    private CraftStartStopConfigRepository craftStartStopConfigRepository;

    @Autowired
    private CraftParamCompareDAO craftParamCompareDAO;

    @Autowired
    private AlarmPointDAO alarmPointDAO;

    @Override
    public CommonResult addDisposition(CraftStartStopDispositionVO vo) {
        log.info("工艺系统-启停时间配置同步接口参数：{}", JSON.toJSONString(vo));
        CommonResult result = new CommonResult();
        result.setMessage("成功");
        result.setIsSuccess(true);
        try {
            Date startTime = vo.getStartTime();
            Date now = new Date();
            CraftStartStopConfig craftStartStopConfig = new CraftStartStopConfig();
            craftStartStopConfig.setUnitCode(vo.getUnitCode());
            craftStartStopConfig.setPrdtCode(vo.getPrdtCode());
            craftStartStopConfig.setStopTime(vo.getStopTime());
            if (startTime == null) {
                craftStartStopConfig.setCreateTime(now);
                craftStartStopConfig.setUpdateTime(now);
            } else {
                List<CraftStartStopConfig> configList = craftStartStopConfigRepository.selectByParams(
                        craftStartStopConfig);
                if (CollectionUtils.isNotEmpty(configList)) {
                    craftStartStopConfig = configList.get(0);
                    craftStartStopConfig.setStartTime(vo.getStartTime());
                    craftStartStopConfig.setUpdateTime(now);
                } else {
                    craftStartStopConfig.setStartTime(vo.getStartTime());
                    craftStartStopConfig.setCreateTime(now);
                    craftStartStopConfig.setUpdateTime(now);
                }
            }
            craftStartStopConfigRepository.saveAndFlush(craftStartStopConfig);
        } catch (Exception e) {
            result.setMessage(e.getMessage());
            result.setIsSuccess(false);
            log.error(e.getMessage(), e);
        }
        log.info("工艺系统-启停时间配置同步接口结果：{}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public CommonResult pointStatusDisposition(List<PointStatusDispositionVO> vo) {
        log.info("工艺系统-参数启停同步接口参数：{}", JSON.toJSONString(vo));
        CommonResult result = new CommonResult();
        result.setMessage("成功");
        try {
            result.setIsSuccess(true);
            for (PointStatusDispositionVO pointStatusDispositionVO : vo) {
                List<CraftParamCompareEntity> compareEntities = craftParamCompareDAO.selectList(
                        Wrappers.lambdaQuery(CraftParamCompareEntity.class)
                                .in(CraftParamCompareEntity::getCraftUnitCode, pointStatusDispositionVO.getUnitCode())
                                .in(CraftParamCompareEntity::getCraftParamDcsCode, pointStatusDispositionVO.getTagCode()));
                if (CollectionUtils.isNotEmpty(compareEntities)) {
                    List<String> tagCodeList = compareEntities.stream().map(CraftParamCompareEntity::getAlarmPointCode)
                            .collect(Collectors.toList());
                    AlarmPointEntity alarmPointEntity = new AlarmPointEntity();
                    alarmPointEntity.setIsTimeout(pointStatusDispositionVO.getCraftStatus());
                    alarmPointDAO.update(alarmPointEntity,
                            Wrappers.lambdaQuery(AlarmPointEntity.class).in(AlarmPointEntity::getTag, tagCodeList));
                }
            }
        } catch (Exception e) {
            result.setMessage(e.getMessage());
            result.setIsSuccess(false);
            log.error(e.getMessage(), e);
        }
        log.info("工艺系统-参数启停同步接口参数：{}", JSON.toJSONString(result));
        return result;
    }

}
