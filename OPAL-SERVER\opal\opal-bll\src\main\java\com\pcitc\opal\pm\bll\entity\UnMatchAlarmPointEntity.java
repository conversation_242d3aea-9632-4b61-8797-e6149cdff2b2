package com.pcitc.opal.pm.bll.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * @USER: chenbo
 * @DATE: 2023/4/23
 * @DESC: 未匹配报警点
 **/
public class UnMatchAlarmPointEntity {
    /**
     * 未匹配报警点ID
     */
    private Long unMatchAlarmPointId;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * DCS编码
     */
    private Long dcsCode;

    /**
     * 生产单元ID
     */
    private Long prdtCellId;

    /**
     * 位号
     */
    private String tag;

    /**
     * 写入时间
     */
    private Date writeDate;

    /**
     * OPC编码ID
     */
    private  Long opcCodeId;

    public Long getUnMatchAlarmPointId() {
        return unMatchAlarmPointId;
    }

    public void setUnMatchAlarmPointId(Long unMatchAlarmPointId) {
        this.unMatchAlarmPointId = unMatchAlarmPointId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getDcsCode() {
        return dcsCode;
    }

    public void setDcsCode(Long dcsCode) {
        this.dcsCode = dcsCode;
    }

    public Long getPrdtCellId() {
        return prdtCellId;
    }

    public void setPrdtCellId(Long prdtCellId) {
        this.prdtCellId = prdtCellId;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Date getWriteDate() {
        return writeDate;
    }

    public void setWriteDate(Date writeDate) {
        this.writeDate = writeDate;
    }

    public Long getOpcCodeId() {
        return opcCodeId;
    }

    public void setOpcCodeId(Long opcCodeId) {
        this.opcCodeId = opcCodeId;
    }
}
