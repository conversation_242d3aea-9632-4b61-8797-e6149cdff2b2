package com.pcitc.opal.pm.dao.imp;

import lombok.Data;

import java.util.Date;

/**
 * @USER: chenbo
 * @DATE: 2023/4/23
 * @DESC: 未匹配报警点
 **/
@Data
public class UnMatchAlarmPointEntityVO {
    public UnMatchAlarmPointEntityVO(Long unMatchAlarmPointId, Long companyId, Long dcsCode, Long prdtCellId, String tag, Date writeDate, Long opcCodeId, String dcsName, String prdtCellName, String unitCode, String unitName) {
        this.unMatchAlarmPointId = unMatchAlarmPointId;
        this.companyId = companyId;
        this.dcsCode = dcsCode;
        this.prdtCellId = prdtCellId;
        this.tag = tag;
        this.writeDate = writeDate;
        this.opcCodeId = opcCodeId;
        this.dcsName = dcsName;
        this.prdtCellName = prdtCellName;
        this.unitCode = unitCode;
        this.unitName = unitName;
    }

    /**
     * 未匹配报警点ID
     */
    private Long unMatchAlarmPointId;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * DCS编码
     */
    private Long dcsCode;

    /**
     * 生产单元ID
     */
    private Long prdtCellId;

    /**
     * 位号
     */
    private String tag;

    /**
     * 写入时间
     */
    private Date writeDate;

    /**
     * OPC编码ID
     */
    private  Long opcCodeId;

    /**
     * dcs名称
     */
    private  String dcsName;

    /**
     * 生产单元名称
     */
    private String prdtCellName;

    /**
     * 装置编码
     */
    private String unitCode;

    /**
     * 装置名称
     */
    private String unitName;
}
