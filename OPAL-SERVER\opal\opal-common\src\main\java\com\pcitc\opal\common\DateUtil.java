package com.pcitc.opal.common;

import java.util.Calendar;
import java.util.Date;

/**
 * @USER: chenbo
 * @DATE: 2022/11/21
 * @TIME: 14:11
 * @DESC: 日期转换工具类
 **/
public class DateUtil {
    /**
     * 获取传入日期的最大时间
     * @param date
     * @return
     */
    public static Date getLastDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 将时分秒,毫秒域清零
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    /**
     * 获取传入日期的最小时间
     * @param date
     * @return
     */
    public static Date getStartDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 将时分秒,毫秒域清零
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        calendar.set(Calendar.MINUTE, 00);
        calendar.set(Calendar.SECOND, 00);
        return calendar.getTime();
    }

}
