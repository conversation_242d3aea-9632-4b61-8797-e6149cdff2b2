package com.pcitc.opal.common.bll.entity;

import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;

/*
 * 班次实体
 * 模块编号：pcitc_opal_bll_class_ShiftEntity
 * 作       者：xuelei.wang
 * 创建时间：2018-07-21
 * 修改编号：1
 * 描       述：班次实体
 */
@SuppressWarnings({"serial"})
public class ShiftEntity extends BaseResRep implements Serializable {
    /**
     * 班次编码
     */
    private String shiftCode;
    /**
     * 班次名称
     */
    private String name;

    /**
     * 简称
     */
    private String sName;

    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    public String getShiftCode() {
        return shiftCode;
    }

    public void setShiftCode(String shiftCode) {
        this.shiftCode = shiftCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getShiftId() {
        return shiftCode;
    }

    public void setShiftId(String shiftCode) {
        this.shiftCode = shiftCode;
    }

    public String getSName() {
        return sName;
    }

    public void setSName(String sname) {
        this.sName = sname;
    }

}
