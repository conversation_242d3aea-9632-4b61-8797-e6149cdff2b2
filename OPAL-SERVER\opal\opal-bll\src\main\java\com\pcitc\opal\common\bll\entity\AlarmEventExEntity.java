package com.pcitc.opal.common.bll.entity;

import com.pcitc.opal.common.CommonEnum;

/*
 * 报警事件查询实体
 * 模块编号：pcitc_opal_bll_class__AlarmEventExEntity
 * 作       者：xuelei.wang
 * 创建时间：2017/10/29
 * 修改编号：1
 * 描       述：报警事件查询实体
 */

@SuppressWarnings({"unused"})
public class AlarmEventExEntity {
    public AlarmEventExEntity(String tag, String flagName, Long count, Integer priority, String prdtName, String unitCode, Long alarmPointId, Long alarmFlagId) {
        this.flagName = flagName;
        this.tag = tag;
        this.count = count;
        this.priority = priority;
        this.prdtName = prdtName;
        this.unitId = unitCode;
        this.alarmPointId = alarmPointId;
        this.alarmFlagId = alarmFlagId;
    }

    public AlarmEventExEntity() {
    }

    /**
     * 装置Code
     */
    private String unitId;
    /**
     * 报警点ID
     */
    private Long alarmPointId;
    /**
     * 报警标识ID
     */
    private Long alarmFlagId;
    /**
     * 报警优先级
     */
    private Integer priority;
    /**
     * 生产单元简称
     */
    private String prdtName;
    /**
     * 装置简称
     */
    private String unitName;
    /**
     * 报警标识名称
     */
    private String flagName;
    /**
     * 位号
     */
    private String tag;
    /**
     * 数量
     */
    private Long count;
    /**
     * 百分比
     */
    private String percentage;
    /**
     * 优先级名称
     */
    private String priorityName;

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getPrdtName() {
        return prdtName;
    }

    public void setPrdtName(String prdtName) {
        this.prdtName = prdtName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPercentage() {
        return percentage;
    }

    public void setPercentage(String percentage) {
        this.percentage = percentage;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public String getFlagName() {
        return flagName;
    }

    public void setFlagName(String flagName) {
        this.flagName = flagName;
    }

    public String getPriorityName() {
        return CommonEnum.AlarmPriorityEnum.getName(this.getPriority());
    }

    public void setPriorityName(String priorityName) {
        this.priorityName = priorityName;
    }

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }
}
