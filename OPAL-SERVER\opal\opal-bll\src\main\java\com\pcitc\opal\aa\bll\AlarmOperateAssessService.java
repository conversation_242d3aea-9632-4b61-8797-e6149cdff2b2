package com.pcitc.opal.aa.bll;

import java.util.Date;
import java.util.List;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.entity.AlarmEventExLocationEntity;
import org.springframework.stereotype.Service;

import com.pcitc.opal.aa.bll.entity.AlarmOperateAssessChartEntity;
import com.pcitc.opal.common.bll.entity.AlarmEventExEntity;

/*
 * 报警操作评估业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmOperateAssessService
 * 作  　者：xuelei.wang
 * 创建时间：2017-10-24
 * 修改编号：1
 * 描    述：报警操作评估业务逻辑层接口
 */
@Service
public interface AlarmOperateAssessService {
    /**
     * 获取报警操作评估首页数据
     *
     * @param startTime  开始日期
     * @param endTime    结束日期
     * @param unitCodes    装置ID集合
     * @param prdtIds    生产单元ID集合
     * @param dateType   查询日期类型(日,周,月)
     * @param checkModel 模式 0:装置模式,1:工厂车间模式
     * @returns
     * @throws Exception
     * <AUTHOR> 2017-10-24
     */
    List<AlarmOperateAssessChartEntity> getAlarmOperateAssess(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, CommonEnum.DateTypeEnum dateType, int checkModel) throws Exception;

    /**
     * 获取报警操作评估首页统计值数据
     *
     * @param startTime  开始日期
     * @param endTime    结束日期
     * @param unitCodes    装置ID集合
     * @param prdtIds    生产单元ID集合
     * @param checkModel 模式 0:装置模式,1:工厂车间模式
     * @param topType Top20,Top10切换选择
     * @return
     * @throws Exception
     * <AUTHOR> 2017-10-25
     */
    List<AlarmEventExLocationEntity> getAlarmOperateAssessTop20(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, int checkModel, Integer topType) throws Exception;

    /**
     * 获取报警操作评估获取操作数详情
     *
     * @param startTime  开始日期
     * @param endTime    结束日期
     * @param id         标识(装置或者生产单元)
     * @param type       装置|单元
     * @param checkModel 模式 0:装置模式,1:工厂车间模式
     * @param page       分页信息
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-9
     */
    PaginationBean<AlarmEventEntity> getAlarmOperateAsseessDetail(Date startTime, Date endTime, String id, CommonEnum.EquipmentTypeEnum type, int checkModel, Pagination page) throws Exception;

    /**
     * 查询最频繁报警详情集合
     *
      * <AUTHOR> 2018-04-13
     * @param alarmPointTag 位号
     * @param alarmFlagId 报警标识id
     * @param startTime 发生时间
     * @param endTime 报结束时间时间
     * @param page 查询分页对象
     * @throws Exception 
     * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity实体分页对象
     */
    PaginationBean<AlarmEventEntity> getOperateDtail(String alarmPointTag, Long alarmFlagId, Date startTime, Date endTime, Pagination page) throws Exception;
}
