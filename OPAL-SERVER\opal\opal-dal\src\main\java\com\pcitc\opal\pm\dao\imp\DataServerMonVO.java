package com.pcitc.opal.pm.dao.imp;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

/*
 * 变更事件查询工艺变更单条件实体
 * 模块编号：pcitc_pojo_ChangeEventCondition
 * 作       者：xuelei.wang
 * 创建时间：2017/09/30
 * 修改编号：1
 * 描       述：变更事件查询工艺变更单条件实体
 */
public class DataServerMonVO {

    public DataServerMonVO() {
    }
    public DataServerMonVO(String sname,
                           String unitCode,
                           Integer state,
                           Date activeTime) {
        this.sname = sname;
        this.unitCode = unitCode;
        this.state = state;
        this.activeTime = activeTime;
    }


    /**
     *装置简称
     */
    private String sname;

    /**
     *装置编码
     */
    private String unitCode;


    /**
     *执行时间s
     */
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+08:00")
    private Date activeTime;

    /**
     *状态
     */
    private Integer state;


    public String getSname() {
        return sname;
    }

    public void setSname(String sname) {
        this.sname = sname;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Date getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(Date activeTime) {
        this.activeTime = activeTime;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
}
