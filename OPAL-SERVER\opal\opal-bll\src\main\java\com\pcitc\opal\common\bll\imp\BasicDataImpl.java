package com.pcitc.opal.common.bll.imp;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.persistence.*;

import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.AAAService;
import com.pcitc.opal.common.bll.FactoryModelService;
import com.pcitc.opal.common.bll.WorkFlowService;
import com.pcitc.opal.common.bll.entity.*;
import com.pcitc.opal.common.bll.entity.FactoryEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.common.bll.entity.WorkshopEntity;
import com.pcitc.opal.pm.bll.entity.*;
import com.pcitc.opal.pm.dao.AlarmPointDelConfigRepository;
import com.pcitc.opal.pm.pojo.*;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.converters.DateConverter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.pcitc.opal.aa.bll.entity.AlarmNumberAssessEntity;
import com.pcitc.opal.aa.bll.entity.AlarmNumberAssessTableEntity;
import com.pcitc.opal.aa.bll.entity.AlarmNumberAssessTrendEntity;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.pojo.AlarmFlag;
import com.pcitc.opal.af.bll.entity.CausalAlarmAnalysisEntity;
import com.pcitc.opal.af.bll.entity.CausalAlarmAnalysisTableEntity;
import com.pcitc.opal.common.CommonEnum.EventTypeEnum;
import com.pcitc.opal.common.bll.BasicDataService;

import pcitc.imp.common.ettool.utils.ObjectConverter;

/*
 * 基础数据实现类
 * 模块编号：pcitc_opal_bll_class_BasicDataImpl
 * 作       者：dongsheng.zhao
 * 创建时间：2017/09/17
 * 修改编号：1
 * 描       述：基础数据实现类
 */
@Component
@SuppressWarnings({"unchecked", "rawtypes"})
public class BasicDataImpl implements BasicDataService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private AlarmEventRepository eventRepo;

    @Autowired
    private AAAService aaaService;

    @Autowired
    private WorkFlowService flowService;

    @Autowired
    private WorkFlowConfig flowConfig;
    @Autowired
    private FactoryModelService factoryModelService;
    @Autowired
    private DbConfig dbConfig;

    @Autowired
    BasicDataService basicDataService;

    @Resource
    private AlarmPointDelConfigRepository alarmPointDelConfigRepository;
    private static final int FACTORY_MODE = 1;

    /**
     * 获取装置树形结构(含工厂，车间）
     *
     * @param enablePrivilege 是否启用权限过滤
     * @return 树形实体集合
     * <AUTHOR> 2017-09-25
     */
    @Override
    public List<OrgEntity> getAllUnitTree(boolean enablePrivilege) throws Exception {
       return factoryModelService.getAllUnitTree(enablePrivilege);
    }

    /**
     * 获取生产装置列表
     *
     * @param enablePrivilege 是否启用权限过滤
     * @return 生产装置列表
     * <AUTHOR> 2017-09-26
     */
    @Override
    public List<UnitEntity> getUnitList( boolean enablePrivilege) throws Exception {
        return factoryModelService.getUnitList(enablePrivilege);
    }

    /**
     * 根据装置编码集合获取装置列表
     *
     * @param unitCodes         装置编码集合
     * @param enablePrivilege 是否启用权限过滤
     * @return 装置列表
     * <AUTHOR> 2017-09-26
     */
    @Override
    public List<UnitEntity> getUnitListByIds(String[] unitCodes, boolean enablePrivilege) throws Exception {
        return factoryModelService.getUnitListByIds(unitCodes, false, enablePrivilege);
    }

    /**
     * 根据车间编码集合获取该车间下所有的已启用的装置列表
     *
     * @param workshopCodes   装置编码集合
     * @param enablePrivilege 是否开启权限
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-7
     */
    @Override
    public List<UnitEntity> getUnitListByWorkshopIds(String[] workshopCodes, boolean enablePrivilege) throws Exception {
        return factoryModelService.getUnitListByWorkshopIds(workshopCodes, enablePrivilege);
    }

    /**
     * 根据车间编码集合获取车间列表
     *
     * @param workshopCodes 车间编码集合
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-7
     */
    @Override
    public List<WorkshopEntity> getWorkshopListByWorkshopIds(String[] workshopCodes) throws Exception {
        return factoryModelService.getWorkshopListByWorkshopIds(workshopCodes);
    }

    /**
     * 是否启用
     *
     * @param isAll 是否显示全部
     * @return 是否启用结果集
     * <AUTHOR> 2017-09-26
     */
    @Override
    public List<DictionaryEntity> getInUse(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            if (isAll) {
                dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
            }
            for (CommonEnum.InUseEnum inUse : CommonEnum.InUseEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(inUse.getIndex(), inUse.getName()));
            }

        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取报警点级别结果集
     *
     * @param isAll 是否显示全部
     * @return 获取报警点级别结果集
     * <AUTHOR> 2017-11-10
     */
    @Override
    public List<DictionaryEntity> getCraftRankList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            if (isAll) {
                dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
            }
            for (CommonEnum.CraftRankEnum rankEnum : CommonEnum.CraftRankEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(rankEnum.getIndex(), rankEnum.getName()));
            }

        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取监测类型列表
     *
     * @param isAll 是否显示全部
     * @return 监测类型列表
     * <AUTHOR> 2017-09-26
     */
    @Override
    public List<DictionaryEntity> getMonitorTypeList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            for (CommonEnum.MonitorTypeEnum monitorTypeEnum : CommonEnum.MonitorTypeEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(monitorTypeEnum.getIndex(), monitorTypeEnum.getName()));
            }
            if (isAll && dictionaryEntityArrayList.size() > 1) {
                DictionaryEntity dictionaryEntity = new DictionaryEntity();
                dictionaryEntity.setKey(-1L);
                dictionaryEntity.setValue("全部");
                dictionaryEntityArrayList.add(0, dictionaryEntity);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取报警优先级列表
     *
     * @param isAll 是否显示全部
     * @return 报警优先级列表
     * <AUTHOR> 2017-09-26
     */
    @Override
    public List<DictionaryEntity> getAlarmPriorityList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            if (isAll) {
                dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
            }
            for (CommonEnum.AlarmPriorityEnum alarmPriority : CommonEnum.AlarmPriorityEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(alarmPriority.getIndex(), alarmPriority.getName()));
            }
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取仪表类型列表
     *
     * @param isAll 是否显示全部
     * @return 仪表类型列表
     * <AUTHOR> 2017-10-9
     * 1监测表；2控制表
     */
    @Override
    public List<DictionaryEntity> getInstrmtTypeList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            for (CommonEnum.InstrmtTypeEnum instrmtTypeEnum : CommonEnum.InstrmtTypeEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(instrmtTypeEnum.getIndex(), instrmtTypeEnum.getName()));
            }
            if (isAll && dictionaryEntityArrayList.size() > 1) {
                DictionaryEntity dictionaryEntity = new DictionaryEntity();
                dictionaryEntity.setKey(-1L);
                dictionaryEntity.setValue("全部");
                dictionaryEntityArrayList.add(0, dictionaryEntity);
            }

        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取虚实标记列表
     *
     * @param isAll 是否显示全部
     * @return 虚实标记列表
     * <AUTHOR> 2017-10-9
     * 0实表(按读数)；1虚表(按用量)
     */
    @Override
    public List<DictionaryEntity> getVirtualRealityFlagList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            for (CommonEnum.VirtualRealityFlagEnum virtualRealityFlagEnum : CommonEnum.VirtualRealityFlagEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(virtualRealityFlagEnum.getIndex(), virtualRealityFlagEnum.getName()));
            }
            if (isAll && dictionaryEntityArrayList.size() > 1) {
                DictionaryEntity dictionaryEntity = new DictionaryEntity();
                dictionaryEntity.setKey(-1L);
                dictionaryEntity.setValue("全部");
                dictionaryEntityArrayList.add(0, dictionaryEntity);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取报警标识列表
     *
     * @param isAll 是否显示全部
     * @return 报警标识列表
     * <AUTHOR> 2017-10-9
     * 1PVHH、2PVHI、3PVLO、4PVLL
     */
    @Override
    public List<DictionaryEntity> getAllAlarmFlagList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityList = new ArrayList<>();
        try {
            //获取所有已启用的生产装置列表
            String unitHql = "from AlarmFlag a where a.inUse=:inUse order by a.sortNum,a.name asc";
            List<AlarmFlag> alarmFlagList = entityManager.createQuery(unitHql, AlarmFlag.class).setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex()).getResultList();
            alarmFlagList.forEach(alarmFlag -> {
                dictionaryEntityList.add(new DictionaryEntity(alarmFlag.getAlarmFlagId(), alarmFlag.getName()));
            });
            if (isAll && dictionaryEntityList.size() > 1) {
                DictionaryEntity allEntity = new DictionaryEntity(-1, "全部");
                dictionaryEntityList.add(0, allEntity);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityList;
    }

    /**
     * 获取报警标识列表
     *
     * @param isAll 是否显示全部
     * @return 报警标识列表
     * <AUTHOR> 2017-10-9
     * 1PVHH、2PVHI、3PVLO、4PVLL
     */
    @Override
    public List<DictionaryEntity> getAlarmFlagList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityList = new ArrayList<>();
        try {
            //获取所有已启用的生产装置列表
            String unitHql = "from AlarmFlag a where a.inUse=:inUse and exists (from AlarmFlagComp afc where afc.alarmFlagId=a.alarmFlagId and afc.inUse=:inUse ) order by a.sortNum,a.name asc";
            List<AlarmFlag> alarmFlagList = entityManager.createQuery(unitHql, AlarmFlag.class).setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex()).getResultList();
            alarmFlagList.forEach(alarmFlag -> {
                dictionaryEntityList.add(new DictionaryEntity(alarmFlag.getAlarmFlagId(), alarmFlag.getName()));
            });
            if (isAll && dictionaryEntityList.size() > 1) {
                DictionaryEntity allEntity = new DictionaryEntity(-1, "全部");
                dictionaryEntityList.add(0, allEntity);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityList;
    }

    /**
     * 获取发生时间列表
     *
     * @param isAll 是否显示全部
     * @return 发生时间列表
     * <AUTHOR> 2017-10-9
     * 0自定义、1近七天、2近半个月、3近一个月、4近两个月、5近三个月、6近半年、7近一年
     */
    @Override
    public List<DictionaryEntity> getStartTimeList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            for (CommonEnum.StartTimeEnum startTimeEnum : CommonEnum.StartTimeEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(startTimeEnum.getIndex(), startTimeEnum.getName()));
            }
            if (isAll && dictionaryEntityArrayList.size() > 1) {
                DictionaryEntity dictionaryEntity = new DictionaryEntity();
                dictionaryEntity.setKey(-1L);
                dictionaryEntity.setValue("全部");
                dictionaryEntityArrayList.add(0, dictionaryEntity);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取报警点类型列表
     *
     * @param isAll 是否显示全部
     * @return 报警点类型列表
     * <AUTHOR> 2017-10-9
     */
    @Override
    public List<AlarmPointTypeEntity> getAlarmPointTypeList(boolean isAll) {
        List<AlarmPointTypeEntity> alarmPointTypeEntityList = new ArrayList<>();
        try {
            String alarmPointHql = "from AlarmPointType a where a.inUse=:inUse order by a.sortNum asc";
            List<AlarmPointType> alarmPointTypeList = entityManager.createQuery(alarmPointHql, AlarmPointType.class).setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex()).getResultList();
            alarmPointTypeEntityList = ObjectConverter.listConverter(alarmPointTypeList, AlarmPointTypeEntity.class);
            if (isAll && alarmPointTypeEntityList.size() > 1) {
                AlarmPointTypeEntity entity = new AlarmPointTypeEntity();
                entity.setAlarmPointTypeId(-1L);
                entity.setName("全部");
                alarmPointTypeEntityList.add(0, entity);
            }
        } catch (Exception ex) {
        }
        return alarmPointTypeEntityList;
    }

    /**
     * 根据装置编码获取生产单元
     *
     * @param unitCode 装置编码
     * @param isAll  是否显示全部
     * @return 生产单元列表
     * @throws Exception
     * <AUTHOR> 2017-10-09
     */
    @Override
    public List<PrdtCellEntity> getUnitPrdtCell(String unitCode, boolean isAll) {
        List<PrdtCellEntity> prdtCellEntityList = new ArrayList<>();
//企业
        CommonProperty commonProperty = new CommonProperty();
        try {
            String prctCellHql = "from PrdtCell p where p.companyId=:companyId and p.unitId=:unitId and p.inUse=:inUse order by p.sortNum asc";
            List<PrdtCell> prdtCellList = entityManager.createQuery(prctCellHql, PrdtCell.class)
                    .setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex())
                    .setParameter("unitId", unitCode)
                    .setParameter("companyId",commonProperty.getCompanyId())
                    .getResultList();
            prdtCellEntityList = ObjectConverter.listConverter(prdtCellList, PrdtCellEntity.class);
            if (isAll && prdtCellEntityList.size() > 1) {
                PrdtCellEntity entity = new PrdtCellEntity();
                entity.setPrdtCellId(-1L);
                entity.setSname("全选");
                prdtCellEntityList.add(0, entity);
            }
        } catch (Exception ex) {

        }
        return prdtCellEntityList;
    }

    /**
     * 获取所有已启用计量单位列表
     *
     * @param isAll 是否显示全部
     * @return 已启用计量单位列表
     * <AUTHOR> 2017-10-10
     */
    @Override
    public List<MeasUnitEntity> getMeasUnitList(boolean isAll) {
        List<MeasUnitEntity> measUnitEntityList = new ArrayList<>();
        try {
            String meaUnitHql = "from MeasUnit m where m.inUse=:inUse order by m.sortNum,m.name,m.sign asc";
            List<MeasUnit> measUnitList = entityManager.createQuery(meaUnitHql, MeasUnit.class)
                    .setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex()).getResultList();
            measUnitEntityList = ObjectConverter.listConverter(measUnitList, MeasUnitEntity.class);
            if (isAll && measUnitEntityList.size() > 1) {
                MeasUnitEntity entity = new MeasUnitEntity();
                entity.setMeasUnitId(-1L);
                entity.setName("全部");
                measUnitEntityList.add(0, entity);
            }
        } catch (Exception ex) {

        }
        return measUnitEntityList;
    }

    /**
     * 获取国际报警标准值列表
     *
     * @return 国际报警标准值列表
     * <AUTHOR> 2017-10-17
     */
    @Override
    public List<DictionaryEntity> getISOAlarmList() {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmPriorityEnum.Emergency.getName(), CommonPropertiesReader.getValue("alarm.iso.emergency")));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmPriorityEnum.Importance.getName(), CommonPropertiesReader.getValue("alarm.iso.importance")));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmPriorityEnum.Normal.getName(), CommonPropertiesReader.getValue("alarm.iso.normal")));
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 查询时间
     *
     * @return 查询时间
     * <AUTHOR> 2017-10-17
     */
    @Override
    public List<DictionaryEntity> getQueryTime() {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        String hourTime=CommonPropertiesReader.getValue("query.time");
        //如果配置的查询时间为8:00:00,统一转成08:00:00
        if(StringUtils.isNoneBlank(hourTime)){
            DateConverter dateConverter = new DateConverter();
            dateConverter.setPattern("HH:mm:ss");
            Date dateTime = dateConverter.convert(Date.class, hourTime);
            hourTime=DateFormatUtils.format(dateTime,"HH:mm:ss");
        }
        try {
            dictionaryEntityArrayList.add(new DictionaryEntity("queryTime",hourTime));
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取查询时间的小时数
     *
     * @return 查询时间的小时数
     * @throws Exception
     * <AUTHOR> 2017-11-2
     */
    @Override
    public int getQueryTimeHour() throws Exception {
        int hour;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
            String queryTime = CommonPropertiesReader.getValue("query.time");
            Date date = sdf.parse(queryTime);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            hour = calendar.get(Calendar.HOUR_OF_DAY);
        } catch (Exception ex) {
            throw ex;
        }
        return hour;
    }

    /**
     * 如果“当前系统时间”大于等于“查询日期+1 8:00:00”，则开始时间大于等于“查询日期-6 8:00:00（公共方法）”， 结束时间小于“
     * 查询日期+1天  8:00:00（公共方法）”；否则，开始时间大于等于“查询日期-6 8:00:00（公共方法）”，结束时间小于等于“当前系统
     * 时间”；(开始时间计算规则，结束时间计算规则（公共方法）
     * 理论:是历史 数据就是完整的7天的数据,如果不是历史数据,则是6天点的数据;
     *
     * @param date 给定的日期
     * @return 开始时间和结束时间List
     * <AUTHOR> 2017-10-22
     */
    @Override
    public List<DictionaryEntity> getQueryStartAndEndDate(Date date) throws Exception {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            String startFlag;
            String endFlag;
            String queryTime = CommonPropertiesReader.getValue("query.time");
            Date startDate;
            Date endDate;
            Date weekBeforeStartDate;
            Date weekBeforeEndDate;
            Date queryDate;

            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
            queryDate = sdf.parse(queryTime);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(queryDate);

            Date queryAddOneDay = DateUtils.addDays(date, 1);
            queryAddOneDay = DateUtils.setHours(queryAddOneDay, calendar.get(Calendar.HOUR_OF_DAY));
            queryAddOneDay = DateUtils.setMinutes(queryAddOneDay, calendar.get(Calendar.MINUTE));
            queryAddOneDay = DateUtils.setSeconds(queryAddOneDay, calendar.get(Calendar.SECOND));

            //查询日期+1 8:00:00 <= 当前系统时间
            if (queryAddOneDay.getTime() <= new Date().getTime()) {//是历史数据,完整的7天
                startDate = DateUtils.addDays(queryAddOneDay, -7);
                endDate = queryAddOneDay;

                endDate = DateUtils.setHours(endDate, calendar.get(Calendar.HOUR));
                endDate = DateUtils.setMinutes(endDate, calendar.get(Calendar.MINUTE));
                endDate = DateUtils.setSeconds(endDate, calendar.get(Calendar.SECOND));

                startFlag = ">=";
                endFlag = "<";
            } else {  //是当天数据,数据不是完整的7天
                startDate = DateUtils.addDays(queryAddOneDay, -7);
                endDate = new Date();

                startFlag = ">=";
                endFlag = "<=";
            }

            weekBeforeStartDate = DateUtils.addDays(startDate, -7);
            weekBeforeEndDate = DateUtils.addDays(endDate, -7);

            startDate = DateUtils.setHours(startDate, calendar.get(Calendar.HOUR_OF_DAY));
            startDate = DateUtils.setMinutes(startDate, calendar.get(Calendar.MINUTE));
            startDate = DateUtils.setSeconds(startDate, calendar.get(Calendar.SECOND));

            weekBeforeStartDate = DateUtils.setHours(weekBeforeStartDate, calendar.get(Calendar.HOUR_OF_DAY));
            weekBeforeStartDate = DateUtils.setMinutes(weekBeforeStartDate, calendar.get(Calendar.MINUTE));
            weekBeforeStartDate = DateUtils.setSeconds(weekBeforeStartDate, calendar.get(Calendar.SECOND));

            dictionaryEntityArrayList.add(new DictionaryEntity("startTime", startDate));
            dictionaryEntityArrayList.add(new DictionaryEntity("endTime", endDate));

            dictionaryEntityArrayList.add(new DictionaryEntity("oneWeekBeforeStartDate", weekBeforeStartDate));
            dictionaryEntityArrayList.add(new DictionaryEntity("oneWeekBeforeEndDate", weekBeforeEndDate));

            dictionaryEntityArrayList.add(new DictionaryEntity("startFlag", startFlag));
            dictionaryEntityArrayList.add(new DictionaryEntity("endFlag", endFlag));
            dictionaryEntityArrayList.add(new DictionaryEntity("nowDate", new Date()));
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    @Override
    public List<DictionaryEntity> getQueryStartAndEndDate(Date startTime, Date endTime) throws Exception {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            String startFlag;
            String endFlag;
            String queryTime = CommonPropertiesReader.getValue("query.time");
            Date startDate;
            Date endDate;
            Date weekBeforeStartDate;
            Date weekBeforeEndDate;
            Date queryDate;

            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
            queryDate = sdf.parse(queryTime);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(queryDate);

            Date queryAddOneDay = DateUtils.addDays(endTime, 1);
            queryAddOneDay = DateUtils.setHours(queryAddOneDay, calendar.get(Calendar.HOUR_OF_DAY));
            queryAddOneDay = DateUtils.setMinutes(queryAddOneDay, calendar.get(Calendar.MINUTE));
            queryAddOneDay = DateUtils.setSeconds(queryAddOneDay, calendar.get(Calendar.SECOND));

            //查询日期+1 8:00:00 <= 当前系统时间
            if (queryAddOneDay.getTime() <= new Date().getTime()) {//是历史数据,完整的7天
                startDate = DateUtils.addDays(queryAddOneDay, -7);
                endDate = endTime;

                endDate = DateUtils.setHours(endDate, calendar.get(Calendar.HOUR));
                endDate = DateUtils.setMinutes(endDate, calendar.get(Calendar.MINUTE));
                endDate = DateUtils.setSeconds(endDate, calendar.get(Calendar.SECOND));

                startFlag = ">=";
                endFlag = "<";
            } else {  //是当天数据,数据不是完整的7天
                startDate = DateUtils.addDays(queryAddOneDay, -7);
                endDate = new Date();

                startFlag = ">=";
                endFlag = "<=";
            }

            weekBeforeStartDate = DateUtils.addDays(startDate, -7);
            weekBeforeEndDate = DateUtils.addDays(endDate, -7);

            startDate = DateUtils.setHours(startDate, calendar.get(Calendar.HOUR_OF_DAY));
            startDate = DateUtils.setMinutes(startDate, calendar.get(Calendar.MINUTE));
            startDate = DateUtils.setSeconds(startDate, calendar.get(Calendar.SECOND));

            weekBeforeStartDate = DateUtils.setHours(weekBeforeStartDate, calendar.get(Calendar.HOUR_OF_DAY));
            weekBeforeStartDate = DateUtils.setMinutes(weekBeforeStartDate, calendar.get(Calendar.MINUTE));
            weekBeforeStartDate = DateUtils.setSeconds(weekBeforeStartDate, calendar.get(Calendar.SECOND));

            dictionaryEntityArrayList.add(new DictionaryEntity("startTime", startDate));
            dictionaryEntityArrayList.add(new DictionaryEntity("endTime", endDate));

            dictionaryEntityArrayList.add(new DictionaryEntity("oneWeekBeforeStartDate", weekBeforeStartDate));
            dictionaryEntityArrayList.add(new DictionaryEntity("oneWeekBeforeEndDate", weekBeforeEndDate));

            dictionaryEntityArrayList.add(new DictionaryEntity("startFlag", startFlag));
            dictionaryEntityArrayList.add(new DictionaryEntity("endFlag", endFlag));
            dictionaryEntityArrayList.add(new DictionaryEntity("nowDate", new Date()));
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }
    /**
     * 获取报警级别列表
     *
     * @param isAll 是否显示全部
     * @return 警级别列表
     * <AUTHOR> 2017-10-17
     */
    @Override
    public List<DictionaryEntity> getAlarmStateComparisonList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            for (CommonEnum.AlarmLevelEnum alarmLevel : CommonEnum.AlarmLevelEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(alarmLevel.getIndex(), alarmLevel.getName()));
            }
            if (isAll && dictionaryEntityArrayList.size() > 1) {
                DictionaryEntity dictionaryEntity = new DictionaryEntity();
                dictionaryEntity.setKey(-1L);
                dictionaryEntity.setValue("全部");
                dictionaryEntityArrayList.add(0, dictionaryEntity);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取报警状态列表
     *
     * @param isAll 是否显示全部
     * @return 报警状态列表
     * <AUTHOR> 2017-10-17
     */
    @Override
    public List<DictionaryEntity> getAlarmLevelList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            for (CommonEnum.StateComparisonEnum stateComparisonEnum : CommonEnum.StateComparisonEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(stateComparisonEnum.getIndex(), stateComparisonEnum.getName()));
            }
            if (isAll && dictionaryEntityArrayList.size() > 1) {
                DictionaryEntity dictionaryEntity = new DictionaryEntity();
                dictionaryEntity.setKey(-1L);
                dictionaryEntity.setValue("全部");
                dictionaryEntityArrayList.add(0, dictionaryEntity);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param interval  间隔分钟数
     * @return 数量
     * <AUTHOR> 2017-10-18
     */
    @Override
    public List<DictionaryEntity> getCountByTimeInterval(Date startDate, Date endDate, int interval) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            Double count;
            //1.获取总的分钟数
            Double totalMinutes = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60.0));
            if (totalMinutes % interval == 0) {
                count = totalMinutes / interval;
            } else {
                count = totalMinutes / interval + 1;
            }
            count = Double.valueOf(String.format("%.5f", count));
            DictionaryEntity dictionaryEntity = new DictionaryEntity("count", count);
            dictionaryEntityArrayList.add(dictionaryEntity);
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 根据报警开始日期和报警结束日期获取装置的总的报警数
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param unitCode    装置编码
     * @return 装置的总的报警数
     * <AUTHOR> 2017-10-30
     */
    @Override
    public List<DictionaryEntity>    getAlarmEventTotalCount(Date startTime, Date endTime, String unitCode) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        //企业
        CommonProperty commonProperty = new CommonProperty();
        try {
            String hql = "select count(*) as total from AlarmEvent ae "
                    + " inner join ae.alarmPoint ap "
                    + " inner join ap.prdtCell pc "
                    + " where ae.companyId=:companyId  and ae.eventTypeId="
                    + EventTypeEnum.ProcessEvent.getIndex() + " and pc.unitId=:unitId and ae.alarmTime between :startTime and :endTime "
                    + " and ap.inUse = 1 "
                    + " and ae.alarmFlagId is not null "
                    + " and ae.priority is not null ";
            Long count = (Long) entityManager.createQuery(hql)
                    .setParameter("unitId", unitCode)
                    .setParameter("startTime", startTime)
                    .setParameter("endTime", endTime)
                    .setParameter("companyId",commonProperty.getCompanyId())
                    .getSingleResult();
            DictionaryEntity dictionaryEntity = new DictionaryEntity("count", count);
            dictionaryEntityArrayList.add(dictionaryEntity);
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 根据报警开始日期和报警结束日期获取装置的总的报警数
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param unitCodes    装置编码
     * @return 装置的总的报警数
     * <AUTHOR> 2017-10-30
     */
    @Override
    public List<DIcCodeCountEntity> getAlarmEventTotalCountByUnits(String[] unitCodes,Date startTime, Date endTime) {
        List<DIcCodeCountEntity> dictionaryEntityArrayList = new ArrayList<>();
        //企业
        CommonProperty commonProperty = new CommonProperty();
        try {
            String hql = "select new com.pcitc.opal.common.bll.entity.DIcCodeCountEntity(ae.unitCode, count(*)) from AlarmEvent ae "
                    + " inner join ae.alarmPoint ap "
                    + " inner join ap.prdtCell pc "
                    + " where ae.companyId=:companyId and ae.eventTypeId="
                    + EventTypeEnum.ProcessEvent.getIndex() + " and ae.unitCode in (:unitId) and ae.alarmTime between :startTime and :endTime "
                    + " and ap.inUse = 1 and pc.inUse = 1 "
                    + " and ae.alarmFlagId is not null "
                    + " and ae.priority is not null "
                    + " group by ae.unitCode ";
            dictionaryEntityArrayList =  entityManager.createQuery(hql)
                    .setParameter("unitId", Arrays.asList(unitCodes))
                    .setParameter("startTime", startTime)
                    .setParameter("endTime", endTime)
                    .setParameter("companyId",commonProperty.getCompanyId())
                    .getResultList();
            //DictionaryEntity dictionaryEntity = new DictionaryEntity("count", count);
            //dictionaryEntityArrayList.add(dictionaryEntity);
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 根据报警开始日期和报警结束日期获取装置的总的操作数
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param unitCode    装置编码
     * @return 装置的总的报警数
     * <AUTHOR> 2017-10-30
     */
    @Override
    public List<DictionaryEntity> getUnitTotalOperate(Date startTime, Date endTime, String unitCode) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        //企业
        CommonProperty commonProperty = new CommonProperty();
        try {
            String hql = "select count(*) as total from AlarmEvent ae "
                    + " inner join ae.alarmPoint ap "
                    + " inner join ap.prdtCell pc "
                    + " inner join ae.eventType et"
                    + " where (et.parentId=" + EventTypeEnum.OperateEvent.getIndex() +" or " +"et.eventTypeId="+EventTypeEnum.ConfirmedEvent.getIndex()+" ) " + " and pc.unitId=:unitId and ae.startTime between :startTime and :endTime "
                    + " and ap.inUse = 1 and ae.companyId=:companyId  ";

            Long count = (Long) entityManager.createQuery(hql)
                    .setParameter("unitId", unitCode)
                    .setParameter("startTime", startTime)
                    .setParameter("endTime", endTime)
                    .setParameter("companyId",commonProperty.getCompanyId())
                    .getSingleResult();
            DictionaryEntity dictionaryEntity = new DictionaryEntity("count", count);
            dictionaryEntityArrayList.add(dictionaryEntity);
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 根据装置Id获取装置的操作人工数
     *
     * @param unitCode 装置编码
     * @return 装置的操作人数
     * <AUTHOR> 2017-10-30
     */
    @Override
    public List<DictionaryEntity> getUnitPerson(String unitCode) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            String unitPersonHql = "select u.operNum from UnitPerson u where u.inUse=:inUse and u.unitId=:unitId";
            Long count = (Long) entityManager.createQuery(unitPersonHql)
                    .setParameter("unitId", unitCode)
                    .setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex())
                    .getSingleResult();

            DictionaryEntity dictionaryEntity = new DictionaryEntity("count", count);
            dictionaryEntityArrayList.add(dictionaryEntity);
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }


    /**
     * 获取所有生产单元列表
     *
     * @return 生产单元列表
     * <AUTHOR> 2017-10-19
     */
    @Override
    public List<PrdtCellEntity> getAllPrdtCellList() throws Exception {
        List<PrdtCell> prdtCellList = new ArrayList<>();
        //企业
        CommonProperty commonProperty = new CommonProperty();
        try {
            String prctCellHql = "from PrdtCell p where p.companyId=:companyId and p.inUse=:inUse";
            prdtCellList = entityManager.createQuery(prctCellHql)
                    .setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex())
                    .setParameter("companyId",commonProperty.getCompanyId())
                    .getResultList();
        } catch (Exception ex) {
            throw ex;
        }
        return ObjectConverter.listConverter(prdtCellList, PrdtCellEntity.class);
    }

    /**
     * 初始化自定义显示日期(近7天)
     * 如果当前系统时间大于等于8:00:00（公共方法），则结束日期默认为“当天日期”（开始日期默认为“当天日期-6”）
     * 否则结束日期默认为“当天日期-1”（开始日期默认为“当天日期-7”）
     *
     * @param date 当前时间
     * @return 开始日期结束日期集合
     * <AUTHOR> 2017-10-19
     */
    @Override
    public List<DictionaryEntity> getShowTime(Date date) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList();
        try {

            String queryTime = CommonPropertiesReader.getValue("query.time");
            Date startDate; //开始日期
            Date endDate; //结束日期
            Date queryDate; //设定时间 8:00:00

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);

            queryDate = DateUtils.parseDate(queryTime, new String[]{"HH:mm:ss"});
            queryDate = DateUtils.setYears(queryDate, calendar.get(Calendar.YEAR));
            queryDate = DateUtils.setMonths(queryDate, calendar.get(Calendar.MONTH));
            queryDate = DateUtils.setDays(queryDate, calendar.get(Calendar.DAY_OF_MONTH));

            //当前时间大于等于8点
            if (date.getTime() >= queryDate.getTime()) {
                endDate = date;
                startDate = DateUtils.addDays(queryDate, -6);
            } else {
                endDate = DateUtils.addDays(queryDate, -1);
                startDate = DateUtils.addDays(queryDate, -7);
            }

            dictionaryEntityArrayList.add(new DictionaryEntity("startDate", DateFormatUtils.format(startDate, "yyyy-MM-dd HH:mm:ss")));
            dictionaryEntityArrayList.add(new DictionaryEntity("endDate", DateFormatUtils.format(endDate, "yyyy-MM-dd HH:mm:ss")));
            dictionaryEntityArrayList.add(new DictionaryEntity("nowDate", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
        } catch (Exception e) {
        }
        return dictionaryEntityArrayList;
    }


    @Override
    public List<DictionaryEntity> getCurrentTime(Date date) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList();
        try {

            String queryTime = CommonPropertiesReader.getValue("query.time");
            Date startDate; //开始日期
            Date endDate; //结束日期
            Date queryDate; //设定时间 8:00:00

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);

            queryDate = DateUtils.parseDate(queryTime, new String[]{"HH:mm:ss"});
            queryDate = DateUtils.setYears(queryDate, calendar.get(Calendar.YEAR));
            queryDate = DateUtils.setMonths(queryDate, calendar.get(Calendar.MONTH));
            queryDate = DateUtils.setDays(queryDate, calendar.get(Calendar.DAY_OF_MONTH));

            endDate = date;
            startDate = DateUtils.addDays(queryDate, -6);

            dictionaryEntityArrayList.add(new DictionaryEntity("startDate", DateFormatUtils.format(startDate, "yyyy-MM-dd HH:mm:ss")));
            dictionaryEntityArrayList.add(new DictionaryEntity("endDate", DateFormatUtils.format(endDate, "yyyy-MM-dd HH:mm:ss")));
            dictionaryEntityArrayList.add(new DictionaryEntity("nowDate", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
        } catch (Exception e) {
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 根据开始日期和结束日期确定查询的开始时间和结束时间
     * 如果“当前系统时间”大于等于“结束日期+1 8:00:00” 结束时间小于"结束日期+1天 8:00:00" 开始时间大于等于“开始日期 8:00:00”
     * 否则 结束时间小于等于"当前系统时间" 开始时间大于等于“开始日期 8:00:00”
     * <p>
     * 如果“当前系统时间”大于等于“查询日期+1 8:00:00”，则开始时间大于等于“查询日期-6 8:00:00（公共方法）”， 结束时间小于“
     * 查询日期+1天  8:00:00（公共方法）”；否则，开始时间大于等于“查询日期-6 8:00:00（公共方法）”，结束时间小于等于“当前系统
     * 时间”；(开始时间计算规则，结束时间计算规则（公共方法）
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 开始时间和结束时间集合
     * <AUTHOR> 2017-10-20
     */
    @Override
    public Map<String, Object> getSearchTime(Date startDate, Date endDate) throws Exception {
        List<DictionaryEntity> dateList = getQueryStartAndEndDate(endDate);
        Date startTime = (Date) dateList.stream().filter(item -> "startTime".equals(item.getKey().toString())).findFirst().orElse(new DictionaryEntity()).getValue();
        Date endTime = (Date) dateList.stream().filter(item -> "endTime".equals(item.getKey().toString())).findFirst().orElse(new DictionaryEntity()).getValue();
        Date nowDate = (Date) dateList.stream().filter(item -> "nowDate".equals(item.getKey().toString())).findFirst().orElse(new DictionaryEntity()).getValue();
        String startFlag = dateList.stream().filter(item -> "startFlag".equals(item.getKey().toString())).findFirst().orElse(new DictionaryEntity()).getValue().toString();
        String endFlag = dateList.stream().filter(item -> "endFlag".equals(item.getKey().toString())).findFirst().orElse(new DictionaryEntity()).getValue().toString();
        Map<String, Object> dateMap = new HashMap<String, Object>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);

        startDate = DateUtils.setHours(startDate, calendar.get(Calendar.HOUR_OF_DAY));
        startDate = DateUtils.setMinutes(startDate, calendar.get(Calendar.MINUTE));
        startDate = DateUtils.setSeconds(startDate, calendar.get(Calendar.SECOND));

        dateMap.put("startFlag", startFlag);
        dateMap.put("endFlag", endFlag);

        dateMap.put("startDate", startDate);
        dateMap.put("endDate", endTime);
        dateMap.put("lastWeekEndDate", DateUtils.addDays(endTime, -7));
        dateMap.put("lastWeekStartDate", DateUtils.addDays(startDate, -7));
        dateMap.put("nowDate", nowDate);
        return dateMap;
    }

    @Override
    public Map<String, Object> getSearchTimeByDate(Date startDate, Date endDate) throws Exception {
        List<DictionaryEntity> dateList = getQueryStartAndEndDate(endDate);
        Date startTime = (Date) dateList.stream().filter(item -> "startTime".equals(item.getKey().toString())).findFirst().orElse(new DictionaryEntity()).getValue();
        Date endTime = (Date) dateList.stream().filter(item -> "endTime".equals(item.getKey().toString())).findFirst().orElse(new DictionaryEntity()).getValue();
        Date nowDate = (Date) dateList.stream().filter(item -> "nowDate".equals(item.getKey().toString())).findFirst().orElse(new DictionaryEntity()).getValue();
        String startFlag = dateList.stream().filter(item -> "startFlag".equals(item.getKey().toString())).findFirst().orElse(new DictionaryEntity()).getValue().toString();
        String endFlag = dateList.stream().filter(item -> "endFlag".equals(item.getKey().toString())).findFirst().orElse(new DictionaryEntity()).getValue().toString();
        Map<String, Object> dateMap = new HashMap<String, Object>();

        LinkedHashMap<String,Object> dateMaps=new LinkedHashMap<String,Object>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);

        startDate = DateUtils.setHours(startDate, calendar.get(Calendar.HOUR_OF_DAY));
        startDate = DateUtils.setMinutes(startDate, calendar.get(Calendar.MINUTE));
        startDate = DateUtils.setSeconds(startDate, calendar.get(Calendar.SECOND));

        dateMaps.put("startTime", startDate);
        dateMaps.put("endTime", endTime);
        dateMaps.put("oneWeekBeforeStartDate", DateUtils.addDays(endTime, -7));
        dateMaps.put("oneWeekBeforeEndDate", DateUtils.addDays(startDate, -7));
        dateMaps.put("startFlag", startFlag);
        dateMaps.put("endFlag", endFlag);
        dateMaps.put("nowDate", nowDate);
        return dateMaps;
    }

    /**
     * 日期格式加时间
     *
     * @param date
     * @return
     * @throws Exception
     * <AUTHOR> 2017-10-30
     */
    public Date getFormatDateTime(Date date) throws Exception {
        String queryTime = CommonPropertiesReader.getValue("query.time");
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        Date queryDate = sdf.parse(queryTime);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(queryDate);

        date = DateUtils.setHours(date, calendar.get(Calendar.HOUR_OF_DAY));
        date = DateUtils.setMinutes(date, calendar.get(Calendar.MINUTE));
        date = DateUtils.setSeconds(date, calendar.get(Calendar.SECOND));
        return date;
    }

    /**
     * 根据开始和结束日期获取Top N 最频繁的报警
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCode    装置编码
     * @param top       选择Top N数据
     * @return 最频繁报警Top N集合
     * <AUTHOR> 2017-10-30
     */
    @Override
    public List<AlarmEventExEntity> getMostAlarmTop(Date startTime, Date endTime, String unitCode, Integer top) throws Exception {
        List<AlarmEventExEntity> alarmEventExEntityList = new ArrayList<>();
        Long totalCount = (Long) getAlarmEventTotalCount(startTime, endTime, unitCode).get(0).getValue();
        Map<String, Object> paramList = new HashMap<>();
        try {
            String hql = "select new com.pcitc.opal.common.bll.entity.AlarmEventExEntity(min(ap.tag),min(af.name),count(*),min(ae.priority),min(pc.sname),min(pc.unitId),ap.alarmPointId,ae.alarmFlagId) from AlarmEvent ae "
                    + " join ae.alarmPoint ap "
                    + " join ap.prdtCell pc "
                    + " join ae.alarmFlag af %s group by pc.unitId,pc.prdtCellId,ap.alarmPointId,ae.alarmFlagId "
                    + " order by count(*) desc,min(ap.tag) asc,min(af.name) asc ";

            StringBuilder whereBuilder = new StringBuilder();

            whereBuilder.append(" where ae.companyId=:companyId and ae.eventTypeId=" + EventTypeEnum.ProcessEvent.getIndex() + " ");
            whereBuilder.append(" and ap.inUse = 1 ");
            if (unitCode != null) {
                whereBuilder.append(" and pc.unitId=:unitId ");
                paramList.put("unitId", unitCode);
            }
            if (startTime != null) {
                whereBuilder.append(" and ae.alarmTime>=:startTime ");
                paramList.put("startTime", startTime);
            }
            if (endTime != null) {
                whereBuilder.append(" and ae.alarmTime<=:endTime ");
                paramList.put("endTime", endTime);
            }
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            hql = String.format(hql, whereBuilder.toString());
            Query query = entityManager.createQuery(hql);
            setParameterList(query, paramList);

            query.setMaxResults(top);
            alarmEventExEntityList = query.getResultList();
            List<UnitEntity> unitList = factoryModelService.getUnitList(true);
            alarmEventExEntityList.stream().forEach(item -> {
                item.setPercentage(String.format("%.2f", (item.getCount() / (totalCount * 1.00) * 100)));
                item.setUnitName(unitList.stream().filter(u -> u.getStdCode().equals(item.getUnitId())).findFirst().orElse(new UnitEntity()).getSname());
            });
            return alarmEventExEntityList;
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 根据开始和结束日期获取Top N 最频繁的操作
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCode    装置编码
     * @param top       选择Top N数据
     * @return 最频繁操作Top N集合
     * <AUTHOR> 2017-10-30
     */
    @Override
    public List<AlarmEventExEntity> getMostAlarmOperateTop(Date startTime, Date endTime, String unitCode, Integer top) throws Exception {

        List<AlarmEventExEntity> alarmEventExEntityList = new ArrayList<>();
        try {
            String hql = "select new com.pcitc.opal.common.bll.entity.AlarmEventExEntity(min(ap.tag),min(af.name),count(*),min(ae.priority),min(pc.sname),min(pc.unitId),ap.alarmPointId,ae.alarmFlagId) from AlarmEvent ae "
                    + " join ae.alarmPoint ap "
                    + " join ap.prdtCell pc "
                    + " join ae.alarmFlag af "
                    + " join ae.eventType et "
                    + " %s group by pc.unitId,pc.prdtCellId,ap.alarmPointId,ae.alarmFlagId "
                    + " order by count(*) desc,min(ap.tag) asc,min(af.name) asc";

            StringBuilder whereBuilder = new StringBuilder();
            Map<String, Object> paramList = new HashMap<>();
            whereBuilder.append(" where (et.parentId=" + EventTypeEnum.OperateEvent.getIndex() + " or " + "et.eventTypeId="+EventTypeEnum.ConfirmedEvent.getIndex()+") ");
            whereBuilder.append(" and ap.inUse = 1 and ae.companyId=:companyId");

            if (unitCode != null) {
                whereBuilder.append(" and pc.unitId=:unitId ");
                paramList.put("unitId", unitCode);
            }
            if (startTime != null) {
                whereBuilder.append(" and ae.startTime>=:startTime ");
                paramList.put("startTime", startTime);
            }
            if (endTime != null) {
                whereBuilder.append(" and ae.startTime<=:endTime ");
                paramList.put("endTime", endTime);
            }

            hql = String.format(hql, whereBuilder.toString());
            Query query = entityManager.createQuery(hql);

            if (unitCode != null) {
                query.setParameter("unitId", unitCode);
            }
            if (startTime != null) {
                query.setParameter("startTime", startTime);
            }
            if (endTime != null) {
                query.setParameter("endTime", endTime);
            }
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            setParameterList(query, paramList);
            query.setMaxResults(top);
            alarmEventExEntityList = query.getResultList();
            List<UnitEntity> unitList = factoryModelService.getUnitList(true);
            alarmEventExEntityList.stream().forEach(item -> {
                item.setUnitName(unitList.stream().filter(u -> u.getStdCode().equals(item.getUnitId())).findFirst().orElse(new UnitEntity()).getSname());
            });
            return alarmEventExEntityList;
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 根据开始时间和结束时间及装置ID获取该装置的报警优先级比率
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCode    装置编码
     * @return 报警优先级分组总数
     * <AUTHOR> 2017-10-30
     */
    @Override
    public List<DictionaryEntity> getAlarmPriorityByUnitId(Date startTime, Date endTime, String unitCode) {
        //1.获取报警总数
        Long totalCount = (Long) getAlarmEventTotalCount(startTime, endTime, unitCode).get(0).getValue();
//企业
        CommonProperty commonProperty = new CommonProperty();
        List<DictionaryEntity> list = new ArrayList<>();
        String hql = " select new com.pcitc.opal.common.bll.entity.DictionaryEntity(ae.priority,count(*)) from AlarmEvent ae "
                + " left join ae.alarmPoint ap "
                + " left join ap.prdtCell pc "
                + " where  ae.companyId=:companyId and ae.eventTypeId=" + EventTypeEnum.ProcessEvent.getIndex() + " and pc.unitId=:unitId and ae.alarmTime between :startTime and :endTime and ae.priority in(1,2,3)  "
                + " and ae.alarmPointId is not null "
                + " and ae.alarmFlagId is not null "
                + " and ae.priority is not null "
                + " and ap.inUse = 1 "
                + " group by ae.priority";
        List<DictionaryEntity> queryList = entityManager.createQuery(hql)
                .setParameter("unitId", unitCode)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("companyId",commonProperty.getCompanyId())
                .getResultList();
        if (3 != queryList.size()) {
            if (queryList.stream().noneMatch(item -> "1".equals(item.getKey().toString()))) {
                queryList.add(new DictionaryEntity(1, 0));
            }
            if (queryList.stream().noneMatch(item -> "2".equals(item.getKey().toString()))) {
                queryList.add(new DictionaryEntity(2, 0));
            }
            if (queryList.stream().noneMatch(item -> "3".equals(item.getKey().toString()))) {
                queryList.add(new DictionaryEntity(3, 0));
            }
        }
        queryList.sort(Comparator.comparing((item) -> item.getKey().toString(), Comparator.naturalOrder()));
        queryList.forEach(item -> {
            switch (item.getKey().toString()) {
                case "1":
                    if ("0".equals(item.getValue().toString())) {
                        list.add(new DictionaryEntity(1, "0.00%"));
                    } else {
                        list.add(new DictionaryEntity(1, String.format("%.2f", (Double.valueOf(item.getValue().toString()) / totalCount) * 100) + "%"));
                    }
                    break;
                case "2":
                    if ("0".equals(item.getValue().toString())) {
                        list.add(new DictionaryEntity(2, "0.00%"));
                    } else {
                        list.add(new DictionaryEntity(2, String.format("%.2f", (Double.valueOf(item.getValue().toString()) / totalCount) * 100) + "%"));
                    }
                    break;
                case "3":
                    if ("0".equals(item.getValue().toString())) {
                        list.add(new DictionaryEntity(3, "0.00%"));
                    } else {
                        list.add(new DictionaryEntity(3, String.format("%.2f", (Double.valueOf(item.getValue().toString()) / totalCount) * 100) + "%"));
                    }
                    break;
                default:
                    break;
            }
        });
        return list;
    }

    /**
     * 获取服务器系统日期时间
     *
     * @return 服务器系统日期时间
     * <AUTHOR> 2017-10-31
     */
    @Override
    public List<DictionaryEntity> getSysDateTime() {
        List<DictionaryEntity> list = new ArrayList<>();
        list.add(new DictionaryEntity("dateTime", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
        return list;
    }

    /**
     * 根据报警事件父ID获取报警事件ID列表
     *
     * @param parentId 父ID
     * @return 报警事件ID列表
     * <AUTHOR> 2017-11-12
     */
    @Override
    public Long[] getEventTypeIdsByParentId(Long parentId) {
        String hql = "select t.eventTypeId from EventType t where t.parentId=:parentId";
        Map<String, Object> paramList = new HashMap<>();
        paramList.put("parentId", parentId);
        Query query = entityManager.createQuery(hql);
        setParameterList(query, paramList);

        List<Long> resultList = query.getResultList();
        Long[] arr = new Long[resultList.size()];
        return  resultList.toArray(arr);
    }

    /**
     * 获取报警数和操作数列表
     *
     * @param startTime  开始日期
     * @param endTime    结束日期
     * @param unitCodes    装置或者车间编码集合
     * @param prdtIds    生产单元ID集合
     * @param checkModel 模式 0:装置模式,1:工厂车间模式
     * @param dateType   查询日期类型(日:day,周:week,月:month,小时:hour)
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-17
     */
    @Override
    public List<AlarmNumberAssessEntity> getOperateAndAlarmNumberList(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, int checkModel, CommonEnum.DateTypeEnum dateType) throws Exception {
        //1.如果是工厂车间模式
        if (checkModel == FACTORY_MODE) {
            prdtIds = null;
            unitCodes = getUnitListByWorkshopIds(unitCodes, true).stream().map(item -> item.getStdCode()).toArray(String[]::new);
        }
        String[] ids;
        CommonEnum.EquipmentTypeEnum equipmentTypeEnum;
        List<UnitEntity> unitList = factoryModelService.getUnitList(true);

        if (unitCodes != null && unitCodes.length == 0) unitCodes = null;
        if (prdtIds != null && prdtIds.length == 0) prdtIds = null;

        //2.计算查询类型
        if (prdtIds != null) {
            ids = (String[])ConvertUtils.convert(prdtIds,String[].class);
            equipmentTypeEnum = CommonEnum.EquipmentTypeEnum.PrdtCell;
        } else {
            ids = unitCodes;
            equipmentTypeEnum = CommonEnum.EquipmentTypeEnum.Unit;
        }
        if (ids == null) {
            ids = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
        }
        return getAlarmNumberAssessList(startTime, endTime, dateType, ids, equipmentTypeEnum);
    }

    /**
     * 获取报警数量评估实体集合
     *
     * @param startTime 时间范围开始
     * @param endTime   时间范围结束
     * @param dateType  时间粒度
     * @param ids       ID数组
     * @param queryType 查询类型
     * @return 报警数量评估实体集合
     * @throws Exception
     * @throws ParseException
     * <AUTHOR> 2017-11-07  update by xuelei.wang 2017-11-17
     */
    @SuppressWarnings("all")
    @Override
    public List<AlarmNumberAssessEntity> getAlarmNumberAssessList(Date startTime, Date endTime, CommonEnum.DateTypeEnum dateType,
                                                                  String[] ids, CommonEnum.EquipmentTypeEnum queryType) throws Exception, ParseException {
        String hourTime = getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();
        int hours;
        ArrayList<AlarmNumberAssessEntity> returnList = new ArrayList<AlarmNumberAssessEntity>();

        //2.计算查询的开始时间和结束时间
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime);
        startTime = shiftDateCalculator.getQueryStartTime();
        endTime = shiftDateCalculator.getQueryEndTime();
        Date displayEndTime = shiftDateCalculator.getDisplayEndTime();
        //3.计算数据分组中的小时
        DateConverter dateConverter = new DateConverter();
        dateConverter.setPattern("HH:mm:ss");
        Date dateTime = dateConverter.convert(Date.class, hourTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateTime);
        hours = calendar.get(Calendar.HOUR_OF_DAY);

        Long operateEventId = EventTypeEnum.OperateEvent.getIndex();
        Long changeEventId = EventTypeEnum.ChangeEvent.getIndex();
        Long confirmedEventId = EventTypeEnum.ConfirmedEvent.getIndex();
        Map<String,String> map = new HashMap<String,String>(){{
            put("alarm_Time","ae.event_type_id = " + EventTypeEnum.ProcessEvent.getIndex());
//            put("start_time","(et.parent_id = " + operateEventId + " or et.parent_id = " + changeEventId + " or ae.event_type_id = " + operateEventId + " or ae.event_type_id = " + confirmedEventId +")");
            //为了优化执行效率，这里采用事件表为查询条件
            put("start_time","(ae.event_type_id in (30 ,1006,3001,3002,3003,3004,3005,3006,3007,300101,300102))");
        }};
        //报警数据
        List<AlarmNumberAssessTrendEntity> alarmEventAlarmList = new ArrayList<AlarmNumberAssessTrendEntity>();
        //操作数据
        List<AlarmNumberAssessTrendEntity> alarmEventOperationList = new ArrayList<AlarmNumberAssessTrendEntity>();

        long l = System.currentTimeMillis();
        List<Object[]> alarmList = eventRepo.getAlarmNumberAssessTrendEntityByTypeQue(ids, queryType, dateType, startTime, endTime, hours, map);
        System.out.println("耗时" + (System.currentTimeMillis() - l));
        alarmList.forEach(x -> {
            if (x[1].toString().equals("报警数")){
                AlarmNumberAssessTrendEntity entity = new AlarmNumberAssessTrendEntity();
                entity.setCount(Long.valueOf(x[0].toString()));
                entity.setName(x[1].toString());
                entity.setTimeStr(x[2].toString());
                alarmEventAlarmList.add(entity);
            } else if (x[1].toString().equals("操作数")) {
                AlarmNumberAssessTrendEntity entity = new AlarmNumberAssessTrendEntity();
                entity.setCount(Long.valueOf(x[0].toString()));
                entity.setName(x[1].toString());
                entity.setTimeStr(x[2].toString());
                alarmEventOperationList.add(entity);

            }

        });

        //4.合并集合
        List<AlarmNumberAssessTableEntity> alarmNumberAssessTableEntityList = combineList(alarmEventAlarmList,
                alarmEventOperationList, dateType, startTime);
        //5.拼装前台数据格式
        if (alarmEventAlarmList != null && alarmEventAlarmList.size() > 0) {
            AlarmNumberAssessEntity alarmNumberAssessEntity = getAlarmNumberAssessEntity(startTime, displayEndTime, dateType, alarmEventAlarmList);
            alarmNumberAssessEntity.setList(alarmNumberAssessTableEntityList);
            returnList.add(alarmNumberAssessEntity);
        }
        if (alarmEventOperationList != null && alarmEventOperationList.size() > 0) {
            returnList.add(getAlarmNumberAssessEntity(startTime, displayEndTime, dateType, alarmEventOperationList));
        }
        return returnList;
    }
    public String conversionTime() {
        //yyyy-MM-dd HH:mm:ss 转换的时间格式  可以自定义
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar.getInstance();
        return sdf.format( Calendar.getInstance().getTime());
    }
    /**
     * 获取报警数量评估实体集合
     *
     * @param startTime 时间范围开始
     * @param endTime   时间范围结束
     * @param dateType  时间粒度
     * @param ids       ID数组
     * @param queryType 查询类型
     * @return 报警数量评估实体集合
     * @throws Exception
     * @throws ParseException
     * <AUTHOR> 2019-12-30
     */
    @SuppressWarnings("all")
    @Override
    public List<AlarmNumberAssessEntity> getAlarmNumberAssessList(Date startTime, Date endTime,Long[] alarmFlagId, CommonEnum.DateTypeEnum dateType,
                                                                  String[] ids, CommonEnum.EquipmentTypeEnum queryType, Integer[] priority, Boolean priorityFlag,  Integer isElimination) throws Exception, ParseException {
        String hourTime = getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();
        int hours;
        ArrayList<AlarmNumberAssessEntity> returnList = new ArrayList<AlarmNumberAssessEntity>();

        //2.计算查询的开始时间和结束时间
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime);
//        startTime = shiftDateCalculator.getQueryStartTime();
//        endTime = shiftDateCalculator.getQueryEndTime();
        Date displayEndTime = shiftDateCalculator.getDisplayEndTime();
        //3.计算数据分组中的小时
        DateConverter dateConverter = new DateConverter();
        dateConverter.setPattern("HH:mm:ss");
        Date dateTime = dateConverter.convert(Date.class, hourTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateTime);
        hours = calendar.get(Calendar.HOUR_OF_DAY);

        Long operateEventId = EventTypeEnum.OperateEvent.getIndex();
        Map<String,String> map = new HashMap<String,String>(){{
            put("alarm_Time","ae.event_type_id = " + EventTypeEnum.ProcessEvent.getIndex());
            put("start_time","(et.parent_id = " + operateEventId + " or ae.event_type_id = " + operateEventId + ")");
        }};
        //报警数据
        List<AlarmNumberAssessTrendEntity> alarmEventAlarmList = new ArrayList<AlarmNumberAssessTrendEntity>();
        System.out.println(conversionTime()+"    TTTTTTTT     "+new Exception().getStackTrace()[0].getLineNumber());
        List<Object[]> alarmList = eventRepo.getAlarmNumberAssessTrendEntityByTypeQue(ids,alarmFlagId, queryType, dateType, startTime, endTime, hours, map, priority, priorityFlag, isElimination);
        alarmList.stream().filter(p->p[1].toString().equals("报警数")).forEach(x -> {
            AlarmNumberAssessTrendEntity entity = new AlarmNumberAssessTrendEntity();
            entity.setCount(Long.valueOf(x[0].toString()));
            entity.setName(x[1].toString());
            entity.setTimeStr(x[2].toString());
            alarmEventAlarmList.add(entity);
        });
        //操作数据
        List<AlarmNumberAssessTrendEntity> alarmEventOperationList = new ArrayList<AlarmNumberAssessTrendEntity>();
        alarmList.stream().filter(p->p[1].toString().equals("操作数")).forEach(x -> {
            AlarmNumberAssessTrendEntity entity = new AlarmNumberAssessTrendEntity();
            entity.setCount(Long.valueOf(x[0].toString()));
            entity.setName(x[1].toString());
            entity.setTimeStr(x[2].toString());
            alarmEventOperationList.add(entity);
        });
        System.out.println(conversionTime()+"    TTTTTTTT     "+new Exception().getStackTrace()[0].getLineNumber());
        //4.合并集合
        List<AlarmNumberAssessTableEntity> alarmNumberAssessTableEntityList = combineList(alarmEventAlarmList,
                alarmEventOperationList, dateType, startTime);
        //5.拼装前台数据格式
        if (alarmEventAlarmList != null && alarmEventAlarmList.size() > 0) {
            AlarmNumberAssessEntity alarmNumberAssessEntity = getAlarmNumberAssessEntity(startTime, displayEndTime, dateType, alarmEventAlarmList);
            alarmNumberAssessEntity.setList(alarmNumberAssessTableEntityList);
            returnList.add(alarmNumberAssessEntity);
        }
        if (alarmEventOperationList != null && alarmEventOperationList.size() > 0) {
            returnList.add(getAlarmNumberAssessEntity(startTime, displayEndTime, dateType, alarmEventOperationList));
        }
        return returnList;
    }

    /**
     * 合并报警和操作集合
     *
     * @param alarmEventAlarmList     报警事件集合
     * @param alarmEventOperationList 操作事件集合
     * @param dateType                时间粒度
     * @param startTime               时间范围开始
     * @return 报警数评估表格实体集合
     * @throws Exception
     * <AUTHOR> 2017-10-30 update by xuelei.wang 2017-11-17
     */
    @SuppressWarnings("all")
    private List<AlarmNumberAssessTableEntity> combineList(List<AlarmNumberAssessTrendEntity> alarmEventAlarmList,
                                                           List<AlarmNumberAssessTrendEntity> alarmEventOperationList,
                                                           CommonEnum.DateTypeEnum dateType,
                                                           Date startTime) throws Exception {
        List<AlarmNumberAssessTableEntity> alarmNumberAssessTableEntityList = ObjectConverter.listConverter(alarmEventAlarmList, AlarmNumberAssessTableEntity.class);
        List<AlarmNumberAssessTableEntity> tempList = new ArrayList<AlarmNumberAssessTableEntity>();

        if (alarmNumberAssessTableEntityList != null && alarmNumberAssessTableEntityList.size() > 0) {
            if (alarmEventOperationList != null && alarmEventOperationList.size() > 0) {
                int i = 0;
                for (AlarmNumberAssessTrendEntity option : alarmEventOperationList) {
                    i = 0;
                    for (AlarmNumberAssessTableEntity table : alarmNumberAssessTableEntityList) {
                        String optionTimeStr = option.getTimeStr();
                        String tableTimeStr = table.getTimeStr();
                        if (StringUtils.isNotEmpty(optionTimeStr) && StringUtils.isNotEmpty(tableTimeStr)) {
                            if (tableTimeStr.equals(optionTimeStr)) {
                                table.setOperationCount(option.getCount());
                                table.setOperationName(option.getName());
                            } else {
                                i++;
                            }
                        }
                    }
                    if (i == alarmNumberAssessTableEntityList.size()) {
                        tempList.add(new AlarmNumberAssessTableEntity(option.getName(), option.getTimeStr(), option.getCount()));
                    }
                }
            }
        } else {
            if (alarmEventOperationList != null && alarmEventOperationList.size() > 0) {
                alarmNumberAssessTableEntityList = ObjectConverter.listConverter(alarmEventOperationList, AlarmNumberAssessTableEntity.class);
                for (AlarmNumberAssessTableEntity table : alarmNumberAssessTableEntityList) {
                    table.setOperationCount(table.getCount());
                    table.setOperationName(table.getName());
                    table.setCount(0L);
                    table.setName(null);
                }
            }
        }
        alarmNumberAssessTableEntityList.addAll(tempList);
        alarmNumberAssessTableEntityList.sort(Comparator.comparing(AlarmNumberAssessTableEntity::getTimeStr, Comparator.naturalOrder()));

        if (dateType != null) {
            String queryTime = " " + CommonPropertiesReader.getValue("query.time");
            if (!dateType.equals(CommonEnum.DateTypeEnum.Hour)) {
                if (dateType.equals(CommonEnum.DateTypeEnum.Month)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
                    String format = sdf.format(startTime);
                    String m = format.split("-")[0];
                    String d = format.split("-")[1];
                    for (AlarmNumberAssessTableEntity entity : alarmNumberAssessTableEntityList) {
                        String timeStr = entity.getTimeStr();
                        String month = timeStr.substring(5);
                        if (month.equals(m)) {
                            entity.setTimeStr(timeStr + "-" + d + queryTime);
                        } else {
                            entity.setTimeStr(timeStr + "-01" + queryTime);
                        }
                    }
                } else {
                    for (AlarmNumberAssessTableEntity entity : alarmNumberAssessTableEntityList) {
                        entity.setTimeStr(entity.getTimeStr() + queryTime);
                    }
                }
            }
        }

        return alarmNumberAssessTableEntityList;
    }

    /**
     * 报警数量评估——趋势图数据——格式拼装
     *
     * @param startTime      时间范围开始
     * @param endTime        时间范围结束
     * @param dateType       时间粒度
     * @param alarmEventList 报警事件集合
     * @return 报警数量评估实体集合
     * @throws ParseException
     * <AUTHOR> 2017-10-30 update by xuelei.wang 2017-11-17
     */
    @SuppressWarnings("all")
    private AlarmNumberAssessEntity getAlarmNumberAssessEntity(Date startTime, Date endTime, CommonEnum.DateTypeEnum dateType, List<AlarmNumberAssessTrendEntity> alarmEventList)
            throws ParseException {

        String hourTime = this.getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();
        List<String> xaxisList = new ArrayList<>();
        List<Long> countsList = new ArrayList<>();
        List<String> tipList = new ArrayList<>();
        AlarmNumberAssessEntity alarmNumberAssessEntity = new AlarmNumberAssessEntity();
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfTip = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdfMonth = new SimpleDateFormat("yyyy-MM");
        AlarmNumberAssessTrendEntity anm = new AlarmNumberAssessTrendEntity();
        String queryTime = " " + CommonPropertiesReader.getValue("query.time");
        switch (dateType) {
            case Hour:
                for (AlarmNumberAssessTrendEntity entity : alarmEventList) {
                    anm = entity;
                    if (xaxisList.size() == 0) {
                        Date timeda = startTime;
                        while (sdf.parse(entity.getTimeStr()).getTime() > timeda.getTime()) {
                            xaxisList.add(sdf.format(timeda));
                            countsList.add(0L);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addHours(timeda, 1);
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(timeda) + "<br>" + entity.getName() + ":" + 0 + "");
                        }
                    } else {
                        Date timeda = sdf.parse(xaxisList.get(xaxisList.size() - 1));
                        timeda = DateUtils.addHours(timeda, 1);
                        while (sdf.parse(entity.getTimeStr()).getTime() > timeda.getTime()) {
                            xaxisList.add(sdf.format(timeda));
                            countsList.add((long) 0);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addHours(timeda, 1);
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(timeda) + "<br>" + entity.getName() + ":" + 0 + "");
                        }
                    }
                    cal.setTime(sdf.parse(entity.getTimeStr()));
                    cal.add(Calendar.HOUR_OF_DAY, 1);
                    xaxisList.add(entity.getTimeStr());
                    if (cal.getTime().getTime() > endTime.getTime()) {
                        tipList.add("从:" + entity.getTimeStr() + "至: " + sdf.format(endTime) + "<br>" + entity.getName() + ":" + entity.getCount());
                    } else {
                        tipList.add("从:" + entity.getTimeStr() + "至: " + sdf.format(cal.getTime()) + "<br>" + entity.getName() + ":" + entity.getCount());
                    }
                    countsList.add(entity.getCount());
                }
                if (xaxisList.size() > 0) {
                    Date timeda = sdf.parse(xaxisList.get(xaxisList.size() - 1));
                    timeda = DateUtils.addHours(timeda, 1);
                    while (endTime.getTime() > timeda.getTime()) {
                        xaxisList.add(sdf.format(timeda));
                        countsList.add((long) 0);
                        Date timeBeg = timeda;
                        timeda = DateUtils.addHours(timeda, 1);
                        if (timeda.getTime() > endTime.getTime()) {
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(endTime) + "<br>" + anm.getName() + ":" + 0 + "");
                        } else {
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                        }
                    }
                }
                break;
            case Day:
                for (AlarmNumberAssessTrendEntity entity : alarmEventList) {
                    anm = entity;
                    if (xaxisList.size() == 0) {
                        Date timeda = startTime;
                        while (sdf.parse(entity.getTimeStr() + queryTime).getTime() > timeda.getTime()) {
                            xaxisList.add(sdfTip.format(timeda));
                            countsList.add((long) 0);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addDays(timeda, 1);
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(timeda) + "<br>" + entity.getName() + ":" + 0 + "");
                        }
                    } else {
                        Date timeda = sdf.parse(xaxisList.get(xaxisList.size() - 1) + queryTime);
                        timeda = DateUtils.addDays(timeda, 1);
                        while (sdf.parse(entity.getTimeStr() + queryTime).getTime() > timeda.getTime()) {
                            xaxisList.add(sdfTip.format(timeda));
                            countsList.add((long) 0);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addDays(timeda, 1);
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(timeda) + "<br>" + entity.getName() + ":" + 0 + "");
                        }
                    }
                    cal.setTime(sdf.parse(entity.getTimeStr() + " " + hourTime));
                    cal.add(Calendar.DAY_OF_MONTH, 1);
                    xaxisList.add(entity.getTimeStr());
                    if (cal.getTime().getTime() > endTime.getTime()) {
                        tipList.add("从:" + entity.getTimeStr() + " " + hourTime + "至: " + sdf.format(endTime) + "<br>" + entity.getName() + ":" + entity.getCount());
                    } else {
                        tipList.add("从:" + entity.getTimeStr() + " " + hourTime + "至: " + sdf.format(cal.getTime()) + "<br>" + entity.getName() + ":" + entity.getCount());
                    }
                    entity.setTimeStr(entity.getTimeStr() + " " + hourTime);
                    countsList.add(entity.getCount());
                }
                if (xaxisList.size() > 0) {
                    Date timeda = sdf.parse(xaxisList.get(xaxisList.size() - 1) + queryTime);
                    timeda = DateUtils.addDays(timeda, 1);
                    while (endTime.getTime() > timeda.getTime()) {
                        xaxisList.add(sdfTip.format(timeda));
                        countsList.add((long) 0);
                        Date timeBeg = timeda;
                        timeda = DateUtils.addDays(timeda, 1);
                        if (timeda.getTime() > endTime.getTime()) {
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(endTime) + "<br>" + anm.getName() + ":" + 0 + "");
                        } else {
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                        }
                    }
                }
                break;
            case Week:
                for (AlarmNumberAssessTrendEntity entity : alarmEventList) {
                    anm = entity;
                    if (xaxisList.size() == 0) {
                        Date timeda = startTime;
                        while (sdf.parse(entity.getTimeStr() + queryTime).getTime() > timeda.getTime()) {
                            xaxisList.add(sdfTip.format(timeda));
                            countsList.add((long) 0);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addWeeks(timeda, 1);
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(timeda) + "<br>" + entity.getName() + ":" + 0 + "");
                        }
                    } else {
                        Date timeda = sdf.parse(xaxisList.get(xaxisList.size() - 1) + queryTime);
                        timeda = DateUtils.addWeeks(timeda, 1);
                        while (sdf.parse(entity.getTimeStr() + queryTime).getTime() > timeda.getTime()) {
                            xaxisList.add(sdfTip.format(timeda));
                            countsList.add((long) 0);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addWeeks(timeda, 1);
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(timeda) + "<br>" + entity.getName() + ":" + 0 + "");
                        }
                    }
                    cal.setTime(sdf.parse(entity.getTimeStr() + " " + hourTime));
                    cal.add(Calendar.WEEK_OF_YEAR, 1);
                    xaxisList.add(entity.getTimeStr());
                    if (cal.getTime().getTime() > endTime.getTime()) {
                        tipList.add("从:" + entity.getTimeStr() + " " + hourTime + "至: " + sdf.format(endTime) + "<br>" + entity.getName() + ":" + entity.getCount());
                    } else {
                        tipList.add("从:" + entity.getTimeStr() + " " + hourTime + "至: " + sdf.format(cal.getTime()) + "<br>" + entity.getName() + ":" + entity.getCount());
                    }
                    entity.setTimeStr(entity.getTimeStr() + " " + hourTime);
                    countsList.add(entity.getCount());
                }
                if (xaxisList.size() > 0) {
                    Date timeda = sdf.parse(xaxisList.get(xaxisList.size() - 1) + queryTime);
                    timeda = DateUtils.addWeeks(timeda, 1);
                    while (endTime.getTime() > timeda.getTime()) {
                        xaxisList.add(sdfTip.format(timeda));
                        countsList.add((long) 0);
                        Date timeBeg = timeda;
                        timeda = DateUtils.addWeeks(timeda, 1);
                        if (timeda.getTime() > endTime.getTime()) {
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(endTime) + "<br>" + anm.getName() + ":" + 0 + "");
                        } else {
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                        }
                    }
                }
                break;
            case Month:
                for (AlarmNumberAssessTrendEntity entity : alarmEventList) {
                    anm = entity;
                    if (countsList.size() == 0) {
                        Date timeda = startTime;
                        while (sdf.parse(entity.getTimeStr() + "-01" + queryTime).getTime() > timeda.getTime()) {
                            xaxisList.add(sdfMonth.format(timeda));
                            countsList.add((long) 0);
                            Date timeBeg = timeda;
                            if (timeda.getTime() == startTime.getTime()) {
                                timeda = sdf.parse(sdfMonth.format(timeda) + "-01" + queryTime);
                            }
                            timeda = DateUtils.addMonths(timeda, 1);
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(timeda) + "<br>" + entity.getName() + ":" + 0 + "");
                        }
                    } else {
                        Date timeda = sdf.parse(xaxisList.get(xaxisList.size() - 1) + "-01" + queryTime);
                        timeda = DateUtils.addMonths(timeda, 1);
                        while (sdf.parse(entity.getTimeStr() + "-01" + queryTime).getTime() > timeda.getTime()) {
                            xaxisList.add(sdfMonth.format(timeda));
                            countsList.add((long) 0);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addMonths(timeda, 1);
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(timeda) + "<br>" + entity.getName() + ":" + 0 + "");
                        }
                    }
                    String tipStr = "";
                    xaxisList.add(entity.getTimeStr());
                    cal.setTime(sdf.parse(entity.getTimeStr() + "-01 " + hourTime));
                    if (startTime.getTime() > cal.getTime().getTime()) {
                        tipStr = "从:" + sdfTip.format(startTime) + " " + hourTime + "至: ";
                        entity.setTimeStr(sdfTip.format(startTime) + " " + hourTime);
                    } else {
                        tipStr = "从:" + entity.getTimeStr() + "-01 " + hourTime + "至: ";
                        entity.setTimeStr(entity.getTimeStr() + "-01 " + hourTime);
                    }
                    cal.add(Calendar.MONTH, 1);
                    if (cal.getTime().getTime() > endTime.getTime()) {
                        tipStr += sdf.format(endTime);
                    } else {
                        tipStr += sdf.format(cal.getTime());
                    }
                    tipList.add(tipStr + "<br>" + entity.getName() + ":" + entity.getCount());
                    countsList.add(entity.getCount());
                }
                if (xaxisList.size() > 0) {
                    Date timeda = sdf.parse(xaxisList.get(xaxisList.size() - 1) + "-01" + queryTime);
                    timeda = DateUtils.addMonths(timeda, 1);
                    while (endTime.getTime() > timeda.getTime()) {
                        xaxisList.add(sdfMonth.format(timeda));
                        countsList.add(0L);
                        Date timeBeg = timeda;
                        timeda = DateUtils.addMonths(timeda, 1);
                        if (timeda.getTime() > endTime.getTime()) {
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(endTime) + "<br>" + anm.getName() + ":" + 0 + "");
                        } else {
                            tipList.add("从:" + sdf.format(timeBeg) + "至: " + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                        }
                    }
                }
                break;
        }
        alarmNumberAssessEntity.setName(alarmEventList.get(0).getName());
        alarmNumberAssessEntity.setXaxis(xaxisList);
        alarmNumberAssessEntity.setTip(tipList);
        alarmNumberAssessEntity.setCounts(countsList);
        return alarmNumberAssessEntity;
    }

    /**
     * 获取图表X轴显示日期区间列表
     *
     * @param startTime 查询开始日期时间
     * @param endTime   查询结束日期时间
     * @param dateType  日期类型(day|month|week|hour)
     * @return
     * <AUTHOR> 2017-11-18
     */
    @Override
    public List<DictionaryEntity> getChartDateShowPeriod(Date startTime, Date endTime, String dateType) {

        CommonEnum.DateTypeEnum dateTypeEnum = CommonEnum.DateTypeEnum.fromValue(dateType);
        List<DictionaryEntity> list = new ArrayList<>();
        Date startTemp = startTime;

        String hourTime = getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();

        DateConverter dateConverter = new DateConverter();
        dateConverter.setPattern("HH:mm:ss");
        Date dateTime = dateConverter.convert(Date.class, hourTime);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateTime);

        switch (dateTypeEnum) {
            case Hour:
                //X轴开始时间
                list.add(new DictionaryEntity("xStartTime", DateFormatUtils.format(startTemp, "yyyy-MM-dd HH")));
                //X轴结束时间
                list.add(new DictionaryEntity("xEndTime", DateFormatUtils.format(endTime, "yyyy-MM-dd HH")));
                break;
            case Day:
                endTime = DateUtils.addDays(endTime, 1);
                //X轴开始时间
                list.add(new DictionaryEntity("xStartTime", DateFormatUtils.format(startTemp, "yyyy-MM-dd")));
                //X轴结束时间
                if (DateUtils.isSameDay(endTime, new Date())) {
                    list.add(new DictionaryEntity("xEndTime", DateFormatUtils.format(endTime, "yyyy-MM-dd")));
                } else {
                    list.add(new DictionaryEntity("xEndTime", DateFormatUtils.format(endTime, "yyyy-MM-dd")));
                }
                break;
            case Week:
                endTime = DateUtils.addDays(endTime, 1);
                //X轴开始时间
                list.add(new DictionaryEntity("xStartTime", DateFormatUtils.format(startTemp, "yyyy-MM-dd")));
                //X轴结束时间
                list.add(new DictionaryEntity("xEndTime", DateFormatUtils.format(endTime, "yyyy-MM-dd")));
                break;
            case Month:
                endTime = DateUtils.addMonths(endTime, 1);
                //X轴开始时间
                list.add(new DictionaryEntity("xStartTime", DateFormatUtils.format(startTemp, "yyyy-MM")));
                //X轴结束时间
                list.add(new DictionaryEntity("xEndTime", DateFormatUtils.format(endTime, "yyyy-MM")));
                break;
            default:
                break;
        }

        //查询开始时间
        list.add(new DictionaryEntity("queryStartTime", DateFormatUtils.format(startTemp, "yyyy-MM-dd HH:mm:ss")));
        //查询结束时间
        list.add(new DictionaryEntity("queryEndTime", DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss")));
        return list;
    }

    /**
     * 查看因果报警分析-一级列表数据
     *
     * @param startTime   时间范围起始
     * @param endTime     时间分为结束
     * @param unitCodes     装置编码数组
     * @param prdtCellIds 生产单元ID数组
     * @param page        分页对象
     * @return 一级列表数据
     * <AUTHOR> 2017-11-16
     */
    @Override
    public PaginationBean<CausalAlarmAnalysisEntity> getCausalAlarmAnalysis(Date startTime, Date endTime,
                                                                            String[] unitCodes, Long[] prdtCellIds, Pagination page) throws Exception {

        try {
//企业
            CommonProperty commonProperty = new CommonProperty();
            String queryUnitIds = "";
            String queryPrdtIds = "";
            if (null == unitCodes || unitCodes.length == 0)
                unitCodes = factoryModelService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
            queryUnitIds = StringUtils.join(unitCodes,",");
            if (null != prdtCellIds) {
                queryPrdtIds = StringUtils.join(prdtCellIds,",");
            }
            Long total = -1L;
            PaginationBean<CausalAlarmAnalysisEntity> returnList = new PaginationBean<CausalAlarmAnalysisEntity>(page, total);
            int beginIndex = returnList.getBeginIndex() + 1;
            int endIndex = beginIndex + returnList.getPageSize() - 1;
            StoredProcedureQuery query = entityManager.createStoredProcedureQuery("P_AF_GETCAUSALALARM");
            query.registerStoredProcedureParameter("v_in_startDate", Date.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_endDate", Date.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_unitIds", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_prdtIds", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_beginIndex", Integer.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_endIndex", Integer.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_companyId",Integer.class,ParameterMode.IN);
            if("oracle".equals(dbConfig.getDataBase())) {
                query.registerStoredProcedureParameter("v_out_result", void.class, ParameterMode.REF_CURSOR);
            }
            query.setParameter("v_in_startDate", startTime);
            query.setParameter("v_in_endDate", endTime);
            query.setParameter("v_in_unitIds", queryUnitIds);
            query.setParameter("v_in_prdtIds", queryPrdtIds);
            query.setParameter("v_in_beginIndex", beginIndex);
            query.setParameter("v_in_endIndex", endIndex);
            query.setParameter("v_in_companyId",commonProperty.getCompanyId());

            List<Object[]> resultPage = query.getResultList();
            String[] filterUnitCodes = resultPage.stream().map(e -> e[2].toString()).distinct().toArray(String[]::new);
            List<UnitEntity> unitEntityList = this.getUnitListByIds(filterUnitCodes,false);
            //获取分页集合和分页总数
            List<CausalAlarmAnalysisEntity> causalAlarmAnalysisEntityList = new ArrayList<CausalAlarmAnalysisEntity>();
            for (int i = 0; i < resultPage.size(); i++) {
                CausalAlarmAnalysisEntity entity = new CausalAlarmAnalysisEntity();
                Object[] element = resultPage.get(i);
                entity.setAlarmPointId(NumberUtils.toLong(element[0].toString()));
                entity.setAlarmFlagId(NumberUtils.toLong(element[1].toString()));
                entity.setUnitId(element[2].toString());
                entity.setTag(element[3].toString());
                entity.setPrdtCellSname(element[4].toString());
                entity.setAlarmFlagName(element[5].toString());
                entity.setCount(NumberUtils.toLong(element[6].toString()));
                entity.setLocation(null != ObjectUtils.toString(element[9],null)? element[9].toString():null);
                if (returnList.getTotal() == -1) {
                    returnList.setTotal(NumberUtils.toLong(element[7].toString()));
                }
                entity.setUnitName(unitEntityList.stream().filter(u -> u.getStdCode().equals(entity.getUnitId())).findFirst().orElse(new UnitEntity()).getSname());
                causalAlarmAnalysisEntityList.add(entity);
            }
            returnList.setPageList(causalAlarmAnalysisEntityList);
            return returnList;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 查看因果报警分析-二级列表数据
     *
     * @param startTime    时间范围起始
     * @param endTime      时间分为结束
     * @param alarmPointId 报警点ID
     * @param alarmFlagId  报警标识ID
     * @param unitCode       装置编码
     * @param page         分页对象
     * @return 二级列表数据
     * <AUTHOR> 2017-11-16
     */
    @Override
    public PaginationBean<CausalAlarmAnalysisTableEntity> getCausalAlarmAnalysisTable(Date startTime, Date endTime,
                                                                                      Long alarmPointId, Long alarmFlagId, String unitCode, Pagination page) {
        try {
            Long total = -1L;
            PaginationBean<CausalAlarmAnalysisTableEntity> returnList = new PaginationBean<CausalAlarmAnalysisTableEntity>(page, total);
            int beginIndex = 1;
            int endIndex = Integer.MAX_VALUE;
//企业
            CommonProperty commonProperty = new CommonProperty();
            StoredProcedureQuery query = entityManager.createStoredProcedureQuery("P_AF_GETCAUSALALARMDETAIL");
            query.registerStoredProcedureParameter("v_in_startDate", Date.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_endDate", Date.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_unitid", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_alarmPointId", Long.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_alarmFlagId", Long.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_beginIndex", Integer.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_endIndex", Integer.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_companyId",Integer.class,ParameterMode.IN);
            if("oracle".equals(dbConfig.getDataBase())){
                query.registerStoredProcedureParameter("v_out_result", void.class, ParameterMode.REF_CURSOR);
            }
            query.setParameter("v_in_startDate", startTime);
            query.setParameter("v_in_endDate", endTime);
            query.setParameter("v_in_unitid", unitCode);
            query.setParameter("v_in_alarmPointId", alarmPointId);
            query.setParameter("v_in_alarmFlagId", alarmFlagId);
            query.setParameter("v_in_beginIndex", beginIndex);
            query.setParameter("v_in_endIndex", endIndex);
            query.setParameter("v_in_companyId",commonProperty.getCompanyId());

            List<Object[]> resultPage = query.getResultList();
            //获取分页集合
            List<CausalAlarmAnalysisTableEntity> causalAlarmAnalysisTableEntityList = new ArrayList<>();

            for (int i = 0; i < resultPage.size(); i++) {
                CausalAlarmAnalysisTableEntity entity = new CausalAlarmAnalysisTableEntity();
                Object[] element = resultPage.get(i);
                entity.setPrdtCellName(element[3].toString());
                entity.setTagName(element[2].toString());
                entity.setAlarmFlagName(element[4].toString());
                entity.setAlarmTimes(Long.valueOf(element[7].toString()));
                entity.setForecast(element[6] == null ? "0.00" : String.format("%.2f", Math.min(Double.valueOf(element[6].toString()), 1.0) * 100.00));
                entity.setImportant(element[5] == null ? "0.00" : String.format("%.2f", Math.min(Double.valueOf(element[5].toString()), 1.0) * 100.00));
                entity.setLocation(null != element[10]?element[10].toString():null);
                if (returnList.getTotal() == -1) {
                    returnList.setTotal(NumberUtils.toLong(element[8].toString()));
                }
                causalAlarmAnalysisTableEntityList.add(entity);
            }
            returnList.setPageList(causalAlarmAnalysisTableEntityList);
            return returnList;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 获取报警国际标准列表
     *
     * @return 报警国际标准列表
     * <AUTHOR> 2017-11-22
     */
    @Override
    public List<DictionaryEntity> getAlarmIso() {
        List<DictionaryEntity> list = new ArrayList<>();
        //6.1紧急报警
        list.add(new DictionaryEntity("alarm_iso_emergency", CommonPropertiesReader.getValue("alarm.iso.emergency")));
        //6.2重要报警
        list.add(new DictionaryEntity("alarm_iso_importance", CommonPropertiesReader.getValue("alarm.iso.importance")));
        //6.3一般报警
        list.add(new DictionaryEntity("alarm_iso_normal", CommonPropertiesReader.getValue("alarm.iso.normal")));

        //6.4每天报警最佳
        list.add(new DictionaryEntity("alarm_day_iso_best", CommonPropertiesReader.getValue("alarm.day.iso.best")));
        //6.5每天报警达标
        list.add(new DictionaryEntity("alarm_day_iso_standard", CommonPropertiesReader.getValue("alarm.day.iso.standard")));
        //6.6每天报警超标
        list.add(new DictionaryEntity("alarm_day_iso_over", CommonPropertiesReader.getValue("alarm.day.iso.over")));

        //6.7平均报警率最佳
        list.add(new DictionaryEntity("alarm_avg_iso_best", CommonPropertiesReader.getValue("alarm.avg.iso.best")));
        //6.8平均报警率达标
        list.add(new DictionaryEntity("alarm_avg_iso_standard", CommonPropertiesReader.getValue("alarm.avg.iso.standard")));
        //6.9平均报警率超标
        list.add(new DictionaryEntity("alarm_avg_iso_over", CommonPropertiesReader.getValue("alarm.avg.iso.over")));

        //6.10峰值报警率达标
        list.add(new DictionaryEntity("alarm_max_value_iso_standard", CommonPropertiesReader.getValue("alarm.max.value.iso.standard")));
        //6.11峰值报警率超标
        list.add(new DictionaryEntity("alarm_max_value_iso_over", CommonPropertiesReader.getValue("alarm.max.value.iso.over")));

        //6.12扰动报警率达标
        list.add(new DictionaryEntity("alarm_disturbance_iso_standard", CommonPropertiesReader.getValue("alarm.disturbance.iso.standard")));
        //6.13扰动率超标
        list.add(new DictionaryEntity("alarm_disturbance_iso_over", CommonPropertiesReader.getValue("alarm.disturbance.iso.over")));

        //6.14高频报警事件百分比达标
        list.add(new DictionaryEntity("alarm_flood_iso_standard", CommonPropertiesReader.getValue("alarm.flood.iso.standard")));
        //6.15高频报警事件百分比超标
        list.add(new DictionaryEntity("alarm_flood_iso_over", CommonPropertiesReader.getValue("alarm.flood.iso.over")));

        //6.16震荡报警达标
        list.add(new DictionaryEntity("alarm_shock_iso_standard", CommonPropertiesReader.getValue("alarm.shock.iso.standard")));
        //6.17震荡报警超标
        list.add(new DictionaryEntity("alarm_shock_iso_over", CommonPropertiesReader.getValue("alarm.shock.iso.over")));

        //6.18每天持续报警达标
        list.add(new DictionaryEntity("alarm_day_continuous_standard", CommonPropertiesReader.getValue("alarm.day.continuous.standard")));
        //6.19每天持续报警超标
        list.add(new DictionaryEntity("alarm_day_continuous_over", CommonPropertiesReader.getValue("alarm.day.continuous.over")));
        return list;
    }

    /**
     * 持续报警分析分页查询
     *
     * @param unitCodes     装置编码集合
     * @param prdtCellIds 生产单元ID集合
     * @param alarmFlagId 报警标识
     * @param isHandle    是否处理（0:未处理,1:已处理,-1:全部）
     * @param beginTime   报警事件开始间
     * @param endTime     报警事件结束时间
     * @param page        分页实现类
     * @return 报警事件实体集合
     * @throws Exception 
     * <AUTHOR> 2017-11-2 update by xuelei.wang 2017-11-24
     */
    @Override
    public PaginationBean<AlarmEventEntity> getPersistentAlarmAnalysis(String[] unitCodes, Long[] prdtCellIds,
                                                                       Integer alarmFlagId, Integer isHandle, Date beginTime, Date endTime, Pagination page) throws Exception {
        if (isHandle == null) isHandle = -1;
        try {
//            List<UnitEntity> unitList = getUnitListByIds(unitCodes, true);
            String vUnitIds = StringUtils.join(unitCodes, ",");
            String vPrdtIds = StringUtils.join(prdtCellIds, ",");
            if (StringUtils.isBlank(vPrdtIds)) vPrdtIds = "";
            if (StringUtils.isBlank(vUnitIds)) vUnitIds = "";
            Long total = 0L;
            PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<AlarmEventEntity>(page, total);
            int beginIndex = returnAlarmEvent.getBeginIndex() + 1;
            int endIndex = beginIndex + returnAlarmEvent.getPageSize() - 1;
            StoredProcedureQuery query = entityManager.createStoredProcedureQuery("P_AF_GETPERSISTENTALARM");
            query.registerStoredProcedureParameter("v_in_startDate", Date.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_endDate", Date.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_unitIds", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_prdtIds", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_alarmFlag", Integer.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_isHandle", Integer.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_beginIndex", Integer.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_endIndex", Integer.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_companyId",Integer.class,ParameterMode.IN);
            if("oracle".equals(dbConfig.getDataBase())){
                query.registerStoredProcedureParameter("v_out_result", void.class, ParameterMode.REF_CURSOR);
            }
            query.setParameter("v_in_startDate", beginTime);
            query.setParameter("v_in_endDate", endTime);
            query.setParameter("v_in_unitIds", vUnitIds);
            query.setParameter("v_in_prdtIds", vPrdtIds);
            query.setParameter("v_in_alarmFlag", alarmFlagId);
            query.setParameter("v_in_isHandle", isHandle);
            query.setParameter("v_in_beginIndex", beginIndex);
            query.setParameter("v_in_endIndex", endIndex);
            Integer companyId = new CommonProperty().getCompanyId();
            //如果企业id为空则根据装置获取
            if (companyId != null){
                query.setParameter("v_in_companyId", companyId);
            }else {
                query.setParameter("v_in_companyId", basicDataService.getCompanyIdByUnit(unitCodes));
            }

            List<Object[]> resultPage = query.getResultList();
            //获取分页集合
            List<AlarmEventEntity> alarmEventEntityList = new ArrayList<AlarmEventEntity>();

            if (resultPage != null && resultPage.size() != 0) {
                for (Object[] element : resultPage) {
                    AlarmEventEntity entity = new AlarmEventEntity();
                    entity.setEventId(Long.valueOf(String.valueOf(element[0])));
                    entity.setContinuousHour(String.format("%.1f",Double.valueOf(element[19].toString())/3600));
                    //查找装置
//                    UnitEntity unit = unitList.stream().filter(u -> u.getStdCode().equals(element[6].toString())).findFirst().orElse(new UnitEntity());
                    entity.setUnitCode(String.valueOf(element[6]));
                    entity.setUnitName(String.valueOf(element[22]));
                    entity.setPrdtCellName(String.valueOf(element[8]));
                    entity.setEventTypeId(Long.valueOf(String.valueOf(element[5])));
                    entity.setEventTypeName(element[17] != null ? element[17].toString() : null);
                    entity.setAlarmPointTag(String.valueOf(element[9]));
                    entity.setAlarmFlagName(String.valueOf(element[10]));
                    entity.setPriority(Integer.valueOf(String.valueOf(element[11])));
                    entity.setStartTime((Date) element[4]);
                    entity.setAlarmTime((Date) element[3]);
                    entity.setNowValue(String.valueOf(element[13]));
                    entity.setMeasUnitName(String.valueOf(element[14]));
                    entity.setDes(element[15] != null ? element[15].toString() : null);
                    entity.setAlarmPointLocation(String.valueOf(element[12]));
                    entity.setRecoveryTime((Date) element[23]);
                    if (element[18] != null)
                        entity.setHandleTime((Date) element[18]);
                    try {
                        returnAlarmEvent.setTotal(NumberUtils.toLong(element[20].toString()));
                    } catch (Exception ex) {
                        returnAlarmEvent.setTotal(0L);
                    }
                    entity.setAlarmPointId(Long.valueOf(String.valueOf(element[1])));
                    entity.setAlarmFlagId(Long.valueOf(String.valueOf(element[2])));
                    entity.setMonitorTypeStr(element[24]!= null? CommonEnum.MonitorTypeEnum.getName(Integer.valueOf(String.valueOf(element[24]))) : null);
                    alarmEventEntityList.add(entity);
                }
            }
            returnAlarmEvent.setPageList(alarmEventEntityList);
            return returnAlarmEvent;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<AlarmEventEntity> getAlarmAmount(String[] unitCodes, Date beginTime, Date endTime) throws Exception {
//企业
        CommonProperty commonProperty = new CommonProperty();
        try {
            List<UnitEntity> unitList = getUnitListByIds(unitCodes, true);
            String vUnitIds = StringUtils.join(unitCodes, ",");

            if (StringUtils.isBlank(vUnitIds)) vUnitIds = "";

            StoredProcedureQuery query = entityManager.createStoredProcedureQuery("P_AF_GETPERSISTENTALARM_SUM");
            query.registerStoredProcedureParameter("v_in_startDate", Date.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_endDate", Date.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_unitIds", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("v_in_companyId",Integer.class,ParameterMode.IN);
            if("oracle".equals(dbConfig.getDataBase())){
                query.registerStoredProcedureParameter("v_out_result", void.class, ParameterMode.REF_CURSOR);
            }
            query.setParameter("v_in_startDate", beginTime);
            query.setParameter("v_in_endDate", endTime);
            query.setParameter("v_in_unitIds", vUnitIds);
            query.setParameter("v_in_companyId",commonProperty.getCompanyId());
            List<Object[]> resultPage = query.getResultList();
            //获取分页集合
            List<AlarmEventEntity> alarmEventEntityList = new ArrayList<AlarmEventEntity>();

            if (resultPage != null && resultPage.size() != 0) {
                for (Object[] element : resultPage) {
                    AlarmEventEntity entity = new AlarmEventEntity();

                    //查找装置
                    UnitEntity unit = unitList.stream().filter(u -> u.getStdCode().equals(element[0].toString())).findFirst().orElse(new UnitEntity());
                    entity.setUnitCode(String.valueOf(element[0]));
                    entity.setUnitName(unit.getSname());
                    entity.setCount(NumberUtils.toLong(element[1].toString()));

                    alarmEventEntityList.add(entity);
                }
            }
            return alarmEventEntityList;
           // return  null;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 根据中装置ID获取操作工人数
     *
     * @param unitCode
     * @return 操作工人数
     * @throws Exception
     * <AUTHOR> 2017-11-30
     */
    @Override
    public Long getOperatePersonCountByUnitId(String unitCode) throws Exception {
        try {
            String hql = "from UnitPerson p where p.unitId=:unitId";
            List<UnitPerson> unitPersonList = entityManager.createQuery(hql)
                    .setParameter("unitId", unitCode)
                    .getResultList();
            Long result = unitPersonList.stream().findFirst().orElse(new UnitPerson()).getOperNum();
            if (result == null || result == 0) return 1L;
            return result;
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 根据装置ID集合获装置人员列表
     *
     * @param unitCodes 装置编码集合
     * @return 装置人员列表
     * @throws Exception
     * <AUTHOR> 2017-12-11
     */
    @Override
    public List<UnitPersonEntity> getUnitPersonListByUnitIds(List<String> unitCodes) throws Exception {
        try {
            StringBuilder hql = new StringBuilder();
            hql.append("from UnitPerson p ");
            Map<String, Object> paramList = new HashMap<>();
            if (unitCodes != null && unitCodes.size() != 0) {
                paramList.put("unitCodes", unitCodes);
                hql.append(" where p.unitId in (:unitCodes) ");
            }
            Query query = entityManager.createQuery(hql.toString());
            setParameterList(query, paramList);
            List<UnitPerson> unitPersonList = query.getResultList();
            return ObjectConverter.listConverter(unitPersonList, UnitPersonEntity.class);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 根据工厂ID集合获取工厂列表
     *
     * @param factoryIds
     * @return
     * <AUTHOR> 2017-12-11
     */
    @Override
    public List<FactoryEntity> getFactoryListById(Long[] factoryIds) throws Exception {
        List<FactoryEntity> factoryEntityList = new ArrayList<>();
        StringBuilder factoryHql = new StringBuilder();
        factoryHql.append("from Factory f  ");
        Map<String, Object> paramList = new HashMap<>();
        if (factoryIds != null) {
            factoryHql.append(" where f.factoryId in:factoryIds ");
            paramList.put("factoryIds", Arrays.asList(factoryIds));
        }
        factoryHql.append(" order by f.sortNum,f.sname asc ");
        try {
            Query query = entityManager.createQuery(factoryHql.toString(), Factory.class);
            setParameterList(query, paramList);
            List<Factory> factoryList = query.getResultList();
            factoryEntityList = ObjectConverter.listConverter(factoryList, FactoryEntity.class);
        } catch (Exception ex) {
            throw ex;
        }
        return factoryEntityList;
    }

    /**
     * 获取已经授权的装置的属性列表
     *
     * @param userId 用户ID
     * @return
     * <AUTHOR> 2017-12-20
     */
    @Override
    public List<AuthorizeEntity> getAuthorizePropertyList(String userId) {
        List<AuthorizeEntity> resultList = new ArrayList();
        try {
            List<AAAPropertyValueEntity> authPropertyValueList = aaaService.getAuthPropertyValueList(userId);
            if (authPropertyValueList != null) {
                for (AAAPropertyValueEntity authPropertyValue : authPropertyValueList) {
                    AuthorizeEntity entity = new AuthorizeEntity();
                    entity.setSname(authPropertyValue.getName());
                    entity.setStdCode(authPropertyValue.getValue());
                    resultList.add(entity);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return resultList;
    }

    /**
     * 获取报警变更方案状态枚举列表
     *
     * @param isAll 是否显示全部
     * @return 报警变更方案状态枚举列表
     * <AUTHOR> 2018-1-19
     */
    @Override
    public List<DictionaryEntity> getAlarmChangePlanStatusList(boolean isAll) throws Exception {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        if (isAll) {
            dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
        }
        try {
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangePlanStatusEnum.UnSubmit.getIndex(), CommonEnum.AlarmChangePlanStatusEnum.UnSubmit.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangePlanStatusEnum.Reject.getIndex(), CommonEnum.AlarmChangePlanStatusEnum.Reject.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangePlanStatusEnum.Submitted.getIndex(), CommonEnum.AlarmChangePlanStatusEnum.Submitted.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangePlanStatusEnum.Audited.getIndex(), CommonEnum.AlarmChangePlanStatusEnum.Audited.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangePlanStatusEnum.Issued.getIndex(), CommonEnum.AlarmChangePlanStatusEnum.Issued.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangePlanStatusEnum.Finished.getIndex(), CommonEnum.AlarmChangePlanStatusEnum.Finished.getName()));
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取是否屏蔽枚举集合
     *
     * @param isAll 是否显示全部
     * @return 是否屏蔽枚举集合
     * <AUTHOR> 2018-1-19
     */
    @Override
    public List<DictionaryEntity> getInSuppressedList(boolean isAll) throws Exception {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        if (isAll) {
            dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
        }
        try {
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.InSuppressedEnum.Yes.getIndex(), CommonEnum.InSuppressedEnum.Yes.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.InSuppressedEnum.No.getIndex(), CommonEnum.InSuppressedEnum.No.getName()));
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 根据报警标识和报警点集合获取变更未完成的集合
     *
     * @param alarmFlagList  报警标识列表
     * @param alarmPointList 报警点列表
     * @return 更未完成的集合
     * <AUTHOR> 2018-01-23
     */
    @Override
    public List<DictionaryEntity> getUnfinishedPlanList(List<Long> alarmFlagList, List<Long> alarmPointList) throws Exception {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        if (alarmFlagList.size() == 0 || alarmPointList.size() == 0) {
            return dictionaryEntityArrayList;
        }
        try {
            String hql = "select new com.pcitc.opal.common.bll.entity.DictionaryEntity(t.alarmPointId,t.alarmFlagId)" +
                    " from  AlarmChangePlanDetail t" +
                    " join t.alarmChangePlan  p" +
                    " where p.status!=" + CommonEnum.AlarmChangePlanStatusEnum.Finished.getIndex() +
                    " and t.alarmFlagId in (:alarmFlagList) " +
                    " and t.alarmPointId in (:alarmPointList) group by t.alarmPointId,t.alarmFlagId";
            Query query = entityManager.createQuery(hql);

            Map<String, Object> paramList = new HashMap<>();
            paramList.put("alarmFlagList", alarmFlagList);
            paramList.put("alarmPointList", alarmPointList);

            setParameterList(query, paramList);
            dictionaryEntityArrayList = (List<DictionaryEntity>) query.getResultList();
            //去除空数据
            dictionaryEntityArrayList = dictionaryEntityArrayList.stream().filter(item -> item.getKey() != null && item.getValue() != null).collect(Collectors.toList());
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }
    /**
     * 获取报警变更方案申请工艺类型状态枚举列表
     *
     * @param isAll 是否显示全部
     * @return 报警变更方案申请状态枚举列表
     * <AUTHOR> 2019-04-11
     */
    @Override
    public List<DictionaryEntity> getTecAlarmChangeBizApplyEnumList(boolean isAll) throws Exception {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        if (isAll) {
            dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
        }
        try {
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangeBizApplyEnum.UnSubmit.getIndex(), CommonEnum.AlarmChangeBizApplyEnum.UnSubmit.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangeBizApplyEnum.Reject.getIndex(), CommonEnum.AlarmChangeBizApplyEnum.Reject.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangeBizApplyEnum.Submitted.getIndex(), CommonEnum.AlarmChangeBizApplyEnum.Submitted.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangeBizApplyEnum.TecFinished.getIndex(), CommonEnum.AlarmChangeBizApplyEnum.TecFinished.getName()));
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取报警变更方案申请状态枚举列表
     *
     * @param isAll 是否显示全部
     * @return 报警变更方案申请状态枚举列表
     * <AUTHOR> 2018-1-29
     */
    @Override
    public List<DictionaryEntity> getAlarmChangeBizApplyEnumList(boolean isAll) throws Exception {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        if (isAll) {
            dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
        }
        try {
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangeBizApplyEnum.UnSubmit.getIndex(), CommonEnum.AlarmChangeBizApplyEnum.UnSubmit.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangeBizApplyEnum.Reject.getIndex(), CommonEnum.AlarmChangeBizApplyEnum.Reject.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangeBizApplyEnum.Submitted.getIndex(), CommonEnum.AlarmChangeBizApplyEnum.Submitted.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangeBizApplyEnum.Finished.getIndex(), CommonEnum.AlarmChangeBizApplyEnum.Finished.getName()));
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取报警变更方案下发状态枚举列表
     *
     * @param isAll 是否显示全部
     * @return 报警变更方案下发状态枚举列表
     * <AUTHOR> 2018-1-29
     */
    @Override
    public List<DictionaryEntity> getAlarmChangeBizIssueList(boolean isAll) throws Exception {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        if (isAll) {
            dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
        }
        try {
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangeBizIssueEnum.Issue.getIndex(), CommonEnum.AlarmChangeBizIssueEnum.Issue.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangeBizIssueEnum.Issued.getIndex(), CommonEnum.AlarmChangeBizIssueEnum.Issued.getName()));
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取报警变更方案确认状态枚举列表
     *
     * @param isAll 是否显示全部
     * @return 报警变更方案确认状态枚举列表
     * <AUTHOR> 2018-1-29
     */
    @Override
    public List<DictionaryEntity> getAlarmChangeBizConfirmList(boolean isAll) throws Exception {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        if (isAll) {
            dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
        }
        try {
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangeBizConfirmEnum.Confirm.getIndex(), CommonEnum.AlarmChangeBizConfirmEnum.Confirm.getName()));
            dictionaryEntityArrayList.add(new DictionaryEntity(CommonEnum.AlarmChangeBizConfirmEnum.Finished.getIndex(), CommonEnum.AlarmChangeBizConfirmEnum.Finished.getName()));
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取报警制度管理分类枚举
     *
     * @param isAll 是否显示全部
     * @return 报警状态列表
     * <AUTHOR> 2018-02-28
     */
    @Override
    public List<DictionaryEntity> getAlarmStdManagmtCatgrList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            if (isAll) {
                dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
            }
            for (CommonEnum.AlarmStdManagmtCatgrEnum catr : CommonEnum.AlarmStdManagmtCatgrEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(catr.getIndex(), catr.getName()));
            }

        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 根据Code获取系统运行参数
     *
     * @param code 参数编码
     * @return 参数信息
     * @throws Exception
     * <AUTHOR> 2018-3-13
     */
    @Override
    public SystRunParaConfEntity getSystRunParamByCode(String code) throws Exception {
        StringBuilder hql = new StringBuilder();
        CommonProperty commonProperty = new CommonProperty();
        hql.append("from SystRunParaConf s where s.code=:code and s.companyId=:companyId ");
        Map<String, Object> paramList = new HashMap<>();
        paramList.put("code", code);
        paramList.put("companyId",commonProperty.getCompanyId());
        try {
            TypedQuery<SystRunParaConf> query = entityManager.createQuery(hql.toString(), SystRunParaConf.class);
            setParameterList(query, paramList);
            SystRunParaConf paramConf = query.getSingleResult();
            return ObjectConverter.entityConverter(paramConf, SystRunParaConfEntity.class);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取变更方案审核状态枚举
     *
     * @param isAll 是否显示全部
     * @return 更方案审核状态枚举列表
     * <AUTHOR> 2018-03-14
     */
    @Override
    public List<DictionaryEntity> getAlarmChangePlanApproStatusList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            if (isAll) {
                dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
            }
            for (CommonEnum.AlarmChangePlanApproStatusEnum catr : CommonEnum.AlarmChangePlanApproStatusEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(catr.getIndex(), catr.getName()));
            }

        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取流程预览URL
     *
     * @return 预览地址
     * @throws Exception
     * <AUTHOR> 2018-03-14
     */
    @Override
    public String getWorkFlowPreviewURL() throws Exception {
        //1.获取token
        String token = flowService.getWorkFlowToken();
        //2.获取categoryCode
        SystRunParaConfEntity entity = getSystRunParamByCode("AlarmChangePlanApro");
        if (entity == null) {
            return "";
        }
        //3.获取应用Id
        String appId = flowConfig.getAppId();
        //4.获取预览URL
        String previewUrl = flowConfig.getSsoUrl();
        if (StringUtils.isBlank(token) || StringUtils.isBlank(appId) || StringUtils.isBlank(previewUrl)) {
            return "";
        }
        previewUrl = String.format("%s?targeturl=/graphic/viewone&appId=%s&token=%s", previewUrl, appId, token);
        return previewUrl;
    }

    /**
     * 获取已启用 DcsCode 列表
     *
     * @param isAll 是否显示全部
     * @return
     * <AUTHOR> 2018-03-30
     */
    @Override
    public List<DcsCodeEntity> getDcsCodeList(boolean isAll) {
        List<DcsCodeEntity> dcsCodeEntityList = new ArrayList<>();
        try {
            String hql = " from DcsCode d where d.inUse=:inUse order by d.sortNum,d.name asc";
            List<DcsCode> dcsCodeList = entityManager.createQuery(hql, DcsCode.class)
                    .setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex()).getResultList();
            dcsCodeEntityList = ObjectConverter.listConverter(dcsCodeList, DcsCodeEntity.class);
            if (isAll && dcsCodeEntityList.size() > 1) {
                DcsCodeEntity entity = new DcsCodeEntity();
                entity.setDcsCodeId(-1L);
                entity.setName("全部");
                dcsCodeEntityList.add(0, entity);
            }
        } catch (Exception ex) {

        }
        return dcsCodeEntityList;
    }
    /**
     * 获取已启用 OpcCode 列表
     *
     * @param isAll 是否显示全部
     * @return
     * <AUTHOR> 2018-08-23
     */
    @Override
    public List<OpcCodeEntity> getOpcCodeList(boolean isAll){

        List<OpcCodeEntity> opcCodeEntityList = new ArrayList<>();

        try {
            String hql = " from OpcCode d where d.inUse=:inUse and d.companyId=:companyId order by d.sortNum,d.name asc";
            CommonProperty commonProperty = new CommonProperty();
            List<OpcCode> opcCodeList = entityManager.createQuery(hql, OpcCode.class)
                    .setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex())
                    .setParameter("companyId",commonProperty.getCompanyId())
                    .getResultList();

            opcCodeEntityList = ObjectConverter.listConverter(opcCodeList, OpcCodeEntity.class);
            if (isAll && opcCodeEntityList.size() > 1) {
                OpcCodeEntity entity = new OpcCodeEntity();
                entity.setOpcCodeId(-1L);
                entity.setName("全部");
                opcCodeEntityList.add(0, entity);
            }
        } catch (Exception ex) {

        }
        return opcCodeEntityList;
    }


    /**
     * 获取已启用事件类型列表
     *
     * @param isAll 是否显示全部
     * @return
     * <AUTHOR> 2018-03-30
     */
    @Override
    public List<EventTypeEntity> getEventTypeList(boolean isAll) throws Exception {
        List<EventTypeEntity> eventTypeEntityList= new ArrayList<>();
        try {
            String eventTypeHql = "from EventType e where e.inUse=:inUse order by e.sortNum,e.name asc ";
            List<EventType> eventTypeList =entityManager.createQuery(eventTypeHql, EventType.class).setParameter("inUse", CommonEnum.InUseEnum.Yes.getIndex()).getResultList();
            eventTypeEntityList = ObjectConverter.listConverter(eventTypeList, EventTypeEntity.class);
        } catch (Exception ex) {
            throw ex;
        }
        return eventTypeEntityList;
    }
    /**
     * 获取未匹配计量单位枚举
     *
     * @param isAll 是否显示全部
     * @return 未匹配计量单位枚举
     * <AUTHOR> 2018-04-19
     */
    @Override
    public List<DictionaryEntity> getMeasUnitUnmatchData(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            if (isAll) {
                dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
            }
            for (CommonEnum.MeasUnitUnmatchEnum catr : CommonEnum.MeasUnitUnmatchEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(catr.getIndex(), catr.getName()));
            }

        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }
    /**
     * 获取未匹配报警点枚举
     *
     * @param isAll 是否显示全部
     * @return 未匹配报警点枚举
     * <AUTHOR> 2018-04-25
     */
    @Override
    public List<DictionaryEntity> getAlarmPointUnmatchData(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            if (isAll) {
                dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
            }
            for (CommonEnum.AlarmPointUnmatchEnum catr : CommonEnum.AlarmPointUnmatchEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(catr.getIndex(), catr.getName()));
            }

        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取系统参数配置参数值
     *
     * @param code 参数编码
     * @return 参数值
     * <AUTHOR> 2018-07-25
     */
    @Override
    public synchronized String getSystRunParaConfParaValue(String code) {
        String paraValue = "";
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from SystRunParaConf t where 1=1 and t.companyId=:companyId");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (!org.springframework.util.StringUtils.isEmpty(code)) {
                hql.append(" and t.code = :code ");
                paramList.put("code", code);
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());

            // 调用基类方法查询返回结果
            TypedQuery<SystRunParaConf> query = entityManager.createQuery(hql.toString(), SystRunParaConf.class);
            this.setParameterList(query, paramList);
            SystRunParaConf systRunParaConf = query.getResultList().stream().findFirst().orElse(null);
            if (systRunParaConf != null) {
                paraValue = systRunParaConf.getParaValue();
            }

        } catch (Exception ex) {
            throw ex;
        }
        return paraValue;
    }

    @Override
    public String getCurrentUser() {
        return new CommonProperty().getUserId();
    }

    /**
     * 设置查询参数
     *
     * @param query
     * @param paramList 查询参数列表
     * <AUTHOR> 2017-11-12
     */
    protected void setParameterList(Query query, Map<String, Object> paramList) {
        for (Map.Entry<String, Object> pair : paramList.entrySet()) {
            query.setParameter(pair.getKey(), pair.getValue());
        }
    }

    public Integer getCompanyIdByUnit(String[] unit) {
        String sql = "select company_id from t_pm_unit where std_code in (:unit) and company_id is not null limit 1";

        Query nativeQuery = entityManager.createNativeQuery(sql);
        nativeQuery.setParameter("unit", Arrays.asList(unit));
        List resultList = nativeQuery.getResultList();

        if (CollectionUtils.isNotEmpty(resultList)){
            return Integer.valueOf(resultList.get(0).toString());
        }else {
            return null;
        }
    }

    @Override
    public List<AlarmPointDelConfigDetailEntity> getAlarmPointDelConfigDetail(String[] unitCodes, Date startTime, Date endTime) {
        ArrayList<AlarmPointDelConfigDetailEntity> list = new ArrayList<>();
        for (AlarmPointDelConfig alarmPointDelConfig : alarmPointDelConfigRepository.selectAlarmPointDelConfigDetail(unitCodes, startTime, endTime)) {
            list.add(new AlarmPointDelConfigDetailEntity(alarmPointDelConfig));
        }
        return list;
    }


}
