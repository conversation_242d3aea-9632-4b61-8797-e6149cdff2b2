var addUrl = OPAL.API.pmUrl + '/unit';
var getSingleUrl = OPAL.API.pmUrl + '/unit';
var factoryUrl = OPAL.API.pmUrl + '/unit' + "/getFactoryList";
var workshopUrl = OPAL.API.pmUrl + '/unit' +"/getWorkshopListByFactoryId";
var inUseUrl = OPAL.API.commUrl + "/getInUse";
var shiftAreaUrl = OPAL.API.commUrl + "/getAllShiftAreaList";
var pageMode = PageModelEnum.NewAdd;
var selectValue='';
var selectFirstValue=true;
window.pageLoadMode = PageLoadMode.Refresh;
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
            page.logic.initFactory();
            page.logic.initInUse();
            page.logic.initShiftAreaList();
        },
        bindUI: function () {
            $('#saveAddModal').click(function () {
                page.logic.save();
            });
            $('.closeBtn').click(function () {
                page.logic.closeLayer(false);
            })
            $('#closePage').click(function () {
                page.logic.closeLayer(false);
            })
        },
        logic: {
            /**
             * 保存
             */
            save: function () {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data=OPAL.form.getETCollectionData("AddOrEditModal");
                //处理提交类型
                var ajaxType = "POST";
                if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                }

                $.ajax({
                    url: addUrl,
                    async: false,
                    type: ajaxType,
                    data: encodeURI(JSON.stringify(data)),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！",{
                                time: 1000
                            },function() {
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                $("#pageTitle").text(data.title);
                if (pageMode == PageModelEnum.NewAdd) {
                    $('#inUse').attr('disabled', 'disabled');
                    return;
                }
                $.ajax({
                    url: getSingleUrl + "/" + data.unitId + "?now=" + Math.random(),
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function (data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        //加载车间有延迟,使用配置方式
                        selectFirstValue=false;
                        selectValue=entity['workshopId'];
                        OPAL.form.setData('AddOrEditModal', entity);
                    },
                    complete: function (XMLHttpRequest, textStatus) {

                    },
                    error: function (XMLHttpRequest, textStatus) {

                    }
                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                parent.isRefresh = isRefresh;
                parent.layer.close(index);
            },
            /**
             * 表单校验
             */
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal',{
                    rules: {
                        factoryId: {
                            required: true
                        },
                        workshopId:{
                            required: true,
                        },
                        name: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        sname: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        stdCode: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        shiftAreaId:{
                            required:true,
                        },
                        operatorNum: {
                            required: true,
                            digits: true,
                            min: 1
                        },
                        sortNum: {
                            required: true,
                            digits: true,
                            min: 0
                        },
                        des:{
                            required: false,
                            rangelength: [0, 1000]
                        }
                    }
                });
            },
            /**
             * 初始化查询inUse
             */
            initInUse: function () {
                OPAL.ui.getCombobox("inUse", inUseUrl, {
                    data: {
                        isAll: true
                    }
                }, null);
            },
            /**
             * 初始化工厂
             */
            initFactory: function () {
                OPAL.ui.getCombobox("factoryId", factoryUrl, {
                    async: false,
                    keyField: "factoryId",
                    valueField: "sname",
                    selectFirstRecord: true,
                    data: {
                        isAll: false
                    }
                }, function () {
                    if ($("#factoryId").val()!=null&&$("#factoryId").val()!='')
                        page.logic.initWorkshop($("#factoryId").val());
                }, function (selectedValue) {
                    if (selectedValue != '' &&selectedValue!=null)
                        page.logic.initWorkshop(selectedValue);
                });
            },
            /**
             * factoryId
             */
            initWorkshop: function (factoryId) {
                OPAL.ui.getCombobox("workshopId", workshopUrl, {
                    keyField: "workshopId",
                    async: false,
                    valueField: "sname",
                    selectFirstRecord: selectFirstValue,
                    selectValue:selectValue,
                    data: {
                        factoryId: factoryId,
                        isAll: false
                    }
                }, function(){
                    if(selectFirstValue==false)
                        selectFirstValue=true;
                })
            },
            /***
             * 初始化轮班域列表
             */
            initShiftAreaList:function(){
                OPAL.ui.getCombobox("shiftAreaId", shiftAreaUrl, {
                    keyField: "shiftAreaId",
                    async: false,
                    valueField: "shiftAreaName",
                    selectFirstRecord: true,
                    data: {
                    }
                },null);
            },
        }

    }
    page.init();
    window.page = page;
})