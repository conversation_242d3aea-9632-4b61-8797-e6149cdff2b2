package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmReasonRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmReason;
import org.apache.commons.lang.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.HashMap;
import java.util.List;

/**
 * @USER: chenbo
 * @DATE: 2023/6/9
 * @TIME: 14:59
 * @DESC:
 **/
public class AlarmReasonRepositoryImpl extends BaseRepository<AlarmReason, Long> implements AlarmReasonRepositoryCustom {
    @Override
    public PaginationBean<AlarmReason> getAlarmReason(Long reasonType, String name, Long inUse, Pagination page) {

        String hql = "from AlarmReason where 1=1 ";

        HashMap<String, Object> param = new HashMap<>();

        if (reasonType != null && reasonType != -1) {
            hql += " and reasonType = :reasonType ";
            param.put("reasonType", reasonType);
        }

        if (StringUtils.isNotEmpty(name)) {
            hql += " and name like :name ";
            param.put("name", "%" + name + "%");
        }

        if (inUse != null && inUse != -1) {
            hql += " and inUse = :inUse ";
            param.put("inUse", inUse);
        }

        //根据“原因分类,排序,名称”正序排列
        hql += " order by reasonType asc ,sortNum asc,name asc ";


        return findAll(page, hql, param);
    }

    @Transactional
    @Override
    public CommonResult updateAlarmReason(AlarmReason alarmReason) {

        CommonResult commonResult = new CommonResult();

        try {
            getEntityManager().merge(alarmReason);
            commonResult.setMessage("更新成功");
        } catch (Exception e) {
            commonResult.setMessage("更新失败" + e.getMessage());
            commonResult.setIsSuccess(false);
        }
        return commonResult;
    }

    @Override
    public boolean existsAlarmReason(Long reasonType, String name) {
        String hql = "select count(1) from AlarmReason where reasonType = :reasonType and name = :name";

        TypedQuery<Long> query = getEntityManager().createQuery(hql, Long.class);

        query.setParameter("name", name)
                .setParameter("reasonType", reasonType);

        Long singleResult = query.getSingleResult();
        return singleResult > 0;
    }

    @Override
    public List<AlarmReason> getReasonByType(Long reasonType) {
        String hql ="from AlarmReason ar where ar.reasonType = :reasonType and ar.inUse=1";
        Query query =getEntityManager().createQuery(hql, AlarmReason.class);
        query.setParameter("reasonType", reasonType);
        return query.getResultList();
    }
}
