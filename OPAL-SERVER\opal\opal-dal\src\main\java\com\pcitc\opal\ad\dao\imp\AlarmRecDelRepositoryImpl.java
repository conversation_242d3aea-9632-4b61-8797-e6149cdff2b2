package com.pcitc.opal.ad.dao.imp;

import com.pcitc.opal.ad.dao.AlarmRecDelRepositoryCustom;
import com.pcitc.opal.ad.pojo.AlarmRecDel;
import com.pcitc.opal.common.dao.BaseRepository;
import org.apache.poi.ss.formula.functions.T;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * @USER: chenbo
 * @DATE: 2023/2/1
 * @TIME: 10:19
 * @DESC:
 **/
public class AlarmRecDelRepositoryImpl extends BaseRepository<AlarmRecDel, Long> implements AlarmRecDelRepositoryCustom {

    @Transactional
    @Override
    public void saveBatch(List<AlarmRecDel> alarmRecDels) {

        int batchSize = 1000;

        for (int i = 0; i < alarmRecDels.size(); i++) {
            alarmRecDels.get(i).setAlarmRecId(null);
            getEntityManager().persist(alarmRecDels.get(i));
            if (i % batchSize == 0) {
                getEntityManager().flush();
                getEntityManager().clear();
            }
        }

        getEntityManager().flush();
        getEntityManager().clear();
    }
}
