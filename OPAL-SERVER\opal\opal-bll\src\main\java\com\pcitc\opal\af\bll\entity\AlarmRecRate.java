package com.pcitc.opal.af.bll.entity;

import com.pcitc.opal.common.CommonEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import pcitc.imp.common.ettool.baseresrep.BaseResRep;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @USER: chenbo
 * @DATE: 2023/3/1
 * @TIME: 10:04
 * @DESC: 工艺参数报警率页面--->报警处置及时率和响应及时率实体
 **/
@Data
@Accessors(chain = true)
public class AlarmRecRate extends BaseResRep implements Serializable {

    /**
     * 生产单元名称
     */
    private String prdtcellName;

    /**
     * 位号
     */
    private String tag;

    /**
     * 描述
     */
    private String des;

    /**
     * 标记标识名称
     */
    private String flagName;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 优先级名称
     */
    private String priorityName;

    /**
     * 计量单位
     */
    private String measUnitName;

    /**
     * 报警时间
     */
    private Date alarmTime;

    /**
     * 响应时间
     */
    private Date responseTime;

    /**
     * 数量
     */
    private Integer number;

    /**
     * 报警时间与响应时间差值
     */
    private Integer timeDifferenceValue;

    /**
     * 详细信息
     */
    List<ContinuedAlarmDetail> details;

    public AlarmRecRate setPriority(Integer priority) {
        this.priority = priority;
        if (priority != null) {
            this.priorityName = CommonEnum.AlarmPriorityEnum.getName(priority);
        }
        return this;
    }


    public String getSplicing(){
        String s = prdtcellName + "/,/" + tag + "/,/" + des + "/,/" + priority + "/,/" + flagName + "/,/" + measUnitName;
        //拼接是会把为空的字符串拼接为null，所以最后替换掉null字符串
        return s.replace("null", "");
    }
    public void setSplicing(String[] str){
        prdtcellName = str[0];
        tag = str[1];
        des = str[2];
        setPriority( StringUtils.isEmpty(str[3]) ? null : Integer.valueOf(str[3]));
        flagName = str[4];
        measUnitName = str[5];
    }
}
