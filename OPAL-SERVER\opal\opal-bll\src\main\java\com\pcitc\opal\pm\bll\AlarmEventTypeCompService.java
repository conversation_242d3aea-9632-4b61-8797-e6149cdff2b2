package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.AlarmEventTypeCompEntity;
import org.springframework.stereotype.Service;
/*
 * 事件类型对照配置业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmEventTypeCompService
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：事件类型对照配置业务逻辑层接口
 */
@Service
public interface AlarmEventTypeCompService {

	/**
	 * 新增数据
	 * 
	 * <AUTHOR>   2018-03-30
	 * @param alarmEventTypeCompEntity  事件类型对照配置实体
	 */
	void addAlarmEventTypeComp(AlarmEventTypeCompEntity alarmEventTypeCompEntity) throws Exception;

	/**
	 * 删除数据
	 * 
	 * <AUTHOR>  2018-03-30
	 * @param alarmEventTypeCompIds   事件类型对照配置ID集合
	 */
	void deleteAlarmEventTypeComp(Long[] alarmEventTypeCompIds) throws Exception;

	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmEventTypeCompEntity
	 */
	void updateAlarmEventTypeComp(AlarmEventTypeCompEntity alarmEventTypeCompEntity) throws Exception;

	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmEventTypeCompId 事件类型对照配置ID
	 * @return 事件类型对照配置实体
	 */
	AlarmEventTypeCompEntity getSingleAlarmEventTypeComp(Long alarmEventTypeCompId);

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param dcsCodeId       DcsCodeID
	 * @param eventTypeSource 源事件类型
	 * @param eventNameSource 源事件名称
	 * @param eventTypeIds    事件类型ID集合
	 * @param inUse           是否启用
     * @param page            分页信息
     * @return alarmEventTypeComp实体集合
	 * @throws Exception
	 */
	PaginationBean<AlarmEventTypeCompEntity> getAlarmEventTypeComp(Long dcsCodeId, String eventTypeSource, String eventNameSource, Long[] eventTypeIds, Integer inUse, Pagination page)throws Exception;
}
