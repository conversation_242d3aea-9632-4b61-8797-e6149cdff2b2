package com.pcitc.opal.pm.bll.imp;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pcitc.imp.common.exception.BusiException;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.AlarmFlagRepository;
import com.pcitc.opal.ad.dao.AlarmRecRepository;
import com.pcitc.opal.ad.dao.TagExtraMessageRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.ad.pojo.AlarmFlag;
import com.pcitc.opal.ad.vo.AlarmEventTableVO;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.CommonEnum.PageModelEnum;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.AlarmPointService;
import com.pcitc.opal.pm.bll.entity.*;
import com.pcitc.opal.pm.dao.*;
import com.pcitc.opal.pm.pojo.*;
import com.pcitc.opal.pm.vo.AlarmPointTableParamVO;
import com.pcitc.opal.pm.vo.AlarmPointTableVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/*
 * 报警点业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmPointImpl
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点业务逻辑层实现类
 */
@Service
@Component
@Slf4j
public class AlarmPointImpl implements AlarmPointService {
    /**
     * 实例化数据访问层接口
     */
    @Autowired
    private AlarmPointRepository repo;

    @Autowired
    private AlarmEventRepository alarmEventRepository;
    /**
     * 装置数据访问层接口
     */
    @Autowired
    private UnitRepository unitRepository;
    /**
     * 生产单元数据访问层接口
     */
    @Autowired
    private PrdtCellRepository prdtCellRepository;

    @Autowired
    private BasicDataService basicDataService;

    @Autowired
    private TagExtraMessageRepository temRepository;

    @Autowired
    private AlarmFlagRepository afRepository;
    @Autowired
    private AlarmMobileConfRepository alarmMobileConfRepository;
    @Autowired
    private SendMsgAlarmFlagConfRepository sendMsgAlarmFlagConfRepository;
    @Autowired
    private MobileListRepository mobileListRepository;
    @Autowired
    private AlarmRecRepository alarmRecRepository;

    @Resource
    private UnMatchAlarmPointRepository unMatchAlarmPointRepository;

    @Autowired
    private AlarmPointDAO alarmPointDAO;

    //获取装置数据
    List<UnitEntity> unitList = null;

    //获取生产单元数据
    List<PrdtCellEntity> prdtCellEntityList = null;

    /**
     * 新增报警点
     * <p>
     *  * <AUTHOR> 2017-10-11
     *
     * @param alarmPointEntity 报警点实体
     * @return 报警点实体
     * @throws Exception 
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addAlarmPoint(AlarmPointEntity alarmPointEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmPoint alarmPointPO = ObjectConverter.entityConverter(alarmPointEntity, AlarmPoint.class);
        // 数据校验
        alarmPointValidation(alarmPointPO);
        // 赋值 创建人、创建名称、创建时间
        CommonUtil.returnValue(alarmPointPO, PageModelEnum.NewAdd.getIndex());
        CommonResult commonResult = repo.addAlarmPoint(alarmPointPO);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false) {
            throw new Exception(commonResult.getMessage());
        } else {
            List<AlarmFlag> afList = afRepository.getAllAlarmFlag();
            AlarmPoint ap = (AlarmPoint) commonResult.getResult();
            Long apId = ap.getAlarmPointId();
            for (AlarmFlag af : afList) {
                TagExtraMessage temEntity = new TagExtraMessage();
                temEntity.setAlarmPointId(apId);
                temEntity.setAlarmFlagId(af.getAlarmFlagId());
                temEntity.setPrescribedResponseDuration(30L);
                // 赋值  创建人、创建名称、创建时间
                CommonUtil.returnValue(temEntity, PageModelEnum.NewAdd.getIndex());
                temRepository.addTagExtraMessage(temEntity);
            }
            ;
        }

        //新增报警点时删除未匹配报警点表中相应的数据
        Integer integer = unMatchAlarmPointRepository.deleteUmMatchByTagPrd(alarmPointPO.getTag(), alarmPointPO.getPrdtCellId());
        log.info("新增报警点时删除unMatchAlarmPoint-{}", integer);

        return commonResult;
    }

    /**
     * 删除报警点维护数据
     * <p>
     *  * <AUTHOR> 2017-10-11
     *
     * @param alarmPointIds 报警点维护主键Id集合
     * @return 报警点主键id集合
     * @throws Exception 
     */
    @Override
    @CacheEvict(value = "AlarmAnalysis", allEntries = true) // 移除AlarmAnalysis下的所有缓存数据
    public CommonResult deleteAlarmPoint(Long[] alarmPointIds) throws Exception {
        // 判断ID集合是否可用
        if (alarmPointIds == null || alarmPointIds.length <= 0) {
            throw new Exception("没有需要删除的报警点数据！");
        }
        List<Long> pointIds = alarmMobileConfRepository.getConfListByPointId(alarmPointIds);
        if (pointIds.size() > 0) {
            throw new Exception("存在关联字段无法删除！");
        }
        List<AlarmPoint> anlyAlarmPointList = repo.getAlarmPoint(alarmPointIds);
        if (anlyAlarmPointList == null || anlyAlarmPointList.isEmpty())
            return new CommonResult();
        Long[] anlyAlarmPointIdList = anlyAlarmPointList.stream().map(item -> item.getAlarmPointId())
                .toArray(Long[]::new);
        // 查询报警事件表中是否有响应的报警点
        List<AlarmEvent> aeList = alarmEventRepository.getListByAlarmPointIds(alarmPointIds);
        if (aeList.isEmpty()) {
            temRepository.deleteTagExtraMessage(anlyAlarmPointIdList);
        }
        alarmRecRepository.deleteAlarmRecByAlarmPointIds(anlyAlarmPointIdList);
        // 调用DAL删除方法
        CommonResult commonResult = repo.deleteAlarmPoint(anlyAlarmPointIdList);

        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 修改报警点
     * <p>
     *  * <AUTHOR> 2017-10-11
     *
     * @param alarmPointEntity 报警点实体类
     * @return 报警点实体类
     * @throws Exception 
     */
    @Override
    @CacheEvict(value = "AlarmAnalysis", allEntries = true) // 移除AlarmAnalysis下的所有缓存数据
    // @CacheUtil.CacheRemove(value = "AlarmAnalysis" , key= "'AlarmDetail_'+#alarmPointEntity.alarmPointId+'_*'" )
    public CommonResult updateAlarmPoint(AlarmPointEntity alarmPointEntity) throws Exception {
        // 实体转换持久层实体
        AlarmPoint alarmPointPO = ObjectConverter.entityConverter(alarmPointEntity, AlarmPoint.class);
        // 校验
        alarmPointValidation(alarmPointPO);
        // 实体转换为持久层实体
        alarmPointPO = repo.getSingleAlarmPoint(alarmPointEntity.getAlarmPointId());
        CommonUtil.objectExchange(alarmPointEntity, alarmPointPO);
        // 赋值 修改人、修改名称、修改时间
        CommonUtil.returnValue(alarmPointPO, PageModelEnum.Edit.getIndex());
        //企业
        CommonProperty commonProperty = new CommonProperty();
        alarmPointPO.setCompanyId(Integer.valueOf(commonProperty.getCompanyId()));
        // 调用DAL更新方法
        CommonResult commonResult = repo.updateAlarmPoint(alarmPointPO);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());


        Integer integer = unMatchAlarmPointRepository.deleteUmMatchByTagPrd(alarmPointPO.getTag(), alarmPointPO.getPrdtCellId());
        log.info("更新报警点时删除unMatchAlarmPoint-{}", integer);

        return commonResult;
    }

    /**
     * 通过报警点ID获取单条数据
     * <p>
     *  * <AUTHOR> 2017-10-11
     *
     * @param alarmPointId 报警点主键id
     * @throws Exception
     * @return 报警点实体类
     */
    @Override
    public AlarmPointEntity getSingleAlarmPoint(Long alarmPointId) throws Exception {
        AlarmPoint alarmPoint = repo.getSingleAlarmPoint(alarmPointId);
        AlarmPointEntity ape = ObjectConverter.entityConverter(alarmPoint, AlarmPointEntity.class);
        ape.setUnitId(alarmPoint.getPrdtCell().getUnitId());
        List<UnitEntity> unitList = basicDataService.getUnitListByIds(new String[]{alarmPoint.getPrdtCell().getUnitId()}, false);
        UnitEntity ue = unitList.stream().filter(ul -> alarmPoint.getPrdtCell().getUnitId().equals(ul.getStdCode())).findFirst().orElse(new UnitEntity());
        ape.setUnitSname(ue.getSname());
        ape.setUnitSname(ue.getSname());
        ape.setPrdtCellSname(alarmPoint.getPrdtCell().getSname());
        ape.setAlarmPointTypeName(alarmPoint.getAlarmPointType().getName());
        ape.setMeasunitName(alarmPoint.getMeasUnit().getName());
        return ape;
    }

    /**
     * 获取分页数据
     * <p>
     *  * <AUTHOR> 2017-10-11
     *
     * @param isExport        是否是导出(0否，1是)
     * @param unitCodes       装置id
     * @param prdtCellIds     生产单元id
     * @param tag             位号
     * @param typeId          报警点类型id
     * @param inUse           是否启用
     * @param instrmtPriority 仪表优先级
     * @param page            翻页实现类
     * @return 翻页对象
     * @throws Exception 
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<AlarmPointTableVO> getAlarmPoint(int isExport, String[] unitCodes, Long[] prdtCellIds, String tag, Long typeId,
                                                          Integer inUse, Integer instrmtPriority, Pagination page) throws Exception {
        if (unitCodes == null || unitCodes.length == 0 || Arrays.asList(unitCodes).contains("-1")) {
            List<UnitEntity> unitList = basicDataService.getUnitList(true);
            unitCodes = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
        }
        Page<AlarmPointTableVO> pager = new Page<AlarmPointTableVO>(page.getPageNumber(), page.getPageSize());
        CommonProperty commonProperty = new CommonProperty();
        AlarmPointTableParamVO paramVO = new AlarmPointTableParamVO();
        paramVO.setCompanyId(commonProperty.getCompanyId());

        if (unitCodes != null && unitCodes.length > 0) {
            paramVO.setUnitCodeList(Arrays.asList(unitCodes));
        }
        if (prdtCellIds!= null && prdtCellIds.length > 0) {
            paramVO.setPrdtCellIds(Arrays.asList(prdtCellIds));
        }
        paramVO.setAlarmPointTypeId(typeId);
        paramVO.setTag(tag);
        paramVO.setInUse(inUse);
        paramVO.setInstrmtPriority(instrmtPriority);
        Page<AlarmPointTableVO> tablePage = alarmPointDAO.selectTable(paramVO, pager);
        PaginationBean<AlarmPointTableVO> returnAlarmPoint = new PaginationBean<>(page, tablePage.getTotal());

        List<AlarmPointTableVO> pageList = tablePage.getRecords();
        for (AlarmPointTableVO tableVO : pageList) {
            Integer monitorType = tableVO.getMonitorType();
            if (monitorType != null) {
                tableVO.setMonitorTypeStr(CommonEnum.MonitorTypeEnum.getName(monitorType));
            }
            if (null != tableVO.getInstrmtPriority()) {
                tableVO.setInstrmtPriorityShow(CommonEnum.InstrmtPriorityEnum.getName(tableVO.getInstrmtPriority()));
            } else {
                tableVO.setInstrmtPriorityShow(CommonEnum.InstrmtPriorityEnum.NoLevel.getName());
            }
            tableVO.setMeasunitName(tableVO.getSignName());
            Double culv = tableVO.getCraftUpLimitValue();//工艺卡片上限值
            Double cdlv = tableVO.getCraftDownLimitValue();//工艺卡片下限值
            Integer culi = tableVO.getCraftUpLimitInclude();//工艺卡片上限值是否包含(1是；0否)
            Integer cdli = tableVO.getCraftDownLimitInclude();//工艺卡片下限值是否包含(1是；0否)
            Double iulv = tableVO.getInterlockUpLimitValue();//联锁上限值
            Double idlv = tableVO.getInterlockDownLimitValue();//联锁下限值
            Integer iuli = tableVO.getInterlockUpLimitInclude();//联锁上限值是否包含(1是；0否)
            Integer idli = tableVO.getInterlockDownLimitInclude();//联锁下限值是否包含(1是；0否)
            iuli = iuli == null ? 0 : iuli;
            idli = idli == null ? 0 : idli;
            if (culv != null && cdlv != null) {
                String culvStr = changeDouble(culv);
                String cdlvStr = changeDouble(cdlv);
                tableVO.setCraftLimitValue(cdlvStr + "～" + culvStr);
            } else if (culv != null && cdlv == null) {
                if (culi != null && culi.intValue() == 1) {
                    String culvStr = changeDouble(culv);
                    tableVO.setCraftLimitValue("≤" + culvStr);
                } else if (culi != null && culi.intValue() == 0) {
                    String culvStr = changeDouble(culv);
                    tableVO.setCraftLimitValue("<" + culvStr);
                }
            } else if (culv == null && cdlv != null) {
                if (cdli != null && cdli.intValue() == 1) {
                    String cdlvStr = changeDouble(cdlv);
                    tableVO.setCraftLimitValue("≥" + cdlvStr);
                } else if (cdli != null && cdli.intValue() == 0) {
                    String cdlvStr = changeDouble(cdlv);
                    tableVO.setCraftLimitValue(">" + cdlvStr);
                }
            } else if (culv == null && cdlv == null) {
                tableVO.setCraftLimitValue("");
            }
            //联锁值
            if (iulv != null && idlv != null) {
                String iulvStr = changeDouble(iulv);
                String idlvStr = changeDouble(idlv);

                if (iuli.intValue() == 1 && idli.intValue() == 1) {
                    tableVO.setInterlockLimitValue(">" + iulvStr + "，<" + idlvStr);
                } else if (iuli.intValue() == 1 && idli.intValue() == 0) {
                    tableVO.setInterlockLimitValue(">" + iulvStr + "，≤" + idlvStr);
                } else if (iuli.intValue() == 0 && idli.intValue() == 1) {
                    tableVO.setInterlockLimitValue("≥" + iulvStr + "，<" + idlvStr);
                } else if (iuli.intValue() == 0 && idli.intValue() == 0) {
                    tableVO.setInterlockLimitValue("≥" + iulvStr + "，≤" + idlvStr);
                }
            } else if (iulv != null && idlv == null) {
                if (iuli.intValue() == 1) {
                    String iulvStr = changeDouble(iulv);
                    tableVO.setInterlockLimitValue(">" + iulvStr);
                } else if (iuli.intValue() == 0) {
                    String iulvStr = changeDouble(iulv);
                    tableVO.setInterlockLimitValue("≥" + iulvStr);
                }
            } else if (iulv == null && idlv != null) {
                if (idli.intValue() == 1) {
                    String idlvStr = changeDouble(idlv);
                    tableVO.setInterlockLimitValue("<" + idlvStr);
                } else if (idli.intValue() == 0) {
                    String idlvStr = changeDouble(idlv);
                    tableVO.setInterlockLimitValue("≤" + idlvStr);
                }
            } else if (iulv == null && idlv == null) {
                tableVO.setInterlockLimitValue("");
            }
        }
        returnAlarmPoint.setPageList(pageList);
        return returnAlarmPoint;

//        PaginationBean<AlarmPoint> listAlarmPoint = repo.getAlarmPoint(unitCodes, prdtCellIds, tag, typeId, inUse, instrmtPriority, page);
//        PaginationBean<AlarmPointEntity> returnAlarmPoint = new PaginationBean<AlarmPointEntity>(page, listAlarmPoint.getTotal());
//        returnAlarmPoint.setPageList(ObjectConverter.listConverter(listAlarmPoint.getPageList(), AlarmPointEntity.class));
//        for (AlarmPointEntity alarmPointEntity : returnAlarmPoint.getPageList()) {
//            if (null != alarmPointEntity.getInstrmtPriority()) {
//                alarmPointEntity.setInstrmtPriorityShow(CommonEnum.InstrmtPriorityEnum.getName(alarmPointEntity.getInstrmtPriority()));
//            } else {
//                alarmPointEntity.setInstrmtPriorityShow(CommonEnum.InstrmtPriorityEnum.NoLevel.getName());
//            }
//        }
//        if (isExport == 0) {
//            //标红
//            returnAlarmPoint.getPageList().stream().forEach(x -> {
//                if ((x.getCraftDownLimitValue() != null && x.getAlarmPointLL() != null && (x.getCraftDownLimitInclude() == null || x.getCraftDownLimitInclude() == CommonEnum.CraftDownLimitEnum.YES.getIndex()) && x.getCraftDownLimitValue() > x.getAlarmPointLL())
//                        || (x.getCraftDownLimitValue() != null && x.getAlarmPointLL() != null && (x.getCraftDownLimitInclude() != null && x.getCraftDownLimitInclude() == CommonEnum.CraftDownLimitEnum.NO.getIndex()) && x.getCraftDownLimitValue() >= x.getAlarmPointLL())
//                        || (x.getCraftUpLimitValue() != null && x.getAlarmPointHH() != null && (x.getCraftUpLimitInclude() == null || x.getCraftUpLimitInclude() == CommonEnum.CraftUpLimitEnum.YES.getIndex()) && x.getCraftUpLimitValue() < x.getAlarmPointHH())
//                        || (x.getCraftUpLimitValue() != null && x.getAlarmPointHH() != null && (x.getCraftUpLimitInclude() != null && x.getCraftUpLimitInclude() == CommonEnum.CraftUpLimitEnum.NO.getIndex()) && x.getCraftUpLimitValue() <= x.getAlarmPointHH())
//                        || (x.getCraftDownLimitValue() != null && x.getAlarmPointLO() != null && (x.getCraftDownLimitInclude() == null || x.getCraftDownLimitInclude() == CommonEnum.CraftDownLimitEnum.YES.getIndex()) && x.getCraftDownLimitValue() > x.getAlarmPointLO())
//                        || (x.getCraftDownLimitValue() != null && x.getAlarmPointLO() != null && (x.getCraftDownLimitInclude() != null && x.getCraftDownLimitInclude() == CommonEnum.CraftDownLimitEnum.NO.getIndex()) && x.getCraftDownLimitValue() >= x.getAlarmPointLO())
//                        || (x.getCraftUpLimitValue() != null && x.getAlarmPointHI() != null && (x.getCraftUpLimitInclude() == null || x.getCraftUpLimitInclude() == CommonEnum.CraftUpLimitEnum.YES.getIndex()) && x.getCraftUpLimitValue() < x.getAlarmPointHI())
//                        || (x.getCraftUpLimitValue() != null && x.getAlarmPointHI() != null && (x.getCraftUpLimitInclude() != null && x.getCraftUpLimitInclude() == CommonEnum.CraftUpLimitEnum.NO.getIndex()) && x.getCraftUpLimitValue() <= x.getAlarmPointHI())) {
//                    x.setIsRed(1);
//                } else
//                    x.setIsRed(0);
//            });
//        }
//
//        List<UnitEntity> unitList = new ArrayList<>();
//        if (listAlarmPoint != null && listAlarmPoint.getTotal() < 1000) {
//            unitList = basicDataService.getUnitListByIds(listAlarmPoint.getPageList().stream().map(x -> x.getPrdtCell().getUnitId()).toArray(String[]::new), false);
//        } else {
//            unitList = basicDataService.getUnitList(false);
//        }
//        int i = 0;
//        for (AlarmPointEntity ape : returnAlarmPoint.getPageList()) {
//            AlarmPoint ap = listAlarmPoint.getPageList().get(i);
//            ape.setUnitId(ap.getPrdtCell().getUnitId());
//            UnitEntity ue = unitList.stream().filter(ul -> ap.getPrdtCell().getUnitId().equals(ul.getStdCode())).findFirst().orElse(new UnitEntity());
//            ape.setUnitSname(ue.getSname());
//            ape.setPrdtCellSname(ap.getPrdtCell().getSname());
//            ape.setAlarmPointTypeName(ap.getAlarmPointType().getName());
//            //ape.setMeasunitName(ap.getMeasUnit().getName() + "(" + ap.getMeasUnit().getSign() + ")");
//            if (isExport == 0) {
//                ape.setMeasunitName(ap.getMeasUnit().getName() + "(" + ap.getMeasUnit().getSign() + ")");
//            } else {
//                ape.setMeasunitName(ap.getMeasUnit().getSign());
//            }
//
//            ape.setSignName(ap.getMeasUnit().getSign());
//            Double culv = ap.getCraftUpLimitValue();//工艺卡片上限值
//            Double cdlv = ap.getCraftDownLimitValue();//工艺卡片下限值
//            Integer culi = ap.getCraftUpLimitInclude();//工艺卡片上限值是否包含(1是；0否)
//            Integer cdli = ap.getCraftDownLimitInclude();//工艺卡片下限值是否包含(1是；0否)
//            Double iulv = ap.getInterlockUpLimitValue();//联锁上限值
//            Double idlv = ap.getInterlockDownLimitValue();//联锁下限值
//            Integer iuli = ap.getInterlockUpLimitInclude();//联锁上限值是否包含(1是；0否)
//            Integer idli = ap.getInterlockDownLimitInclude();//联锁下限值是否包含(1是；0否)
//            iuli = iuli == null ? 0 : iuli;
//            idli = idli == null ? 0 : idli;
//            if (culv != null && cdlv != null) {
//                String culvStr = changeDouble(culv);
//                String cdlvStr = changeDouble(cdlv);
//                ape.setCraftLimitValue(cdlvStr + "～" + culvStr);
//            } else if (culv != null && cdlv == null) {
//                if (culi != null && culi.intValue() == 1) {
//                    String culvStr = changeDouble(culv);
//                    ape.setCraftLimitValue("≤" + culvStr);
//                } else if (culi != null && culi.intValue() == 0) {
//                    String culvStr = changeDouble(culv);
//                    ape.setCraftLimitValue("<" + culvStr);
//                }
//            } else if (culv == null && cdlv != null) {
//                if (cdli != null && cdli.intValue() == 1) {
//                    String cdlvStr = changeDouble(cdlv);
//                    ape.setCraftLimitValue("≥" + cdlvStr);
//                } else if (cdli != null && cdli.intValue() == 0) {
//                    String cdlvStr = changeDouble(cdlv);
//                    ape.setCraftLimitValue(">" + cdlvStr);
//                }
//            } else if (culv == null && cdlv == null) {
//                ape.setCraftLimitValue("");
//            }
//            //联锁值
//            if (iulv != null && idlv != null) {
//                String iulvStr = changeDouble(iulv);
//                String idlvStr = changeDouble(idlv);
//
//                if (iuli.intValue() == 1 && idli.intValue() == 1) {
//                    ape.setInterlockLimitValue(">" + iulvStr + "，<" + idlvStr);
//                } else if (iuli.intValue() == 1 && idli.intValue() == 0) {
//                    ape.setInterlockLimitValue(">" + iulvStr + "，≤" + idlvStr);
//                } else if (iuli.intValue() == 0 && idli.intValue() == 1) {
//                    ape.setInterlockLimitValue("≥" + iulvStr + "，<" + idlvStr);
//                } else if (iuli.intValue() == 0 && idli.intValue() == 0) {
//                    ape.setInterlockLimitValue("≥" + iulvStr + "，≤" + idlvStr);
//                }
//            } else if (iulv != null && idlv == null) {
//                if (iuli.intValue() == 1) {
//                    String iulvStr = changeDouble(iulv);
//                    ape.setInterlockLimitValue(">" + iulvStr);
//                } else if (iuli.intValue() == 0) {
//                    String iulvStr = changeDouble(iulv);
//                    ape.setInterlockLimitValue("≥" + iulvStr);
//                }
//            } else if (iulv == null && idlv != null) {
//                if (idli.intValue() == 1) {
//                    String idlvStr = changeDouble(idlv);
//                    ape.setInterlockLimitValue("<" + idlvStr);
//                } else if (idli.intValue() == 0) {
//                    String idlvStr = changeDouble(idlv);
//                    ape.setInterlockLimitValue("≤" + idlvStr);
//                }
//            } else if (iulv == null && idlv == null) {
//                ape.setInterlockLimitValue("");
//            }
//            i++;
//        }

    }

    @Override
    public List<AlarmPointTableVO> getAlarmPointExport(int isExport, String[] unitCodes, Long[] prdtCellIds, String tag, Long typeId,
                                                           Integer inUse, Integer instrmtPriority) throws Exception {
        if (unitCodes == null || unitCodes.length == 0 || Arrays.asList(unitCodes).contains("-1")) {
            List<UnitEntity> unitList = basicDataService.getUnitList(true);
            unitCodes = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
        }
        CommonProperty commonProperty = new CommonProperty();
        AlarmPointTableParamVO paramVO = new AlarmPointTableParamVO();
        paramVO.setCompanyId(commonProperty.getCompanyId());

        if (unitCodes != null && unitCodes.length > 0) {
            paramVO.setUnitCodeList(Arrays.asList(unitCodes));
        }
        if (prdtCellIds != null && prdtCellIds.length > 0) {
            paramVO.setPrdtCellIds(Arrays.asList(prdtCellIds));
        }
        paramVO.setAlarmPointTypeId(typeId);
        paramVO.setTag(tag);
        paramVO.setInUse(inUse);
        paramVO.setInstrmtPriority(instrmtPriority);
        List<AlarmPointTableVO> pageList = alarmPointDAO.selectTable(paramVO);
        for (AlarmPointTableVO tableVO : pageList) {
            Integer monitorType = tableVO.getMonitorType();
            if (monitorType != null) {
                tableVO.setMonitorTypeStr(CommonEnum.MonitorTypeEnum.getName(monitorType));
            }
            if (null != tableVO.getInstrmtPriority()) {
                tableVO.setInstrmtPriorityShow(CommonEnum.InstrmtPriorityEnum.getName(tableVO.getInstrmtPriority()));
            } else {
                tableVO.setInstrmtPriorityShow(CommonEnum.InstrmtPriorityEnum.NoLevel.getName());
            }
            tableVO.setMeasunitName(tableVO.getMeasunitName() + "(" + tableVO.getSignName() + ")");
            Double culv = tableVO.getCraftUpLimitValue();//工艺卡片上限值
            Double cdlv = tableVO.getCraftDownLimitValue();//工艺卡片下限值
            Integer culi = tableVO.getCraftUpLimitInclude();//工艺卡片上限值是否包含(1是；0否)
            Integer cdli = tableVO.getCraftDownLimitInclude();//工艺卡片下限值是否包含(1是；0否)
            Double iulv = tableVO.getInterlockUpLimitValue();//联锁上限值
            Double idlv = tableVO.getInterlockDownLimitValue();//联锁下限值
            Integer iuli = tableVO.getInterlockUpLimitInclude();//联锁上限值是否包含(1是；0否)
            Integer idli = tableVO.getInterlockDownLimitInclude();//联锁下限值是否包含(1是；0否)
            iuli = iuli == null ? 0 : iuli;
            idli = idli == null ? 0 : idli;
            Double alarmPointLl = tableVO.getAlarmPointLl();
            Double alarmPointLo = tableVO.getAlarmPointLo();
            Double alarmPointHh = tableVO.getAlarmPointHh();
            Double alarmPointHi = tableVO.getAlarmPointHi();
            if ((cdlv != null && alarmPointLl != null && (cdli == null || cdli == CommonEnum.CraftDownLimitEnum.YES.getIndex()) && cdlv > alarmPointLl)
                    || (cdlv != null && alarmPointLl != null && (cdli != null && cdli == CommonEnum.CraftDownLimitEnum.NO.getIndex()) && cdlv >= alarmPointLl)
                    || (culv != null && alarmPointHh != null && (culi == null || culi == CommonEnum.CraftUpLimitEnum.YES.getIndex()) && culv < alarmPointHh)
                    || (culv != null && alarmPointHh != null && (culi != null && culi == CommonEnum.CraftUpLimitEnum.NO.getIndex()) && culv <= alarmPointHh)
                    || (cdlv != null && alarmPointLo != null && (cdli == null || cdli == CommonEnum.CraftDownLimitEnum.YES.getIndex()) && cdlv > alarmPointLo)
                    || (cdlv != null && alarmPointLo != null && (cdli != null && cdli == CommonEnum.CraftDownLimitEnum.NO.getIndex()) && cdlv >= alarmPointLo)
                    || (culv != null && alarmPointHi != null && (culi == null || culi == CommonEnum.CraftUpLimitEnum.YES.getIndex()) && culv < alarmPointHi)
                    || (culv != null && alarmPointHi != null && (culi != null && culi == CommonEnum.CraftUpLimitEnum.NO.getIndex()) && culv <= alarmPointHi)) {
                tableVO.setIsRed(1);
            } else {
                tableVO.setIsRed(0);
            }
            if (culv != null && cdlv != null) {
                String culvStr = changeDouble(culv);
                String cdlvStr = changeDouble(cdlv);
                tableVO.setCraftLimitValue(cdlvStr + "～" + culvStr);
            } else if (culv != null && cdlv == null) {
                if (culi != null && culi.intValue() == 1) {
                    String culvStr = changeDouble(culv);
                    tableVO.setCraftLimitValue("≤" + culvStr);
                } else if (culi != null && culi.intValue() == 0) {
                    String culvStr = changeDouble(culv);
                    tableVO.setCraftLimitValue("<" + culvStr);
                }
            } else if (culv == null && cdlv != null) {
                if (cdli != null && cdli.intValue() == 1) {
                    String cdlvStr = changeDouble(cdlv);
                    tableVO.setCraftLimitValue("≥" + cdlvStr);
                } else if (cdli != null && cdli.intValue() == 0) {
                    String cdlvStr = changeDouble(cdlv);
                    tableVO.setCraftLimitValue(">" + cdlvStr);
                }
            } else if (culv == null && cdlv == null) {
                tableVO.setCraftLimitValue("");
            }
            //联锁值
            if (iulv != null && idlv != null) {
                String iulvStr = changeDouble(iulv);
                String idlvStr = changeDouble(idlv);

                if (iuli.intValue() == 1 && idli.intValue() == 1) {
                    tableVO.setInterlockLimitValue(">" + iulvStr + "，<" + idlvStr);
                } else if (iuli.intValue() == 1 && idli.intValue() == 0) {
                    tableVO.setInterlockLimitValue(">" + iulvStr + "，≤" + idlvStr);
                } else if (iuli.intValue() == 0 && idli.intValue() == 1) {
                    tableVO.setInterlockLimitValue("≥" + iulvStr + "，<" + idlvStr);
                } else if (iuli.intValue() == 0 && idli.intValue() == 0) {
                    tableVO.setInterlockLimitValue("≥" + iulvStr + "，≤" + idlvStr);
                }
            } else if (iulv != null && idlv == null) {
                if (iuli.intValue() == 1) {
                    String iulvStr = changeDouble(iulv);
                    tableVO.setInterlockLimitValue(">" + iulvStr);
                } else if (iuli.intValue() == 0) {
                    String iulvStr = changeDouble(iulv);
                    tableVO.setInterlockLimitValue("≥" + iulvStr);
                }
            } else if (iulv == null && idlv != null) {
                if (idli.intValue() == 1) {
                    String idlvStr = changeDouble(idlv);
                    tableVO.setInterlockLimitValue("<" + idlvStr);
                } else if (idli.intValue() == 0) {
                    String idlvStr = changeDouble(idlv);
                    tableVO.setInterlockLimitValue("≤" + idlvStr);
                }
            } else if (iulv == null && idlv == null) {
                tableVO.setInterlockLimitValue("");
            }
        }
        return pageList;
    }

    /**
     * 判断报警点在报警事件表中是否使用
     *
     * @param alarmPointId
     * @return
     * <AUTHOR> 2017-11-29
     */
    public CommonResult getAlarmPointIsUseInAlarmEvent(Long alarmPointId) {
        CommonResult commonResult = new CommonResult();
        Long count = 0L;
        count = alarmEventRepository.getAlarmPointIsUseInAlarmEvent(alarmPointId);
        commonResult.setMessage(count + "");
        return commonResult;
    }

    public String changeDouble(Double num) {
        if ((num + "").endsWith(".0")) {
            return num.intValue() + "";
        }
        return num + "";
    }

    /**
     * 报警点维护数据导入
     *
     * @param inputStream Excel文件流
     * @return 导入成功信息
     * <AUTHOR> 2017-11-16
     */
    @SuppressWarnings("unchecked")
    public String importAlarmPoint(InputStream inputStream) throws Exception {
        List<List<String[]>> allSheetContent = ExcelHelper.importAllSheetExcel(inputStream, 0, true);
        if (allSheetContent.size() == 0) {
            return "没有可导入的数据！";
        } else if (allSheetContent.size() == 1) {
            if (allSheetContent.get(0).size() < 2)
                return "没有可导入的数据！";
        } else if (allSheetContent.get(0).size() < 2 && allSheetContent.get(1).size() < 2)
            return "没有可导入的数据！";
        //sheet1 内容导入
        int sheet1Num = sheet1Import(allSheetContent.get(0));
        //sheet2 内容导入
//        int sheet2Num =0;
//        if (allSheetContent.size()>1)
//          sheet2Num = sheet2Import(allSheetContent.get(1));
//        return String.format("sheet-1共导入%s条数据，sheet-2共更新%s条数据，导入成功！", sheet1Num, sheet2Num);
        return String.format("sheet-1共导入%s条数据，导入成功！", sheet1Num);
    }

    /**
     * 停用报警点
     *
     * @param alarmPointIds 报警点id集合
     * @return String 返回字符串对象
     * <AUTHOR> 2018-08-30
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult stopAlarmPoint(Long[] alarmPointIds) throws Exception {
        CommonResult commonResult = null;
        List<AlarmPoint> alarmPoint = repo.getAlarmPoint(alarmPointIds);
        for (AlarmPoint ap : alarmPoint) {
            ap.setInUse(CommonEnum.InUseEnum.No.getIndex());
            commonResult = repo.updateAlarmPoint(ap);
        }
        return commonResult;
    }

    /**
     * 获取优先级列表
     *
     * @param isAll 是否显示全部
     * @return
     */
    @Override
    public List<DictionaryEntity> getInstrmtPriorityList(boolean isAll) throws Exception {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            for (CommonEnum.InstrmtPriorityEnum InstrmtPriorityEnum : CommonEnum.InstrmtPriorityEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(InstrmtPriorityEnum.getIndex(), InstrmtPriorityEnum.getName()));
            }
            if (isAll) {
                dictionaryEntityArrayList.add(0, new DictionaryEntity(-1, "全部"));
                //dictionaryEntityArrayList.add(new DictionaryEntity(9, "空"));
            } else {
                //dictionaryEntityArrayList.add(0, new DictionaryEntity(9, ""));
            }
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 校验
     *
     * @param entity 报警点实体
     * @return void
     * @throws BusiException 
     * <AUTHOR> 2017-10-11
     */
    @SuppressWarnings("unused")
    private void alarmPointValidation(AlarmPoint entity) throws Exception {
        CommonResult commonResult = new CommonResult();
        // 实体不能为空
        if (entity == null) {
            throw new BusiException("00", "没有报警点数据！");
        }
        Double hh = entity.getAlarmPointHH();
        Double hi = entity.getAlarmPointHI();
        Double lo = entity.getAlarmPointLO();
        Double ll = entity.getAlarmPointLL();
        String message = "";
        if (hh != null) {
            if (hi != null && hh < hi) {
                message = "报警点高高报须大于等于报警点高报！";
                lo = ll = hi = null;
            }
            if (lo != null && hi != null && hi <= lo) {
                message = "报警点高报须大于报警点低报！";
                lo = ll = hi = null;
            } else if (ll != null && hi != null && hi <= ll) {
                message = "报警点高报须大于报警点低低报！";
                lo = ll = hi = null;
            }
            if (lo != null && hh <= lo) {
                message = "报警点高高报须大于报警点低报！";
                lo = ll = hi = null;
            }
            if (ll != null && lo != null && lo < ll) {
                message = "报警点低报须大于等于报警点低低报！";
                lo = ll = hi = null;
            }
            if (ll != null && hh <= ll) {
                message = "报警点高高报须大于报警点低低报！";
                lo = ll = hi = null;
                if (lo != null && lo < ll) {
                    message = "报警点低报须大于等于报警点低低报！";
                    lo = ll = hi = null;
                }
            }
        } else if (hi != null) {
            if (lo != null && hi <= lo) {
                message = "报警点高报须大于报警点低报！";
                lo = ll = null;
            }
            if (ll != null && hi <= ll) {
                message = "报警点高报须大于报警点低低报！";
                hh = hi = lo = ll = null;
            }
        } else if (lo != null && ll != null && lo < ll) {
            message = "报警点低报须大于等于报警点低低报！";
        }
        if (!"".equals(message)) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(message);
        }
        if (commonResult.getIsSuccess() == true) {
            // 调用DAL与数据库相关的校验
            commonResult = repo.alarmPointValidation(entity);
        }
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
    }

    //region 数据导入

    /**
     * 导入sheet1数据
     *
     * @param excelContent excel内容
     * @return void
     * @throws BusiException 
     * <AUTHOR> 2017-10-11
     */
    @Transactional
    public int sheet1Import(List<String[]> excelContent) throws Exception {

        //读取excel文件
        List<AlarmPointEntity> apEntityList = new ArrayList<>();
        //校验字段
        String message = sheet1Verify(excelContent, apEntityList);
        if (!message.trim().isEmpty()) {
            throw new Exception(message);
        } else if (apEntityList != null && apEntityList.size() == 0) {
            return 0;
        } else {
            // 实体转换为持久层实体
            List<AlarmPoint> alarmPointList = ObjectConverter.listConverter(apEntityList, AlarmPoint.class);
            List<AlarmFlag> afList = afRepository.getAllAlarmFlag();
            List<TagExtraMessage> temList = new ArrayList<>();

            CommonProperty commonProperty = new CommonProperty();
            String userId = commonProperty.getUserId();
            String userName = commonProperty.getUserName();
            Date systemDateTime = commonProperty.getSystemDateTime();
            Integer companyId = commonProperty.getCompanyId();
            for (AlarmPoint alarmPoint : alarmPointList) {
                // 数据校验
                CommonResult commonResult = alarmPointImportValidation(alarmPoint);
                //“级别”，工艺卡片上限值、工艺卡片下限值、联锁上限值、联锁下限值均为空时级别为B，否则级别为A
                if (alarmPoint.getCraftUpLimitValue() == null
                        && alarmPoint.getCraftDownLimitValue() == null
                        && alarmPoint.getInterlockUpLimitValue() == null
                        && alarmPoint.getInterlockDownLimitValue() == null)
                    alarmPoint.setCraftRank(CommonEnum.CraftRankEnum.B.getIndex());
                else
                    alarmPoint.setCraftRank(CommonEnum.CraftRankEnum.A.getIndex());
                //新增
                if (!commonResult.getIsSuccess()) {

                    //region 级别
                    //新增时默认为“B”，否则不更新；
                    //alarmPoint.setCraftRank(CommonEnum.CraftRankEnum.B.getIndex());
                    //endregion

                    //region 虚拟标识
                    //列“虚拟标识”新增时默认为“0”，否则不更新
                    alarmPoint.setVirtualFlag(0);
                    //endregion

                    //设置发送短信默认为0
                    alarmPoint.setInSendMsg(0);

                    // 赋值 创建人、创建名称、创建时间
                    //CommonUtil.returnValue(alarmPoint, PageModelEnum.NewAdd.getIndex());
                    alarmPoint.setMntUserId(userId);
                    alarmPoint.setMntUserName(userName);
                    alarmPoint.setMntDate(systemDateTime);
                    alarmPoint.setCompanyId(companyId);

                    alarmPoint.setCrtUserId(userId);
                    alarmPoint.setCrtUserName(userName);
                    alarmPoint.setCrtDate(systemDateTime);
                    alarmPoint.setCompanyId(commonProperty.getCompanyId());
                    commonResult = repo.addAlarmPoint(alarmPoint);
                    // 如果失败，直接throw
                    if (commonResult.getIsSuccess() == false)
                        throw new Exception(commonResult.getMessage());

                    for (AlarmFlag af : afList) {
                        TagExtraMessage temEntity = new TagExtraMessage();
                        temEntity.setAlarmPointId(alarmPoint.getAlarmPointId());
                        temEntity.setAlarmFlagId(af.getAlarmFlagId());
                        temEntity.setPrescribedResponseDuration(30L);
                        // 赋值 创建人、创建名称、创建时间
                        //CommonUtil.returnValue(temEntity, PageModelEnum.NewAdd.getIndex());
                        temEntity.setMntUserId(userId);
                        temEntity.setMntUserName(userName);
                        temEntity.setMntDate(systemDateTime);

                        temEntity.setCrtUserId(userId);
                        temEntity.setCrtUserName(userName);
                        temEntity.setCrtDate(systemDateTime);
                        temList.add(temEntity);
                    }
                }
                //编辑
                else {
                    AlarmPoint alarmPoint1 = (AlarmPoint) commonResult.getResult();
                    alarmPoint.setAlarmPointId(alarmPoint1.getAlarmPointId());
                    //alarmPoint.setCraftRank(alarmPoint1.getCraftRank());
                    alarmPoint.setVirtualFlag(alarmPoint1.getVirtualFlag());

                    //设置发送短信默认为0
                    alarmPoint.setInSendMsg(0);

                    alarmPoint.setCompanyId(commonProperty.getCompanyId());
                    CommonUtil.objectExchange(alarmPoint, alarmPoint1);
                    // 赋值 修改人、修改名称、修改时间
                    //CommonUtil.returnValue(alarmPoint1, PageModelEnum.Edit.getIndex());
                    alarmPoint1.setMntUserId(userId);
                    alarmPoint1.setMntUserName(userName);
                    alarmPoint1.setMntDate(systemDateTime);
                    // 调用DAL更新方法
                    commonResult = repo.updateAlarmPoint(alarmPoint1);
                    // 如果失败，直接throw
                    if (commonResult.getIsSuccess() == false)
                        throw new Exception(commonResult.getMessage());
                }
            }

            temRepository.batchInsert(temList);

            //批量导入数据时删除未匹配报警点中的数据
            for (AlarmPoint alarmPoint : alarmPointList) {
                Integer res = unMatchAlarmPointRepository.deleteUmMatchByTagPrd(alarmPoint.getTag(), alarmPoint.getPrdtCellId());
                log.info("批量导入报警点时删除unMatchAlarmPoint-{}", res);
            }


            return alarmPointList.size();
        }
    }

    /**
     * 导入sheet2数据
     *
     * @param excelContent excel内容
     * @return void
     * @throws BusiException 
     * <AUTHOR> 2017-10-11
     */
    @Transactional
    public int sheet2Import(List<String[]> excelContent) throws Exception {
        int num = 0;
        //读取excel文件
        List<AlarmPointEntity> apEntityList = new ArrayList<>();
        //校验字段
        String message = sheet2Verify(excelContent, apEntityList);
        if (!message.trim().isEmpty()) {
            throw new Exception(message);
        } else if (apEntityList != null && apEntityList.size() == 0) {
            return 0;
        } else {
            // 实体转换为持久层实体
            List<AlarmPoint> alarmPointList = ObjectConverter.listConverter(apEntityList, AlarmPoint.class);

            CommonProperty commonProperty = new CommonProperty();
            String userId = commonProperty.getUserId();
            String userName = commonProperty.getUserName();
            Date systemDateTime = commonProperty.getSystemDateTime();

            for (AlarmPoint alarmPoint : alarmPointList) {
                // 数据校验
                CommonResult commonResult = alarmPointImportValidation(alarmPoint);
                if (!commonResult.getIsSuccess()) {
                    continue;
                }
                AlarmPoint alarmPoint1 = (AlarmPoint) commonResult.getResult();
                alarmPoint1.setCraftUpLimitInclude(alarmPoint.getCraftUpLimitInclude());
                alarmPoint1.setCraftUpLimitValue(alarmPoint.getCraftUpLimitValue());
                alarmPoint1.setCraftDownLimitInclude(alarmPoint.getCraftDownLimitInclude());
                alarmPoint1.setCraftDownLimitValue(alarmPoint.getCraftDownLimitValue());
                alarmPoint1.setInterlockUpLimitInclude(alarmPoint.getInterlockUpLimitInclude());
                alarmPoint1.setInterlockUpLimitValue(alarmPoint.getInterlockUpLimitValue());
                alarmPoint1.setInterlockDownLimitInclude(alarmPoint.getInterlockDownLimitInclude());
                alarmPoint1.setInterlockDownLimitValue(alarmPoint.getInterlockDownLimitValue());
                alarmPoint1.setInstrmtPriority(alarmPoint.getInstrmtPriority());
                //region 级别
                //“级别”，工艺卡片上限值、工艺卡片下限值、联锁上限值、联锁下限值均为空时级别为B，否则级别为A
                if (alarmPoint1.getCraftUpLimitValue() == null
                        && alarmPoint1.getCraftDownLimitValue() == null
                        && alarmPoint1.getInterlockUpLimitValue() == null
                        && alarmPoint1.getInterlockDownLimitValue() == null)
                    alarmPoint1.setCraftRank(CommonEnum.CraftRankEnum.B.getIndex());
                else
                    alarmPoint1.setCraftRank(CommonEnum.CraftRankEnum.A.getIndex());

                //endregion

                // 赋值 修改人、修改名称、修改时间
                //CommonUtil.returnValue(alarmPoint1, PageModelEnum.Edit.getIndex());
                alarmPoint1.setMntUserId(userId);
                alarmPoint1.setMntUserName(userName);
                alarmPoint1.setMntDate(systemDateTime);

                // 调用DAL更新方法
                commonResult = repo.updateAlarmPoint(alarmPoint1);
                // 如果失败，直接throw
                if (commonResult.getIsSuccess() == false)
                    throw new Exception(commonResult.getMessage());
                num++;
            }
            return num;
        }
    }

    /**
     * 抽取sheet1数据
     *
     * @param excelContent excel导入内容
     * @param apEntityList 报警点实体集合
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-16
     */
    @SuppressWarnings("unchecked")
    private String sheet1Verify(List<String[]> excelContent, List<AlarmPointEntity> apEntityList) throws Exception {
        //没有可导入的数据！
        if (excelContent.size() < 2) {
            return "";
        }

        //region 校验标题
        String titles = "装置、生产单元、位号、位置、PID图号、计量单位、高高报值、高报值、低报值、低低报值、是否虚表、报警点类型、监测类型、仪表类型、实时数据库位号、仪表优先级、工艺卡片值、联锁值、是否启用、排序、描述";
        String[] titlesArr = titles.split("、");
        for (int i = 0; i < titlesArr.length; i++) {
            if (!titlesArr[i].equals(excelContent.get(0)[i])) {
                return "sheet-1 列" + titlesArr[i] + "列名不合法！";
            }
        }
        //endregion

        //region 数据校验枚举值、数据

        //获取装置数据
//        unitList = unitRepository.getUnit(null);
        unitList = basicDataService.getUnitList(false);
        if (this.unitList == null || this.unitList.isEmpty()) {
            throw new Exception("装置表没有数据");
        }

        //获取生产单元数据
        List<PrdtCell> prdtCellList = prdtCellRepository.getPrdtCell(null);
        if (prdtCellList == null || prdtCellList.isEmpty()) {
            throw new Exception("生产单元表没有数据");
        }
        prdtCellList = prdtCellList.stream().filter(x -> x.getInUse().equals(CommonEnum.InUseEnum.Yes.getIndex())).collect(Collectors.toList());
        prdtCellEntityList = ObjectConverter.listConverter(prdtCellList, PrdtCellEntity.class);

        //报警点类型
        List<AlarmPointTypeEntity> aptEntityList = basicDataService.getAlarmPointTypeList(false);
        //监测类型
        List<DictionaryEntity> mtList = basicDataService.getMonitorTypeList(false);
        //计量单位
        List<MeasUnitEntity> muList = basicDataService.getMeasUnitList(false);
        //仪表类型
        List<DictionaryEntity> itList = basicDataService.getInstrmtTypeList(false);

        //是否虚表
        List<DictionaryEntity> vrfList = new ArrayList<>();
        for (CommonEnum.VirtualRealityFlagEnum vrfEnum : CommonEnum.VirtualRealityFlagEnum.values()) {
            vrfList.add(new DictionaryEntity(vrfEnum.getIndex(), vrfEnum.getName()));
        }

        //是否启用
        List<DictionaryEntity> yonList = new ArrayList<>();
        for (CommonEnum.InUseEnum inUseEnum : CommonEnum.InUseEnum.values()) {
            yonList.add(new DictionaryEntity(inUseEnum.getIndex(), inUseEnum.getName()));
        }

        //endregion

        for (int i = 1; i < excelContent.size(); i++) {
            AlarmPointEntity apEntity = new AlarmPointEntity();
            String[] itemContent = excelContent.get(i);
            for (int j = 0; j < itemContent.length; j++) {
                itemContent[j] = itemContent[j].replace("　", "").trim();
            }
            //region 检查数据必须录入和数据格式

            //region 装置
            if (itemContent[0].isEmpty()) {
                return "sheet-1 行" + (i + 1) + "装置不能为空！";
            }
            UnitEntity unit = this.unitList.stream().filter(u -> u.getSname()
                            .equals(itemContent[0]))
                    .findFirst().orElse(null);

            if (unit == null) {
                return "sheet-1 行" + (i + 1) + "装置不存在！";
            }
            apEntity.setUnitId(unit.getStdCode());
            //endregion

            //region 生产单元
            if (itemContent[1].isEmpty()) {
                return "sheet-1 行" + (i + 1) + "生产单元不能为空！";
            }
            PrdtCellEntity pcEntity = prdtCellEntityList.stream().filter(u -> u.getUnitId().equals(apEntity.getUnitId()) && u.getSname()
                            .equals(itemContent[1]))
                    .findFirst().orElse(null);

            if (pcEntity == null) {
                return "sheet-1 行" + (i + 1) + "该装置下生产单元不存在！";
            }
            apEntity.setPrdtCellId(pcEntity.getPrdtCellId());
            //endregion

            //region 位号
            if (itemContent[2].isEmpty()) {
                return "sheet-1 行" + (i + 1) + "位号不能为空！";
            }
            AlarmPointEntity ape = apEntityList.stream().filter(x -> x.getPrdtCellId().equals(apEntity.getPrdtCellId()) && x.getTag().equals(itemContent[2])).findFirst().orElse(null);
            if (ape != null) {
                return "sheet-1 行" + (i + 1) + "位号重复！";
            }
            apEntity.setTag(itemContent[2]);
            //endregion

            //region 位置
            if (!itemContent[3].isEmpty()) {
                apEntity.setLocation(itemContent[3]);
            }
            //endregion

            //region PID图号
            if (!itemContent[4].isEmpty()) {
                apEntity.setPidCode(itemContent[4]);
            }
            //endregion

            //region 计量单位
            if (itemContent[5].isEmpty()) {
                return "sheet-1 行" + (i + 1) + "计量单位不能为空！";
            }
            String sign = itemContent[5].trim().toLowerCase()
                    .replace("（", "(")
                    .replace("）", ")")
                    .replace(" ", "");
            MeasUnitEntity muEntity = muList.stream().filter(u -> u.getSign().trim().toLowerCase()
                            .replace("（", "(")
                            .replace("）", ")")
                            .replace(" ", "")
                            .equals(sign))
                    .findFirst().orElse(null);
            if (muEntity == null) {
                return "sheet-1 行" + (i + 1) + "计量单位不存在！";
            }
            apEntity.setMeasunitId(muEntity.getMeasUnitId());
            //endregion

            //region 报警点高高报
            if (!itemContent[6].isEmpty()) {
                String validMess = numberValidation(itemContent[6], 20, 5);
                if (StringUtils.isEmpty(validMess)) {
                    apEntity.setAlarmPointHH(Double.valueOf(itemContent[6]));
                } else {
                    return "sheet-1 行" + (i + 1) + "高高报值，" + validMess;
                }
            }
            //endregion

            //region 报警点高报
            if (!itemContent[7].isEmpty()) {
                String validMess = numberValidation(itemContent[7], 20, 5);
                if (StringUtils.isEmpty(validMess)) {
                    apEntity.setAlarmPointHI(Double.valueOf(itemContent[7]));
                } else {
                    return "sheet-1 行" + (i + 1) + "高报值，" + validMess;
                }
            }
            //endregion

            //region 报警点低报
            if (!itemContent[8].isEmpty()) {
                String validMess = numberValidation(itemContent[8], 20, 5);
                if (StringUtils.isEmpty(validMess)) {
                    apEntity.setAlarmPointLO(Double.valueOf(itemContent[8]));
                } else {
                    return "sheet-1 行" + (i + 1) + "低报值，" + validMess;
                }
            }
            //endregion

            //region 报警点低低报
            if (!itemContent[9].isEmpty()) {
                String validMess = numberValidation(itemContent[9], 20, 5);
                if (StringUtils.isEmpty(validMess)) {
                    apEntity.setAlarmPointLL(Double.valueOf(itemContent[9]));
                } else {
                    return "sheet-1 行" + (i + 1) + "低低报值，" + validMess;
                }
            }
            //endregion

            //region 校验各报警点数据是否准确
            String message = alarmPointValidation(apEntity);
            if (message.length() > 0) {
                return "sheet-1 行" + (i + 1) + message;
            }
            //endregion

            //region 是否虚表
            if (itemContent[10].isEmpty()) {
                return "sheet-1 行" + (i + 1) + "是否虚表不能为空！请选择是否虚表";
            }
            DictionaryEntity vrfDicEntity = vrfList.stream().filter(u -> u.getValue()
                            .equals(itemContent[10]))
                    .findFirst().orElse(null);

            if (vrfDicEntity == null) {
                return "sheet-1 行" + (i + 1) + "是否虚表不存在！";
            }
            apEntity.setVirtualRealityFlag(Integer.valueOf(vrfDicEntity.getKey().toString()));
            //endregion

            //region 报警点类型
            if (itemContent[11].isEmpty()) {
                return "sheet-1 行" + (i + 1) + "报警点类型不能为空！请选择报警点类型";
            }
            AlarmPointTypeEntity aptEntity = aptEntityList.stream().filter(u -> u.getName()
                            .equals(itemContent[11]))
                    .findFirst().orElse(null);

            if (aptEntity == null) {
                return "sheet-1 行" + (i + 1) + "报警点类型不存在！";
            }
            apEntity.setAlarmPointTypeId(aptEntity.getAlarmPointTypeId());
            //endregion

            //region 监测类型
            if (itemContent[12].isEmpty()) {
                return "sheet-1 行" + (i + 1) + "监测类型不能为空！请选择监测类型";
            }
            DictionaryEntity dicEntity = mtList.stream().filter(u -> u.getValue()
                            .equals(itemContent[12]))
                    .findFirst().orElse(null);

            if (dicEntity == null) {
                return "sheet-1 行" + (i + 1) + "监测类型不存在！";
            }
            apEntity.setMonitorType(Integer.valueOf(dicEntity.getKey().toString()));
            //endregion

            //region 仪表类型
            if (itemContent[13].isEmpty()) {
                return "sheet-1 行" + (i + 1) + "仪表类型不能为空！请选择仪表类型";
            }
            DictionaryEntity itDicEntity = itList.stream().filter(u -> u.getValue()
                            .equals(itemContent[13]))
                    .findFirst().orElse(null);

            if (itDicEntity == null) {
                return "sheet-1 行" + (i + 1) + "仪表类型不存在！";
            }
            apEntity.setInstrmtType(Integer.valueOf(itDicEntity.getKey().toString()));
            //endregion


            //rtdbTag
            if (!itemContent[14].isEmpty()) {
                apEntity.setRtdbTag(itemContent[14]);
            }
            //endrtdbTag

            //instrmtPriorityStr仪表优先级
            String instrmtPriorityStr = itemContent[15];
            //region 仪表优先级
            if (StringUtils.isNotEmpty(instrmtPriorityStr)) {
                if (CommonEnum.InstrmtPriorityEnum.MonitoringInstrmt.getName().equals(instrmtPriorityStr))
                    apEntity.setInstrmtPriority(CommonEnum.InstrmtPriorityEnum.MonitoringInstrmt.getIndex());
                else if (CommonEnum.InstrmtPriorityEnum.ControlInstrmt.getName().equals(instrmtPriorityStr))
                    apEntity.setInstrmtPriority(CommonEnum.InstrmtPriorityEnum.ControlInstrmt.getIndex());
                else if (CommonEnum.InstrmtPriorityEnum.Other.getName().equals(instrmtPriorityStr))
                    apEntity.setInstrmtPriority(CommonEnum.InstrmtPriorityEnum.Other.getIndex());
                else if (CommonEnum.InstrmtPriorityEnum.NoLevel.getName().equals(instrmtPriorityStr))
                    apEntity.setInstrmtPriority(null);
                else
                    return "sheet-1 行" + (i + 1) + "仪表优先级格式不正确！";
            }
            //endinstrmtPriorityStr

            //region 工艺卡片值
            String cardValueStr = itemContent[16];
            if (StringUtils.isNotEmpty(cardValueStr)) {
                if (cardValueStr.contains("～")) {
                    String[] cardValues = cardValueStr.split("～");
                    //下限值
                    String validMess0 = numberValidation(cardValues[0], 20, 5);
                    //上限值
                    String validMess1 = numberValidation(cardValues[1], 20, 5);
                    if (StringUtils.isEmpty(validMess0) && StringUtils.isEmpty(validMess1)) {
                        if (!(Double.valueOf(cardValues[1]) > Double.valueOf(cardValues[0])))
                            return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                        apEntity.setCraftDownLimitValue(Double.valueOf(cardValues[0]));
                        apEntity.setCraftDownLimitInclude(CommonEnum.CraftDownLimitEnum.YES.getIndex());

                        apEntity.setCraftUpLimitValue(Double.valueOf(cardValues[1]));
                        apEntity.setCraftUpLimitInclude(CommonEnum.CraftUpLimitEnum.YES.getIndex());
                    } else {
                        return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                    }
                } else if (cardValueStr.contains("≯")) {
                    if (cardValueStr.startsWith("≯")) {
                        String[] cardValues = cardValueStr.split("≯");

                        //上限值
                        String validMess = numberValidation(cardValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setCraftUpLimitValue(Double.valueOf(cardValues[1]));
                            apEntity.setCraftUpLimitInclude(CommonEnum.CraftUpLimitEnum.YES.getIndex());
                        } else {
                            return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                        }
                    } else
                        return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                } else if (cardValueStr.contains("≮")) {
                    if (cardValueStr.startsWith("≮")) {
                        String[] cardValues = cardValueStr.split("≮");

                        //下限值
                        String validMess = numberValidation(cardValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setCraftDownLimitValue(Double.valueOf(cardValues[1]));
                            apEntity.setCraftDownLimitInclude(CommonEnum.CraftDownLimitEnum.YES.getIndex());
                        } else {
                            return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                        }
                    } else
                        return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                } else if (cardValueStr.contains("＜")) {
                    if (cardValueStr.startsWith("＜")) {
                        String[] cardValues = cardValueStr.split("＜");
                        if (cardValues[1].startsWith("±")) {
                            String[] cardValues2 = cardValues[1].split("±");
                            String validMess = numberValidation(cardValues2[1], 20, 5);
                            if (StringUtils.isEmpty(validMess)) {
                                //下限值
                                apEntity.setCraftDownLimitValue(Double.valueOf("-" + cardValues2[1]));
                                apEntity.setCraftDownLimitInclude(CommonEnum.CraftDownLimitEnum.NO.getIndex());
                                //上限值
                                apEntity.setCraftUpLimitValue(Double.valueOf(cardValues2[1]));
                                apEntity.setCraftUpLimitInclude(CommonEnum.CraftUpLimitEnum.NO.getIndex());
                            } else {
                                return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                            }
                        } else {
                            //上限值
                            String validMess = numberValidation(cardValues[1], 20, 5);
                            if (StringUtils.isEmpty(validMess)) {
                                apEntity.setCraftUpLimitValue(Double.valueOf(cardValues[1]));
                                apEntity.setCraftUpLimitInclude(CommonEnum.CraftUpLimitEnum.NO.getIndex());
                            } else {
                                return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                            }
                        }
                    } else
                        return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                } else if (cardValueStr.contains("≤")) {
                    if (cardValueStr.startsWith("≤")) {
                        String[] cardValues = cardValueStr.split("≤");
                        //上限值
                        String validMess = numberValidation(cardValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setCraftUpLimitValue(Double.valueOf(cardValues[1]));
                            apEntity.setCraftUpLimitInclude(CommonEnum.CraftUpLimitEnum.YES.getIndex());
                        } else {
                            return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                        }
                    } else
                        return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                } else if (cardValueStr.contains("＞")) {
                    if (cardValueStr.startsWith("＞")) {
                        String[] cardValues = cardValueStr.split("＞");

                        //下限值
                        String validMess = numberValidation(cardValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setCraftDownLimitValue(Double.valueOf(cardValues[1]));
                            apEntity.setCraftDownLimitInclude(CommonEnum.CraftDownLimitEnum.NO.getIndex());
                        } else {
                            return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                        }
                    } else
                        return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                } else if (cardValueStr.contains("≥")) {
                    if (cardValueStr.startsWith("≥")) {
                        String[] cardValues = cardValueStr.split("≥");

                        //下限值
                        String validMess = numberValidation(cardValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setCraftDownLimitValue(Double.valueOf(cardValues[1]));
                            apEntity.setCraftDownLimitInclude(CommonEnum.CraftDownLimitEnum.YES.getIndex());
                        } else {
                            return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                        }
                    } else
                        return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";
                } else
                    return "sheet-1 行" + (i + 1) + "工艺卡片值格式不正确！";

                //region比较工艺卡片上下限值
                if (apEntity.getCraftUpLimitValue() != null && apEntity.getCraftDownLimitValue() != null) {
                    if (apEntity.getCraftUpLimitValue() < apEntity.getCraftDownLimitValue()) {
                        return "sheet-1 行" + (i + 1) + "工艺卡片上限值须大于等于工艺卡片下限值！";
                    }
                }
                //endregion
            }
            //endregion

            //interlockValueStr 联锁值
            String interlockValueStr = itemContent[17];
            if (StringUtils.isNotEmpty(interlockValueStr)) {
                if (interlockValueStr.contains("≯")) {
                    if (interlockValueStr.startsWith("≯")) {
                        String[] interlockValues = interlockValueStr.split("≯");

                        //下限值
                        String validMess = numberValidation(interlockValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setInterlockDownLimitValue(Double.valueOf(interlockValues[1]));
                            apEntity.setInterlockDownLimitInclude(CommonEnum.CraftInterlockDownLimitEnum.NO.getIndex());
                        } else {
                            return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                        }
                    } else
                        return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                } else if (interlockValueStr.contains("≮")) {
                    if (interlockValueStr.startsWith("≮")) {
                        String[] interlockValues = interlockValueStr.split("≮");

                        //上限值
                        String validMess = numberValidation(interlockValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setInterlockUpLimitValue(Double.valueOf(interlockValues[1]));
                            apEntity.setInterlockUpLimitInclude(CommonEnum.CraftInterlockUpLimitEnum.NO.getIndex());
                        } else {
                            return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                        }
                    } else
                        return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                } else if (interlockValueStr.contains("＜")) {
                    if (interlockValueStr.startsWith("＜")) {
                        String[] interlockValues = interlockValueStr.split("＜");

                        //下限值
                        String validMess = numberValidation(interlockValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setInterlockDownLimitValue(Double.valueOf(interlockValues[1]));
                            apEntity.setInterlockDownLimitInclude(CommonEnum.CraftInterlockDownLimitEnum.YES.getIndex());
                        } else {
                            return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                        }
                    } else
                        return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                } else if (interlockValueStr.contains("≤")) {
                    if (interlockValueStr.startsWith("≤")) {
                        String[] interlockValues = interlockValueStr.split("≤");

                        //下限值
                        String validMess = numberValidation(interlockValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setInterlockDownLimitValue(Double.valueOf(interlockValues[1]));
                            apEntity.setInterlockDownLimitInclude(CommonEnum.CraftInterlockDownLimitEnum.NO.getIndex());
                        } else {
                            return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                        }
                    } else
                        return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                } else if (interlockValueStr.contains("＞")) {
                    if (interlockValueStr.startsWith("＞")) {
                        String[] interlockValues = interlockValueStr.split("＞");
                        if (interlockValues[1].startsWith("±")) {
                            String[] interlockValues2 = interlockValues[1].split("±");
                            String validMess = numberValidation(interlockValues2[1], 20, 5);
                            if (StringUtils.isEmpty(validMess)) {
                                //下限值
                                apEntity.setInterlockDownLimitValue(Double.valueOf("-" + interlockValues2[1]));
                                apEntity.setInterlockDownLimitInclude(CommonEnum.CraftInterlockDownLimitEnum.YES.getIndex());
                                //上限值
                                apEntity.setInterlockUpLimitValue(Double.valueOf(interlockValues2[1]));
                                apEntity.setInterlockUpLimitInclude(CommonEnum.CraftInterlockUpLimitEnum.YES.getIndex());
                            } else {
                                return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                            }
                        } else {
                            //上限值
                            String validMess = numberValidation(interlockValues[1], 20, 5);
                            if (StringUtils.isEmpty(validMess)) {
                                apEntity.setInterlockUpLimitValue(Double.valueOf(interlockValues[1]));
                                apEntity.setInterlockUpLimitInclude(CommonEnum.CraftInterlockUpLimitEnum.YES.getIndex());
                            } else {
                                return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                            }
                        }
                    } else
                        return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                } else if (interlockValueStr.contains("≥")) {
                    if (interlockValueStr.startsWith("≥")) {
                        String[] interlockValues = interlockValueStr.split("≥");
                        if (interlockValues[1].startsWith("±")) {
                            String[] interlockValues2 = interlockValues[1].split("±");
                            String validMess = numberValidation(interlockValues2[1], 20, 5);
                            if (StringUtils.isEmpty(validMess)) {
                                //下限值
                                apEntity.setInterlockDownLimitValue(Double.valueOf("-" + interlockValues2[1]));
                                apEntity.setInterlockDownLimitInclude(CommonEnum.CraftInterlockDownLimitEnum.NO.getIndex());
                                //上限值
                                apEntity.setInterlockUpLimitValue(Double.valueOf(interlockValues2[1]));
                                apEntity.setInterlockUpLimitInclude(CommonEnum.CraftInterlockUpLimitEnum.NO.getIndex());
                            } else {
                                return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                            }
                        } else {
                            //上限值
                            String validMess = numberValidation(interlockValues[1], 20, 5);
                            if (StringUtils.isEmpty(validMess)) {
                                apEntity.setInterlockUpLimitValue(Double.valueOf(interlockValues[1]));
                                apEntity.setInterlockUpLimitInclude(CommonEnum.CraftInterlockUpLimitEnum.NO.getIndex());
                            } else {
                                return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                            }
                        }
                    } else
                        return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
                } else
                    return "sheet-1 行" + (i + 1) + "联锁值格式不正确！";
            }
            //endregion
            //end

            //region 是否启用
            if (itemContent[18].isEmpty()) {
                return "sheet-1 行" + (i + 1) + "是否启用不能为空！请选择是否启用";
            }
            DictionaryEntity yonDicEntity = yonList.stream().filter(u -> u.getValue()
                            .equals(itemContent[18]))
                    .findFirst().orElse(null);

            if (yonDicEntity == null) {
                return "sheet-1 行" + (i + 1) + "是否启用不存在！";
            }
            apEntity.setInUse(Integer.valueOf(yonDicEntity.getKey().toString()));
            //endregion

            //region 排序
            if (itemContent[19].isEmpty()) {
                return "sheet-1 行" + (i + 1) + "排序不能为空！";
            }
            String validMess = numberValidation(itemContent[19], -1, 0);
            if (!StringUtils.isEmpty(validMess)) {
                return "sheet-1 行" + (i + 1) + "排序不合法！";
            }
            if (Integer.valueOf(itemContent[19]) < 0) {
                return "sheet-1 行" + (i + 1) + "排序不合法！";
            }
            try {
                apEntity.setSortNum(Integer.valueOf(itemContent[19]));
            } catch (Exception e) {
                return "sheet-1 行" + (i + 1) + "排序不合法！为正整数";
            }
            //endregion

            //region 描述
            if (!itemContent[20].isEmpty()) {
                apEntity.setDes(itemContent[20]);
            }
            //endregion

            //endregion
            apEntityList.add(apEntity);
        }

        return "";
    }

    /**
     * 抽取sheet2数据
     *
     * @param excelContent excel导入内容
     * @param apEntityList 报警点实体集合
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-16
     */
    @SuppressWarnings("unchecked")
    private String sheet2Verify(List<String[]> excelContent, List<AlarmPointEntity> apEntityList) throws Exception {
        //没有可导入的数据！
        if (excelContent.size() < 2) {
            return "";
        }

        //region 校验标题
        String titles = "装置、生产单元、位号、仪表优先级、工艺卡片值、联锁值";
        String[] titlesArr = titles.split("、");
        for (int i = 0; i < titlesArr.length; i++) {
            if (!titlesArr[i].equals(excelContent.get(0)[i])) {
                return "sheet-2 列" + titlesArr[i] + "列名不合法！";
            }
        }
        //endregion

        //region 数据校验枚举值、数据

        //获取装置数据
        if (unitList == null) {
//            unitList = unitRepository.getUnit(null);
            unitList = basicDataService.getUnitList(false);
            if (unitList == null || unitList.isEmpty()) {
                throw new Exception("装置表没有数据");
            }
        }

        //获取生产单元数据
        if (prdtCellEntityList == null) {
            List<PrdtCell> prdtCellList = prdtCellRepository.getPrdtCell(null);
            if (prdtCellList == null || prdtCellList.isEmpty()) {
                throw new Exception("生产单元表没有数据");
            }
            prdtCellList = prdtCellList.stream().filter(x -> x.getInUse().equals(CommonEnum.InUseEnum.Yes.getIndex())).collect(Collectors.toList());
            prdtCellEntityList = ObjectConverter.listConverter(prdtCellList, PrdtCellEntity.class);
        }
        //endregion

        for (int i = 1; i < excelContent.size(); i++) {
            AlarmPointEntity apEntity = new AlarmPointEntity();
            apEntity.setVirtualFlag(0);
            String[] itemContent = excelContent.get(i);
            for (int j = 0; j < itemContent.length; j++) {
                itemContent[j] = itemContent[j].replace("　", "").trim();
            }
            //region 检查数据必须录入和数据格式

            String instrmtPriorityStr = itemContent[3];
            String cardValueStr = itemContent[4];
            String interlockValueStr = itemContent[5];

            //region 装置
            if (itemContent[0].isEmpty()) {
                return "sheet-2 行" + (i + 1) + "装置不能为空！";
            }
            UnitEntity unit = unitList.stream().filter(u -> u.getSname()
                            .equals(itemContent[0]))
                    .findFirst().orElse(null);

            if (unit == null) {
                return "sheet-2 行" + (i + 1) + "装置不存在！";
            }
            apEntity.setUnitId(unit.getStdCode());
            //endregion

            //region 生产单元
            if (itemContent[1].isEmpty()) {
                return "sheet-2 行" + (i + 1) + "生产单元不能为空！";
            }
            PrdtCellEntity pcEntity = prdtCellEntityList.stream().filter(u -> u.getUnitId().equals(apEntity.getUnitId()) && u.getSname()
                            .equals(itemContent[1]))
                    .findFirst().orElse(null);

            if (pcEntity == null) {
                return "sheet-2 行" + (i + 1) + "该装置下生产单元不存在！";
            }
            apEntity.setPrdtCellId(pcEntity.getPrdtCellId());
            //endregion

            //region 位号
            if (itemContent[2].isEmpty()) {
                return "sheet-2 行" + (i + 1) + "位号不能为空！";
            }
            AlarmPointEntity ape = apEntityList.stream().filter(x -> x.getPrdtCellId().equals(apEntity.getPrdtCellId()) && x.getTag().equals(itemContent[2])).findFirst().orElse(null);
            if (ape != null) {
                return "sheet-2 行" + (i + 1) + "位号重复！";
            }

            apEntity.setTag(itemContent[2]);

            AlarmPoint alarmPoint = ObjectConverter.entityConverter(apEntity, AlarmPoint.class);

            // 数据校验
            CommonResult commonResult = alarmPointImportValidation(alarmPoint);
            if (commonResult.getResult() == null) {
                return "sheet-2 行" + (i + 1) + "位号不存在！";
            }
            //endregion

            //region 仪表优先级
            if (StringUtils.isNotEmpty(instrmtPriorityStr)) {
                if (CommonEnum.InstrmtPriorityEnum.MonitoringInstrmt.getName().equals(instrmtPriorityStr))
                    apEntity.setInstrmtPriority(CommonEnum.InstrmtPriorityEnum.MonitoringInstrmt.getIndex());
                else if (CommonEnum.InstrmtPriorityEnum.ControlInstrmt.getName().equals(instrmtPriorityStr))
                    apEntity.setInstrmtPriority(CommonEnum.InstrmtPriorityEnum.ControlInstrmt.getIndex());
                else if (CommonEnum.InstrmtPriorityEnum.Other.getName().equals(instrmtPriorityStr))
                    apEntity.setInstrmtPriority(CommonEnum.InstrmtPriorityEnum.Other.getIndex());
                else
                    return "sheet-2 行" + (i + 1) + "仪表优先级格式不正确！";
            }
            //endregion

            //region 工艺卡片值
            if (StringUtils.isNotEmpty(cardValueStr)) {
                if (cardValueStr.contains("～")) {
                    String[] cardValues = cardValueStr.split("～");
                    //下限值
                    String validMess0 = numberValidation(cardValues[0], 20, 5);
                    //上限值
                    String validMess1 = numberValidation(cardValues[1], 20, 5);
                    if (StringUtils.isEmpty(validMess0) && StringUtils.isEmpty(validMess1)) {
                        if (!(Double.valueOf(cardValues[1]) > Double.valueOf(cardValues[0])))
                            return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                        apEntity.setCraftDownLimitValue(Double.valueOf(cardValues[0]));
                        apEntity.setCraftDownLimitInclude(CommonEnum.CraftDownLimitEnum.YES.getIndex());

                        apEntity.setCraftUpLimitValue(Double.valueOf(cardValues[1]));
                        apEntity.setCraftUpLimitInclude(CommonEnum.CraftUpLimitEnum.YES.getIndex());
                    } else {
                        return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                    }
                } else if (cardValueStr.contains("≯")) {
                    if (cardValueStr.startsWith("≯")) {
                        String[] cardValues = cardValueStr.split("≯");

                        //上限值
                        String validMess = numberValidation(cardValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setCraftUpLimitValue(Double.valueOf(cardValues[1]));
                            apEntity.setCraftUpLimitInclude(CommonEnum.CraftUpLimitEnum.YES.getIndex());
                        } else {
                            return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                        }
                    } else
                        return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                } else if (cardValueStr.contains("≮")) {
                    if (cardValueStr.startsWith("≮")) {
                        String[] cardValues = cardValueStr.split("≮");

                        //下限值
                        String validMess = numberValidation(cardValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setCraftDownLimitValue(Double.valueOf(cardValues[1]));
                            apEntity.setCraftDownLimitInclude(CommonEnum.CraftDownLimitEnum.YES.getIndex());
                        } else {
                            return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                        }
                    } else
                        return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                } else if (cardValueStr.contains("＜")) {
                    if (cardValueStr.startsWith("＜")) {
                        String[] cardValues = cardValueStr.split("＜");
                        if (cardValues[1].startsWith("±")) {
                            String[] cardValues2 = cardValues[1].split("±");
                            String validMess = numberValidation(cardValues2[1], 20, 5);
                            if (StringUtils.isEmpty(validMess)) {
                                //下限值
                                apEntity.setCraftDownLimitValue(Double.valueOf("-" + cardValues2[1]));
                                apEntity.setCraftDownLimitInclude(CommonEnum.CraftDownLimitEnum.NO.getIndex());
                                //上限值
                                apEntity.setCraftUpLimitValue(Double.valueOf(cardValues2[1]));
                                apEntity.setCraftUpLimitInclude(CommonEnum.CraftUpLimitEnum.NO.getIndex());
                            } else {
                                return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                            }
                        } else {
                            //上限值
                            String validMess = numberValidation(cardValues[1], 20, 5);
                            if (StringUtils.isEmpty(validMess)) {
                                apEntity.setCraftUpLimitValue(Double.valueOf(cardValues[1]));
                                apEntity.setCraftUpLimitInclude(CommonEnum.CraftUpLimitEnum.NO.getIndex());
                            } else {
                                return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                            }
                        }
                    } else
                        return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                } else if (cardValueStr.contains("≤")) {
                    if (cardValueStr.startsWith("≤")) {
                        String[] cardValues = cardValueStr.split("≤");
                        //上限值
                        String validMess = numberValidation(cardValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setCraftUpLimitValue(Double.valueOf(cardValues[1]));
                            apEntity.setCraftUpLimitInclude(CommonEnum.CraftUpLimitEnum.YES.getIndex());
                        } else {
                            return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                        }
                    } else
                        return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                } else if (cardValueStr.contains("＞")) {
                    if (cardValueStr.startsWith("＞")) {
                        String[] cardValues = cardValueStr.split("＞");

                        //下限值
                        String validMess = numberValidation(cardValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setCraftDownLimitValue(Double.valueOf(cardValues[1]));
                            apEntity.setCraftDownLimitInclude(CommonEnum.CraftDownLimitEnum.NO.getIndex());
                        } else {
                            return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                        }
                    } else
                        return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                } else if (cardValueStr.contains("≥")) {
                    if (cardValueStr.startsWith("≥")) {
                        String[] cardValues = cardValueStr.split("≥");

                        //下限值
                        String validMess = numberValidation(cardValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setCraftDownLimitValue(Double.valueOf(cardValues[1]));
                            apEntity.setCraftDownLimitInclude(CommonEnum.CraftDownLimitEnum.YES.getIndex());
                        } else {
                            return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                        }
                    } else
                        return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";
                } else
                    return "sheet-2 行" + (i + 1) + "工艺卡片值格式不正确！";

                //region比较工艺卡片上下限值
                if (apEntity.getCraftUpLimitValue() != null && apEntity.getCraftDownLimitValue() != null) {
                    if (apEntity.getCraftUpLimitValue() < apEntity.getCraftDownLimitValue()) {
                        return "sheet-2 行" + (i + 1) + "工艺卡片上限值须大于等于工艺卡片下限值！";
                    }
                }
                //endregion
            }
            //endregion

            //region 联锁值

            if (StringUtils.isNotEmpty(interlockValueStr)) {
                if (interlockValueStr.contains("≯")) {
                    if (interlockValueStr.startsWith("≯")) {
                        String[] interlockValues = interlockValueStr.split("≯");

                        //下限值
                        String validMess = numberValidation(interlockValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setInterlockDownLimitValue(Double.valueOf(interlockValues[1]));
                            apEntity.setInterlockDownLimitInclude(CommonEnum.CraftInterlockDownLimitEnum.NO.getIndex());
                        } else {
                            return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                        }
                    } else
                        return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                } else if (interlockValueStr.contains("≮")) {
                    if (interlockValueStr.startsWith("≮")) {
                        String[] interlockValues = interlockValueStr.split("≮");

                        //上限值
                        String validMess = numberValidation(interlockValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setInterlockUpLimitValue(Double.valueOf(interlockValues[1]));
                            apEntity.setInterlockUpLimitInclude(CommonEnum.CraftInterlockUpLimitEnum.NO.getIndex());
                        } else {
                            return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                        }
                    } else
                        return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                } else if (interlockValueStr.contains("＜")) {
                    if (interlockValueStr.startsWith("＜")) {
                        String[] interlockValues = interlockValueStr.split("＜");

                        //下限值
                        String validMess = numberValidation(interlockValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setInterlockDownLimitValue(Double.valueOf(interlockValues[1]));
                            apEntity.setInterlockDownLimitInclude(CommonEnum.CraftInterlockDownLimitEnum.YES.getIndex());
                        } else {
                            return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                        }
                    } else
                        return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                } else if (interlockValueStr.contains("≤")) {
                    if (interlockValueStr.startsWith("≤")) {
                        String[] interlockValues = interlockValueStr.split("≤");

                        //下限值
                        String validMess = numberValidation(interlockValues[1], 20, 5);
                        if (StringUtils.isEmpty(validMess)) {
                            apEntity.setInterlockDownLimitValue(Double.valueOf(interlockValues[1]));
                            apEntity.setInterlockDownLimitInclude(CommonEnum.CraftInterlockDownLimitEnum.NO.getIndex());
                        } else {
                            return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                        }
                    } else
                        return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                } else if (interlockValueStr.contains("＞")) {
                    if (interlockValueStr.startsWith("＞")) {
                        String[] interlockValues = interlockValueStr.split("＞");
                        if (interlockValues[1].startsWith("±")) {
                            String[] interlockValues2 = interlockValues[1].split("±");
                            String validMess = numberValidation(interlockValues2[1], 20, 5);
                            if (StringUtils.isEmpty(validMess)) {
                                //下限值
                                apEntity.setInterlockDownLimitValue(Double.valueOf("-" + interlockValues2[1]));
                                apEntity.setInterlockDownLimitInclude(CommonEnum.CraftInterlockDownLimitEnum.YES.getIndex());
                                //上限值
                                apEntity.setInterlockUpLimitValue(Double.valueOf(interlockValues2[1]));
                                apEntity.setInterlockUpLimitInclude(CommonEnum.CraftInterlockUpLimitEnum.YES.getIndex());
                            } else {
                                return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                            }
                        } else {
                            //上限值
                            String validMess = numberValidation(interlockValues[1], 20, 5);
                            if (StringUtils.isEmpty(validMess)) {
                                apEntity.setInterlockUpLimitValue(Double.valueOf(interlockValues[1]));
                                apEntity.setInterlockUpLimitInclude(CommonEnum.CraftInterlockUpLimitEnum.YES.getIndex());
                            } else {
                                return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                            }
                        }
                    } else
                        return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                } else if (interlockValueStr.contains("≥")) {
                    if (interlockValueStr.startsWith("≥")) {
                        String[] interlockValues = interlockValueStr.split("≥");
                        if (interlockValues[1].startsWith("±")) {
                            String[] interlockValues2 = interlockValues[1].split("±");
                            String validMess = numberValidation(interlockValues2[1], 20, 5);
                            if (StringUtils.isEmpty(validMess)) {
                                //下限值
                                apEntity.setInterlockDownLimitValue(Double.valueOf("-" + interlockValues2[1]));
                                apEntity.setInterlockDownLimitInclude(CommonEnum.CraftInterlockDownLimitEnum.NO.getIndex());
                                //上限值
                                apEntity.setInterlockUpLimitValue(Double.valueOf(interlockValues2[1]));
                                apEntity.setInterlockUpLimitInclude(CommonEnum.CraftInterlockUpLimitEnum.NO.getIndex());
                            } else {
                                return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                            }
                        } else {
                            //上限值
                            String validMess = numberValidation(interlockValues[1], 20, 5);
                            if (StringUtils.isEmpty(validMess)) {
                                apEntity.setInterlockUpLimitValue(Double.valueOf(interlockValues[1]));
                                apEntity.setInterlockUpLimitInclude(CommonEnum.CraftInterlockUpLimitEnum.NO.getIndex());
                            } else {
                                return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                            }
                        }
                    } else
                        return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
                } else
                    return "sheet-2 行" + (i + 1) + "联锁值格式不正确！";
            }
            //endregion

            //endregion
            apEntityList.add(apEntity);
        }

        return "";
    }

    /**
     * 校验报警点高报、高高报、低报、低低报
     *
     * @param entity 报警点实体
     * @return
     * <AUTHOR> 2017-11-16
     */
    @SuppressWarnings("unused")
    private String alarmPointValidation(AlarmPointEntity entity) {
        Double hh = entity.getAlarmPointHH();
        Double hi = entity.getAlarmPointHI();
        Double lo = entity.getAlarmPointLO();
        Double ll = entity.getAlarmPointLL();
        String message = "";
        if (hh != null) {
            if (hi != null && hh < hi) {
                message = "高高报值须大于等于高报值！";
                lo = ll = hi = null;
            }
            if (lo != null && hi != null && hi <= lo) {
                message = "高报值须大于低报值！";
                lo = ll = hi = null;
            } else if (ll != null && hi != null && hi <= ll) {
                message = "高报值须大于低低报值！";
                lo = ll = hi = null;
            }
            if (lo != null && hh <= lo) {
                message = "高高报值须大于低报值！";
                lo = ll = hi = null;
            }
            if (ll != null && lo != null && lo < ll) {
                message = "低报值须大于等于低低报值！";
                lo = ll = hi = null;
            }
            if (ll != null && hh <= ll) {
                message = "高高报值须大于低低报值！";
                lo = ll = hi = null;
                if (lo != null && lo < ll) {
                    message = "低报值须大于等于低低报值！";
                    lo = ll = hi = null;
                }
            }
        } else if (hi != null) {
            if (lo != null && hi <= lo) {
                message = "高报值须大于低报值！";
                lo = ll = null;
            }
            if (ll != null && hi <= ll) {
                message = "高报值须大于低低报值！";
                hh = hi = lo = ll = null;
            }
        } else if (lo != null && ll != null && lo < ll) {
            message = "低报值须大于等于低低报值！";
        }
        return message;
    }

    /**
     * 校验导入报警点数据
     *
     * @param entity 报警点实体
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-16
     */
    private CommonResult alarmPointImportValidation(AlarmPoint entity) throws Exception {
        CommonResult commonResult = new CommonResult();
        if (commonResult.getIsSuccess() == true) {
            // 调用DAL与数据库相关的校验
            commonResult = repo.alarmPointImportValidation(entity);
        }
        return commonResult;
    }

    /**
     * 校验数值是否符合要求
     *
     * @param num           校验值
     * @param length        值长度
     * @param decimalPlaces 保留小数位数
     * @return 返回true 或者false
     * <AUTHOR> 2017-11-16
     */
    private String numberValidation(Object num, int length, int decimalPlaces) {
        try {
            Double.valueOf(num.toString());
        } catch (Exception ex) {
            return "格式不正确";
        }
        String numStr = num.toString();
        String[] numArr = numStr.split("\\.");
        if (length != -1 && numStr.length() > length) {
            return "最大长度为20位，小数位不超过5位！";
        }
        if (numArr.length > 1 && numArr[1].length() > decimalPlaces) {
            return "最大长度为20位，小数位不超过5位！";
        }
        return "";
    }

    //endregion

    //endregion

    /**
     * 获取是否发送报警短信列表（1是，0否）
     *
     * @param isAll
     * @return
     */
    @Override
    public List<DictionaryEntity> getAlarmPriorityList(boolean isAll) {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            if (isAll) {
                dictionaryEntityArrayList.add(new DictionaryEntity(-1, "全部"));
            }
            for (CommonEnum.InSendMsgEnum inSendMsgEnum : CommonEnum.InSendMsgEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(inSendMsgEnum.getIndex(), inSendMsgEnum.getName()));
            }
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 主页面查询
     *
     * @param unitCodes
     * @param prdtCellIds 生产单元id
     * @param tag         位号
     * @param inSendMsg   是否发短信
     * @param page        分页
     * @return
     * @throws Exception
     */
    @Override
    public PaginationBean<AlarmPointEntity> getAlarmMsgConfig(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inSendMsg, Pagination page) throws Exception {
        if (unitCodes == null || unitCodes.length == 0) {
            List<UnitEntity> unitList = basicDataService.getUnitList(true);
            unitCodes = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
        }
        PaginationBean<AlarmPoint> listAlarmPoint = repo.getAlarmMsgConfig(unitCodes, prdtCellIds, tag, inSendMsg, page);
        PaginationBean<AlarmPointEntity> returnAlarmPoint = new PaginationBean<AlarmPointEntity>(page, listAlarmPoint.getTotal());
        returnAlarmPoint.setPageList(ObjectConverter.listConverter(listAlarmPoint.getPageList(), AlarmPointEntity.class));
        for (AlarmPointEntity alarmPointEntity : returnAlarmPoint.getPageList()) {
            alarmPointEntity.setInSendMsgShow(CommonEnum.InSendMsgEnum.getName(alarmPointEntity.getInSendMsg()));
        }
        List<UnitEntity> unitList = new ArrayList<>();
        if (listAlarmPoint != null && listAlarmPoint.getTotal() < 1000) {
            unitList = basicDataService.getUnitListByIds(listAlarmPoint.getPageList().stream().map(x -> x.getPrdtCell().getUnitId()).toArray(String[]::new), false);
        } else {
            unitList = basicDataService.getUnitList(false);
        }

        for (int i = 0; i < returnAlarmPoint.getPageList().size(); i++) {
            AlarmPointEntity ape = returnAlarmPoint.getPageList().get(i);

            AlarmPoint ap = listAlarmPoint.getPageList().get(i);

            Long alarmPointId = ap.getAlarmPointId();
            Long[] alarmpointIds = new Long[]{alarmPointId};
            List<SendMsgAlarmFlagConf> sendMsgAlarmFlagConfList = sendMsgAlarmFlagConfRepository.getSendMsgAlarmFlagConfByAlarmPointId(alarmpointIds);
            List<AlarmMobileConf> alarmMobileConfList = alarmMobileConfRepository.getAlarmMobileConfPointId(alarmpointIds);
            StringBuilder mobileBook = new StringBuilder();//电话本
            StringBuilder mobileListId = new StringBuilder();
            if (alarmMobileConfList.size() > 0) {
                for (int j = 0; j < alarmMobileConfList.size(); j++) {
                    AlarmMobileConf alarmMobileConf = alarmMobileConfList.get(j);
                    String name = alarmMobileConf.getMobilelist().getName();
                    String mobile = alarmMobileConf.getMobilelist().getMobile();
                    Long mobileId = alarmMobileConf.getMobileListId();
                    if (alarmMobileConfList.size() - 1 == j) {
                        mobileBook.append(name + "(" + mobile + ")");
                        mobileListId.append(mobileId);
                    } else {
                        mobileBook.append(name + "(" + mobile + "),");
                        mobileListId.append(mobileId + ",");
                    }
                }
            }
            StringBuilder alarmflagName = new StringBuilder();
            if (sendMsgAlarmFlagConfList.size() > 0) {
                for (int j = 0; j < sendMsgAlarmFlagConfList.size(); j++) {
                    SendMsgAlarmFlagConf sendMsgAlarmFlagConf = sendMsgAlarmFlagConfList.get(j);
                    if (j == sendMsgAlarmFlagConfList.size() - 1) {
                        alarmflagName.append(sendMsgAlarmFlagConf.getAlarmFlag().getName());
                    } else {
                        alarmflagName.append(sendMsgAlarmFlagConf.getAlarmFlag().getName() + ",");
                    }
                }
            }

            ape.setMobileBookId(mobileListId.toString());
            ape.setMobileBook(mobileBook.toString());
            ape.setAlarmflagName(alarmflagName.toString());

            ape.setUnitId(ap.getPrdtCell().getUnitId());
            UnitEntity ue = unitList.stream().filter(ul -> ap.getPrdtCell().getUnitId().equals(ul.getStdCode())).findFirst().orElse(new UnitEntity());
            ape.setUnitSname(ue.getSname());
            ape.setPrdtCellSname(ap.getPrdtCell().getSname());
        }
        return returnAlarmPoint;
    }

    /**
     * 通过报警点IDs 获取信息列表
     *
     * @param alarmPointIds
     * @return
     * @throws Exception
     */
    @Override
    public List<AlarmPointEntity> getAlarmMsgConfigByAlarmPointIds(Long[] alarmPointIds) throws Exception {
        List<AlarmPoint> alarmPointList = repo.getAlarmMsgConfigByAlarmPointIds(alarmPointIds);
        List<AlarmPointEntity> alarmPointEntityList = ObjectConverter.listConverter(alarmPointList, AlarmPointEntity.class);
        for (AlarmPointEntity alarmPointEntity : alarmPointEntityList) {
            alarmPointEntity.setInSendMsgShow(CommonEnum.InSendMsgEnum.getName(alarmPointEntity.getInSendMsg()));
        }


        List<UnitEntity> unitList = new ArrayList<>();

        unitList = basicDataService.getUnitList(false);

        int i = 0;
        for (AlarmPointEntity ape : alarmPointEntityList) {
            Long[] Ids = new Long[]{ape.getAlarmPointId()};
            List<SendMsgAlarmFlagConf> sendMsgAlarmFlagConfList = sendMsgAlarmFlagConfRepository.getSendMsgAlarmFlagConfByAlarmPointId(Ids);
            StringBuilder setAlarmflagId = new StringBuilder();
            for (int j = 0; j < sendMsgAlarmFlagConfList.size(); j++) {
                if (j == sendMsgAlarmFlagConfList.size() - 1) {
                    setAlarmflagId.append(sendMsgAlarmFlagConfList.get(j).getAlarmFlagId());
                } else {
                    setAlarmflagId.append(sendMsgAlarmFlagConfList.get(j).getAlarmFlagId() + ",");
                }
            }
            ape.setAlarmflagId(setAlarmflagId.toString());

            List<AlarmMobileConf> alarmMobileConfList = alarmMobileConfRepository.getAlarmMobileConfPointId(Ids);
            StringBuilder mobileBook = new StringBuilder();//电话本
            StringBuilder mobileBooks = new StringBuilder();
            StringBuilder mobileListIds = new StringBuilder();
            if (alarmMobileConfList.size() > 0) {
                for (int j = 0; j < alarmMobileConfList.size(); j++) {
                    AlarmMobileConf alarmMobileConf = alarmMobileConfList.get(j);
                    String name = alarmMobileConf.getMobilelist().getName();
                    String mobile = alarmMobileConf.getMobilelist().getMobile();
                    Long mobileListId = alarmMobileConf.getMobileListId();
                    if (alarmMobileConfList.size() - 1 == j) {
                        mobileBook.append(name + "(" + mobile + ")");
                        mobileListIds.append(mobileListId);
                        mobileBooks.append(name + "(" + mobile + ")");
                    } else {
                        mobileBook.append(name + "(" + mobile + "),");
                        mobileListIds.append(mobileListId + ",");
                        mobileBooks.append(name + "(" + mobile + ")" + "#");
                    }
                }
            }
            ape.setMobileBooks(mobileBooks.toString());
            ape.setMobileBookId(mobileListIds.toString());
            ape.setMobileBook(mobileBook.toString());
            AlarmPoint ap = alarmPointList.get(i);
            i++;
            ape.setUnitId(ap.getPrdtCell().getUnitId());
            UnitEntity ue = unitList.stream().filter(ul -> ap.getPrdtCell().getUnitId().equals(ul.getStdCode())).findFirst().orElse(new UnitEntity());
            ape.setUnitSname(ue.getSname());
            ape.setPrdtCellSname(ap.getPrdtCell().getSname());
        }
        return alarmPointEntityList;
    }

    /**
     * 修改发送信息
     *
     * @param alarmPointIds
     * @param inSendMsg
     * @param mobilePhone
     * @return
     */
    @Override
    @Transactional
    public CommonResult updateAlarmMsgConfig(Long[] alarmPointIds, Integer inSendMsg, String mobilePhone, Long[] MobileBookIds, Integer[] alarmFlagIds) {
        CommonResult commonResult = null;
        try {
            List<AlarmPoint> alarmPointList = repo.getAlarmMsgConfigByAlarmPointIds(alarmPointIds);
            for (AlarmPoint alarmPoint : alarmPointList) {
                alarmPoint.setInSendMsg(inSendMsg);
                alarmPoint.setMobilePhone(mobilePhone);
            }
            for (AlarmPoint alarmPoint : alarmPointList) {
                commonResult = repo.updateAlarmPoint(alarmPoint);
            }
            //（2）根据网格列表的“报警点ID”删除<报警点手机号配置>数据；根据网格列表的“报警点ID”和选择的“电话本ID”新增<报警点手机号配置>数据；
            List<AlarmMobileConf> alarmMobileConfList = alarmMobileConfRepository.getAlarmMobileConfPointId(alarmPointIds);
            if (null != MobileBookIds) {
                alarmMobileConfRepository.deleteAll(alarmMobileConfList);
                List<AlarmMobileConf> alarmMobileConfs = new ArrayList<>();
                for (Long id : alarmPointIds) {
                    for (Long mId : MobileBookIds) {
                        AlarmMobileConf alarmMobileConf = new AlarmMobileConf();
                        alarmMobileConf.setAlarmPointId(id);
                        alarmMobileConf.setMobileListId(mId);
                        alarmMobileConfs.add(alarmMobileConf);
                    }
                }
                alarmMobileConfRepository.saveAll(alarmMobileConfs);
            }

            //（3）根据网格列表的“报警点ID”删除<短信报警标识配置>数据；根据网格列表的“报警点ID”和选择的“报警标识ID”新增<短信报警标识配置>数据；
            List<SendMsgAlarmFlagConf> sendMsgAlarmFlagConfList = sendMsgAlarmFlagConfRepository.getSendMsgAlarmFlagConfByAlarmPointId(alarmPointIds);
            sendMsgAlarmFlagConfRepository.deleteAll(sendMsgAlarmFlagConfList);
            List<SendMsgAlarmFlagConf> sendMsgAlarmFlagConfs = new ArrayList<>();
            if (null != alarmFlagIds) {
                for (Long id : alarmPointIds) {
                    for (Integer aId : alarmFlagIds) {
                        SendMsgAlarmFlagConf sendMsgAlarmFlagConf = new SendMsgAlarmFlagConf();
                        sendMsgAlarmFlagConf.setAlarmPointId(id);
                        sendMsgAlarmFlagConf.setAlarmFlagId(aId);
                        sendMsgAlarmFlagConfs.add(sendMsgAlarmFlagConf);
                    }
                }
            }
            sendMsgAlarmFlagConfRepository.saveAll(sendMsgAlarmFlagConfs);
        } catch (Exception e) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(e.getMessage());
        }
        return commonResult;
    }

    /**
     * 获取报警标识（短信配置）枚举列表
     *
     * @return
     */
    @Override
    public List<DictionaryEntity> getAlarmFlagList() {
        List<DictionaryEntity> dictionaryEntityArrayList = new ArrayList<>();
        try {
            for (CommonEnum.AlarmFlagNameEnum alarmFlagNameEnum : CommonEnum.AlarmFlagNameEnum.values()) {
                dictionaryEntityArrayList.add(new DictionaryEntity(alarmFlagNameEnum.getIndex(), alarmFlagNameEnum.getName()));
            }
        } catch (Exception ex) {
            throw ex;
        }
        return dictionaryEntityArrayList;
    }

    /**
     * 获取电话本列表
     *
     * @param factoryId
     * @param workshopId
     * @param unitId
     * @return
     * @throws Exception
     */
    @Override
    public List<MobileListEntity> getMobileList(Long factoryId, Long workshopId, String unitId, String name, String phoneNum) throws Exception {

        List<MobileList> mobileListList = mobileListRepository.getMobileList(factoryId, workshopId, unitId, name, phoneNum);
        List<MobileListEntity> mobileListEntityList = ObjectConverter.listConverter(mobileListList, MobileListEntity.class);
        List<UnitEntity> unitList = new ArrayList<>();

        unitList = basicDataService.getUnitList(true);
        for (int i = 0; i < mobileListList.size(); i++) {
            MobileList mobileList = mobileListList.get(i);
            MobileListEntity mobileListEntity = mobileListEntityList.get(i);
            mobileListEntity.setFactoryName(null != mobileList.getFactory() ? mobileList.getFactory().getSname() : null);
            mobileListEntity.setWorkshopName(null != mobileList.getWorkshop() ? mobileList.getWorkshop().getSname() : null);
            if (null != mobileList.getUnitCode()) {
                UnitEntity ue = unitList.stream().filter(ul -> mobileList.getUnitCode().equals(ul.getStdCode())).findFirst().orElse(new UnitEntity());
                mobileListEntity.setUnitCodeName(ue.getSname());
            }
        }
        return mobileListEntityList;
    }

    /**
     * 获取装置
     *
     * @param workshopId
     * @param isAll
     * @return
     * @throws Exception
     */
    @Override
    public List<DBUnitEntity> getUnitListByWorkshopId(Long workshopId, boolean isAll) throws Exception {
        Pagination page = new Pagination();
        page.setPageNumber(0);
        page.setPageSize(Integer.MAX_VALUE);
        Long[] workshopIds = null;
        if (null != workshopId) {
            workshopIds = new Long[]{workshopId};
        }
        PaginationBean<Unit> unitList = unitRepository.getUnitList(null, workshopIds, null, null, 1, page);
        List<DBUnitEntity> unitEntities = ObjectConverter.listConverter(unitList.getPageList(), DBUnitEntity.class);
        if (isAll && unitEntities.size() > 1) {
            DBUnitEntity unitEntity = new DBUnitEntity();
            unitEntity.setUnitId(-1L);
            unitEntity.setStdCode("-1");
            unitEntity.setSname("全部");
            unitEntities.add(0, unitEntity);

        }
        return unitEntities;
    }

    @Override
    public CommonResult updateAlarmMsgConfigForInUse(Long[] alarmPointIds, Integer inSendMsg) {
        CommonResult commonResult = null;
        try {
            List<AlarmPoint> alarmPointList = repo.getAlarmMsgConfigByAlarmPointIds(alarmPointIds);
            for (AlarmPoint alarmPoint : alarmPointList) {
                alarmPoint.setInSendMsg(inSendMsg);
            }
            for (AlarmPoint alarmPoint : alarmPointList) {
                commonResult = repo.updateAlarmPoint(alarmPoint);
            }
        } catch (Exception e) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(e.getMessage());
        }
        return commonResult;
    }

    @Override
    public List<AlarmPointEntity> getAlarmPonitListInfo(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inSendMsg) throws Exception {
        if (unitCodes == null || unitCodes.length == 0) {
            List<UnitEntity> unitList = basicDataService.getUnitList(true);
            unitCodes = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
        }
        List<AlarmPoint> listAlarmPoint = repo.findAlarmPonitListByInfo(unitCodes, prdtCellIds, tag, inSendMsg);
        List<AlarmPointEntity> returnAlarmPoint = ObjectConverter.listConverter(listAlarmPoint, AlarmPointEntity.class);

        for (int i = 0; i < returnAlarmPoint.size(); i++) {
            AlarmPointEntity ape = returnAlarmPoint.get(i);
            PrdtCell pc = prdtCellRepository.getSinglePrdtCell(ape.getPrdtCellId());
            Unit u = unitRepository.getUnitByStdCode(pc.getUnitId());
            ape.setUnitSname(u.getSname());
            ape.setPrdtCellSname(pc.getSname());
        }
        return returnAlarmPoint;
    }

    @Override
    public List<AlarmPointEntity> getAlarmPointList(Long[] alarmPointIds) throws Exception {
        List<AlarmPoint> alarmPoint = repo.getAlarmMsgConfigByAlarmPointIds(alarmPointIds);
        List<AlarmPointEntity> ape = ObjectConverter.listConverter(alarmPoint, AlarmPointEntity.class);
        for (int i = 0, j = ape.size(); i < j; i++) {
            PrdtCell pc = prdtCellRepository.getSinglePrdtCell(ape.get(i).getPrdtCellId());
            Unit u = unitRepository.getUnitByStdCode(pc.getUnitId());
            ape.get(i).setUnitSname(u.getSname());
            ape.get(i).setPrdtCellSname(pc.getSname());
        }
        return ape;
    }
}
