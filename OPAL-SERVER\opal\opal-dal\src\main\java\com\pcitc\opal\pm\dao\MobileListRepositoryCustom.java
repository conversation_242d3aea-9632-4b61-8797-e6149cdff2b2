package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.dao.imp.MobileListDTO;
import com.pcitc.opal.pm.pojo.MobileList;

import java.util.List;

public interface MobileListRepositoryCustom {
    List<MobileList> getMobileList(Long factoryIds, Long workshopIds, String unitIds, String name, String phoneNum);

    List<MobileList> getMobileListByIds(List<Long> mobileIds);

    PaginationBean<MobileListDTO> getMobileListPage(Long factoryId, Long workshopId, String unitCode, String name, String mobile, Pagination page);

    List<MobileListDTO> getMobileListExport(Long factoryId, Long workshopId, String unitCode, String name, String mobile);


    CommonResult deleteMobileList(Long[] mobileIds);

    CommonResult addMobileList(MobileList mobileList);

    CommonResult updateMobileList(MobileList mobileList);

    List<String> getDelList();
}
