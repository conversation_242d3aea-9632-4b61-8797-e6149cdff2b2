package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.AlarmPriorityCompEntity;
import org.springframework.stereotype.Service;

/*
 * 报警优先级对照业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmFlagCompService
 * 作       者：zheng.yang
 * 创建时间：2018/03/30
 * 修改编号：1
 * 描       述：报警优先级对照业务逻辑层接口
 */
@Service
public interface AlarmPriorityCompService {
    /**
     * 新增数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompEntity 报警标识对照实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult addPriorityComp(AlarmPriorityCompEntity alarmPriorityCompEntity) throws Exception;

    /**
     * 删除数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompIds 报警标识对照主键Id集合
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult deleteAlarmPriorityComp(Long[] alarmPriorityCompIds) throws Exception;

    /**
     * 更新数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompEntity 报警标识对照实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult updateAlarmPriorityComp(AlarmPriorityCompEntity alarmPriorityCompEntity) throws Exception;

    /**
     * 获取单条数据
     *
      * <AUTHOR> 2018-03-30
     * @param alarmPriorityCompId 报警标识对照ID
     * @return AlarmPriorityCompEntity 报警标识对照实体类
     * @throws Exception
     */
    AlarmPriorityCompEntity getSingleAlarmPriorityComp(Long alarmPriorityCompId) throws Exception;

    /**
     * 获取分页数据
     *
      * <AUTHOR> 2018-03-30
     * @param dCSCodeId DCS编码ID
     * @param prioritySource 源报警优先级
     * @param priority    报警优先级ID
     * @param inUse 是否启用
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmPriorityCompEntity> 翻页对象
     */
    PaginationBean<AlarmPriorityCompEntity> getAlarmPriorityComp(Long dCSCodeId, String prioritySource, Integer priority,Integer inUse, Pagination page) throws  Exception;

}
