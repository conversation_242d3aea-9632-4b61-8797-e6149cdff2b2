package com.pcitc.opal.ad.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 报警事件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_ad_alarmevent")
public class AlarmEventEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "event_id", type = IdType.AUTO)
    private Long eventId;

    @TableField("event_type_id")
    private Long eventTypeId;

    @TableField("alarm_point_id")
    private Long alarmPointId;

    @TableField("alarm_flag_id")
    private Long alarmFlagId;

    /**
     * 发生时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 报警时间
     */
    @TableField("alarm_time")
    private Date alarmTime;

    @TableField("priority")
    private Long priority;

    @TableField("limit_value")
    private Long limitValue;

    @TableField("in_shelved")
    private Long inShelved;

    @TableField("in_suppressed")
    private Long inSuppressed;

    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;

    /**
     * 描述
     */
    @TableField("des")
    private String des;

    /**
     * 先前值
     */
    @TableField("previous_value")
    private String previousValue;

    /**
     * 值
     */
    @TableField("now_value")
    private String nowValue;

    /**
     * 参数
     */
    @TableField("parameter")
    private String parameter;

    /**
     * 装置编码
     */
    @TableField("unit_code")
    private String unitCode;

    @TableField("prdtcell_id")
    private Long prdtcellId;

    /**
     * DCS编码(缓存表)
     */
    @TableField("dcs_code")
    private String dcsCode;

    /**
     * 位号(缓存表)
     */
    @TableField("tag")
    private String tag;

    /**
     * 报警标识(缓存表)
     */
    @TableField("alarm_flag")
    private String alarmFlag;

    /**
     * 优先级(缓存表)
     */
    @TableField("priority_cache")
    private String priorityCache;

    /**
     * 写入时间
     */
    @TableField("write_time")
    private Date writeTime;

    /**
     * 企业ID
     */
    @TableField("company_id")
    private Long companyId;


}
