var addUrl = OPAL.API.pmUrl + '/company';
var getSingleUrl = OPAL.API.pmUrl + '/company';
var pageMode = PageModelEnum.NewAdd;
window.pageLoadMode = PageLoadMode.None;
$(function () {
    var index = parent.layer.getFrameIndex(window.name);//获取子窗口索引
    var page = {
        init: function () {
            this.bindUI();
        },
        bindUI: function () {
            $('#saveAddModal').click(function () {
                page.logic.save();
            });
            $('.closeBtn').click(function () {
                window.pageLoadMode = PageLoadMode.None;
                page.logic.closeLayer(false);
            })
        },
        logic: {
            /**
             * 保存
             */
            save: function () {
                page.logic.formValidate();
                if (!$('#AddOrEditModal').valid()) {
                    return;
                }
                var data = OPAL.form.getETCollectionData('AddOrEditModal');
                //处理提交类型
                var ajaxType = "POST";
                if (pageMode == PageModelEnum.NewAdd) {
                    window.pageLoadMode = PageLoadMode.Reload;
                }
                else if (pageMode == PageModelEnum.Edit) {
                    ajaxType = "PUT";
                    window.pageLoadMode = PageLoadMode.Refresh;
                }
                $.ajax({
                    url: addUrl,
                    async: false,
                    type: ajaxType,
                    data: encodeURI(JSON.stringify(data)),
                    processData: false,
                    contentType: "application/json;charset=utf-8",
                    dataType: "text",
                    success: function (result, XMLHttpRequest) {
                        if (result.indexOf('collection') < 0) {
                            layer.msg("保存成功！",{
                                time: 1000
                            },function() {
                                page.logic.closeLayer(true);
                            });
                        } else {
                            layer.msg(result.collection.error.message)
                        }
                    }, error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化编辑数据
             */
            setData: function (data) {
                pageMode = data.pageMode;
                if (pageMode == PageModelEnum.NewAdd) {
                    $('input[name=inUse]').attr('disabled', 'disabled');
                    return;
                }
                $.ajax({
                    url: getSingleUrl + "/" + data.companyId + "?now=" + Math.random(),
                    type: "get",
                    async: true,
                    dataType: "json",
                    success: function (data) {
                        var entity = $.ET.toObjectArr(data)[0];
                        OPAL.form.setData('AddOrEditModal', entity);
                        
                    },
                    error: function (result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                });
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function (isRefresh) {
                window.parent.pageLoadMode = window.pageLoadMode;
                parent.layer.close(index);
            },
            formValidate: function () {
                OPAL.form.formValidate('AddOrEditModal',{
                    rules: {
                        name: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        sname: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        stdCode: {
                            required: true,
                            rangelength: [0, 100]
                        },
                        sortNum: {
                            required: true,
                            digits: true,
                            min: 0
                        }
                    }
                })
                
            }
        }

    }
    page.init();
    window.page = page;
})