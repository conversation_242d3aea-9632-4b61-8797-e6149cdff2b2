package com.pcitc.opal.af.bll.entity;

/*
 * 因果报警分析-一级列表报警详情数据实体
 * 模块编号：pcitc_opal_bll_class_CausalAlarmAnalysisEntity
 * 作       者：kun.zhao
 * 创建时间：2017/11/16
 * 修改编号：1
 * 描       述：因果报警分析-一级列表报警详情数据实体
 */
public class CausalAlarmAnalysisEntity {
	
	/**
	 * 位号ID
	 */
	private Long alarmPointId;
	/**
	 * 装置名称
	 */
	private String unitName;
	/**
     * 位号
     */
    private String tag;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;
    
    /**
     * 报警标识名称
     */
    private String alarmFlagName;
    
    /**
     * 生产单元简称
     */
    private String prdtCellSname;
    
    /**
     * 数量
     */
    private Long count;
    
    /**
     * 装置编码
     */
    private String unitId;

	/**
	 * 位置
	 */
    private String location;

	public Long getAlarmPointId() {
		return alarmPointId;
	}

	public void setAlarmPointId(Long alarmPointId) {
		this.alarmPointId = alarmPointId;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public Long getAlarmFlagId() {
		return alarmFlagId;
	}

	public void setAlarmFlagId(Long alarmFlagId) {
		this.alarmFlagId = alarmFlagId;
	}

	public String getAlarmFlagName() {
		return alarmFlagName;
	}

	public void setAlarmFlagName(String alarmFlagName) {
		this.alarmFlagName = alarmFlagName;
	}

	public String getPrdtCellSname() {
		return prdtCellSname;
	}

	public void setPrdtCellSname(String prdtCellSname) {
		this.prdtCellSname = prdtCellSname;
	}

	public Long getCount() {
		return count;
	}

	public void setCount(Long count) {
		this.count = count;
	}

	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}
}
