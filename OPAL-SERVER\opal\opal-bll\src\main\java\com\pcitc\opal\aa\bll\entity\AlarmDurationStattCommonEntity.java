package com.pcitc.opal.aa.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/**
 * @ClassName
 * <AUTHOR>
 * @create 2024/11/13 下午3:56
 */
public class AlarmDurationStattCommonEntity extends BasicEntity {
    /**
     * 报警时长统计总计
     */
    private String totalAlarmQuantity;

    /**
     * 紧急报警时长
     */
    private EchartsShowEntity emergencyAlarmQuantityEntity;
    private String emergencyAlarmQuantity;

    /**
     * 重要报警时长
     */
    private EchartsShowEntity importantAlarmQuantityEntity;
    private String importantAlarmQuantity;

    /**
     * 一般报警时长
     */
    private EchartsShowEntity generalAlarmQuantityEntity;
    private String generalAlarmQuantity;

    /**
     * 空报警时长
     */
    private EchartsShowEntity nullAlarmQuantityEntity;
    private String nullAlarmQuantity;

    /**
     * 数据Id
     */
    private String code;

    /**
     * 数据名称
     */
    private String name;

    public String getTotalAlarmQuantity() {
        return totalAlarmQuantity;
    }

    public void setTotalAlarmQuantity(String totalAlarmQuantity) {
        this.totalAlarmQuantity = totalAlarmQuantity;
    }

    public EchartsShowEntity getEmergencyAlarmQuantityEntity() {
        return emergencyAlarmQuantityEntity;
    }

    public void setEmergencyAlarmQuantityEntity(EchartsShowEntity emergencyAlarmQuantityEntity) {
        this.emergencyAlarmQuantityEntity = emergencyAlarmQuantityEntity;
    }

    public String getEmergencyAlarmQuantity() {
        return emergencyAlarmQuantity;
    }

    public void setEmergencyAlarmQuantity(String emergencyAlarmQuantity) {
        this.emergencyAlarmQuantity = emergencyAlarmQuantity;
    }

    public EchartsShowEntity getImportantAlarmQuantityEntity() {
        return importantAlarmQuantityEntity;
    }

    public void setImportantAlarmQuantityEntity(EchartsShowEntity importantAlarmQuantityEntity) {
        this.importantAlarmQuantityEntity = importantAlarmQuantityEntity;
    }

    public String getImportantAlarmQuantity() {
        return importantAlarmQuantity;
    }

    public void setImportantAlarmQuantity(String importantAlarmQuantity) {
        this.importantAlarmQuantity = importantAlarmQuantity;
    }

    public EchartsShowEntity getGeneralAlarmQuantityEntity() {
        return generalAlarmQuantityEntity;
    }

    public void setGeneralAlarmQuantityEntity(EchartsShowEntity generalAlarmQuantityEntity) {
        this.generalAlarmQuantityEntity = generalAlarmQuantityEntity;
    }

    public String getGeneralAlarmQuantity() {
        return generalAlarmQuantity;
    }

    public void setGeneralAlarmQuantity(String generalAlarmQuantity) {
        this.generalAlarmQuantity = generalAlarmQuantity;
    }

    public EchartsShowEntity getNullAlarmQuantityEntity() {
        return nullAlarmQuantityEntity;
    }

    public void setNullAlarmQuantityEntity(EchartsShowEntity nullAlarmQuantityEntity) {
        this.nullAlarmQuantityEntity = nullAlarmQuantityEntity;
    }

    public String getNullAlarmQuantity() {
        return nullAlarmQuantity;
    }

    public void setNullAlarmQuantity(String nullAlarmQuantity) {
        this.nullAlarmQuantity = nullAlarmQuantity;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
