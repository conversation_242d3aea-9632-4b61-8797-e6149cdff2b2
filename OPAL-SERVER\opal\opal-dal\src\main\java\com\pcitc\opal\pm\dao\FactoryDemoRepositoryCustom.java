package com.pcitc.opal.pm.dao;

import java.util.List;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.FactoryDemo;

/*
 * Factory实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_FactoryRepositoryCustom
 * 作       者：dongsheng.zhao
 * 创建时间：2017/09/17
 * 修改编号：1
 * 描       述：Factory实体的Repository的JPA自定义接口 
 */
public interface FactoryDemoRepositoryCustom {

	/**
	 * 校验数据
	 * 
	 * <AUTHOR> 2017-09-18
	 * @param factoryEntity
	 *            工厂实体
	 * @return 返回结果信息类
	 */
	CommonResult factoryValidation(FactoryDemo factoryEntity);

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-09-18
	 * @param factoryEntity
	 *            工厂实体
	 * @return 返回结果信息类
	 */
	CommonResult addFactory(FactoryDemo factoryEntity);

	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-09-18
	 * @param factoryIds
	 *            工厂ID集合
	 * @return 返回结果信息类
	 */
	CommonResult deleteFactory(Long[] factoryIds);

	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-09-18
	 * @param factoryEntity
	 * @return 返回结果信息类
	 */
	CommonResult updateFactory(FactoryDemo factoryEntity);

	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-09-18
	 * @param factoryId
	 *            工厂ID
	 * @return 工厂实体
	 */
	FactoryDemo getSingleFactory(Long factoryId);

	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2017-09-18
	 * @param factoryIds
	 *            工厂ID集合
	 * @return 工厂实体集合
	 */
	List<FactoryDemo> getFactory(Long[] factoryIds);

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-09-18
	 * @param name
	 *            工厂名称
	 * @param inUse
	 *            是否启动
	 * @param page
	 *            分页参数
	 * @return 工厂实体集合
	 */
	PaginationBean<FactoryDemo> getFactory(String name, String stdCode, Integer inUse, Pagination page);
}
