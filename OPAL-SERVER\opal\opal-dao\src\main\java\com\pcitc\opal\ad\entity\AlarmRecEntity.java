package com.pcitc.opal.ad.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 报警记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_ad_alarmrec")
public class AlarmRecEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "alarm_rec_id", type = IdType.AUTO)
    private Long alarmRecId;

    /**
     * 装置编码
     */
    @TableField("unit_code")
    private String unitCode;

    @TableField("prdtcell_id")
    private Long prdtcellId;

    /**
     * DCS编码(缓存表)
     */
    @TableField("dcs_code")
    private String dcsCode;

    @TableField("event_type_id")
    private Long eventTypeId;

    @TableField("alarm_point_id")
    private Long alarmPointId;

    /**
     * 位号(缓存表)
     */
    @TableField("tag")
    private String tag;

    @TableField("alarm_flag_id")
    private Long alarmFlagId;

    /**
     * 报警标识(缓存表)
     */
    @TableField("alarm_flag")
    private String alarmFlag;

    /**
     * 报警时间
     */
    @TableField("alarm_time")
    private Date alarmTime;

    /**
     * 恢复时间
     */
    @TableField("recovery_time")
    private Date recoveryTime;

    /**
     * 响应时间
     */
    @TableField("response_time")
    private Date responseTime;

    /**
     * 优先级
     */
    @TableField("priority")
    private Long priority;

    /**
     * 优先级(缓存表)
     */
    @TableField("priority_cache")
    private String priorityCache;

    /**
     * 先前值
     */
    @TableField("previous_value")
    private String previousValue;

    /**
     * 值
     */
    @TableField("now_value")
    private String nowValue;

    @TableField("limit_value")
    private Long limitValue;

    @TableField("in_shelved")
    private Long inShelved;

    @TableField("in_suppressed")
    private Long inSuppressed;

    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;

    /**
     * 参数
     */
    @TableField("parameter")
    private String parameter;

    /**
     * 描述
     */
    @TableField("des")
    private String des;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    @TableField("is_ack")
    private Long isAck;

    @TableField("event_id")
    private Long eventId;

    /**
     * 企业ID
     */
    @TableField("company_id")
    private Long companyId;


}
