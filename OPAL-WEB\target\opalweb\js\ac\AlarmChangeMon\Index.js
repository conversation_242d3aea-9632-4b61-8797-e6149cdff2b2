var alarmNumStattUrl = OPAL.API.acUrl + '/changeMonitoring/getChangeMonitoringChart';
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var commonCasualMainUrl = OPAL.API.commUrl + "/getCausalAlarmAnalysis";
var commonCasualSubUrl = OPAL.API.commUrl + "/getCausalAlarmAnalysisTable";
var changeMonitorUrl = OPAL.API.acUrl + "/changeMonitoring/getChangeMonitoring";
var changeInfoUrl = OPAL.API.acUrl + "/changeMonitoring/getCraftChangeInfo";
var workTeamUrl = OPAL.API.commUrl + "/getWorkTeam";
var alarmPointId;
var alarmFlagId;
var queryHour = 8;
var isLoading = true;
var floodAlarmChart;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            /**
             *绑定事件
             */
            this.bindUi();
            OPAL.util.extendDate();
            /**
             * 初始化日期时间选择控件组
             */
            page.logic.initTime();
            $('#workTeamIds').html("");
            $("#workTeamIds").prop('disabled', true);
            /**
             * 初始化装置数
             */
            page.logic.initUnitTree();
            page.logic.initCausalMainTable({});
            OPAL.util.getQueryTime(function (data) {
                queryHour = moment(data, "HH:mm:ss").get('hour');
                if (queryHour == undefined) {
                    queryHour = 0;
                }
            });

            //装置赋值
            if (isLoading && (page.data.param.unitIds == null || page.data.param.unitIds == undefined || page.data.param.unitIds.length == 0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("FloodAlarmAnalysis");
                if (cookieValue != null && cookieValue != undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
            document.getElementById("btnSearch").click();
        },
        bindUi: function () {
            /**
             * 查询
             */
            $('#btnSearch').click(function () {
                if (OPAL.util.checkDateIsValid() == true) {
                    isLoading = false;
                    page.logic.search();
                }
            })
            //表格自适应页面拉伸宽度
            $(window).resize(function () {
                // floodAlarmChart.resize();
                $('#tableCausal').bootstrapTable('resetView');
            });
        },
        data: {
            param: {},
            subParam: {}
        },
        logic: {

            /**
             * 初始化 时间
             */
            initTime: function() {
                OPAL.ui.initDateTimePeriodPicker({
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm:ss',
                })
            },
            /***
             * 查询
             */
            search: function () {
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;
                $("#btnSearch").prop('disabled', true);
                $("#random").val(Math.random());
                let searchData = OPAL.form.getData("formSearch");
                $.ajax({
                    url: alarmNumStattUrl,
                    data: searchData,
                    dataType: 'json',
                    success: function (data) {
                        var result = $.ET.toObjectArr(data);
                        page.logic.initFloodChart(JSON.stringify(result));
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
                OPAL.util.getSearchTime({
                    startTime: $("#startTime").val(),
                    endTime: $("#endTime").val()
                }, function (data) {
                    page.data.param = searchData;
                    $('#tableCausal').bootstrapTable('refresh', {
                        "url": changeMonitorUrl,
                        "pageNumber": 1
                    })
                });
            },

            /**
             * 初始化图表
             * @param data
             */
            initFloodChart: function (data) {
                var results = JSON.parse(data);
                if (floodAlarmChart && !floodAlarmChart.isDisposed()) {
                    // floodAlarmChart.clear();
                    floodAlarmChart.dispose();
                }
                if (results == undefined || results.length == 0) {
                    floodAlarmChart = OPAL.ui.chart.initEmptyChart('floodAlarmChart');
                    return;
                }
                var xAxis = [];
                // legalStatusNum 合法 
                // notLegalStatusNum 非法
                // unknownStatusNum 未知
                var legalStatusNum = [];
                var notLegalStatusNum = [];
                var unknownStatusNum = [];
                unitId = results[0].unitId;
                unit = results[0].unitName;
                for (var i = 0; i < results.length; i++) {
                    if (results[i].unitName) {
                        xAxis.push(results[i].unitName)
                    }
                    legalStatusNum.push(results[i].legalStatusNum);
                    notLegalStatusNum.push(results[i].notLegalStatusNum);
                    unknownStatusNum.push(results[i].unknownStatusNum);
                }
                var option = {
                    color: ['#F4D312', '#FE6732', '#59E817', '#C9C9C9'],
                    tooltip: {
                        trigger: 'axis',
                        // formatter: function (params) {
                        //     var colors = ['#F4D312', '#FE6732', '#CD1515','#C9C9C9'];
                        //     var total = 0;
                        //     for(var i = 0; i < params.length; i++){
                        //         total += parseInt(params[i].value);
                        //     }
                        //     var tips = '<div>'+ params[0].name + '<span style="margin-left: 10px;font-weight: bolder">'+page.logic.ThreeBits(total)+'</span></div>';
                        //     for(var i = 0; i < params.length; i++){
                        //         tips += '<div><span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'+colors[i]+';"></span>'+params[i].seriesName+'：'+page.logic.ThreeBits(params[i].value)+'</div>';
                        //         total += parseInt(params[i].value);
                        //     }
                        //     return tips;
                        // }
                    },
                    // legend: {
                    //     itemHeight: 8,
                    //     itemWidth: 18,
                    //     data: ['一般', '重要', '紧急','空'],
                    // },
                    grid: {
                        left: '1%',
                        right: '1%',
                        top: '10%',
                        height: '270px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            show: true,
                            splitNumber: 5,
                            textStyle: {
                                fontSize: 12,
                                color: '#000'
                            },
                        },
                        data: xAxis,
                    }],
                    dataZoom: [{
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        // left: '9%',
                        bottom: -5,
                        start: 0,
                        end: 100 //初始化滚动条
                    }],
                    yAxis: {
                        name: '调整情况',
                        splitLine: {
                            show: false
                        },
                        type: 'value'
                    },
                    series: [{
                        name: '未知',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: unknownStatusNum
                    },
                    {
                        name: '非法',
                        type: 'bar',
                        barWidth: '20px',
                        stack: 'sum',
                        data: notLegalStatusNum,
                    },
                    {
                        name: '合法',
                        type: 'bar',
                        stack: 'sum',
                        barWidth: '20px',
                        data: legalStatusNum
                    }
                    ]
                };
                floodAlarmChart = echarts.init(document.getElementById('floodAlarmChart'));
                floodAlarmChart.setOption(option);
                floodChartConfig = option;
                floodAlarmChart.on('click', function (param) {
                    var indexs = param.dataIndex;
                    unitId = results[indexs].unitId;
                    unit = param.name;

                });
            },
            ThreeBits: function (value) {
                var value = String(value);
                var temp = value.split('').reverse().join('').match(/(\d{1,3})/g);
                var result = (Number(value) < 0 ? "-" : "") + temp.join(',').split('').reverse().join('');
                return result;
            },
            // 展开二级表格
            expandRow: function () {
                $('.expand-row').click(function (e) {
                    var row = e.target.parentElement.parentElement;
                    var id = row.dataset.uniqueid;
                    var rowInfo = '';
                    // 如果是展开操作
                    if (row.dataset.expand == undefined || row.dataset.expand == 'false') {
                        row.dataset.expand = true;
                        // 获取行信息
                        rowInfo = $("#tableCausal").bootstrapTable('getRowByUniqueId', id);
                        $.ajax({
                            url: changeInfoUrl,
                            data: { craftChangeInfoId: rowInfo.craftChangeInfoId },
                            dataType: 'json',
                            success: function (data) {
                                var result = $.ET.toObjectArr(data)[0];
                                let tag = rowInfo.tag ? rowInfo.tag : result.tagCode;
                                let alarmFlag = rowInfo.alarmFlag ? rowInfo.alarmFlag : result.alarmFlag;
                                let measunit = rowInfo.measuringUnit ? rowInfo.measuringUnit : result.measunit;
                                // 创建行
                                var tr = '<tr>';
                                tr += '<td colspan="11">';
                                tr += '<table class="table-two table table-striped alarm-public-table-style table-hover text-center" border="1"><thead><tr><th>调整单号</th><th style="width: 100px">位号</th><th>项目</th><th>计量单位</th><th>报警标识</th><th>修改前指标</th><th>修改后指标</th><th>提交时间</th><th>发布时间</th></tr></thead><tbody><tr>';
                                tr += '<td>' + result.sn + '</td>';
                                tr += '<td>' + tag + '</td>';
                                tr += '<td>' + result.item + '</td>';
                                tr += '<td>' + measunit + '</td>';
                                tr += '<td>' + alarmFlag + '</td>';
                                tr += '<td>' + result.beforeValue + '</td>';
                                tr += '<td>' + result.afterValue + '</td>';
                                tr += '<td>' + result.submitTime + '</td>';
                                tr += '<td>' + result.rlsTime + '</td>';
                                tr += '</tr></tbody></table>';
                                tr += '</td>';
                                tr += '</tr>';
                                $(row).after(tr);
                            },
                            complete: function () {

                            }
                        });
                    } else {
                        // 如果是收缩操作
                        row.dataset.expand = false;
                        var rowIndex = row.rowIndex;
                        $('#tableCausal > tbody > tr').eq(rowIndex).remove();
                        // $('#tableCausal').bootstrapTable('hideRow', {index: rowIndex + 1})
                    }
                });
            },
            /**
             * 初始化因果分析一级列表
             */
            initCausalMainTable: function () {
                OPAL.ui.initBootstrapTable('tableCausal', {
                    detailView: false,
                    pageSize: 20,
                    showColumns: false,
                    uniqueId: 'craftChangeInfoId',
                    columns: [{
                        title: "编号",
                        formatter: function (value, row, index) {
                            var data = page.data.param;
                            return index + 1 + (data.pageNumber - 1) * data.pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    },
                    {
                        field: 'sn',
                        // name: 'Expand',
                        title: '调整单号',
                        class: 'expand-column',
                        formatter: function (value, row, index) {
                            if (row.sn != '') {
                                return '<span class="expand-row text-center" aria-hidden="true" style="color:#348fe2;display:block;">' + row.sn + '</span> ';
                            } else {
                                return ''
                            }
                        }
                    }, {
                        field: 'startTime',
                        title: '调整时间',
                        align: 'center',
                    }, {
                        field: 'unitName',
                        title: '装置',
                        align: 'center',
                    }, {
                        field: 'prdtcellName',
                        title: '生产单元',
                        align: 'center',
                    }, {
                        field: 'workerName',
                        title: '班组',
                        align: 'center',
                    }, {
                        field: 'tag',
                        title: '位号',
                        align: 'center',
                    }, {
                        field: 'des',
                        title: '参数描述',
                        align: 'center',
                    }, {
                        field: 'alarmFlag',
                        title: '报警标识',
                        align: 'center',
                    }, {
                        field: 'measuringUnit',
                        title: '计量单位',
                        align: 'center',
                    }, {
                        field: 'value',
                        title: '当前值',
                        align: 'center',
                    }, {
                        field: 'status',
                        title: '调整状态',
                        align: 'center',
                        formatter: function (value, row, index) {
                            let status = '';
                            if (row.status == 1) {
                                status = '非法'
                            } else if (row.status == 2) {
                                status = '合法'
                            } else {
                                status = '未知'
                            }
                            return '<div class="text-center">' + status + '</div> ';
                        }
                    }],
                    onLoadSuccess: function () {
                        // 初始化展开按钮
                        page.logic.expandRow();
                    }
                }, page.logic.queryParams);
                var tableOption = $('#tableCausal').bootstrapTable('getOptions');
                tableOption.pageList = [20];
                $("#tableCausal").bootstrapTable('refreshOptions', tableOption);
            },
            /**
             * 查询父表格参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) {
                var param = {
                    // isCausalAlarmAnalysis:false,
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    // sortOrder: p.sortOrder,
                    now: Math.random()
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function (node, checked) {
                        var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (unitIds != undefined && unitIds.length == 1) {
                            $("#prdtIds").combo('enable');
                            $('#prdtIds').combotree('setValues', []);
                            page.logic.initPrdtMultipleSelect(unitIds[0]);
                            $("#workTeamIds").prop('disabled', false);
                            page.logic.initWorkTeam();
                            $('.textbox,.combo').css('background-color', '');
                        } else {
                            $('#prdtIds').combotree('setValues', []);
                            $("#prdtIds").combo('disable');
                            $('#workTeamIds').html("");
                            $("#workTeamIds").prop('disabled', true);
                            $('.textbox-disabled').css('background-color', 'rgb(235, 235, 228)');
                        }
                    }
                }, false);
            },
            /**
             * 初始化生产单元下拉多选
             * @param unitId
             */
            initPrdtMultipleSelect: function (unitId) {
                OPAL.ui.getComboMultipleSelect('prdtIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function () {
                    $("#prdtIds").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化班组选择
             */
            initWorkTeam: function () {
                var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                if (unitIds.length != 1) return;
                if ($("#startTime").val() == '' || $("#endTime").val() == '') return;
                let endTime = $("#endTime").val() + ' ' + OPAL.util.dateFormat(new Date(), "HH:mm:ss");
                OPAL.ui.getCombobox("workTeamIds", workTeamUrl, {
                    keyField: "workTeamId",
                    valueField: "workTeamSName",
                    selectFirstRecord: true,
                    mapManyValues: true, //是否一条记录匹配多个隐藏值
                    mapManyDataFieldName: 'workTeamIdList',
                    data: {
                        "startTime": OPAL.util.dateFormat(OPAL.util.strToDate(OPAL.form.getData('formSearch').startTime), "yyyy-MM-dd HH:mm:ss"),
                        "endTime": endTime,
                        "unitId": unitIds[0],
                    }
                }, null);
            },
        }
    };
    page.init();
    window.page = page;
});