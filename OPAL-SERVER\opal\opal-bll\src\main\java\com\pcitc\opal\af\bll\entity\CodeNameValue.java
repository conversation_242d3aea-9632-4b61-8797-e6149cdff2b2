package com.pcitc.opal.af.bll.entity;


import com.pcitc.opal.ad.dao.imp.AlarmTagUnitVO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/*
 * 工艺参数报警率数据实体
 * 作  　  者：shufei.sui
 * 创建时间：2019/12/11
 * 修改编号：1
 * 描       述：工艺参数报警率数据实体
 */
@Data
public class CodeNameValue {
    public CodeNameValue(String name, Long value) {
        this.name = name;
        this.value = value;
    }
    public CodeNameValue() {}

    private Integer code;
    private String name;
    private Long value;
}


