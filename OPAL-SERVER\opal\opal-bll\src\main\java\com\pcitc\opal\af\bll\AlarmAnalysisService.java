package com.pcitc.opal.af.bll;

import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.af.bll.entity.AlarmDetailEntity;
import com.pcitc.opal.af.bll.entity.OperateDetailEntity;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Date;

/*
 * 报警分析逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmAnalysisService
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/30
 * 修改编号：1
 * 描    述：报警分析逻辑层接口
 */
@Service
public interface AlarmAnalysisService {

    /**
     * 查看报警分析-操作详情
     *
     * <AUTHOR> 2017-10-30
     * @param dateJason   日期集合
     * @param alarmPointId 报警点ID
     * @param alarmFlagId 报警标识ID
     * @return 操作详情数据
     */
    OperateDetailEntity getOperateDetail(String dateJason, Long alarmPointId, Long alarmFlagId) throws IOException;

    /**
     * 查看报警分析-报警详情
     *
     * <AUTHOR> 2017-11-08
     * @param dateJason   日期集合
     * @param alarmPointId 报警点ID
     * @param alarmFlagId 报警标识ID
     * @return 报警详情数据
     */
    AlarmDetailEntity getAlarmDetail(String dateJason, Long alarmPointId, Long alarmFlagId) throws Exception;

    /**
     * 查看报警分析-报警详情报警值变更
     *
     * <AUTHOR> 2017-11-09
     * @param dateJason     日期集合
     * @param alarmPointId  报警点ID
     * @param alarmFlagId   报警标识ID
     * @param alarmValue    报警值变更
     * @return 报警详情数据
     */
    AlarmDetailEntity getChangeAlarmDetail(String dateJason, Long alarmPointId, Long alarmFlagId,Double alarmValue) throws IOException;

    /**
     * 查看报警分析-操作详情网格列数据
     *
     * <AUTHOR> 2017-10-30
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param endFlag       标志
     * @param alarmPointId  报警点ID
     * @param alarmFlagId   报警标识ID
     * @return 操作详情数据
     */
    PaginationBean<AlarmEventEntity> getGridOperateDetail(Date startTime, Date endTime,String endFlag, Long alarmPointId, Long alarmFlagId,Pagination page) throws Exception;

    /**
     * 根据装置编码和时间区间获取持续的报警事件列表
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param unitCode     装置编码
     * @param page       分页信息
     * @return 持续的报警事件列表
     * <AUTHOR> 2017-10-30
     */
    PaginationBean<AlarmEventEntity> getContinuousAlarmList(Date startTime, Date endTime, String unitCode, Pagination page)throws Exception;
    /**
     * 根据装置编码和时间区间获取搁置的报警事件列表
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param unitCode     装置编码
     * @param page       分页信息
     * @return 搁置的报警事件列表
     * <AUTHOR> 2017-10-30
     */
    PaginationBean<AlarmEventEntity> getShelvedAlarmList(Date startTime, Date endTime, String unitCode, Pagination page)throws Exception;
    /**
     * 根据装置编码和时间区间获取屏蔽的报警事件列表
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param unitCode     装置编码
     * @param page       分页信息
     * @return 屏蔽的报警事件列表
     * <AUTHOR> 2017-10-30
     */
    PaginationBean<AlarmEventEntity> getSuppressedAlarmList(Date startTime, Date endTime, String unitCode, Pagination page)throws Exception;

}
