package com.pcitc.opal.ap.dao;

import com.pcitc.opal.ak.pojo.AlarmKnowlgManagmt;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleDetailEntityVO;
import com.pcitc.opal.ap.pojo.Group;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

import java.util.List;

/*
 * 报警知识管理实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_AlarmKnowlgManagmtRepositoryCustom
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理实体的Repository的JPA自定义接口
 */
public interface GroupRepositoryCustom {


    List<Group> getGroupNames();


    /**
     * 根据指定id查询群组人员以及电话本
     */
    List<Group> getGroupDetail(Long groupId);
}
