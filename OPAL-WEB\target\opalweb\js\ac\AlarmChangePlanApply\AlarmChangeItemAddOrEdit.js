var addUrl = OPAL.API.acUrl + '/alarmChangePlanApply/addAlarmChangeItem';
var updateUrl = OPAL.API.acUrl + '/alarmChangePlanApply/updateAlarmChangeItem';
var getAlarmChangeItemByPlanDetailUrl = OPAL.API.acUrl + '/alarmChangePlanApply/getAlarmChangeItemByPlanDetail';
var getChangeAlarmDetailUrl = OPAL.API.afUrl + '/alarmAnalysis/getChangeAlarmDetail';
var getAlarmDetailUrl = OPAL.API.afUrl + '/alarmAnalysis/getAlarmDetail';
var alarmPriorityListUrl = OPAL.API.commUrl + '/getAlarmPriorityList';
var getInSuppressedListUrl = OPAL.API.commUrl + '/getInSuppressedList';
var getalarmPointsUrl = OPAL.API.pmUrl + '/alarmPoints';
var getTimeUrl = OPAL.API.commUrl + '/getQueryStartAndEndDate';
window.pageLoadMode = PageLoadMode.None;
var AlarmFlagCharts; // 定义图表
var queryTimeArray; // 固定时间点 10：00
var dateJason;
var unitId; //  装置id
var alarmFlagId;
var alarmPointId;
var alarmFlagName; // 报警标识
var alarmValue; // 输入的修改值
var limitValueLength; // 限值的小数个数
var maxValue;
var limitValue;
var unitFlag; // 单位
var seriesOld; //原报警数
var upLimitValue, downLimitValue; //工艺卡片的范围
var startTime, endTime; // 开始时间 结束时间
var seriesNow = new Array();
var alarmPointHH, alarmPointHI, alarmPointLL, alarmPointLO;
var alarmChangeItemArr; // 查询的参数
$(function() {
	var index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
	var page = {
		/**
		 * 初始化
		 */
		init: function() {
			this.bindUi();
			// 获得固定的时间点
			page.logic.getQueryTime();
			// 选择是否屏蔽
			page.logic.shieldChoose();
			//设置时间插件
			page.logic.initTime();
		},
		bindUi: function() {
			/**
			 * 设置图表自适应屏幕
			 */
			window.onresize = function() {
				if(AlarmFlagCharts != undefined){
					AlarmFlagCharts.resize();
				}
			};
			$('#saveAddModal').click(function() {
				page.logic.save();
			});
			$('#closePageChild').click(function() {
				parent.pageLoadMode = PageLoadMode.None;
				page.logic.closeLayer(false);
			});
			$('#sureBtn').click(function() {
				if ($('#alarmValue').val() == alarmValue) {
					layer.confirm('修改值须做修改！', {
						btn: ['确定']
					})
					return false;
				}
				page.logic.refreshDetail();
			})
			// 查询
			$('#btnSearch').click(function() {
				if ($('#alarmTime').val() == '') {
					layer.msg('查询时间不可为空！')
					return false;
				}
				// 获得查询时间json 
				page.logic.search();
			})
		},
		logic: {
			/**
			 * 获得固定的时间点
			 */
			getQueryTime: function(dateTimeList, unitIdList, id) {
				OPAL.util.getQueryTime(function(queryTime) {
					queryTimeArray = queryTime;
				});
			},
			setData: function(data, rowData) {
				//初始化优先级
				page.logic.initAlarmPriorityList();
				page.logic.initInSuppressedList();
				pageMode = rowData.pageMode;
				dateJason = JSON.stringify(data);
				alarmFlagId = rowData.alarmFlagId;
				alarmFlagName = rowData.alarmFlagName;
				alarmPointId = rowData.alarmPointId;
				planDetailId = rowData.planDetailId == undefined ? '' : rowData.planDetailId;
				planId = rowData.planId;
				unitId = rowData.unitId;
				startTime = OPAL.util.dateFormat(new Date($.parseJSON(dateJason)[0].value), 'yyyy-MM-dd HH:mm:ss');
				endTime = OPAL.util.dateFormat(new Date($.parseJSON(dateJason)[1].value), 'yyyy-MM-dd HH:mm:ss');
				alarmTime = rowData.alarmTime;
				page.logic.initAlarmChangePage(planId, planDetailId, alarmPointId, alarmFlagId);
				if (pageMode == PageModelEnum.View) {
					$('#alarmChange').css('display', 'block');
					$('#timeDiv').css('display', 'none');
					$('#showList').css('display', 'none');
					$('#AlarmFlagCharts').css('display', 'none');
					//todo:由薛江涛暂时修改
					$('#shieldAfter').attr('disabled', 'disabled');
					$('#limitAfter').attr('disabled', 'disabled');
					$('#priority').attr('disabled', 'disabled');
					$('#remarks').attr('disabled', 'disabled');
					$('#saveAddModal').css('display', 'none');
					return;
				}
				if (pageMode == PageModelEnum.NewAdd) {
					$('#timeDiv').css('display', 'block');
					$('#showList').css('display', 'block');
					$('#AlarmFlagCharts').css('display', 'block');
					$('#alarmChange').css('display', 'block');
					$('#alarmTime').val(alarmTime);
				}
				if (pageMode == PageModelEnum.Edit) {
					$('#timeDiv').css('display', 'block');
					$('#showList').css('display', 'block');
					$('#AlarmFlagCharts').css('display', 'block');
					$('#alarmChange').css('display', 'block');
				}
				page.logic.initAlarmDetailPage(unitId, dateJason, alarmPointId, alarmFlagId);
				page.logic.getAlarmPointValue(alarmPointId);
			},
			/**
			 * 获得图表信息
			 */
			initAlarmDetailPage: function(unitId, dateJason, alarmPointId, alarmFlagId) {
				$.ajax({
					url: getAlarmDetailUrl,
					async: false,
					data: {
						unitId: unitId,
						dateJason: dateJason,
						alarmPointId: alarmPointId,
						alarmFlagId: alarmFlagId
					},
					dataType: "JSON",
					type: 'GET',
					success: function(result) {
						var res = $.ET.toObjectArr(result);
						if (res != null && res != undefined && res != '') {
							var alarmDuration = res[0].alarmDuration; // 报警总时长
							alarmFlagName = res[0].alarmFlagName; //报警标识名称
							var alarmTimes = res[0].alarmTimes; //报警次数
							alarmValue = res[0].alarmValue; // 修改值
							var avgConfirmTime = res[0].avgConfirmTime; //平均确认时间
							var confirmTimes = res[0].confirmTimes; // 确认次数
							var location = res[0].location; // 位置
							maxValue = res[0].maxValue;
							minValue = res[0].minValue;
							var nowAlarmTimes = res[0].nowAlarmTimes;
							var prdtCellSname = res[0].prdtCellSname; // 生产单元
							var reduceRate = res[0].reduceRate; //减少比例
							var tag = res[0].tag; // 位号
							downLimitValue = res[0].downLimitValue;
							upLimitValue = res[0].upLimitValue;
							unitFlag = res[0].unitFlag;
							limitValue = res[0].limitValue;
							limitValueLength = page.logic.limitValueLength(limitValue);
							if (downLimitValue != '' && upLimitValue != '') {
								var upAnddownLimitValue = page.logic.upAnddownLimitValue(downLimitValue, upLimitValue);
							}
							$('#tag').html(tag);
							$('#alarmFlagName').html(alarmFlagName);
							$('#prdtCellSname').html(prdtCellSname);
							$('#location').html(location);
							$('#alarmDuration').html(alarmDuration);
							$('#alarmTimes').html(alarmTimes);
							$('#confirmTimes').html(confirmTimes);
							$('#avgConfirmTime').html(avgConfirmTime);
							$('#upAnddownLimitValue').html(upAnddownLimitValue);
							$('#upLimitValue').val(upLimitValue);
							$('#downLimitValue').val(downLimitValue);
							var histogramData = res[0].histogramData;
							var lineChartData = res[0].lineChartData;
							if (alarmFlagName == 'PVHH' || alarmFlagName == 'PVHI' || alarmFlagName == 'PVLO' || alarmFlagName == 'PVLL') {
								$('#alarmValueBox').css('display', 'inline-block');
								$('#nowAlarmTimesBox').css('display', 'inline-block');
								$('#reduceRateBox').css('display', 'inline-block');
								$('#alarmValue').val(alarmValue);
								$('#nowAlarmTimes').html(nowAlarmTimes);
								$('#reduceRate').html(reduceRate + '%');
								if (histogramData != '' && lineChartData != '') {
									if (AlarmFlagCharts && !AlarmFlagCharts.isDisposed()) {
										AlarmFlagCharts.clear();
										AlarmFlagCharts.dispose();
									}
									page.logic.initCharts(histogramData, lineChartData);
									$('.row2').css('display', 'block');
									$('.row3').css('display', 'block');
								} else {
									if (AlarmFlagCharts && !AlarmFlagCharts.isDisposed()) {
										AlarmFlagCharts.clear();
										AlarmFlagCharts.dispose();
									}
									AlarmFlagCharts = OPAL.ui.chart.initEmptyChart('AlarmFlagCharts');
									$('.row2').css('display', 'none');
									$('.row3').css('display', 'none');
								}
							} else {
								if (histogramData != '' && lineChartData != '') {
									$('.row2').css('display', 'block');
									$('.row3').css('display', 'block');
									$('#alarmValueBox').css('display', 'none');
									$('#nowAlarmTimesBox').css('display', 'none');
									$('#reduceRateBox').css('display', 'none');
									$('#upAnddownLimitValueBox').css('margin-left', '30px');
									if (AlarmFlagCharts && !AlarmFlagCharts.isDisposed()) {
										AlarmFlagCharts.clear();
										AlarmFlagCharts.dispose();
									}
									page.logic.initChartsOther(histogramData, lineChartData)
								} else {
									if (AlarmFlagCharts && !AlarmFlagCharts.isDisposed()) {
										AlarmFlagCharts.clear();
										AlarmFlagCharts.dispose();
									}
									AlarmFlagCharts = OPAL.ui.chart.initEmptyChart('AlarmFlagCharts');
									$('.row2').css('display', 'none');
									$('.row3').css('display', 'none');
								}
								//todo:我整理由我暂时修改
							}
						}
					},
					error: function(result) {
						var errorResult = $.parseJSON(result.responseText);
						layer.msg(errorResult.collection.error.message);
					}
				});
			},
			/**
			 * 请求主页面
			 */
			initAlarmChangePage: function(planId, planDetailId, alarmPointId, alarmFlagId) {
				if (planDetailId == undefined || planDetailId == null) {
					planDetailId = '';
				}
				$.ajax({
					url: getAlarmChangeItemByPlanDetailUrl,
					type: "get",
					data: {
						planId: planId,
						planDetailId: planDetailId,
						alarmPointId: alarmPointId,
						alarmFlagId: alarmFlagId
					},
					async: false,
					dataType: "json",
					success: function(result) {
						var entity = $.ET.toObjectArr(result)[0];
						var beforeInSuppressed = entity.beforeInSuppressed;
						var beforeInSuppressedStr = entity.beforeInSuppressedStr;
						var beforeLimitValue = entity.beforeLimitValue;
						var beforePriority = entity.beforePriority;
						var beforePriorityName = entity.beforePriorityName;
						var afterInSuppressed = entity.afterInSuppressed;
						var afterLimitValue = entity.afterLimitValue;
						var afterPriority = entity.afterPriority;
						var remarks = entity.remark;
						$('#shieldBefore').html(beforeInSuppressedStr);
						$('#shieldBefore').attr('dataId', beforeInSuppressed);
						$('#limitBefore').html(beforeLimitValue);
						$('#priorityBefore').html(beforePriorityName);
						$('#priorityBefore').attr('dataId', beforePriority);
						$('#shieldAfter').val(afterInSuppressed);
						if (afterInSuppressed == 1) {
							$('#limitAfter').val('');
							$('#limitAfter').prop('disabled', true);
							$('#priority').val('');
							$('#priority').prop('disabled', true);
						} else {
							$('#limitAfter').val(afterLimitValue);
							$('#priority').val(afterPriority);
							$('#limitAfter').prop('disabled', false);
							$('#priority').prop('disabled', false);
							if (PageLoadMode == PageLoadMode.View) {
								$('#limitAfter').val('');
								$('#priority').val('');
								$('#limitAfter').prop('disabled', true);
								$('#priority').prop('disabled', true);
							}
						}
						$('#remarks').val(remarks)
					},
					error: function(result) {
						var errorResult = $.parseJSON(result.responseText);
						layer.msg(errorResult.collection.error.message);
					}
				});
			},
			/**
			 * 获取高报低报的值
			 */
			getAlarmPointValue: function(alarmPointId) {
				$.ajax({
					url: getalarmPointsUrl + "/" + alarmPointId + "?now=" + Math.random(),
					type: "get",
					async: false,
					dataType: "json",
					success: function(data) {
						var entity = $.ET.toObjectArr(data)[0];
						alarmPointHH = entity.alarmPointHH;
						alarmPointHI = entity.alarmPointHI;
						alarmPointLL = entity.alarmPointLL;
						alarmPointLO = entity.alarmPointLO;
					},
					error: function(result) {
						var errorResult = $.parseJSON(result.responseText);
						layer.msg(errorResult.collection.error.message);
					}
				});
			},
			/**
			 * 初始化图表 (报警标识为PVHH、PVHI、PVLO、PVLL)
			 */
			initCharts: function(data1, data2) {
				if (AlarmFlagCharts && !AlarmFlagCharts.isDisposed()) {
					AlarmFlagCharts.clear();
					AlarmFlagCharts.dispose();
				}
				var histogramData = JSON.parse(data1);
				var lineChartData = JSON.parse(data2);
				var seriesOld = new Array();
				// var seriesNow = new Array();
				var seriesAlarmValue = new Array();
				$.each(histogramData, function(i, el) {
					seriesOld[i] = [histogramData[i].key, histogramData[i].value.key];
					seriesNow[i] = [histogramData[i].key, histogramData[i].value.value];
				})
				$.each(lineChartData, function(index, ele) {
					seriesAlarmValue.push([index, ele])
				});
				AlarmFlagCharts = echarts.init(document.getElementById('AlarmFlagCharts'));
				option = {
					tooltip: {
						trigger: 'item', //axis
						axisPointer: { // 坐标轴指示器，坐标轴触发有效
							type: '' // 默认为直线，可选为：'line' | 'shadow'
						}
					},
					grid: {
						left: '1%',
						right: '2%',
						bottom: '1%',
						top: '12%',
						height: '215px',
						containLabel: true
					},
					legend: {
						left: '80px',
						top: '7%',
						itemWidth: 20, //设置icon大小
						itemHeight: 10, //设置icon大小
						data: [{
							name: '报警数',
							icon: 'roundRect',
							textStyle: {
								color: '#9d9d9d',
							}
						}, {
							name: '报警值',
							icon: '',
							textStyle: {
								color: '#9d9d9d',
							}
						}, {
							name: '修改值',
							icon: 'line',
							textStyle: {
								color: '#9d9d9d',
							}
						}, {
							name: '限值',
							icon: 'line',
							textStyle: {
								color: '#9d9d9d',
							}
						}]
					},
					xAxis: [{
						type: 'time',
						minInterval: 3600 * 24 * 1000,
						maxInterval: 3600 * 24 * 1000,
						min: function(value) {
							return Date.parse(startTime.replace(/-/g, "/")) - 3600 * 15 * 1000;
						},
						max: function(value) {
							return Date.parse(endTime.replace(/-/g, "/")) + 3600 * 5 * 1000;
						},
						axisLine: {
							onZero: false
						},
						axisLabel: {
							showMinLabel: false,
							showMaxLabel: false,
							formatter: function(value, index) {
								var date = new Date(value);
								var y = date.getFullYear();
								var m = date.getMonth() + 1;
								var d = date.getDate();
								return y + '-' + m + '-' + d;
							}
						},
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						},
					}],
					yAxis: [{
						name: '报警数',
						type: 'value',
						min: 0,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}, {
						name: '报警值(' + unitFlag + ')',
						nameLocation: 'end',
						position: 'right',
						minInterval: 1,
						axisLabel: {
							formatter: function(value, index) {
								return value.toFixed(limitValueLength);
							}
						},
						type: 'value',
						splitLine: { //网格线
							show: false,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					series: [{
						name: '原报警数',
						type: 'bar',
						barWidth: '15',
						silent: false, // 图形是否不响应和触发鼠标事件
						itemStyle: {
							normal: {
								color: '#e3e8ed'
							},
							emphasis: {
								color: 'rgba(0, 0, 0, 0.1)'
							}
						},
						barGap: '-100%',
						data: [],
						tooltip: {
							formatter: function(params) {
								var seriesOldValue = seriesOld[(params.dataIndex)][1];
								var seriesNowValue = seriesNow[(params.dataIndex)][1];
								var name = seriesOld[(params.dataIndex)][0].replace(/\//g, "-");
								var d1 = Date.parse(new Date(seriesOld[(params.dataIndex)][0]));
								var d2 = d1 + (60 * 60 * 24 * 1000);
								var Y = (new Date(d2)).getFullYear();
								var M = (new Date(d2)).getMonth() + 1;
								var D = (new Date(d2)).getDate();
								if (Math.floor(M / 10) == 0) {
									M = '0' + M
								}
								if (Math.floor(D / 10) == 0) {
									D = '0' + D
								}
								var d3 = Y + "-" + M + "-" + D;
								var timeData = '从：' + name + ' ' + queryTimeArray + '<br>至：' + d3 + ' ' + queryTimeArray + '<br>原报警数：' + seriesOldValue + '&nbsp;&nbsp;&nbsp;&nbsp;现报警数：' + seriesNowValue
								return timeData;
							}
						}
					}, {
						name: '报警数',
						type: 'bar',
						barWidth: '15',
						itemStyle: {
							normal: {
								color: '#3398DB',
							}
						},
						data: [],
						tooltip: {
							formatter: function(params) {
								var seriesOldValue = seriesOld[(params.dataIndex)][1];
								var name = seriesOld[(params.dataIndex)][0].replace(/\//g, "-");
								var d1 = Date.parse(new Date(seriesOld[(params.dataIndex)][0]));
								var d2 = d1 + (60 * 60 * 24 * 1000);
								var Y = (new Date(d2)).getFullYear();
								var M = (new Date(d2)).getMonth() + 1;
								var D = (new Date(d2)).getDate();
								if (Math.floor(M / 10) == 0) {
									M = '0' + M
								}
								if (Math.floor(D / 10) == 0) {
									D = '0' + D
								}
								var d3 = Y + "-" + M + "-" + D;
								var timeData = '从：' + name + ' ' + queryTimeArray + '<br>至：' + d3 + ' ' + queryTimeArray + '<br>原报警数：' + seriesOldValue + '&nbsp;&nbsp;&nbsp;&nbsp;现报警数：' + params.value[1]
								return timeData;
							}
						}
					}, {
						name: '报警值',
						type: 'line',
						yAxisIndex: 1,
						smooth: true,
						itemStyle: {
							normal: {
								color: '#1cb964',
							}
						},
						data: seriesAlarmValue,
						tooltip: {
							formatter: function(params) {
								var timeData = '报警时间：' + params.value[0] + ' <br> 报警值：' + params.value[1]
								return timeData;
							}
						}
					}, {
						name: '限值',
						type: 'line',
						yAxisIndex: 0,
						itemStyle: {
							normal: {
								color: '#ffc200'
							}
						},
						markLine: {
							lineStyle: {
								normal: {
									type: 'solid',
									color: '#ffc200'
								}
							},
							silent: true,
							label: {
								normal: {
									show: false
								}
							},
							data: [{
								name: '限值',
								yAxis: 0
							}]
						}
					}, {
						name: '修改值',
						yAxisIndex: 1,
						type: 'line',
						itemStyle: {
							normal: {
								color: '#ff6b5c'
							}
						},
						markLine: {
							silent: true,
							label: {
								normal: {
									show: true,
									position: 'middle',
								}
							},
							lineStyle: {
								normal: {
									type: 'solid',
									color: '#ff6b5c'
								}
							},
							data: [{
								name: '修改值',
								yAxis: alarmValue
							}]
						}
					}]
				};
				if (alarmFlagName == 'PVHH' || alarmFlagName == 'PVHI') {
					option.yAxis[0].inverse = false;
					option.yAxis[1].inverse = false;
					option.yAxis[1].nameLocation = 'end';
					option.yAxis[1].min = (limitValue == '') ? minValue : limitValue;
					option.yAxis[1].max = maxValue;
					option.xAxis[0].position = 'bottom';
					option.legend.top = '7%';
					option.grid.top = '20%'
				} else if (alarmFlagName == 'PVLO' || alarmFlagName == 'PVLL') {
					option.yAxis[0].inverse = true;
					option.yAxis[1].nameLocation = 'start';
					// option.yAxis[1].max = (limitValue == '') ? ((maxValue == minValue)?(maxValue-0.00001):maxValue) : limitValue;
					option.yAxis[1].max = (limitValue == '') ? maxValue : limitValue;
					// option.yAxis[1].min = minValue;
					option.xAxis[0].position = 'top';
					option.legend.top = '7%';
					option.grid.top = '14%';
				}
				if (limitValue == '') {
					option.legend.data[3] = {};
					option.series[3].markLine.data = []
				}
				option.series[0].data = seriesOld;
				option.series[1].data = seriesNow;
				AlarmFlagCharts.setOption(option);
			},
			/**
			 * 初始化图表 (报警标识除了PVHH、PVHI、PVLO、PVLL之外的其他的情况)
			 */
			initChartsOther: function(data1, data2) {
				if (AlarmFlagCharts && !AlarmFlagCharts.isDisposed()) {
					AlarmFlagCharts.clear();
					AlarmFlagCharts.dispose();
				}
				var histogramData = JSON.parse(data1);
				var lineChartData = JSON.parse(data2);
				var seriesAlarmValue = new Array();
				$.each(histogramData, function(i, el) {
					seriesNow[i] = [histogramData[i].key, histogramData[i].value.value];
				})
				$.each(lineChartData, function(index, ele) {
					seriesAlarmValue.push([index, ele])
				})
				AlarmFlagCharts = echarts.init(document.getElementById('AlarmFlagCharts'));
				option = {
					tooltip: {
						trigger: 'item', //axis
						axisPointer: { // 坐标轴指示器，坐标轴触发有效
							type: '' // 默认为直线，可选为：'line' | 'shadow'
						}
					},
					grid: {
						left: '1%',
						right: '3%',
						bottom: '1%',
						top: '16%',
						height: '230px',
						containLabel: true
					},
					legend: {
						left: '80px',
						top: '4%',
						itemWidth: 20, //设置icon大小
						itemHeight: 10, //设置icon大小
						data: [{
							name: '报警数',
							icon: 'roundRect',
							textStyle: {
								color: '#9d9d9d',
							}
						}, {
							name: '报警值',
							icon: '',
							textStyle: {
								color: '#9d9d9d',
							}
						}]
					},
					xAxis: [{
						type: 'time',
						minInterval: 3600 * 24 * 1000,
						maxInterval: 3600 * 24 * 1000,
						min: function(value) {
							return Date.parse(startTime.replace(/-/g, "/")) - 3600 * 15 * 1000;
						},
						max: function(value) {
							return Date.parse(endTime.replace(/-/g, "/")) + 3600 * 5 * 1000;
						},
						axisLine: {
							onZero: false
						},
						axisLabel: {
							showMinLabel: false,
							showMaxLabel: false,
							formatter: function(value, index) {
								var date = new Date(value);
								var y = date.getFullYear();
								var m = date.getMonth() + 1;
								var d = date.getDate();
								return y + '-' + m + '-' + d;
							}
						},
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						},
					}],
					yAxis: [{
						name: '报警数',
						type: 'value',
						min: 0,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}, {
						name: '报警值(' + unitFlag + ')',
						nameLocation: 'end',
						position: 'right',
						type: 'value',
						splitLine: { //网格线
							show: false,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}],
					series: [{
						name: '报警数',
						type: 'bar',
						barWidth: '15',
						itemStyle: {
							normal: {
								color: '#3398DB',
							}
						},
						data: [],
						tooltip: {
							formatter: function(params) {
								var name = params.value[0].replace(/\//g, "-");
								var d1 = Date.parse(new Date(params.value[0]));
								var d2 = d1 + (60 * 60 * 24 * 1000);
								var Y = (new Date(d2)).getFullYear();
								var M = (new Date(d2)).getMonth() + 1;
								var D = (new Date(d2)).getDate();
								if (Math.floor(M / 10) == 0) {
									M = '0' + M
								}
								if (Math.floor(D / 10) == 0) {
									D = '0' + D
								}
								var d3 = Y + "-" + M + "-" + D;
								var timeData = '从：' + name + ' ' + queryTimeArray + '<br>至：' + d3 + ' ' + queryTimeArray + '<br>报警数：' + params.value[1]
								return timeData;
							}
						}
					}, {
						name: '报警值',
						type: 'line',
						yAxisIndex: 1,
						smooth: true,
						itemStyle: {
							normal: {
								color: '#1cb964',
							}
						},
						data: seriesAlarmValue,
						tooltip: {
							formatter: function(params) {
								var timeData = '报警时间：' + params.value[0] + ' <br> 报警值：' + params.value[1]
								return timeData;
							}
						}
					}]
				};
				option.series[0].data = seriesNow;
				if (seriesAlarmValue.length == 0) {
					option.legend.data[1] = [];
					option.yAxis = [{
						name: '报警数',
						type: 'value',
						min: 0,
						splitLine: { //网格线
							show: true,
							lineStyle: {
								color: ['#e5e5e5'],
								type: 'solid'
							}
						}
					}];
					option.series[1].yAxisIndex = 0;
					option.xAxis[0].min = function(value) {
						return Date.parse(startTime.replace(/-/g, "/")) - 3600 * 15 * 1000;
					}
					option.xAxis[0].max = function(value) {
						return value.max + 3600 * 5 * 1000;
					}
				} else {
					option.yAxis[1].min = minValue;
					option.yAxis[1].max = maxValue;
					option.yAxis[1].name = '报警值(' + unitFlag + ')';
				}
				AlarmFlagCharts.setOption(option);
			},
			/**
			 * 保存
			 */
			save: function() {
				var shieldBefore = $('#shieldBefore').html();
				var limitBefore = $('#limitBefore').html();
				var priorityBefore = $('#priorityBefore').html();
				var shieldAfter = $('#shieldAfter').val();
				var shieldAfterName = $('#shieldAfter option:selected').text()
				var limitAfter = $('#limitAfter').val();
				var priority = $('#priority').val();
				var priorityName = $('#priority option:selected').text();
				var remarks = $('#remarks').val();
				if (shieldAfterName == '是' && shieldBefore == '是') {
					layer.confirm('报警点已屏蔽不能进行变更！', {
						btn: ['确定']
					});
					return false;
				} else if ((shieldAfterName == '否' && shieldBefore == '否' || shieldAfterName == '') && (limitAfter == limitBefore || limitAfter == '') && (priorityName == priorityBefore || priorityName == '') && remarks == '') {
					layer.confirm('变更内容未改变，无需变更！', {
						btn: ['确定']
					});
					return false;
				} else if ((shieldAfterName == '否' && shieldBefore == '否') || (shieldAfterName == '否' && shieldBefore == '是')) {
					if (limitAfter != limitBefore && limitAfter != '') {
						if (alarmFlagName == 'PVHH') {
							if (alarmPointHI != '') {
								if (limitAfter < alarmPointHI) {
									layer.confirm('PVHH（高高报）值小于PVHI（高报）值，是否继续保存？', {
										btn: ['是', '否']
									}, function(index) {
										layer.close(index)
										page.logic.saveData()
									}, function(index) {
										layer.close(index)
										return false;
									});
								} else {
									page.logic.saveData()
								}
							} else if (alarmPointLO != '') {
								if (limitAfter <= alarmPointLO) {
									layer.confirm('PVHH（高高报）值小于PVLI（低报）值，是否继续保存？', {
										btn: ['是', '否']
									}, function(index) {
										layer.close(index)
										page.logic.saveData()
									}, function(index) {
										layer.close(index)
										return false;
									});
								} else {
									page.logic.saveData()
								}
							} else if (alarmPointLL != '') {
								if (limitAfter <= alarmPointLO) {
									layer.confirm('PVHH（高高报）值小于PVLL（低低报）值，是否继续保存？', {
										btn: ['是', '否']
									}, function(index) {
										layer.close(index)
										page.logic.saveData()
									}, function(index) {
										layer.close(index)
										return false;
									});
								} else {
									page.logic.saveData()
								}
							} else {
								page.logic.saveData()
							}
						} else if (alarmFlagName == 'PVHI') {
							if (alarmPointLO != '') {
								if (limitAfter <= alarmPointLO) {
									layer.confirm('PVHI（高报）值小于PVLI（低报）值，是否继续保存？', {
										btn: ['是', '否']
									}, function(index) {
										layer.close(index)
										page.logic.saveData()
									}, function(index) {
										layer.close(index)
										return false;
									});
								} else {
									page.logic.saveData()
								}
							} else if (alarmPointLL != '') {
								if (limitAfter <= alarmPointLO) {
									layer.confirm('PVHI（高报）值小于PVLL（低低报）值，是否继续保存？', {
										btn: ['是', '否']
									}, function(index) {
										layer.close(index)
										page.logic.saveData()
									}, function(index) {
										layer.close(index)
										return false;
									});
								} else {
									page.logic.saveData()
								}
							} else {
								page.logic.saveData()
							}
						} else if (alarmFlagName == 'PVLI') {
							if (alarmPointLL != '') {
								if (limitAfter < alarmPointLL) {
									layer.confirm('PVLI（低报）值小于PVLL（低低报）值，是否继续保存？', {
										btn: ['是', '否']
									}, function(index) {
										layer.close(index)
										page.logic.saveData()
									}, function(index) {
										layer.close(index)
										return false;
									});
								} else {
									page.logic.saveData()
								}
							} else {
								page.logic.saveData()
							}
						} else {
							page.logic.saveData()
						}
					} else {
						page.logic.saveData();
					}
				} else {
					page.logic.saveData();
				}

			},
			/**
			 * 报存数据
			 */
			saveData: function() {
				var ajaxType = "POST";
				if (pageMode == PageModelEnum.NewAdd) {
					window.pageLoadMode = PageLoadMode.Reload;
				} else if (pageMode == PageModelEnum.Edit) {
					ajaxType = "PUT";
					addUrl = updateUrl;
					window.pageLoadMode = PageLoadMode.Refresh;
				}
				var obj = {
					'afterInSuppressed': $('#shieldAfter').val(),
					'afterLimitValue': $('#limitAfter').val(),
					'afterPriority': $('#priority').val(),
					'alarmFlagId': alarmFlagId,
					'alarmPointId': alarmPointId,
					'planDetailId': planDetailId == undefined ? '' : planDetailId,
					'planId': planId,
					'beforeInSuppressed': $('#shieldBefore').attr('dataId'),
					'beforeLimitValue': $('#limitBefore').html(),
					'beforePriority': $('#priorityBefore').attr('dataId'),
					'remark': $('#remarks').val()
				}
				var arr = new Array();
				arr.push(obj);
				var data = $.ET.toCollectionJson(arr);
				$.ajax({
					url: addUrl,
					type: ajaxType,
					async: true,
					data: JSON.stringify(data),
					dataType: "text",
					processData: false,
					contentType: "application/json;charset=utf-8",
					success: function(result, XMLHttpRequest) {
						if (result.indexOf('collection') < 0) {
							layer.msg("保存成功！",{
                                time: 1000
                            },function() {
                                parent.pageLoadMode = PageLoadMode.Refresh;
                                page.logic.closeLayer(true);
                            });
						} else {
							layer.msg(result.collection.error.message)
						}
					},
					error: function(result) {
						var errorResult = $.parseJSON(result.responseText);
						layer.msg(errorResult.collection.error.message);
					}
				});
			},
			/**
			 * 刷新详情列表
			 */
			refreshDetail: function() {
				var flag1 = page.logic.checkRules(limitValue, $('#alarmValue').val());
				if (alarmFlagName == 'PVHH' || alarmFlagName == 'PVHI') { // 高
					var flag2 = page.logic.checklimtArea('H', limitValue, maxValue, $('#alarmValue').val());
				} else if (alarmFlagName == 'PVLO' || alarmFlagName == 'PVLL') { // 低
					var flag2 = page.logic.checklimtArea('L', limitValue, minValue, $('#alarmValue').val());
				}
				var flag3 = page.logic.checkAreas($('#alarmValue').val());
				if (!flag1) {
					layer.confirm('修改值的小数位数不可超过5位！', {
						btn: ['确定']
					})
					return false;
				} else if (flag2 != true) {
					layer.confirm(flag2, {
						btn: ['确定']
					})
					return false;
				} else if (!flag3) {
					layer.confirm('该修改值不在工艺卡片值范围，是否继续？', {
						btn: ['是', '否']
					}, function(index) {
						layer.close(index)
						page.logic.changeAlarmDetail()
					}, function(index) {
						layer.close(index)
					});
				} else {
					page.logic.changeAlarmDetail()
				}
			},
			changeAlarmDetail: function() {
				$.ajax({
					url: getChangeAlarmDetailUrl,
					async: true,
					data: {
						unitId: unitId,
						dateJason: dateJason,
						alarmPointId: alarmPointId,
						alarmFlagId: alarmFlagId,
						alarmValue: $('#alarmValue').val()
					},
					dataType: "JSON",
					type: 'GET',
					success: function(result) {
						var res = $.ET.toObjectArr(result);
						if (res != null && res != undefined && res != '') {
							if (res.length > 0) {
								var nowAlarmTimes = res[0].nowAlarmTimes;
								var reduceRate = res[0].reduceRate;
								$('#nowAlarmTimes').html(nowAlarmTimes);
								$('#reduceRate').html(reduceRate + "%");
								alarmValue = $('#alarmValue').val();
								page.logic.setCharts(res[0].histogramData);
							}
						}
					},
					error: function(result) {
						var errorResult = $.parseJSON(result.responseText);
						layer.msg(errorResult.collection.error.message);
					}
				});
			},
			/**
			 * 重新渲染chart
			 */
			setCharts: function(histogramData) {
				var seriesOld = new Array();
				// var seriesNow = new Array();
				var histogramData = JSON.parse(histogramData);
				$.each(histogramData, function(i, el) {
					seriesNow[i] = [histogramData[i].key, histogramData[i].value.value];
				})
				option.series[1].data = seriesNow;
				option.series[4].markLine.data[0].yAxis = alarmValue;
				AlarmFlagCharts.setOption(option)
			},
			/**
			 * 填写工艺卡片值
			 */
			upAnddownLimitValue: function(downLimitValue, upLimitValue) {
				var flagUp, flagDown, valueUp, valueDowm;
				var str = '';
				if (upLimitValue != '{}' && downLimitValue != '{}') {
					var downValue = JSON.parse(downLimitValue);
					var upValue = JSON.parse(upLimitValue);
					if (upValue.key == 1) {
						flagUp = '<='
					} else if (upValue.key == 0) {
						flagUp = '<'
					}
					if (downValue.key == 1) {
						flagDown = '>='
					} else if (downValue.key == 0) {
						flagDown = '>'
					}
					str = downValue.value + '~' + upValue.value
				} else if (upLimitValue != '{}' && downLimitValue == '{}') {
					var upValue = JSON.parse(upLimitValue);
					if (upValue.key == 1) {
						flagUp = '<='
					} else if (upValue.key == 0) {
						flagUp = '<'
					}
					str = flagUp + upValue.value
				} else if (upLimitValue == '{}' && downLimitValue != '{}') {
					var downValue = JSON.parse(downLimitValue);
					if (downValue.key == 1) {
						flagDown = '>='
					} else if (downValue.key == 0) {
						flagDown = '>'
					}
					str = flagDown + downValue.value
				} else if (upLimitValue == '{}' && downLimitValue == '{}') {
					str = ''
				}
				return str;
			},
			/**
			 * 校验修改值的数值规则
			 */
			checkRules: function(limtStr, strVal) {
				r = '^-?\\d+(\\.\\d{0,5})?$'
				return (new RegExp(r)).test(strVal);
			},
			/**
			 * 校验报警值的数值是否在限值和报警值范围内
			 */
			checklimtArea: function(flag, limitValue, alarmValue, strVal) {
				if (flag == 'H') {
					if (limitValue == '') {
						limitValue = minValue;
						if (strVal >= limitValue && strVal <= alarmValue) {
							return true;
						} else {
							return '修改值须大于等于该位号的最小报警值，小于等于最大报警值！报警值范围为（' + limitValue + '（最小报警值）～' + alarmValue + '（最大报警值））'
						}
					} else {
						if (strVal >= limitValue && strVal <= alarmValue) {
							return true;
						} else {
							return '修改值须大于等于该位号的限值，小于等于最大报警值！报警值范围为（' + limitValue + '（限值）～' + alarmValue + '（最大报警值））'
						}
					}

				} else if (flag == 'L') {
					if (limitValue == '') {
						limitValue = maxValue;
						if (strVal <= limitValue && strVal >= alarmValue) {
							return true;
						} else {
							return '修改值须大于等于该位号的最小报警值，小于等于最大报警值！报警值范围为（' + alarmValue + '（最小报警值）～' + limitValue + '（最大报警值））'
						}
					} else {
						if (strVal <= limitValue && strVal >= alarmValue) {
							return true;
						} else {
							return '修改值须大于等于该位号的最小报警值，小于等于最大报警值！报警值范围为（' + alarmValue + '（最小报警值）～' + limitValue + '（限值））'
						}
					}
				}
			},
			/**
			 * 校验修改值的数值是否在工艺范围
			 */
			checkAreas: function(strVal) {
				var upValue = $('#upLimitValue').val();
				var downValue = $('#downLimitValue').val();
				var limitFlag, limitUp, limitDown;
				if (upValue != '{}' && downValue != '{}') {
					var upVal = JSON.parse(upValue);
					var downVal = JSON.parse(downValue);
					if (upVal.key == 1) {
						flagUp = '<=';
						limitUp = strVal <= upVal.value
					} else if (upVal.key == 0) {
						flagUp = '<';
						limitUp = strVal < upVal.value
					}
					if (downVal.key == 1) {
						flagDown = '>=';
						limitDown = strVal >= downVal.value
					} else if (downVal.key == 0) {
						flagDown = '>';
						limitDown = strVal > downVal.value
					}
					if (limitUp && limitDown) {
						limitFlag = true
					} else {
						limitFlag = false
					}
				} else if (upValue != '{}' && downValue == '{}') {
					var upVal = JSON.parse(upValue);
					if (upVal.key == 1) {
						flagUp = '<='
						limitFlag = strVal <= upVal.value
					} else if (upVal.key == 0) {
						flagUp = '<'
						limitFlag = strVal < upVal.value
					}
				} else if (upValue == '{}' && downValue != '{}') {
					var downVal = JSON.parse(downValue);
					if (downVal.key == 1) {
						flagDown = '>=';
						limitFlag = strVal >= downVal.value
					} else if (downVal.key == 0) {
						flagDown = '>';
						limitFlag = strVal > downVal.value
					}
				} else if (upValue == '{}' && downValue == '{}') {
					return true;
				}
				return limitFlag;
			},
			limitValueLength: function(limtStr) {
				if (limtStr != '') {
					if (String(limtStr).indexOf(".") == -1) { // 整数
						var n = 0;
					} else {
						var n = String(limtStr).split('.')[1].length
					}
				} else {
					n = 2
				}

				return n;
			},
			/**
			 * 关闭弹出层
			 */
			closeLayer: function(isRefresh) {
				parent.layer.close(index);
			},
			/**
			 * 设置日期插
			 */
			initTime: function() {
				OPAL.ui.initDateTimePicker({
					"ctrId": "alarmTime"
				})
			},
			/**
			 * 初始化查询 优先级
			 */
			initAlarmPriorityList: function() {
				OPAL.ui.getCombobox("priority", alarmPriorityListUrl, {
					selectValue: 1,
					async: false,
					data: {
						'isAll': false
					}
				}, function() {
					var a = $('#priority').html();
					var _html = '<option></option>';
					_html += a;
					$('#priority').html(_html)
				});
			},
			/**
			 * 初始化 是否屏蔽
			 */
			initInSuppressedList: function() {
				OPAL.ui.getCombobox("shieldAfter", getInSuppressedListUrl, {
					selectValue: 1,
					async: false,
					data: {
						'isAll': false
					}
				}, function() {
					var a = $('#shieldAfter').html();
					var _html = '<option></option>';
					_html += a;
					$('#shieldAfter').html(_html)
				});
			},
			/**
			 * 选择屏蔽选择框
			 */
			shieldChoose: function() {
				$('#shieldAfter').change(function() {
					var shieldVal = $('#shieldAfter option:selected').val();
					if (shieldVal == 1) {
						$('#limitAfter').val('');
						$('#limitAfter').prop('disabled', true);
						$('#priority').val('');
						$('#priority').prop('disabled', true);
					} else {
						$('#limitAfter').prop('disabled', false);
						$('#priority').prop('disabled', false);
					}
				})

			},
			/**
			 * 时间
			 */
			search: function() {
				var data = {
					"endDate": $("#alarmTime").val()
				}
				$.ajax({
					url: getTimeUrl,
					data: data,
					dataType: 'json',
					type: 'get',
					success: function(result) {
						alarmChangeItemArr = $.ET.toObjectArr(result);
						var obj = new Object();
						$.each(alarmChangeItemArr, function(i, el) {
							if (i != 4 && i != 5) {
								el.value = OPAL.util.strToDate(el.value).getTime();
							}
						})
						startTime = OPAL.util.dateFormat(new Date(alarmChangeItemArr[0].value), 'yyyy-MM-dd HH:mm:ss');
						endTime = OPAL.util.dateFormat(new Date(alarmChangeItemArr[1].value), 'yyyy-MM-dd HH:mm:ss');
						dateJason = JSON.stringify(alarmChangeItemArr)
						// 请求图表部分接口
						page.logic.initAlarmDetailPage(unitId, dateJason, alarmPointId, alarmFlagId);
					}
				})
			},
		}
	}
	page.init();
	window.page = page;
})