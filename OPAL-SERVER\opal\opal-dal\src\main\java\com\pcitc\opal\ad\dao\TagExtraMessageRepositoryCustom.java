package com.pcitc.opal.ad.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.pm.pojo.TagExtraMessage;

import java.util.List;

/*
 * TagExtraMessage实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_TagExtraMessageRepositoryCustom
 * 作       者：dageng.sun
 * 创建时间：2017/12/07 
 * 修改编号：1
 * 描       述：TagExtraMessage实体的Repository的JPA自定义接口 
 */
public interface TagExtraMessageRepositoryCustom {

	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-12-07
	 * @param alarmPointIds 报警点ID集合
	 * @return CommonResult 消息结果类
	 */
	CommonResult deleteTagExtraMessage(Long[] alarmPointIds);
	
	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-12-07
	 * @param tagExtraMessageEntity 附加信息实体
	 * @return 
	 * @return CommonResult 消息结果类
	 */
	CommonResult addTagExtraMessage(TagExtraMessage tagExtraMessageEntity);

	/**
	 * 批量插入报警点附加信息
	 *
	 * <AUTHOR> 2017-12-07
	 * @param list 报警点附加信息集合
	 */
	void batchInsert(List<TagExtraMessage> list);
}

