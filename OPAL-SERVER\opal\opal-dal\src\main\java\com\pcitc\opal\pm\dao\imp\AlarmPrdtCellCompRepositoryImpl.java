package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmPrdtCellCompRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmPrdtCellComp;
import com.pcitc.opal.pm.pojo.AlarmPriorityComp;
import com.pcitc.opal.pm.pojo.PrdtCell;
import org.apache.commons.lang.ArrayUtils;
import org.junit.Test;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.lang.reflect.Array;
import java.util.*;

/*
 * AlarmPrdtCellComp实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_AlarmPrdtCellCompRepositoryImpl
 * 作    者：jiangtao.xue
 * 创建时间：2018/04/04
 * 修改编号：1
 * 描    述：AlarmPrdtCellComp实体的Repository实现
 */
public class AlarmPrdtCellCompRepositoryImpl extends BaseRepository<AlarmPrdtCellComp, Long> implements AlarmPrdtCellCompRepositoryCustom {

    /**
     * 唯一性校验13
     *
     * @param alarmPrdtCellCompEntity 报警标识对照实体
     * @return 查询返回信息类
     * <AUTHOR> 2018-04-04
     */
    @Override
    public CommonResult alarmPrdtCellCompValidation(AlarmPrdtCellComp alarmPrdtCellCompEntity) {
        CommonResult commonResult = new CommonResult();
        try {
            //“OPC”编码、“源报警生产单元”联合唯一性校验，提示：“该OPC下源报警生产单元已存在！”
            StringBuilder hql = new StringBuilder(
                    "from AlarmPrdtCellComp t where t.companyId=:companyId and t.opcCodeId = :opcCodeId and t.prdtCellSource =:prdtCellSource " +
                            " and t.alarmPrdtCellCompId<>:alarmPrdtCellCompId");
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("opcCodeId", alarmPrdtCellCompEntity.getOpcCodeId());
            paramList.put("prdtCellSource", alarmPrdtCellCompEntity.getPrdtCellSource());
            paramList.put("alarmPrdtCellCompId", alarmPrdtCellCompEntity.getAlarmPrdtCellCompId() == null ? 0 : alarmPrdtCellCompEntity.getAlarmPrdtCellCompId());
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());

            long index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("该OPC下源报警生产单元已存在！");
            }

        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    /**
     * 新增报警标识对照
     *
     * @param alarmPrdtCellCompEntity 添加的实体
     * <AUTHOR> 2018-04-04
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult addAlarmPrdtCellComp(AlarmPrdtCellComp alarmPrdtCellCompEntity) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(alarmPrdtCellCompEntity);
            commonResult.setResult(alarmPrdtCellCompEntity);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 删除报警标识对照
     *
     * @param alarmPrdtCellCompIds 报警标识对照ID集合
     * @return 消息结果类
     * <AUTHOR> 2018-04-04
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult deleteAlarmPrdtCellComp(Long[] alarmPrdtCellCompIds) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            String hql = " from AlarmPrdtCellComp t where t.companyId=:companyId and t.alarmPrdtCellCompId in (:alarmPrdtCellCompIds)";
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> alarmPrdtCellCompIdsList = Arrays.asList(alarmPrdtCellCompIds);
            paramList.put("alarmPrdtCellCompIds", alarmPrdtCellCompIdsList);
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());

            TypedQuery<AlarmPrdtCellComp> query = getEntityManager().createQuery(hql, AlarmPrdtCellComp.class);
            this.setParameterList(query, paramList);
            List<AlarmPrdtCellComp> alarmPrdtCellCompList = query.getResultList();
            alarmPrdtCellCompList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 更新报警标识对照
     *
     * @param alarmPrdtCellCompEntity 报警标识对照实体
     * @return 消息结果类
     * <AUTHOR> 2018-04-04
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult updateAlarmPrdtCellComp(AlarmPrdtCellComp alarmPrdtCellCompEntity) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(alarmPrdtCellCompEntity);
            commonResult.setResult(alarmPrdtCellCompEntity);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    /**
     * 获取报警标识对照实体
     *
     * @param alarmPrdtCellCompId 报警标识对照ID
     * @return 报警标识对照实体
     * <AUTHOR>  2018-04-04
     */
    @Override
    public AlarmPrdtCellComp getSingleAlarmPrdtCellComp(Long alarmPrdtCellCompId) {
        try {
            return getEntityManager().find(AlarmPrdtCellComp.class, alarmPrdtCellCompId);
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取报警标识对照实体
     *
     * @param alarmPrdtCellCompIds 报警标识对照ID集合
     * @return 报警标识对照实体集合
     * <AUTHOR> 2018-04-04
     */
    @Override
    public List<AlarmPrdtCellComp> getAlarmPrdtCellComp(Long[] alarmPrdtCellCompIds) {
        try {
            // 查询字符串
            String hql = "from AlarmPrdtCellComp t ";
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (alarmPrdtCellCompIds.length > 0) {
                hql += " where t.companyId=:companyId  and t.alarmPrdtCellCompId in (:alarmPrdtCellCompIds)";
                List<Long> alarmPrdtCellCompIdsList = Arrays.asList(alarmPrdtCellCompIds);
                paramList.put("alarmPrdtCellCompIds", alarmPrdtCellCompIdsList);
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            TypedQuery<AlarmPrdtCellComp> query = getEntityManager().createQuery(hql, AlarmPrdtCellComp.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * 获取报警标识对照实体（分页）
     *
     * @param dcsCodeId DCS名称
     * @param opcCodeId OPC名称
     * @param prdtCellSource 源报警生产单元
     * @param unitCodes 装置编码集合
     * @param prdtCellIds   本系统生产单元
     * @param inUse   是否使用
     * @param page 翻页实现类
     * @return 报警标识对照实体（分页）
     * <AUTHOR> 2018-04-04
     */
    @Override
    public PaginationBean<AlarmPrdtCellComp> getAlarmPrdtCellComp(Long dcsCodeId,Long opcCodeId, String prdtCellSource, String[] unitCodes,
                                                                  Long[] prdtCellIds,Integer inUse, Pagination page) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from AlarmPrdtCellComp t left join fetch t.dcsCode d " +
                    " left join fetch t.opcCode o ");
            StringBuilder hqlWhere = new StringBuilder("where 1=1 and t.companyId=:companyId and o.companyId=:companyId and pc.companyId=:companyId ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();

            if (dcsCodeId != null) {
                hqlWhere.append("  and t.dcsCodeId = :dcsCodeId ");
                paramList.put("dcsCodeId", dcsCodeId);
            }
            if (opcCodeId != null) {
                hqlWhere.append("  and t.opcCodeId = :opcCodeId ");
                paramList.put("opcCodeId", opcCodeId);
            }
            // 源报警生产单元
            if (!StringUtils.isEmpty(prdtCellSource)) {
                hqlWhere.append("  and (t.prdtCellSource like :prdtCellSource)");
                paramList.put("prdtCellSource", "%" + this.sqlLikeReplaceNew(prdtCellSource) + "%");
            }

            // 装置
            hql.append(" inner join fetch t.prdtCell pc ");
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hqlWhere.append("and pc.unitId in (:unitIds) ");
                List<String> unitIdsList = Arrays.asList(unitCodes);
                paramList.put("unitIds", unitIdsList);
                // 生产单元
                if (unitCodes.length == 1 && ArrayUtils.isNotEmpty(prdtCellIds)) {
                    hqlWhere.append("and pc.prdtCellId in (:prdtCellIds) ");
                    List<Long> prdtCellIdsList = Arrays.asList(prdtCellIds);
                    paramList.put("prdtCellIds", prdtCellIdsList);
                }
            }

            if (inUse != null) {
                hqlWhere.append("  and t.inUse = :inUse ");
                paramList.put("inUse", inUse);
            }
            hqlWhere.append(" order by d.name,o.name, t.prdtCellSource");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            // 调用基类方法查询返回结果
            return this.findAll(page, hql.toString()+hqlWhere.toString(), paramList);
        } catch (Exception ex) {
            throw ex;
        }
    }
    /**
     *根据生产单元名称获取源报警生产单元实体
     *
     * <AUTHOR> 2018-04-17
     * @param prdtCellSource 源报警生产单元
     * @param opcCode opc编码
     * @param dcsCode dcs编码
     * @throws Exception 
     * @return AlarmPrdtCellComp  报警生产单元对照实体
     */
    public AlarmPrdtCellComp getPrdtCellInPrdtCellComp(String prdtCellSource,Long opcCode,Long dcsCode) throws Exception {
        String hql = "from AlarmPrdtCellComp t where t.companyId=:companyId and t.prdtCellSource =:prdtCellSource and t.opcCodeId =:opcCode and t.dcsCodeId =:dcsCode and t.inUse=1";
        //企业
        CommonProperty commonProperty = new CommonProperty();
        try {
            List<AlarmPrdtCellComp> alarmPrdtCellCompList = getEntityManager().createQuery(hql, AlarmPrdtCellComp.class)
                    .setParameter("prdtCellSource", prdtCellSource)
                    .setParameter("opcCode",opcCode)
                    .setParameter("dcsCode",dcsCode)
                    .setParameter("companyId",commonProperty.getCompanyId())
                    .getResultList();

            if(alarmPrdtCellCompList !=null && alarmPrdtCellCompList.size()>0){
                return alarmPrdtCellCompList.get(0);
            }else{
                throw new Exception("无数据");
            }

        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<DataServerMonVO> getDataServerMon(String unitCodes) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.SECOND, -18000);
        Date startTime1 = calendar.getTime();

        List<String> strList = Arrays.asList(unitCodes.split(","));
        StringBuilder hql = new StringBuilder(
                "select new com.pcitc.opal.pm.dao.imp.DataServerMonDTO(u.sname ,p.unitId,p.dcsCodeId ,p.opcCodeId,k.activeTime) from AlarmPrdtCellComp p ");
        hql.append("left join Unit u on u.stdCode = p.unitId ");
        hql.append("left join Check k on p.dcsCodeId=(CAST(k.dcsCode AS long)) and p.opcCodeId=(CAST(k.opcCode AS long)) ");
        hql.append("where p.unitId in(?1) ");
        hql.append("order by k.activeTime desc,u.sname");

        Query query =this.getEntityManager().createQuery(hql.toString());
        query.setParameter(1,strList);
        List<DataServerMonDTO> dataServerMonList = query.getResultList();
        List<DataServerMonVO> dataServerMonVOList = new ArrayList<>();
        dataServerMonList.forEach(d->{
            DataServerMonVO dataServerMonVO =new DataServerMonVO();
            dataServerMonVO.setSname(d.getsName());
            dataServerMonVO.setUnitCode(d.getUnitCode());
            if (d.getActiveTime()!=null){
                dataServerMonVO.setActiveTime(d.getActiveTime());
                if (d.getActiveTime().getTime()>=startTime1.getTime()){
                    dataServerMonVO.setState(1);
                }
                else dataServerMonVO.setState(0);
            }
            else dataServerMonVO.setState(0);
            dataServerMonVOList.add(dataServerMonVO);
        });
        return dataServerMonVOList;
    }

    /**
     * 根据“DCS名称”获取生产单元列表
     *
     * @param dcsCodeId DCS编码ID
     * @return 报警标识对照实体集合
     * <AUTHOR> 2018-04-04
     */
    @Override
    public List<PrdtCell> getPrdtCellByDcsName(Long dcsCodeId) {
        try {
            Map<String, Object> paramList = new HashMap<String, Object>();
            // 查询字符串
            String hql = "select p from UnMatchAlarmPoint t " +
                    "left join t.prdtCell p " +
                    "where 1=1";
            if (dcsCodeId !=null) {
                hql += "and t.dcsCode = (:dcsCode) ";
                paramList.put("dcsCode", dcsCodeId);
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
            hql += "and t.companyId=:companyId ";
            paramList.put("companyId",Long.valueOf(commonProperty.getCompanyId()));
            hql += "group by t.prdtCellId ";
            hql += "order by p.sortNum,p.sname ";
            TypedQuery<PrdtCell> query = getEntityManager().createQuery(hql, PrdtCell.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }
}
