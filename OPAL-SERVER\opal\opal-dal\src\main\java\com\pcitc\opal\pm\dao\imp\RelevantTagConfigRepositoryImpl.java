package com.pcitc.opal.pm.dao.imp;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.TypedQuery;

import com.pcitc.opal.common.CommonProperty;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.RelevantTagConfigRepositoryCustom;
import com.pcitc.opal.pm.pojo.RelevantTagConfig;
import com.pcitc.opal.pm.pojo.RelevantTagConfigDtl;

/*
 * RelevantTagConfig实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_RelevantTagConfigRepositoryImpl
 * 作    者：dageng.sun
 * 创建时间：2018/8/1
 * 修改编号：1
 * 描    述：RelevantTagConfig实体的Repository实现
 */
public class RelevantTagConfigRepositoryImpl extends BaseRepository<RelevantTagConfig, Long>
		implements RelevantTagConfigRepositoryCustom {

	/**
	 * 新增相关性位号配置
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfig 相关性位号配置实体
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addRelevantTagConfig(RelevantTagConfig relevantTagConfig) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			this.getEntityManager().persist(relevantTagConfig);
			commonResult.setResult(relevantTagConfig.getRelevantTagConfigId());
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		return commonResult;
	}
	
	/**
	 * 删除相关性位号配置实体
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigIds 相关性位号配置主键id集合
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteRelevantTagConfig(Long[] relevantTagConfigIds) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			List<Long> relevantTagConfigIdList = Arrays.asList(relevantTagConfigIds);
			
			String hqlDtl = " from RelevantTagConfigDtl t where t.relevantTagConfigId in (:relevantTagConfigIds)";
			Map<String, Object> paramDtlList = new HashMap<String, Object>();
			paramDtlList.put("relevantTagConfigIds", relevantTagConfigIdList);
			TypedQuery<RelevantTagConfigDtl> queryDtl = getEntityManager().createQuery(hqlDtl, RelevantTagConfigDtl.class);
			this.setParameterList(queryDtl, paramDtlList);
			List<RelevantTagConfigDtl> relevantTagConfigDtlList = queryDtl.getResultList();
			relevantTagConfigDtlList.forEach(x -> {
				this.getEntityManager().remove(x);
			});
			
			String hql = " from RelevantTagConfig t where t.relevantTagConfigId in (:relevantTagConfigIds)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("relevantTagConfigIds", relevantTagConfigIdList);
			TypedQuery<RelevantTagConfig> query = getEntityManager().createQuery(hql, RelevantTagConfig.class);
			this.setParameterList(query, paramList);
			List<RelevantTagConfig> relevantTagConfigList = query.getResultList();
			relevantTagConfigList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}
	
	/**
	 * 更新相关性位号配置
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigEntity 相关性位号配置实体
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult updateRelevantTagConfig(RelevantTagConfig relevantTagConfigEntity) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			getEntityManager().merge(relevantTagConfigEntity);
			commonResult.setResult(relevantTagConfigEntity);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("更新成功！");
		} catch (Exception ex) {
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}
	
	/**
	 * 获取相关性位号配置实体
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigId 相关性位号配置主键id
	 */
	@Override
	public RelevantTagConfig getSingleRelevantTagConfig(Long relevantTagConfigId) {
		try {
			StringBuilder hql = new StringBuilder("select t from RelevantTagConfig t ");
			hql.append("left join fetch t.alarmPoint ap ");
			hql.append("left join fetch ap.prdtCell pc ");
			hql.append("left join fetch ap.alarmPointType apt ");
			hql.append("left join fetch ap.measUnit mu where ap.companyId=:companyId and pc.companyId=:companyId and t.relevantTagConfigId=:relevantTagConfigId ");
//			hql.append(" and case when ae.alarm_point_id is not null then ap.in_use  end =1 ");
			hql.append(" and case when ap.alarmPointId is not null then ap.inUse  end =1 ");
			Map<String, Object> paramList = new HashMap<String, Object>();
			paramList.put("relevantTagConfigId", relevantTagConfigId);
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			TypedQuery<RelevantTagConfig> query = getEntityManager().createQuery(hql.toString(), RelevantTagConfig.class);
			this.setParameterList(query, paramList);
			return query.getSingleResult();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 获取相关性位号配置集合
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigIds 相关性位号配置主键id集合
	 */
	@Override
	public List<RelevantTagConfig> getRelevantTagConfig(Long[] relevantTagConfigIds) {
		try {
			// 查询字符串
			String hql = "from RelevantTagConfig t join fetch t.alarmPoint ";
			Map<String, Object> paramList = new HashMap<String, Object>();
			if (relevantTagConfigIds.length > 0) {
				hql += " where t.relevantTagConfigId in (:relevantTagConfigIds)";
				List<Long> relevantTagConfigIdsList = Arrays.asList(relevantTagConfigIds);
				paramList.put("relevantTagConfigIds", relevantTagConfigIdsList);
			}
			TypedQuery<RelevantTagConfig> query = getEntityManager().createQuery(hql, RelevantTagConfig.class);
			this.setParameterList(query, paramList);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 获取相关性位号配置实体（分页）
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param tag 主位号
	 * @param page 翻页对象
	 */
	@Override
	public PaginationBean<RelevantTagConfig> getRelevantTagConfig(String[] unitCodes, Long[] prdtCellIds, String tag,
			Pagination page) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("select t from RelevantTagConfig t ");
			hql.append("left join fetch t.alarmPoint ap ");
			hql.append("left join fetch ap.prdtCell pc ");
			hql.append("left join fetch ap.alarmPointType apt ");
			hql.append("left join fetch ap.measUnit mu where 1=1 and ap.companyId=:companyId and pc.companyId=:companyId ");
			hql.append(" and case when t.alarmPointId is not null then ap.inUse  end =1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			// 装置
			if (ArrayUtils.isNotEmpty(unitCodes)) {
				hql.append("and pc.unitId in (:unitIds) ");
				List<String> unitIdsList = Arrays.asList(unitCodes);
				paramList.put("unitIds", unitIdsList);
				// 生产单元
				if (unitCodes.length == 1 && ArrayUtils.isNotEmpty(prdtCellIds)) {
					hql.append("and ap.prdtCellId in (:prdtCellIds) ");
					List<Long> prdtCellIdsList = Arrays.asList(prdtCellIds);
					paramList.put("prdtCellIds", prdtCellIdsList);
				}
			}
			// 位号
			if (!StringUtils.isEmpty(tag)) {
				hql.append("and upper(ap.tag) like upper(:tag) escape '/' ");
				paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
			}
			hql.append("order by ap.tag");
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());

			// 调用基类方法查询返回结果
			PaginationBean<RelevantTagConfig> bean = this.findAll(page, hql.toString(), paramList);
			return bean;
		} catch (Exception ex) {
			throw ex;
		}
	}

}
