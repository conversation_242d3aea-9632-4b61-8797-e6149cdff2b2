package com.pcitc.opal.aa.bll.entity;

import lombok.Getter;
import lombok.Setter;

/*
 * 报警数量评估实体
 * 模块编号：pcitc_opal_bll_class_AlarmNumberAssessDataEntity
 * 作  　者：zheng.yang
 * 创建时间：2017-10-27
 * 修改编号：1
 * 描    述：报警数量评估实体
 */
@Getter
@Setter
public class AlarmNumberAssessDataEntity {

	public AlarmNumberAssessDataEntity(String tag, String alarmFlag, Long alarmCount, Integer priority,
									   String prdtCellName, String unitId) {
		this.tag = tag;
		this.alarmFlag = alarmFlag;
		this.alarmCount = alarmCount;
		this.priority = priority;
		this.prdtCellName = prdtCellName;
		this.unitId = unitId;
	}

	public AlarmNumberAssessDataEntity(){
	}

	/**
	 * id
	 */
	private String id;
	/**
	 * 位号
	 */
	private String tag;
	/**
	 * 报警标识
	 */
	private String alarmFlag;
	/**
	 * 报警数量
	 */
	private Long alarmCount;
	/**
	 * 百分比
	 */
	private String percent;
	/**
	 * 优先级(1紧急；2重要；3一般)
	 * 专业暂时也使用的是该字段
	 *  monitorType
	 */
    private Integer priority;
    /**
	 * 优先级名称
	 */
    private String priorityName;
	/**
	 * 装置名称
	 */
	private String name;
    /**
	 * 装置简称
	 */
    private String unitName;
    /**
	 * 生产单元
	 */
    private String prdtCellName;
    /**
	 * 报警总数
	 */
    private String sum;
    /**
	 * 报警平均数
	 */
    private String avg;
    /**
	 * 装置编码
	 */
    private String unitId;

	/**
	 * 报警标识id
	 */
	private Long alarmFlagId;

	/**
	 * 位置
	 */
	private String location;

	private String monitorTypeName;

	/**
	 *导出excel序号
	 */
	private Integer index;

}
