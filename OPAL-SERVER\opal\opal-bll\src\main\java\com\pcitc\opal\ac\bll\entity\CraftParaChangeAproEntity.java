package com.pcitc.opal.ac.bll.entity;

import java.util.Date;

public class CraftParaChangeAproEntity {
    /**
     * 工艺参数变更审批记录ID
     */
    private Long craftAproId;

    /**
     * 报警变更方案明细ID
     */
    private Long planDetailId;

    /**
     * 审批状态（0未提交；1已提交；2通过；3驳回）
     */
    private Integer aproStatus;

    /**
     * 审批意见
     */
    private String aproOpnion;

    /**
     * 审批时间
     */
    private Date aproTime;

    /**
     * 审批人ID
     */
    private String aproUserId;

    /**
     * 审批人名称
     */
    private String aproUserName;

    public Long getCraftAproId() {
        return craftAproId;
    }

    public void setCraftAproId(Long craftAproId) {
        this.craftAproId = craftAproId;
    }

    public Long getPlanDetailId() {
        return planDetailId;
    }

    public void setPlanDetailId(Long planDetailId) {
        this.planDetailId = planDetailId;
    }

    public Integer getAproStatus() {
        return aproStatus;
    }

    public void setAproStatus(Integer aproStatus) {
        this.aproStatus = aproStatus;
    }

    public String getAproOpnion() {
        return aproOpnion;
    }

    public void setAproOpnion(String aproOpnion) {
        this.aproOpnion = aproOpnion;
    }

    public Date getAproTime() {
        return aproTime;
    }

    public void setAproTime(Date aproTime) {
        this.aproTime = aproTime;
    }

    public String getAproUserId() {
        return aproUserId;
    }

    public void setAproUserId(String aproUserId) {
        this.aproUserId = aproUserId;
    }

    public String getAproUserName() {
        return aproUserName;
    }

    public void setAproUserName(String aproUserName) {
        this.aproUserName = aproUserName;
    }
}
