var searchUrl = OPAL.API.aaUrl + '/alarmPriorityAssess/getAllAlarmEvent';
var subUrl = OPAL.API.aaUrl + '/alarmPriorityAssess/getAlarmEventDtl';
var getAlarmPriorityAssessStatisticDatavUrl = OPAL.API.aaUrl + '/alarmPriorityAssess/getAlarmPriorityAssessStatisticData';
var getAlarmIsoUrl = OPAL.API.commUrl + '/getAlarmIso';
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var getShowTimeUrl = OPAL.API.commUrl + '/getShowTime';
var alarmFlagListUrl = OPAL.API.commUrl + '/getAlarmFlagList';
var alarmPriorityListUrl = OPAL.API.commUrl + '/getAlarmPriorityList';
var startTimeListUrl = OPAL.API.commUrl + '/getStartTimeList';
var getQueryTimeUrl = OPAL.API.commUrl + '/getQueryTime';
var startDate, endDate;
var priorityId; // 优先级
var priorityAssessReadOnlyFlag = false; // 是否只读优先级页面数据
var index;
var flag = false;
var standardEmergency,standardImportant,standardGeneral;//国际标准数据
var isLoading = true;
$(function() {
    if (priorityAssessReadOnlyFlag) {
        index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
    }
    var page = {
        /**
         * 初始化
         */
        init: function() {
            this.bindUi();
            //扩展日期插件
            OPAL.util.extendDate();
            // 初始化 报警时间的时间点
            page.logic.getQueryTime();
            // 根据日期时间计算开始时间和结束时间和一周之前的开始和结束时间
            page.logic.getShowTime();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化报警标识
            page.logic.initAlarmFlagList();
            //初始化优先级
            page.logic.initAlarmPriorityList();
            //初始化 发生时间
            page.logic.initStartTimeList();
            //初始化表格
            page.logic.initTable();
            // 初始化图表
            page.logic.initCharts();
            // 点击图表改变优先级
            page.logic.alarmChangeTable();

            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("AlarmPriorityAssess");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
            priorityAssessReadOnlyFlag = OPAL.util.getQueryParam("priorityAssessReadOnlyFlag")||false;
            //默认查询数据
            if(!priorityAssessReadOnlyFlag) {
                setTimeout(function () {
                    document.getElementById("search").click();
                }, 500);
            }
        },
        bindUi: function() {
            //选择发生时间
            $('#occurrenceTime').change(function() {
                page.logic.timeChange();
            });
            //查询
            $('#search').click(function() {
                flag = true;
                isLoading = false;
                page.logic.search();
            });
            // 关闭
            $('#closePage').click(function() {
                page.logic.closeLayer();
            })
            // 判断是否只读该页面
            if (!priorityAssessReadOnlyFlag) {
                $('#rowHandle').css('display', 'block');
                $('#rowReadOnly').css('display', 'none');
                $('#headerTitle1').css('display','block');
                $('#headerTitle2').css('display','none');
            } else {
                $('#rowHandle').css('display', 'none');
                $('#rowReadOnly').css('display', 'block');
                $('#headerTitle1').css('display','none');
                $('#headerTitle2').css('display','block');
            }
        },
        data: {
            // 设置查询参数
            param: {},
            subParam:{}
        },
        logic: {
            setData: function(data) {
                flag = true;
                $('#rowHandle').css('display', 'none');
                $('#rowReadOnly').css('display', 'block');
                $('.change-details-box').css('display', 'block');
                $('#headerTitle1').css('display','none');
                $('#headerTitle2').css('display','block');
                var unitName = data.unitName;
                var unitIds = [data.unitId];
                var startDate = data.startDate;
                var endDate = data.endDate;
                var startTime = data.startTime;
                var endTime = data.endTime;
                var endFlag = data.endFlag;
                priorityAssessReadOnlyFlag = true;
                index = parent.layer.getFrameIndex(window.name); //获取子窗口索引
                $('#unitName').html(unitName);
                $('#startDate').html(startDate);
                $('#endDate').html(endDate);
                page.data.param = {
                    unitIds: unitIds,
                    //startTime: OPAL.util.strToDate(startTime),
                    //endTime: OPAL.util.strToDate(endTime),
                    startTime: startTime,
                    endTime: endTime,
                    endFlag: endFlag,
                    now: Math.random()
                };
                var data = {
                    //startTime: OPAL.util.strToDate(startTime),
                    //endTime: OPAL.util.strToDate(endTime),
                    startTime: startTime,
                    endTime: endTime,
                    unitIds: unitIds,
                    endFlag: endFlag
                }
                page.logic.initCharts(data);
                $('#table').bootstrapTable('refreshOptions', {
                    url: searchUrl,
                    detailView: true,
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    },  {
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "位号",
                        field: 'alarmPointTag',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "参数名称",
                        field: 'alarmPointExplain',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "报警等级",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    },{
                        title: "数量",
                        field: 'count',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }],
                })
            },
            initTable: function() {
                OPAL.ui.initBootstrapTable("table", {
                    detailView: true,
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    },  {
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'center',
                        width: '120px'
                    }, {
                        title: "位号",
                        field: 'alarmPointTag',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "参数名称",
                        field: 'alarmPointExplain',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "报警等级",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    },{
                        title: "数量",
                        field: 'count',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }],
                    onExpandRow: function (index, row, $detail) {
                        page.data.subParam = page.data.param;
                        page.data.subParam.alarmPointTag = row['alarmPointTag'];
                        page.data.subParam.alarmFlagId = row['alarmFlagId'];
                        page.data.subParam.unitCode  = row['unitCode'];
                        page.data.subParam.prdtCellId  = row['prdtCellId'];
                        page.logic.initSubTable(index, row, $detail);
                    },
                    onLoadSuccess: function(data) {
                        var dataRows = data.rows;
                        // if(dataRows.length > 0){
                        //     $("#totalCounts").html(dataRows[0].totalAlarmNum);
                        //     // $("#aveCounts").html(dataRows[0].avgAlarmNum);
                        // }
                        //设置鼠标浮动提示
                        var tds = $('#table').find('tbody tr td');
                        $.each(tds, function(i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 初始化二级列表
             */
            initSubTable: function (index, row, $detail) {
                var subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                OPAL.ui.initBootstrapTable(subId, {
                    url: subUrl,
                    striped: true,
                    // pagination: false,
                        columns: [{
                            title: "序号",
                            formatter: function(value, row, index) {
                                var data = page.data.param;
                                var pageNumber = data.pageNumber;
                                var pageSize = data.pageSize;
                                return index + 1 + (pageNumber - 1) * pageSize;
                            },
                            rowspan: 1,
                            align: 'center',
                            width: '50px'
                        }, {
                            title: "报警时间",
                            field: 'alarmTime',
                            rowspan: 1,
                            align: 'center',
                            width: '150px',
                        }, {
                            title: "事件类型",
                            field: 'eventTypeName',
                            rowspan: 1,
                            align: 'center',
                            width: '100px'
                        }, {
                            title: "优先级",
                            field: 'priorityName',
                            rowspan: 1,
                            align: 'center',
                            width: '60px'
                        }, {
                            title: "调整前的值",
                            field: 'previousValue',
                            rowspan: 1,
                            align: 'center',
                            width: '100px'
                        }, {
                            title: "当前值",
                            field: 'nowValue',
                            rowspan: 1,
                            align: 'center',
                            width: '100px'
                        }, {
                            title: "报警值（限值）",
                            field: 'limitValue',
                            rowspan: 1,
                            align: 'center',
                            width: '105px'
                        }, {
                            title: "计量单位",
                            field: 'measUnitName',
                            rowspan: 1,
                            align: 'center',
                            width: '100px'
                        } ,{
                            title: "事件描述",
                            field: 'des',
                            rowspan: 1,
                            align: 'center',
                            width: '150px'
                        } ],
                    formatNoMatches: function () {
                        return "";
                    },
                }, page.logic.subQueryParams)
            },
            /**
             * 查询子表格参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            subQueryParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder,
                    now: Math.random()
                };
                return $.extend(page.data.subParam, param);
            },
            /**
             * 获得固定的时间点
             */
            getQueryTime: function() {
                $.ajax({
                    url: getQueryTimeUrl,
                    async: false,
                    data: '',
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function(result) {
                        var res = $.ET.toObjectArr(result);
                        queryTimeArray = res[0].value.split(':');
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            getShowTime: function(data) {
                $.ajax({
                    url: getShowTimeUrl,
                    async: false,
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function(result) {
                        var dataArr = $.ET.toObjectArr(result);
                        getStartTime = dataArr[0].value;
                        getEndTime = dataArr[1].value;
                        //设置时间插件
                        page.logic.initTime();
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 设置日期插件
             */
            initTime: function() {
                var myDate = new Date();
                var start = laydate.render({
                    elem: '#startTime', //指定元素
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd HH:mm:ss', //日期格式
                    value: getStartTime,
                    max: getEndTime, //最大日期
                });
                var end = laydate.render({
                    elem: '#endTime',
                    type: 'datetime',
                    trigger: 'click',
                    btns: ['clear', 'confirm'],
                    format: 'yyyy-MM-dd HH:mm:ss',
                    value: getEndTime,
                    max: getEndTime,
                });
                $('#startTime').attr('maxDate',getEndTime)
                $('#endTime').attr('maxDate',getEndTime)
            },
            /**
             * 更改发生时间
             */
            timeChange: function() {
                var _V = $('#occurrenceTime').val();
                $('#startTime').attr('disabled', true);
                $('#endTime').attr('disabled', true);
                switch (_V) {
                    case '1':
                        $('#startTime').val(getStartTime);
                        $('#endTime').val(getEndTime);
                        break;
                    case '2':
                        page.logic.setTime('d', -15);
                        break;
                    case '3':
                        page.logic.setTime('d', -30);
                        break;
                    case '4':
                        page.logic.setTime('d', -60);
                        break;
                    case '5':
                        page.logic.setTime('d', -90);
                        break;
                    case '6':
                        page.logic.setTime('d', -180);
                        break;
                    case '7':
                        page.logic.setTime('y', -1);
                        break;
                    default:
                        $('#startTime').attr('disabled', false);
                        $('#endTime').attr('disabled', false);
                        $('#startTime').val(getStartTime);
                        $('#endTime').val(getEndTime);
                        break;
                }
            },

            /**
             * 设置查询日期
             */
            setTime: function(str, num) {
                var myDate = new Date();
                var currentTime = new Date(getEndTime)
                laydate.render({
                    elem: '#startTime',
                    format: 'yyyy-MM-dd HH:mm:ss',
                    value: new Date(currentTime.dateAdd(str, num))
                });
                laydate.render({
                    elem: '#endTime',
                    format: 'yyyy-MM-dd HH:mm:ss',
                    value: getEndTime
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function(nodes) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $('#prdtCellIds').combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $('.textbox,.combo').css('background-color','');
                        } else {
                            $('#prdtCellIds').combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('.textbox-disabled').css('background-color','rgb(235, 235, 228)');
                        }
                    }
                }, false, function() {
                    $("#searchPrdt").combotree("checkAllNodes");
                });
            },
            /**
             * 搜索
             */
            search: function() {
                $('.alarmClick').removeClass('alarm-priority-add-class');
                var flag = OPAL.util.checkDateIsValid();
                if (!flag) {
                    return false;
                }
                page.data.param = OPAL.form.getData("searchForm");
                page.data.param.priority = -1;
                //page.data.param.startTime = OPAL.util.strToDate(page.data.param.startTime);
                //page.data.param.endTime = OPAL.util.strToDate(page.data.param.endTime);
                page.logic.initCharts(page.data.param);
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 初始化查询 报警标识
             */
            initAlarmFlagList: function() {
                OPAL.ui.getCombobox("alarmFlag", alarmFlagListUrl, {
                    async: false,
                    selectValue: -1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },
            /**
             * 初始化查询 优先级
             */
            initAlarmPriorityList: function() {
                $.ajax({
                    url: alarmPriorityListUrl,
                    async: false,
                    data: '',
                    dataType: "JSON",
                    contentType: "X-WWW-FORM-URLENCODED",
                    type: 'GET',
                    success: function(result) {
                        var res = $.ET.toObjectArr(result);
                        $('#generalBox').attr('dataId', res[2].key)
                        $('#emergencyBox').attr('dataId', res[0].key)
                        $('#importantBox').attr('dataId', res[1].key)
                    },
                    error: function(result) {
                        var errorResult = $.parseJSON(result.responseText);
                        layer.msg(errorResult.collection.error.message);
                    }
                })
            },
            /**
             * 初始化查询 发生时间
             */
            initStartTimeList: function() {
                OPAL.ui.getCombobox("occurrenceTime", startTimeListUrl, {
                    async: false,
                    selectValue: 0,
                }, null);
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function() {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化图表
             */
            initCharts: function(data) {
                if (data != undefined) {
                    $.ajax({
                        url: getAlarmPriorityAssessStatisticDatavUrl,
                        async: false,
                        data: data,
                        dataType: "JSON",
                        contentType: "X-WWW-FORM-URLENCODED",
                        type: 'GET',
                        success: function(res) {
                            var result = $.ET.toObjectArr(res);
                            if (result.length != 0) {
                                $('#sysEmergency').find('h3').html(result[0].sysEmergencyAlarmRate);
                                $('#sysImportant').find('h3').html(result[0].sysImportantAlarmRate);
                                $('#sysGeneral').find('h3').html(result[0].sysGeneralAlarmRate);
                                $('#totalAlarmEvents').val(result[0].totalAlarmEvents);
                                $("#totalCounts").html(result[0].totalAlarmEvents);
                                $('#modelGeneral').find('h3').html(result[0].normalRatio);
                                $('#modelImportant').find('h3').html(result[0].importanceRatio);
                                $('#modelEmergency').find('h3').html(result[0].emergencyRatio);
                            } else {
                                $('#sysEmergency').find('h3').html('0.00%');
                                $('#sysImportant').find('h3').html('0.00%');
                                $('#sysGeneral').find('h3').html('0.00%');
                                $('#modelGeneral').find('h3').html('0.00%');
                                $('#modelImportant').find('h3').html('0.00%');
                                $('#modelEmergency').find('h3').html('0.00%');
                                $('#totalAlarmEvents').val('0');
                            }
                            var totalAlarmEvents = $('#totalAlarmEvents').val();
                            var sysEmergency = $('#sysEmergency').find('h3').html(); //紧急系统现状
                            var sysImportant = $('#sysImportant').find('h3').html(); //重要系统现状
                            var sysGeneral = $('#sysGeneral').find('h3').html(); //一般系统现状
                            var modelGeneral = $('#modelGeneral').find('h3').html(); //一般模型现状
                            var modelImportant = $('#modelImportant').find('h3').html(); //重要模型现状
                            var modelEmergency = $('#modelEmergency').find('h3').html(); //紧急模型现状
                            function toNumberPoint(percent) {
                                var str = percent.replace("%", "");
                                str = str / 100;
                                return Number(str);
                            }
                            page.logic.setCharts("紧急报警", "datastyle", toNumberPoint(sysEmergency) * totalAlarmEvents, toNumberPoint(standardEmergency) * 100, Number(totalAlarmEvents),toNumberPoint(modelEmergency)* 100);
                            page.logic.setCharts("重要报警", "datastyle1", toNumberPoint(sysImportant) * totalAlarmEvents, toNumberPoint(standardImportant) * 100, Number(totalAlarmEvents),toNumberPoint(modelImportant)* 100);
                            page.logic.setCharts("一般报警", "datastyle2", toNumberPoint(sysGeneral) * totalAlarmEvents, toNumberPoint(standardGeneral) * 100, Number(totalAlarmEvents),toNumberPoint(modelGeneral)* 100);
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                } else {
                    $.ajax({
                        url: getAlarmIsoUrl,
                        async: false,
                        data: '',
                        dataType: "JSON",
                        contentType: "X-WWW-FORM-URLENCODED",
                        type: 'GET',
                        success: function(result) {
                            var res = $.ET.toObjectArr(result);
                            standardEmergency = res[0].value;
                            standardImportant = res[1].value;
                            standardGeneral = res[2].value;
                            $('#sysEmergency').find('h3').html('0.00%');
                            $('#standardEmergency').find('h3').html(standardEmergency);
                            $('#sysImportant').find('h3').html('0.00%');
                            $('#standardImportant').find('h3').html(standardImportant);
                            $('#sysGeneral').find('h3').html('0.00%');
                            $('#standardGeneral').find('h3').html(standardGeneral);
                            $('#totalAlarmEvents').val('0');
                            $('#modelGeneral').find('h3').html('0.00%');
                            $('#modelImportant').find('h3').html('0.00%');
                            $('#modelEmergency').find('h3').html('0.00%');
                            var totalAlarmEvents = $('#totalAlarmEvents').val();
                            var sysEmergency = $('#sysEmergency').find('h3').html();
                            var sysImportant = $('#sysImportant').find('h3').html();
                            var sysGeneral = $('#sysGeneral').find('h3').html();
                            var modelGeneral = $('#modelGeneral').find('h3').html(); //一般模型现状
                            var modelImportant = $('#modelImportant').find('h3').html(); //重要模型现状
                            var modelEmergency = $('#modelEmergency').find('h3').html(); //紧急模型现状
                            function toNumberPoint(percent) {
                                var str = percent.replace("%", "");
                                str = str / 100;
                                return Number(str);
                            }
                            page.logic.setCharts("紧急报警", "datastyle", toNumberPoint(sysEmergency) * totalAlarmEvents, toNumberPoint(standardEmergency) * 100, Number(totalAlarmEvents),toNumberPoint(modelEmergency)* 100);
                            page.logic.setCharts("重要报警", "datastyle1", toNumberPoint(sysImportant) * totalAlarmEvents, toNumberPoint(standardImportant) * 100, Number(totalAlarmEvents),toNumberPoint(modelImportant)* 100);
                            page.logic.setCharts("一般报警", "datastyle2", toNumberPoint(sysGeneral) * totalAlarmEvents, toNumberPoint(standardGeneral) * 100, Number(totalAlarmEvents),toNumberPoint(modelGeneral)* 100);
                        },
                        error: function(result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }
            },
            setCharts: function(text, chartId, sysData, standData, total,modelData) {
                var myChart = echarts.init(document.getElementById(chartId));
                option = {
                    tooltip: {
                        show: false,
                    },
                    graphic: {
                        type: 'text',
                        left: 'center',
                        top: 'center',
                        style: {
                            text: text,
                            textAlign: 'center',
                            fill: '#313131',
                            width: 30,
                            height: 30
                        }
                    },
                    series: [ {
                        type: 'pie',
                        radius: ['72%', '82%'],
                        color: ['#2ac70b', '#d9d9d9'],
                        hoverAnimation: false,
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderWidth: 6
                          },
                        label: {
                            normal: {
                                show: true,
                                position: 'right'
                            },
                        },
                        labelLine: {
                            normal: {
                                show: false,
                            }
                        },
                        data: [{
                            value: 0,
                        }, {
                            value: 1
                        }],
                    }, {
                        type: 'pie',
                        radius: ['60%', '70%'],
                        color: ['#f59c1a', '#d9d9d9'],
                        hoverAnimation: false,
                        avoidLabelOverlap: false,
                        label: {
                            normal: {
                                show: true,
                                position: 'left'
                            },
                        },
                        labelLine: {
                            normal: {
                                show: false
                            }
                        },
                        data: [{
                            value: 0,
                        }, {
                            value: 1,
                        }],

                    }, {
                        type: 'pie',
                        radius: ['48%', '58%'],
                        color: ['#3590e2', '#d9d9d9'],
                        hoverAnimation: false,
                        avoidLabelOverlap: true,
                        label: {
                            normal: {
                                show: false,
                                position: 'right'
                            },
                        },
                        labelLine: {
                            normal: {
                                show: false,
                            }
                        },
                        data: [{
                            value: 0,
                        }, {
                            value: 1
                        }],
                    }]
                };
                myChart.setOption(option);
                if(total == 0){
                    total = 1
                }
                myChart.setOption({
                    series: [
                        {
                            data: [{
                                value: modelData,
                            }, {
                                value: (100 - modelData)
                            }],
                        },{
                        data: [{
                            value: sysData,
                        }, {
                            value: (total - sysData),
                        }],
                    }, {
                        data: [{
                            value: standData,
                        }, {
                            value: (100 - standData)
                        }],
                    }]
                })
            },
            /**
             * 校验开始时间和结束时间
             */
            checkTime: function(startTime, endTime) {
                var time1 = new Date(startTime).getTime();
                var time2 = new Date(endTime).getTime();
                var timeDifference = Math.floor((time2 - time1) / 86400000) > 365;
                var flag = true;
                if (startTime == '' || endTime == '') {
                    layer.msg('开始时间和结束时间不能为空！')
                    flag = false;
                }
                if (new Date(startTime) > new Date(endTime)) {
                    layer.msg('开始时间不能大于结束时间！')
                    flag = false;
                }
                if (timeDifference) {
                    layer.msg('查询时间范围不能超过365天！')
                    flag = false;
                }
                return flag;
            },
            alarmChangeTable: function() {
                $('.alarmClick').click(function() {
                    if(flag){
                        $('.alarmClick').removeClass('alarm-priority-add-class');
                        $(this).addClass('alarm-priority-add-class');
                        var dataId = $(this).attr('dataId');
                        switch (dataId) {
                            case '1':
                                priorityId = 1;
                                break;
                            case '2':
                                priorityId = 2;
                                break;
                            case '3':
                                priorityId = 3;
                                break;
                            default:
                                priorityId = -1;
                        }
                        page.data.param.priority = priorityId;
                        $("#table").bootstrapTable('refresh', {
                            "url": searchUrl,
                            "pageNumber": 1
                        });
                    }
                })
            },
            /**
             * 关闭弹出层
             */
            closeLayer: function(isRefresh) {
                parent.layer.close(index);
            }
        }
    };
    page.init();
    window.page = page;
});