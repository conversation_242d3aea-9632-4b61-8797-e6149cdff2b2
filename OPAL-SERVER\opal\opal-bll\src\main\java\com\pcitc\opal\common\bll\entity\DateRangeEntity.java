package com.pcitc.opal.common.bll.entity;


import java.io.Serializable;
import java.util.Date;

/*
 * 临时表日期时间区间实体
 * 模块编号：pcitc_opal_bll_class_DateRange
 * 作       者：xuelei.wang
 * 创建时间：2017/11/28
 * 修改编号：1
 * 描       述：临时表日期时间区间实体
 */
@SuppressWarnings({"serial"})
public class DateRangeEntity implements Serializable {

    public DateRangeEntity(Date startTime, Date endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }

    public DateRangeEntity() {
    }

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}
