package com.pcitc.opal.ad.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AlarmAnlyRecExportVO implements Serializable {

    /**
     * 分析状态
     */
    private Integer anlyStatus;
    /**
     * 报警时间
     */
    private Date alarmTime;
    /**
     * 车间
     */
    private String workshopName;
    /**
     * 装置
     */
    private String unitName;
    /**
     * 生产单元
     */
    private String prdtCellName;
    /**
     * 参数描述
     */
    private String tagDes;
    /**
     * 参数位号
     */
    private String tag;
    /**
     * 计量单位
     */
    private String measUnitName;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 报警点高高报
     */
    private Double alarmPointHH;
    /**
     * 报警点高报
     */
    private Double alarmPointHI;
    /**
     * 报警点低报
     */
    private Double alarmPointLO;
    /**
     * 报警点低低报
     */
    private Double alarmPointLL;
    /**
     * 专业
     */
    private Integer monitorType;
    /**
     * 报警等级
     */
    private String alarmFlagName;
    /**
     * 原因类型
     */
    private Long reasonType;
    /**
     * 原因
     */
    private String alarmReasonName;
    /**
     * 原因分析及处置措施
     */
    private String reasonDes;
    /**
     *创建时间
     */
    private Date crtTime;
    /**
     *创建人名称
     */
    private String crtUserName;
    /**
     *提交时间
     */
    private Date submitTime;
    /**
     *提交人名称
     */
    private String submitUserName;

}
