package com.pcitc.opal.ad.dao;

import com.pcitc.opal.pm.pojo.AlarmEventH1Cache;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * AlarmEventH1Cache实体的Repository的JPA标准接口
 * 模块编号：pcitc_opal_dal_interface_AlarmEventH1CacheRepository
 * 作       者：zheng.yang
 * 创建时间：2019/08/01
 * 修改编号：1
 * 描       述：AlarmEventH1Cache实体的Repository实现
 */
public interface AlarmEventH1CacheRepository extends JpaRepository<AlarmEventH1Cache,Long>,AlarmEventH1CacheRepositoryCustom {
}
