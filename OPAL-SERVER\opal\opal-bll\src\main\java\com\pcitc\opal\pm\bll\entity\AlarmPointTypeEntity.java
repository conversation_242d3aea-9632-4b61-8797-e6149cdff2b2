package com.pcitc.opal.pm.bll.entity;
import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 报警点类型实体
 * 模块编号：pcitc_bll_class_AlarmPointTypeEntity
 * 作       者：xuelei.wang
 * 创建时间：2017/10/09
 * 修改编号：1
 * 描       述：报警点类型实体
 */
public class AlarmPointTypeEntity  extends BasicEntity {
	/**
	 * 报警点类型ID
	 */
	private Long alarmPointTypeId;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 排序
	 */
	private Integer sortNum;

	/**
	 * 描述
	 */
	private String des;

	public Long getAlarmPointTypeId() {
		return alarmPointTypeId;
	}

	public void setAlarmPointTypeId(Long alarmPointTypeId) {
		this.alarmPointTypeId = alarmPointTypeId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getSortNum() {
		return sortNum;
	}

	public void setSortNum(Integer sortNum) {
		this.sortNum = sortNum;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}

}