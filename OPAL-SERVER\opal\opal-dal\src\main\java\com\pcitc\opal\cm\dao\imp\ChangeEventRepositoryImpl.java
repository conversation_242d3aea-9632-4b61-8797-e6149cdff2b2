package com.pcitc.opal.cm.dao.imp;

import com.pcitc.opal.cm.dao.ChangeEventRepositoryCustom;
import com.pcitc.opal.cm.pojo.ChangeEvent;
import com.pcitc.opal.cm.pojo.ChangeEventEntity;
import com.pcitc.opal.cm.pojo.ChangeMonitoringChartEntity;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.dao.BaseRepository;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;

import javax.persistence.Query;
import java.util.*;

import static com.pcitc.opal.common.ObjectUtil.*;

/*
 * ChangeEvent实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_ChangeEventRepositoryImpl
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：ChangeEvent实体的Repository实现
 */
public class ChangeEventRepositoryImpl extends BaseRepository<ChangeEvent, Long> implements ChangeEventRepositoryCustom {


    @Override
    public List<ChangeEventCondition> getChangeEventInfo(Long comId) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, -30);
        Date startTime1 = calendar.getTime();

        StringBuilder hql = new StringBuilder(
                "select new com.pcitc.opal.cm.dao.imp.ChangeEventCondition(t.changeEventId,t.companyId,o.unitcode,o.crafttag,g.name,t.startTime,t.nowValue) from ChangeEvent t ");
        hql.append("left join AlarmPointTagComp o on o.alarmPointId = t.alarmPointId ");
        hql.append("left join AlarmFlag g on g.alarmFlagId = t.alarmFlagId ");
        hql.append("where ");
        //hql.append("t.companyId =:comId");
        //hql.append("and o.companyId =:comId");
        hql.append("(t.status =:three or t.status =:one) and t.startTime>=:time");

        Query query =this.getEntityManager().createQuery(hql.toString());
        //comId参数暂无用
        //query.setParameter("comId",comId);
        query.setParameter("three",3);
        query.setParameter("one",1);
        query.setParameter("time",startTime1);

        return query.getResultList();
    }

    @Override
    public ChangeEvent getChangeEventInfoById(Long Id) {
        try {
            return getEntityManager().find(ChangeEvent.class, Id);
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public CommonResult updateChangeEventInfo(ChangeEvent changeEvent) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(changeEvent);
            commonResult.setResult(changeEvent);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    @Override
    public List<ChangeMonitoringChartEntity> selectStatusNumByUnitIds(String[] unitIds, Long[] prdtCellIds, String tag, Integer status, Date startTime, Date endTime) {

        CommonProperty commonProperty = new CommonProperty();
        Integer companyId = commonProperty.getCompanyId();
        StringBuilder sql = new StringBuilder("");

        sql.append("select t2.sname, status, count(1) num\n" +
                "from (select *\n" +
                "      from t_cm_changeevent\n" +
                "      where start_time between :startTime and :endTime\n" +
                "     ) t1\n" +
                "         inner join t_pm_unit t2 on t1.unit_code = t2.std_code\n" +
                "         inner join t_pm_alarmpoint tpa on t1.alarm_point_id = tpa.alarm_point_id " +
                " where t1.company_id = :companyId" +
                " and t2.company_id = :companyId" +
                " and tpa.company_id = :companyId " +
                " and t2.in_use = 1 \n" +
                " and tpa.in_use = 1 ");

        HashMap<String, Object> param = new HashMap<>();

        param.put("companyId", new CommonProperty().getCompanyId());

        //过滤生产单元
        if (ArrayUtils.isNotEmpty(prdtCellIds)){
            sql.append(" and t1.prdtcell_id in (:prdtcellId) ");
            param.put("prdtcellId", Arrays.asList(prdtCellIds));
        }

        //过滤装置
        if (ArrayUtils.isNotEmpty(unitIds)){
            sql.append(" and t2.std_code in (:unitId) ");

            param.put("unitId", Arrays.asList(unitIds));
        }

        //过滤状态
        if (status != null){
            sql.append(" and t1.status = :status");
            param.put("status", status);
        }

        //过滤位号
        if(StringUtils.isNotEmpty(tag)){
            sql.append(" and (case\n" +
                    "           when t1.alarm_point_id is null\n" +
                    "               then tpa.tag like :tag\n" +
                    "           else t1.tag like :tag end\n" +
                    "    ) ");
            param.put("tag", "%" + tag + "%");
        }

        //过滤开始结束时间
        param.put("startTime", DateFormatUtils.format(startTime, "yyyy-MM-dd HH:mm:ss"));
        param.put("endTime", DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss"));

        //分组
        sql.append(" group by sname, status ");
        Query nativeQuery = this.getEntityManager().createNativeQuery(sql.toString());


        this.setParameterList(nativeQuery, param);
        nativeQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> resultList = nativeQuery.getResultList();

        HashMap<String, ChangeMonitoringChartEntity> chartEntityHashMap = new HashMap<>();
        for (Map<String, Object> map : resultList) {

            String sname = map.get("sname").toString();
            if (chartEntityHashMap.get(sname) == null) {
                ChangeMonitoringChartEntity changeMonitoringChartEntity = new ChangeMonitoringChartEntity();
                if (map.get("status").equals(2)){
                    changeMonitoringChartEntity.setLegalStatusNum(ObjectUtil.objectToInteger(map.get("num")));
                } else if (map.get("status").equals(1)) {
                    changeMonitoringChartEntity.setNotLegalStatusNum(ObjectUtil.objectToInteger(map.get("num")));
                } else if (map.get("status").equals(3)) {
                    changeMonitoringChartEntity.setUnknownStatusNum(ObjectUtil.objectToInteger(map.get("num")));
                }
                changeMonitoringChartEntity.setUnitName(ObjectUtil.objectToString(map.get("sname")));
                chartEntityHashMap.put(sname, changeMonitoringChartEntity);
            }else{
                ChangeMonitoringChartEntity changeMonitoringChartEntity = chartEntityHashMap.get(sname);
                if (map.get("status").equals(2)){
                    changeMonitoringChartEntity.setLegalStatusNum(ObjectUtil.objectToInteger(map.get("num")));
                } else if (map.get("status").equals(1)) {
                    changeMonitoringChartEntity.setNotLegalStatusNum(ObjectUtil.objectToInteger(map.get("num")));
                } else if (map.get("status").equals(3)) {
                    changeMonitoringChartEntity.setUnknownStatusNum(ObjectUtil.objectToInteger(map.get("num")));
                }
                chartEntityHashMap.put(sname, changeMonitoringChartEntity);
            }

        }

        ArrayList<ChangeMonitoringChartEntity> returnList = new ArrayList<>();
        returnList.addAll(chartEntityHashMap.values());
        return returnList;
    }

    @Override
    public PaginationBean<ChangeEventEntity> selectAllByUnitAndStartTime(String[] unitIds, Long[] prdtCellIds, String tag, Integer status, Date startTime, Date endTime, Pagination page) {

        CommonProperty commonProperty = new CommonProperty();
        Integer companyId = commonProperty.getCompanyId();
        StringBuilder sql = new StringBuilder("");

        sql.append("select t.craft_change_info_id,\n" +
                "       update_time,\n" +
                "       t2.sname                                unitName,\n" +
                "       tpp.sname                               prdtcellName,\n" +
                "       (case when t.tag is null then tpa.tag else t.tag end) tag,\n" +
                "       tpa.location des,\n" +
                "       taa.name alarmFlag,\n" +
                "       CONCAT(tpm.NAME, '(', tpm.SIGN, ')') AS measuringUnit,\n" +
                "       t.now_value,\n" +
                "       t.status," +
                "       t.start_time ,craf.sn " +
                "from (select * " +
                "      from t_cm_changeevent  " +
                "       where start_time between :startTime and :endTime \n" +
                "                 ) t\n" +
                "         inner join t_pm_alarmpoint tpa on t.alarm_point_id = tpa.alarm_point_id\n" +
                "         left join T_PM_MEASUNIT tpm on tpa.MEASUNIT_ID = tpm.MEASUNIT_ID\n" +
                "         left join t_pm_prdtcell tpp on t.prdtcell_id = tpp.prdtcell_id\n" +
                "         inner join t_pm_unit t2 on t.unit_code = t2.std_code\n" +
                "         left join t_ad_alarmflag taa on t.alarm_flag_id = taa.alarm_flag_id\n" +
                " left join t_cm_craftchangeinfo craf on t.craft_change_info_id = craf.craft_change_info_id " +
                "where t.company_id = :companyId " +
                "and tpa.in_use = 1\n" +
                "and t2.in_use = 1 \n");

        HashMap<String, Object> param = new HashMap<>();
        //过滤企业id
        param.put("companyId", new CommonProperty().getCompanyId());


        //过滤生产单元
        if (ArrayUtils.isNotEmpty(prdtCellIds)){
            sql.append(" and t.prdtcell_id in (:prdtcellId) ");
            param.put("prdtcellId", Arrays.asList(prdtCellIds));
        }

        //过滤装置
        if (ArrayUtils.isNotEmpty(unitIds)){
            sql.append(" and t2.std_code in (:unitId) ");

            param.put("unitId", Arrays.asList(unitIds));
        }

        //过滤状态
        if (status != null){
            sql.append(" and t.status = :status");
            param.put("status", status);
        }

        //过滤位号
        if(StringUtils.isNotEmpty(tag)){
            sql.append(" and (case\n" +
                    "           when t.alarm_point_id is null\n" +
                    "               then tpa.tag like :tag\n" +
                    "           else t.tag like :tag end\n" +
                    "    ) ");
            param.put("tag", "%" + tag + "%");
        }

        //过滤开始结束时间
        param.put("startTime", DateFormatUtils.format(startTime, "yyyy-MM-dd HH:mm:ss"));
        param.put("endTime", DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss"));

        sql.append(" order by status,start_time desc");

        Query count = this.getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(count, param);
        Integer resultCount = count.getResultList().size();

        Query nativeQuery = this.getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(nativeQuery, param);

        //分页对象
        PaginationBean<ChangeEventEntity> paginationBean = new PaginationBean(page, Long.valueOf(resultCount));
        //设置分页值
        nativeQuery.setFirstResult(paginationBean.getBeginIndex()).setMaxResults(paginationBean.getPageSize());

        //设置返回对象为map
        nativeQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);

        List<Map<String, Object>> resultList = nativeQuery.getResultList();

        List<ChangeEventEntity> changeEventEntities = new ArrayList<>();

        for (Map<String, Object> map : resultList) {
            ChangeEventEntity changeEventEntity = new ChangeEventEntity();
            changeEventEntity.setCraftChangeInfoId(objectToInteger(map.get("craft_change_info_id")));
            changeEventEntity.setUpdateTime(objectToDate(map.get("update_time")));
            changeEventEntity.setUnitName(objectToString(map.get("unitName")));
            changeEventEntity.setPrdtcellName(objectToString(map.get("prdtcellName")));
            changeEventEntity.setTag(objectToString(map.get("tag")));
            changeEventEntity.setDes(objectToString(map.get("des")));
            changeEventEntity.setAlarmFlag(objectToString(map.get("alarmFlag")));
            changeEventEntity.setMeasuringUnit(objectToString(map.get("measuringUnit")));
            changeEventEntity.setValue(objectToString(map.get("now_value")));
            changeEventEntity.setStatus(objectToInteger(map.get("status")));
            changeEventEntity.setStartTime(objectToDate(map.get("start_time")));
            changeEventEntity.setSn(objectToString(map.get("sn")));
            changeEventEntities.add(changeEventEntity);
        }

        paginationBean.setPageList(changeEventEntities);

        return paginationBean;
    }
}
