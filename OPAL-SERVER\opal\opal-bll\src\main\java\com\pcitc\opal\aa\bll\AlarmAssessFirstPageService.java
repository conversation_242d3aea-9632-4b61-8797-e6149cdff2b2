package com.pcitc.opal.aa.bll;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pcitc.opal.aa.bll.entity.*;
import com.pcitc.opal.ad.vo.WorkshopResponseNumberVO;
import com.pcitc.opal.ad.vo.UnitResponseNumberVO;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/*
 * 评估首页逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmAssessFirstPageService
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/17
 * 修改编号：1
 * 描    述：评估首页逻辑层接口
 */
@Service
public interface AlarmAssessFirstPageService {
    /**
     * 获取评估首页实体集合
     *
     * @param dateTime 发生时间
     * @return 评估首页实体集合
     * <AUTHOR> 2017-10-17
     */
    AlarmAssessFirstPageEntity getAlarmAssessFirstPage(Date dateTime) throws Exception;

    /**
     * 获取评估首页实体集合————工艺参数报警率————平均报警数专用
     *
     * @param startDate 发生时间
     * @param endDate 结束时间
     * @return 评估首页实体集合
     * <AUTHOR> 2017-10-17
     */
    AlarmAssessFirstPageEntity getAlarmAssessFirstPage(Date startDate, Date endDate, String[] unitStdCodes) throws Exception;

    /**
     * 获取评估首页实体集合
     *
     * @param startDate 发生时间
     * @param endDate 结束时间
     * @return 评估首页实体集合
     * <AUTHOR> 2017-10-17
     */
    AlarmAssessFirstPageEntity getAlarmAssessFirstPageAve(Date startDate, Date endDate, String[] unitStdCodes) throws Exception;

    /**
     * 获取评估等级数据
     *
     * @param isAll 是否显示全部
     * @return 评估等级结果集
     * <AUTHOR> 2017-10-23
     */
    List<DictionaryEntity> getAssessLevel(boolean isAll);

    /**
     * 获取等级评估详情
     *
     * <AUTHOR> 2017-10-24
     * @param dateJason 时间集合
     * @param assessLevelJason 评估等级装置ID
     * @param assessLevel 评估等级
     * @return 等级评估详情页面实体
     */
    AlarmLevelAssessDetailEntity getAlarmLevelAssessDetail(String dateJason, String assessLevelJason, Long assessLevel) throws Exception;

    /***
     * 获取装置的优化效果评估
     *
     * @param unitCode   装置编码
     * @param startTime 查询开始时间
     * @param endTime   查询结束时间
     * @return
     * <AUTHOR> 2017-11-22
     */
    GridViewEntity getUnitOptimizationEffectAssess(String unitCode, Date startTime, Date endTime) throws Exception;

    /**
     * 获取单个装置的趋势图
     *
     * <AUTHOR> 2017-10-24
     * @param dateJason 时间集合
     * @param unitCode 装置编码
     * @return 等级评估详情页面实体
     */
    AlarmLevelAssessDetailEntity getVariationTrend(String dateJason, String unitCode) throws Exception;

    /**
     * 获取单个装置的趋势图
     * @param startTime
     * @param endTime
     * @param unitId
     * @return
     * @throws Exception
     */
    AlarmLevelAssessDetailEntity getVariationTrend(Date startTime, Date endTime, String[] unitId) throws Exception;

    /**
     * 获取 1.装置名称, 2.装置ID, 3.报警总数 ,4.滋扰报警数量,5.报警确认数,6.报警恢复数,7.报警搁置数列表
     *
     * @param startTime 查询开始时间
     * @param endTime   查询结束时间
     * @return 1.装置名称, 2.装置ID, 3.报警总数 ,4.滋扰报警数量,5.报警确认数,6.报警恢复数,7.报警搁置数列表
     * <AUTHOR> 2017-12-20
     */
    Map<String, List<StaticInfoEntity>> getStatisticsAlarmCount(Date startTime, Date endTime) throws Exception;

    /**
     *查看报警评估总览
     *
     * @param startTime
     * @param endTime
     * @param assessLevel 评估等级
     * @return 等级评估详情
     * <AUTHOR> 2019-12-20
     */
    AlarmLevelAssessDetailEntity getAlarmAssessOverviewDetail(Date startTime, Date endTime, Long assessLevel) throws Exception;

    List<PeakAlarmRateEntity> getPeakAlarmRate(Date startTime, Date endTime, String[] unitIds) throws Exception;

    /**
     *
     * @param startDate
     * @param endDate
     * @return
     */
    List<UnitResponseNumberVO> getUnitAlarmConfirmRate(Date startDate, Date endDate);

    Page<UnitResponseNumberVO> getUnitAlarmStatistics(Date startDate, Date endDate, Pagination page);

    List<WorkshopResponseNumberVO> getWorkshopAlarmConfirmRate(Date startDate, Date endDate);

    Page<WorkshopResponseNumberVO> getWorkshopAlarmStatistics(Date startDate, Date endDate, Pagination page);

}
