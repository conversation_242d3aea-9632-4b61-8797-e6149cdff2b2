var pushRuleRelUrl = OPAL.API.apUrl + '/PushRuleRel/getAlarmPushRuleUnitRel';  //一级列表
var pushRuleRelCurrentUrl = OPAL.API.apUrl + '/PushRuleRelAddOrEdit/getRuleUnitRelDetailList'; //二级列表
var parentDelUrl = OPAL.API.apUrl + '/PushRuleRel/delete'; //主删除
var childDelUrl = OPAL.API.apUrl + '/PushRuleRelAddOrEdit/delete'; //子删除
var alarmPriorityListUrl = OPAL.API.afUrl + "/alarmDurationStatt/getAlarmPriorityList";//优先级
var isLoading = true;
var unitId;
var BusinessType = 1;
var BusinessTypeTitle = '';
//专业
var majorStatusList = [
    { value: '-1', text: '全部' },
    { value: '1', text: '工艺' },
];
var rowsChildArr = [];
var num = 0;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            /**
             *绑定事件
             */
            this.bindUi();
            BusinessType = OPAL.util.getQueryParam('BusinessType');
            if (BusinessType == 1) {
                BusinessTypeTitle = '超时';
                $('.priorityDiv').show();
            } else {
                BusinessTypeTitle = '超限';
                $('.priorityDiv').hide();
            }
            $(".alarm-public-title-dis").html(BusinessTypeTitle + '推送规则关联');
            //初始化优先级
            page.logic.initAlarmPriorityList();
            //初始化专业
            page.logic.initMajorStatus();
            //初始化表格
            page.logic.initOpetateTable();
            // 查询
            page.logic.search();

        },
        bindUi: function () {
            // 新增
            $('#AlarmGroupAdd').click(function () {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            });
            //批量删除
            $('#AlarmGroupDel').click(function () {
                page.logic.delAll();
            });
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                $('#MostAlarmOperateTable').bootstrapTable('resetView');
            };
            /**
             * 查询
             */
            $('#btnSearch').click(function () {
                isLoading = false;
                page.logic.search();
            })
        },
        data: {
            param: {},
            subParam: {}
        },

        logic: {
            /***
             * 查询
             */
            search: function () {
                page.data.param = OPAL.form.getData("searchForm");
                page.logic.queryMostOperate();
            },
            /**
             * 加载 下方表格
             */
            queryMostOperate: function () {
                $("#MostAlarmOperateTable").bootstrapTable('refresh', {
                    "url": pushRuleRelUrl,
                    "pageNumber": 1
                });
            },
            //初始化下方表格
            initOpetateTable: function () {
                page.logic.initBootstrapTable("MostAlarmOperateTable", {
                    detailView: true,
                    cache: false,
                    uniqueId: 'apRuleUnitRelId',
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '100px',
                        formatter: page.logic.onActionRenderer
                    },
                    {
                        field: 'pushRuleName',
                        title: '推送规则名称',
                        align: 'center',
                        width: '150px'
                    }, {
                        field: 'specialityName',
                        title: '专业',
                        align: 'center',
                        width: '150px'
                    }, {
                        field: 'priorityName',
                        title: '优先级',
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "维护时间",
                        field: 'mntDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "维护人",
                        field: 'mntUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }],
                    onExpandRow: function (index, row, $detail) {
                        page.data.subParam.apRuleUnitRelId = row['apRuleUnitRelId'];
                        page.logic.initCausalSubTable(index, row, $detail);
                    },
                    onLoadSuccess: function() {
                        num = $('#MostAlarmOperateTable>tbody>tr').length;
                        if (BusinessType == 2) {
                            $('#MostAlarmOperateTable').bootstrapTable('hideColumn', 'priorityName');
                        }
                    }
                }, page.logic.queryParams);
                var tableOption = $('#MostAlarmOperateTable').bootstrapTable('getOptions');
                $("#MostAlarmOperateTable").bootstrapTable('refreshOptions', tableOption);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.edit(\'' + rowData.apRuleUnitRelId + '\',\'' + rowData.unitName + '\')">编辑</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.apRuleUnitRelId + '\',\'' + arguments[2] + '\')" >删除</a>  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + 
                    '<a  name="TableDelete"  href="javascript:window.page.logic.view(\'' + rowData.apRuleUnitRelId + '\')" >详情</a>  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'
                ]
            },
            queryParams: function (p) {
                var param = {
                    businessType: BusinessType,
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            //二级表格
            subParams: function (p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.subParam, param);
            },
            /**
             * 初始化二级列表
             */
            initCausalSubTable: function (index, row, $detail) {
                let subId = 'sub_table' + index;
                $detail.html('<table></table>').find('table').attr('id', subId);
                OPAL.ui.initBootstrapTable(subId, {
                    url: pushRuleRelCurrentUrl,
                    striped: true,
                    pagination: true,
                    pageList: [5, 10, 20, 50, 100],
                    columns: [
                        {
                            field: 'state',
                            checkbox: true,
                            rowspan: 1,
                            align: 'center',
                            width: '50px'
                        },
                        // {
                        //     title: "操作",
                        //     field: 'event_cancel',
                        //     rowspan: 1,
                        //     align: 'center',
                        //     width: '90px',
                        //     formatter: function (value, row, index) {
                        //         return '<a name="TableDelete" href="javascript:window.page.logic.delSingleSon(\'' + row.dtlId + '\',\'' + subId + '\'\,\'' + index + '\'\)">删除</a>'
                        //     }
                        // }, 
                        {
                            field: 'factoryName',
                            title: '工厂',
                            align: 'left',
                            width: '150px'
                        }, {
                            field: 'workshopName',
                            title: '车间',
                            align: 'left',
                            width: '150px'
                        }, {
                            field: 'unitName',
                            title: '装置',
                            align: 'left',
                            width: '150px'
                        }],
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "rows": $.ET.toObjectArr(res),
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                        };
                        return item;
                    }
                }, page.logic.subParams);
            },
            //获取所有选中的子表格行
            getAllSelects: function(){
                let arr = [];
                for(let i = 0;i < num;i++) {
                    let list = $('#sub_table' + i).bootstrapTable('getAllSelections');
                    if (list.length > 0) {
                        arr = arr.concat($('#sub_table' + i).bootstrapTable('getAllSelections'));
                    }
                }
                return arr;
            },
            //初始化表格
            initBootstrapTable: function (tableID, config, queryParams) {
                var _config = {
                    method: 'get',
                    url: '',
                    cache: false,
                    pagination: true, //启动分页
                    pageSize: 10,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    sidePagination: "server", // 表示服务端请求 后台分页
                    //设置为undefined可以获取pageNumber，pageSize，searchText，sortName，sortOrder
                    //设置为limit可以获取limit, offset, search, sort, order
                    queryParamsType: "undefined",
                    queryParams: queryParams,
                    contentType: 'application/x-www-form-urlencoded',
                    formatNoMatches: function () {
                        return "";
                    },
                    responseHandler: function (res) {
                        var item = {
                            "pageNumber": $.ET.getPageInfo(res)[0]["pageNumber"],
                            "pageSize": $.ET.getPageInfo(res)[0]["pageSize"],
                            "total": $.ET.getPageInfo(res)[0]["total"],
                            "rows": $.ET.toObjectArr(res)
                        };
                        return item;
                    },
                    onLoadSuccess: function () {
                        //设置鼠标浮动提示
                        var tds = $('#' + tableID).find('tbody tr td');
                        $.each(tds, function (i, el) {
                            $(this).attr("title", $(this).text())
                        })
                    }
                };
                $.extend(true, _config, config);
                $('#' + tableID).bootstrapTable(_config);
            },

            /**
             * 批量删除
             */
            delAll: function () {
                var parentIdsArr = new Array();
                var childIdsArr = new Array();
                var rowsParentArr = $("#MostAlarmOperateTable").bootstrapTable('getSelections'); //选中主数据
                rowsChildArr = page.logic.getAllSelects(); //选中子数据
                if (rowsParentArr.length == 0 && rowsChildArr.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                if (rowsChildArr.length > 0) {
                    $.each(rowsChildArr, function (i, el) {
                        childIdsArr.push(el.apRuleUnitRelDetailId);
                    })
                    page.logic.childDelFun(childIdsArr);
                }
                if (rowsParentArr.length > 0) {
                    $.each(rowsParentArr, function (i, el) {
                        parentIdsArr.push(el.apRuleUnitRelId);
                    })
                    page.logic.parentDelFun(parentIdsArr);
                }
            },
            //主删除
            parentDelFun: function(idsArray) {
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: parentDelUrl,
                        async: true,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            //子删除
            childDelFun: function(idsArray) {
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: childDelUrl,
                        async: true,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE', //PUT DELETE POST
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    page.logic.search();
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 单条主删除
             */
            delSingle: function (id, index) {
                var data = new Array();
                data.push(id);
                page.logic.parentDelFun(data);
            },
            /**
             * 单条子表删除
             */
            delSingleSon: function (id, idName, index) {
                var data = new Array();
                data.push(id);
                page.logic.childDelFun(data);
            },
            /**
             * 新增
             */
            add: function () {
                var pageMode = PageModelEnum.NewAdd;
                var title = "推送规则关联新增";
                page.logic.detail(title, "", pageMode);
            },
            /**
             * 编辑
             * @param apRuleUnitRelId
             */
            edit: function (id) {
                var pageMode = PageModelEnum.Edit;
                var title = "推送规则关联编辑";
                page.logic.detail(title, id, pageMode);
            },
            /**
             * 详情
             * @param apRuleUnitRelId
             */
            view: function (id) {
                var pageMode = PageModelEnum.View;
                var title = "推送规则关联详情";
                page.logic.detail(title, id, pageMode);
            },
            /**
             * 装置新增或者编辑详细页面
             */
            detail: function (title, ID, pageMode) {
                let row = $('#MostAlarmOperateTable').bootstrapTable('getRowByUniqueId', ID);
                layer.open({
                    type: 2,
                    title: BusinessTypeTitle + title,
                    closeBtn: 1,
                    area: ['1000px', '520px'],
                    shadeClose: false,
                    content: 'PushRuleRelAddOrEdit.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "apRuleUnitRelId": ID,
                            'BusinessType': BusinessType,
                            row: JSON.stringify(row)
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (window.pageLoadMode == PageLoadMode.Refresh) {
                            page.logic.search();
                            window.pageLoadMode = PageLoadMode.None;
                        } else if (window.pageLoadMode == PageLoadMode.Reload) {
                            $('#MostAlarmOperateTable').bootstrapTable('selectPage', 1);
                            window.pageLoadMode = PageLoadMode.None;
                        }
                    }
                })
            },
            /**
             * 初始化查询 优先级
             */
            initAlarmPriorityList: function () {
                OPAL.ui.getCombobox("priority", alarmPriorityListUrl, {
                    selectValue: '-1',
                    data: {
                        'isAll': true
                    }
                }, function (e) {
                    $("#priority option").eq(0).html("全部");
                });
            },
            /**
             * 初始化查询 报警时长
             */
            initMajorStatus: function () {
                var str = '';
                $.each(majorStatusList, function (i, el) {
                    str += '<option value="' + el.value + '">' + el.text + '</option>';
                });
                $('#speciality').html(str);
                //设置默认值
                $('#speciality').val('-1');
            },
        }
    };
    page.init();
    window.page = page;
});