package com.pcitc.opal.ap.bll;

import com.pcitc.opal.ap.bll.entity.AlarmPushRuleEntity;
import com.pcitc.opal.ap.bll.entity.AlarmPushRuleUnitRelEntity;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleUnitRelEntityVO;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import org.springframework.stereotype.Service;

/*
 * 报警知识管理业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_AlarmKnowlgManagmtService
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理业务逻辑层接口
 */
@Service
public interface AlarmPushRuleUnitRelService {

    /**
     * 获取分页数据
     *
      * <AUTHOR> 2017-10-11
     * @param name 名称
     * @param page 翻页实现类
     * @throws Exception 
     * @return PaginationBean<AlarmPointEntity> 分页对象
     */
    PaginationBean<AlarmPushRuleUnitRelEntityVO> getAlarmPushRuleUnitRel(String name, Integer companyId, Integer pushType,Integer speciality,Long priority, Pagination page) throws  Exception;


    /**
     * 新增数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPushRuleUnitRelEntity 报警点实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult addAlarmPushRuleUnitRel(AlarmPushRuleUnitRelEntity alarmPushRuleUnitRelEntity) throws Exception;


    /**
     * 删除报警点分组数据
     *
      * <AUTHOR> 2017-10-11
     * @param ids 报警点分组主键Id集合
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult deleteAlarmPushRuleUnitRel(Long[] ids) throws Exception;

    /**
     * 报警点分组更新数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPushRuleUnitRelEntity 报警点分组实体
     * @throws Exception 
     * @return CommonResult 消息结果类
     */
    CommonResult updateAlarmPushRuleUnitRel(AlarmPushRuleUnitRelEntity alarmPushRuleUnitRelEntity) throws Exception;
}
