package com.pcitc.opal.aa.bll.entity;

import java.util.List;

/*
 * 报警数量评估实体
 * 模块编号：pcitc_opal_bll_class_AlarmNumberAssessEntity
 * 作  　者：kun.zhao
 * 创建时间：2017-10-30
 * 修改编号：1
 * 描    述：报警数量评估实体
 */
public class AlarmNumberAssessEntity {
	/**
	 * 名称
	 */
	private String name;
	
	/**
	 * 横坐标集合
	 */
	private List<String> xaxis;
	
	/**
	 * 提示语集合
	 */
	private List<String> tip;
	
	/**
	 * 报警数集合
	 */
	private List<Long> counts;
	
	/**
     * 数据信息
     */
    private List<AlarmNumberAssessTableEntity> list;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public List<String> getXaxis() {
		return xaxis;
	}

	public void setXaxis(List<String> xaxis) {
		this.xaxis = xaxis;
	}

	public List<String> getTip() {
		return tip;
	}

	public void setTip(List<String> tip) {
		this.tip = tip;
	}

	public List<Long> getCounts() {
		return counts;
	}

	public void setCounts(List<Long> counts) {
		this.counts = counts;
	}

	public List<AlarmNumberAssessTableEntity> getList() {
		return list;
	}

	public void setList(List<AlarmNumberAssessTableEntity> list) {
		this.list = list;
	}
    
}
