package com.pcitc.opal.common;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.text.ParseException;
import java.util.Date;

/*
 * JSON日期序列化
 * 模块编号：pcitc_wm_common_class_CustomJsonDateDeserializer
 * 作    者：xuelei.wang
 * 创建时间：2018/07/26
 * 修改编号：1
 * 描    述：JSON日期序列化
 */
public class CustomJsonDateDeserializer extends JsonDeserializer<Date> {
    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
        if (StringUtils.isEmpty(jsonParser.getText())) return null;
        String date = jsonParser.getText();
        try {
            return DateUtils.parseDate(date, dateFormatPatterns);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String[] dateFormatPatterns = new String[]{"yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ss.000'Z'","yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", "yyyy-MM-dd HH:mm", "yyyy-MM-dd'T'", "yyyy-MM-dd", "yyyy-MM-dd'T'HH:mm:ss","yyyy-MM"};
}
