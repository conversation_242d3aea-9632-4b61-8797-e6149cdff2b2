package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.dao.imp.AlarmPointGroupConfig;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import com.pcitc.opal.pm.pojo.AlarmPointGroup;

import java.util.List;

/*
 * AlarmPoint实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmPointRepositoryCustom
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：AlarmPoint实体的Repository的JPA自定义接口 
 */
public interface AlarmPointGroupRepositoryCustom {




	/**
	 * 获取分页数据
	 *
	 * <AUTHOR> 2017-10-11
	 * @param unitCodes 装置编码
	 * @return List<AlarmPointGroupConfig> 翻页对象
	 */
	PaginationBean<AlarmPointGroupConfig> getAlarmPointGroupsPage(String[] unitCodes,String groupName,Long companyId,Pagination page);

	CommonResult deleteAlarmPointGroup(Long[] alarmPointIds);

	CommonResult addAlarmPointGroupDtl(AlarmPointGroup alarmPointGroupEntity);

	CommonResult updateAlarmPointGroupDtl(AlarmPointGroup alarmPointGroupEntity);

	AlarmPointGroupConfig getSingleAlarmPointGroupConfig(Long alarmPointGroupId);

	List<AlarmPointGroup> getSingleAlarmPointGroup(Long alarmPointGroupId);

	List<AlarmPointGroup> getAlarmPointGroupsByUnit(String unitCode);
}
