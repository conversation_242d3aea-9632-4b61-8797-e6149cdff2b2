var pmUrl = OPAL.API.pmUrl;
var delUrl = pmUrl + '/mobileList';
var searchUrl = pmUrl + '/mobileList/getMobileListPages';
var factoryUrl = OPAL.API.pmUrl + '/unit/getFactoryList';    //工厂
var workshopUrl = OPAL.API.pmUrl + '/unit/getWorkshopListByFactoryId';   //车间
var unitUrl = OPAL.API.pmUrl + "/alarmMsgConfig/getUnitListByWorkshopId";   //装置
var importUrl = OPAL.API.pmUrl + '/mobileList/importMobileList' //导入
var exportUrl = OPAL.API.pmUrl + '/mobileList/exportMobileList'; //导出
var downTemplateUrl = OPAL.API.pmUrl + '/mobileList/downloadMobileList'; //下载
var isRefresh = false;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            //绑定事件
            this.bindUI();
            //初始化表格
            page.logic.initTable();
            //导入
            page.logic.importExcel();
            // 工厂
            page.logic.initFactory();
            //默认查询数据
            setTimeout(function () {
                if ($("#factoryId").val()!=null) {
                    page.logic.search();
                }
            }, 500);
        },
        /**
         * 绑定事件
         */
        bindUI: function () {
            // 新增
            $('#unitAdd').click(function () {
                page.logic.add('新增', "", PageModelEnum.NewAdd);
            })
            //批量删除
            $('#unitDel').click(function () {
                page.logic.delAll();
            })
            //查询
            $('#btnSearch').click(function () {
                page.logic.search();
            })
            // 导出
            $('#Export').click(function () {
                page.logic.exportExcel();
            });
            // 下载模板
            $('#DownFile').click(function () {
                page.logic.downFile();
            });
        },
        data: {
            // 设置查询参数
            param: {}
        },
        /**
         * 方法
         */
        logic: {
            /**
             * 初始化表格
             */
            initTable: function () {
                OPAL.ui.initBootstrapTable("table", {
                    cache: false,
                    columns: [{
                        field: 'state',
                        checkbox: true,
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        title: "操作",
                        field: 'event_cancel',
                        rowspan: 1,
                        align: 'center',
                        width: '90px',
                        formatter: page.logic.onActionRenderer
                    }, {
                        title: "工厂",
                        field: 'factoryName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "车间",
                        field: 'workshopName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "姓名",
                        field: 'name',
                        rowspan: 1,
                        align: 'left',
                        width: '180px'
                    }, {
                        title: "手机号",
                        field: 'mobile',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "创建时间",
                        field: 'crtDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "创建人",
                        field: 'crtUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "维护时间",
                        field: 'mntDate',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "维护人",
                        field: 'mntUserName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }]
                }, page.logic.queryParams)
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function (p) { // 设置查询参数
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 添加配置行按钮操作
             * @returns {*[]}
             */
            onActionRenderer: function () {
                var rowData = arguments[1];
                return [
                    '<a  name="TableEditor"  href="javascript:window.page.logic.edit(\'' + rowData.mobileListId + '\')">编辑</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    '<a  name="TableDelete"  href="javascript:window.page.logic.delSingle(\'' + rowData.mobileListId + '\')" >删除</a> '
                ]
            },
            /**
             * 批量删除
             */
            delAll: function () {
                var idsArray = new Array();
                var rowsArray = $("#table").bootstrapTable('getSelections')
                $.each(rowsArray, function (i, el) {
                    idsArray.push(el.mobileListId);
                })
                if (idsArray.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(idsArray),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 单条删除
             */
            delSingle: function (id) {
                var data = new Array();
                data.push(id);
                layer.confirm('确定删除吗？', {
                    btn: ['确定', '取消']
                }, function () {
                    $.ajax({
                        url: delUrl,
                        async: false,
                        data: JSON.stringify(data),
                        dataType: "text",
                        contentType: "application/json;charset=utf-8",
                        type: 'DELETE',
                        success: function (result) {
                            if (result.indexOf('collection') < 0) {
                                layer.msg(result, {
                                    time: 1000
                                }, function () {
                                    $('#table').bootstrapTable('selectPage', 1);
                                });
                            } else {
                                result = JSON.parse(result)
                                layer.msg(result.collection.error.message)
                            }
                        },
                        error: function (result) {
                            var errorResult = $.parseJSON(result.responseText);
                            layer.msg(errorResult.collection.error.message);
                        }
                    })
                }, function (index) {
                    layer.close(index)
                });
            },
            /**
             * 新增
             */
            add: function () {
                var pageMode = PageModelEnum.NewAdd;
                var title = "电话本新增";
                page.logic.detail(title, "", pageMode);
            },
            /**
             * 编辑
             * @param factoryId
             */
            edit: function (id) {
                var pageMode = PageModelEnum.Edit;
                var title = "电话本编辑";
                page.logic.detail(title, id, pageMode);
            },
            /**
             * 电话本新增或者编辑详细页面
             */
            detail: function (title, id, pageMode) {
                layer.open({
                    type: 2,
                    title: '',
                    closeBtn: false,
                    area: ['800px', '300px'],
                    shadeClose: false,
                    content: 'MobileListAddOrEdit.html?' + Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var data = {
                            "pageMode": pageMode,
                            "mobileListId": id,
                            'title': title
                        };
                        iframeWin.page.logic.setData(data);
                    },
                    end: function () {
                        if (isRefresh == true) {
                            if (pageMode == PageModelEnum.Edit)
                                page.logic.search();
                            else if (pageMode == PageModelEnum.NewAdd)
                                $('#table').bootstrapTable('selectPage', 1);
                        }
                    }
                })
            },
            /**
             * 搜索
             */
            search: function () {
                page.data.param = OPAL.form.getData("searchForm");
                if (page.data.param.workshopId == -1) {
                    page.data.param.workshopId = '';
                }
                if (page.data.param.unitId == -1) {
                    page.data.param.unitId = '';
                }
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 导入
             */
            importExcel: function () {
                //实例化一个plupload上传对象
                var uploader = new plupload.Uploader({
                    browse_button: 'Import', //触发文件选择对话框的按钮，为那个元素id
                    url: importUrl, //服务器端的上传页面地址
                    flash_swf_url: '../../../js/common/plupload/js/Moxie.swf', //swf文件，当需要使用swf方式进行上传时需要配置该参数
                    multi_selection: false,
                    filters: {
                        mime_types: [{
                            title: "Excel files",
                            extensions: "xls,xlsx"
                        }]
                    }
                });
                uploader.init();
                uploader.bind('FilesAdded', function (uploader, files) {
                    uploader.start();
                    index = layer.msg('加载中', {
                        icon: 16,
                        shade: 0.1,
                        time: 0
                    });
                });
                uploader.bind('FileUploaded', function (uploader, file, responseObject) {
                    var msg = responseObject.response;
                    if (msg.indexOf('导入成功') != -1) {
                        var index = layer.confirm(msg, {
                            btn: ['确定']
                        }, function () {
                            layer.close(index);
                            $("#table").bootstrapTable('refresh', {
                                "url": searchUrl,
                                "pageNumber": 1
                            });
                        })
                    } else {
                        layer.confirm(msg, {
                            btn: ['确定']
                        })
                    }
                })
                uploader.bind('Error', function (uploader, errObject) {
                    layer.close(index);
                    if (errObject.response != undefined && errObject.response != '') {
                        var message = $.parseJSON(errObject.response).collection.error.message;
                    } else {
                        var message = errObject.message
                    }
                    layer.confirm(message, {
                        btn: ['确定']
                    })
                });
            },
            /**
             * 导出
             */
            exportExcel: function () {
                var titleArray = new Array();
                var tableTitle;
                tableTitle = $('#table').bootstrapTable('getOptions').columns[0];
                tableTitle.splice(0, 2);
                $.each(tableTitle, function (i, el) {
                    titleArray.push({
                        'key': el.field,
                        'value': el.title
                    })
                })
                var data = {};
                var pageSize = $('#table').bootstrapTable('getOptions').pageSize;
                var pageNumber = $('#table').bootstrapTable('getOptions').pageNumber;
                data.titles = JSON.stringify(titleArray);
                data.pageSize = pageSize;
                data.pageNumber = pageNumber;
                $.extend(data, page.data.param);
                $('#formExPort').attr('action', exportUrl);
                $('#titles').val(data.titles);
                $('#pageSize').val(data.pageSize);
                $('#pageNumber').val(data.pageNumber);
                $('#factoryId1').val(data.factoryId);
                $('#workshopId1').val(data.workshopId);
                $('#unitId1').val(data.unitId);
                $('#mobile1').val(data.mobile);
                $('#name1').val(data.name);
                $('#formExPort').submit();
            },

            /**
             * 导入模板下载
             */
            downFile: function () {
                $('#form_Down').attr('action', downTemplateUrl);
                $('#fileName').val('电话本导入模板');
                $('#templateName').val('');
                $('#form_Down').submit();
            },
            /**
             * 初始化工厂
             */
            initFactory: function () {
                OPAL.ui.getCombobox("factoryId", factoryUrl, {
                    keyField: "factoryId",
                    valueField: "sname",
                    selectFirstRecord: true,
                    data: {
                        isAll: true
                    }
                }, function (data) {
                }, function (selectedValue) {
                    if (selectedValue != '' &&selectedValue!=null){
                        page.logic.initWorkshop(selectedValue);
                    }else{
                        $("#workshopId").val([]);
                        $("#workshopId option").remove();
                        $("#unitId").val([]);
                        $("#unitId option").remove();
                    }
                });
            },
            /**
             * 初始化车间
             */
            initWorkshop: function (factoryId) {
                OPAL.ui.getCombobox("workshopId", workshopUrl, {
                    keyField: "workshopId",
                    valueField: "sname",
                    selectFirstRecord: true,
                    data: {
                        factoryId: factoryId,
                        isAll: true
                    }
                }, function () {
                }, function (selectedValue) {
                    if (selectedValue != '' && selectedValue != null && $("#workshopId").val() != -1) {
                        page.logic.initUnit(selectedValue);
                    } else {
                        $("#unitId").val([]);
                        $("#unitId option").remove();
                    }
                })
            },
            /**
             * 初始化装置
             */
            initUnit: function (workshopId) {
                OPAL.ui.getCombobox("unitId", unitUrl, {
                    keyField: "stdCode",
                    valueField: "sname",
                    selectFirstRecord: true,
                    data: {
                        workshopId: workshopId,
                        isAll: true
                    }
                }, null, null)
            },
        }

    }
    page.init();
    window.page = page;
})