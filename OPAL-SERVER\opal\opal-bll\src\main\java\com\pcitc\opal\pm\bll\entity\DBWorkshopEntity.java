package com.pcitc.opal.pm.bll.entity;
import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 车间实体
 * 模块编号：pcitc_opal_bll_class_WorkshopEntity
 * 作   者： xuelei.wang
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描   述：车间实体
 */
public class DBWorkshopEntity extends BasicEntity {

	/**
	 * 车间ID
	 */
	private Long workshopId;

	/**
	 * 工厂ID
	 */
	private Long factoryId;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 简称
	 */
	private String sname;

	/**
	 * 标准编码
	 */
	private String stdCode;

	/**
	 * 排序
	 */
	private Integer sortNum;

	/**
	 * 描述
	 */
	private String des;

	/**
	 * 工厂简称
	 */
	private String factorySname;

	public Long getWorkshopId() {
		return workshopId;
	}

	public void setWorkshopId(Long workshopId) {
		this.workshopId = workshopId;
	}

	public Long getFactoryId() {
		return factoryId;
	}

	public void setFactoryId(Long factoryId) {
		this.factoryId = factoryId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSname() {
		return sname;
	}

	public void setSname(String sname) {
		this.sname = sname;
	}

	public String getStdCode() {
		return stdCode;
	}

	public void setStdCode(String stdCode) {
		this.stdCode = stdCode;
	}

	public Integer getSortNum() {
		return sortNum;
	}

	public void setSortNum(Integer sortNum) {
		this.sortNum = sortNum;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}

	public String getFactorySname() {
		return factorySname;
	}

	public void setFactorySname(String factorySname) {
		this.factorySname = factorySname;
	}
	
}