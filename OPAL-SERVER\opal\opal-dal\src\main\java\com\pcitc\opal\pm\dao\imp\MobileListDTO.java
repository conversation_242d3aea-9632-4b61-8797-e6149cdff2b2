package com.pcitc.opal.pm.dao.imp;

import java.util.Date;

/*
 * 电话本
 * 模块编号：pcitc_pojo_class_MobileList
 * 作    者：guoganxin
 * 创建时间：2020-05-11 10:14:49
 * 修改编号：1
 * 描    述：电话本
 */
public class MobileListDTO {
    public MobileListDTO() {
    }

    ;

    public MobileListDTO(Long mobileListId, String factoryName, String workshopName, String unitName, String name, String mobile, Date crtDate, String crtUserName, Date mntDate, String mntUserName) {
        this.mobileListId = mobileListId;
        this.factoryName = factoryName;
        this.workshopName = workshopName;
        this.unitName = unitName;
        this.name = name;
        this.mobile = mobile;
        this.crtDate = crtDate;
        this.crtUserName = crtUserName;
        this.mntDate = mntDate;
        this.mntUserName = mntUserName;
    }

    /**
     * 电话本ID
     */
    private Long mobileListId;

    /**
     * 工厂名称
     */
    private String factoryName;

    /**
     * 车间名称
     */
    private String workshopName;

    /**
     * 装置名称
     */
    private String unitName;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 创建时间
     */
    private Date crtDate;

    /**
     * 创建人名称
     */
    private String crtUserName;

    /**
     * 修改时间
     */
    private Date mntDate;

    /**
     * 修改人名称
     */
    private String mntUserName;

    public Long getMobileListId() {
        return mobileListId;
    }

    public void setMobileListId(Long mobileListId) {
        this.mobileListId = mobileListId;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    public String getWorkshopName() {
        return workshopName;
    }

    public void setWorkshopName(String workshopName) {
        this.workshopName = workshopName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Date getCrtDate() {
        return crtDate;
    }

    public void setCrtDate(Date crtDate) {
        this.crtDate = crtDate;
    }

    public String getCrtUserName() {
        return crtUserName;
    }

    public void setCrtUserName(String crtUserName) {
        this.crtUserName = crtUserName;
    }

    public Date getMntDate() {
        return mntDate;
    }

    public void setMntDate(Date mntDate) {
        this.mntDate = mntDate;
    }

    public String getMntUserName() {
        return mntUserName;
    }

    public void setMntUserName(String mntUserName) {
        this.mntUserName = mntUserName;
    }
}

