package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmPushLogRepository;
import com.pcitc.opal.pm.dao.AlarmPushLogRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmPushLog;

import javax.persistence.TypedQuery;

/**
 * @USER: chenbo
 * @DATE: 2023/5/29
 * @TIME: 17:11
 * @DESC:
 **/
public class AlarmPushLogRepositoryImpl  extends BaseRepository<AlarmPushLog,Long> implements AlarmPushLogRepositoryCustom {
    @Override
    public Long getMaxCurrentId() {
        String hql = "select max(currentId) from AlarmPushLog";

        TypedQuery<Long> query = getEntityManager().createQuery(hql, Long.class);
        try {
            return query.getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }
}
