package com.pcitc.opal.common.bll.imp;

import com.pcitc.opal.common.MicroServiceClient;
import com.pcitc.opal.common.bll.AAAService;
import com.pcitc.opal.common.bll.entity.AAAOrgUnitEntity;
import com.pcitc.opal.common.bll.entity.AAAPropertyValueEntity;
import com.pcitc.opal.common.bll.entity.AAAUserEntity;
import com.pcitc.opal.common.bll.entity.UserEntity;
import com.pcitc.opal.common.bll.vo.PropertyValueVO;
import com.pcitc.opal.common.bll.vo.UserVO;
import com.pcitc.opal.pm.dao.CompanyRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import pcitc.imp.common.ettool.utils.ObjectConverter;
import pcitc.imp.common.ettool.utils.RestfulTool;

import java.util.ArrayList;
import java.util.List;

/**
 * Java版AAA平台提供的面向应用的服务接口
 * 模块编号：pcitc_opal_common_interface_ProMACEAAAServiceImpl
 * 作    者：jiangtao.xue
 * 创建时间：2017-12-14
 * 修改编号：1
 * 描    述：AAA平台提供的面向应用的服务接口
 */
@Component
@ConditionalOnProperty(name ="aaa_version",havingValue = "promace")
@Slf4j
public class ProMACEAAAServiceImpl implements AAAService {

    @Value("${aaa.resource.url}")
    private String aaaUrl;
    @Value("${aaa.organduser.url}/OrgAndUserService")
    private String userUrl;
    @Value("${aaa.appCode}")
    private String appCode;
    @Value("${aaa.resource.opalunitProperty}")
    private String propertyName;

    @Value("${aaa.appcompany}")
    private String appCompany;
    @Value("${aaa.resource.opalCompanyProperty}")
    private String opalCompanyProperty;

    @Autowired
    private CompanyRepository companyRepository;
    /**
     * 根据角色获取用户集合
     *
     * <AUTHOR> 2018-02-05
     * @param role 用户角色
     * @return
     */
    @Override
    public List<AAAUserEntity> listUsersByRole(String role) throws Exception {
        if (StringUtils.isEmpty(role))
            throw new IllegalArgumentException("role");
        List<AAAUserEntity> userEntityList = new ArrayList<>();
        String apiUrl = String.format("%s/apps/%s/roles/%s/users", userUrl, appCode, role);
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        //发送请求
        String collection = MicroServiceClient.getRequest(apiUrl, list);
        List<UserVO> uesrVOList = RestfulTool.toResourceRepList(collection, UserVO.class);
        //实体转换
        if (uesrVOList != null)
            userEntityList = ObjectConverter.listConverter(uesrVOList, AAAUserEntity.class);
        return userEntityList;
    }
    /**
     * 根据UserId 获取所属的组织单元
     * @param userId 用户ID或者LoginName
     * @return  组织单元
     * @throws Exception
     * <AUTHOR> 2018-03-09
     */
    @Override
    public AAAOrgUnitEntity getOrgByUserId(String userId) throws Exception{
        if (StringUtils.isEmpty(userId))
            throw new IllegalArgumentException("userId");
        List<AAAUserEntity> userEntityList = new ArrayList<>();
        String apiUrl = String.format("%s/users/%s", userUrl, userId);
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("userCode", userId));
        //发送请求
        String collection = MicroServiceClient.getRequest(apiUrl, list);
        List<UserVO> userVOList = RestfulTool.toResourceRepList(collection, UserVO.class);
        if(userVOList.size()==0) return null;
        AAAOrgUnitEntity orgUnitEntity = new AAAOrgUnitEntity();
        orgUnitEntity.setCode(userVOList.get(0).getOrgUnitCode());
        orgUnitEntity.setName(userVOList.get(0).getOrgUnitName());
        orgUnitEntity.setSName(userVOList.get(0).getOrgUnitName());
        return orgUnitEntity;
    }

    /**
     * 获取用户授权属性
     *
     * @param userId 用户ID
     * @return
     * @throws Exception
     * <AUTHOR> 2017-12-20
     */
    @Override
    public List<AAAPropertyValueEntity> getAuthPropertyValueList(String userId) throws Exception {
        if(userId == null) {
            throw new IllegalArgumentException("userId");
        }
        List<AAAPropertyValueEntity> propertyValueEntityList = new ArrayList<>();
        String propertyUrl = String.format("%s/apps/%s/properties/%s/propertyValues", aaaUrl, appCode, propertyName);
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("userCode", userId));
        //发送请求
        System.out.println("装置url："+propertyUrl);
        System.out.println("用户id："+userId);
        String collection = MicroServiceClient.getRequest(propertyUrl, list);
        List<PropertyValueVO> propertyValueVOList = RestfulTool.toResourceRepList(collection, PropertyValueVO.class);
        //实体转换
        if (propertyValueVOList != null)
            propertyValueEntityList = ObjectConverter.listConverter(propertyValueVOList, AAAPropertyValueEntity.class);
        return propertyValueEntityList;
    }
    /**
     * 获取指定用户的详细信息
     *
     * @param userCode 用户编码
     * @return 用户信息
     * @throws Exception
     */
    @Override
    public UserEntity getUserInfoByUserCode(String userCode) throws Exception {
        if (org.apache.commons.lang.StringUtils.isEmpty(userCode))
            throw new IllegalArgumentException("userCode");
        List<UserEntity> userEntityList = new ArrayList<>();
        String propertyUrl = String.format("%s/users/%s", userUrl, userCode);
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("userCode", userCode));
        //发送请求
        String collection = MicroServiceClient.getRequest(propertyUrl, list);
        List<UserVO> uesrVOList = RestfulTool.toResourceRepList(collection, UserVO.class);

        //实体转换
        if (uesrVOList != null)
            userEntityList = ObjectConverter.listConverter(uesrVOList, UserEntity.class);
        return userEntityList.stream().findFirst().orElse(null);
    }
    //获取企业编码
    @Override
    public Integer getCompanyIdByUserId(String userId){
        Integer companyId=null;
        //测试专用
        //http://10.238.220.64:8080/opalweb/html/pm/WorkShop/Index.html
        if(userId == null) {
            throw new IllegalArgumentException("userId");
        }
        List<AAAPropertyValueEntity> propertyValueEntityList = new ArrayList<>();
        String propertyUrl = String.format("%s/apps/%s/properties/%s/propertyValues", aaaUrl, appCompany, opalCompanyProperty);
        //组装请求参数
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("userCode", userId));
        try {
            String collection = MicroServiceClient.getRequest(propertyUrl, list);
            List<PropertyValueVO> propertyValueVOList = RestfulTool.toResourceRepList(collection, PropertyValueVO.class);
            if(propertyValueVOList !=null && propertyValueVOList.size()>0){
                companyId=Integer.valueOf(propertyValueVOList.get(0).getSourceId());
                log.info("获取当前用户《{}》所属企业ID《{}》", userId, propertyValueVOList.get(0).getSourceId());
            }else{
                log.error("未获取到当前用户{}的企业id，url为{}", userId, propertyUrl);
            }
        } catch (Exception e) {
            log.error("当前用户《{}》所属企业ID获取失败,{}", userId, e.getMessage());
        }
        return companyId;
    }
}
