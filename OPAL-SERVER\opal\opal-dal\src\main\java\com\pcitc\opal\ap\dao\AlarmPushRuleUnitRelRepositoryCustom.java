package com.pcitc.opal.ap.dao;

import com.pcitc.opal.ap.dao.imp.AlarmPushRuleUnitRelEntityVO;
import com.pcitc.opal.ap.dao.imp.RelAndDetailEntity;
import com.pcitc.opal.ap.pojo.AlarmPushRule;
import com.pcitc.opal.ap.pojo.AlarmPushRuleUnitRel;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

import java.util.List;

/*
 * 报警知识管理实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_AlarmKnowlgManagmtRepositoryCustom
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理实体的Repository的JPA自定义接口
 */
public interface AlarmPushRuleUnitRelRepositoryCustom {

    PaginationBean<AlarmPushRuleUnitRelEntityVO> getAlarmPushRuleUnitRelPage(String name, Integer companyId, Integer pushType, Integer speciality, Long priority, Pagination page);

    CommonResult addAlarmPushRuleUnitRel(AlarmPushRuleUnitRel alarmPushRuleUnitRel);

    CommonResult deleteAlarmPushRuleUnitRel(Long[] ids);

    CommonResult updateAlarmPushRuleUnitRel(AlarmPushRuleUnitRel alarmPushRuleUnitRel);

    List<String> findAllNameRule();

    List<RelAndDetailEntity> getAlarmPushRuleUnitRelById(Long apRuleUnitRelId);
}
