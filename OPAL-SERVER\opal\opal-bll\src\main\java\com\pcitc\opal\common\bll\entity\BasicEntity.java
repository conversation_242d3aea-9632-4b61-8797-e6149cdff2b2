package com.pcitc.opal.common.bll.entity;

import java.util.Date;

import com.pcitc.opal.common.CommonEnum;

/*
 * 创建人修改人相关信息公用实体  
 * 模块编号：pcitc_opal_bll_class_BasicEntity
 * 作       者：dongsheng.zhao
 * 创建时间：2017/09/17
 * 修改编号：1
 * 描       述：创建人修改人相关信息公用实体  
 */
public class BasicEntity {

	/**
	 * 创建时间
	 */
	private Date crtDate;

	/**
	 * 创建人ID
	 */
	private String crtUserId;

	/**
	 * 创建人名称
	 */
	private String crtUserName;

	/**
	 * 修改时间
	 */
	private Date mntDate;

	/**
	 * 修改人ID
	 */
	private String mntUserId;

	/**
	 * 修改人
	 */
	private String mntUserName;

	/**
	 * 是否启用
	 */
	private Integer inUse = 0;

	/**
	 * 是否启用描述（是，否）
	 */
	@SuppressWarnings("unused")
	private String inUseShow;

	public Date getCrtDate() {
		return crtDate;
	}

	public void setCrtDate(Date crtDate) {
		this.crtDate = crtDate;
	}

	public String getCrtUserId() {
		return crtUserId;
	}

	public void setCrtUserId(String crtUserId) {
		this.crtUserId = crtUserId;
	}

	public String getCrtUserName() {
		return crtUserName;
	}

	public void setCrtUserName(String crtUserName) {
		this.crtUserName = crtUserName;
	}

	public Date getMntDate() {
		return mntDate;
	}

	public void setMntDate(Date mntDate) {
		this.mntDate = mntDate;
	}

	public String getMntUserId() {
		return mntUserId;
	}

	public void setMntUserId(String mntUserId) {
		this.mntUserId = mntUserId;
	}

	public String getMntUserName() {
		return mntUserName;
	}

	public void setMntUserName(String mntUserName) {
		this.mntUserName = mntUserName;
	}

	public Integer getInUse() {
		return inUse;
	}

	public void setInUse(Integer inUse) {
		this.inUse = inUse;
	}

	public String getInUseShow() {
		return CommonEnum.InUseEnum.getName(inUse);
	}

	public void setInUseShow(String inUseShow) {
		this.inUseShow = inUseShow;
	}
}
