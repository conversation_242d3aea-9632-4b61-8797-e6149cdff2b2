package com.pcitc.opal.ap.bll.imp;

import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.AlarmFlagRepository;
import com.pcitc.opal.ak.dao.AlarmKnowlgManagmtRepository;
import com.pcitc.opal.ap.bll.AlarmPushRuleService;
import com.pcitc.opal.ap.bll.GroupService;
import com.pcitc.opal.ap.dao.AlarmPushRuleDetailRepository;
import com.pcitc.opal.ap.dao.AlarmPushRuleRepository;
import com.pcitc.opal.ap.dao.GroupRepository;
import com.pcitc.opal.ap.pojo.Group;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.pm.dao.AlarmPointRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/*
 * 报警制度管理业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmStdManagmtImpl
 * 作	者：kun.zhao
 * 创建时间：2018/02/28
 * 修改编号：1
 * 描    述：报警制度管理业务逻辑层实现类
 */
@Service
public class GroupImpl implements GroupService {


    @Autowired
    private AlarmPushRuleDetailRepository alarmPushRuleDetailRepository;
    @Autowired
    private AlarmPushRuleRepository alarmPushRuleRepository;
    @Autowired
    private GroupRepository groupRepository;


    @Override
    public List<Group> getGroupNames() {
        return groupRepository.getGroupNames();
    }
}
