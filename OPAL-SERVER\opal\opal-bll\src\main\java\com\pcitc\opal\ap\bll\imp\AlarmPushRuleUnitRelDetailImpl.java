package com.pcitc.opal.ap.bll.imp;

import com.pcitc.opal.ap.bll.AlarmPushRuleDetailService;
import com.pcitc.opal.ap.bll.AlarmPushRuleUnitRelDetailService;
import com.pcitc.opal.ap.bll.entity.AlarmPushRuleDetailEntity;
import com.pcitc.opal.ap.bll.entity.AlarmPushRuleUnitRelDetailEntity;
import com.pcitc.opal.ap.dao.*;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleDetailEntityVO;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleUnitRelDetailEntityVO;
import com.pcitc.opal.ap.pojo.AlarmPushRuleDetail;
import com.pcitc.opal.ap.pojo.AlarmPushRuleUnitRelDetail;
import com.pcitc.opal.common.*;
import com.pcitc.opal.pm.pojo.Unit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/*
 * 报警制度管理业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmStdManagmtImpl
 * 作	者：kun.zhao
 * 创建时间：2018/02/28
 * 修改编号：1
 * 描    述：报警制度管理业务逻辑层实现类
 */
@Service
public class AlarmPushRuleUnitRelDetailImpl implements AlarmPushRuleUnitRelDetailService {


    @Autowired
    private AlarmPushRuleUnitRelDetailRepository alarmPushRuleUnitRelDetailRepository;
    @Autowired
    private AlarmPushRuleUnitRelRepository alarmPushRuleUnitRelRepository;


    @Override
    public PaginationBean<AlarmPushRuleUnitRelDetailEntityVO> getAlarmPushRuleUnitRelDetail(Long alarmPushRuleId, Pagination page) throws Exception {
        PaginationBean<AlarmPushRuleUnitRelDetailEntityVO> alarmPushRuleDetailList =alarmPushRuleUnitRelDetailRepository.getAlarmPushRuleUnitRelDetails(alarmPushRuleId, page);
        return alarmPushRuleDetailList;
    }

    @Override
    public CommonResult addAlarmPushRuleUnitRelDetail(AlarmPushRuleUnitRelDetailEntity alarmPushRuleUnitRelDetailEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmPushRuleUnitRelDetail alarmPushRule = ObjectConverter.entityConverter(alarmPushRuleUnitRelDetailEntity, AlarmPushRuleUnitRelDetail.class);
        CommonUtil.returnValue(alarmPushRule, CommonEnum.PageModelEnum.NewAdd.getIndex());
        return alarmPushRuleUnitRelDetailRepository.addAlarmPushRuleUnitRelDetail(alarmPushRule);
    }

    @Override
    public CommonResult deleteAlarmPushRuleUnitRelDetail(Long[] ids) throws Exception {
        return alarmPushRuleUnitRelDetailRepository.deleteAlarmPushRuleUnitRelDetail(Arrays.asList(ids));
    }
    @Override
    public CommonResult deleteAlarmPushRuleUnitRelDetailOne(Long ids) throws Exception {
        return alarmPushRuleUnitRelDetailRepository.deleteAlarmPushRuleUnitRelDetailOne(ids);
    }
    @Override
    public List<Unit> getAllUnitsUsed(Integer speciality,Long priority) throws Exception {
        return alarmPushRuleUnitRelDetailRepository.getAllUnitsUsed( speciality, priority);
    }

    @Override
    public CommonResult updateAlarmPushRuleUnitRelDetail(AlarmPushRuleUnitRelDetailEntity alarmPushRuleUnitRelDetailEntity) throws Exception {
        // 实体转换为持久层实体
        AlarmPushRuleUnitRelDetail alarmPushRule = ObjectConverter.entityConverter(alarmPushRuleUnitRelDetailEntity, AlarmPushRuleUnitRelDetail.class);
        CommonUtil.returnValue(alarmPushRule, CommonEnum.PageModelEnum.NewAdd.getIndex());
        return alarmPushRuleUnitRelDetailRepository.updateAlarmPushRuleUnitRelDetail(alarmPushRule);
    }
}
