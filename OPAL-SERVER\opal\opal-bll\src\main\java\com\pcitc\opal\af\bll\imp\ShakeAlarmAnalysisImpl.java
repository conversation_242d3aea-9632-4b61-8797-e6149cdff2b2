package com.pcitc.opal.af.bll.imp;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.ParameterMode;
import javax.persistence.PersistenceContext;
import javax.persistence.StoredProcedureQuery;

import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.AlarmFlagRepository;
import com.pcitc.opal.af.bll.entity.ShakeAlarmAnalysisChartEntity;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.dao.AlarmPointRepository;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pcitc.opal.af.bll.ShakeAlarmAnalysisService;
import com.pcitc.opal.af.bll.entity.ShakeAlarmAnalysisDataEntity;
import com.pcitc.opal.af.bll.entity.ShakeAlarmAnalysisTableEntity;
import com.pcitc.opal.common.bll.BasicDataService;

/*
 * 震荡报警分析的业务实现类
 * 模块编号：pcitc_opal_bll_class_ShakeAlarmAnalysisImpl
 * 作  　  者：kun.zhao
 * 创建时间：2017/11/2
 * 修改编号：1
 * 描       述：震荡报警分析的业务实现类
 */
@Service
public class ShakeAlarmAnalysisImpl implements ShakeAlarmAnalysisService {

	@PersistenceContext
    private EntityManager entityManager;
	@Autowired
	private AlarmEventRepository repository;
	@Autowired
	private AlarmPointRepository alarmPointRepository;
	@Autowired
	private AlarmFlagRepository alarmFlagRepository;
	@Autowired
    private BasicDataService basicDataService;
	@Autowired
	private DbConfig dbConfig;
	
	/**
	 * 震荡报警分析——柱状图
	 * 
	 * <AUTHOR> 2017-11-02
	 * @param startTime 查询范围起始时间
	 * @param endTime	查询范围结束时间
	 * @param unitCodes	装置ID数组
	 * @param prdtIds	生产单元ID数组
	 * @return 震荡报警事件柱状图数据
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<ShakeAlarmAnalysisDataEntity> getShakeAlarmAnalysisEntity(Date startTime, Date endTime, String[] unitCodes,
			Long[] prdtIds) throws Exception {

		try {
			List<ShakeAlarmAnalysisDataEntity> shakeAlarmAnalysisDataEntityList = new ArrayList<>();
			StringBuilder queryUnitIds = new StringBuilder();
			StringBuilder queryPrdtIds = new StringBuilder();
			//获取装置ID字符串
			if(unitCodes == null){
				unitCodes = basicDataService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
			}
			Arrays.stream(unitCodes).forEach(id -> queryUnitIds.append(String.valueOf(id) + ","));
			//获取生产单元ID字符串
			if (prdtIds != null) {
				Arrays.stream(prdtIds).forEach(id -> queryPrdtIds.append(String.valueOf(id) + ","));
			}
			//企业
			CommonProperty commonProperty = new CommonProperty();
			StoredProcedureQuery query = entityManager.createStoredProcedureQuery("P_AF_GETShakeAlarmAnalysis");
			query.registerStoredProcedureParameter("v_in_startDate", Date.class, ParameterMode.IN);
			query.registerStoredProcedureParameter("v_in_endDate", Date.class, ParameterMode.IN);
			query.registerStoredProcedureParameter("v_in_tag", String.class, ParameterMode.IN);
			query.registerStoredProcedureParameter("v_in_alarmFlagId", Long.class, ParameterMode.IN);
			query.registerStoredProcedureParameter("v_in_beginIndex", Integer.class, ParameterMode.IN);
			query.registerStoredProcedureParameter("v_in_endIndex", Integer.class, ParameterMode.IN);
			query.registerStoredProcedureParameter("v_in_unitIds", String.class, ParameterMode.IN);
			query.registerStoredProcedureParameter("v_in_prdtIds", String.class, ParameterMode.IN);
			query.registerStoredProcedureParameter("v_in_type", Integer.class, ParameterMode.IN);
			query.registerStoredProcedureParameter("v_in_companyId",Integer.class,ParameterMode.IN);
			if("oracle".equals(dbConfig.getDataBase())){
				query.registerStoredProcedureParameter("v_results", void.class, ParameterMode.REF_CURSOR);
			}

			query.setParameter("v_in_startDate", startTime);
			query.setParameter("v_in_endDate", endTime);
			query.setParameter("v_in_tag", "");
			query.setParameter("v_in_alarmFlagId", 0L);
			query.setParameter("v_in_beginIndex", 0);
			query.setParameter("v_in_endIndex", 0);
			query.setParameter("v_in_unitIds", queryUnitIds.toString());
			query.setParameter("v_in_prdtIds", queryPrdtIds.toString());
			query.setParameter("v_in_type", 0);
			query.setParameter("v_in_companyId",commonProperty.getCompanyId());

            query.setFirstResult(0).setMaxResults(20);
            List<Object[]> result = query.getResultList();
        	String[] filterUnitCodes = result.stream().map(e->e[4].toString()).distinct().toArray(String[]::new);
        	List<UnitEntity> units = basicDataService.getUnitListByIds(filterUnitCodes, false);
            for (int i = 0; i < result.size(); i++) {
            	Object[] element = result.get(i);
            	 ShakeAlarmAnalysisDataEntity entity = new ShakeAlarmAnalysisDataEntity();
                 entity.setAlarmPointTag(element[0].toString());
                 entity.setAlarmFlagName(element[1].toString());
                 entity.setCount(NumberUtils.toLong(element[2].toString()));
                 entity.setAlarmFlagId(NumberUtils.toLong(element[3].toString()));
                 UnitEntity unit = units.stream().filter(u -> element[4].toString().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
                 entity.setUnitName(unit.getSname());
                 entity.setPrdtName(element[5].toString());
                 entity.setCraftRank(NumberUtils.toInt(element[6].toString()));
                 entity.setCraftRankName(entity.getCraftRankName());
                 entity.setAlarmPointId(NumberUtils.toLong(element[7].toString()));
                 entity.setLimitValue(element[8].toString());
                 entity.setLocation(element[9]==null?"":element[9].toString());
                 shakeAlarmAnalysisDataEntityList.add(entity);
			}
			return shakeAlarmAnalysisDataEntityList;
		} catch (Exception e) {
			throw e;
		}
	}

	/**
	 * 震荡报警分析——表格
	 * 
	 * <AUTHOR> 2017-11-02
	 * @param startTime		时间范围开始
	 * @param endTime		时间范围结束
	 * @param alarmPointTag 报警点位号
	 * @param alarmFlagId   报警标识ID
	 * @param page			分页对象
	 * @return 震荡报警事件图表数据
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@Override
	public PaginationBean<ShakeAlarmAnalysisTableEntity> getShakeAlarmAnalysisTableEntity(Date startTime, Date endTime,
			String alarmPointTag, Long alarmFlagId, Pagination page) throws Exception {
		
		try {
			Long total = -1L;
            PaginationBean<ShakeAlarmAnalysisTableEntity> returnList = new PaginationBean<ShakeAlarmAnalysisTableEntity>(page, total);
            int beginIndex = 0;
            int endIndex = 0;
            if(page.getPageSize()!=Integer.MAX_VALUE){
				if("oracle".equals(dbConfig.getDataBase())){
					beginIndex = returnList.getBeginIndex() + 1;
					endIndex = beginIndex + returnList.getPageSize() - 1;
				}else{
					beginIndex = (page.getPageNumber()-1)*page.getPageSize();
					endIndex = page.getPageSize();
				}
			}else{//图形数据查询
				beginIndex = 0;
				endIndex = page.getPageSize();
			}
			//企业
			CommonProperty commonProperty = new CommonProperty();
	        //获取分页数据
	        StoredProcedureQuery queryPage = entityManager.createStoredProcedureQuery("P_AF_GETShakeAlarmAnalysis");
			queryPage.registerStoredProcedureParameter("v_in_startDate", Date.class, ParameterMode.IN);
			queryPage.registerStoredProcedureParameter("v_in_endDate", Date.class, ParameterMode.IN);
			queryPage.registerStoredProcedureParameter("v_in_tag", String.class, ParameterMode.IN);
			queryPage.registerStoredProcedureParameter("v_in_alarmFlagId", Long.class, ParameterMode.IN);
			queryPage.registerStoredProcedureParameter("v_in_beginIndex", Integer.class, ParameterMode.IN);
			queryPage.registerStoredProcedureParameter("v_in_endIndex", Integer.class, ParameterMode.IN);
			queryPage.registerStoredProcedureParameter("v_in_unitIds", String.class, ParameterMode.IN);
			queryPage.registerStoredProcedureParameter("v_in_prdtIds", String.class, ParameterMode.IN);
			queryPage.registerStoredProcedureParameter("v_in_type", Integer.class, ParameterMode.IN);
			queryPage.registerStoredProcedureParameter("v_in_companyId",Integer.class,ParameterMode.IN);
			if("oracle".equals(dbConfig.getDataBase())){
				queryPage.registerStoredProcedureParameter("v_results", void.class, ParameterMode.REF_CURSOR);
			}
			queryPage.setParameter("v_in_startDate", startTime);
			queryPage.setParameter("v_in_endDate", endTime);
			queryPage.setParameter("v_in_tag", alarmPointTag);
			queryPage.setParameter("v_in_alarmFlagId", alarmFlagId);
			queryPage.setParameter("v_in_beginIndex", beginIndex);
			queryPage.setParameter("v_in_endIndex", endIndex);
			queryPage.setParameter("v_in_unitIds", "");
			queryPage.setParameter("v_in_prdtIds", "");
			queryPage.setParameter("v_in_type", 1);
			queryPage.setParameter("v_in_companyId",commonProperty.getCompanyId());

	        List<Object[]> resultPage = queryPage.getResultList();
	        //获取分页集合
	        List<ShakeAlarmAnalysisTableEntity> shakeAlarmAnalysisTableEntityList = new ArrayList<ShakeAlarmAnalysisTableEntity>();
	        resultPage.stream().forEach((element) -> {
	            ShakeAlarmAnalysisTableEntity entity = new ShakeAlarmAnalysisTableEntity();
	            entity.setAlarmTime((Date)element[0]);
	            entity.setPriority(CommonEnum.AlarmPriorityEnum.getName(NumberUtils.toInt(element[1].toString())));
	            entity.setNowValue(element[2]==null?null:element[2].toString());
	            entity.setLimitValue(NumberUtils.toDouble(element[3]==null?null:element[3].toString()));
	            entity.setMeasUnitName(element[4].toString());
	            if (returnList.getTotal() == -1)
                    returnList.setTotal(NumberUtils.toLong(element[5].toString()));
	            shakeAlarmAnalysisTableEntityList.add(entity);
	            entity.setDes(element[6]==null?null:element[6].toString());
				entity.setLocation(element[7]==null?null:element[7].toString());
				entity.setMonitorTypeStr(CommonEnum.MonitorTypeEnum.getName(NumberUtils.toInt(String.valueOf(element[8]))));
	        });
	        returnList.setPageList(shakeAlarmAnalysisTableEntityList);
			return returnList;
		} catch (Exception e) {
			throw e;
		}
	}

	/**
	 * 震荡报警分析——下面图表
	 *
	 * <AUTHOR> 2018-04-23
	 * @param startTime		时间范围开始
	 * @param endTime		时间范围结束
	 * @param alarmPointTag 报警点位号
	 * @param alarmFlagId   报警标识ID
	 * @return 震荡报警事件图表数据
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@Override
	public ShakeAlarmAnalysisChartEntity getShakeAlarmAnalysisChartEntity(Date startTime, Date endTime,
					 String alarmPointTag, Long alarmFlagId) throws Exception {

		Pagination page = new Pagination();
		page.setPageSize(Integer.MAX_VALUE);
		page.setPageNumber(1);

		PaginationBean<ShakeAlarmAnalysisTableEntity> pages = getShakeAlarmAnalysisTableEntity(startTime,endTime,alarmPointTag,alarmFlagId,page);
		List<ShakeAlarmAnalysisTableEntity> saatList = pages.getPageList();

		ShakeAlarmAnalysisChartEntity saacEntity = new ShakeAlarmAnalysisChartEntity();
		AlarmPoint alarmPoint = alarmPointRepository.getSingleAlarmPointByTag(alarmPointTag);
		String hourTime = basicDataService.getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();

		//按时间分组
		DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
		DateFormat dateFormatDetial = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
		DateFormat dateFormatM = new SimpleDateFormat("yyyy/MM/dd HH:mm");
		//原柱状图数据
		Map<String, Long> oriHistogramData = saatList.stream()
				.collect(Collectors.groupingBy(p -> dateFormat.format(DateUtils.addHours(p.getAlarmTime(),
						-Integer.parseInt(hourTime.replace(":00:00","")))) , Collectors.counting()));

		//折线图数据
        List<DictionaryEntity> lineChartMap = new ArrayList();

		//获取x轴日期跨度
		List<String> positionDateList = getVariationTrendDate(startTime, endTime);
		List<DictionaryEntity> oriList = new ArrayList<>();
		//报警次数
		Long alarmTimes = Long.valueOf(saatList.size());

		//平均值，最大值，最小值
		OptionalDouble avgValueOpt =saatList.stream().mapToDouble(x->Double.valueOf(isNumeric(x.getNowValue())?x.getNowValue():"0" )).average();
        OptionalDouble maxValueOpt =saatList.stream().mapToDouble(x->Double.valueOf(isNumeric(x.getNowValue())?x.getNowValue():"0" )).max();
        OptionalDouble minValueOpt =saatList.stream().mapToDouble(x->Double.valueOf(isNumeric(x.getNowValue())?x.getNowValue():"0" )).min();
		Double avgValue = Double.valueOf(String.format("%.2f", avgValueOpt.getAsDouble()));
        Double maxValue = Double.valueOf(maxValueOpt.getAsDouble());
        Double minValue = Double.valueOf(minValueOpt.getAsDouble());

		if (alarmFlagId == CommonEnum.AlarmFlagEnum.PVHH.getIndex()
				|| alarmFlagId == CommonEnum.AlarmFlagEnum.PVHI.getIndex()
				|| alarmFlagId == CommonEnum.AlarmFlagEnum.PVLO.getIndex()
				|| alarmFlagId == CommonEnum.AlarmFlagEnum.PVLL.getIndex()) {

			//限值
			Object limitValue = getLimitValue(alarmPoint, alarmFlagId);
			//大于小于限值集合
			List<ShakeAlarmAnalysisTableEntity> saatNowList = new ArrayList();
			if (alarmFlagId == CommonEnum.AlarmFlagEnum.PVHH.getIndex() || alarmFlagId == CommonEnum.AlarmFlagEnum.PVHI.getIndex()) {
				saatNowList = saatList.stream().filter(x -> StringUtils.isNotEmpty(x.getNowValue())).filter(x->Double.parseDouble(x.getNowValue())>avgValue).collect(Collectors.toList());
			} else if (alarmFlagId == CommonEnum.AlarmFlagEnum.PVLO.getIndex() || alarmFlagId == CommonEnum.AlarmFlagEnum.PVLL.getIndex()) {
				saatNowList = saatList.stream().filter(x -> StringUtils.isNotEmpty(x.getNowValue())).filter(x->Double.parseDouble(x.getNowValue())<avgValue).collect(Collectors.toList());
			}
			//现报警数按分钟分组，每分钟大于3次
			Map<String, Long> nowGroupM = saatNowList.stream().collect(Collectors.groupingBy(p->
					dateFormatM.format(p.getAlarmTime()),Collectors.counting()))
					.entrySet().stream().filter(x->x.getValue()>3).collect(Collectors.toMap(x->x.getKey(),x->x.getValue()));

			//现报警数
			Long alarmNowTimes = nowGroupM.entrySet().stream().collect(Collectors.summingLong(x->x.getValue()));

			//减少比例 =（“报警次数”-“现报警数”）/“报警次数”*100%，保留2位小数；
			Double reduceRate = alarmTimes == 0 ? 0 : Double.valueOf(String.format("%.2f", ((alarmTimes - alarmNowTimes) / (alarmTimes * 1.0)) * 100));

			//现柱状图数据
			Map <String, Long> nowHistogramData = nowGroupM.entrySet().stream().collect
					(Collectors.groupingBy(p ->dateFormat.format(DateUtils.addHours(new Date(p.getKey()),
							-Integer.valueOf(hourTime.replace(":00:00","")))), Collectors.summingLong(p->p.getValue())));

			//获取折线图数据
			saatNowList.stream().forEach(x -> {
				boolean fcont = nowGroupM.entrySet().stream().anyMatch(y->y.getKey().equals(dateFormatM.format(x.getAlarmTime())));
				if (fcont){
                    DictionaryEntity de = new DictionaryEntity();
                    de.setKey(dateFormatDetial.format(x.getAlarmTime()));
                    de.setValue(Double.valueOf(x.getNowValue()));
					lineChartMap.add(de);
				}
			});

			positionDateList.forEach(x -> {
				DictionaryEntity oriEntity = new DictionaryEntity();
				DictionaryEntity itemEntity = new DictionaryEntity();
				Map<String, Long> oriItem = oriHistogramData.entrySet().stream().filter(u -> u.getKey().equals(x)).collect(Collectors.toMap(p->p.getKey(),p->p.getValue()));
				Map<String, Long> nowItem = nowHistogramData.entrySet().stream().filter(u -> u.getKey().equals(x)).collect(Collectors.toMap(p->p.getKey(),p->p.getValue()));
				Long oriValue = oriItem == null ||oriItem.size() ==0 ? 0L : oriItem.get(x).longValue();
				Long nowValue = nowItem == null ||nowItem.size() ==0 ? 0L : nowItem.get(x).longValue();
				itemEntity.setKey(oriValue);
				itemEntity.setValue(nowValue);

				oriEntity.setKey(x);
				oriEntity.setValue(itemEntity);
				oriList.add(oriEntity);
			});

			saacEntity.setNowAlarmTimes(alarmNowTimes);
			saacEntity.setReduceRate(reduceRate);
			saacEntity.setLimitValue(limitValue);
		} else {
			//获取折线图数据
			saatList.stream().forEach(x -> {
				if (isNumeric(x.getNowValue())) {
                    DictionaryEntity de = new DictionaryEntity();
                    de.setKey(dateFormatDetial.format(x.getAlarmTime()));
                    de.setValue(Double.valueOf(x.getNowValue()));
                    lineChartMap.add(de);
                }
			});

			positionDateList.forEach(x -> {
				DictionaryEntity oriEntity = new DictionaryEntity();
				DictionaryEntity itemEntity = new DictionaryEntity();
				Map<String, Long> oriItem = oriHistogramData.entrySet().stream().filter(u -> u.getKey().equals(x)).collect(Collectors.toMap(p -> p.getKey(), p -> p.getValue()));
				Long oriValue = oriItem == null || oriItem.size() == 0 ? 0L : oriItem.get(x).longValue();
				itemEntity.setKey(0);
				itemEntity.setValue(oriValue);

				oriEntity.setKey(x);
				oriEntity.setValue(itemEntity);
				oriList.add(oriEntity);
			});
		}

		//工艺卡片上限值
		DictionaryEntity upLimitValue = new DictionaryEntity();
		upLimitValue.setKey(alarmPoint.getCraftUpLimitInclude());
		upLimitValue.setValue(alarmPoint.getCraftUpLimitValue());
		if (upLimitValue.getKey() == null || upLimitValue.getValue() == null) {
			upLimitValue = new DictionaryEntity();
		}

		//工艺卡片下限值
		DictionaryEntity downLimitValue = new DictionaryEntity();
		downLimitValue.setKey(alarmPoint.getCraftDownLimitInclude());
		downLimitValue.setValue(alarmPoint.getCraftDownLimitValue());
		if (downLimitValue.getKey() == null || downLimitValue.getValue() == null) {
			downLimitValue = new DictionaryEntity();
		}

		saacEntity.setAlarmValue(avgValue == null ? null : avgValue);
		saacEntity.setMaxValue(maxValue == null ? null : maxValue);
		saacEntity.setMinValue(minValue == null ? null : minValue);
		saacEntity.setAlarmFlag(alarmFlagId);
		saacEntity.setAlarmTimes(alarmTimes);
		saacEntity.setUnitFlag(alarmPoint.getMeasUnit().getSign());
		saacEntity.setUpLimitValue(upLimitValue);
		saacEntity.setDownLimitValue(downLimitValue);
		saacEntity.setHistogramData(oriList);
		saacEntity.setLineChartData(lineChartMap);

		return saacEntity;
	}

	/**
	 * 震荡报警分析——下面图表 修改值
	 *
	 * <AUTHOR> 2018-04-23
	 * @param startTime		时间范围开始
	 * @param endTime		时间范围结束
	 * @param alarmPointTag 报警点位号
	 * @param alarmFlagId   报警标识ID
	 * @param alarmValue    修改值
	 * @return 震荡报警事件图表数据
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@Override
	public ShakeAlarmAnalysisChartEntity getChangeShakeAlarmAnalysisl(Date startTime, Date endTime,
															   String alarmPointTag, Long alarmFlagId,Double alarmValue) throws Exception{
		Pagination page = new Pagination();
		page.setPageSize(Integer.MAX_VALUE);
		page.setPageNumber(1);

		PaginationBean<ShakeAlarmAnalysisTableEntity> pages = getShakeAlarmAnalysisTableEntity(startTime,endTime,alarmPointTag,alarmFlagId,page);
		List<ShakeAlarmAnalysisTableEntity> saatList = pages.getPageList();

		//按时间分组
		DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
		DateFormat dateFormatDetial = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
		DateFormat dateFormatM = new SimpleDateFormat("yyyy/MM/dd HH:mm");
		String hourTime = basicDataService.getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();

		//报警次数
		Long alarmTimes = Long.valueOf(saatList.size());

		List<ShakeAlarmAnalysisTableEntity> saatNowList = new ArrayList();
		if (alarmFlagId == CommonEnum.AlarmFlagEnum.PVHH.getIndex() || alarmFlagId == CommonEnum.AlarmFlagEnum.PVHI.getIndex()) {
			saatNowList = saatList.stream().filter(x->Double.valueOf(x.getNowValue())>alarmValue).collect(Collectors.toList());
		} else if (alarmFlagId == CommonEnum.AlarmFlagEnum.PVLO.getIndex() || alarmFlagId == CommonEnum.AlarmFlagEnum.PVLL.getIndex()) {
			saatNowList = saatList.stream().filter(x->Double.valueOf(x.getNowValue())<alarmValue).collect(Collectors.toList());
		}

		//现报警数按分钟分组，每分钟大于3次
		Map<String, Long> nowGroupM = saatNowList.stream().collect(Collectors.groupingBy(p->
				dateFormatM.format(p.getAlarmTime()),Collectors.counting()))
				.entrySet().stream().filter(x->x.getValue()>3).collect(Collectors.toMap(x->x.getKey(),x->x.getValue()));

		//现报警数
		Long alarmNowTimes = nowGroupM.entrySet().stream().collect(Collectors.summingLong(x->x.getValue()));

		//减少比例 =（“报警次数”-“现报警数”）/“报警次数”*100%，保留2位小数；
		Double reduceRate = alarmTimes == 0 ? 0 : Double.valueOf(String.format("%.2f", ((alarmTimes - alarmNowTimes) / (alarmTimes * 1.0)) * 100));

		//现柱状图数据
		Map <String, Long> nowHistogramData = nowGroupM.entrySet().stream().collect
				(Collectors.groupingBy(p ->dateFormat.format(DateUtils.addHours(new Date(p.getKey()),
						-Integer.valueOf(hourTime.replace(":00:00","")))), Collectors.summingLong(p->p.getValue())));

		//折线图数据
        List<DictionaryEntity> lineChartMap = new ArrayList<>();

		//获取折线图数据
		saatNowList.stream().forEach(x -> {
			boolean fcont = nowGroupM.entrySet().stream().anyMatch(y->y.getKey().equals(dateFormatM.format(x.getAlarmTime())));
			if (fcont){

                DictionaryEntity de = new DictionaryEntity();
                de.setKey(dateFormatDetial.format(x.getAlarmTime()));
                de.setValue(Double.valueOf(x.getNowValue()));
                lineChartMap.add(de);
			}
		});

		//获取x轴日期跨度
		List<String> positionDateList = getVariationTrendDate(startTime, endTime);
		List<DictionaryEntity> oriList = new ArrayList<>();

		positionDateList.forEach(x -> {
			DictionaryEntity oriEntity = new DictionaryEntity();
			DictionaryEntity itemEntity = new DictionaryEntity();
			Map<String, Long> nowItem = nowHistogramData.entrySet().stream().filter(u -> u.getKey().equals(x)).collect(Collectors.toMap(p->p.getKey(),p->p.getValue()));
			Long nowValue = nowItem == null ||nowItem.size() ==0 ? 0L : nowItem.get(x).longValue();
			itemEntity.setKey(0);
			itemEntity.setValue(nowValue);
			oriEntity.setKey(x);
			oriEntity.setValue(itemEntity);
			oriList.add(oriEntity);
		});
		ShakeAlarmAnalysisChartEntity saacEntity = new ShakeAlarmAnalysisChartEntity();
		saacEntity.setNowAlarmTimes(alarmNowTimes);
		saacEntity.setReduceRate(reduceRate);
		saacEntity.setHistogramData(oriList);
		saacEntity.setLineChartData(lineChartMap);
		return saacEntity;
	}

	/**
	 * 获取x轴时间
	 *
	 * <AUTHOR> 2017-10-23
	 * @param startTime 开始时间
	 * @param endTime   结束时间
	 * @return 时间日期集合
	 */
	private List<String> getVariationTrendDate(Date startTime, Date endTime) {
		List<String> dateList = new ArrayList<>();
		int diffDays = differentDays(startTime, endTime)+1;
		DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");

		for (int i = 0; i < diffDays; i++) {
			Date newDate = DateUtils.addDays(startTime, i);
			String vtDate = dateFormat.format(newDate);
			dateList.add(vtDate);
		}
		return dateList;
	}
	/**
	 * startDate比endDate多的天数
	 *
	 * <AUTHOR> 2017-10-23
	 * @param startDate 开始时间
	 * @param endDate   结束时间
	 * @return 时间天数差
	 */
	private int differentDays(Date startDate, Date endDate) {
		String hourTime = " " + basicDataService.getQueryTime().stream().findFirst().orElse(new DictionaryEntity()).getValue().toString();
		int diffDays = 0;
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(startDate);

		Calendar cal2 = Calendar.getInstance();
		cal2.setTime(endDate);
		int day1 = cal1.get(Calendar.DAY_OF_YEAR);
		int day2 = cal2.get(Calendar.DAY_OF_YEAR);

		int year1 = cal1.get(Calendar.YEAR);
		int year2 = cal2.get(Calendar.YEAR);
		if (year1 != year2)   //不同一年
		{
			int timeDistance = 0;
			for (int i = year1; i < year2; i++) {
				if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0)    //闰年
				{
					timeDistance += 366;
				} else    //不是闰年
				{
					timeDistance += 365;
				}
			}

			diffDays = timeDistance + (day2 - day1);
		} else    //同年
		{
			diffDays = day2 - day1;
		}

		try {
			//根据业务需要，如果结束时间大于现在日期的8点，则日期加一天
			DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
			DateFormat dateFormat2 = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
			String strNow = dateFormat.format(new Date()) + hourTime;

			if ((endDate.getTime() - (dateFormat2.parse(strNow).getTime())) > 0) {
				diffDays++;
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return diffDays;
	}
	/**
	 * 获取限值
	 *
	 * <AUTHOR> 2017-10-30
	 * @param alarmPoint  报警点对象
	 * @param alarmFlagId 报警标识
	 * @return
	 */
	private Object getLimitValue(AlarmPoint alarmPoint, Long alarmFlagId) {
		Object limitValue = 0;
		switch (Integer.valueOf(alarmFlagId.toString())) {
			case 1:
				limitValue = alarmPoint.getAlarmPointHH();
				break;
			case 2:
				limitValue = alarmPoint.getAlarmPointHI();
				break;
			case 3:
				limitValue = alarmPoint.getAlarmPointLO();
				break;
			case 4:
				limitValue = alarmPoint.getAlarmPointLL();
				break;
		}
		return limitValue;
	}

	/**
	 * 判断字符串是否为数字
	 *
     * <AUTHOR> 2018-04-27
	 * @param str
	 * @return
	 */
	private boolean isNumeric(String str) {
	    if (StringUtils.isEmpty(str)){
	        return false;
        }
        return str.matches("^[-+]?(([0-9]+)([.]([0-9]+))?|([.]([0-9]+))?)$");
	}
}
