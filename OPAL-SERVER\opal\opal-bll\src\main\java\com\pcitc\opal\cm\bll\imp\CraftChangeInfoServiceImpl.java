package com.pcitc.opal.cm.bll.imp;

import com.pcitc.opal.cm.bll.CraftChangeInfoService;
import com.pcitc.opal.cm.bll.entity.CraftChangeInfoEntity;
import com.pcitc.opal.cm.dao.CraftChangeInfoRepository;
import com.pcitc.opal.cm.dao.imp.ChangeEventCondition;
import com.pcitc.opal.cm.pojo.CraftChangeInfo;
import com.pcitc.opal.common.CommonResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

/*
 * 事件类型对照配置业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_AlarmEventTypeCompServiceImpl
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：事件类型对照配置业务逻辑层实现类
 */
@Service
@Component
public class CraftChangeInfoServiceImpl implements CraftChangeInfoService {


    @Autowired
    CraftChangeInfoRepository craftChangeInfoRepository;

    @Override
    public Date getCraftChangeInfoMaxRlsTime(Long comId) throws Exception {
        return craftChangeInfoRepository.getCraftChangeInfoMaxRlsTime(comId);
    }

    @Override
    public CommonResult addCraftChangeInfo(CraftChangeInfo craftChangeInfo) throws Exception {
        return craftChangeInfoRepository.addCraftChangeInfo(craftChangeInfo);
    }

    @Override
    public CraftChangeInfoEntity getCraftChangeInfoInfo(ChangeEventCondition changeEventCondition, Integer val) throws Exception {

        CraftChangeInfoEntity craftChangeInfoEntity = new CraftChangeInfoEntity();
        CraftChangeInfo craftChangeInfo = craftChangeInfoRepository.getCraftChangeInfoInfo(
                changeEventCondition.getUnitCode(),
                changeEventCondition.getTag(),
                changeEventCondition.getAlarmFlagName(),
                changeEventCondition.getStartTime(),
                changeEventCondition.getNowValue(),
                val);
        if (craftChangeInfo != null) {
            BeanUtils.copyProperties(craftChangeInfo, craftChangeInfoEntity);
            return craftChangeInfoEntity;
        } else {
            return null;
        }
    }

    @Override
    public CraftChangeInfo getCraftChangeInfoInfoById(Long craftChangeInfoId) {
        Optional<CraftChangeInfo> byId = craftChangeInfoRepository.findById(craftChangeInfoId);
        if (byId.isPresent()) {
            return byId.get();
        } else {
            return new CraftChangeInfo();
        }

    }
}
