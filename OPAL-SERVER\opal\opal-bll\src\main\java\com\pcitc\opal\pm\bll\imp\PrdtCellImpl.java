package com.pcitc.opal.pm.bll.imp;

import java.util.List;
import java.util.UUID;

import com.pcitc.opal.common.*;
import com.pcitc.opal.pm.dao.AlarmPointRepository;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import com.pcitc.opal.common.CommonEnum.PageModelEnum;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.pm.bll.PrdtCellService;
import com.pcitc.opal.pm.bll.entity.PrdtCellEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.dao.PrdtCellRepository;
import com.pcitc.opal.pm.pojo.PrdtCell;

/*
 * 生产单元业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_PrdtCellImpl
 * 作       者：zheng.yang
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描       述：生产单元业务逻辑层实现类
 */
@Service
@Component
public class PrdtCellImpl implements PrdtCellService {

    @Autowired
    PrdtCellRepository prdtCellRepository;

    @Autowired
    BasicDataService basicDataService;

    @Autowired
    AlarmPointRepository alarmPointRepository;

    /**
     * 新增生产单元
     *
     * @param prdtCellEntity 生产单元实体
     * @throws Exception
     * <AUTHOR> 2017-09-25
     */
    @Transactional
    @Override
    public CommonResult addPrdtCell(PrdtCellEntity prdtCellEntity) throws Exception {
        CommonProperty commonProperty = new CommonProperty();
        // 实体转换为持久层实体
        PrdtCell prdtCellPO = ObjectConverter.entityConverter(prdtCellEntity, PrdtCell.class);
        // 数据校验
        prdtCellValidation(prdtCellPO);
        // 赋值  创建人、创建名称、创建时间
        CommonUtil.returnValue(prdtCellPO, PageModelEnum.NewAdd.getIndex());
        CommonResult commonResult = prdtCellRepository.addPrdtCell(prdtCellPO);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());

        //新增报警点数据
        AlarmPoint alarmPointPo=new AlarmPoint();
        alarmPointPo.setVirtualRealityFlag(1);
        alarmPointPo.setVirtualFlag(1);
        alarmPointPo.setAlarmPointTypeId(99L);
        alarmPointPo.setTag(UUID.randomUUID().toString());
        alarmPointPo.setMonitorType(5);
        alarmPointPo.setInstrmtType(5);
        alarmPointPo.setCraftRank(2);
        alarmPointPo.setMeasunitId(1L);
        alarmPointPo.setSortNum(0);
        alarmPointPo.setInUse(1);
        alarmPointPo.setCompanyId(commonProperty.getCompanyId());
        alarmPointPo.setPrdtCellId(prdtCellPO.getPrdtCellId());
        CommonUtil.returnValue(alarmPointPo, PageModelEnum.NewAdd.getIndex());
        commonResult = alarmPointRepository.addAlarmPoint(alarmPointPo);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 删除生产单元
     *
     * @param prdtCellIds 生产单元ID集合
     * @throws Exception
     * <AUTHOR> 2017-09-25
     */
    @Override
    public CommonResult deletePrdtCell(Long[] prdtCellIds) throws Exception {
        // 判断ID集合是否可用
        if (prdtCellIds == null || prdtCellIds.length <= 0) {
            throw new Exception("没有需要删除的生产单元数据~！");
        }
        List<PrdtCell> prdtCellList = prdtCellRepository.getPrdtCell(prdtCellIds);
        if (prdtCellList == null || prdtCellList.isEmpty())
            return new CommonResult();
        Long[] prdtCellIdList = prdtCellList.stream().map(item -> item.getPrdtCellId()).toArray(Long[]::new);
        // 调用DAL删除方法
        CommonResult commonResult = prdtCellRepository.deletePrdtCell(prdtCellIdList);

        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 修改生产单元
     *
     * @param prdtCellEntity 生产单元实体
     * @throws Exception
     * <AUTHOR> 2017-09-25
     */
    @Override
    public CommonResult updatePrdtCell(PrdtCellEntity prdtCellEntity) throws Exception {
        // 实体转换持久层实体
        PrdtCell prdtCellPO = ObjectConverter.entityConverter(prdtCellEntity, PrdtCell.class);
        // 校验
        prdtCellValidation(prdtCellPO);
        // 实体转换为持久层实体
        prdtCellPO = prdtCellRepository.getSinglePrdtCell(prdtCellPO.getPrdtCellId());
        CommonUtil.objectExchange(prdtCellEntity, prdtCellPO);
        // 赋值 修改人、修改名称、修改时间
        CommonUtil.returnValue(prdtCellPO, PageModelEnum.Edit.getIndex());
        // 调用DAL更新方法
        CommonResult commonResult = prdtCellRepository.updatePrdtCell(prdtCellPO);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 通过生产单元ID获取单条数据
     *
     * @param prdtCellId 生产单元ID
     * <AUTHOR> 2017-09-25
     */
    @Override
    public PrdtCellEntity getSinglePrdtCell(Long prdtCellId) throws Exception {
        PrdtCell prdtCell = prdtCellRepository.getSinglePrdtCell(prdtCellId);
        PrdtCellEntity prdtCellEntity = ObjectConverter.entityConverter(prdtCell, PrdtCellEntity.class);
        List<UnitEntity> unitList = basicDataService.getUnitListByIds(new String[]{prdtCell.getUnitId()}, false);
        if (unitList.size() == 1)
            prdtCellEntity.setUnitName(unitList.get(0).getSname());
        return prdtCellEntity;
    }

    /**
     * 生产单元查询
     *
     * @param unitCodes 装置id集合
     * @param name    生产单元名称
     * @param inUse   是否启用
     * @param page    分页参数
     * @return 生产单元实体（分页）
     * <AUTHOR> 2017-09-25
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<PrdtCellEntity> getPrdtCell(String[] unitCodes, String name, Integer inUse, Pagination page)
            throws Exception {
    	//调用获取装置集合公共方法
    	List<UnitEntity> unitList = basicDataService.getUnitListByIds(unitCodes, false);
		if (ArrayUtils.isEmpty(unitCodes)) {
			unitCodes=unitList.stream().map(ul->ul.getStdCode()).distinct().toArray(String[]::new);
		}
        PaginationBean<PrdtCell> listPrdtCell = prdtCellRepository.getPrdtCell(unitCodes, name, inUse, page);
        PaginationBean<PrdtCellEntity> returnPrdtCell = new PaginationBean<PrdtCellEntity>(page,
                listPrdtCell.getTotal());
        returnPrdtCell.setPageList(ObjectConverter.listConverter(listPrdtCell.getPageList(), PrdtCellEntity.class));
        returnPrdtCell.getPageList().forEach(l->{
            l.setInUseShow(l.getInUse()>0?"是":"否");
        });
        unitList.forEach(y->{
            returnPrdtCell.getPageList().forEach(x -> {
                if (x!=null&&x.getUnitId()!=null&&x.getUnitId().equals(y.getStdCode())) {
                    x.setUnitName(y.getSname());
                }
            });
        });
        return returnPrdtCell;
    }

    /**
     * 判断生产单元在报警点中是否使用
     *
     * <AUTHOR> 2017-11-30
     * @param prdtCellId 报警点实体
     * @return CommonResult 消息结果类
     */
    public CommonResult getPrdtCellIsUseInAlarmPoint(Long prdtCellId) {
        CommonResult commonResult = new CommonResult();
        Long count = 0L;
        count = alarmPointRepository.getPrdtCellIsUseInAlarmPoint(prdtCellId);
        commonResult.setMessage(count+"");
        return commonResult;
    }

    /**
     * 校验
     *
     * @param entity 生产单元实体
     * @throws Exception
     * <AUTHOR> 2017-09-25
     */
    private void prdtCellValidation(PrdtCell entity) throws Exception {
        // 实体不能为空
        if (entity == null) {
            throw new Exception("没有生产单元数据！");
        }
        // 调用DAL与数据库相关的校验
        CommonResult commonResult = prdtCellRepository.prdtCellValidation(entity);

        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
    }
}
