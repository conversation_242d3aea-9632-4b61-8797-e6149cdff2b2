package com.pcitc.opal.cm.dao;

import com.pcitc.opal.cm.dao.imp.ChangeEventCondition;
import com.pcitc.opal.cm.pojo.ChangeEvent;
import com.pcitc.opal.cm.pojo.ChangeEventEntity;
import com.pcitc.opal.cm.pojo.ChangeMonitoringChartEntity;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;

import java.util.Date;
import java.util.List;

/*
 * AlarmEventTypeComp实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_AlarmEventTypeCompRepositoryCustom
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：AlarmEventTypeComp实体的Repository的JPA自定义接口 
 */
public interface ChangeEventRepositoryCustom {


    /**
     * 根据“企业ID”查询“状态”等于“1非法”或“3未知”的<变更事件>
     *
     * @param comId 企业ID
     * @return
     * <AUTHOR> 2018-01-22
     */
    List<ChangeEventCondition> getChangeEventInfo(Long comId);

    /**
     * 根据ID查询<变更事件>
     *
     * @param Id 变更事件ID
     * @return
     * <AUTHOR> 2018-01-22
     */
    ChangeEvent getChangeEventInfoById(Long Id);

    /**
     * 更新变更事件信息
     *
     * @param changeEvent 变更事件
     * @return 返回结果信息类
     * <AUTHOR> 2018-01-22
     */
    CommonResult updateChangeEventInfo(ChangeEvent changeEvent);


    /**
     * 根据装置id和时间查询各个状态的总和
     * @param unitIds     装置数组
     * @param prdtCellIds 生产单元
     * @param tag         位号
     * @param status      变更状态
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return ChangeMonitoringChartEntity实体
     */
    List<ChangeMonitoringChartEntity> selectStatusNumByUnitIds(String[] unitIds, Long[] prdtCellIds, String tag, Integer status, Date startTime, Date endTime);

    PaginationBean<ChangeEventEntity> selectAllByUnitAndStartTime(String[] units, Long[] prdtCellIds, String tag, Integer status, Date startTime, Date endTime, Pagination page);
}
