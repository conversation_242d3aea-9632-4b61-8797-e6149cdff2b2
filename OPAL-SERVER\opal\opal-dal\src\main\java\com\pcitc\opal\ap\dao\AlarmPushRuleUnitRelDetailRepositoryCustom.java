package com.pcitc.opal.ap.dao;

import com.pcitc.opal.ap.dao.imp.AlarmPushRuleDetailEntityVO;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleUnitRelDetailEntityVO;
import com.pcitc.opal.ap.pojo.AlarmPushRuleDetail;
import com.pcitc.opal.ap.pojo.AlarmPushRuleUnitRelDetail;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.Unit;

import java.util.List;

/*
 * 报警知识管理实体的Repository的JPA自定义接口
 * 模块编号： pcitc_opal_dal_interface_AlarmKnowlgManagmtRepositoryCustom
 * 作    者：jiangtao.xue
 * 创建时间：2018/03/09
 * 修改编号：1
 * 描    述：报警知识管理实体的Repository的JPA自定义接口
 */
public interface AlarmPushRuleUnitRelDetailRepositoryCustom {

    PaginationBean<AlarmPushRuleUnitRelDetailEntityVO> getAlarmPushRuleUnitRelDetails(Long apRuleUnitRelId, Pagination page);
    List<String> getUnitByApRule(Long apRuleUnitRelId);
    List<AlarmPushRuleUnitRelDetail> getRelDetailByUnit(Long apRuleUnitRelId,String unitCode);
    CommonResult addAlarmPushRuleUnitRelDetail(AlarmPushRuleUnitRelDetail alarmPushRuleUnitRelDetail);

    CommonResult deleteAlarmPushRuleUnitRelDetail(List<Long> ids);
    CommonResult deleteAlarmPushRuleUnitRelDetailOne(Long ids);
    List<Unit> getAllUnitsUsed(Integer speciality,Long priority);
    CommonResult updateAlarmPushRuleUnitRelDetail(AlarmPushRuleUnitRelDetail alarmPushRuleUnitRelDetail);
    List<Unit> getAllCUnits(Long apRuleUnitRelId);
}
