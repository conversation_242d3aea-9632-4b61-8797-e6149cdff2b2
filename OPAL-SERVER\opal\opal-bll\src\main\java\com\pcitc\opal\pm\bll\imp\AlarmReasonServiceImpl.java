package com.pcitc.opal.pm.bll.imp;

import com.alibaba.fastjson.JSONObject;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.pm.bll.AlarmReasonService;
import com.pcitc.opal.pm.bll.entity.AlarmReasonEntity;
import com.pcitc.opal.pm.dao.AlarmReasonRepository;
import com.pcitc.opal.pm.dao.imp.ReasonIdNameVO;
import com.pcitc.opal.pm.pojo.AlarmReason;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;

import javax.annotation.Resource;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @USER: chenbo
 * @DATE: 2023/6/9
 * @TIME: 15:07
 * @DESC:
 **/
@Service
@Slf4j
public class AlarmReasonServiceImpl implements AlarmReasonService {

    @Autowired
    private AlarmReasonRepository alarmReasonRepository;


    @Override
    public PaginationBean<AlarmReasonEntity> getAlarmReason(Long reasonType, String name, Long inUse, Pagination page) {
        PaginationBean<AlarmReason> alarmReason = alarmReasonRepository.getAlarmReason(reasonType, name, inUse, page);

        PaginationBean<AlarmReasonEntity> alarmReasonPaginationBean = new PaginationBean<>(page, alarmReason.getTotal());

        ArrayList<AlarmReasonEntity> list = new ArrayList<>();
        for (AlarmReason reason : alarmReason.getPageList()) {

            AlarmReasonEntity alarmReasonEntity = new AlarmReasonEntity(reason);
            alarmReasonEntity.setReasonTypeName(CommonEnum.ReasonType.getName(reason.getReasonType()));
            alarmReasonEntity.setInUserName(reason.getInUse() == 1 ? "是" : "否");

            list.add(alarmReasonEntity);
        }

        alarmReasonPaginationBean.setPageList(list);

        return alarmReasonPaginationBean;
    }

    @Override
    public CommonResult addAlarmReason(Long reasonType, String name, Long inUse, Long sortNum, String des) {

        //参数校验
        if (alarmReasonRepository.existsAlarmReason(reasonType, name)) {
            throw new RuntimeException("数据已存在");
        }

        CommonProperty commonProperty = new CommonProperty();

        AlarmReason alarmReason = new AlarmReason();

        alarmReason.setReasonType(reasonType);
        alarmReason.setName(name);
        alarmReason.setDes(des);
        alarmReason.setInUse(inUse);
        alarmReason.setSortNum(sortNum);
        alarmReason.setCrtDate(new Date());
        alarmReason.setCrtUserId(commonProperty.getUserId());
        try {
            alarmReason.setCrtUserName(commonProperty.getUserName());
        } catch (RemoteException e) {
            log.error("获取用户名异常" + e.getMessage());
        }


        CommonResult commonResult = new CommonResult();

        try {
            alarmReasonRepository.saveAndFlush(alarmReason);
            commonResult.setMessage("新增成功");
        } catch (Exception e) {
            commonResult.setMessage("新增失败，" + e.getMessage());
            commonResult.setIsSuccess(false);
        }

        return commonResult;
    }

    @Override
    public CommonResult updateAlarmReason(Long alarmReasonId, Long reasonType, String name, Long inUse, Long sortNum, String des) {

        CommonProperty commonProperty = new CommonProperty();

        AlarmReason alarmReason = alarmReasonRepository.getOne(alarmReasonId);
        alarmReason.setAlarmReasonId(alarmReasonId);
        alarmReason.setReasonType(reasonType);
        alarmReason.setName(name);
        alarmReason.setDes(des);
        alarmReason.setInUse(inUse);
        alarmReason.setSortNum(sortNum);
        alarmReason.setMntDate(new Date());
        alarmReason.setMntUserId(commonProperty.getUserId());
        try {
            alarmReason.setMntUserName(commonProperty.getUserName());
        } catch (RemoteException e) {
            log.error("获取用户名异常" + e.getMessage());
        }

        CommonResult commonResult = new CommonResult();

        //判断该条数据是否存在
        if (alarmReasonRepository.existsById(alarmReasonId)) {
            try {
                commonResult = alarmReasonRepository.updateAlarmReason(alarmReason);
            } catch (Exception e) {
                if (e instanceof DataIntegrityViolationException) {
                    commonResult.setMessage("数据重复，更新失败");
                } else {
                    commonResult.setMessage("更新失败" + e.getMessage());
                    log.error(e.getMessage());
                    e.printStackTrace();
                }
            }
        } else {
            commonResult.setMessage("要更新的数据不存在");
        }

        return commonResult;
    }

    @Override
    public CommonResult deleteAlarmReason(Long[] alarmReasonId) {
        CommonResult commonResult = new CommonResult();

        ArrayList<AlarmReason> alarmReasons = new ArrayList<>();
        for (Long aLong : alarmReasonId) {
            AlarmReason alarmReason = new AlarmReason();
            alarmReason.setAlarmReasonId(aLong);
            alarmReasons.add(alarmReason);
        }

        try {
            alarmReasonRepository.deleteInBatch(alarmReasons);
            commonResult.setMessage("删除成功");
        } catch (Exception e) {
            if (e instanceof DataIntegrityViolationException) {
                commonResult.setMessage("存在关联关系，删除失败");
            } else {
                commonResult.setMessage("删除失败" + e.getMessage());
            }
            commonResult.setIsSuccess(false);
            e.printStackTrace();
        }

        return commonResult;
    }

    @Override
    public ArrayList<DictionaryEntity> getReasonType() {
        ArrayList<DictionaryEntity> list = new ArrayList<>();
        list.add(new DictionaryEntity("-1", "全部"));
        for (CommonEnum.ReasonType value : CommonEnum.ReasonType.values()) {
            list.add(new DictionaryEntity(value.getType() + "", value.getName()));
        }
        return list;
    }

    /**
     * 通过类型获取原因Id和Name
     */
    @Override
    public String getReasonByType(Long reasonType) {
        List<AlarmReason> list =new ArrayList<>();
        list = alarmReasonRepository.getReasonByType(reasonType);
        List<ReasonIdNameVO> listVO =new ArrayList<>();
        list.forEach(l->{
            ReasonIdNameVO vo =new ReasonIdNameVO();
            vo.setId(l.getAlarmReasonId());
            vo.setName(l.getName());
            listVO.add(vo);
        });
        return JSONObject.toJSONString(listVO);
    }
}
