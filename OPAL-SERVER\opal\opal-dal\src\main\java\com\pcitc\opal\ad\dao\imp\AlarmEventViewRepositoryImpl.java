package com.pcitc.opal.ad.dao.imp;

import com.pcitc.opal.ad.dao.AlarmEventViewRepositoryCustom;
import com.pcitc.opal.ad.pojo.AlarmEventView;
import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.DbConfig;
import com.pcitc.opal.common.DbConversion;
import com.pcitc.opal.common.dao.BaseRepository;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.*;

/*
 * AlertEvent实体的Repository实现
 * 模块编号：pcitc_opal_dal_class_AlertEventRepositoryImpl
 * 作    者：kun.zhao
 * 创建时间：2017/10/09
 * 修改编号：1
 * 描    述：AlertEvent实体的Repository实现
 */
public class AlarmEventViewRepositoryImpl extends BaseRepository<AlarmEventView, Long> implements AlarmEventViewRepositoryCustom {
    @Autowired
    private DbConfig dbConfig;
    /**
     * 获取每10分钟报警事件视图实体
     *
     * @param startTime 发生时间范围起始
     * @param endTime   发生时间范围结束
     * @param unitCodes 装置编码集合
     * @return 报警事件实体集合
     * <AUTHOR> 2017-10-17
     */
    @Override
    public List<AlarmEventView> getAlarmEventViewData(Date startTime, Date endTime, String[] unitCodes) {
        try {
            // 查询字符串
            StringBuilder hql = new StringBuilder("from AlarmEventView ae where ae.alarmTime between :startTime and :endTime ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);

            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append(" and ae.unitId in (:unitIds)");
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            TypedQuery<AlarmEventView> query = getEntityManager().createQuery(hql.toString(), AlarmEventView.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 根据装置或者生产单元和时间区间获取高频报警清单列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCodes 装置编码集合
     * @param prdtIds   生产单元集合
     * @return 高频报警清单列表
     * <AUTHOR> 2017-11-15
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<AlarmEventView> getFloodAlarmAnalysisList(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds) {
        List<AlarmEventView> resultList = new ArrayList<>();
        try {
            // 查询字符串
            StringBuilder  hql = new StringBuilder(" select sum(ae.alarm_times),");
                hql.append("         ae.alarm_time,");
                hql.append("         min(ae.unit_code),");
                hql.append("         min(ae.prdtcell_id)");
                hql.append(        "    from\n");
                hql.append("    (SELECT T.EVENT_ID,\n");
                hql.append("       T.ALARM_POINT_ID,\n");
                hql.append("       T.ALARM_TIMES,\n");
                hql.append("       R.UNIT_CODE,\n");
                hql.append("       T.ALARM_TIME,\n");
                hql.append("       A.PRDTCELL_ID,\n");
                hql.append("       "+ DbConversion.nvlFunction()+"(UP.OPER_NUM, 1) AS OPER_NUM\n");
                hql.append("  FROM (SELECT MAX(T.EVENT_ID) EVENT_ID,\n");
                hql.append("               T.ALARM_POINT_ID,\n");
                hql.append("               COUNT(*) ALARM_TIMES,\n");
                if("oracle".equals(dbConfig.getDataBase())){
                    hql.append("               TO_DATE((TO_CHAR(ALARM_TIME, 'YYYY-MM-DD HH24:') ||\n" );
                    hql.append("               TRUNC((TO_NUMBER(TO_CHAR(T.ALARM_TIME, 'MI')) / 10)) || '0'),\n");
                    hql.append("               'YYYY-MM-DD HH24:MI:SS') ALARM_TIME\n");
                }else{
                    hql.append("              STR_TO_DATE(concat(date_format(ALARM_TIME, '%Y-%m-%d %H:') ,\n");
                    hql.append("                       truncate((cast(date_format(T.ALARM_TIME, '%i') as SIGNED ) / 10) , 0 ) , '0'),\n");
                    hql.append("                       '%Y-%m-%d %H:%i:%s') ALARM_TIME\n");
                }
                hql.append("      FROM T_AD_ALARMEVENT T\n");
                hql.append("      WHERE T.EVENT_TYPE_ID = 1001 AND T.company_id=:companyId \n");
                hql.append("      and t.alarm_time between :startTime and :endTime ");
            // 参数集合
            Map<String, Object> paramList = new HashMap<>();
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
            String groupBy = "";
            String prdtCellhql="";
            if (ArrayUtils.isNotEmpty(prdtIds)) {
                if (prdtIds.length > 1) {
                    paramList.put("ids", Arrays.asList(prdtIds));
                    prdtCellhql=" and R.PRDTCELL_ID in(:ids) ";
                } else if (prdtIds.length == 1) {
                    prdtCellhql=" and R.PRDTCELL_ID =:ids ";
                    paramList.put("ids", prdtIds[0]);
                }
                groupBy = " ae.PRDTCELL_ID ";
            } else {
                if (unitCodes.length > 1) {
                    paramList.put("ids", Arrays.asList(unitCodes));
                    hql.append(" and t.unit_code in(:ids) ");
                } else if (unitCodes.length == 1) {
                    hql.append("  and t.unit_code = :ids ");
                    paramList.put("ids", unitCodes[0]);
                }
                groupBy = " ae.UNIT_CODE ";
            }

            hql.append("      AND T.ALARM_POINT_ID is not null and T.ALARM_FLAG_ID is not null and T.priority is not null \n" +
                        "      GROUP BY T.ALARM_POINT_ID,\n");
            if("oracle".equals(dbConfig.getDataBase())) {
                hql.append("               TO_DATE((TO_CHAR(ALARM_TIME, 'YYYY-MM-DD HH24:') ||\n" +
                        "               TRUNC((TO_NUMBER(TO_CHAR(T.ALARM_TIME, 'MI')) / 10)) || '0'),\n" +
                        "               'YYYY-MM-DD HH24:MI:SS')) T\n");
            }else{
                hql.append("               STR_TO_DATE(concat(date_format(ALARM_TIME, '%Y-%m-%d %H:') ,\n" +
                        "               truncate((cast(date_format(T.ALARM_TIME, '%i') as SIGNED ) / 10) , 0 ) , '0'),\n" +
                        "               '%Y-%m-%d %H:%i:%s')) T\n" );
            }
            hql.append(        "    INNER JOIN T_PM_ALARMPOINT A\n" +
                        "      ON T.ALARM_POINT_ID = A.ALARM_POINT_ID\n");
            hql.append("     INNER JOIN T_PM_PRDTCELL R \n" +
                    "      ON A.PRDTCELL_ID = R.PRDTCELL_ID \n "+prdtCellhql);

            hql.append("     LEFT JOIN T_PM_UNITPERSON UP\n" +
                    "      ON R.UNIT_CODE = UP.UNIT_CODE\n" +
                    "       where A.In_Use =1 and R.company_id=:companyId AND A.company_id=:companyId" +
                    "      )ae\n" +
                    "   group by " + groupBy + ", ae.alarm_time\n" +
                    "  having sum(ae.alarm_times) >= 4\n" +
                    "   order by ae.alarm_time asc ");

            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId",commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            List<Object[]> objList = query.getResultList();
            for (Object[] objArr : objList) {
                AlarmEventView view = new AlarmEventView();
                view.setAlarmTimes(NumberUtils.toLong(objArr[0].toString()));
                view.setAlarmTime((Date) objArr[1]);
                view.setUnitId(objArr[2].toString());
                view.setPrdtCellId(NumberUtils.toLong(objArr[3].toString()));
                resultList.add(view);
            }
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }
}
