package com.pcitc.opal.ad.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pcitc.opal.ad.entity.AlarmRecEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pcitc.opal.ad.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 报警记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public interface AlarmRecDAO extends BaseMapper<AlarmRecEntity> {


    List<UnitRecNumVO> getUnitRecNum(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<PriorityRecNumVO> getPriorityRecNum(@Param("startTime") Date startTime);

    List<UnitRecCurrentVO> getUnitRecCurrent(@Param("startTime") Date startTime);

    List<PointRecNumVO> getPointRecNum(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<MonitorRecNumVO> getMonitorRecNum(@Param("startTime") Date startTime);

    List<AlarmAnlyInfoVO> selectAlarmAnlyByEvent(@Param("eventIds") List<Long> eventIds);

    List<AlarmAnlyRecExportVO> selectAnalyseRecExport(@Param("vo") AlarmAnlyRecExportParamVO paramVO);

    List<UnitResponseNumberVO> selectUnitResponseNumber(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    Page<UnitResponseNumberVO> selectUnitResponseNumber(@Param("pager") Page<UnitResponseNumberVO> pager,
                                                        @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<WorkshopResponseNumberVO> selectWorkshopResponseNumber(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    Page<WorkshopResponseNumberVO> selectWorkshopResponseNumber(@Param("pager") Page<WorkshopResponseNumberVO> pager,
                                                                @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<AlarmRecInfoVO> selectInfoList(@Param("vo") AlarmRecInfoParamVO paramVO);

    List<AlarmRecInfoRespModel> getAlarmRecInfo(@Param("reqModel") AlarmRecInfoReqModel reqModel);

}