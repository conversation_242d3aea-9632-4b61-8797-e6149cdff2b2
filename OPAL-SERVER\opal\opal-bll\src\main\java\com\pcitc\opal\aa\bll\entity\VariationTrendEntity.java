package com.pcitc.opal.aa.bll.entity;

import java.util.List;

/*
 * 变化趋势数据实体
 * 模块编号：pcitc_opal_bll_class_VariationTrendData
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/17
 * 修改编号：1
 * 描    述：变化趋势数据实体
 */
public class VariationTrendEntity {
    /**
     * 装置编码
     */
    private String unitId;
    /**
     * 装置名称
     */
    private String unitName;
    /**
     * 报警点数量/平均报警率/峰值报警率/扰动报警率
     */
    private List<Object> alarmRate;

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public List<Object> getAlarmRate() {
        return alarmRate;
    }

    public void setAlarmRate(List<Object> alarmRate) {
        this.alarmRate = alarmRate;
    }

}
