package com.pcitc.opal.af.bll.imp;

import com.pcitc.opal.aa.bll.entity.AlarmNumberAssessEntity;
import com.pcitc.opal.aa.bll.entity.GridViewEntity;
import com.pcitc.opal.ad.dao.AlarmEventDAO;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.imp.AlarmNumStattDtlEntityVO;
import com.pcitc.opal.af.bll.AlarmNumStattService;
import com.pcitc.opal.af.bll.entity.AlarmNumStattCommonEntity;
import com.pcitc.opal.af.bll.entity.AlarmNumStattDataCommonEntity;
import com.pcitc.opal.af.bll.entity.AlarmNumStattDataEntity;
import com.pcitc.opal.af.bll.entity.AlarmNumStattEntity;
import com.pcitc.opal.ap.dao.imp.AlarmPushRuleUnitRelEntityVO;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.ShiftDateCalculator;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.dao.PrdtCellRepository;
import com.pcitc.opal.pm.pojo.PrdtCell;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AlarmNumStattServiceImpl implements AlarmNumStattService {

    @Autowired
    private BasicDataService basicDataService;
    @Autowired
    private AlarmEventRepository alarmEventRepository;
    @Autowired
    private PrdtCellRepository prdtCellRepository;

    @Autowired
    private AlarmEventDAO alarmEventDAO;

    @Override
    public List<AlarmNumStattEntity> getAlarmNumStattStatisticData(String[] unitCodes, Integer[] priority, Long[] alarmFlagIds,
                                                                   Date startDate, Date endDate, Boolean priorityFlag,
                                                                   String endFlag1, Integer isElimination) throws Exception {
        if (unitCodes == null) {
            unitCodes = basicDataService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct()
                    .toArray(String[]::new);
        }
        List alarmNumStattStatisticData = alarmEventRepository.getAlarmNumStattStatisticData(startDate, endDate, unitCodes,
                priority, alarmFlagIds, priorityFlag, isElimination);
        List<AlarmNumStattEntity> returnList = new ArrayList<>();
        Long allTotalAlarmQuantity = 0L;
        for (int i = 0; i < alarmNumStattStatisticData.size(); i++) {
            AlarmNumStattEntity alarmNumStattEntity = new AlarmNumStattEntity();
            Object[] object = (Object[]) alarmNumStattStatisticData.get(i);
            BigInteger totalAlarmQuantity = (BigInteger) object[0];
            alarmNumStattEntity.setTotalAlarmQuantity(totalAlarmQuantity.longValue());
            allTotalAlarmQuantity += totalAlarmQuantity.longValue();
            alarmNumStattEntity.setUnitId((String) object[1]);
            alarmNumStattEntity.setName((String) object[2]);
            alarmNumStattEntity.setSname((String) object[3]);
            BigDecimal emergencyAlarmQuantity = (BigDecimal) object[4];
            alarmNumStattEntity.setEmergencyAlarmQuantity(emergencyAlarmQuantity.longValue());
            BigDecimal importantAlarmQuantity = (BigDecimal) object[5];
            alarmNumStattEntity.setImportantAlarmQuantity(importantAlarmQuantity.longValue());
            BigDecimal generalAlarmQuantity = (BigDecimal) object[6];
            alarmNumStattEntity.setGeneralAlarmQuantity(generalAlarmQuantity.longValue());
            BigDecimal nullAlarmQuantity = (BigDecimal) object[7];
            alarmNumStattEntity.setNullAlarmQuantity(nullAlarmQuantity.longValue());
            returnList.add(alarmNumStattEntity);
        }
        for (AlarmNumStattEntity alarmNumStattEntity : returnList) {
            alarmNumStattEntity.setAllTotalAlarmQuantity(allTotalAlarmQuantity);
        }
        return returnList;
    }

    @Override
    public List<AlarmNumStattEntity> getUnitMonitorAlarmNumStat(String[] unitCodes, Integer[] priority, Long[] alarmFlagIds,
                                                                Date startDate, Date endDate, String endFlag1, Integer isElimination) throws Exception {
        if (unitCodes == null) {
            unitCodes = basicDataService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct()
                    .toArray(String[]::new);
        }

        List alarmNumStattStatisticData = alarmEventRepository.getUnitMonitorAlarmNumStat(startDate, endDate, unitCodes,
                priority, alarmFlagIds, isElimination);
        List<AlarmNumStattEntity> returnList = new ArrayList<>();
        Long allTotalAlarmQuantity = 0L;
        for (int i = 0; i < alarmNumStattStatisticData.size(); i++) {
            AlarmNumStattEntity alarmNumStattEntity = new AlarmNumStattEntity();
            Object[] object = (Object[]) alarmNumStattStatisticData.get(i);
            BigInteger totalAlarmQuantity = (BigInteger) object[0];
            alarmNumStattEntity.setTotalAlarmQuantity(totalAlarmQuantity.longValue());
            allTotalAlarmQuantity += totalAlarmQuantity.longValue();
            alarmNumStattEntity.setUnitId((String) object[1]);
            alarmNumStattEntity.setName((String) object[2]);
            alarmNumStattEntity.setSname((String) object[3]);
            BigDecimal emergencyAlarmQuantity = (BigDecimal) object[4];
            alarmNumStattEntity.setTechnology(emergencyAlarmQuantity.longValue());
            BigDecimal importantAlarmQuantity = (BigDecimal) object[5];
            alarmNumStattEntity.setDevice(importantAlarmQuantity.longValue());
            BigDecimal generalAlarmQuantity = (BigDecimal) object[6];
            alarmNumStattEntity.setSafe(generalAlarmQuantity.longValue());
            BigDecimal other = (BigDecimal) object[7];
            alarmNumStattEntity.setOther(other.longValue());
            BigDecimal nothing = (BigDecimal) object[8];
            alarmNumStattEntity.setNothing(nothing.longValue());
            BigDecimal nullAlarmQuantity = (BigDecimal) object[9];
            alarmNumStattEntity.setNullAlarmQuantity(nullAlarmQuantity.longValue());
            returnList.add(alarmNumStattEntity);
        }
        for (AlarmNumStattEntity alarmNumStattEntity : returnList) {
            alarmNumStattEntity.setAllTotalAlarmQuantity(allTotalAlarmQuantity);
        }
        return returnList;
    }


    @Override
    public List<AlarmNumStattCommonEntity> getAlarmNumStattStatisticDataWorkshop(String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Date startDate, Date endDate, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception {

        if (unitCodes == null)
            unitCodes = basicDataService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);

        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startDate, endDate);
        Date startTime = shiftDateCalculator.getQueryStartTime();
        Date newEndTime = shiftDateCalculator.getQueryEndTime();


        List alarmNumStattStatisticDataWorkshop = alarmEventRepository.getAlarmNumStattStatisticDataWorkshop(startDate, endDate, unitCodes, priority, alarmFlagIds, priorityFlag, isElimination);
        List<AlarmNumStattCommonEntity> returnList = new ArrayList<>();
        Long allTotalAlarmQuantity = 0L;
        for (int i = 0; i < alarmNumStattStatisticDataWorkshop.size(); i++) {
            AlarmNumStattCommonEntity alarmNumStattCommonEntity = new AlarmNumStattCommonEntity();
            Object[] object = (Object[]) alarmNumStattStatisticDataWorkshop.get(i);
//            BigDecimal totalAlarmQuantity = (BigDecimal) object[0];
            BigInteger totalAlarmQuantity = (BigInteger) object[0];
            alarmNumStattCommonEntity.setTotalAlarmQuantity(totalAlarmQuantity.longValue());
            allTotalAlarmQuantity += totalAlarmQuantity.longValue();
            alarmNumStattCommonEntity.setCode((String) object[1]);
            alarmNumStattCommonEntity.setName((String) object[2]);
            alarmNumStattCommonEntity.setSname((String) object[3]);
            BigDecimal emergencyAlarmQuantity = (BigDecimal) object[4];
            alarmNumStattCommonEntity.setEmergencyAlarmQuantity(emergencyAlarmQuantity.longValue());
            BigDecimal importantAlarmQuantity = (BigDecimal) object[5];
            alarmNumStattCommonEntity.setImportantAlarmQuantity(importantAlarmQuantity.longValue());
            BigDecimal generalAlarmQuantity = (BigDecimal) object[6];
            alarmNumStattCommonEntity.setGeneralAlarmQuantity(generalAlarmQuantity.longValue());
            BigDecimal nullAlarmQuantity = (BigDecimal) object[7];
            alarmNumStattCommonEntity.setNullAlarmQuantity(nullAlarmQuantity.longValue());
            returnList.add(alarmNumStattCommonEntity);
        }
        for (AlarmNumStattCommonEntity alarmNumStattCommonEntity : returnList) {
            alarmNumStattCommonEntity.setAllTotalAlarmQuantity(allTotalAlarmQuantity);
        }
        return returnList;
    }

    @Override
    public List<AlarmNumStattCommonEntity> getAlarmNumStattStatisticDataWorkshopType(String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds, Date startDate, Date endDate, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception {

        if (unitCodes == null)
            unitCodes = basicDataService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);

        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startDate, endDate);
        Date startTime = shiftDateCalculator.getQueryStartTime();
        Date newEndTime = shiftDateCalculator.getQueryEndTime();


        List alarmNumStattStatisticDataWorkshop = alarmEventRepository.getAlarmNumStattStatisticDataWorkshopType(startDate, endDate, unitCodes, monitorType, alarmFlagIds, priorityFlag, isElimination);
        List<AlarmNumStattCommonEntity> returnList = new ArrayList<>();
        Long allTotalAlarmQuantity = 0L;
        for (int i = 0; i < alarmNumStattStatisticDataWorkshop.size(); i++) {
            AlarmNumStattCommonEntity alarmNumStattCommonEntity = new AlarmNumStattCommonEntity();
            Object[] object = (Object[]) alarmNumStattStatisticDataWorkshop.get(i);
            BigInteger totalAlarmQuantity = (BigInteger) object[0];
            alarmNumStattCommonEntity.setTotalAlarmQuantity(totalAlarmQuantity.longValue());
            allTotalAlarmQuantity += totalAlarmQuantity.longValue();
            alarmNumStattCommonEntity.setCode((String) object[1]);
            alarmNumStattCommonEntity.setName((String) object[2]);
            alarmNumStattCommonEntity.setSname((String) object[3]);
            BigDecimal emergencyAlarmQuantity = (BigDecimal) object[4];
            alarmNumStattCommonEntity.setTechnology(emergencyAlarmQuantity.longValue());
            BigDecimal importantAlarmQuantity = (BigDecimal) object[5];
            alarmNumStattCommonEntity.setDevice(importantAlarmQuantity.longValue());
            BigDecimal generalAlarmQuantity = (BigDecimal) object[6];
            alarmNumStattCommonEntity.setSafe(generalAlarmQuantity.longValue());
            BigDecimal other = (BigDecimal) object[7];
            alarmNumStattCommonEntity.setOther(other.longValue());
            BigDecimal nothing = (BigDecimal) object[8];
            alarmNumStattCommonEntity.setNothing(nothing.longValue());
            BigDecimal nullAlarmQuantity = (BigDecimal) object[9];
            alarmNumStattCommonEntity.setNullAlarmQuantity(nullAlarmQuantity.longValue());
            returnList.add(alarmNumStattCommonEntity);
        }
        for (AlarmNumStattCommonEntity alarmNumStattCommonEntity : returnList) {
            alarmNumStattCommonEntity.setAllTotalAlarmQuantity(allTotalAlarmQuantity);
        }
        return returnList;
    }

    @Override
    public List<AlarmNumStattCommonEntity> getAlarmNumStattStatisticDataUnit(String[] workshopCodes, String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Date startDate, Date endDate, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception {

        if (unitCodes == null)
            unitCodes = basicDataService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);

        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startDate, endDate);
        Date startTime = shiftDateCalculator.getQueryStartTime();
        Date newEndTime = shiftDateCalculator.getQueryEndTime();


        List alarmNumStattStatisticDataWorkshop = alarmEventRepository.getAlarmNumStattStatisticDataUnit(workshopCodes, startDate, endDate, unitCodes, priority, alarmFlagIds, priorityFlag, isElimination);
        List<AlarmNumStattCommonEntity> returnList = new ArrayList<>();
        Long allTotalAlarmQuantity = 0L;
        for (int i = 0; i < alarmNumStattStatisticDataWorkshop.size(); i++) {
            AlarmNumStattCommonEntity alarmNumStattCommonEntity = new AlarmNumStattCommonEntity();
            Object[] object = (Object[]) alarmNumStattStatisticDataWorkshop.get(i);
//            BigDecimal totalAlarmQuantity = (BigDecimal) object[0];
            BigInteger totalAlarmQuantity = (BigInteger) object[0];
            alarmNumStattCommonEntity.setTotalAlarmQuantity(totalAlarmQuantity.longValue());
            allTotalAlarmQuantity += totalAlarmQuantity.longValue();
            alarmNumStattCommonEntity.setCode((String) object[1]);
            alarmNumStattCommonEntity.setName((String) object[2]);
            alarmNumStattCommonEntity.setSname((String) object[3]);
            BigDecimal emergencyAlarmQuantity = (BigDecimal) object[4];
            alarmNumStattCommonEntity.setEmergencyAlarmQuantity(emergencyAlarmQuantity.longValue());
            BigDecimal importantAlarmQuantity = (BigDecimal) object[5];
            alarmNumStattCommonEntity.setImportantAlarmQuantity(importantAlarmQuantity.longValue());
            BigDecimal generalAlarmQuantity = (BigDecimal) object[6];
            alarmNumStattCommonEntity.setGeneralAlarmQuantity(generalAlarmQuantity.longValue());
            BigDecimal nullAlarmQuantity = (BigDecimal) object[7];
            alarmNumStattCommonEntity.setNullAlarmQuantity(nullAlarmQuantity.longValue());
            returnList.add(alarmNumStattCommonEntity);
        }
        for (AlarmNumStattCommonEntity alarmNumStattCommonEntity : returnList) {
            alarmNumStattCommonEntity.setAllTotalAlarmQuantity(allTotalAlarmQuantity);
        }
        return returnList;
    }

    @Override
    public List<AlarmNumStattCommonEntity> getAlarmNumStattStatisticDataUnitType(String[] workshopCodes, String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds, Date startDate, Date endDate, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception {

        if (unitCodes == null)
            unitCodes = basicDataService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);

        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startDate, endDate);
        Date startTime = shiftDateCalculator.getQueryStartTime();
        Date newEndTime = shiftDateCalculator.getQueryEndTime();


        List alarmNumStattStatisticDataWorkshop = alarmEventRepository.getAlarmNumStattStatisticDataUnitType(workshopCodes, startDate, endDate, unitCodes, monitorType, alarmFlagIds, priorityFlag, isElimination);
        List<AlarmNumStattCommonEntity> returnList = new ArrayList<>();
        Long allTotalAlarmQuantity = 0L;
        for (int i = 0; i < alarmNumStattStatisticDataWorkshop.size(); i++) {
            AlarmNumStattCommonEntity alarmNumStattCommonEntity = new AlarmNumStattCommonEntity();
            Object[] object = (Object[]) alarmNumStattStatisticDataWorkshop.get(i);
            BigInteger totalAlarmQuantity = (BigInteger) object[0];
            alarmNumStattCommonEntity.setTotalAlarmQuantity(totalAlarmQuantity.longValue());
            allTotalAlarmQuantity += totalAlarmQuantity.longValue();
            alarmNumStattCommonEntity.setCode((String) object[1]);
            alarmNumStattCommonEntity.setName((String) object[2]);
            alarmNumStattCommonEntity.setSname((String) object[3]);
            BigDecimal emergencyAlarmQuantity = (BigDecimal) object[4];
            alarmNumStattCommonEntity.setTechnology(emergencyAlarmQuantity.longValue());
            BigDecimal importantAlarmQuantity = (BigDecimal) object[5];
            alarmNumStattCommonEntity.setDevice(importantAlarmQuantity.longValue());
            BigDecimal generalAlarmQuantity = (BigDecimal) object[6];
            alarmNumStattCommonEntity.setSafe(generalAlarmQuantity.longValue());
            BigDecimal other = (BigDecimal) object[7];
            alarmNumStattCommonEntity.setOther(other.longValue());
            BigDecimal nothing = (BigDecimal) object[8];
            alarmNumStattCommonEntity.setNothing(nothing.longValue());
            BigDecimal nullAlarmQuantity = (BigDecimal) object[9];
            alarmNumStattCommonEntity.setNullAlarmQuantity(nullAlarmQuantity.longValue());
            returnList.add(alarmNumStattCommonEntity);
        }
        for (AlarmNumStattCommonEntity alarmNumStattCommonEntity : returnList) {
            alarmNumStattCommonEntity.setAllTotalAlarmQuantity(allTotalAlarmQuantity);
        }
        return returnList;
    }

    @Override
    public List<AlarmNumStattCommonEntity> getAlarmNumStattStatisticDataPrdtcell(String[] unitIds, String[] unitCodes, Integer[] priority, Long[] alarmFlagIds, Date startDate, Date endDate, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception {

        if (unitCodes == null)
            unitCodes = basicDataService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);

        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startDate, endDate);
        Date startTime = shiftDateCalculator.getQueryStartTime();
        Date newEndTime = shiftDateCalculator.getQueryEndTime();


        List alarmNumStattStatisticDataWorkshop = alarmEventRepository.getAlarmNumStattStatisticDataPrdtcell(unitIds, startDate, endDate, unitCodes, priority, alarmFlagIds, priorityFlag, isElimination);
        List<AlarmNumStattCommonEntity> returnList = new ArrayList<>();
        Long allTotalAlarmQuantity = 0L;
        for (int i = 0; i < alarmNumStattStatisticDataWorkshop.size(); i++) {
            AlarmNumStattCommonEntity alarmNumStattCommonEntity = new AlarmNumStattCommonEntity();
            Object[] object = (Object[]) alarmNumStattStatisticDataWorkshop.get(i);
//            BigDecimal totalAlarmQuantity = (BigDecimal) object[0];
            BigInteger totalAlarmQuantity = (BigInteger) object[0];
            alarmNumStattCommonEntity.setTotalAlarmQuantity(totalAlarmQuantity.longValue());
            allTotalAlarmQuantity += totalAlarmQuantity.longValue();
            alarmNumStattCommonEntity.setCode(object[1].toString());
            alarmNumStattCommonEntity.setName((String) object[2]);
            alarmNumStattCommonEntity.setSname((String) object[3]);
            BigDecimal emergencyAlarmQuantity = (BigDecimal) object[4];
            alarmNumStattCommonEntity.setEmergencyAlarmQuantity(emergencyAlarmQuantity.longValue());
            BigDecimal importantAlarmQuantity = (BigDecimal) object[5];
            alarmNumStattCommonEntity.setImportantAlarmQuantity(importantAlarmQuantity.longValue());
            BigDecimal generalAlarmQuantity = (BigDecimal) object[6];
            alarmNumStattCommonEntity.setGeneralAlarmQuantity(generalAlarmQuantity.longValue());
            BigDecimal nullAlarmQuantity = (BigDecimal) object[7];
            alarmNumStattCommonEntity.setNullAlarmQuantity(nullAlarmQuantity.longValue());
            returnList.add(alarmNumStattCommonEntity);
        }
        for (AlarmNumStattCommonEntity alarmNumStattCommonEntity : returnList) {
            alarmNumStattCommonEntity.setAllTotalAlarmQuantity(allTotalAlarmQuantity);
        }
        return returnList;
    }

    @Override
    public List<AlarmNumStattCommonEntity> getAlarmNumStattStatisticDataPrdtcellType(String[] unitIds, String[] unitCodes, Integer[] monitorType, Long[] alarmFlagIds, Date startDate, Date endDate, Boolean priorityFlag, String endFlag, Integer isElimination) throws Exception {

        if (unitCodes == null)
            unitCodes = basicDataService.getUnitList(true).stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);

        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startDate, endDate);
        Date startTime = shiftDateCalculator.getQueryStartTime();
        Date newEndTime = shiftDateCalculator.getQueryEndTime();


        List alarmNumStattStatisticDataWorkshop = alarmEventRepository.getAlarmNumStattStatisticDataPrdtcellType(unitIds, startDate, endDate, unitCodes, monitorType, alarmFlagIds, priorityFlag, isElimination);
        List<AlarmNumStattCommonEntity> returnList = new ArrayList<>();
        Long allTotalAlarmQuantity = 0L;
        for (int i = 0; i < alarmNumStattStatisticDataWorkshop.size(); i++) {
            AlarmNumStattCommonEntity alarmNumStattCommonEntity = new AlarmNumStattCommonEntity();
            Object[] object = (Object[]) alarmNumStattStatisticDataWorkshop.get(i);
            BigInteger totalAlarmQuantity = (BigInteger) object[0];
            alarmNumStattCommonEntity.setTotalAlarmQuantity(totalAlarmQuantity.longValue());
            allTotalAlarmQuantity += totalAlarmQuantity.longValue();
            alarmNumStattCommonEntity.setCode(object[1].toString());
            alarmNumStattCommonEntity.setName((String) object[2]);
            alarmNumStattCommonEntity.setSname((String) object[3]);
            
            BigDecimal emergencyAlarmQuantity = (BigDecimal) object[4];
            alarmNumStattCommonEntity.setTechnology(emergencyAlarmQuantity.longValue());
            BigDecimal importantAlarmQuantity = (BigDecimal) object[5];
            alarmNumStattCommonEntity.setDevice(importantAlarmQuantity.longValue());
            BigDecimal generalAlarmQuantity = (BigDecimal) object[6];
            alarmNumStattCommonEntity.setSafe(generalAlarmQuantity.longValue());
            BigDecimal other = (BigDecimal) object[7];
            alarmNumStattCommonEntity.setOther(other.longValue());
            BigDecimal nothing = (BigDecimal) object[8];
            alarmNumStattCommonEntity.setNothing(nothing.longValue());

            BigDecimal nullAlarmQuantity = (BigDecimal) object[9];
            alarmNumStattCommonEntity.setNullAlarmQuantity(nullAlarmQuantity.longValue());
            returnList.add(alarmNumStattCommonEntity);
        }
        for (AlarmNumStattCommonEntity alarmNumStattCommonEntity : returnList) {
            alarmNumStattCommonEntity.setAllTotalAlarmQuantity(allTotalAlarmQuantity);
        }
        return returnList;
    }

    /**
     * 工艺系统调用--报警次数统计接口
     * @param unitCodes
     * @param priority
     * @param alarmFlagId
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    @Override
    public List<AlarmNumStattDataEntity> getAlarmNumStattStatisticData(String[] unitCodes, Integer[] priority, Long[] alarmFlagId, Date startTime, Date endTime) throws Exception {

        List<PrdtCell> prdtCellList = prdtCellRepository.getPrdtCellByUnitCode(unitCodes);

        String[] unitCodeIds = prdtCellList.stream().map(PrdtCell::getUnitId).distinct().toArray(String[]::new);
        if (unitCodeIds.length == 0)
            return new ArrayList<>();
        List alarmNumStattStatisticData = alarmEventRepository.getAlarmNumStattStatisticData(startTime, endTime, unitCodeIds, priority, alarmFlagId);
        List<AlarmNumStattDataEntity> returnList = new ArrayList<>();
        for (int i = 0; i < alarmNumStattStatisticData.size(); i++) {
            AlarmNumStattDataEntity alarmNumStattDataEntity = new AlarmNumStattDataEntity();
            Object[] object = (Object[]) alarmNumStattStatisticData.get(i);
            BigInteger alarmQuantity = (BigInteger) object[0];
            alarmNumStattDataEntity.setAlarmQuantity((alarmQuantity).longValue());
            alarmNumStattDataEntity.setUnitCode((String) object[1]);
            BigInteger priorityB = (BigInteger) object[2];
            alarmNumStattDataEntity.setPriority(priorityB == null ? 9 : priorityB.intValue());
            for (PrdtCell prdtCell : prdtCellList) {
                if (alarmNumStattDataEntity.getUnitCode().equals(prdtCell.getUnitId())) {
                    alarmNumStattDataEntity.setName(prdtCell.getName());
                }
            }

            String name = priorityB == null ? null : CommonEnum.AlarmPriorityEnum.getName(priorityB.intValue());
            alarmNumStattDataEntity.setPriorityName(name);
            returnList.add(alarmNumStattDataEntity);
        }
        returnList = returnList.stream().sorted(Comparator.comparing(AlarmNumStattDataEntity::getAlarmQuantity).reversed()).collect(Collectors.toList());

        for (PrdtCell prdtCell : prdtCellList) {
            if (!returnList.stream().filter(w -> String.valueOf(w.getUnitCode()).equals(prdtCell.getUnitId().toString())).findAny().isPresent()) {
                for (Integer p : priority) {
                    AlarmNumStattDataEntity alarmNumStattDataEntity = new AlarmNumStattDataEntity();
                    alarmNumStattDataEntity.setPriority(p);
                    alarmNumStattDataEntity.setUnitCode(prdtCell.getUnitId());
                    alarmNumStattDataEntity.setName(prdtCell.getName());
                    alarmNumStattDataEntity.setAlarmQuantity(0L);
                    String name = CommonEnum.AlarmPriorityEnum.getName(p);
                    alarmNumStattDataEntity.setPriorityName(name);
                    returnList.add(alarmNumStattDataEntity);
                }
            }
        }

        return returnList;
    }

    @Override
    public List<AlarmNumStattDataCommonEntity> getAlarmNumStattStatisticDataWorkshop(String[] unitCodes, Integer[] priority, Long[] alarmFlagId, Date startTime, Date endTime) throws Exception {

        List<PrdtCell> prdtCellList = prdtCellRepository.getPrdtCellByUnitCode(unitCodes);

        String[] unitCodeIds = prdtCellList.stream().map(PrdtCell::getUnitId).distinct().toArray(String[]::new);
        if (unitCodeIds.length == 0)
            return new ArrayList<>();
        List alarmNumStattStatisticData = alarmEventRepository.getAlarmNumStattStatisticDataWorkshop(startTime, endTime, unitCodeIds, priority, alarmFlagId);
        List<AlarmNumStattDataCommonEntity> returnList = new ArrayList<>();
        for (int i = 0; i < alarmNumStattStatisticData.size(); i++) {
            AlarmNumStattDataCommonEntity alarmNumStattDataCommonEntity = new AlarmNumStattDataCommonEntity();
            Object[] object = (Object[]) alarmNumStattStatisticData.get(i);
            BigInteger alarmQuantity = (BigInteger) object[0];
            alarmNumStattDataCommonEntity.setAlarmQuantity((alarmQuantity).longValue());
            String code = (String) object[1];
            alarmNumStattDataCommonEntity.setCode(code);
            String sname = (String) object[2];
            alarmNumStattDataCommonEntity.setName(sname);
            BigInteger priorityB = (BigInteger) object[2];
            alarmNumStattDataCommonEntity.setPriority(priorityB == null ? 9 : priorityB.intValue());
            String name = priorityB == null ? null : CommonEnum.AlarmPriorityEnum.getName(priorityB.intValue());
            alarmNumStattDataCommonEntity.setPriorityName(name);
            returnList.add(alarmNumStattDataCommonEntity);
        }
        returnList = returnList.stream().sorted(Comparator.comparing(AlarmNumStattDataCommonEntity::getAlarmQuantity).reversed()).collect(Collectors.toList());
        return returnList;
    }

    @Override
    public List<AlarmNumStattDataCommonEntity> getAlarmNumStattStatisticDataUnit(String[] workshopCodes, String[] unitCodes, Integer[] priority, Long[] alarmFlagId, Date startTime, Date endTime) throws Exception {
        List<PrdtCell> prdtCellList = prdtCellRepository.getPrdtCellByUnitCode(unitCodes);

        String[] unitCodeIds = prdtCellList.stream().map(PrdtCell::getUnitId).distinct().toArray(String[]::new);
        if (unitCodeIds.length == 0)
            return new ArrayList<>();
        List alarmNumStattStatisticData = alarmEventRepository.getAlarmNumStattStatisticDataUnit(workshopCodes, startTime, endTime, unitCodeIds, priority, alarmFlagId);
        List<AlarmNumStattDataCommonEntity> returnList = new ArrayList<>();
        for (int i = 0; i < alarmNumStattStatisticData.size(); i++) {
            AlarmNumStattDataCommonEntity alarmNumStattDataCommonEntity = new AlarmNumStattDataCommonEntity();
            Object[] object = (Object[]) alarmNumStattStatisticData.get(i);
            BigInteger alarmQuantity = (BigInteger) object[0];
            alarmNumStattDataCommonEntity.setAlarmQuantity((alarmQuantity).longValue());
            String code = (String) object[1];
            alarmNumStattDataCommonEntity.setCode(code);
            String sname = (String) object[2];
            alarmNumStattDataCommonEntity.setName(sname);
            BigInteger priorityB = (BigInteger) object[2];
            alarmNumStattDataCommonEntity.setPriority(priorityB == null ? 9 : priorityB.intValue());
            String name = priorityB == null ? null : CommonEnum.AlarmPriorityEnum.getName(priorityB.intValue());
            alarmNumStattDataCommonEntity.setPriorityName(name);
            returnList.add(alarmNumStattDataCommonEntity);
        }
        returnList = returnList.stream().sorted(Comparator.comparing(AlarmNumStattDataCommonEntity::getAlarmQuantity).reversed()).collect(Collectors.toList());
        return returnList;
    }

    @Override
    public List<AlarmNumStattDataCommonEntity> getAlarmNumStattStatisticDataPrdtcell(String[] unitIds, String[] unitCodes, Integer[] priority, Long[] alarmFlagId, Date startTime, Date endTime) throws Exception {
        List<PrdtCell> prdtCellList = prdtCellRepository.getPrdtCellByUnitCode(unitCodes);

        String[] unitCodeIds = prdtCellList.stream().map(PrdtCell::getUnitId).distinct().toArray(String[]::new);
        if (unitCodeIds.length == 0)
            return new ArrayList<>();
        List alarmNumStattStatisticData = alarmEventRepository.getAlarmNumStattStatisticDataPrdtcell(unitIds, startTime, endTime, unitCodeIds, priority, alarmFlagId);
        List<AlarmNumStattDataCommonEntity> returnList = new ArrayList<>();
        for (int i = 0; i < alarmNumStattStatisticData.size(); i++) {
            AlarmNumStattDataCommonEntity alarmNumStattDataCommonEntity = new AlarmNumStattDataCommonEntity();
            Object[] object = (Object[]) alarmNumStattStatisticData.get(i);
            BigInteger alarmQuantity = (BigInteger) object[0];
            alarmNumStattDataCommonEntity.setAlarmQuantity((alarmQuantity).longValue());
            String code = (String) object[1];
            alarmNumStattDataCommonEntity.setCode(code);
            String sname = (String) object[2];
            alarmNumStattDataCommonEntity.setName(sname);
            BigInteger priorityB = (BigInteger) object[2];
            alarmNumStattDataCommonEntity.setPriority(priorityB == null ? 9 : priorityB.intValue());
            String name = priorityB == null ? null : CommonEnum.AlarmPriorityEnum.getName(priorityB.intValue());
            alarmNumStattDataCommonEntity.setPriorityName(name);
            returnList.add(alarmNumStattDataCommonEntity);
        }
        returnList = returnList.stream().sorted(Comparator.comparing(AlarmNumStattDataCommonEntity::getAlarmQuantity).reversed()).collect(Collectors.toList());
        return returnList;
    }

    private List getAlarmNumStattStatisticDataPage(Date startTime, Date endTime, String[] unitCodeIds, Integer[] priority, Long[] alarmFlagId) {
        List alarmNumStattStatisticData = new ArrayList();
        List<String> asList = Arrays.asList(unitCodeIds);


        int pageSize = 40;
        for (int i = 0; i < asList.size(); i++) {
            List<String> asList2 = asList.stream()
                    .skip(i * pageSize)
                    .limit(pageSize)
                    .collect(Collectors.toList());
            if (asList2.size() != 0) {
                List alarmNumStattL = alarmEventRepository.getAlarmNumStattStatisticData(startTime, endTime, asList2.toArray(new String[asList2.size()]), priority, alarmFlagId);
                alarmNumStattStatisticData.addAll(alarmNumStattL);
            }
        }
        return alarmNumStattStatisticData;
    }

    /**
     * 报警数量统计——报警数趋势图
     * @param startTime
     * @param endTime
     * @param unitIds
     * @param priority
     * @param priorityFlag
     * @return
     * @throws Exception
     */
    @Override
    public List<AlarmNumberAssessEntity> getAlarmNumberList(Date startTime, Date endTime, Long[] alarmFlagId, String[] unitIds, Integer[] priority, Boolean priorityFlag, Integer isElimination) throws Exception {
        CommonEnum.DateTypeEnum dateTypeEnum = CommonEnum.DateTypeEnum.fromValue("day");
        CommonEnum.EquipmentTypeEnum equipmentTypeEnum = CommonEnum.EquipmentTypeEnum.Unit;

        /*ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startTime, endTime);
        startTime = shiftDateCalculator.getQueryStartTime();
        endTime = shiftDateCalculator.getQueryEndTime();*/
        return basicDataService.getAlarmNumberAssessList(startTime, endTime, alarmFlagId, dateTypeEnum, unitIds, equipmentTypeEnum, priority, priorityFlag, isElimination);
        //return null;
    }

    @Override
    public PaginationBean<AlarmNumStattDtlEntityVO> getAlarmNumStattDtl(
            Date startDate, Date endDate, String unitCode, Integer[] priority, Long[] alarmFlagIds, Pagination page) {
        ShiftDateCalculator shiftDateCalculator = new ShiftDateCalculator(startDate, endDate);
        Date startTime = shiftDateCalculator.getQueryStartTime();
        Date newEndTime = shiftDateCalculator.getQueryEndTime();
        return alarmEventRepository.getAlarmNumStattDtl(startDate, endDate, unitCode, priority, alarmFlagIds, page);
    }
}
