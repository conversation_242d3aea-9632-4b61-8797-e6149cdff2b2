package com.pcitc.opal.pm.bll;

import com.pcitc.opal.pm.bll.entity.AlarmEventCacheEntity;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/*
 * 未分类数据查询业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_UnclaimDataService
 * 作       者：xuelei.wang
 * 创建时间：2018-04-16
 * 修改编号：1
 * 描       述：未分类数据查询业务逻辑层接口
 */
@Service
public interface UnclaimDataService {
    /**
     * 获取未匹配计量单位列表
     *
     * @return 未匹配计量单位列表
     * <AUTHOR> 2018-04-19
     */
    List<AlarmEventCacheEntity> getCacheMeasUnitList(Long[] dcsCodeIds,String measUnit, Long reason, Date startTime, Date endTime) throws Exception;

    /**
     * 获取未匹配优先级列表
     *
     * @param priority  优先级
     * @param dcsIds    DCSID集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 未匹配优先级列表
     * <AUTHOR> 2018-04-17
     */
    List<AlarmEventCacheEntity> getCachePriorityList(String priority, Long[] dcsIds, Date startTime, Date endTime) throws Exception;

    /**
     * 获取未匹配报警标识列表
     *
     * @param alarmFlag  报警标识
     * @param dcsIds    DCSID集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 未匹配报警标识列表
     * <AUTHOR> 2018-04-17
     */
    List<AlarmEventCacheEntity> getCacheAlarmFlagList(String alarmFlag, Long[] dcsIds, Date startTime, Date endTime) throws Exception;

    /**
     * 获取未匹配报警点列表
     *
     *
     * @param alarmPoint 报警点
     * @param dcsIds    DCSID集合
     * @param reason    不一致原因
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 未匹配报警点列表
     * <AUTHOR> 2018-04-26
     */
    List<AlarmEventCacheEntity> getCacheAlarmPointList(String alarmPoint, Long[] dcsIds,Long reason, Date startTime, Date endTime) throws Exception;

    /**
     * 获取未匹配生产单元列表
     *
     * @param prdtCell  生产单元
     * @param dcsIds    DCSID集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 未匹配生产单元列表
     * <AUTHOR> 2018-04-17
     */
    List<AlarmEventCacheEntity> getCachePrdtCellList(String prdtCell,Long[] dcsIds, Date startTime, Date endTime) throws Exception;

    /**
     * 获取未匹配事件名称列表
     *
     *
     * @param eventName 事件名称
     * @param eventType 事件名称
     * @param dcsIds    DCSID集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 未匹配事件名称列表
     * <AUTHOR> 2018-04-17
     */
    List<AlarmEventCacheEntity> getCacheEventNameList(String eventName, String eventType, Long[] dcsIds, Date startTime, Date endTime) throws Exception;

    /**
     * 获取未匹配事件类型列表
     *
     *
     * @param eventType 事件类型
     * @param dcsIds    DCSID集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 未匹配事件类型列表
     * <AUTHOR> 2018-04-17
     */
    List<AlarmEventCacheEntity> getCacheEventTypeList(String eventType, Long[] dcsIds, Date startTime, Date endTime) throws Exception;

    /**
     * 获取未配置数据分页
     *
     * @param dcsCodeIds      DcsCodeID
     * @param prdtCell        生产单元
     * @param alarmPoint      报警点
     * @param alarmFlag       报警标识
     * @param eventTypeSource 源事件类型
     * @param eventNameSource 源事件名称
     * @param priority        优先级
     * @param startTime       查询开始时间
     * @param endTime         查询结束时间
     * @param page
     * @return 未匹配数据
     * @throws Exception
     * <AUTHOR>  2018-04-16
     */
    PaginationBean<AlarmEventCacheEntity> getUnconfiguredDataList(Long[] dcsCodeIds,
                                                                  String prdtCell,
                                                                  String alarmPoint,
                                                                  String alarmFlag,
                                                                  String eventTypeSource,
                                                                  String eventNameSource,
                                                                  String priority,
                                                                  Date startTime,
                                                                  Date endTime,
                                                                  Pagination page) throws Exception;
    /**
     * 获取计量单位未配置数据分页
     *
     * @param dcsCodeIds        DcsCodeID
     * @param measUnit         计量单位
     * @param reson            不一致原因
     * @param startTime       查询开始时间
     * @param endTime         查询结束时间
     * @param page
     * @return                未匹配数据
     * @throws Exception
     * <AUTHOR>  2018-04-19
     */
    PaginationBean<AlarmEventCacheEntity> getUnconfiguredMeasUnitList(Long[] dcsCodeIds,String measUnit,Long reson,
                                                                      Date startTime,Date endTime,Pagination page) throws Exception;

    /**
     * 获取报警点未分类数据分页
     *
     * @param dcsCodeIds        DcsCodeID
     * @param alarmPoint         报警点
     * @param reson            不一致原因
     * @param startTime       查询开始时间
     * @param endTime         查询结束时间
     * @param page
     * @return                未匹配数据
     * @throws Exception
     * <AUTHOR>  2018-04-26
     */
    PaginationBean getUnconfiguredAlarmPointList(Long[] dcsCodeIds,String alarmPoint,Long reson,
                                                 Date startTime,Date endTime,Pagination page) throws Exception;
}
