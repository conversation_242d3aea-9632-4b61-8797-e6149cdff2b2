package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.Unit;

import java.util.List;

/*
 * Unit实体的Repository的JPA自定义接口
 * 模块编号：pcitc_opal_dal_interface_UnitRepositoryCustom
 * 作       者：xuelei.wang
 * 创建时间：2017-12-11
 * 修改编号：1
 * 描       述：Unit实体的Repository的JPA自定义接口
 */
public interface UnitRepositoryCustom {
	/**
	 * 校验数据
	 *
	 * <AUTHOR> 2017-12-11
	 * @param unit 装置实体
	 * @return     返回结果信息类
	 */
	CommonResult unitValidation(Unit unit);

	/**
	 * 新增数据
	 *
	 * <AUTHOR> 2017-12-11
	 * @param unit 装置实体
	 * @return 返回结果信息类
	 */
	CommonResult addUnit(Unit unit);

	/**
	 * 删除数据
	 *
	 * <AUTHOR> 2017-12-11
	 * @param unitIds 装置ID集合
	 * @return 返回结果信息类
	 */
	CommonResult deleteUnit(Long[] unitIds);

	/**
	 * 更新数据
	 *
	 * <AUTHOR> 2017-12-11
	 * @param unit
	 * @return 返回结果信息类
	 */
	CommonResult updateUnit(Unit unit);

	/**
	 * 获取单条数据
	 *
	 * <AUTHOR> 2017-12-11
	 * @param unitId  装置ID
	 * @return 装置实体
	 */
	Unit getSingleUnit(Long unitId);

	/**
	 * 获取多条数据
	 *
	 * <AUTHOR> 2017-12-11
	 * @param unitIds 装置ID集合
	 * @return 装置实体集合
	 */
	List<Unit> getUnit(Long[] unitIds);

	/**
	 *获取装置分页数据
	 *
	 * @param factoryId   工厂ID
	 * @param workshopIds  车间ID集合
	 * @param name        名称或简称
	 * @param stdCode     编码
	 * @param inUse       是否启用
	 * @param page        分页信息
	 * @return            装置分页数据
	 * <AUTHOR> 2017-12-11
	 */
	PaginationBean<Unit> getUnitList(Long factoryId,Long[] workshopIds, String name,String stdCode, Integer inUse, Pagination page) throws Exception;

	Unit getUnitByStdCode(String unitId);

	/**
	 * 通过装置编码和指定的企业id查询装置
	 */
	Unit getUnitByStdCode(String unitId, Integer companyId);

	Unit getUnitInfoByStdCode(String stdCode);

	/**
	 * 根据企业id查询装置
	 * @param companyId 企业id
	 * @return list
	 */
	List<Unit> getUnitByCompanyId(Long companyId);

	boolean isFilterWorkTeam(String[] unitId);
}
