package com.pcitc.opal.ad.dao.imp;

import com.pcitc.opal.ad.dao.AlarmEventCacheRepositoryCustom;
import com.pcitc.opal.ad.pojo.AlarmEventCache;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.dao.BaseRepository;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.EntityTransaction;
import javax.persistence.PersistenceContext;
import java.util.List;

public class AlarmEventCacheRepositoryImpl extends BaseRepository<AlarmEventCache,Long> implements AlarmEventCacheRepositoryCustom {

    @Transactional
    @Override
    @Async
    public CommonResult addAlarmEventCache(List<AlarmEventCache> alarmEventCacheList) {
        CommonResult commonResult = new CommonResult();
        try {
            for(AlarmEventCache po :alarmEventCacheList){
                this.getEntityManager().persist(po);
            }
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
            return commonResult;
        }
        return commonResult;

//        int batchSize = 100;
//
//        EntityTransaction transaction = null;
//        CommonResult commonResult = new CommonResult();
//
//        try {
//            transaction = entityManager.getTransaction();
//            transaction.begin();
//
//            for ( int i = 0; i < alarmEventCache.size(); ++i ) {
//                if ( i > 0 && i % batchSize == 0 ) {
//                    entityManager.flush();
//                    entityManager.clear();
//                    transaction.commit();
//                    transaction.begin();
//                }
//                entityManager.persist(alarmEventCache.get(i));
//            }
//            transaction.commit();
//        } catch (RuntimeException e) {
////            if ( transaction != null &&
////                    transaction.isActive()) {
////                transaction.rollback();
////            }
//            throw e;
//        } finally {
//            if (entityManager != null) {
//                entityManager.close();
//            }
//        }
//        return commonResult;
    }
}
