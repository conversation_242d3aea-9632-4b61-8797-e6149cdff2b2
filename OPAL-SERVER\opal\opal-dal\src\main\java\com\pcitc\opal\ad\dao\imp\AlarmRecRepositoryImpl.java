package com.pcitc.opal.ad.dao.imp;

import com.pcitc.opal.ad.dao.AlarmRecRepositoryCustom;
import com.pcitc.opal.ad.pojo.AlarmRec;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.annotation.MeasureTime;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.common.pojo.DateRange;
import com.pcitc.opal.pm.dao.AlarmPointDelConfigRepository;
import com.pcitc.opal.pm.pojo.AlarmPointDelConfig;
import com.pcitc.opal.pm.pojo.AlarmRecLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class AlarmRecRepositoryImpl extends BaseRepository<AlarmRec, Long> implements AlarmRecRepositoryCustom {

    @Resource
    private AlarmPointDelConfigRepository alarmPointDelConfigRepository;
    private static final Logger logger = LoggerFactory.getLogger(AlarmRecRepositoryImpl.class);
    @Autowired
    private DbConfig dbConfig;

    @Override
    public List<AlarmRec> getAlarmDurationStatt(String[] unitIds, Integer[] priority, Date startTime, Date endTime,
                                                Boolean priorityFlag, String tag, Long alarmFlagId,
                                                Integer isElimination) {
        try {
            Map<String, Object> paramList = new HashMap<>();
            // 查询字符串
            StringBuilder hql = new StringBuilder(" from AlarmRec ar ");
            // 关联报警点
            hql.append(" inner join fetch ar.alarmPoint ap ");
            // 关联报警标识
            hql.append(" inner join fetch ar.alarmFlag af ");
            // 关联报警事件类型
            hql.append(" inner join fetch ar.eventType et ");

            hql.append(" inner join fetch ar.prdtCell pc ");

            hql.append(
                    " where ap.inUse =1 and ar.companyId=:companyId and ap.companyId=:companyId and pc.companyId=:companyId ");

            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append(" and ar.unitCode in (:unitCode)");
                paramList.put("unitCode", Arrays.asList(unitIds));
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //开始过滤需要剔除的数据
            if (isElimination == 0) {
                //查询剔除配置信息
                for (AlarmPointDelConfig a : alarmPointDelConfigRepository.selectAlarmPointDelConfigDetail(unitIds,
                        startTime, endTime)) {
                    hql.append(" and ( ");
                    if (a.getAlarmPointId() != "") {
                        hql.append("ap.alarmPointId not in (").append((a.getAlarmPointId())).append(") ");
                        hql.append(" or ");
                    }
                    //过滤报警标识
                    if (!a.getAlarmFlagId().equals("")) {
                        hql.append("af.alarmFlagId not in (").append((a.getAlarmFlagId())).append(") ");
                        hql.append(" or ");
                    }
                    //过滤时间
                    hql.append("ar.alarmTime not between '").append(sdf.format(a.getDelStartTime())).append("' and '")
                            .append(sdf.format(a.getDelEndTime())).append("' ");
                    hql.append(" ) ");
                }

            }

            //过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ar.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ar.priority in (:priority) or ar.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ar.priority is null ");
            }
            //过滤位号
            if (!StringUtils.isEmpty(tag)) {
                hql.append(" and ar.tag =:tag ");
                paramList.put("tag", tag);
            }
            //过滤报警标识
            if (null != alarmFlagId && alarmFlagId != 0) {
                hql.append(" and ar.alarmFlagId =:alarmFlagId ");
                paramList.put("alarmFlagId", alarmFlagId);
            }

            //过滤时间
            if (null != startTime && null != endTime) {
                hql.append(" and ar.alarmTime <= :endTime and ar.recoveryTime >=:startTime ");
                paramList.put("endTime", endTime);
                paramList.put("startTime", startTime);
            }
            //根据“报警时间+位号+报警标识”倒序排列
            hql.append(" order by ar.alarmTime desc");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            TypedQuery<AlarmRec> query = getEntityManager().createQuery(hql.toString(), AlarmRec.class);
            this.setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public PaginationBean<AlarmRec> getAlarmDurationStatt(Pagination page, String[] unitIds, Integer[] priority,
                                                          Date startTime, Date endTime, Boolean priorityFlag,
                                                          String tag, Long[] alarmFlagId, Integer isElimination) {
        try {
            Map<String, Object> paramList = new HashMap<>();
            // 查询字符串
            StringBuilder hql = new StringBuilder(" from AlarmRec ar ");
            // 关联报警点
            hql.append(" inner join fetch ar.alarmPoint ap ");
            // 关联报警标识
            hql.append(" inner join fetch ar.alarmFlag af ");
            // 关联报警事件类型
            hql.append(" inner join fetch ar.eventType et ");

            hql.append(" inner join fetch ar.prdtCell pc ");

            hql.append(
                    " where ap.inUse =1 and ar.companyId=:companyId and ap.companyId=:companyId and pc.companyId=:companyId ");

            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append(" and ar.unitCode in (:unitCode)");
                paramList.put("unitCode", Arrays.asList(unitIds));
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //开始过滤需要剔除的数据
            if (isElimination == 0) {
                //查询剔除配置信息
                for (AlarmPointDelConfig a : alarmPointDelConfigRepository.selectAlarmPointDelConfigDetail(unitIds,
                        startTime, endTime)) {
                    hql.append(" and ( ");
                    if (a.getAlarmPointId() != "") {
                        hql.append("ap.alarmPointId not in (").append((a.getAlarmPointId())).append(") ");
                        hql.append(" or ");
                    }
                    //过滤报警标识
                    if (!a.getAlarmFlagId().equals("")) {
                        hql.append("af.alarmFlagId not in (").append((a.getAlarmFlagId())).append(") ");
                        hql.append(" or ");
                    }
                    //过滤时间
                    hql.append("ar.alarmTime not between '").append(sdf.format(a.getDelStartTime())).append("' and '")
                            .append(sdf.format(a.getDelEndTime())).append("' ");
                    hql.append(" ) ");
                }

            }

            //过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ar.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ar.priority in (:priority) or ar.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ar.priority is null ");
            }
            //过滤位号
            if (!StringUtils.isEmpty(tag)) {
                hql.append(" and ar.tag =:tag ");
                paramList.put("tag", tag);
            }
            //过滤报警标识
//            if (null != alarmFlagId && alarmFlagId != 0) {
//                hql.append(" and ar.alarmFlagId =:alarmFlagId ");
//                paramList.put("alarmFlagId", alarmFlagId);
//            }
            // 过滤报警标识
            if (alarmFlagId != null && alarmFlagId.length > 0) {
                for (int i = 0; i < alarmFlagId.length; i++) {
                    if (alarmFlagId[i] == (-9L)) {
                        hql.append(" and ( ar.alarmFlagId in (:alarmFlagIds) or ar.alarmFlagId is null) ");
                        break;
                    }
                    if (i == alarmFlagId.length - 1) {
                        hql.append(" and  ar.alarmFlagId in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagId));
            }

            //过滤时间
            if (null != startTime && null != endTime) {
                hql.append(" and ar.alarmTime <= :endTime and ar.recoveryTime >=:startTime ");
                paramList.put("endTime", endTime);
                paramList.put("startTime", startTime);
            }
            //根据“报警时间+位号+报警标识”倒序排列
            hql.append(" order by ar.alarmTime desc");
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
//            TypedQuery<AlarmRec> query = getEntityManager().createQuery(hql.toString(), AlarmRec.class);
//            this.setParameterList(query, paramList);
            return this.findAll(page, hql.toString(), paramList);
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<Object[]> getAlarmDurationStattMain(String[] unitIds, Long[] alarmFlagIds, Integer[] priority, Date
            startTime, Date endTime, Boolean priorityFlag, Pagination page, Integer isElimination) {
        try {
            Map<String, Object> paramList = new HashMap<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            StringBuilder hql = new StringBuilder();
            // 查询字符串
            if ("oracle".equals(dbConfig.getDataBase())) {
                hql.append("SELECT *\n" +
                        "  FROM (SELECT tt.*, ROWNUM AS rowno\n" +
                        "  FROM (\n");
            }
            hql.append(
                    "select ar.unit_code, max(ar.prdtcell_id),pc.sname as pcSname, ar.tag, ar.des, ar.alarm_flag_id,af.name as afName," +
                            "sum(round(" + DbConversion.toNumber() + "((" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "          then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "          else ar.recovery_time end ) ",
                            " (case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "          then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                         else ar.alarm_time end)") + " ) * 24 * 60),2)) as continuousHour,ar.priority, ap.monitor_type, ap.location\n");


            hql.append("  from t_ad_AlarmRec ar\n" +
                    " left join t_pm_alarmpoint ap on ar.alarm_point_id = ap.alarm_point_id\n" +
                    " left join t_ad_alarmflag af on ar.alarm_flag_id= af.alarm_flag_id\n" +
//                    " left join t_pm_eventtype et on ar.event_type_id = et.event_type_id\n" +
                    " left join t_pm_prdtcell pc on ar.prdtcell_id =pc.prdtcell_id\n" +
                    " where ap.in_use = 1 and ar.company_id=:companyId ");


            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append(" and ar.unit_code = (:unitCode)");
                paramList.put("unitCode", unitIds[0]);
            }

            //开始过滤需要剔除的数据
//            if (isElimination == 0){
//                List<AlarmPointDelConfig> alarmPointDelConfigs = alarmPointDelConfigRepository.selectAlarmPointDelConfigDetail(unitIds, startTime, endTime);
//                //查询剔除配置信息
//                for (AlarmPointDelConfig a : alarmPointDelConfigs) {
//                    hql.append(" and ( ");
//                    if (a.getAlarmPointId()!=""){
//                        hql.append("ap.alarm_point_id not in (").append((a.getAlarmPointId())).append(") ");
//                        hql.append(" or ");
//                    }
//                    //过滤报警标识
//                    if (!a.getAlarmFlagId().equals("")){
//                        hql.append("af.alarm_flag_id not in (").append((a.getAlarmFlagId())).append(") ");
//                        hql.append(" or ");
//                    }
//                    //过滤时间
//                    hql.append("ar.alarm_time not between '").append(sdf.format(a.getDelStartTime())).append("' and '").append(sdf.format(a.getDelEndTime())).append("' ");
//                    hql.append(" ) ");
//                }
//
//            }

            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ar.alarm_flag_id in (:alarmFlagIds) or ar.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ar.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            //过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ar.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ar.priority in (:priority) or ar.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ar.priority is null ");
            }

            //过滤时间
            if (null != startTime && null != endTime) {
                hql.append(" and ar.alarm_time <= :endTime and ar.recovery_time >=:startTime ");
                paramList.put("endTime", endTime);
                paramList.put("startTime", startTime);
            }
            hql.append("  group by ar.tag, ar.alarm_flag_id, ar.priority ");
            //根据“报警时间+位号+报警标识”倒序排列
            hql.append(" order by continuousHour desc,ar.tag desc, ar.alarm_flag_id desc\n");
            if ("oracle".equals(dbConfig.getDataBase())) {
                hql.append(") tt\n" +
                        "         WHERE ROWNUM <= :big) table_alias\n" +
                        " WHERE table_alias.rowno > :smail");
                paramList.put("big", page.getPageNumber() * page.getPageSize());
                paramList.put("smail", (page.getPageNumber() - 1) * page.getPageSize());
            } else {
                hql.append(" limit :smail , :big");
                paramList.put("big", page.getPageSize());
                paramList.put("smail", (page.getPageNumber() - 1) * page.getPageSize());
            }
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            //query.setFirstResult(0).setMaxResults(20);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<Object[]> getAlarmDurationStattMainCommon(String[] unitCodes, String[] workshopCodes, Long[]
                                                                  prdtCellIds, String[] unitIds, Long[] alarmFlagIds, Integer[] priority, Date startTime, Date endTime,
                                                          Boolean
                                                                  priorityFlag, Pagination page,
                                                          Integer isElimination) {
        try {
            Map<String, Object> paramList = new HashMap<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            StringBuilder hql = new StringBuilder();
            hql.append(
                    "select ar.unit_code, max(ar.prdtcell_id),pc.sname as pcSname, ar.tag, ar.des, ar.alarm_flag_id,af.name as afName," +
                            "sum(round(" + DbConversion.toNumber() + "((" + DbConversion.dateFieldSub(
                            "ar.recovery_time ",
                            "ar.alarm_time") + " ) * 24 * 60),2)) as continuousHour,ar.priority, ap.monitor_type, ap.location\n");


            hql.append("  from t_ad_AlarmRec ar\n" +
                    " inner join t_pm_alarmpoint ap on ar.alarm_point_id = ap.alarm_point_id\n" +
                    " inner join t_ad_alarmflag af on ar.alarm_flag_id= af.alarm_flag_id\n" +
                    " inner join t_pm_prdtcell pc on ar.prdtcell_id = pc.prdtcell_id \n" +
                    " inner join t_pm_unit u on ar.unit_code =u.std_code \n" +
                    " inner join t_pm_workshop w on w.workshop_id  = u.workshop_id  \n" +
                    " where ap.in_use = 1 and ar.company_id=:companyId ");


            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append(" and ar.unit_code in (:unitCodes)");
                paramList.put("unitCodes", Arrays.asList(unitCodes));
            }
            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append(" and ar.unit_code in (:unitIds)");
                paramList.put("unitIds", Arrays.asList(unitIds));
            }
            if (ArrayUtils.isNotEmpty(workshopCodes)) {
                hql.append(" and w.std_code = (:workshopCode)");
                paramList.put("workshopCode", workshopCodes[0]);
            }
            // 过滤生产单元
            if (prdtCellIds != null && prdtCellIds.length > 0) {
                for (int i = 0; i < prdtCellIds.length; i++) {
                    if (prdtCellIds[i] == (-9L)) {
                        hql.append(" and ( ar.prdtcell_id in (:prdtCellIds) or ar.prdtcell_id is null) ");
                        break;
                    }
                    if (i == prdtCellIds.length - 1) {
                        hql.append(" and  ar.prdtcell_id in (:prdtCellIds) ");
                    }
                }
                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
            }

            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ar.alarm_flag_id in (:alarmFlagIds) or ar.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ar.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            //过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ar.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ar.priority in (:priority) or ar.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ar.priority is null ");
            }

            //过滤时间
            if (null != startTime && null != endTime) {
                hql.append(" and ar.alarm_time <= :endTime and ar.recovery_time >=:startTime ");
                paramList.put("endTime", endTime);
                paramList.put("startTime", startTime);
            }
            hql.append("  group by ar.tag, ar.alarm_flag_id, ar.priority, ap.monitor_type ");
            //根据“报警时间+位号+报警标识”倒序排列
            hql.append(" order by continuousHour desc,ar.tag desc, ar.alarm_flag_id desc\n");
            if ("oracle".equals(dbConfig.getDataBase())) {
                hql.append(") tt\n" +
                        "         WHERE ROWNUM <= :big) table_alias\n" +
                        " WHERE table_alias.rowno > :smail");
                paramList.put("big", page.getPageNumber() * page.getPageSize());
                paramList.put("smail", (page.getPageNumber() - 1) * page.getPageSize());
            } else {
                hql.append(" limit :smail , :big");
                paramList.put("big", page.getPageSize());
                paramList.put("smail", (page.getPageNumber() - 1) * page.getPageSize());
            }
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            //query.setFirstResult(0).setMaxResults(20);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<Object[]> getAlarmDurationStattTotal(String[] unitIds, Long[] alarmFlagIds, Integer[] priority, Date
            startTime, Date endTime, Boolean priorityFlag, Integer isElimination) {
        try {
            Map<String, Object> paramList = new HashMap<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 查询字符串
            StringBuilder hql = new StringBuilder(" select ar.unit_code,u.sname,\n");
            hql.append(
                    "sum(round(" + DbConversion.toNumber() + "(case when ar.priority = 1 then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                         then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                       else ar.recovery_time end ) ",
                            "(case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                           then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                           else ar.alarm_time end)") + " ) * 24 * 60 else 0 end),2)) as emergency, \n" +
                            "sum(round(" + DbConversion.toNumber() + "(case when ar.priority = 2 then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                             then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                               else ar.recovery_time end ) ",
                            "(case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                          then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                           else ar.alarm_time end)") + " ) * 24 * 60 else 0 end),2)) as important, \n" +
                            "sum(round(" + DbConversion.toNumber() + "(case when ar.priority = 3 then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                               else ar.recovery_time end ) ",
                            " (case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                               else ar.alarm_time end)") + "  ) * 24 * 60 else 0 end),2)) as general, \n" +
                            "sum(round(" + DbConversion.toNumber() + "(case when ar.priority is null then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.recovery_time end ) ",
                            " (case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.alarm_time end) ") + " ) * 24 * 60 else 0 end),2)) as nullAlarmQuantity,\n" +
                            "sum(round(" + DbConversion.toNumber() + "((" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "')" +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.recovery_time end ) ",
                            "(case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.alarm_time end) ") + ") * 24 * 60),2)) as total \n");

            hql.append(" from t_ad_AlarmRec ar \n" +
                    " inner join t_pm_alarmpoint ap on ar.alarm_point_id = ap.alarm_point_id \n" +
//                    " inner join t_ad_alarmflag af on ar.alarm_flag_id =af.alarm_flag_id \n" +
//                    " inner join t_pm_eventtype et on ar.event_type_id =et.event_type_id \n" +
//                    " inner join t_pm_prdtcell pc on ar.prdtcell_id = pc.prdtcell_id \n" +
                    " left join t_pm_unit u on ar.unit_code =u.std_code \n" +
                    " where ap.in_use = 1 and ar.company_id=:companyId ");

            //开始过滤需要剔除的数据
//            if (isElimination == 0){
//                List<AlarmPointDelConfig> alarmPointDelConfigs = alarmPointDelConfigRepository.selectAlarmPointDelConfigDetail(unitIds, startTime, endTime);
//                //查询剔除配置信息
//                for (AlarmPointDelConfig a : alarmPointDelConfigs) {
//                    hql.append(" and ( ");
//                    if (a.getAlarmPointId()!=""){
//                        hql.append("ap.alarm_point_id not in (").append((a.getAlarmPointId())).append(") ");
//                        hql.append(" or ");
//                    }
//                    //过滤报警标识
//                    if (!a.getAlarmFlagId().equals("")){
//                        hql.append("af.alarm_flag_id not in (").append((a.getAlarmFlagId())).append(") ");
//                        hql.append(" or ");
//                    }
//                    //过滤时间
//                    hql.append("ar.alarm_time not between '").append(sdf.format(a.getDelStartTime())).append("' and '").append(sdf.format(a.getDelEndTime())).append("' ");
//                    hql.append(" ) ");
//                }
//
//            }

            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append(" and ar.unit_code in (:unitCode)");
                paramList.put("unitCode", Arrays.asList(unitIds));
            }

            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ar.alarm_flag_id in (:alarmFlagIds) or ar.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ar.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            //过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ar.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ar.priority in (:priority) or ar.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ar.priority is null ");
            }

            //过滤时间
            if (null != startTime && null != endTime) {
                hql.append(" and ar.alarm_time <= :endTime and ar.recovery_time >=:startTime ");
                paramList.put("endTime", endTime);
                paramList.put("startTime", startTime);
            }
            //根据“报警时间+位号+报警标识”倒序排列
            hql.append(" group by ar.unit_code\n" +
                    " order by total desc");
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            //query.setFirstResult(0).setMaxResults(20);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<Object[]> getAlarmDurationStattTotalWorkshop(String[] unitIds, Long[] alarmFlagIds, Integer[]
            priority, Date startTime, Date endTime, Boolean priorityFlag, Integer isElimination) {
        try {
            Map<String, Object> paramList = new HashMap<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 查询字符串
            StringBuilder hql = new StringBuilder(" select w.std_code,w.sname,\n");
            hql.append(
                    "sum(round(" + DbConversion.toNumber() + "(case when ar.priority = 1 then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                         then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                       else ar.recovery_time end ) ",
                            "(case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                           then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                           else ar.alarm_time end)") + " ) * 24 * 60 else 0 end),2)) as emergency, \n" +
                            "sum(round(" + DbConversion.toNumber() + "(case when ar.priority = 2 then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                             then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                               else ar.recovery_time end ) ",
                            "(case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                          then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                           else ar.alarm_time end)") + " ) * 24 * 60 else 0 end),2)) as important, \n" +
                            "sum(round(" + DbConversion.toNumber() + "(case when ar.priority = 3 then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                               else ar.recovery_time end ) ",
                            " (case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                               else ar.alarm_time end)") + "  ) * 24 * 60 else 0 end),2)) as general, \n" +
                            "sum(round(" + DbConversion.toNumber() + "(case when ar.priority is null then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.recovery_time end ) ",
                            " (case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.alarm_time end) ") + " ) * 24 * 60 else 0 end),2)) as nullAlarmQuantity,\n" +
                            "sum(round(" + DbConversion.toNumber() + "((" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "')" +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.recovery_time end ) ",
                            "(case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.alarm_time end) ") + ") * 24 * 60),2)) as total \n");

            hql.append(" from t_ad_AlarmRec ar \n" +
                    " inner join t_pm_alarmpoint ap on ar.alarm_point_id = ap.alarm_point_id \n" +
                    " inner join t_pm_unit u on ar.unit_code =u.std_code \n" +
                    " inner join t_pm_workshop w on w.workshop_id  = u.workshop_id  \n" +
                    " where ap.in_use = 1 and ar.company_id=:companyId ");

            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append(" and ar.unit_code in (:unitCode)");
                paramList.put("unitCode", Arrays.asList(unitIds));
            }

            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ar.alarm_flag_id in (:alarmFlagIds) or ar.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ar.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            //过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ar.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ar.priority in (:priority) or ar.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ar.priority is null ");
            }

            //过滤时间
            if (null != startTime && null != endTime) {
                hql.append(" and ar.alarm_time <= :endTime and ar.recovery_time >=:startTime ");
                paramList.put("endTime", endTime);
                paramList.put("startTime", startTime);
            }
            //根据“报警时间+位号+报警标识”倒序排列
            hql.append(" group by w.std_code\n" +
                    " order by total desc");
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            //query.setFirstResult(0).setMaxResults(20);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<Object[]> getAlarmDurationStattTotalunit(String[] workshopCodes, String[] unitIds, Long[]
                                                                 alarmFlagIds, Integer[] priority, Date startTime, Date endTime, Boolean priorityFlag,
                                                         Integer isElimination) {
        try {
            Map<String, Object> paramList = new HashMap<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 查询字符串
            StringBuilder hql = new StringBuilder(" select u.std_code,u.sname,\n");
            hql.append(
                    "sum(round(" + DbConversion.toNumber() + "(case when ar.priority = 1 then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                         then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                       else ar.recovery_time end ) ",
                            "(case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                           then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                           else ar.alarm_time end)") + " ) * 24 * 60 else 0 end),2)) as emergency, \n" +
                            "sum(round(" + DbConversion.toNumber() + "(case when ar.priority = 2 then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                             then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                               else ar.recovery_time end ) ",
                            "(case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                          then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                           else ar.alarm_time end)") + " ) * 24 * 60 else 0 end),2)) as important, \n" +
                            "sum(round(" + DbConversion.toNumber() + "(case when ar.priority = 3 then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                               else ar.recovery_time end ) ",
                            " (case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                               else ar.alarm_time end)") + "  ) * 24 * 60 else 0 end),2)) as general, \n" +
                            "sum(round(" + DbConversion.toNumber() + "(case when ar.priority is null then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.recovery_time end ) ",
                            " (case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.alarm_time end) ") + " ) * 24 * 60 else 0 end),2)) as nullAlarmQuantity,\n" +
                            "sum(round(" + DbConversion.toNumber() + "((" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "')" +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.recovery_time end ) ",
                            "(case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.alarm_time end) ") + ") * 24 * 60),2)) as total \n");

            hql.append(" from t_ad_AlarmRec ar \n" +
                    " inner join t_pm_alarmpoint ap on ar.alarm_point_id = ap.alarm_point_id \n" +
                    " inner join t_pm_prdtcell pc on ar.prdtcell_id = pc.prdtcell_id \n" +
                    " inner join t_pm_unit u on ar.unit_code =u.std_code \n" +
                    " inner join t_pm_workshop w on w.workshop_id  = u.workshop_id  \n" +
                    " where ap.in_use = 1 and ar.company_id=:companyId ");

            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append(" and ar.unit_code in (:unitCode)");
                paramList.put("unitCode", Arrays.asList(unitIds));
            }

            if (ArrayUtils.isNotEmpty(workshopCodes)) {
                hql.append(" and w.std_code in (:workshopCodes)");
                paramList.put("workshopCodes", Arrays.asList(workshopCodes));
            }

            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ar.alarm_flag_id in (:alarmFlagIds) or ar.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ar.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            //过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ar.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ar.priority in (:priority) or ar.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ar.priority is null ");
            }

            //过滤时间
            if (null != startTime && null != endTime) {
                hql.append(" and ar.alarm_time <= :endTime and ar.recovery_time >=:startTime ");
                paramList.put("endTime", endTime);
                paramList.put("startTime", startTime);
            }
            //根据“报警时间+位号+报警标识”倒序排列
            hql.append(" group by ar.unit_code\n" +
                    " order by total desc");
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            //query.setFirstResult(0).setMaxResults(20);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<Object[]> getAlarmDurationStattTotalPrdtcell(String[] unitCodes, String[] unitIds, Long[]
                                                                     alarmFlagIds, Integer[] priority, Date startTime, Date endTime, Boolean priorityFlag,
                                                             Integer isElimination) {
        try {
            Map<String, Object> paramList = new HashMap<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 查询字符串
            StringBuilder hql = new StringBuilder(" select pc.prdtcell_id,pc.sname,\n");
            hql.append(
                    "sum(round(" + DbConversion.toNumber() + "(case when ar.priority = 1 then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                         then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                       else ar.recovery_time end ) ",
                            "(case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                           then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                           else ar.alarm_time end)") + " ) * 24 * 60 else 0 end),2)) as emergency, \n" +
                            "sum(round(" + DbConversion.toNumber() + "(case when ar.priority = 2 then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                             then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                               else ar.recovery_time end ) ",
                            "(case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                          then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                           else ar.alarm_time end)") + " ) * 24 * 60 else 0 end),2)) as important, \n" +
                            "sum(round(" + DbConversion.toNumber() + "(case when ar.priority = 3 then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                               else ar.recovery_time end ) ",
                            " (case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "')  " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                               else ar.alarm_time end)") + "  ) * 24 * 60 else 0 end),2)) as general, \n" +
                            "sum(round(" + DbConversion.toNumber() + "(case when ar.priority is null then (" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.recovery_time end ) ",
                            " (case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.alarm_time end) ") + " ) * 24 * 60 else 0 end),2)) as nullAlarmQuantity,\n" +
                            "sum(round(" + DbConversion.toNumber() + "((" + DbConversion.dateFieldSub(
                            "(case when ar.recovery_time > " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "')" +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.recovery_time end ) ",
                            "(case when ar.alarm_time<" + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                              then " + DbConversion.dateFunction() + "('" + sdf.format(
                                    startTime) + "','" + DbConversion.dateYmdhmsFunction() + "') " +
                                    "                                else ar.alarm_time end) ") + ") * 24 * 60),2)) as total \n");

            hql.append(" from t_ad_AlarmRec ar \n" +
                    " inner join t_pm_alarmpoint ap on ar.alarm_point_id = ap.alarm_point_id \n" +
                    " inner join t_pm_prdtcell pc on ar.prdtcell_id = pc.prdtcell_id \n" +
                    " inner join t_pm_unit u on ar.unit_code =u.std_code \n" +
                    " inner join t_pm_workshop w on w.workshop_id  = u.workshop_id  \n" +
                    " where ap.in_use = 1 and ar.company_id=:companyId ");

            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append(" and ar.unit_code in (:unitCode)");
                paramList.put("unitCode", Arrays.asList(unitIds));
            }

            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append(" and ar.unit_code in (:unitCodes)");
                paramList.put("unitCodes", Arrays.asList(unitCodes));
            }

            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ar.alarm_flag_id in (:alarmFlagIds) or ar.alarm_flag_id is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ar.alarm_flag_id in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            //过滤优先级
            if (priority != null && !priorityFlag) {
                hql.append(" and ar.priority in (:priority) ");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority != null && priorityFlag) {
                hql.append(" and (ar.priority in (:priority) or ar.priority is null)");
                paramList.put("priority", Arrays.asList(priority));
            }
            if (priority == null && priorityFlag) {
                hql.append(" and ar.priority is null ");
            }

            //过滤时间
            if (null != startTime && null != endTime) {
                hql.append(" and ar.alarm_time <= :endTime and ar.recovery_time >=:startTime ");
                paramList.put("endTime", endTime);
                paramList.put("startTime", startTime);
            }
            //根据“报警时间+位号+报警标识”倒序排列
            hql.append(" group by pc.prdtcell_id\n" +
                    " order by total desc");
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            //query.setFirstResult(0).setMaxResults(20);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    @Transactional
    public List<Object[]> getAlarmRespond(String[] unitCodes, Long[] prdtCellIds, String tag, Long
                                                  alarmFlagId, Integer priority, Date beginTime, Date endTime, Integer
                                                  isTimeResponse, List<DateRange> dateList, Integer responseDuration,
                                          Pagination page) {
        try {
            Map<String, Object> paramList = new HashMap<>();
            // 查询字符串
            StringBuilder hql = new StringBuilder();
            if ("oracle".equals(dbConfig.getDataBase())) {
                hql.append(" SELECT *\n");
                hql.append("  FROM (SELECT tt.*, ROWNUM AS rowno from (\n");
            }
            hql.append(
                    "select ar.alarm_time,ar.response_time,ar.unit_code,ar.prdtcell_id,ap.tag,ar.des,af.alarm_flag_id,ar.priority,tem.prescribed_response_duration,pc.unit_code as unitCode,pc.sname as pcName,af.name as afName, ap.location as location ");
            hql.append(" from t_ad_alarmrec ar\n" +
                    " inner join t_pm_alarmpoint ap\n" +
                    "    on ar.alarm_point_id = ap.alarm_point_id\n" +
                    " inner join t_pm_prdtcell pc\n" +
                    "    on ap.prdtcell_id = pc.prdtcell_id\n" +
                    " inner join t_ad_alarmflag af\n" +
                    "    on ar.alarm_flag_id = af.alarm_flag_id\n" +
                    " inner join t_pm_tagextramessage tem\n" +
                    "    on ar.alarm_point_id = tem.alarm_point_id\n" +
                    "   and ar.alarm_flag_id = tem.alarm_flag_id\n" +
                    // "  inner join t_pm_unit u on ar.unit_code =u.std_code\n "+
                    " where 1 = 1 and ar.company_id=:companyId and pc.company_id=:companyId and ap.company_id=:companyId  and (ar.priority is not null) and ap.in_use = 1 ");
            //装置
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql.append(" and pc.unit_code in (:unitCode) ");
                paramList.put("unitCode", Arrays.asList(unitCodes));
            }
            //生产单元
            if (ArrayUtils.isNotEmpty(prdtCellIds) && unitCodes.length == 1) {
                hql.append(" and pc.prdtcell_id in (:prdtCellIds) ");
                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
            }
            //位号
            if (!StringUtils.isEmpty(tag)) {
                hql.append(" and upper(ap.tag) like upper(:tag) escape '/' ");
                paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
            }
            //报警标识
            if (!StringUtils.isEmpty(alarmFlagId) && alarmFlagId != -1) {
                hql.append("and af.alarm_flag_id=:alarmFlagId ");
                paramList.put("alarmFlagId", alarmFlagId);
            }
            //优先级
            if (!StringUtils.isEmpty(priority) && priority != -1) {
                hql.append("and ar.priority=:priority ");
                paramList.put("priority", priority);
            }

            hql.append("and ar.alarm_time between :beginTime and :endTime ");
            paramList.put("beginTime", beginTime);
            paramList.put("endTime", endTime);
            // 是否及时响应
            if (!StringUtils.isEmpty(isTimeResponse) && isTimeResponse != -1) {
                if (isTimeResponse == 1) {
//                    hql.append("and ar.response_time is not null and (ar.response_time-ar.alarm_time)*24*60*60<= tem.prescribed_response_duration and ar.response_time<=:filterTime ");
                    hql.append(
                            "and ar.response_time is not null and (" + DbConversion.dateFieldSub("ar.response_time",
                                    "ar.alarm_time") + ")*24*60*60<= tem.prescribed_response_duration and ar.response_time<=:filterTime ");
                    paramList.put("filterTime", endTime);
                } else if (isTimeResponse == 2) {
//                    hql.append("and (ar.response_time is null or (ar.response_time-ar.alarm_time)*24*60*60>tem.prescribed_response_duration or ar.response_time>:timeOut) ");
                    hql.append("and (ar.response_time is null or (" + DbConversion.dateFieldSub("ar.response_time",
                            "ar.alarm_time") + ")*24*60*60>tem.prescribed_response_duration or ar.response_time>:timeOut) ");
                    paramList.put("timeOut", endTime);
                }
            }

            if (dateList != null && dateList.size() > 0) {
                this.getEntityManager().clear();
                this.saveDateRangeList(dateList);
                hql.append(
                        "and exists(select dr.* from  temp_pm_daterange dr where ar.alarm_time >=dr.start_time and ar.alarm_time<dr.end_time) ");
            }

            //过滤报警标识
            hql.append(" and exists (select afc.alarmflagcomp_id\n" +
                    "                  from t_ad_alarmflagcomp afc\n" +
                    "                 where afc.alarm_flag_id = af.alarm_flag_id\n" +
                    "                   and afc.in_use = 1) ");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 查询条件响应时长
            if (!StringUtils.isEmpty(responseDuration) && responseDuration != -1) {
                if (responseDuration.intValue() == 1) {
//                    hql.append("and (ar.response_time-ar.alarm_time)*24*60<=0.5 ");
                    hql.append("and (" + DbConversion.dateFieldSub("ar.response_time",
                            "ar.alarm_time") + ")*24*60<=0.5 ");
                } else if (responseDuration.intValue() == 2) {
//                    hql.append("and (ar.response_time-ar.alarm_time)*24*60>0.5 and (ar.response_time-ar.alarm_time)*24*60<=2 ");
                    hql.append("and (" + DbConversion.dateFieldSub("ar.response_time",
                            "ar.alarm_time") + ")*24*60>0.5 and (" + DbConversion.dateFieldSub("ar.response_time",
                            "ar.alarm_time") + ")*24*60<=2 ");
                } else if (responseDuration.intValue() == 3) {
//                    hql.append("and (ar.response_time-ar.alarm_time)*24*60>2 and (ar.response_time-ar.alarm_time)*24*60<=5 ");
                    hql.append("and (" + DbConversion.dateFieldSub("ar.response_time",
                            "ar.alarm_time") + ")*24*60>2 and (" + DbConversion.dateFieldSub("ar.response_time",
                            "ar.alarm_time") + ")*24*60<=5 ");
                } else if (responseDuration.intValue() == 4) {
//                    hql.append("and (ar.response_time-ar.alarm_time)*24*60>5 ");
                    hql.append("and (" + DbConversion.dateFieldSub("ar.response_time",
                            "ar.alarm_time") + ")*24*60>5 ");
                } else if (responseDuration.intValue() == 5) {
                    hql.append("and ar.response_time is null ");
                } else if (responseDuration.intValue() == 6) {
//                    hql.append("and (ar.response_time-ar.alarm_time)*24*60>0.75 and (ar.response_time-ar.alarm_time)*24*60<=2 ");
                    hql.append("and (" + DbConversion.dateFieldSub("ar.response_time",
                            "ar.alarm_time") + ")*24*60>0.75 and (" + DbConversion.dateFieldSub("ar.response_time",
                            "ar.alarm_time") + ")*24*60<=2 ");
                } else if (responseDuration.intValue() == 7) {
                    hql.append("and ar.response_time is not null ");
                }
            }
            // 响应时间不为空
//            hql.append("order by (case when ar.response_time>"+DbConversion.dateFunction()+"('"+sdf.format(endTime)+"','"+DbConversion.dateYmdhmsFunction()+"') then null else (ar.response_time-ar.alarm_time) end) desc,ar.alarm_time desc,ap.tag,af.name ");
            hql.append("order by ar.alarm_time desc,ap.tag,af.name ");
            if ("oracle".equals(dbConfig.getDataBase())) {
                hql.append(" ) tt" +
                        " WHERE ROWNUM <= :big) table_alias\n" +
                        " WHERE table_alias.rowno >=:smail ");

                paramList.put("big", page.getPageNumber() * page.getPageSize());
                paramList.put("smail", (page.getPageNumber() - 1) * page.getPageSize());
            } else {
                hql.append(" limit :smail , :big");

                paramList.put("big", page.getPageSize());
                paramList.put("smail", (page.getPageNumber() - 1) * page.getPageSize());
            }
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            //query.setFirstResult(0).setMaxResults(20);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }


    @Override
    @Transactional
    public Object getAlarmRespondTotal(String[] unitCodes, Long[] prdtCellIds, String tag, Long alarmFlagId, Integer
            priority, Date beginTime, Date endTime, Integer isTimeResponse, List<DateRange> dateList, Integer
                                               responseDuration) {
        Map<String, Object> paramList = new HashMap<>();
        // 查询字符串
        StringBuilder hql = new StringBuilder(" SELECT count(*) from (\n" +
                "select ar.alarm_time,ar.response_time,ar.unit_code,ar.prdtcell_id,ap.tag,ar.des,af.alarm_flag_id,tem.prescribed_response_duration ");
        hql.append(" from t_ad_alarmrec ar\n" +
                " inner join t_pm_alarmpoint ap\n" +
                "    on ar.alarm_point_id = ap.alarm_point_id\n" +
                " inner join t_pm_prdtcell pc\n" +
                "    on ap.prdtcell_id = pc.prdtcell_id\n" +
                " inner join t_ad_alarmflag af\n" +
                "    on ar.alarm_flag_id = af.alarm_flag_id\n" +
                " inner join t_pm_tagextramessage tem\n" +
                "    on ar.alarm_point_id = tem.alarm_point_id\n" +
                "   and ar.alarm_flag_id = tem.alarm_flag_id\n" +
                " where 1 = 1 and ar.company_id=:companyId and ap.company_id=:companyId and pc.company_id=:companyId and (ar.priority is not null) and ap.in_use = 1 ");
        //装置
        if (ArrayUtils.isNotEmpty(unitCodes)) {
            hql.append(" and pc.unit_code in (:unitCode) ");
            paramList.put("unitCode", Arrays.asList(unitCodes));
        }
        //生产单元
        if (ArrayUtils.isNotEmpty(prdtCellIds) && unitCodes.length == 1) {
            hql.append(" and pc.prdtcell_id in (:prdtCellIds) ");
            paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
        }
        //位号
        if (!StringUtils.isEmpty(tag)) {
            hql.append(" and upper(ap.tag) like upper(:tag) escape '/' ");
            paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
        }
        //报警标识
        if (!StringUtils.isEmpty(alarmFlagId) && alarmFlagId != -1) {
            hql.append("and af.alarm_flag_id=:alarmFlagId ");
            paramList.put("alarmFlagId", alarmFlagId);
        }
        //优先级
        if (!StringUtils.isEmpty(priority) && priority != -1) {
            hql.append("and ar.priority=:priority ");
            paramList.put("priority", priority);
        }

        hql.append("and ar.alarm_time between :beginTime and :endTime ");
        paramList.put("beginTime", beginTime);
        paramList.put("endTime", endTime);
        // 是否及时响应
        if (!StringUtils.isEmpty(isTimeResponse) && isTimeResponse != -1) {
            if (isTimeResponse == 1) {
//                hql.append("and ar.response_time is not null and (ar.response_time-ar.alarm_time)*24*60*60<= tem.prescribed_response_duration and ar.response_time<=:filterTime ");
                hql.append("and ar.response_time is not null and (" + DbConversion.dateFieldSub("ar.response_time",
                        "ar.alarm_time") + ")*24*60*60<= tem.prescribed_response_duration and ar.response_time<=:filterTime ");
                paramList.put("filterTime", endTime);
            } else if (isTimeResponse == 2) {
//                hql.append("and (ar.response_time is null or (ar.response_time-ar.alarm_time)*24*60*60>tem.prescribed_response_duration or ar.response_time>:timeOut) ");
                hql.append("and (ar.response_time is null or (" + DbConversion.dateFieldSub("ar.response_time",
                        "ar.alarm_time") + ")*24*60*60>tem.prescribed_response_duration or ar.response_time>:timeOut) ");
                paramList.put("timeOut", endTime);
            }
        }

        if (dateList != null && dateList.size() > 0) {
            this.getEntityManager().clear();
            this.saveDateRangeList(dateList);
            hql.append(
                    "and exists(select dr.* from  temp_pm_daterange dr where ar.alarm_time >=dr.start_time and ar.alarm_time<dr.end_time) ");
        }

        //过滤报警标识
        hql.append(" and exists (select afc.alarmflagcomp_id\n" +
                "                  from t_ad_alarmflagcomp afc\n" +
                "                 where afc.alarm_flag_id = af.alarm_flag_id\n" +
                "                   and afc.in_use = 1) ");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 查询条件响应时长
        if (!StringUtils.isEmpty(responseDuration) && responseDuration != -1) {
            if (responseDuration.intValue() == 1) {
//                hql.append("and (ar.response_time-ar.alarm_time)*24*60<=0.5 ");
                hql.append(
                        "and (" + DbConversion.dateFieldSub("ar.response_time", "ar.alarm_time") + ")*24*60<=0.5 ");
            } else if (responseDuration.intValue() == 2) {
//                hql.append("and (ar.response_time-ar.alarm_time)*24*60>0.5 and (ar.response_time-ar.alarm_time)*24*60<=2 ");
                hql.append("and (" + DbConversion.dateFieldSub("ar.response_time",
                        "ar.alarm_time") + ")*24*60>0.5 and (" + DbConversion.dateFieldSub("ar.response_time",
                        "ar.alarm_time") + ")*24*60<=2 ");
            } else if (responseDuration.intValue() == 3) {
//                hql.append("and (ar.response_time-ar.alarm_time)*24*60>2 and (ar.response_time-ar.alarm_time)*24*60<=5 ");
                hql.append("and (" + DbConversion.dateFieldSub("ar.response_time",
                        "ar.alarm_time") + ")*24*60>2 and (" + DbConversion.dateFieldSub("ar.response_time",
                        "ar.alarm_time") + ")*24*60<=5 ");
            } else if (responseDuration.intValue() == 4) {
//                hql.append("and (ar.response_time-ar.alarm_time)*24*60>5 ");
                hql.append("and (" + DbConversion.dateFieldSub("ar.response_time", "ar.alarm_time") + ")*24*60>5 ");
            } else if (responseDuration.intValue() == 5) {
                hql.append("and ar.response_time is null ");
            } else if (responseDuration.intValue() == 6) {
//                hql.append("and (ar.response_time-ar.alarm_time)*24*60>0.75 and (ar.response_time-ar.alarm_time)*24*60<=2 ");
                hql.append("and (" + DbConversion.dateFieldSub("ar.response_time",
                        "ar.alarm_time") + ")*24*60>0.75 and (" + DbConversion.dateFieldSub("ar.response_time",
                        "ar.alarm_time") + ")*24*60<=2 ");
            } else if (responseDuration.intValue() == 7) {
                hql.append("and ar.response_time is not null ");
            }
        }
        // 响应时间不为空
        hql.append("order by (case when ar.response_time>" + DbConversion.dateFunction() + "('" + sdf.format(
                endTime) + "','" + DbConversion.dateYmdhmsFunction() + "') then null else (ar.response_time-ar.alarm_time) end) desc,ar.alarm_time desc,ap.tag,af.name ");
        hql.append(" ) tt");
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        return query.getSingleResult();
    }

    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public CommonResult deleteAlarmRecByAlarmPointIds(Long[] anlyAlarmPointIdList) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            String hql = " from AlarmRec ar where ar.companyId=:companyId and ar.alarmPointId in (:alarmPointIds)";
            Map<String, Object> paramList = new HashMap<String, Object>();
            List<Long> alarmPointIdList = Arrays.asList(anlyAlarmPointIdList);
            paramList.put("alarmPointIds", alarmPointIdList);
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            TypedQuery<AlarmRec> query = getEntityManager().createQuery(hql, AlarmRec.class);
            this.setParameterList(query, paramList);
            List<AlarmRec> alarmRecList = query.getResultList();
            alarmRecList.forEach(x -> {
                this.getEntityManager().remove(x);
            });

            commonResult.setIsSuccess(true);
            commonResult.setMessage("删除成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    @Override
    public List<Object[]> getAlarmRespondOfUnRespond(String[] unitIds, Date startTime, Date endTime, Date now) {
        try {
            Map<String, Object> paramList = new HashMap<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 查询字符串
            StringBuilder hql = new StringBuilder(
                    " select min(ar.unit_code) unit,ap.tag tag,(" + DbConversion.dateFunction() + "('" + sdf.format(
                            now) + "','" + DbConversion.dateYmdhmsFunction() + "') - max(ar.alarm_time)) * 24 * 60 * 60 as duration ");
            hql.append(" from t_ad_alarmrec ar\n" +
                    " inner join t_pm_alarmpoint ap\n" +
                    "    on ar.alarm_point_id = ap.alarm_point_id\n" +
                    " inner join t_pm_prdtcell pc\n" +
                    "    on ap.prdtcell_id = pc.prdtcell_id\n" +
                    " inner join t_ad_alarmflag af\n" +
                    "    on ar.alarm_flag_id = af.alarm_flag_id\n" +
                    " inner join t_pm_tagextramessage tem\n" +
                    "    on ar.alarm_point_id = tem.alarm_point_id\n" +
                    "   and ar.alarm_flag_id = tem.alarm_flag_id\n" +
                    // "  inner join t_pm_unit u on ar.unit_code =u.std_code\n "+
                    " where 1 = 1 and ar.company_id=:companyId and ap.company_id=:companyId  and pc.company_id=:companyId and (ar.priority is not null) and ap.in_use = 1 ");
            //装置
            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append(" and pc.unit_code in (:unitCode) ");
                paramList.put("unitCode", Arrays.asList(unitIds));
            }

            hql.append("and ar.alarm_time between :beginTime and :endTime ");
            paramList.put("beginTime", startTime);
            paramList.put("endTime", endTime);

            //过滤报警标识
            hql.append(" and exists (select afc.alarmflagcomp_id\n" +
                    "                  from t_ad_alarmflagcomp afc\n" +
                    "                 where afc.alarm_flag_id = af.alarm_flag_id\n" +
                    "                   and afc.in_use = 1) ");

            hql.append("and ar.response_time is null ");
//            hql.append(" and (to_date('"+sdf.format(now)+"','yyyy-mm-dd hh24:mi:ss')-ar.alarm_time)*24 > 24 ");
            hql.append(" and (" + DbConversion.dateFunction() + "('" + sdf.format(
                    now) + "','" + DbConversion.dateYmdhmsFunction() + "')-ar.alarm_time)*24 > 24 ");
            //paramList.put("now", now);
            hql.append("group by ap.tag ");
            hql.append("order by max(ar.alarm_time) desc,ap.tag ");
//企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());
            Query query = getEntityManager().createNativeQuery(hql.toString());
            this.setParameterList(query, paramList);
            //query.setFirstResult(0).setMaxResults(20);
            List resultList = query.getResultList();
            return resultList;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public Object getTotalAlarmDurationStatt(String[] unitIds, Long[] alarmFlagIds, Integer[] priority, Date
            startTime, Date endTime, Boolean priorityFlag) {
        Map<String, Object> paramList = new HashMap<>();
        StringBuilder hql = new StringBuilder(
                "select count(*) from ( select ar.unit_code, max(ar.prdtcell_id),pc.sname as pcSname, ar.tag, ar.des, ar.alarm_flag_id,af.name as afName,sum(ar.recovery_time-ar.alarm_time) as continuousHour,ar.priority\n" +
                        "  from t_ad_AlarmRec ar\n" +
                        " left join t_pm_alarmpoint ap on ar.alarm_point_id = ap.alarm_point_id\n" +
                        " left join t_ad_alarmflag af on ar.alarm_flag_id= af.alarm_flag_id\n" +
//                " inner join t_pm_eventtype et on ar.event_type_id = et.event_type_id\n" +
                        " left join t_pm_prdtcell pc on ar.prdtcell_id =pc.prdtcell_id\n" +
                        " where ap.in_use = 1 and ar.company_id=:companyId ");

        if (ArrayUtils.isNotEmpty(unitIds)) {
            hql.append(" and ar.unit_code in (:unitCode)");
            paramList.put("unitCode", Arrays.asList(unitIds));
        }
        if (ArrayUtils.isNotEmpty(alarmFlagIds)) {
            hql.append(" and ar.alarm_flag_id in (:alarmFlagId) ");
            paramList.put("alarmFlagId", Arrays.asList(alarmFlagIds));
        }
        //过滤优先级
        if (priority != null && !priorityFlag) {
            hql.append(" and ar.priority in (:priority) ");
            paramList.put("priority", Arrays.asList(priority));
        }
        if (priority != null && priorityFlag) {
            hql.append(" and (ar.priority in (:priority) or ar.priority is null)");
            paramList.put("priority", Arrays.asList(priority));
        }
        if (priority == null && priorityFlag) {
            hql.append(" and ar.priority is null ");
        }

        //过滤时间
        if (null != startTime && null != endTime) {
            hql.append(" and ar.alarm_time <= :endTime and ar.recovery_time >=:startTime ");
            paramList.put("endTime", endTime);
            paramList.put("startTime", startTime);
        }
        hql.append("  group by ar.tag, ar.alarm_flag_id, ar.priority ");
        //根据“报警时间+位号+报警标识”倒序排列
        hql.append(" ) ");
        if ("mysql".equals(dbConfig.getDataBase())) {
            //mysql 表起别名
            hql.append(" as t");
        }
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        Object o = query.getSingleResult();
        return o;
    }


    @Override
    public Object getTotalAlarmDurationStattCommon(String[] unitCodes, String[] workshopCodes, Long[]
                                                           prdtCellIds, String[] unitIds, Long[] alarmFlagIds, Integer[] priority, Date startTime, Date endTime,
                                                   Boolean
                                                           priorityFlag) {
        Map<String, Object> paramList = new HashMap<>();
        StringBuilder hql = new StringBuilder(
                "select count(*) from ( select ar.unit_code, max(ar.prdtcell_id),pc.sname as pcSname, ar.tag, ar.des, ar.alarm_flag_id,af.name as afName,sum(ar.recovery_time-ar.alarm_time) as continuousHour,ar.priority\n" +
                        "  from t_ad_AlarmRec ar\n" +
                        " left join t_pm_alarmpoint ap on ar.alarm_point_id = ap.alarm_point_id\n" +
                        " left join t_ad_alarmflag af on ar.alarm_flag_id= af.alarm_flag_id\n" +
//                " inner join t_pm_eventtype et on ar.event_type_id = et.event_type_id\n" +
                        " left join t_pm_prdtcell pc on ar.prdtcell_id =pc.prdtcell_id\n" +
                        " left join t_pm_unit u on ar.unit_code =u.std_code \n" +
                        " left join t_pm_workshop w on w.workshop_id  = u.workshop_id  \n" +
                        " where ap.in_use = 1 and ar.company_id=:companyId ");

        if (ArrayUtils.isNotEmpty(unitCodes)) {
            hql.append(" and ar.unit_code in (:unitCodes)");
            paramList.put("unitCodes", Arrays.asList(unitCodes));
        }
        if (ArrayUtils.isNotEmpty(unitIds)) {
            hql.append(" and ar.unit_code in (:unitCode)");
            paramList.put("unitCode", Arrays.asList(unitIds));
        }
        if (ArrayUtils.isNotEmpty(workshopCodes)) {
            hql.append(" and w.std_code = (:workshopCode)");
            paramList.put("workshopCode", workshopCodes[0]);
        }
        // 过滤生产单元
        if (prdtCellIds != null && prdtCellIds.length > 0) {
            for (int i = 0; i < prdtCellIds.length; i++) {
                if (prdtCellIds[i] == (-9L)) {
                    hql.append(" and ( ar.prdtcell_id in (:prdtCellIds) or ar.prdtcell_id is null) ");
                    break;
                }
                if (i == prdtCellIds.length - 1) {
                    hql.append(" and  ar.prdtcell_id in (:prdtCellIds) ");
                }
            }
            paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
        }
        if (ArrayUtils.isNotEmpty(alarmFlagIds)) {
            hql.append(" and ar.alarm_flag_id in (:alarmFlagId) ");
            paramList.put("alarmFlagId", Arrays.asList(alarmFlagIds));
        }
        //过滤优先级
        if (priority != null && !priorityFlag) {
            hql.append(" and ar.priority in (:priority) ");
            paramList.put("priority", Arrays.asList(priority));
        }
        if (priority != null && priorityFlag) {
            hql.append(" and (ar.priority in (:priority) or ar.priority is null)");
            paramList.put("priority", Arrays.asList(priority));
        }
        if (priority == null && priorityFlag) {
            hql.append(" and ar.priority is null ");
        }

        //过滤时间
        if (null != startTime && null != endTime) {
            hql.append(" and ar.alarm_time <= :endTime and ar.recovery_time >=:startTime ");
            paramList.put("endTime", endTime);
            paramList.put("startTime", startTime);
        }
        hql.append("  group by ar.tag, ar.alarm_flag_id, ar.priority ");
        //根据“报警时间+位号+报警标识”倒序排列
        hql.append(" ) ");
        if ("mysql".equals(dbConfig.getDataBase())) {
            //mysql 表起别名
            hql.append(" as t");
        }
        //企业
        CommonProperty commonProperty = new CommonProperty();
        paramList.put("companyId", commonProperty.getCompanyId());
        Query query = getEntityManager().createNativeQuery(hql.toString());
        this.setParameterList(query, paramList);
        Object o = query.getSingleResult();
        return o;
    }

    @Transactional
    @Override
    public CommonResult removeRecoveryTimeIsNullByAlarmTime(String startDate, String endDate, Integer companyId) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            //        String sql = "delete from T_AD_ALARMREC where RECOVERY_TIME is null and ALARM_TIME >= :startDate AND ALARM_TIME <= :endDate ";
            String hql = "delete from AlarmRec where recoveryTime is null and companyId=:companyId and alarmTime >= str_to_date(:startDate,'%Y-%m-%d %H:%i:%s') AND alarmTime <= str_to_date(:endDate,'%Y-%m-%d %H:%i:%s') ";
            Map<String, Object> param = new HashMap<>();
            param.put("startDate", startDate);
            param.put("endDate", endDate);
            param.put("companyId", companyId);
            Query query = getEntityManager().createQuery(hql);
            this.setParameterList(query, param);

            int i = query.executeUpdate();
            commonResult.setIsSuccess(true);

            if (i > 0) {
                commonResult.setMessage("删除成功，共删除" + i + "条数据");
            } else {
                commonResult.setMessage("没有需要删除的数据");
            }
        } catch (Exception e) {
            e.printStackTrace();
            commonResult.setIsSuccess(false);
            commonResult.setMessage(e.getMessage());
            logger.error("删除恢复时间为空的数据失败时间区间为{}--{},异常信息{}======={}", startDate, endDate,
                    e.getMessage(), e.toString());
        }
        return commonResult;

    }

    @Override
    public int findAlarmRecCountByStartTime(String unitCode, Long prdtcellId, String tag, String alarmFlag, Date
            alarmTime, String companyId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  COUNT(*)  FROM T_AD_ALARMREC T WHERE T.company_id=:companyId " +
                "AND T.UNIT_CODE=:unitCode " +
                "AND T.PRDTCELL_ID=:prdtcellId " +
                "AND T.TAG=:tag " +
                "AND T.alarm_Flag=:alarmFlagType " +
                "AND T.ALARM_TIME=str_to_date(:alarmTime,'%Y-%m-%d %H:%i:%s') ");
        Map<String, Object> param = new HashMap<>();
        param.put("unitCode", unitCode);
        param.put("prdtcellId", prdtcellId);
        param.put("tag", tag);
        param.put("alarmFlagType", alarmFlag);
        String time = sdf.format(alarmTime);
        param.put("alarmTime", time);
        param.put("companyId", companyId);
        Query query = this.getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, param);
        int i = Integer.valueOf(String.valueOf(query.getResultList().get(0)));
        return i;
    }

    @Transactional
    @Modifying(clearAutomatically = true)
    @Override
    public int updateAlarmRecRecoveryTime(String unitCode, Long prdtcellId, String tag, String alarmFlag, Date
            alarmTime, Date recoveryTime, String companyId) {
        int result = 0;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            StringBuilder hql = new StringBuilder();
            hql.append(
                    "UPDATE t_ad_alarmrec  SET response_time = str_to_date(:recoveryTime,'%Y-%m-%d %H:%i:%s')\n" +
                            "   WHERE company_id=:companyId and unit_code=:unitCode " +
                            " AND prdtcell_id=:prdtcellId " +
                            " AND tag=:tag " +
                            " AND alarm_flag=:alarmFlagType " +
                            " AND alarm_time=str_to_date(:alarmTime,'%Y-%m-%d %H:%i:%s') " +
                            " and response_time is null"
            );
            String time = sdf.format(recoveryTime);
            String time2 = sdf.format(alarmTime);

            Query query = this.getEntityManager().createNativeQuery(hql.toString());
            query.setParameter("unitCode", unitCode);
            query.setParameter("prdtcellId", prdtcellId);
            query.setParameter("tag", tag);
            query.setParameter("alarmFlagType", alarmFlag);
            query.setParameter("recoveryTime", time);
            query.setParameter("alarmTime", time2);
            query.setParameter("companyId", Integer.valueOf(companyId));

            result = query.executeUpdate();
//            result = this.getEntityManager().createQuery(hql.toString())
//                    .setParameter("unitCode",unitCode)
//                    .setParameter("prdtcellId",prdtcellId)
//                    .setParameter("tag",tag)
//                    .setParameter("alarmFlagType",alarmFlag)
//                    .setParameter("recoveryTime",time)
//                    .setParameter("alarmTime",time2)
//                    .setParameter("companyId",Integer.valueOf(companyId))
//                    .executeUpdate();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    @Transactional
    @Modifying(clearAutomatically = true)
    @Override
    public int updateAlarmRecResponseTimeByLessAlarmTime(String unitCode, Long prdtcellId, String tag, String
            alarmFlag, Date alarmTime, Date recoveryTime, String companyId) {
        int result = 0;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String hql = "UPDATE t_ad_alarmrec  SET response_time = str_to_date(:recoveryTime,'%Y-%m-%d %H:%i:%s')\n" +
                    "   WHERE company_id=:companyId and unit_code=:unitCode " +
                    " AND prdtcell_id=:prdtcellId " +
                    " AND tag=:tag " +
                    " AND alarm_flag=:alarmFlagType " +
                    " AND alarm_time<=str_to_date(:alarmTime,'%Y-%m-%d %H:%i:%s') " +
                    " and response_time is null";
            String time = sdf.format(recoveryTime);
            String time2 = sdf.format(alarmTime);
            Query query = this.getEntityManager().createNativeQuery(hql.toString())
                    .setParameter("unitCode", unitCode)
                    .setParameter("prdtcellId", prdtcellId)
                    .setParameter("tag", tag)
                    .setParameter("alarmFlagType", alarmFlag)
                    .setParameter("recoveryTime", time)
                    .setParameter("alarmTime", time2)
                    .setParameter("companyId", Integer.valueOf(companyId));

            result = query.executeUpdate();
//                    .executeUpdate();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public List<Object[]> getResponseValue(Date startTime, Date endTime, Integer companyId) {
        Map<String, Object> paramList = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT arc.unit_code AS unitId, un.name AS unitName, " +
                "(COUNT(arc.response_time) * 100.0 / COUNT(*)) AS value " +
                "FROM t_ad_alarmrec arc " +
                "INNER JOIN t_pm_unit un ON arc.unit_code = un.std_code where 1=1");
        //过滤时间
        if (null != startTime && null != endTime) {
            sql.append(" and arc.alarm_time <= :endTime and arc.alarm_time >=:startTime ");
            paramList.put("endTime", endTime);
            paramList.put("startTime", startTime);
        }
        if (null != companyId) {
            sql.append(" and arc.company_id =:companyId ");
            //企业
            paramList.put("companyId", companyId);
        }
        sql.append(" group by arc.unit_code , un.name");
        Query query = this.getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, paramList);
        return query.getResultList();
    }


    @Override
    public Object findAlarmRecMaxAlarmTime(String unitCode, String prdtcellId, String tag, String alarmFlag, Date
            startTime, String companyId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT MAX(ALARM_TIME)  FROM T_AD_ALARMREC T\n" +
                "   WHERE T.company_id=:companyId and T.UNIT_CODE=:unitCode " +
                "     AND T.PRDTCELL_ID=:prdtcellId " +
                "     AND T.TAG=:tag " +
                "     AND T.ALARM_FLAG=:alarmFlag \n" +
                "     AND T.ALARM_TIME <= str_to_date(:startTime,'%Y-%m-%d %H:%i:%s') \n" +
                "     AND T.IS_ACK = 1");
        Map<String, Object> param = new HashMap<>();
        param.put("unitCode", unitCode);
        param.put("prdtcellId", prdtcellId);
        param.put("tag", tag);
        param.put("alarmFlag", alarmFlag);
        String time = sdf.format(startTime);
        param.put("startTime", time);
        param.put("companyId", companyId);
        Query query = this.getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, param);
        return query.getSingleResult();
    }

    @Override
    public int updateResponseTimeByStartTime(String unitCode, String prdtcellId, String tag, String alarmFlag, Date
            startTime, Date recoveryTime, String companyId) {
        int result = 0;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat();
            StringBuilder hql = new StringBuilder();
            hql.append("UPDATE AlarmRec SET responseTime = str_to_date(:startTime,'%Y-%m-%d %H:%i:%s') \n" +
                    "   WHERE companyId=:companyId and responseTime IS NULL\n" +
                    "     AND unitCode=:unitCode " +
                    "   AND prdtcellId=:prdtcellId " +
                    "   AND tag=:tag " +
                    "   AND alarmFlag=:alarmFlag\n" +
                    "   AND recoveryTime <= str_to_date(:recoveryTime,'%Y-%m-%d %H:%i:%s') ");
            String time1 = sdf.format(recoveryTime);
            String time2 = sdf.format(startTime);
            result = this.getEntityManager().createQuery(hql.toString(), AlarmRec.class)
                    .setParameter("unitCode", unitCode)
                    .setParameter("prdtcellId", prdtcellId)
                    .setParameter("tag", tag)
                    .setParameter("alarmFlag", alarmFlag)
                    .setParameter("recoveryTime", time1)
                    .setParameter("startTime", time2)
                    .setParameter("companyId", companyId)
                    .executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public void insertAlarmRecByInfo(List<AlarmRec> list) {
        for (AlarmRec alarmRec : list) {
            this.getEntityManager().persist(alarmRec);
            this.getEntityManager().flush();
            this.getEntityManager().clear();
        }
    }

    @Override
    public int getCountByAlarmRec(AlarmRec alarmRec, String companyId) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(1)\n" +
                "        into V_EXISTS\n" +
                "        FROM alarmrec_test1 T\n" +
                "        WHERE T.UNIT_CODE = :UNIT_CODE\n" +
                "          AND T.PRDTCELL_ID = :PRDTCELL_ID\n" +
                "          AND T.TAG = :TAG\n" +
                "          AND T.ALARM_FLAG = :ALARM_FLAG\n" +
                "          AND T.ALARM_TIME = :ALARM_TIME;");
        Map<String, Object> param = new HashMap<>();
        param.put("UNIT_CODE", alarmRec.getUnitCode());
//        param.put("prdtcellId",prdtcellId);
//        param.put("tag",tag);
//        param.put("alarmFlag",alarmFlag);
//        this.setParameterList();
        return 0;
    }

    //Object转Long
    private Long toL(Object o) {
        return o != null ? new BigDecimal(o + "").longValue() : null;
    }

    //Object转String
    private String toStr(Object o) {
        return o == null ? "" : o.toString();
    }


    /**
     * 统计装置报警响应及时次数
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @return 统计装置报警响应及时次数
     * <AUTHOR> 2022-11-30
     */
    @Override
    public List<AlarmTimelyCount> getAlarmTimelyResponseCount(String[] unitCodes, Date startTime, Date
            endTime, Integer companyId) {
        List<AlarmTimelyCount> alarmTimelyResponseList = new ArrayList<>();

        try {
            String hql = "select new com.pcitc.opal.ad.dao.imp.AlarmTimelyCount(t.unitCode,count(t.alarmRecId)) from AlarmRec t " +
                    "inner join AlarmPoint ap on t.alarmPointId =ap.alarmPointId \n" +
                    "inner join PrdtCell pc on ap.prdtCellId =pc.prdtCellId " +
                    "where t.alarmTime between :startTime and :endTime " +
                    "and t.eventTypeId=" + CommonEnum.EventTypeEnum.ProcessEvent.getIndex() +
                    "and t.alarmFlagId is not null \n" +
                    "and t.priority is not null\n" +
                    "and ap.inUse =1 and pc.inUse = 1 " +
                    "and ((t.responseTime is null and timestampdiff(second,t.alarmTime,now()) <=30) or (t.responseTime is not null and timestampdiff(second,t.alarmTime,t.responseTime) <=30))" +
                    "and t.companyId = (:companyId) ";
            //"and if(t.responseTime is null, now()-t.alarmTime <=30, t.responseTime-t.alarmTime <=30) "+

            Map<String, Object> paramList = new HashMap<String, Object>();
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql += " and t.unitCode in (:unitIds) ";
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            //企业
            paramList.put("companyId", companyId);
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
            hql += " group by t.unitCode ";
            Query query = this.getEntityManager().createQuery(hql);
            this.setParameterList(query, paramList);
            alarmTimelyResponseList = query.getResultList();

        } catch (Exception ex) {
            throw ex;
        }
        return alarmTimelyResponseList;
    }

    /**
     * 统计装置报警处置及时次数
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @return 统计装置报警处置及时次数
     * <AUTHOR> 2022-11-30
     */
    @Override
    public List<AlarmTimelyCount> getAlarmTimelyDisposalCount(String[] unitCodes, Date startTime, Date
            endTime, Integer companyId) {
        List<AlarmTimelyCount> alarmTimelyDisposalList = new ArrayList<>();
        try {
            String hql = "select new com.pcitc.opal.ad.dao.imp.AlarmTimelyCount(t.unitCode,count(t.alarmRecId)) from AlarmRec t " +
                    "inner join AlarmPoint ap on t.alarmPointId =ap.alarmPointId \n" +
                    "inner join PrdtCell pc on ap.prdtCellId =pc.prdtCellId " +
                    "where t.alarmTime between :startTime and :endTime " +
                    "and t.eventTypeId=" + CommonEnum.EventTypeEnum.ProcessEvent.getIndex() +
                    " and t.alarmFlagId is not null \n" +
                    "and t.priority is not null \n" +
                    "and ap.inUse =1 and pc.inUse = 1 " +
                    "and ((t.recoveryTime is null and timestampdiff(second,t.alarmTime,now()) <=1800 ) or (t.recoveryTime is not null and timestampdiff(second,t.alarmTime,t.recoveryTime) <=1800 ))" +
                    "and t.companyId = (:companyId) ";
            //"and if(t.recoveryTime is null, now()-t.alarmTime <=1800, t.recoveryTime-t.alarmTime <=1800) "+
            Map<String, Object> paramList = new HashMap<String, Object>();
            if (ArrayUtils.isNotEmpty(unitCodes)) {
                hql += " and t.unitCode in (:unitIds) ";
                paramList.put("unitIds", Arrays.asList(unitCodes));
            }
            //企业
            paramList.put("companyId", companyId);
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
            hql += " group by t.unitCode ";
            Query query = this.getEntityManager().createQuery(hql);
            this.setParameterList(query, paramList);
            alarmTimelyDisposalList = query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
        return alarmTimelyDisposalList;
    }

    @Override
    public List<AlarmRec> selectDelAlarmRec(List<AlarmPointDelConfig> alarmPointDelConfigs) {
        if (alarmPointDelConfigs.size() == 0) {
            return new ArrayList<AlarmRec>();
        }

        StringBuilder hql = new StringBuilder("from AlarmRec where 1=2");
        //查询剔除配置信息
        for (AlarmPointDelConfig a : alarmPointDelConfigs) {
            //过滤报警点
            hql.append(" or (( alarmPointId  in (").append(a.getAlarmPointId()).append(") ");
            //过滤报警标识,-9表示标识为空
            if (a.getAlarmFlagId().contains("-9")) {
                hql.append(" and (");
                hql.append("  alarmFlagId  in (").append(a.getAlarmFlagId()).append(") ");
                hql.append(" or alarmFlagId is null )");
            } else {
                hql.append(" and alarmFlagId  in (").append(a.getAlarmFlagId()).append(") ");
            }
            //过滤企业id
            hql.append(" and companyId = ").append(a.getCompanyId());
            //过滤时间
            hql.append(" and alarmTime  between '")
                    .append(org.apache.commons.lang.time.DateFormatUtils.format(a.getDelStartTime(),
                            "yyyy-MM-dd HH:mm:ss")).append("' and '")
                    .append(org.apache.commons.lang.time.DateFormatUtils.format(a.getDelEndTime(),
                            "yyyy-MM-dd HH:mm:ss")).append("' ))");
        }


        return getEntityManager().createQuery(hql.toString(), AlarmRec.class).getResultList();

    }

    @Transactional
    @Override
    public void deleteBatch(List<Long> alarmRecs, Integer batchSize) {
        batchSize = batchSize == null ? 1000 : batchSize;

        List<List<Long>> lists = ListUtils.splitList(alarmRecs, batchSize);

        for (List<Long> list : lists) {

            String sql = "delete from AlarmRec where alarmRecId in (:alarmRecId)";

            Query query = getEntityManager().createQuery(sql);

            query.setParameter("alarmRecId", list);
            int i = query.executeUpdate();
            log.info("记录表删除--{}", i);

        }

//        for (int i = 0; i < alarmRecs.size(); i++) {
//            getEntityManager().remove(alarmRecs.get(i));
//            if (i % batchSize == 0) {
//                getEntityManager().flush();
//                getEntityManager().clear();
//            }
//        }
//
//        getEntityManager().flush();
//        getEntityManager().clear();
    }

    @Override
    public PaginationBean<AlarmRec> getAlarmExamineRec(String[] unitIds, Long[] prdtCellIds, String tag, Integer
            alarmFlagId, Integer priority, Date startTime, Date endTime, Integer alarmDuration, Pagination page) {

        StringBuilder hql = new StringBuilder();

        StringBuilder where = new StringBuilder();

        HashMap<String, Object> param = new HashMap<>();

        hql.append("select ar from AlarmRec ar ")
                .append(" inner join ar.alarmFlag af")
                .append(" inner join ar.prdtCell pc ")
                .append(" inner join ar.alarmPoint ap ");

        //过滤装置
        where.append(" where ar.unitCode in (:unitIds) ");
        param.put("unitIds", Arrays.asList(unitIds));

        CommonProperty commonProperty = new CommonProperty();
        where.append(" and ar.companyId = :companyId ");
        param.put("companyId", commonProperty.getCompanyId());

        //过滤时间
        where.append(" and ar.alarmTime between :startTime and :endTime ");
        param.put("startTime", startTime);
        param.put("endTime", endTime);

        //查询报警点已启用的数据
        where.append(" and ap.inUse = 1 ");

        //已经在审查流程中的数据不会展示在未变更列表中
        where.append(" and ar.alarmRecId not in (select distinct alarmRecId from AlarmExamineRec ) ");


        //1:0.5~1；2:1~12；3:12~24；4:＞＝24小时
        switch (alarmDuration) {
            case 1:
                where.append(
                        " and TIMESTAMPDIFf(minute, ar.alarmTime, ifnull(ar.recoveryTime, :endTime)) between 30 and 59 ");
                break;
            case 2:
                where.append(
                        " and TIMESTAMPDIFf(hour, ar.alarmTime, ifnull(ar.recoveryTime, :endTime)) between 1 and 11 ");
                break;
            case 3:
                where.append(
                        " and TIMESTAMPDIFf(hour, ar.alarmTime, ifnull(ar.recoveryTime, :endTime)) between 12 and 23 ");
                break;
            case 4:
                where.append(" and TIMESTAMPDIFf(hour, ar.alarmTime, ifnull(ar.recoveryTime, :endTime)) > 24 ");
                break;
        }


        //过滤生产单元
        if (ArrayUtils.isNotEmpty(prdtCellIds)) {
            where.append(" and ar.prdtCellId in (:prdtCellIds) ");
            param.put("prdtCellIds", Arrays.asList(prdtCellIds));
        }

        //过滤位号
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(tag)) {
            where.append(" and ar.tag like :tag ");
            param.put("tag", "%" + tag + "%");
        }

        //过滤报警标识
        if (alarmFlagId != null && alarmFlagId != -1) {
            where.append(" and ar.alarmFlagId in (:alarmFlagId) ");
            param.put("alarmFlagId", Long.valueOf(alarmFlagId));
        }

        //过滤优先级
        if (priority != null && priority != -1) {
            where.append(" and ar.priority in (:priority) ");
            param.put("priority", priority);
        }


        where.append(" order by ar.alarmTime desc ");


        String sql = hql.toString() + where.toString();

        return findAll(page, sql, param);
    }

    @Transactional
    @Override
    public Integer updateResponseTimeByRec(AlarmRec alarmRec, Integer companyId) {
        String sql = "   UPDATE AlarmRec T\n" +
                "        SET T.responseTime = :responseTime\n" +
                "        WHERE T.unitCode = :unitCode\n" +
                "          AND T.prdtCellId = :prdtCellId\n" +
                "          AND T.tag = :tag\n" +
                "          AND T.alarmFlagId = :alarmFlagId\n" +
                "          AND T.alarmTime = :alarmTime\n" +
                "          and responseTime is null" +
                "          and companyId = :companyId";

        return getEntityManager().createQuery(sql)
                .setParameter("responseTime", alarmRec.getResponseTime())
                .setParameter("unitCode", alarmRec.getUnitCode())
                .setParameter("prdtCellId", alarmRec.getPrdtCellId())
                .setParameter("tag", alarmRec.getTag())
                .setParameter("alarmFlagId", alarmRec.getAlarmFlagId())
                .setParameter("alarmTime", alarmRec.getAlarmTime())
                .setParameter("companyId", companyId)
                .executeUpdate();
    }

    @Override
    public PaginationBean<AlarmDurationDtlEntityVO> getAlarmNumStattDtl(Date startTime, Date endTime, String
            unitCode, Integer priority, Long[] alarmFlagIds, Pagination page) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            StringBuilder hql = new StringBuilder(
                    " select new com.pcitc.opal.ad.dao.imp.AlarmDurationDtlEntityVO(ar.alarmTime,"
//                            +" round((TIME_TO_SEC(timediff(ar.recoveryTime,ar.alarmTime))/convert(60,decimal(10,2)),2) , "
                            + " TIME_TO_SEC(timediff(ar.recoveryTime,ar.alarmTime)), "
                            + " ar.recoveryTime,pc.sname,ar.tag,ar.des,af.name) "
                            + " from AlarmRec ar "
                            + " left join ar.alarmPoint ap  "
                            + " inner join ar.prdtCell pc  "
                            + " left join ar.alarmFlag af "
                            + " where ar.eventTypeId = :eventTypeId and ar.companyId=:companyId "
                            + " and case when ar.alarmPointId is not null then ap.inUse  end =1 "
            );
            Map<String, Object> paramList = new HashMap<String, Object>();
            paramList.put("eventTypeId", CommonEnum.EventTypeEnum.ProcessEvent.getIndex());
            paramList.put("startTime", startTime);
            paramList.put("endTime", endTime);
            //企业
            CommonProperty commonProperty = new CommonProperty();
            paramList.put("companyId", commonProperty.getCompanyId());

            // 过滤装置
            if (!StringUtils.isEmpty(unitCode)) {
                hql.append(" and ar.unitCode =:unitCode ");
                paramList.put("unitCode", unitCode);
            }
            // 过滤优先级
            if (priority != null && priority != -1) {
                if (priority == 9) {
                    hql.append(" and ar.priority is null ");
                } else {
                    hql.append(" and ar.priority = :priority ");
                    paramList.put("priority", priority);
                }
            }
            // 过滤报警标识
            if (alarmFlagIds != null && alarmFlagIds.length > 0) {
                for (int i = 0; i < alarmFlagIds.length; i++) {
                    if (alarmFlagIds[i] == (-9L)) {
                        hql.append(" and ( ar.alarmFlagId in (:alarmFlagIds) or ar.alarmFlagId is null) ");
                        break;
                    }
                    if (i == alarmFlagIds.length - 1) {
                        hql.append(" and  ar.alarmFlagId in (:alarmFlagIds) ");
                    }
                }
                paramList.put("alarmFlagIds", Arrays.asList(alarmFlagIds));
            }
            //过滤时间
            if (null != startTime && null != endTime) {
                hql.append(" and ar.alarmTime <= :endTime and ar.recoveryTime >=:startTime ");
                paramList.put("endTime", endTime);
                paramList.put("startTime", startTime);
            }
            hql.append(" order by ar.alarmTime desc,ar.tag desc,af.name desc ");


            Long count = Long.valueOf(this.findCusCount(hql.toString(), paramList));
            BaseRepository<AlarmDurationDtlEntityVO, Long> br = new BaseRepository();
            return br.findCusTomAll(this.getEntityManager(), page, count, hql.toString(), paramList,
                    AlarmDurationDtlEntityVO.class);

        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<AlarmRec> getMaxRecIdOrRestored(Long maxId) {

        if (maxId == null) {
            maxId = 0L;
        }

        String hql = " from AlarmRec ar inner join ar.alarmPoint ap  where ap.inUse = 1 and (ar.recoveryTime is not null or ar.alarmRecId > :maxId)";

        Query query = getEntityManager().createQuery(hql);
        query.setParameter("maxId", maxId);

        return query.getResultList();
    }

    @Override
    public Long getMaxRecId() {
        //获取最大recId
        String maxIdHql = "select max(currentId) from AlarmRecLog";
        Long singleResult = getEntityManager().createQuery(maxIdHql, Long.class).getSingleResult();
        return singleResult == null ? 0L : singleResult;
    }

    @Override
    public void insertMaxRecId(AlarmRecLog alarmRecLog) {
        getEntityManager().persist(alarmRecLog);
        getEntityManager().flush();
    }

    @Override
    public AlarmRec getAlarmRecByEventId(Long eventId) {
        String hql = "from AlarmRec where eventId in (:eventId)";
        TypedQuery<AlarmRec> query = getEntityManager().createQuery(hql, AlarmRec.class);
        query.setParameter("eventId", eventId);
        List<AlarmRec> resultList = query.getResultList();
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        return resultList.get(0);
    }


    @MeasureTime
    @Override
    public List<AlarmRec> getAlarmRec(Date startTime, Date endTime, String[] unitIds) {
        String hql = "from AlarmRec rec " +
                "inner join AlarmPoint ap on rec.alarmPointId = ap.alarmPointId " +
                "inner join MeasUnit mu on ap.measunitId = mu.measUnitId " +
                "inner join PrdtCell prd on rec.prdtCellId = prd.prdtCellId " +
                "inner join AlarmFlag af on rec.alarmFlagId = af.alarmFlagId " +
                "where rec.alarmTime between :start and :end and prd.unitId in (:unitCodes) " +
                "and rec.priority is not null and rec.alarmFlagId is not null and ap.inUse = 1";

        Query query = getEntityManager().createQuery(hql);
        query.setParameter("start", startTime)
                .setParameter("end", endTime)
                .setParameter("unitCodes", Arrays.asList(unitIds));

        return query.getResultList();
    }

    @Override
    public List<AlarmRec> getNotRecoveryTime(Date end) {
        String hql = "from AlarmRec rec where rec.recoveryTime is null and timestampdiff(second,rec.alarmTime,:end) between 20*60*60 and 24*60*60";
        Query query = getEntityManager().createQuery(hql);
        query.setParameter("end", end);
        return query.getResultList();
    }

    @Override
    public List<AlarmRec> getAlarmRec(Date start, Date end, String unitCode) {
        String hql = "from AlarmRec where alarmTime between :start and :end and unitCode = :unitCode";

        TypedQuery<AlarmRec> query = getEntityManager().createQuery(hql, AlarmRec.class);
        query.setParameter("start", start);
        query.setParameter("end", end);
        query.setParameter("unitCode", unitCode);
        return query.getResultList();
    }

    @MeasureTime
    @Override
    public List<AlarmRec> getAlarmRecByMonitor(Date startTime, Date endTime, String[] unitIds, Integer monitorType) {
        StringBuilder hqlS = new StringBuilder("from AlarmRec rec inner join AlarmPoint ap " +
                "on rec.alarmPointId = ap.alarmPointId inner join PrdtCell prd " +
                "on rec.prdtCellId = prd.prdtCellId inner join AlarmFlag af " +
                "on rec.alarmFlagId = af.alarmFlagId " +
                "where rec.alarmTime between :start and :end and rec.unitCode in (:unitCodes) " +
                "and rec.priority is not null and rec.alarmFlagId is not null and ap.inUse = 1");
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("start", startTime);
        paramList.put("end", endTime);
        paramList.put("unitCodes", Arrays.asList(unitIds));
        if (monitorType != null && monitorType != 9) {
            hqlS.append("  and ap.monitorType = :monitorType ");
            paramList.put("monitorType", monitorType);
        }

        Query query = getEntityManager().createQuery(hqlS.toString());
        setParameterList(query, paramList);

        return query.getResultList();
    }

    @Override
    public List<AlarmTagUnitVO> getTagUnitByMonitor(Date startTime, Date endTime, String[] unitIds, Integer
            monitorType) {
        StringBuilder hqlS = new StringBuilder(
                "select new com.pcitc.opal.ad.dao.imp.AlarmTagUnitVO(rec.tag,rec.unitCode,ut.sname,count(*) as size)");
        hqlS.append(
                "from AlarmRec rec " +
                        "inner join AlarmPoint ap on rec.alarmPointId = ap.alarmPointId " +
                        "inner join PrdtCell prd on rec.prdtCellId = prd.prdtCellId " +
                        "inner join AlarmFlag af on rec.alarmFlagId = af.alarmFlagId " +
                        "left join Unit ut on ut.stdCode = rec.unitCode " +
                        "where rec.alarmTime between :start and :end " +
                        "and rec.unitCode in (:unitCodes) " +
                        "and rec.priority is not null " +
                        "and rec.alarmFlagId is not null and ap.inUse = 1 "
        );
        Map<String, Object> paramList = new HashMap<String, Object>();
        paramList.put("start", startTime);
        paramList.put("end", endTime);
        paramList.put("unitCodes", Arrays.asList(unitIds));
        if (monitorType != null && monitorType != 9) {
            hqlS.append("  and ap.monitorType = :monitorType ");
            paramList.put("monitorType", monitorType);
        }
        hqlS.append(" group by rec.tag order by size desc ");

        Query query = getEntityManager().createQuery(hqlS.toString());
        setParameterList(query, paramList);

        return query.getResultList();
    }

    /**
     * 设置查询参数
     *
     * @param query
     * @param paramList 查询参数列表
     * <AUTHOR> 2017-11-12
     */
    protected void setParameterList(Query query, Map<String, Object> paramList) {
        for (Map.Entry<String, Object> pair : paramList.entrySet()) {
            query.setParameter(pair.getKey(), pair.getValue());
        }
    }

}
