package com.pcitc.opal.cm.bll.entity;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;


/**
 * <p>
 * 工艺变更单信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-31
 */

public class CraftChangeInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工艺变更单信息ID
     */
    private Long craftChangeInfoId;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 装置编码
     */
    private String unitCode;

    /**
     * 装置简称
     */
    private String unitSname;

    /**
     * 申请单编号
     */
    private String sn;

    /**
     * 位号
     */
    private String tagCode;

    /**
     * 物料
     */
    private String mtrl;

    /**
     * 项目
     */
    private String item;

    /**
     * 计量单位
     */
    private String measunit;

    /**
     * 报警标识（PHVV：高高报、PVHI：高报、PVLO：低报、PVLL：低低报）
     */
    private String submitUserName;

    /**
     * 修改前指标
     */
    private String beforeValue;

    /**
     * 修改后指标
     */
    private String afterValue;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 发布时间
     */
    private Date rlsTime;

    /**
     * 写入时间
     */
    private Date writeTime;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getCraftChangeInfoId() {
        return craftChangeInfoId;
    }

    public void setCraftChangeInfoId(Long craftChangeInfoId) {
        this.craftChangeInfoId = craftChangeInfoId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitSname() {
        return unitSname;
    }

    public void setUnitSname(String unitSname) {
        this.unitSname = unitSname;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getTagCode() {
        return tagCode;
    }

    public void setTagCode(String tagCode) {
        this.tagCode = tagCode;
    }

    public String getMtrl() {
        return mtrl;
    }

    public void setMtrl(String mtrl) {
        this.mtrl = mtrl;
    }

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public String getMeasunit() {
        return measunit;
    }

    public void setMeasunit(String measunit) {
        this.measunit = measunit;
    }

    public String getSubmitUserName() {
        return submitUserName;
    }

    public void setSubmitUserName(String submitUserName) {
        this.submitUserName = submitUserName;
    }

    public String getBeforeValue() {
        return beforeValue;
    }

    public void setBeforeValue(String beforeValue) {
        this.beforeValue = beforeValue;
    }

    public String getAfterValue() {
        return afterValue;
    }

    public void setAfterValue(String afterValue) {
        this.afterValue = afterValue;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Date getRlsTime() {
        return rlsTime;
    }

    public void setRlsTime(Date rlsTime) {
        this.rlsTime = rlsTime;
    }

    public Date getWriteTime() {
        return writeTime;
    }

    public void setWriteTime(Date writeTime) {
        this.writeTime = writeTime;
    }
}
