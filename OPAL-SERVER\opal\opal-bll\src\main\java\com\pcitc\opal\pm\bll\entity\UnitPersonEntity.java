package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.ComparatorList;
import com.pcitc.opal.common.bll.entity.BasicEntity;

import java.util.Comparator;

/*
 * 装置人员信息实体
 * 模块编号：pcitc_opal_bll_class_UnitPersonEntity
 * 作    者：xuelei.wang
 * 创建时间：2017-11-30 19:31:39
 * 修改编号：1
 * 描    述：装置人员信息实体
 */
public class UnitPersonEntity extends BasicEntity {

    /**
     * 装置人员ID
     */
    private Long unitPersonId;

    /**
     * 操作人数
     */
    private Long operNum;

    /**
     * 装置ID
     */
    private String unitId;


    /**
     * 是否启用（1是；0否）
     */
    private Integer inUse;

    /**
     * 装置名称
     */
    private String name;

    /**
     * 装置简称
     */
    private String sName;
    /**
     * 车间名称
     */
    private String workShopName;
    private String workShopCode;
    private String workShopSname;

    /**
     * 工厂编码
     */
    private String factoryCode;

    public String getWorkShopSname() {
        return workShopSname;
    }

    public void setWorkShopSname(String workShopSname) {
        this.workShopSname = workShopSname;
    }

    public String getFactoryCode() {
        return factoryCode;
    }

    public void setFactoryCode(String factoryCode) {
        this.factoryCode = factoryCode;
    }

    public Long getUnitPersonId() {
        return unitPersonId;
    }

    public void setUnitPersonId(Long unitPersonId) {
        this.unitPersonId = unitPersonId;
    }

    public Long getOperNum() {
        return operNum;
    }

    public void setOperNum(Long operNum) {
        this.operNum = operNum;
    }

    public String getWorkShopCode() {
        return workShopCode;
    }

    public void setWorkShopCode(String workShopCode) {
        this.workShopCode = workShopCode;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public Integer getInUse() {
        return inUse;
    }

    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }



    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getsName() {
        return sName;
    }
    public String getSName() {
        return sName;
    }
    public String getWorkShopName() {
        return workShopName;
    }

    public void setWorkShopName(String workShopName) {
        this.workShopName = workShopName;
    }

    public void setsName(String sName) {
        this.sName = sName;
    }
    public void setSName(String sName) {
        this.sName = sName;
    }


}

