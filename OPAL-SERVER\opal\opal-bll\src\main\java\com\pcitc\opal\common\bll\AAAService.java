package com.pcitc.opal.common.bll;


import com.pcitc.opal.common.bll.entity.AAAPropertyValueEntity;
import com.pcitc.opal.common.bll.entity.AAAOrgUnitEntity;
import com.pcitc.opal.common.bll.entity.AAAUserEntity;
import com.pcitc.opal.common.bll.entity.UserEntity;
import org.springframework.stereotype.Service;

import java.rmi.RemoteException;
import java.util.List;

/*
 * AAA平台提供的面向应用的服务接口
 * 模块编号：pcitc_opal_common_interface_AuthenticateService
 * 作    者：jiangtao.xue
 * 创建时间：2017-12-14
 * 修改编号：1
 * 描    述：AAA平台提供的面向应用的服务接口
 */
public interface AAAService {
    /**
     * 根据角色获取用户集合
     *
     * @param role 用户角色
     * @return
     * <AUTHOR> 2018-02-05
     */
    List<AAAUserEntity> listUsersByRole(String role) throws Exception;

    /**
     * 根据UserId 获取所属的组织单元
     *
     * @param userId 用户ID或者LoginName
     * @return 组织单元
     * @throws Exception
     * <AUTHOR> 2018-03-09
     */
    AAAOrgUnitEntity getOrgByUserId(String userId) throws Exception;

    /**
     * 获取用户授权属性
     * @param userId 用户ID
     * @return
     * @throws Exception
     * <AUTHOR> 2017-12-20
     */
    List<AAAPropertyValueEntity> getAuthPropertyValueList(String userId) throws Exception;
    /**
     * 获取指定用户的详细信息
     *
     * @param userCode 用户编码
     * @return 用户信息
     * @throws Exception
     */
    UserEntity getUserInfoByUserCode(String userCode) throws Exception;
    Integer getCompanyIdByUserId(String userId);
}
