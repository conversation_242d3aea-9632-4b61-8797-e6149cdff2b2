package com.pcitc.opal.pm.dao;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pcitc.opal.pm.pojo.RelevantTagConfigDtl;

/*
 * RelevantTagConfigDtl实体的Repository的JPA标准接口 
 * 模块编号：pcitc_opal_dal_interface_RelevantTagConfigDtlRepository
 * 作       者：dageng.sun
 * 创建时间：2018/8/2
 * 修改编号：1
 * 描       述：RelevantTagConfigDtl实体的Repository实现   
 */
public interface RelevantTagConfigDtlRepository extends JpaRepository<RelevantTagConfigDtl, Long>, RelevantTagConfigDtlRepositoryCustom {

}
