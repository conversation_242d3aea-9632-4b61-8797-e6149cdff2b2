package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.common.CommonProperty;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.pm.bll.AlarmMsgConfigService;
import com.pcitc.opal.pm.bll.SendMsgAlarmConfService;
import com.pcitc.opal.pm.bll.entity.AlarmMsgConfigEntity;
import com.pcitc.opal.pm.dao.*;
import com.pcitc.opal.pm.pojo.AlarmPoint;
import com.pcitc.opal.pm.pojo.MobileList;
import com.pcitc.opal.pm.pojo.SendMsgAlarmConf;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AlarmMsgConfigImpl implements AlarmMsgConfigService {

    @Autowired
    private BasicDataService basicDataService;

    @Autowired
    private MobileListRepository mobileListRepository;
    @Autowired
    private AlarmPointRepository repo;

    @Autowired
    SendMsgAlarmConfService sendMsgAlarmConfService;

    @Resource
    private SendMsgAlarmConfRepositoryCustom sendMsgAlarmConfRepositoryCustom;

    @Resource
    private SendMsgAlarmConfRepository sendMsgAlarmConfRepository;

    @Override
    public PaginationBean<AlarmMsgConfigEntity> getAlarmMsgConfigByInfo(String[] unitCodes, Long[] prdtCellIds, String tag, Integer inSendMsg, Integer timeInterval, Pagination page) throws Exception{
        if(StringUtils.isNotBlank(tag)) {
            tag = tag.trim();
        }
        if (ArrayUtils.isEmpty(unitCodes)){
            List<UnitEntity> unitList = basicDataService.getUnitList(true);
            unitCodes = unitList.stream().map(UnitEntity::getStdCode).toArray(String[]::new);
        }
        if (prdtCellIds!=null&&prdtCellIds.length==0){
            prdtCellIds=null;
        }
        PaginationBean<SendMsgAlarmConf> alarmMsgConfigList = sendMsgAlarmConfRepositoryCustom.findSendMsgAlarmConfByInfo(unitCodes,prdtCellIds, tag, inSendMsg, timeInterval, page);
        PaginationBean<AlarmMsgConfigEntity> returnList = new PaginationBean<AlarmMsgConfigEntity>(page,alarmMsgConfigList.getTotal());

        StringBuilder mobId = new StringBuilder();

        for(SendMsgAlarmConf o:alarmMsgConfigList.getPageList()){
            AlarmMsgConfigEntity ae = new AlarmMsgConfigEntity();
            ae.setAlarmMsgConfId(o.getSendMsgAlarmConfId());
            ae.setMobileListId(o.getMobileListIds());
            ae.setTimeInterval(o.getTimeInterval());
            ae.setTag(o.getAlarmPoint().getTag());
            ae.setPrdtcellName(o.getPrdtCell().getName());
            ae.setAlarmFlagName(o.getAlarmFlag().getName());
            ae.setUnitName(o.getUnit().getName());
            ae.setInSendMsg(o.getAlarmPoint().getInSendMsg().toString());
            mobId.append(o.getMobileListIds()).append(",");
            returnList.getPageList().add(ae);
        }

        //电话本为空跳过赋值
        if (mobId.length() == 1 || StringUtils.isEmpty(mobId.toString())){
            return returnList;
        }

        List<Long> collect = Arrays.stream(mobId.toString().split(",")).distinct().map(Long::parseLong).collect(Collectors.toList());
        //单独查询电话本信息
        List<MobileList> mobileLists = mobileListRepository.findAllById(collect);

        //电话本信息赋值
        Map<Long, String> mobileMap = mobileLists.stream().collect(Collectors.toMap(MobileList::getMobileListId, mo -> mo.getName() + "(" + mo.getMobile() + ")"));
        for (AlarmMsgConfigEntity ae : returnList.getPageList()) {
            if(StringUtils.isNotEmpty(ae.getMobileListId())) {
                StringBuilder sb = new StringBuilder();
                String[] mobile = ae.getMobileListId().split(",");
                for (String str : mobile) {
                    sb.append(mobileMap.get(Long.valueOf(str))).append(",");
                }
                ae.setMobileListStr(sb.substring(0, sb.length() - 1));
            }
        }
        return returnList;
    }

    @Override
    public List<AlarmMsgConfigEntity> getAlarmMsgConfigByAlarmMsgConfId(Long[] alarmMsgConfId) {
        List<Object[]> list = sendMsgAlarmConfRepositoryCustom.findSendMsgAlarmConfBySendMsgAlarmConfId(alarmMsgConfId);
        List<AlarmMsgConfigEntity> aeList = new ArrayList<>();
        for(Object[] o :list){
            AlarmMsgConfigEntity ae = new AlarmMsgConfigEntity();
            ae.setAlarmMsgConfId(toL(o[0]));
            ae.setAlarmPointId(toL(o[1]));
            ae.setAlarmFlagId(toL(o[2]));
            ae.setMobileListId(o[3].toString());
            ae.setTimeInterval(toL(o[4]));
            ae.setTag(o[5].toString());
            ae.setPrdtcellName(o[6].toString());
            ae.setAlarmFlagName(o[7].toString());
            ae.setUnitName(o[8].toString());
            ae.setInSendMsg(o[9].toString());
            if(ae.getMobileListId() !=null && !"".equals(ae.getMobileListId())){
                StringBuilder sb = new StringBuilder();
                String[] mobile = ae.getMobileListId().split(",");
                for(String str : mobile){
                    MobileList mo=mobileListRepository.findById(Long.valueOf(str)).get();
                    sb.append(mo.getName()+"("+mo.getMobile()+")").append(",");
                }
                ae.setMobileListStr(sb.substring(0,sb.length()-1));
                ae.setMobileBook(ae.getMobileListStr());
            }
            aeList.add(ae);
        }
        return aeList;
    }

    @Override
    public CommonResult updateAlarmMsgConfigInfo(Long[] sendMsgAlarmConfId, String mobileBookId,Long timeInterval, Integer inSendMsg) {
        if (inSendMsg != 0 && inSendMsg != 1){
            throw new RuntimeException("参数不合法");
        }
        CommonResult commonResult = new CommonResult();
        try {
            int i = sendMsgAlarmConfRepositoryCustom.updateSendMsgAlarmConfBySendMsgAlarmConfId(sendMsgAlarmConfId, mobileBookId, timeInterval);
            Long[] alarmPointId = sendMsgAlarmConfRepositoryCustom.findAlarmPointIdBySendMsgAlarmConfId(sendMsgAlarmConfId);
            Integer result = repo.updateInSendMsgByAlarmPointId(alarmPointId, inSendMsg);

            commonResult.setMessage("更新成功");

        } catch (Exception e) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(e.getMessage());
        }
        return commonResult;
    }

    @Override
    public CommonResult deleteAlarmMsgConfigById(Long[] id) {
        boolean isSuccess = true;
        for (Long aLong : id) {
            try {
                sendMsgAlarmConfRepository.deleteById(aLong);
            }catch (Exception e) {
                e.printStackTrace();
                isSuccess = false;
                continue;
            }
        }
        CommonResult commonResult = new CommonResult();
        if (isSuccess){
            commonResult.setMessage("删除成功");
        }else {
            commonResult.setMessage("部分删除成功");
        }
        commonResult.setIsSuccess(isSuccess);
        return commonResult;
    }

    @Override
    public CommonResult addOrUpdateAlarmMsgConfig(Long[] alarmPointIds, Long[] alarmFlagIds, String mobileBookId, Long timeInterval, Integer inSendMsg) {
        CommonProperty commonProperty = new CommonProperty();
        Integer companyId = commonProperty.getCompanyId();
        CommonResult cm = new CommonResult();
        try {
            for(Long ap :alarmPointIds){
                for(Long af:alarmFlagIds){

                    SendMsgAlarmConf ac = this.sendMsgAlarmConfRepositoryCustom.getAlarmMsgConfigByInfo(ap, af, timeInterval);
                    Long timeInt = Long.valueOf(timeInterval);
                    if (ac == null) {
                        SendMsgAlarmConf a = new SendMsgAlarmConf();
                        a.setAlarmPointId(Long.valueOf(ap));
                        a.setAlarmFlagId(Long.valueOf(af));
                        a.setMobileListIds(mobileBookId);
                        a.setTimeInterval(timeInt);
                        a.setCompanyId(companyId);
                        sendMsgAlarmConfRepository.save(a);
                    } else if (!ac.getTimeInterval().equals(timeInt)) {
                        sendMsgAlarmConfRepositoryCustom.updateTimeIntervalBySendMsgAlarmConf(timeInt, ac.getSendMsgAlarmConfId());
                    }else {
                        continue;
                    }

                    AlarmPoint p = this.repo.findById(ap).get();
                    if (!inSendMsg.equals(p.getInSendMsg())){
                        String userId = commonProperty.getUserId();
                        String userName = commonProperty.getUserName();
                        p.setInSendMsg(inSendMsg);
                        p.setMntUserId(userId);
                        p.setMntUserName(userName);
                        p.setMntDate(new Date());
                        p.setCompanyId(companyId);
                        this.repo.updateAlarmPoint(p);
                    }

                }
            }
            cm.setIsSuccess(true);
            cm.setMessage("操作成功！");
        } catch (Exception e) {
            cm.setIsSuccess(false);
            cm.setMessage(e.getMessage());
        }
        return cm;
    }

    //Object转Long
    private Long toL(Object o) {
        return o != null ? new BigDecimal(o+"").longValue() : null;
    }
    //Object转String
    private String toStr(Object o ){
        return o==null ? "":o.toString();
    }
}
