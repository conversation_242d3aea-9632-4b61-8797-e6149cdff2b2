package com.pcitc.opal.pm.bll.imp;

import com.alibaba.fastjson.JSON;
import com.pcitc.opal.common.EncryptUtil;
import com.pcitc.opal.common.ListUtils;
import com.pcitc.opal.pm.bll.SendMsgService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @USER: chenbo
 * @DATE: 2023/1/30
 * @TIME: 11:16
 * @DESC: 荆门短信发送实现类
 **/
@Slf4j
@Component
@ConditionalOnProperty(name = "send_type", havingValue = "jm", matchIfMissing = false)
public class SendMsgServiceImplJM implements SendMsgService {


    @Value("${send.ecName:中国石油化工股份有限公司荆门分公司信息管理中心}")
    String ecName;

    @Value("${send.apId:jmbjts}")
    String apId;

    @Value("${send.secretkey:JM2023+bjdx}")
    String secretkey;

    @Value("${send.sign:KmuppCMyL}")
    String sign;


    final String URL = "https://*************:58888/sms/submit";


    @Override
    public Map<String, String> sendBatch(List<String> mobiles, String content) {
        //key->手机号，value->发送结果
        HashMap<String, String> resMap = new HashMap<>();

        //请求对象，忽略主机名验证
        OkHttpClient client = new OkHttpClient.Builder()
                .hostnameVerifier((hostname, session) -> true)
                .build();

        //号码数量小于1000个
        List<List<String>> lists = ListUtils.splitList(mobiles, 999);


        for (List<String> list : lists) {

            //保存请求结果以及异常信息
            StringBuilder result = new StringBuilder();

            //拼接手机号
            String join = StringUtils.join(list, ",");
            JMEntity jmEntity = new JMEntity();
            jmEntity.setMobiles(join);
            jmEntity.setContent(content);
            jmEntity.setSign(sign);
            jmEntity.setApId(apId);
            jmEntity.setEcName(ecName);
            jmEntity.setSecretkey(secretkey);
            //将参数进行md5加密并且设置到改
            jmEntity.setMac(jmEntity.getSplicing());

            String jsonString = JSON.toJSONString(jmEntity);

            //base64编码
            String base64 = EncryptUtil.base64Encode(jsonString);

            RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.TEXT_PLAIN_VALUE), base64);
            Request request = new Request.Builder()
                    .url(URL)
                    .post(body)
                    .addHeader("content-type", "application/json")
                    .build();
            try {
                Response response = client.newCall(request).execute();

                String res = Objects.requireNonNull(response.body()).string();
                res = res.replaceAll("true", "发送成功").replaceAll("false", "发送失败");
                result.append("返回结果：").append(res);

            } catch (IOException e) {
                result.append("当前发送短信接口调用失败，手机号为").append(StringUtils.join(mobiles, ",")).append("，异常内容").append(e.getMessage());
                e.printStackTrace();
            } catch (Exception e) {
                result.append("发送异常").append(e.getMessage());
                e.printStackTrace();
            } finally {
                //循环将接口返回结果复制到map中
                list.forEach(x -> resMap.put(x, result.toString()));
            }

        }

        return resMap;
    }


}


@Data
class JMEntity {


    private String ecName;
    private String apId;
    private String secretkey;
    private String mobiles;
    private String content;
    private String sign;
    private String addSerial = "";
    private String mac;


    /**
     * 参数校验序列，生成方法：将ecName、apId、secretKey、mobiles、content、sign、addSerial按序拼接（无间隔符），
     * 通过MD5（32位小写）计算得出值
     */
    public String getSplicing() {
        String splicing = ecName + apId + secretkey + mobiles + content + sign + addSerial;
        return EncryptUtil.md5(splicing);
    }
}