package com.pcitc.opal.common;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tomcat.util.http.fileupload.FileUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/*
 * Excel操作实现类
 * 模块编号：pcitc_opal_common_class_ExcelHelper
 * 作    者：jiangtao.xue
 * 创建时间：2017/11/16
 * 修改编号：1
 * 描    述：Excel操作实现类
 */
public class ExcelHelper<T> {

    public static String DEFAULT_DATE_PATTERN = "yyyy年MM月dd日";//默认日期格式
    public static int DEFAULT_COLOUMN_WIDTH = 17;

    //region 导入excel

    private static boolean isExcel2003(String filePath) {
        return filePath.matches("^.+\\.(?i)(xls)$");
    }

    private static boolean isExcel2007(String filePath) {
        return filePath.matches("^.+\\.(?i)(xlsx)$");
    }

    /**
     * 读取Excel文件
     *
     * @param filePath excel路径地址
     * @return
     */
    public static List<String[]> importExcel(String filePath) throws Exception {
        return importExcel(filePath, 0, 0, false);
    }

    /**
     * 读取Excel文件
     *
     * @param inputStream excel路径地址
     * @param nullRowEnd  遇到空行结束读取
     * @return
     */
    public static List<String[]> importExcel(InputStream inputStream, boolean nullRowEnd) throws Exception {
        return importExcel(inputStream, 0, 0, nullRowEnd);
    }

    /**
     * 读取Excel文件
     *
     * @param filePath   excel路径地址
     * @param sheetnum   获取指定的sheet页
     * @param startRow   从指定行数开始读取
     * @param nullRowEnd 遇到空行结束读取
     * @return
     */
    public static List<String[]> importExcel(String filePath, int sheetnum, int startRow, boolean nullRowEnd) throws Exception {
        List<String[]> result = new ArrayList<String[]>();

        InputStream inputStream = null;
        Workbook wb = null;
        try {
            inputStream = new FileInputStream(filePath);
            //2003;
            if (isExcel2003(filePath)) {
                wb = new HSSFWorkbook(inputStream);
                //2007;
            } else if (isExcel2007(filePath)) {
                wb = new XSSFWorkbook(inputStream);
            }
            //获取公式计算器;
            FormulaEvaluator evaluator = wb.getCreationHelper().createFormulaEvaluator();

            //获取指定的sheet;
            Sheet sheet = wb.getSheetAt(sheetnum);
            //行数;
            int rowNum = sheet.getPhysicalNumberOfRows();
            //取前5行中的最大列数;
            int max = 5;
            int colNum = 0;
            for (int i = 0; i < rowNum && i < max; i++) {
                Row r = sheet.getRow(i);
                if (r != null) {
                    colNum = Math.max(r.getPhysicalNumberOfCells(), colNum);
                }
            }
            //循环遍历excel表格;
            boolean isNullRow = false;
            for (int i = startRow; !isNullRow && i < rowNum; i++) {
                int nullRow = 0;
                String[] obj = new String[colNum];
                for (int j = 0; j < colNum; j++) {
                    //获取单元格值;
                    obj[j] = getCellValue(evaluator, sheet, i, j);
                    if (nullRowEnd && obj[j].trim().isEmpty()) {
                        nullRow++;
                    }
                    if (nullRowEnd && nullRow >= colNum) {
                        isNullRow = true;
                    }
                }
                if (!isNullRow) {
                    result.add(obj);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            inputStream.close();
        }
        return result;
    }

    /**
     * 读取Excel文件
     *
     * @param inputStream excel路径地址
     * @param sheetnum    获取指定的sheet页
     * @param startRow    从指定行数开始读取
     * @param nullRowEnd  遇到空行结束读取
     * @return
     */
    public static List<String[]> importExcel(InputStream inputStream, int sheetnum, int startRow, boolean nullRowEnd) throws Exception {
        List<String[]> result = new ArrayList<String[]>();

        Workbook wb = null;
        try {
            wb = WorkbookFactory.create(inputStream);
            //获取公式计算器;
            FormulaEvaluator evaluator = wb.getCreationHelper().createFormulaEvaluator();

            //获取指定的sheet;
            Sheet sheet = wb.getSheetAt(sheetnum);
            //行数;
            int rowNum = sheet.getPhysicalNumberOfRows();
            //取前5行中的最大列数;
            int max = 5;
            int colNum = 0;
            for (int i = 0; i < rowNum && i < max; i++) {
                Row r = sheet.getRow(i);
                if (r != null) {
                    colNum = Math.max(r.getPhysicalNumberOfCells(), colNum);
                }
            }
            //循环遍历excel表格;
            boolean isNullRow = false;
            for (int i = startRow; !isNullRow && i < rowNum; i++) {
                int nullRow = 0;
                String[] obj = new String[colNum];
                for (int j = 0; j < colNum; j++) {
                    //获取单元格值;
                    obj[j] = getCellValue(evaluator, sheet, i, j);
                    if (nullRowEnd && obj[j].trim().isEmpty()) {
                        nullRow++;
                    }
                    if (nullRowEnd && nullRow >= colNum) {
                        isNullRow = true;
                    }
                }
                if (!isNullRow) {
                    result.add(obj);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            inputStream.close();
        }
        return result;
    }

    /**
     * 读取Excel文件
     *
     * @param inputStream excel路径地址
     * @param startRow    从指定行数开始读取
     * @param nullRowEnd  遇到空行结束读取
     * @return
     */
    public static List<List<String[]>> importAllSheetExcel(InputStream inputStream, int startRow, boolean nullRowEnd) throws Exception {
        List<List<String[]>> result = new ArrayList<>();
        Workbook wb = null;
        try {
            wb = WorkbookFactory.create(inputStream);
            for (int sheetNum = 0; sheetNum < wb.getNumberOfSheets(); sheetNum++) {
                List<String[]> sheetResult = new ArrayList<>();
                //获取公式计算器;
                FormulaEvaluator evaluator = wb.getCreationHelper().createFormulaEvaluator();

                //获取指定的sheet;
                Sheet sheet = wb.getSheetAt(sheetNum);
                //行数;
                int rowNum = sheet.getPhysicalNumberOfRows();
                //取前5行中的最大列数;
                int max = 5;
                int colNum = 0;
                for (int i = 0; i < rowNum && i < max; i++) {
                    Row r = sheet.getRow(i);
                    if (r != null) {
                        colNum = Math.max(r.getPhysicalNumberOfCells(), colNum);
                    }
                }
                //循环遍历excel表格;
                boolean isNullRow = false;
                for (int i = startRow; !isNullRow && i < rowNum; i++) {
                    int nullRow = 0;
                    String[] obj = new String[colNum];
                    for (int j = 0; j < colNum; j++) {
                        //获取单元格值;
                        obj[j] = getCellValue(evaluator, sheet, i, j);
                        if (nullRowEnd && obj[j].trim().isEmpty()) {
                            nullRow++;
                        }
                        if (nullRowEnd && nullRow >= colNum) {
                            isNullRow = true;
                        }
                    }
                    if (!isNullRow) {
                        sheetResult.add(obj);
                    }
                }
                result.add(sheetResult);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            inputStream.close();
        }
        return result;
    }

    /**
     * 获取指定的行列的cell;当行列在合并区域内时,返回该合并去区域的第一个cell;
     *
     * @param sheet
     * @param row
     * @param column
     * @return
     */
    public static Cell getCell(Sheet sheet, int row, int column) {
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress ca = sheet.getMergedRegion(i);
            int firstColumn = ca.getFirstColumn();
            int lastColumn = ca.getLastColumn();
            int firstRow = ca.getFirstRow();
            int lastRow = ca.getLastRow();
            if (row >= firstRow && row <= lastRow) {
                if (column >= firstColumn && column <= lastColumn) {
                    Row fRow = sheet.getRow(firstRow);
                    Cell fCell = fRow.getCell(firstColumn);
                    return fCell;
                }
            }
        }

        Cell cell = sheet.getRow(row) == null ? null : sheet.getRow(row).getCell(column);
        return cell;
    }

    /**
     * 获取包含合并区域内的cell的值;
     *
     * @param evaluator, 没有可以为null;
     * @param sheet
     * @param row
     * @param column
     * @return
     */
    public static String getCellValue(FormulaEvaluator evaluator, Sheet sheet, int row, int column) throws Exception {
        Cell cell = getCell(sheet, row, column);
        try {
            return getCellValue(cell, evaluator);
        } catch (Exception ex) {
            throw new Exception("第" + row + "行，第" + column + "列的数据非法，请检查！");
        }
    }

    /**
     * 将给定的值设置给该cell;
     *
     * @param sheet
     * @param row
     * @param column
     * @param value
     */
    public static void setCellValue(Sheet sheet, int row, int column, String value) {
        Cell cell = getCell(sheet, row, column);
        cell.setCellValue(StringUtils.trimToEmpty(value));
    }

    /**
     * 获取指定cell的值;
     *
     * @param cell
     * @param evaluator
     * @return
     */
    public static String getCellValue(Cell cell, FormulaEvaluator evaluator) {
        if (cell == null) {
            return "";
        }
        CellType cellType = cell.getCellType();

        //当cell表明是公式时,单独处理;
        if (CellType.FORMULA == cellType
                && evaluator != null) {
            CellValue cv = evaluator.evaluate(cell);
            return getCellValue(cv);
        } else {
            return getCellValue(cell);
        }
    }

    /**
     * 获取值;
     *
     * @param obj
     * @return
     */
    public static String getCellValue(Object obj) {

        String cellValue = null;

        CellValue cv = null;
        Cell cell = null;

        if (obj instanceof CellValue) {
            cv = (CellValue) obj;
        } else if (obj instanceof Cell) {
            cell = (Cell) obj;
        } else {
            return cellValue;
        }
        CellType cellType = (cv != null) ? cv.getCellType() : cell.getCellType();
        if (cellType == CellType.STRING) {
            cellValue = (cv != null) ? cv.getStringValue() : cell.getStringCellValue();
        } else if (cellType == CellType.BOOLEAN) {
            cellValue = (cv != null) ? String.valueOf(cv.getBooleanValue()) : String.valueOf(cell.getBooleanCellValue());
        } else if (cellType == CellType.BLANK) {
            cellValue = (cv != null) ? cv.getStringValue() : cell.getStringCellValue();
        } else if (cellType == CellType.FORMULA) {
            cellValue = cell.getCellFormula();
        } else if (cellType == CellType.ERROR) {
            cellValue = "error";
        } else if (cellType == CellType.NUMERIC) {
            //日期型
            if (cell != null && org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                double value = cell.getNumericCellValue();
                Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
                cellValue = DateHelper.format(date, DateHelper.DATE_FMT_1);
            }
            //数字型
            else {
                //这种方法对于自动加".0"的数字可直接解决
                NumberFormat nf = NumberFormat.getInstance();
                nf.setMaximumFractionDigits(10);
                cellValue = (cv != null) ? String.valueOf(cv.getNumberValue()) : String.valueOf(nf.format(cell.getNumericCellValue()));
                //但如果是科学计数法的数字就转换成了带逗号的，例如：12345678912345的科学计数法是1.23457E+13，经过这个格式化后就变成了字符串“12,345,678,912,345”，这也并不是想要的结果，所以要将逗号去掉
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
            }


        } else {
            cellValue = null;
        }
        return cellValue.trim();
    }

    //endregion

    //region 导出excel

    /**
     * 生成excel下载;
     *
     * @param filePath
     * @param response
     * @throws Exception
     */
    public void exportExcel(String filePath, HttpServletResponse response) throws Exception {
        exportExcel(filePath, null, response, null);
    }

    /**
     * 生成指定的文件名称的excel下载;
     *
     * @param filePath
     * @param response
     * @param fileName
     * @throws Exception
     */
    public void exportExcel(String filePath, HttpServletRequest request, HttpServletResponse response, String fileName) throws Exception {
        BufferedInputStream bis = null;
        OutputStream out = null;
        File file = null;
        try {
            file = new File(filePath);
            if (StringUtils.isBlank(fileName)) {
                fileName = file.getName();
            }
            String rtn = "filename=\"" + new String(fileName.getBytes("utf-8"), "iso-8859-1") + "\"";
            if (request == null) {
            } else {
                String userAgent = request.getHeader("User-Agent");
                if (userAgent != null) {
                    String new_filename = URLEncoder.encode(fileName, "utf-8");
                    userAgent = userAgent.toLowerCase();
                    // IE浏览器，只能采用URLEncoder编码
                    if (userAgent.indexOf("msie") != -1) {
                        rtn = "filename=\"" + new_filename + "\"";
                    }
                    // Opera浏览器只能采用filename*
                    else if (userAgent.indexOf("opera") != -1) {
                        rtn = "filename*=UTF-8''" + new_filename;
                    }
                    // Safari浏览器，只能采用ISO编码的中文输出
                    else if (userAgent.indexOf("safari") != -1) {
                    }
                    // Chrome浏览器，只能采用MimeUtility编码或ISO编码的中文输出
                    else if (userAgent.indexOf("applewebkit") != -1) {
                        rtn = "filename=\"" + new_filename + "\"";
                    }
                    // FireFox浏览器，可以使用MimeUtility或filename*或ISO编码的中文输出
                    else if (userAgent.indexOf("mozilla") != -1) {
                        rtn = "filename*=UTF-8''" + new_filename;
                    }
                }
            }

            bis = new BufferedInputStream(new FileInputStream(file));
            byte[] buf = new byte[1024];
            int len = 0;

            response.reset();
            response.setContentType("application/x-msdownload");
            response.setHeader("Content-Disposition", "attachment; " + rtn);
            out = response.getOutputStream();
            while ((len = bis.read(buf)) > 0) {
                out.write(buf, 0, len);
            }
            out.flush();
        } finally {
            bis.close();
            out.close();

            if (file != null) {
                try {
                    FileUtils.forceDelete(file);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void exportExcel(Collection<T> dataset, String filePath) throws FileNotFoundException {
        exportExcel("Sheet1", null, null, dataset, filePath, "yyyy-MM-dd");
    }

    public void exportExcel(String[] headers, Collection<T> dataset, String filePath) throws FileNotFoundException {
        exportExcel("Sheet1", headers, null, dataset, filePath, "yyyy-MM-dd");
    }

    public void exportExcel(String[] headers, Collection<T> dataset, String filePath, String pattern) throws FileNotFoundException {
        exportExcel("Sheet1", headers, null, dataset, filePath, pattern);
    }

    public HSSFWorkbook exportExcel(String[] headers, String[] fields, Collection<T> dataset, String pattern) throws FileNotFoundException {
        return exportExcel("Sheet1", headers, fields, dataset, pattern);
    }

    public void exportExcel(String[] headers, String[] fields, Collection<T> dataset, String filePath, String pattern) throws FileNotFoundException {
        exportExcel("Sheet1", headers, null, dataset, filePath, pattern);
    }

    /**
     * 这是一个通用的方法，利用了JAVA的反射机制，可以将放置在JAVA集合中并且符号一定条件的数据以EXCEL 的形式输出到指定IO设备上
     *
     * @param title    表格标题名
     * @param headers  表格属性列名数组
     * @param dataset  需要显示的数据集合,集合中一定要放置符合javabean风格的类的对象。此方法支持的
     *                 javabean属性的数据类型有基本数据类型及String,Date,byte[](图片数据)
     * @param filePath excel文件路径，可以将EXCEL文档导出到本地文件或者网络中
     * @param pattern  如果有时间数据，设定输出格式。默认为"yyy-MM-dd"
     */
    public void exportExcel(String title, String[] headers, String[] fields, Collection<T> dataset,
                            String filePath, String pattern) throws FileNotFoundException {
        FileOutputStream out = new FileOutputStream(filePath);
        HSSFWorkbook workbook = exportExcel(title, headers, fields, dataset, pattern);
        try {
            workbook.write(out);
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * 这是一个通用的方法，利用了JAVA的反射机制，可以将放置在JAVA集合中并且符号一定条件的数据以EXCEL 的形式输出到指定IO设备上
     * 导出Excel <=2003 数据限制，行(65536)列(256)
     *
     * @param title   表格标题名
     * @param headers 表格属性列名数组
     * @param dataset 需要显示的数据集合,集合中一定要放置符合javabean风格的类的对象。此方法支持的
     *                javabean属性的数据类型有基本数据类型及String,Date,byte[](图片数据)
     * @param fields  excel文件路径，可以将EXCEL文档导出到本地文件或者网络中
     * @param pattern 如果有时间数据，设定输出格式。默认为"yyy-MM-dd"
     */
    @SuppressWarnings({"deprecation"})
    public HSSFWorkbook exportExcel(String title, String[] headers, String[] fields,
                                    Collection<T> dataset, String pattern) throws FileNotFoundException {

        // 声明一个工作薄
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 生成一个表格
        HSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 15);
        // 生成一个样式
        HSSFCellStyle style = workbook.createCellStyle();
        // 设置标题样式
        style.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        // 生成一个字体
        HSSFFont font = workbook.createFont();
        font.setColor(IndexedColors.VIOLET.getIndex());
        font.setFontHeightInPoints((short) 12);
//        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 把字体应用到当前的样式
        style.setFont(font);
        // 生成并设置内容样式
        HSSFCellStyle style2 = workbook.createCellStyle();
        // style2.setFillForegroundColor(HSSFColor.LIGHT_YELLOW.index);//设置背景颜色
        //style2.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);//solid 填充  foreground  前景色
        style2.setBorderBottom(BorderStyle.THIN);
        style2.setBorderLeft(BorderStyle.THIN);
        style2.setBorderRight(BorderStyle.THIN);
        style2.setBorderTop(BorderStyle.THIN);
        style2.setAlignment(HorizontalAlignment.CENTER);
        style2.setVerticalAlignment(VerticalAlignment.CENTER);
        // 生成另一个字体
        HSSFFont font2 = workbook.createFont();
//        font2.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
        // 把字体应用到当前的样式
        style2.setFont(font2);

        //HSSFFont font3 = workbook.createFont();

        // 声明一个画图的顶级管理器
        HSSFPatriarch patriarch = sheet.createDrawingPatriarch();
        // 定义注释的大小和位置,详见文档
        //HSSFComment comment = patriarch.createComment(new HSSFClientAnchor(0,
        //0, 0, 0, (short) 4, 2, (short) 6, 5));
        // 设置注释内容
        //comment.setString(new HSSFRichTextString("可以在POI中添加注释！"));
        // 设置注释作者，当鼠标移动到单元格上是可以在状态栏中看到该内容.
        //comment.setAuthor("leno");

        // 产生表格标题行
        HSSFRow row = sheet.createRow(0);
        for (short i = 0; i < headers.length; i++) {
            HSSFCell cell = row.createCell(i);
            cell.setCellStyle(style);
            HSSFRichTextString text = new HSSFRichTextString(headers[i]);
            cell.setCellValue(text);
        }

        // 遍历集合数据，产生数据行
        Iterator<T> it = dataset.iterator();
        int index = 0;
        boolean isContinu = true;
        while (it.hasNext() && isContinu) {
            index++;
            row = sheet.createRow(index);
            T t = (T) it.next();
            if (fields == null || fields.length == 0) {
                // 利用反射，根据javabean属性的先后顺序，动态调用getXxx()方法得到属性值
                Field[] fields2 = t.getClass().getDeclaredFields();
                fields = Arrays.stream(fields2).map(x -> x.getName()).toArray(String[]::new);
            }

            for (short i = 0; i < fields.length; i++) {
                HSSFCell cell = row.createCell(i);
                cell.setCellStyle(style2);
                //Field field = fields[i];
                //String fieldName = field.getName();
                String fieldName = fields[i];
                String getMethodName = "get"
                        + fieldName.substring(0, 1).toUpperCase()
                        + fieldName.substring(1);
                try {
                    Class<? extends Object> tCls = t.getClass();
                    Method getMethod = tCls.getMethod(getMethodName, new Class[]{});
                    Object value = getMethod.invoke(t, new Object[]{});
                    // 判断值的类型后进行强制类型转换
                    String textValue = null;
                    if (value instanceof Date) {
                        Date date = (Date) value;
                        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                        textValue = sdf.format(date);
                    } else if (value instanceof byte[]) {
                        // 有图片时，设置行高为60px;
                        row.setHeightInPoints(60);
                        // 设置图片所在列宽度为80px,注意这里单位的一个换算
                        sheet.setColumnWidth(i, (short) (35.7 * 80));
                        // sheet.autoSizeColumn(i);
                        byte[] bsValue = (byte[]) value;
                        HSSFClientAnchor anchor = new HSSFClientAnchor(0, 0,
                                1023, 255, (short) 6, index, (short) 6, index);
                        anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_DONT_RESIZE);
                        patriarch.createPicture(anchor, workbook.addPicture(
                                bsValue, HSSFWorkbook.PICTURE_TYPE_JPEG));
                    } else {
                        // 其它数据类型都当作字符串简单处理
                        textValue = value == null ? null : value.toString().endsWith(".0") ?
                                value.toString().replace(".0", "") : value.toString();
                    }
                    // 如果不是图片数据，就利用正则表达式判断textValue是否全部由数字组成
                    if (textValue != null) {
                        Pattern p = Pattern.compile("^//d+(//.//d+)?$");
                        Matcher matcher = p.matcher(textValue);
                        if (matcher.matches()) {
                            // 是数字当作double处理
                            cell.setCellValue(Double.parseDouble(textValue));
                        } else {
                            HSSFRichTextString richString = new HSSFRichTextString(textValue);
                            //font3.setColor(HSSFColor.BLUE.index);
                            //richString.applyFont(font3);
                            cell.setCellValue(richString);
                        }
                    }
                } catch (SecurityException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                } catch (NoSuchMethodException e) {
                    // TODO Auto-generated catch block
                    isContinu = false;
                    e.printStackTrace();
                } catch (IllegalArgumentException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                } finally {
                    // 清理资源
                }
            }
        }
        return workbook;
    }

    /**
     * Excel =2007 数据限制，行(1048576)列(16384)
     *
     * @param headers     属性-列头
     * @param fields      字段
     * @param dataset     导出数据集合
     * @param datePattern 日期格式，传null值则默认 年月日
     */
    public SXSSFWorkbook exportExcelX(String[] headers, String[] fields, Collection<T> dataset, String datePattern) {
        return exportExcelX("", headers, fields, dataset, datePattern, DEFAULT_COLOUMN_WIDTH);
    }

    /**
     * Excel =2007 数据限制，行(1048576)列(16384)
     *
     * @param x1          工厂名称
     * @param x2          车间名称
     * @param headers     属性-列头
     * @param fields      字段
     * @param dataset     导出数据集合
     * @param datePattern 日期格式，传null值则默认 年月日
     */
    public SXSSFWorkbook exportExcelXForZip(String x1, String x2, String[] headers, String[] fields, Object dataset, String datePattern) throws Exception {
        return exportExcelXForZip(x1, x2, headers, fields, dataset, datePattern, DEFAULT_COLOUMN_WIDTH);
    }

    /**
     * Excel =2007 数据限制，行(1048576)列(16384)
     *
     * @param title       标题行
     * @param headers     属性-列头
     * @param fields      字段
     * @param dataset     导出数据集合
     * @param datePattern 日期格式，传null值则默认 年月日
     */
    public SXSSFWorkbook exportExcelX(String title, String[] headers, String[] fields, Collection<T> dataset, String datePattern) {
        return exportExcelX(title, headers, fields, dataset, datePattern, DEFAULT_COLOUMN_WIDTH);
    }

    /**
     * Excel =2007 数据限制，行(1048576)列(16384)
     *
     * @param headers     属性-列头
     * @param fields      字段
     * @param dataset     导出数据集合
     * @param datePattern 日期格式，传null值则默认 年月日
     * @param colWidth    列宽 默认 至少17个字节
     */
    public SXSSFWorkbook exportExcelX(String[] headers, String[] fields, Collection<T> dataset, String datePattern, int colWidth) {
        return exportExcelX("", headers, fields, dataset, datePattern, colWidth);
    }

    /**
     * Excel =2007 数据限制，行(1048576)列(16384)
     *
     * @param title       标题行
     * @param headers     属性-列头
     * @param fields      字段
     * @param dataset     导出数据集合
     * @param datePattern 日期格式，传null值则默认 年月日
     * @param colWidth    列宽 默认 至少17个字节
     */
    public SXSSFWorkbook exportExcelX(String title, String[] headers, String[] fields, Collection<T> dataset,
                                      String datePattern, int colWidth) {
        if (datePattern == null) datePattern = DEFAULT_DATE_PATTERN;
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook(1000);//缓存
        workbook.setCompressTempFiles(true);
        //表头样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        Font titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 20);
//        titleFont.setBoldweight((short) 700);
        titleStyle.setFont(titleFont);
        // 列头样式
        CellStyle headerStyle = workbook.createCellStyle();
        //headerStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        Font headerFont = workbook.createFont();
        headerFont.setFontHeightInPoints((short) 12);
//        headerFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        headerStyle.setFont(headerFont);
        // 单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        //cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font cellFont = workbook.createFont();
//        cellFont.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
        cellStyle.setFont(cellFont);
        // 生成一个(带标题)表格
        SXSSFSheet sheet = (SXSSFSheet) workbook.createSheet();
        //设置列宽
        int minBytes = colWidth < DEFAULT_COLOUMN_WIDTH ? DEFAULT_COLOUMN_WIDTH : colWidth;//至少字节数
        int[] arrColWidth = new int[headers.length];
        // 产生表格标题行,以及设置列宽
        String[] properties = new String[headers.length];
        //String[] headers = new String[headers.length];

        // 遍历集合数据，产生数据行
        int rowIndex = 0;
        //先把列头输出到excel
        SXSSFRow headerRow = (SXSSFRow) sheet.createRow(rowIndex); //列头 rowIndex =1
        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
            headerRow.getCell(i).setCellStyle(headerStyle);
        }

        for (Object obj : dataset) {
            if (rowIndex == 65535 || rowIndex == 0) {

                if (rowIndex != 0) {
                    sheet = (SXSSFSheet) workbook.createSheet();//如果数据超过了，则在第二页显示
                    rowIndex = 0;

                    //新sheet页创建列头
                    SXSSFRow headerRow2 = (SXSSFRow) sheet.createRow(rowIndex); //列头 rowIndex =1
                    for (int i = 0; i < headers.length; i++) {
                        headerRow2.createCell(i).setCellValue(headers[i]);
                        headerRow2.getCell(i).setCellStyle(headerStyle);
                    }
                }
                if (!StringUtils.isEmpty(title)) {
                    SXSSFRow titleRow = (SXSSFRow) sheet.createRow(0);//表头 rowIndex=0
                    titleRow.createCell(0).setCellValue(title);
                    titleRow.getCell(0).setCellStyle(titleStyle);
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headers.length - 1));
                    rowIndex = 1;//数据内容从 rowIndex=1开始
                }
                int ii = 0;
                for (String fieldName : fields) {
                    properties[ii] = fieldName;
                    int bytes = fieldName.getBytes().length;
                    arrColWidth[ii] = bytes < minBytes ? minBytes : bytes;
                    sheet.setColumnWidth(ii, arrColWidth[ii] * 256);
                    ii++;
                }
                rowIndex++;
            }
            JSONObject jo = (JSONObject) JSONObject.toJSON(obj);
            SXSSFRow dataRow = (SXSSFRow) sheet.createRow(rowIndex);
            for (int i = 0; i < properties.length; i++) {
                SXSSFCell newCell = (SXSSFCell) dataRow.createCell(i);

                Object o = jo.get(properties[i]);
                String cellValue = "";
                if (o == null) cellValue = "";
                else if (o instanceof Date) cellValue = new SimpleDateFormat(datePattern).format(o);
                else if (o instanceof Float || o instanceof Double) {
                    cellValue = new BigDecimal(o.toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                    if (cellValue.lastIndexOf(".00") > 0) {
                        cellValue = cellValue.replace(".00", "");
                    }
                } else cellValue = o.toString();

                newCell.setCellValue(cellValue);
                newCell.setCellStyle(cellStyle);
            }
            rowIndex++;
        }
        // 自动调整宽度
        /*for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }*/
        //try {
        //    workbook.write(out);
        //   // workbook.close();
        //    workbook.dispose();
        //} catch (IOException e) {
        //    e.printStackTrace();
        //}
        return workbook;
    }


    /**
     * Excel =2007 数据限制，行(1048576)列(16384)
     *
     * @param x1          工厂名称
     * @param x2          车间名称
     * @param headers     属性-列头
     * @param fields      字段
     * @param dataset     导出数据集合
     * @param datePattern 日期格式，传null值则默认 年月日
     * @param colWidth    列宽 默认 至少17个字节
     */
    public SXSSFWorkbook exportExcelXForZip(String x1, String x2, String[] headers, String[] fields, Object dataset,
                                            String datePattern, int colWidth) throws Exception {

        if (datePattern == null) datePattern = DEFAULT_DATE_PATTERN;
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook(1000);//缓存
        workbook.setCompressTempFiles(true);

        //region 单元格样式

        // 单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        //cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setWrapText(true);

        //格式化单元格类型
        DataFormat format = workbook.createDataFormat();
        cellStyle.setDataFormat(format.getFormat("@"));

        //设置字体
        Font cellFont = workbook.createFont();
        cellFont.setFontName("宋体");
        cellFont.setFontHeightInPoints((short) 11);//设置字体大小
        cellStyle.setFont(cellFont);


        Font font_song = workbook.createFont();
        font_song.setFontName("宋体");
        font_song.setFontHeightInPoints((short) 11);//设置字体大小
//        font_song.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
        //单元格样式垂直、水平居中
        CellStyle style_Center = workbook.createCellStyle();// 样式对象
        style_Center.setBorderBottom(BorderStyle.THIN);
        style_Center.setBorderLeft(BorderStyle.THIN);
        style_Center.setBorderRight(BorderStyle.THIN);
        style_Center.setBorderTop(BorderStyle.THIN);
        style_Center.setAlignment(HorizontalAlignment.CENTER);
        style_Center.setVerticalAlignment(VerticalAlignment.CENTER);
        style_Center.setFont(font_song);


        Font font_black = workbook.createFont();
        font_black.setFontName("黑体");
        font_black.setFontHeightInPoints((short) 12);//设置字体大小

        //单元格样式垂直、水平居中
        CellStyle style_Center_black = workbook.createCellStyle();// 样式对象
        style_Center_black.setBorderBottom(BorderStyle.THIN);
        style_Center_black.setBorderLeft(BorderStyle.THIN);
        style_Center_black.setBorderRight(BorderStyle.THIN);
        style_Center_black.setBorderTop(BorderStyle.THIN);
        style_Center_black.setAlignment(HorizontalAlignment.CENTER);
        style_Center_black.setVerticalAlignment(VerticalAlignment.CENTER);
        style_Center_black.setFont(font_black);

        //单元格样式垂直居左、水平居中
        CellStyle cellStyleLeft = workbook.createCellStyle();// 样式对象
        cellStyleLeft.setVerticalAlignment(VerticalAlignment.BOTTOM);// 垂直居左

        //endregion 单元格样式

        Map<String, Object> mapList = (Map<String, Object>) dataset;

        // 产生表格标题行,以及设置列宽
        String[] properties = new String[headers.length];
        //设置列宽
        int minBytes = colWidth < DEFAULT_COLOUMN_WIDTH ? DEFAULT_COLOUMN_WIDTH : colWidth;//至少字节数
        int[] arrColWidth = new int[headers.length];

        for (String key : mapList.keySet()) {

            // 生成一个(带标题)表格
            SXSSFSheet sheet = null;

            // 遍历集合数据，产生数据行
            int rowIndex = 4;
            List values = (List) mapList.get(key);
            for (Object obj : values) {
                if (rowIndex == 65535 || rowIndex == 4) {

                    sheet = workbook.createSheet(key);//第二页显示

                    //新sheet页创建列头

                    //region 创建单元格

                    //region 插入图片

                    ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
                    Resource resource = new ClassPathResource("/sinopec.png");
                    BufferedImage bufferImg = ImageIO.read(resource.getFile());
                    ImageIO.write(bufferImg, "png", byteArrayOut);
                    int pictureIdx = workbook.addPicture(byteArrayOut.toByteArray(), Workbook.PICTURE_TYPE_PNG);

                    //画图的顶级管理器，一个sheet只能获取一个（一定要注意这点）
                    Drawing drawing = sheet.createDrawingPatriarch();
                    int dx1 = 100;
                    int dy1 = 30;
                    int dx2 = 1023;
                    int dy2 = 255;
                    int row22 = 2;
                    int col2 = 4;
                    //anchor主要用于设置图片的属性
                    //设置锚点 （在起始单元格的X坐标0-1023，Y的坐标0-255，在终止单元格的X坐标0-1023，Y的坐标0-255，起始单元格列数，行数，终止单元格列数，行数）
                    //XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 54, 55, (short) 1, 1, (short) 2, 4);
                    //anchor.setAnchorType(3);
                    XSSFClientAnchor anchor = new XSSFClientAnchor();
                    anchor.setDx1(dx1);
                    anchor.setDy1(dy1);
                    anchor.setDx2(dx2);
                    anchor.setDy2(dy2);
                    anchor.setRow1(1);//开始插入起始行
                    anchor.setCol1(1);//开始插入起始列
                    //anchor.setRow2(row22);//开始插入起始行
                    //anchor.setCol2(col2);//开始插入起始列
                    //插入图片
                    Picture picture = drawing.createPicture(anchor, pictureIdx);
                    picture.resize();

                    //endregion

                    SXSSFRow row1 = (SXSSFRow) sheet.createRow(1);
                    Cell c1_1 = row1.createCell(1);
                    c1_1.setCellStyle(style_Center); // 样式
                    Cell c1_2 = row1.createCell(2);
                    c1_2.setCellValue(new HSSFRichTextString(x1 + "管理体系"));
                    c1_2.setCellStyle(style_Center); // 样式


                    SXSSFRow row2 = (SXSSFRow) sheet.createRow(2);
                    Cell c2_2 = row2.createCell(2);
                    c2_2.setCellValue(new HSSFRichTextString(x2 + "工况报警值清单"));
                    c2_2.setCellStyle(style_Center_black); // 样式


                    SXSSFRow row3 = (SXSSFRow) sheet.createRow(3);
                    Cell c3_2 = row3.createCell(2);
                    c3_2.setCellValue(new HSSFRichTextString("记录编号"));
                    c3_2.setCellStyle(style_Center); // 样式
                    Cell c3_3 = row3.createCell(4);
                    c3_3.setCellValue(new HSSFRichTextString());
                    c3_3.setCellStyle(style_Center); // 样式
                    Cell c3_6 = row3.createCell(6);
                    c3_6.setCellValue(new HSSFRichTextString("使用单位"));
                    c3_6.setCellStyle(style_Center); // 样式
                    Cell c3_8 = row3.createCell(7);
                    c3_8.setCellValue(new HSSFRichTextString(key + "装置"));
                    c3_8.setCellStyle(cellStyleLeft); // 样式

                    //合并单元格
                    CellRangeAddress region1 = new CellRangeAddress(1, 3, 1, 1);
                    CellRangeAddress region2 = new CellRangeAddress(1, 1, 2, 9);
                    CellRangeAddress region3 = new CellRangeAddress(2, 2, 2, 9);
                    CellRangeAddress region4 = new CellRangeAddress(3, 3, 3, 5);
                    CellRangeAddress region5 = new CellRangeAddress(3, 3, 7, 9);

                    sheet.addMergedRegion(region1);
                    sheet.addMergedRegion(region2);
                    sheet.addMergedRegion(region3);
                    sheet.addMergedRegion(region4);
                    sheet.addMergedRegion(region5);

                    setRegionBorder(BorderStyle.THIN, region1, sheet);
                    setRegionBorder(BorderStyle.THIN, region2, sheet);
                    setRegionBorder(BorderStyle.THIN, region3, sheet);
                    setRegionBorder(BorderStyle.THIN, region4, sheet);
                    setRegionBorder(BorderStyle.THIN, region5, sheet);

                    //endregion


                    //先把列头输出到excel
                    SXSSFRow headerRow = (SXSSFRow) sheet.createRow(rowIndex); //列头 rowIndex =1
                    for (int i = 0; i < headers.length; i++) {
                        headerRow.createCell(i + 1).setCellValue(headers[i]);
                        headerRow.getCell(i + 1).setCellStyle(style_Center);
                    }

                    int ii = 0;
                    for (String fieldName : fields) {
                        properties[ii] = fieldName;
                        int bytes = fieldName.getBytes().length;
                        arrColWidth[ii] = bytes < minBytes ? minBytes : bytes;
                        sheet.setColumnWidth(ii, arrColWidth[ii] * 256);
                        ii++;
                    }

                    rowIndex++;
                }
                JSONObject jo = (JSONObject) JSONObject.toJSON(obj);
                SXSSFRow dataRow = (SXSSFRow) sheet.createRow(rowIndex);
                for (int n = 0; n < properties.length; n++) {
                    SXSSFCell newCell = (SXSSFCell) dataRow.createCell(n + 1);

                    Object o = jo.get(properties[n]);
                    String cellValue = "";
                    if (o == null) cellValue = "";
                    else if (o instanceof Date) cellValue = new SimpleDateFormat(datePattern).format(o);
                    else if (o instanceof Float || o instanceof Double) {
                        cellValue = new BigDecimal(o.toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                        if (cellValue.lastIndexOf(".00") > 0) {
                            cellValue = cellValue.replace(".00", "");
                        }
                    } else cellValue = o.toString();

                    newCell.setCellValue(cellValue);
                    newCell.setCellStyle(cellStyle);
                }
                rowIndex++;
            }
        }
        // 自动调整宽度
        /*for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }*/
        //try {
        //    workbook.write(out);
        //   // workbook.close();
        //    workbook.dispose();
        //} catch (IOException e) {
        //    e.printStackTrace();
        //}
        return workbook;
    }

    private static void setRegionBorder(BorderStyle borderStyle, CellRangeAddress region, SXSSFSheet sheet) {
        RegionUtil.setBorderBottom(borderStyle, region, sheet);
        RegionUtil.setBorderLeft(borderStyle, region, sheet);
        RegionUtil.setBorderRight(borderStyle, region, sheet);
        RegionUtil.setBorderTop(borderStyle, region, sheet);
    }
    public static Map<String, Object> objectToMap(Object obj) throws Exception {
        if (obj == null) {
            return null;
        }
        //获取关联的所有类，本类以及所有父类
        boolean ret = true;
        Class oo = obj.getClass();
        List<Class> clazzs = new ArrayList<Class>();
        while (ret) {
            clazzs.add(oo);
            oo = oo.getSuperclass();
            if (oo == null || oo == Object.class) break;
        }

        Map<String, Object> map = new HashMap<String, Object>();

        for (int i = 0; i < clazzs.size(); i++) {
            Field[] declaredFields = clazzs.get(i).getDeclaredFields();
            for (Field field : declaredFields) {
                int mod = field.getModifiers();
                //过滤 static 和 final 类型
                if (Modifier.isStatic(mod) || Modifier.isFinal(mod)) {
                    continue;
                }
                field.setAccessible(true);
                map.put(field.getName(), field.get(obj));
            }
        }

        return map;
        //endregion
    }


    public static Map<String, Object> objectToMap2(Object obj) throws Exception {
        if (obj == null) {
            return null;
        }

        Map<String, Object> map = new HashMap<String, Object>();

        Field[] declaredFields = obj.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            map.put(field.getName(), field.get(obj));
        }

        return map;
    }

}

