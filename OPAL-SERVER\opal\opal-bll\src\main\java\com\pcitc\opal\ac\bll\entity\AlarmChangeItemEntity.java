package com.pcitc.opal.ac.bll.entity;

import com.pcitc.opal.ac.pojo.ChangeItemType;

/*
 * 报警变更事项实体
 * 模块编号：pcitc_opal_bll_class_AlarmChangeItem
 * 作    者：xuelei.wang
 * 创建时间：2018-01-19
 * 修改编号：1
 * 描    述：报警变更事项实体
 */
public class AlarmChangeItemEntity{

    /**
     * 报警变更事项ID
     */
    private Long itemId;

    /**
     * 报警变更方案ID
     */
    private Long planId;

    /**
     * 报警变更方案明细ID
     */
    private Long planDetailId;

    /**
     * 变更事项分类ID
     */
    private Long changeItemTypeId;

    /**
     * 变更前内容
     */
    private String beforeContent;

    /**
     * 变更后内容
     */
    private String afterContent;
    /**
     * 变更事项分类名称
     */
    private String changeItemTypeName;

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getPlanDetailId() {
        return planDetailId;
    }

    public void setPlanDetailId(Long planDetailId) {
        this.planDetailId = planDetailId;
    }

    public Long getChangeItemTypeId() {
        return changeItemTypeId;
    }

    public void setChangeItemTypeId(Long changeItemTypeId) {
        this.changeItemTypeId = changeItemTypeId;
    }

    public String getBeforeContent() {
        return beforeContent;
    }

    public void setBeforeContent(String beforeContent) {
        this.beforeContent = beforeContent;
    }

    public String getAfterContent() {
        return afterContent;
    }

    public void setAfterContent(String afterContent) {
        this.afterContent = afterContent;
    }

    public String getChangeItemTypeName() {
        return changeItemTypeName;
    }

    public void setChangeItemTypeName(String changeItemTypeName) {
        this.changeItemTypeName = changeItemTypeName;
    }
}

