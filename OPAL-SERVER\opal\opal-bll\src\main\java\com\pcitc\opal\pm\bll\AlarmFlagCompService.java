package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.AlarmFlagCompEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;

/*
 * 报警标识对照业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_AlarmFlagCompService
 * 作	者：jiangtao.xue
 * 创建时间：2018/03/30
 * 修改编号：1
 * 描	述：报警标识对照业务逻辑层接口 
 */
@Service
public interface AlarmFlagCompService {

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompEntity 报警标识对照实体
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult addAlarmFlagComp(AlarmFlagCompEntity alarmFlagCompEntity) throws Exception;
	
	/**
	 * 删除报警标识对照维护数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompIds 报警标识对照维护主键Id集合
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult deleteAlarmFlagComp(Long[] alarmFlagCompIds) throws Exception;
	
	/**
	 * 报警标识对照更新数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompEntity 报警标识对照实体
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult updateAlarmFlagComp(AlarmFlagCompEntity alarmFlagCompEntity) throws Exception;
	
	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param alarmFlagCompId 报警标识对照ID
	 * @return AlarmFlagCompEntity 报警标识对照实体类
	 * @throws Exception 
	 */
	AlarmFlagCompEntity getSingleAlarmFlagComp(Long alarmFlagCompId) throws Exception;
	
	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2018-03-30
	 * @param dcsCodeId DCS名称
	 * @param alarmFlagSource 源报警事件标识
	 * @param alarmFlagId 本系统报警标识
	 * @param inUse   是否使用
	 * @param page 翻页实现类
	 * @throws Exception 
	 * @return PaginationBean<AlarmFlagCompEntity> 翻页对象
	 */
	PaginationBean<AlarmFlagCompEntity> getAlarmFlagComp( Long dcsCodeId, String alarmFlagSource, Long alarmFlagId,Integer inUse, Pagination page) throws  Exception;
}
