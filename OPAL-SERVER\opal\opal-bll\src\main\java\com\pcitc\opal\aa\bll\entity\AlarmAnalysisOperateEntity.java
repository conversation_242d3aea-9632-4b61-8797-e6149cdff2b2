package com.pcitc.opal.aa.bll.entity;

import java.util.Date;

 /*
 * 报警分析-操作详情图表数据实体
 * 模块编号：pcitc_opal_bll_class_AlarmAnalysisOperateEntity
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/30
 * 修改编号：1
 * 描    述：报警分析-操作详情图表数据实体
 */
public class AlarmAnalysisOperateEntity {

   public AlarmAnalysisOperateEntity(Long alarmPointId, Long alarmFlagId, Long times, Date startDate){
        super();
        this.alarmPointId = alarmPointId;
        this.alarmFlagId = alarmFlagId;
        this.times = times;
        this.startDate = startDate;
    }
    /**
     *报警点ID
     */
    private Long alarmPointId;

    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    /**
     *报警次数
     */
    private Long times;

    /**
     *发生时间
     */
    private Date startDate;

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    public Long getAlarmFlagId() {
        return alarmFlagId;
    }

    public void setAlarmFlagId(Long alarmFlagId) {
        this.alarmFlagId = alarmFlagId;
    }

    public Long getTimes() {
        return times;
    }

    public void setTimes(Long times) {
        this.times = times;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }
}
