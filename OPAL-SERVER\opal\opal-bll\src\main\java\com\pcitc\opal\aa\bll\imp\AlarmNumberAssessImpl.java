package com.pcitc.opal.aa.bll.imp;

import com.pcitc.opal.aa.bll.AlarmNumberAssessService;
import com.pcitc.opal.aa.bll.entity.*;
import com.pcitc.opal.ad.bll.entity.AlarmEventEntity;
import com.pcitc.opal.ad.dao.AlarmEventRepository;
import com.pcitc.opal.ad.dao.AlarmNumberRepository;
import com.pcitc.opal.ad.pojo.AlarmEvent;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.CommonEnum.AlarmPriorityEnum;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import com.pcitc.opal.common.bll.entity.UnitEntity;
import com.pcitc.opal.common.bll.entity.WorkshopEntity;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.LongValue;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import java.math.BigInteger;
import java.text.Collator;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/*
 * 报警数量评估业务逻辑层接口实现
 * 模块编号：pcitc_opal_bll_class_AlarmNumberAssessImpl
 * 作  　者：
 * 创建时间：2017-10-27
 * 修改编号：1
 * 描    述：报警数量评估业务逻辑层接口实现
 */
@Slf4j
@Service
public class AlarmNumberAssessImpl implements AlarmNumberAssessService {

    /**
     * 实例化数据访问层接口
     */
    @Autowired
    private AlarmNumberRepository repo;
    @Autowired
    private AlarmEventRepository eventRepo;
    @Autowired
    private BasicDataService basicDataService;
    @Autowired
    private ShiftService shiftService;

    /**
     * 报警数量评估-报警数-图形显示(按班组统计)
     * <p>
     *  * <AUTHOR> 2019-06-26
     *
     * @param alarmList 报警统计数据
     * @param teamCode  班组编码
     * @param unitCode  装置编码
     * @param beginTime 报警事件的开始间
     * @param endTime   报警事件的结束时间
     * @param dateType  日期枚举类型
     * @param unitName  装置名称
     * @return List<AlarmNumberModel> AlarmNumberModel实体类集合
     * @throws Exception 
     */
    private List<AlarmNumberConvertEntity> getWorkTeamAlarmNumber(
            List<AlarmNumberEntity> alarmList, Long[] teamCode, String unitCode, Date beginTime, Date endTime,
            CommonEnum.DateTypeEnum dateType, String unitName) throws Exception {
        List<AlarmNumberConvertEntity> result = new ArrayList<>();
        //班组待处理数据
        List<ShiftWorkTeamEntity> shiftList = shiftService.getShiftList(unitCode, beginTime, endTime, Arrays.asList(teamCode));
        if (shiftList == null || shiftList.size() == 0) {
            return result;
        }
        for (ShiftWorkTeamEntity shift : shiftList) {
            long sum = alarmList.stream()
                    .filter(x -> x.getAlarmDate().getTime() >= shift.getStartTime().getTime() && x.getAlarmDate().getTime() < shift.getEndTime().getTime())
                    .mapToLong(x -> x.getAlarmCount()).sum();
            shift.setShiftSortNum(sum);//该班次报警数
        }
        List<String> legend = shiftList.stream().map(x -> x.getWorkTeamSName()).distinct().sorted((s1, s2) -> Collator.getInstance(Locale.CHINESE).compare(s1, s2)).collect(Collectors.toList());
        Map<String, List<ShiftWorkTeamEntity>> groupShiftList = shiftList.stream().collect(Collectors.groupingBy(ShiftWorkTeamEntity::getWorkTeamSName));

        //获取班次排序
        //1)get one day shift
//		Map<Date, List<ShiftWorkTeamEntity>> dateListMap = shiftList.stream().collect(Collectors.groupingBy(ShiftWorkTeamEntity::getShiftDate));
//		//2)get shifName has been sorted
//		List<ShiftWorkTeamEntity> sortedShift = dateListMap.get(shiftList.get(0).getShiftDate()).stream()
//				.sorted(Comparator.comparing(ShiftWorkTeamEntity::getStartTime)).collect(Collectors.toList());

        /**
         * 表格数据处理
         */
        List<AlarmNumberEntity> alarmNumberEntityList = new ArrayList<>();
        //Top20
        List<ShiftWorkTeamEntity> top20List = shiftList.stream().sorted(Comparator.comparing(x -> x.getShiftSortNum(), Comparator.reverseOrder())).collect(Collectors.toList());

        //获取班组名称排序
        List<String> workTeamNames = shiftList.stream().map(x -> x.getWorkTeamSName()).distinct().sorted((s1, s2) -> Collator.getInstance(Locale.CHINESE).compare(s1, s2)).collect(Collectors.toList());
        //按班组分组 处理表格数据
        Map<String, List<ShiftWorkTeamEntity>> groupworkTeamList = shiftList.stream().collect(Collectors.groupingBy(ShiftWorkTeamEntity::getWorkTeamSName));
        for (String workTeamName : workTeamNames) {
            List<ShiftWorkTeamEntity> workTeamList = groupworkTeamList.get(workTeamName);
            workTeamList = workTeamList.stream().sorted(Comparator.comparing(ShiftWorkTeamEntity::getShiftDate)).collect(Collectors.toList());
            AlarmNumberEntity entity = new AlarmNumberEntity();
            entity.setName(unitName);
            entity.setWorkTeamName(workTeamName);
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            entity.setAlarmTime(format.format(workTeamList.get(0).getShiftDate()));
            entity.setEndTime(format.format(workTeamList.get(workTeamList.size() - 1).getShiftDate()));
            entity.setAlarmCount(workTeamList.stream().mapToLong(ShiftWorkTeamEntity::getShiftSortNum).sum());
            alarmNumberEntityList.add(entity);
        }
        for (String shift : legend) {
            //实体赋值
            AlarmNumberConvertEntity alarmNumberConvertEntity = new AlarmNumberConvertEntity();
            alarmNumberConvertEntity.setId(unitCode);
            alarmNumberConvertEntity.setName(shift);
            if (result.size() == 0) {
                long sumAlarmCount = alarmList.stream().mapToLong(x -> x.getAlarmCount()).sum();//总报警数
                float totalDayCount = (float) Math.round((float) (endTime.getTime() - beginTime.getTime()) / (float) (24 * 3600 * 1000) * 100) / 100;//总天数
                if (totalDayCount < 1)
                    totalDayCount = 1;
                float avgAlarmCount = (float) Math.round((float) sumAlarmCount / totalDayCount * 100) / 100;
                alarmNumberConvertEntity.setSum(sumAlarmCount);
                alarmNumberConvertEntity.setAvg(avgAlarmCount);
                alarmNumberConvertEntity.setLegend(legend);
                alarmNumberConvertEntity.setList(alarmNumberEntityList);
                alarmNumberConvertEntity.setGroupworkTeamList(groupworkTeamList);
                alarmNumberConvertEntity.setShiftWorkTeamEntityList(top20List);
            }
            //x轴赋值
            List<String> xaxis = new ArrayList<>();
            //数据赋值
            List<Long> counts = new ArrayList<>();
            List<String> tips = new ArrayList<>();
            List<String> barNames = new ArrayList<>();
            Date time = beginTime;
            DateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            DateFormat monthFormat = new SimpleDateFormat("yyyy-MM");
            List<ShiftWorkTeamEntity> shiftWorkTeamList = groupShiftList.get(shift);
            List<ShiftWorkTeamEntity> rangeShiftList = new ArrayList<>();
            while (time.getTime() < endTime.getTime()) {
                long count = 0;
                String tip = "";
                switch (dateType) {
                    case Day:
                        xaxis.add(dateFormat.format(time));
                        Date beginDays = time;
                        Date addDays = DateUtils.addDays(beginDays, 1);
                        Long beginDaysMilli = dateFormat.parse(dateFormat.format(time)).getTime();
                        Long addDaysMilli = dateFormat.parse(dateFormat.format(addDays)).getTime();
                        if (shiftWorkTeamList != null && shiftWorkTeamList.size() != 0) {
                            rangeShiftList = shiftWorkTeamList.stream().filter(x -> x.getShiftDate().getTime() >= beginDaysMilli
                                    && x.getShiftDate().getTime() < addDaysMilli).collect(Collectors.toList());
                            count = rangeShiftList.stream().mapToLong(ShiftWorkTeamEntity::getShiftSortNum).sum();
                        }
                        counts.add(count);
                        if (rangeShiftList.size() > 0) {
                            tip = "从:" + dateFormat.format(time) + "到" + dateFormat.format(addDays) + "<br>" + rangeShiftList.get(0).getWorkTeamSName() + ":" + count + "";
                        }
                        tips.add(tip);
                        barNames.add(shift);
                        time = addDays;
                        break;
                    case Week:
                        xaxis.add(dateFormat.format(time));
                        Date beginWeeks = time;
                        Long beginWeeksMilli = dateFormat.parse(dateFormat.format(time)).getTime();
                        Date addWeeks = DateUtils.addWeeks(beginWeeks, 1);
                        Long addWeeksMilli = dateFormat.parse(dateFormat.format(addWeeks)).getTime();
                        if (shiftWorkTeamList != null && shiftWorkTeamList.size() != 0) {
                            rangeShiftList = shiftWorkTeamList.stream().filter(x -> x.getShiftDate().getTime() >= beginWeeksMilli
                                    && x.getShiftDate().getTime() < addWeeksMilli).collect(Collectors.toList());
                            count = rangeShiftList.stream().mapToLong(ShiftWorkTeamEntity::getShiftSortNum).sum();
                        }
                        counts.add(count);
                        if (rangeShiftList.size() > 0) {
                            tip = "从:" + dateFormat.format(time) + "到" + dateFormat.format(addWeeks) + "<br>" + rangeShiftList.get(0).getWorkTeamSName() + ":" + count + "";
                        }
                        barNames.add(shift);
                        tips.add(tip);
                        time = addWeeks;
                        break;
                    case Month:
                        xaxis.add(monthFormat.format(time));
                        Date beginMonths = time;
                        Date addMonths = DateUtils.addMonths(beginMonths, 1);
                        Long beginMonthMilli = dateFormat.parse(dateFormat.format(time)).getTime();
                        Long addMonthsMilli = dateFormat.parse(dateFormat.format(addMonths)).getTime();
                        if (shiftWorkTeamList != null && shiftWorkTeamList.size() != 0) {
                            rangeShiftList = shiftWorkTeamList.stream().filter(x -> x.getShiftDate().getTime() >= beginMonthMilli
                                    && x.getShiftDate().getTime() < addMonthsMilli).collect(Collectors.toList());
                            count = rangeShiftList.stream().mapToLong(ShiftWorkTeamEntity::getShiftSortNum).sum();
                        }
                        counts.add(count);
                        if (rangeShiftList.size() > 0) {
                            tip = "从:" + dateFormat.format(time) + "到" + dateFormat.format(addMonths) + "<br>" + rangeShiftList.get(0).getWorkTeamSName() + ":" + count + "";
                        }
                        barNames.add(shift);
                        tips.add(tip);
                        time = addMonths;
                        break;
                    default:
                        break;
                }
            }
            alarmNumberConvertEntity.setXaxis(xaxis);
            alarmNumberConvertEntity.setCounts(counts);
            alarmNumberConvertEntity.setTip(tips);
            alarmNumberConvertEntity.setBarNames(barNames);
            alarmNumberConvertEntity.setList(alarmNumberEntityList);
            result.add(alarmNumberConvertEntity);
        }
        return result;
    }

    /**
     * 报警数量评估-报警数-图形显示
     * <p>
     *  * <AUTHOR> 2017-10-30
     *
     * @param unitCodes   装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param beginTime   报警事件的开始间
     * @param endTime     报警事件的结束时间
     * @param dateType    日期枚举类型
     * @return List<AlarmNumberModel> AlarmNumberModel实体类集合
     * @throws Exception 
     */
    @Override
    public List<AlarmNumberConvertEntity> getAlarmNumber(String[] unitCodes, Long[] prdtCellIds, Date beginTime,
                                                         Date endTime, CommonEnum.DateTypeEnum dateType, String checkTeam, Long[] team) throws Exception {
        List<UnitEntity> unitList = basicDataService.getUnitListByIds(unitCodes, false);
        List<AlarmNumberEntity> pageEntity = new ArrayList<AlarmNumberEntity>();
        if (checkTeam != null && !checkTeam.equals("")) {
            List<Object[]> listAlarmEvent = repo.getAlarmNumber(unitCodes, prdtCellIds, beginTime, endTime, CommonEnum.DateTypeEnum.Hour);
            for (Object[] ae : listAlarmEvent) {
                AlarmNumberEntity ane = new AlarmNumberEntity();
                ane.setId(ae[0].toString());
                UnitEntity ue = unitList.stream().filter(ul -> ul.getStdCode().equals(ae[0])).findFirst().orElse(new UnitEntity());
                ane.setName(ue.getSname());
                ane.setAlarmCount(Long.valueOf(ae[2].toString()));
                ane.setAlarmTime(ae[3].toString());
                ane.setAlarmDate(DateUtils.parseDate(ae[3].toString(), Locale.CHINESE, "yyyy-MM-dd HH:mm:ss"));
                pageEntity.add(ane);
            }
            ;
            List<AlarmNumberConvertEntity> workTeamAlarmNumber = getWorkTeamAlarmNumber(pageEntity, team, unitCodes[0], beginTime, endTime, dateType, unitList.get(0).getSname());
            return workTeamAlarmNumber;
        } else {
            List<Object[]> listAlarmEvent = repo.getAlarmNumber(unitCodes, prdtCellIds, beginTime, endTime, dateType);
            if (ArrayUtils.isEmpty(prdtCellIds)) {
                for (Object[] ae : listAlarmEvent) {
                    AlarmNumberEntity ane = new AlarmNumberEntity();
                    ane.setId(ae[0].toString());
                    UnitEntity ue = unitList.stream().filter(ul -> ul.getStdCode().equals(ae[0])).findFirst().orElse(new UnitEntity());
                    ane.setName(ue.getSname());
                    ane.setUnitName(ue.getName());
                    ane.setAlarmCount(Long.valueOf(ae[2].toString()));
                    ane.setAlarmTime(ae[3].toString());
                    pageEntity.add(ane);
                }
                ;
            } else {
                for (Object[] ae : listAlarmEvent) {
                    AlarmNumberEntity ane = new AlarmNumberEntity();
                    ane.setId(ae[0].toString());
                    ane.setName(ae[1].toString());
                    ane.setAlarmCount(Long.valueOf(ae[2].toString()));
                    ane.setAlarmTime(ae[3].toString());
                    pageEntity.add(ane);
                }
                ;
            }
            long sumAlarmCount = listAlarmEvent.stream().mapToLong(x -> (long) x[2]).sum();
            float totalDayCount = (float) Math.round((float) (endTime.getTime() - beginTime.getTime()) / (float) (24 * 3600 * 1000) * 100) / 100;
            if (totalDayCount < 1)
                totalDayCount = 1;
            float avgAlarmCount = (float) Math.round((float) sumAlarmCount / totalDayCount * 100) / 100;

            pageEntity = pageEntity.stream().sorted(Comparator.comparing(AlarmNumberEntity::getName, ComparatorList.orderByASC())).collect(Collectors.toList());
            if (pageEntity != null && pageEntity.size() > 0) {
                pageEntity.get(0).setSum(sumAlarmCount);
                pageEntity.get(0).setAvg(avgAlarmCount);
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfTip = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdfMonth = new SimpleDateFormat("yyyy-MM");
            List<AlarmNumberConvertEntity> list = new ArrayList<AlarmNumberConvertEntity>();
            List<String> legend = pageEntity.stream().map(x -> x.getName()).distinct().sorted((s1, s2) -> Collator.getInstance(Locale.CHINESE).compare(s1, s2)).collect(Collectors.toList());
            List<String> colorList = new ArrayList<String>();
            List<String> entityList = pageEntity.stream().map(x -> x.getName()).distinct().collect(Collectors.toList());
            for (String strEn : entityList) {
                int i = 0;
                for (String strLe : legend) {
                    if (strLe.equals(strEn)) {
                        i = i % 5;
                        break;
                    }
                    i++;
                }
                switch (i) {
                    case 0:
                        colorList.add("#6699CC");
                        break;
                    case 1:
                        colorList.add("#669999");
                        break;
                    case 2:
                        colorList.add("#CC99CC");
                        break;
                    case 3:
                        colorList.add("#66CCCC");
                        break;
                    case 4:
                        colorList.add("#9999CC");
                        break;
                    default:
                        break;
                }
            }
            AlarmNumberConvertEntity anm = new AlarmNumberConvertEntity();
            anm.setId("-1");
            List<String> xaxis = new ArrayList<String>();
            List<String> tip = new ArrayList<String>();
            List<Long> counts = new ArrayList<Long>();
            String queryTime = " " + basicDataService.getQueryTime().get(0).getValue().toString();
            Date cal = null;
            switch (dateType) {
                case Hour:
                    for (AlarmNumberEntity ane : pageEntity) {
                        if (ane.getId().equals(anm.getId()) == false) {
                            if (xaxis.size() > 0) {
                                Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1));
                                timeda = DateUtils.addHours(timeda, 1);
                                while (endTime.getTime() > timeda.getTime()) {
                                    xaxis.add(sdf.format(timeda));
                                    counts.add(0L);
                                    Date timeBeg = timeda;
                                    timeda = DateUtils.addHours(timeda, 1);
                                    tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                                }
                            }
                            anm.setXaxis(xaxis);
                            anm.setCounts(counts);
                            anm.setTip(tip);
                            list.add(anm);
                            anm = new AlarmNumberConvertEntity();
                            anm.setId(ane.getId());
                            anm.setName(ane.getName());
                            xaxis = new ArrayList<String>();
                            counts = new ArrayList<Long>();
                            tip = new ArrayList<String>();
                        }
                        if (xaxis.size() == 0) {
                            Date timeda = beginTime;
                            while (sdf.parse(ane.getAlarmTime()).getTime() > timeda.getTime()) {
                                xaxis.add(sdf.format(timeda));
                                counts.add(0L);
                                Date timeBeg = timeda;
                                timeda = DateUtils.addHours(timeda, 1);
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                            }
                        } else {
                            Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1));
                            timeda = DateUtils.addHours(timeda, 1);
                            while (sdf.parse(ane.getAlarmTime()).getTime() > timeda.getTime()) {
                                xaxis.add(sdf.format(timeda));
                                counts.add(0L);
                                Date timeBeg = timeda;
                                timeda = DateUtils.addHours(timeda, 1);
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                            }
                        }
                        cal = DateUtils.addHours(sdf.parse(ane.getAlarmTime()), 1);
                        if (cal.getTime() > endTime.getTime()) {
                            tip.add("从:" + ane.getAlarmTime() + "到" + sdf.format(DateUtils.addSeconds(endTime, +1)) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                            ane.setEndTime(sdf.format(endTime));
                        } else {
                            tip.add("从:" + ane.getAlarmTime() + "到" + sdf.format(cal) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                            ane.setEndTime(sdf.format(DateUtils.addSeconds(cal, -1)));
                        }
                        xaxis.add(ane.getAlarmTime());
                        counts.add(ane.getAlarmCount());
                    }
                    if (xaxis.size() > 0) {
                        Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1));
                        timeda = DateUtils.addHours(timeda, 1);
                        while (endTime.getTime() > timeda.getTime()) {
                            xaxis.add(sdf.format(timeda));
                            counts.add(0L);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addHours(timeda, 1);
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                        }
                    }
                    break;
                case Day:
                    for (AlarmNumberEntity ane : pageEntity) {
                        if (ane.getId().equals(anm.getId()) == false) {
                            if (xaxis.size() > 0) {
                                Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + queryTime);
                                timeda = DateUtils.addDays(timeda, 1);
                                while (endTime.getTime() > timeda.getTime()) {
                                    xaxis.add(sdfTip.format(timeda));
                                    counts.add(0L);
                                    Date timeBeg = timeda;
                                    timeda = DateUtils.addDays(timeda, 1);
                                    tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                                }
                            }
                            anm.setXaxis(xaxis);
                            anm.setCounts(counts);
                            anm.setTip(tip);
                            list.add(anm);
                            anm = new AlarmNumberConvertEntity();
                            anm.setId(ane.getId());
                            anm.setName(ane.getName());
                            xaxis = new ArrayList<String>();
                            counts = new ArrayList<Long>();
                            tip = new ArrayList<String>();
                        }
                        if (xaxis.size() == 0) {
                            Date timeda = beginTime;
                            while (sdf.parse(ane.getAlarmTime() + queryTime).getTime() > timeda.getTime()) {
                                xaxis.add(sdfTip.format(timeda));
                                counts.add(0L);
                                Date timeBeg = timeda;
                                timeda = DateUtils.addDays(timeda, 1);
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                            }
                        } else {
                            Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + queryTime);
                            timeda = DateUtils.addDays(timeda, 1);
                            while (sdf.parse(ane.getAlarmTime() + queryTime).getTime() > timeda.getTime()) {
                                xaxis.add(sdfTip.format(timeda));
                                counts.add(0L);
                                Date timeBeg = timeda;
                                timeda = DateUtils.addDays(timeda, 1);
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                            }
                        }
                        cal = DateUtils.addDays(sdf.parse(ane.getAlarmTime() + queryTime), 1);
                        if (cal.getTime() > endTime.getTime()) {
                            tip.add("从:" + ane.getAlarmTime() + queryTime + "到" + sdf.format(DateUtils.addSeconds(endTime, 1)) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                            ane.setEndTime(sdf.format(endTime));
                        } else {
                            tip.add("从:" + ane.getAlarmTime() + queryTime + "到" + sdf.format(cal) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                            ane.setEndTime(sdf.format(DateUtils.addSeconds(cal, -1)));
                        }
                        xaxis.add(ane.getAlarmTime());
                        ane.setAlarmTime(ane.getAlarmTime() + queryTime);
                        counts.add(ane.getAlarmCount());
                    }
                    if (xaxis.size() > 0) {
                        Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + queryTime);
                        timeda = DateUtils.addDays(timeda, 1);
                        while (endTime.getTime() > timeda.getTime()) {
                            xaxis.add(sdfTip.format(timeda));
                            counts.add(0L);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addDays(timeda, 1);
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                        }
                    }
                    break;
                case Week:
                    for (AlarmNumberEntity ane : pageEntity) {
                        if (ane.getId().equals(anm.getId()) == false) {
                            if (xaxis.size() > 0) {
                                Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + queryTime);
                                timeda = DateUtils.addWeeks(timeda, 1);
                                while (endTime.getTime() > timeda.getTime()) {
                                    xaxis.add(sdfTip.format(timeda));
                                    counts.add(0L);
                                    Date timeBeg = timeda;
                                    timeda = DateUtils.addWeeks(timeda, 1);
                                    tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                                }
                            }
                            anm.setXaxis(xaxis);
                            anm.setCounts(counts);
                            anm.setTip(tip);
                            list.add(anm);
                            anm = new AlarmNumberConvertEntity();
                            anm.setId(ane.getId());
                            anm.setName(ane.getName());
                            xaxis = new ArrayList<String>();
                            counts = new ArrayList<Long>();
                            tip = new ArrayList<String>();
                        }
                        if (xaxis.size() == 0) {
                            Date timeda = beginTime;
                            while (sdf.parse(ane.getAlarmTime() + queryTime).getTime() > timeda.getTime()) {
                                xaxis.add(sdfTip.format(timeda));
                                counts.add(0L);
                                Date timeBeg = timeda;
                                timeda = DateUtils.addWeeks(timeda, 1);
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                            }
                        } else {
                            Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + queryTime);
                            timeda = DateUtils.addWeeks(timeda, 1);
                            while (sdf.parse(ane.getAlarmTime() + queryTime).getTime() > timeda.getTime()) {
                                xaxis.add(sdfTip.format(timeda));
                                counts.add(0L);
                                Date timeBeg = timeda;
                                timeda = DateUtils.addWeeks(timeda, 1);
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                            }
                        }
                        cal = DateUtils.addWeeks(sdf.parse(ane.getAlarmTime() + queryTime), 1);
                        if (cal.getTime() > endTime.getTime()) {
                            tip.add("从:" + ane.getAlarmTime() + queryTime + "到" + sdf.format(DateUtils.addSeconds(endTime, 1)) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                            ane.setEndTime(sdf.format(endTime));
                        } else {
                            tip.add("从:" + ane.getAlarmTime() + queryTime + "到" + sdf.format(cal) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                            ane.setEndTime(sdf.format(DateUtils.addSeconds(cal, -1)));
                        }
                        xaxis.add(ane.getAlarmTime());
                        ane.setAlarmTime(ane.getAlarmTime() + queryTime);
                        counts.add(ane.getAlarmCount());
                    }
                    if (xaxis.size() > 0) {
                        Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + queryTime);
                        timeda = DateUtils.addWeeks(timeda, 1);
                        while (endTime.getTime() > timeda.getTime()) {
                            xaxis.add(sdfTip.format(timeda));
                            counts.add(0L);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addWeeks(timeda, 1);
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                        }
                    }
                    break;
                case Month:
                    for (AlarmNumberEntity ane : pageEntity) {
                        if (ane.getId().equals(anm.getId()) == false) {
                            if (xaxis.size() > 0) {
                                Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + "-01" + queryTime);
                                timeda = DateUtils.addMonths(timeda, 1);
                                while (endTime.getTime() > timeda.getTime()) {
                                    xaxis.add(sdfMonth.format(timeda));
                                    counts.add(0L);
                                    Date timeBeg = timeda;
                                    timeda = DateUtils.addMonths(timeda, 1);
                                    if (timeda.getTime() > endTime.getTime()) {
                                        tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(endTime) + "<br>" + anm.getName() + ":" + 0 + "");
                                    } else {
                                        tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                                    }
                                }
                            }
                            anm.setXaxis(xaxis);
                            anm.setCounts(counts);
                            anm.setTip(tip);
                            list.add(anm);
                            anm = new AlarmNumberConvertEntity();
                            anm.setId(ane.getId());
                            anm.setName(ane.getName());
                            xaxis = new ArrayList<String>();
                            counts = new ArrayList<Long>();
                            tip = new ArrayList<String>();
                        }
                        if (xaxis.size() == 0) {
                            Date timeda = beginTime;
                            while (sdf.parse(ane.getAlarmTime() + "-01" + queryTime).getTime() > timeda.getTime()) {
                                xaxis.add(sdfMonth.format(timeda));
                                counts.add(0L);
                                Date timeBeg = timeda;
                                if (timeda.getTime() == beginTime.getTime()) {
                                    timeda = sdf.parse(sdfMonth.format(timeda) + "-01" + queryTime);
                                }
                                timeda = DateUtils.addMonths(timeda, 1);
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                            }
                        } else {
                            Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + "-01" + queryTime);
                            timeda = DateUtils.addMonths(timeda, 1);
                            while (sdf.parse(ane.getAlarmTime() + "-01" + queryTime).getTime() > timeda.getTime()) {
                                xaxis.add(sdfMonth.format(timeda));
                                counts.add(0L);
                                Date timeBeg = timeda;
                                timeda = DateUtils.addMonths(timeda, 1);
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                            }
                        }
                        String tipStr = "";
                        xaxis.add(ane.getAlarmTime());
                        String alarmTime = ane.getAlarmTime();
                        if (beginTime.getTime() > sdf.parse(alarmTime + "-01" + queryTime).getTime()) {
                            tipStr = "从:" + sdfTip.format(beginTime) + queryTime + "到";
                            ane.setAlarmTime(sdfTip.format(beginTime) + queryTime);
                        } else {
                            tipStr = "从:" + alarmTime + "-01" + queryTime + "到";
                            ane.setAlarmTime(ane.getAlarmTime() + "-01" + queryTime);
                        }
                        cal = DateUtils.addMonths(sdf.parse(alarmTime + "-01" + queryTime), 1);
                        if (cal.getTime() > endTime.getTime()) {
                            tipStr += sdf.format(DateUtils.addSeconds(endTime, 1));
                            ane.setEndTime(sdf.format(endTime));
                        } else {
                            tipStr += sdf.format(cal);
                            ane.setEndTime(sdf.format(DateUtils.addSeconds(cal, -1)));
                        }
                        tip.add(tipStr += "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                        counts.add(ane.getAlarmCount());
                    }
                    if (xaxis.size() > 0) {
                        Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + "-01" + queryTime);
                        timeda = DateUtils.addMonths(timeda, 1);
                        while (endTime.getTime() > timeda.getTime()) {
                            xaxis.add(sdfMonth.format(timeda));
                            counts.add(0L);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addMonths(timeda, 1);
                            if (timeda.getTime() > endTime.getTime()) {
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(endTime) + "<br>" + anm.getName() + ":" + 0 + "");
                            } else {
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
            anm.setXaxis(xaxis);
            anm.setCounts(counts);
            anm.setTip(tip);
            list.add(anm);
            list.remove(0);
            if (list.size() > 0) {
                list.get(0).setList(pageEntity.stream().sorted(Comparator.comparing(AlarmNumberEntity::getAlarmTime).thenComparing(AlarmNumberEntity::getName, ComparatorList.orderByASC())).collect(Collectors.toList()));
                list.get(0).setSum(pageEntity.get(0).getSum());
                list.get(0).setAvg(pageEntity.get(0).getAvg());
                list.get(0).setLegend(legend);
                list.get(0).setColor(colorList);
            }

            return list;
        }

    }

    /**
     * 获取最频繁报警数量TOP20
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param unitCodes 装置编码集合
     * @param prdtIds   生产单元ID集合
     * @param topType   Top20,Top10切换选择
     * @return
     * @throws Exception
     * <AUTHOR> 2017-10-30
     */
    @Override
    public List<AlarmNumberAssessDataEntity> getAlarmNumberAssessTop20(String[] unitCodes, Long[] prdtIds, String[] wokrUnitCodes, Date startTime, Date endTime, Integer topType) throws Exception {
        Map<String, Object> timeMap = basicDataService.getSearchTime(startTime, endTime);
        startTime = (Date) timeMap.get("startDate");
        endTime = (Date) timeMap.get("endDate");
        Long day = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
        String startFlag = (String) timeMap.get("startFlag");
        String endFlag = (String) timeMap.get("endFlag");

        if (wokrUnitCodes != null && wokrUnitCodes.length > 0) {
            wokrUnitCodes = basicDataService.getUnitListByWorkshopIds(wokrUnitCodes, true).stream().map(x -> x.getStdCode()).toArray(String[]::new);
            unitCodes = null;
            prdtIds = null;
        }

        return alarmNumberAssessTop20(startTime, endTime, day, startFlag, endFlag, wokrUnitCodes, unitCodes,
                prdtIds, topType);
    }

    /**
     * 获取最频繁报警数量TOP20-单一工厂/装置/生产单元
     *
     * @param id                ID
     * @param equipmentTypeEnum 查询类型
     * @param startTime         开始日期
     * @param endTime           结束日期
     * @param topType           Top20,Top10切换选择
     * @return
     * @throws Exception
     * <AUTHOR> 2017-11-08
     */
    @Override
    public List<AlarmNumberAssessDataEntity> getAlarmNumberAssessTop20(String id, CommonEnum.EquipmentTypeEnum equipmentTypeEnum, Date startTime,
                                                                       Date endTime, Integer topType) throws Exception {
        Map<String, Object> timeMap = basicDataService.getSearchTime(startTime, endTime);
        Long day = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
        String startFlag = (String) timeMap.get("startFlag");
        String endFlag = "<=";

        String[] workUnitIds = null;
        String[] unitIds = null;
        Long[] prdtIds = null;
        if (equipmentTypeEnum.equals(CommonEnum.EquipmentTypeEnum.Factory)) {
            workUnitIds = basicDataService.getUnitListByWorkshopIds(new String[]{id}, true).stream().map(x -> x.getStdCode()).toArray(String[]::new);
        } else if (equipmentTypeEnum.equals(CommonEnum.EquipmentTypeEnum.Unit)) {
            unitIds = new String[]{id};
        } else if (equipmentTypeEnum.equals(CommonEnum.EquipmentTypeEnum.PrdtCell)) {
            prdtIds = new Long[]{Long.valueOf(id)};
        }

        return alarmNumberAssessTop20(startTime, endTime, day, startFlag, endFlag, workUnitIds, unitIds,
                prdtIds, topType);
    }

    /**
     * 获取最频繁报警数量TOP20-单一工厂/装置/生产单元
     *
     * @param id                ID
     * @param equipmentTypeEnum 查询类型
     * @param startTime         开始日期
     * @param endTime           结束日期
     * @param topType           Top20,Top10切换选择
     * @param priority          优先级
     * @return
     * @throws Exception
     * <AUTHOR> 2019-09-30
     */
    @Override
    public List<AlarmNumberAssessDataEntity> getAlarmNumberAssessTop20(String[] id, Long[] alarmFlagId,
                                                                       CommonEnum.EquipmentTypeEnum equipmentTypeEnum,
                                                                       Date startTime, Date endTime, Integer topType,
                                                                       Integer[] priority, Boolean priorityFlag,
                                                                       Integer isElimination, Long[] prdtcellIds){
        List<AlarmNumberAssessDataEntity> list = new ArrayList<>();
        try {
            Map<String, Object> timeMap = basicDataService.getSearchTime(startTime, endTime);
            Long day = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
            String startFlag = (String) timeMap.get("startFlag");
            String endFlag = "<=";
            String[] workUnitIds = null;
            String[] unitIds = null;
            Long[] prdtIds = null;
            if (equipmentTypeEnum.equals(CommonEnum.EquipmentTypeEnum.Factory)) {
                workUnitIds = basicDataService.getUnitListByWorkshopIds(id, true).stream().map(x -> x.getStdCode()).toArray(String[]::new);
            } else if (equipmentTypeEnum.equals(CommonEnum.EquipmentTypeEnum.Unit)) {
                unitIds = id;
            } else if (equipmentTypeEnum.equals(CommonEnum.EquipmentTypeEnum.PrdtCell)) {
                prdtIds = prdtcellIds;
                unitIds = id;
            }
            list = alarmNumberAssessTop20(startTime, endTime, alarmFlagId, day, startFlag, endFlag, workUnitIds, unitIds,
                    prdtIds, topType, priority, priorityFlag, isElimination);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return list;
    }

    @Override
    public PaginationBean<AlarmNumberAssessDataEntity> getAlarmNumberAssessAll(String[] workshopCodes, String[] unitCode,
                                                                               Long[] prdtCellId, String id, Long[] alarmFlagId,
                                                                               CommonEnum.EquipmentTypeEnum equipmentTypeEnum,
                                                                               Date startTime, Date endTime, Integer topType,
                                                                               Integer[] priority, Boolean priorityFlag,
                                                                               Integer isElimination, Long[] prdtcellIds,
                                                                               Pagination page) {
        PaginationBean<AlarmNumberAssessDataEntity> paginationBean = new PaginationBean<>();
        try {
            Map<String, Object> timeMap = basicDataService.getSearchTime(startTime, endTime);
            Long day = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
            String startFlag = (String) timeMap.get("startFlag");
            String endFlag = "<=";
            String[] workUnitIds = null;
            String[] unitIds = null;
            Long[] prdtIds = null;
            paginationBean = alarmNumberAssessAll(workshopCodes, unitCode, prdtCellId, startTime, endTime, alarmFlagId, day, startFlag,
                    endFlag, workUnitIds, unitIds, prdtIds, topType, priority, priorityFlag, isElimination, page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return paginationBean;
    }
    @Override
    public PaginationBean<AlarmNumberAssessDataEntity> getAlarmNumberAssessAllType(String[] workshopCodes, String[] unitCode,
                                                                               Long[] prdtCellId, String id, Long[] alarmFlagId,
                                                                               CommonEnum.EquipmentTypeEnum equipmentTypeEnum,
                                                                               Date startTime, Date endTime, Integer topType,
                                                                               Integer[] monitorType, Boolean priorityFlag,
                                                                               Integer isElimination, Long[] prdtcellIds,
                                                                               Pagination page) {
        PaginationBean<AlarmNumberAssessDataEntity> paginationBean = new PaginationBean<>();
        try {
            Map<String, Object> timeMap = basicDataService.getSearchTime(startTime, endTime);
            Long day = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
            String startFlag = (String) timeMap.get("startFlag");
            String endFlag = "<=";
            String[] workUnitIds = null;
            String[] unitIds = null;
            Long[] prdtIds = null;
            paginationBean = alarmNumberAssessAllType(workshopCodes, unitCode, prdtCellId, startTime, endTime, alarmFlagId, day, startFlag,
                    endFlag, workUnitIds, unitIds, prdtIds, topType, monitorType, priorityFlag, isElimination, page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return paginationBean;
    }

    /**
     * 报警数量评估-报警数-单元显示
     * <p>
     *  * <AUTHOR> 2017-10-30
     *
     * @param unitCodes   装置编码数组
     * @param prdtCellIds 生产单元id数组
     * @param beginTime   报警事件的开始间
     * @param endTime     报警事件的结束时间
     * @return List<AlarmNumberModel> 返回AlarmNumberModel实体类集合
     * @throws Exception 
     */
    @Override
    public List<AlarmNumberConvertEntity> getAlarmNumberUnit(String[] unitCodes, Long[] prdtCellIds, Date beginTime,
                                                             Date endTime) throws Exception {
        List<UnitEntity> unitList = basicDataService.getUnitListByIds(unitCodes, true);
        List<Object[]> listAlarmEvent = repo.getAlarmNumberUnit(unitCodes, prdtCellIds, beginTime, endTime);
        List<AlarmNumberEntity> pageEntity = new ArrayList<AlarmNumberEntity>();
        for (Object[] ae : listAlarmEvent) {
            AlarmNumberEntity ane = new AlarmNumberEntity();
            ane.setId(ae[0].toString());
            if (ArrayUtils.isEmpty(prdtCellIds)) {
                UnitEntity ue = unitList.stream().filter(ul -> ul.getStdCode().equals(ae[0])).findFirst().orElse(new UnitEntity());
                ane.setName(ue.getSname());
            } else {
                ane.setName(ae[1].toString());
            }
            ane.setAlarmCount(Long.valueOf(ae[2].toString()));
            pageEntity.add(ane);
        }
        ;

        List<AlarmNumberConvertEntity> list = new ArrayList<AlarmNumberConvertEntity>();
        pageEntity = pageEntity.stream().sorted(Comparator.comparing(AlarmNumberEntity::getName, ComparatorList.orderByASC())).collect(Collectors.toList());
        AlarmNumberConvertEntity anm = new AlarmNumberConvertEntity();
        anm.setId("-1");
        List<String> xaxis = new ArrayList<String>();
        List<String> tip = new ArrayList<String>();
        List<Long> counts = new ArrayList<Long>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (AlarmNumberEntity ane : pageEntity) {
            if (ane.getId().equals(anm.getId())) {
                anm.setXaxis(xaxis);
                anm.setCounts(counts);
                anm.setTip(tip);
                list.add(anm);
                anm = new AlarmNumberConvertEntity();
                anm.setId(ane.getId());
                anm.setName(ane.getName());
                xaxis = new ArrayList<String>();
                counts = new ArrayList<Long>();
                tip = new ArrayList<String>();
            }
            xaxis.add(ane.getName());
            counts.add(ane.getAlarmCount());
            tip.add("从:" + sdf.format(beginTime) + "到" + sdf.format(endTime) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
            ane.setAlarmTime(sdf.format(beginTime));
            ane.setEndTime(sdf.format(DateUtils.addSeconds(endTime, -1)));
        }
        anm.setXaxis(xaxis);
        anm.setCounts(counts);
        anm.setTip(tip);
        list.add(anm);
        if (list.size() > 1) {
            list.remove(0);
        }
        if (list.size() > 0) {
            list.get(0).setList(pageEntity);
        }
        return list;
    }

    /**
     * 报警数量评估——趋势图数据
     *
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param unitCodes     装置编码数组
     * @param prdtIds       生产单元ID数组
     * @param workUnitCodes 车间编码数组
     * @param dateType      时间粒度
     * @return 报警数量评估趋势图数据实体
     * <AUTHOR> 2017-10-30
     */
    @Override
    public List<AlarmNumberAssessEntity> getAlarmNumberAssessTrendEntity(Date startTime, Date endTime, String[] unitCodes,
                                                                         Long[] prdtIds, String[] workUnitCodes, CommonEnum.DateTypeEnum dateType) throws Exception {

        String[] ids = null;
        CommonEnum.EquipmentTypeEnum equipmentTypeEnum;
        //1.计算查询类型
        if (prdtIds != null) {
            equipmentTypeEnum = CommonEnum.EquipmentTypeEnum.PrdtCell;
            ids = (String[]) ConvertUtils.convert(prdtIds, String[].class);
        } else {
            equipmentTypeEnum = CommonEnum.EquipmentTypeEnum.Unit;
            ids = unitCodes;
        }
        if (workUnitCodes != null) {
            equipmentTypeEnum = CommonEnum.EquipmentTypeEnum.Unit;
            ids = basicDataService.getUnitListByWorkshopIds(workUnitCodes, true).stream().map(x -> x.getStdCode()).toArray(String[]::new);
        }

        return basicDataService.getAlarmNumberAssessList(startTime, endTime, dateType, ids, equipmentTypeEnum);
    }


    /**
     * 报警数量评估——单一装置/生产单元/工厂趋势图数据
     *
     * @param startTime 时间范围起始
     * @param endTime   时间范围结束
     * @param id        装置或生产单元ID
     * @param queryType 查询类型
     * @param dateType  时间粒度
     * @return 报警数量评估趋势图数据实体集合
     * @throws Exception
     * <AUTHOR> 2017-11-07
     */
    @Override
    public List<AlarmNumberAssessEntity> getAlarmNumberAssessTrendEntity(Date startTime, Date endTime, String id,
                                                                         CommonEnum.EquipmentTypeEnum queryType, CommonEnum.DateTypeEnum dateType) throws Exception {
        String[] ids = new String[]{id};
        //1.计算查询类型
        if (queryType.equals(CommonEnum.EquipmentTypeEnum.Factory)) {
            queryType = CommonEnum.EquipmentTypeEnum.Unit;
            ids = basicDataService.getUnitListByWorkshopIds(ids, true).stream().map(x -> x.getStdCode()).collect(Collectors.toList()).toArray(ids);
        }

        return basicDataService.getAlarmNumberAssessList(startTime, endTime, dateType, ids, queryType);
    }

    /**
     * 查询报警数详情集合
     * <p>
     *  * <AUTHOR> 2017-11-07
     *
     * @param id         车间/装置/生产单元的id
     * @param searchType 搜索的类型
     * @param beginTime  报警事件的开始间
     * @param endTime    报警事件的结束时间
     * @param page       查询分页对象
     * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity实体类分页对象
     * @throws Exception 
     */
    @SuppressWarnings("unchecked")
    @Override
    public PaginationBean<AlarmEventEntity> getAlarmNumberDetail(String id, Integer searchType, Date beginTime, Date endTime, Pagination page) throws Exception {
        String[] unitCodes = null;
        Long prdtCellId = null;
        List<UnitEntity> unitEntityList = null;
        String unitSname = null;
        if (searchType == 1) {//车间
            unitEntityList = basicDataService.getUnitListByWorkshopIds(new String[]{id}, true);
            unitCodes = unitEntityList.stream().map(UnitEntity::getStdCode).distinct().toArray(String[]::new);
        } else if (searchType == 2) {//装置
            unitCodes = new String[]{id};
            unitEntityList = basicDataService.getUnitListByIds(unitCodes, true);
        } else if (searchType == 3) {//生产单元
            prdtCellId = new Long(id);
        }
        PaginationBean<AlarmEvent> listAlarmEvent = eventRepo.getAlarmNumberDetail(prdtCellId, unitCodes, beginTime, endTime, page);
        if (searchType == 3) {//生产单元
            unitEntityList = basicDataService.getUnitListByIds(listAlarmEvent.getPageList().stream().map(x -> x.getAlarmPoint().getPrdtCell().getUnitId()).distinct().toArray(String[]::new), true);
            if (!unitEntityList.isEmpty())
                unitSname = unitEntityList.get(0).getSname();
        }
        PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<AlarmEventEntity>(page, listAlarmEvent.getTotal());
        returnAlarmEvent.setPageList(ObjectConverter.listConverter(listAlarmEvent.getPageList(), AlarmEventEntity.class));
        int i = 0;
        for (AlarmEventEntity aee : returnAlarmEvent.getPageList()) {
            AlarmEvent ae = listAlarmEvent.getPageList().get(i);
            if (searchType == 1 || searchType == 2) {//车间和装置
                UnitEntity ue = unitEntityList.stream().filter(x -> x.getStdCode().equals(ae.getAlarmPoint().getPrdtCell().getUnitId())).findFirst().orElse(new UnitEntity());
                aee.setUnitName(ue.getSname());
            } else {//生产单元
                aee.setUnitName(unitSname);
            }
            aee.setPrdtCellName(ae.getAlarmPoint().getPrdtCell().getSname());
            aee.setAlarmPointTag(ae.getAlarmPoint().getTag());
            aee.setAlarmFlagName(ae.getAlarmFlag().getName());
            aee.setCraftRank(ae.getAlarmPoint().getCraftRank());
            i++;
        }
        return returnAlarmEvent;
    }

    /**
     * 查询车间报警数量评估-报警数-图形显示
     * <p>
     *  * <AUTHOR> 2017-11-08
     *
     * @param workShopCodes 车间编码数组
     * @param beginTime     报警事件的开始间
     * @param endTime       报警事件的结束时间
     * @param dateType      日期枚举类型
     * @return List<AlarmNumberModel> 返回AlarmNumberModel实体类集合
     * @throws Exception 
     */
    @Override
    public List<AlarmNumberConvertEntity> getWorkShopAlarmNumber(String[] workShopCodes, Date beginTime, Date endTime,
                                                                 CommonEnum.DateTypeEnum dateType) throws Exception {
        List<UnitEntity> unitList = null;
        if (workShopCodes != null) {
            unitList = basicDataService.getUnitListByWorkshopIds(workShopCodes, true);
        } else {
            unitList = basicDataService.getUnitList(true);
        }
        String[] unitCodes = unitList.stream().map(x -> x.getStdCode()).distinct().toArray(String[]::new);
        List<Object[]> listAlarmEvent = repo.getWorkShopAlarmNumber(unitCodes, beginTime, endTime, dateType);
        List<WorkshopEntity> wsList = basicDataService.getWorkshopListByWorkshopIds(workShopCodes);
        List<AlarmNumberEntity> pageEntity = new ArrayList<AlarmNumberEntity>();
        List<AlarmNumberEntity> listTemp = new ArrayList<AlarmNumberEntity>();
        for (Object[] an : listAlarmEvent) {
            if (listTemp.size() > 0 && !listTemp.get(0).getAlarmTime().equals((String) an[3])) {
                pageEntity.addAll(listTemp);
                listTemp = new ArrayList<AlarmNumberEntity>();
            }
            for (UnitEntity ue : unitList) {
                if (ue.getStdCode().equals(an[0])) {
                    int i = 0;
                    for (AlarmNumberEntity alnu : listTemp) {
                        if (alnu.getId().equals(ue.getWorkshopCode())) {
                            i++;
                            alnu.setAlarmCount(alnu.getAlarmCount() + (long) an[2]);
                        }
                    }
                    if (i == 0) {
                        AlarmNumberEntity aann = new AlarmNumberEntity();
                        wsList.stream().filter(x -> String.valueOf(x.getStdCode()).equals(ue.getWorkshopCode())).limit(1).forEach(y -> aann.setName(y.getSname()));
                        aann.setId(ue.getWorkshopCode());
                        aann.setAlarmCount((long) an[2]);
                        aann.setAlarmTime((String) an[3]);
                        listTemp.add(aann);
                    }
                }
            }
        }
        if (listTemp.size() > 0) {
            pageEntity.addAll(listTemp);
        }
        pageEntity = pageEntity.stream().sorted(Comparator.comparing(AlarmNumberEntity::getName, ComparatorList.orderByASC())).collect(Collectors.toList());
        long sumAlarmCount = pageEntity.stream().mapToLong(x -> x.getAlarmCount()).sum();
        float totalDayCount = (float) Math.round((float) (endTime.getTime() - beginTime.getTime()) / (float) (24 * 3600 * 1000) * 100) / 100;
        if (totalDayCount < 1)
            totalDayCount = 1;
        float avgAlarmCount = (float) Math.round((float) sumAlarmCount / totalDayCount * 100) / 100;
        if (pageEntity != null && pageEntity.size() > 0) {
            pageEntity.get(0).setSum(sumAlarmCount);
            pageEntity.get(0).setAvg(avgAlarmCount);
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfTip = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdfMonth = new SimpleDateFormat("yyyy-MM");
        List<AlarmNumberConvertEntity> list = new ArrayList<AlarmNumberConvertEntity>();
        List<String> legend = pageEntity.stream().map(x -> x.getName()).distinct().sorted((s1, s2) -> Collator.getInstance(Locale.CHINESE).compare(s1, s2)).collect(Collectors.toList());
        List<String> colorList = new ArrayList<String>();
        List<String> entityList = pageEntity.stream().map(x -> x.getName()).distinct().collect(Collectors.toList());
        for (String strEn : entityList) {
            int i = 0;
            for (String strLe : legend) {
                if (strLe.equals(strEn)) {
                    i = i % 5;
                    break;
                }
                i++;
            }
            switch (i) {
                case 0:
                    colorList.add("#6699CC");
                    break;
                case 1:
                    colorList.add("#669999");
                    break;
                case 2:
                    colorList.add("#CC99CC");
                    break;
                case 3:
                    colorList.add("#66CCCC");
                    break;
                case 4:
                    colorList.add("#9999CC");
                    break;
                default:
                    break;
            }
        }
        AlarmNumberConvertEntity anm = new AlarmNumberConvertEntity();
        anm.setId("-1");
        List<String> xaxis = new ArrayList<String>();
        List<String> tip = new ArrayList<String>();
        List<Long> counts = new ArrayList<Long>();
        String queryTime = " " + CommonPropertiesReader.getValue("query.time");
        Date cal = null;
        switch (dateType) {
            case Hour:
                for (AlarmNumberEntity ane : pageEntity) {
                    if (ane.getId().equals(anm.getId()) == false) {
                        if (xaxis.size() > 0) {
                            Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1));
                            timeda = DateUtils.addHours(timeda, 1);
                            while (endTime.getTime() > timeda.getTime()) {
                                xaxis.add(sdf.format(timeda));
                                counts.add(0L);
                                Date timeBeg = timeda;
                                timeda = DateUtils.addHours(timeda, 1);
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                            }
                        }
                        anm.setXaxis(xaxis);
                        anm.setCounts(counts);
                        anm.setTip(tip);
                        list.add(anm);
                        anm = new AlarmNumberConvertEntity();
                        anm.setId(ane.getId());
                        anm.setName(ane.getName());
                        xaxis = new ArrayList<String>();
                        counts = new ArrayList<Long>();
                        tip = new ArrayList<String>();
                    }
                    if (xaxis.size() == 0) {
                        Date timeda = beginTime;
                        while (sdf.parse(ane.getAlarmTime()).getTime() > timeda.getTime()) {
                            xaxis.add(sdf.format(timeda));
                            counts.add(0L);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addHours(timeda, 1);
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                        }
                    } else {
                        Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1));
                        timeda = DateUtils.addHours(timeda, 1);
                        while (sdf.parse(ane.getAlarmTime()).getTime() > timeda.getTime()) {
                            xaxis.add(sdf.format(timeda));
                            counts.add(0L);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addHours(timeda, 1);
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                        }
                    }
                    cal = DateUtils.addHours(sdf.parse(ane.getAlarmTime()), 1);
                    if (cal.getTime() > endTime.getTime()) {
                        tip.add("从:" + ane.getAlarmTime() + "到" + sdf.format(DateUtils.addSeconds(endTime, +1)) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                        ane.setEndTime(sdf.format(endTime));
                    } else {
                        tip.add("从:" + ane.getAlarmTime() + "到" + sdf.format(cal) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                        ane.setEndTime(sdf.format(DateUtils.addSeconds(cal, -1)));
                    }
                    xaxis.add(ane.getAlarmTime());
                    counts.add(ane.getAlarmCount());
                }
                if (xaxis.size() > 0) {
                    Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1));
                    timeda = DateUtils.addHours(timeda, 1);
                    while (endTime.getTime() > timeda.getTime()) {
                        xaxis.add(sdf.format(timeda));
                        counts.add(0L);
                        Date timeBeg = timeda;
                        timeda = DateUtils.addHours(timeda, 1);
                        tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                    }
                }
                break;
            case Day:
                for (AlarmNumberEntity ane : pageEntity) {
                    if (ane.getId().equals(anm.getId()) == false) {
                        if (xaxis.size() > 0) {
                            Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + queryTime);
                            timeda = DateUtils.addDays(timeda, 1);
                            while (endTime.getTime() > timeda.getTime()) {
                                xaxis.add(sdfTip.format(timeda));
                                counts.add(0L);
                                Date timeBeg = timeda;
                                timeda = DateUtils.addDays(timeda, 1);
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                            }
                        }
                        anm.setXaxis(xaxis);
                        anm.setCounts(counts);
                        anm.setTip(tip);
                        list.add(anm);
                        anm = new AlarmNumberConvertEntity();
                        anm.setId(ane.getId());
                        anm.setName(ane.getName());
                        xaxis = new ArrayList<String>();
                        counts = new ArrayList<Long>();
                        tip = new ArrayList<String>();
                    }
                    if (xaxis.size() == 0) {
                        Date timeda = beginTime;
                        while (sdf.parse(ane.getAlarmTime() + queryTime).getTime() > timeda.getTime()) {
                            xaxis.add(sdfTip.format(timeda));
                            counts.add(0L);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addDays(timeda, 1);
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                        }
                    } else {
                        Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + queryTime);
                        timeda = DateUtils.addDays(timeda, 1);
                        while (sdf.parse(ane.getAlarmTime() + queryTime).getTime() > timeda.getTime()) {
                            xaxis.add(sdfTip.format(timeda));
                            counts.add(0L);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addDays(timeda, 1);
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                        }
                    }
                    cal = DateUtils.addDays(sdf.parse(ane.getAlarmTime() + queryTime), 1);
                    if (cal.getTime() > endTime.getTime()) {
                        tip.add("从:" + ane.getAlarmTime() + queryTime + "到" + sdf.format(DateUtils.addSeconds(endTime, 1)) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                        ane.setEndTime(sdf.format(endTime));
                    } else {
                        tip.add("从:" + ane.getAlarmTime() + queryTime + "到" + sdf.format(cal) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                        ane.setEndTime(sdf.format(DateUtils.addSeconds(cal, -1)));
                    }
                    xaxis.add(ane.getAlarmTime());
                    ane.setAlarmTime(ane.getAlarmTime() + queryTime);
                    counts.add(ane.getAlarmCount());
                }
                if (xaxis.size() > 0) {
                    Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + queryTime);
                    timeda = DateUtils.addDays(timeda, 1);
                    while (endTime.getTime() > timeda.getTime()) {
                        xaxis.add(sdfTip.format(timeda));
                        counts.add(0L);
                        Date timeBeg = timeda;
                        timeda = DateUtils.addDays(timeda, 1);
                        tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                    }
                }
                break;
            case Week:
                for (AlarmNumberEntity ane : pageEntity) {
                    if (ane.getId().equals(anm.getId()) == false) {
                        if (xaxis.size() > 0) {
                            Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + queryTime);
                            timeda = DateUtils.addWeeks(timeda, 1);
                            while (endTime.getTime() > timeda.getTime()) {
                                xaxis.add(sdfTip.format(timeda));
                                counts.add(0L);
                                Date timeBeg = timeda;
                                timeda = DateUtils.addWeeks(timeda, 1);
                                tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                            }
                        }
                        anm.setXaxis(xaxis);
                        anm.setCounts(counts);
                        anm.setTip(tip);
                        list.add(anm);
                        anm = new AlarmNumberConvertEntity();
                        anm.setId(ane.getId());
                        anm.setName(ane.getName());
                        xaxis = new ArrayList<String>();
                        counts = new ArrayList<Long>();
                        tip = new ArrayList<String>();
                    }
                    if (xaxis.size() == 0) {
                        Date timeda = beginTime;
                        while (sdf.parse(ane.getAlarmTime() + queryTime).getTime() > timeda.getTime()) {
                            xaxis.add(sdfTip.format(timeda));
                            counts.add(0L);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addWeeks(timeda, 1);
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                        }
                    } else {
                        Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + queryTime);
                        timeda = DateUtils.addWeeks(timeda, 1);
                        while (sdf.parse(ane.getAlarmTime() + queryTime).getTime() > timeda.getTime()) {
                            xaxis.add(sdfTip.format(timeda));
                            counts.add(0L);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addWeeks(timeda, 1);
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                        }
                    }
                    cal = DateUtils.addWeeks(sdf.parse(ane.getAlarmTime() + queryTime), 1);
                    if (cal.getTime() > endTime.getTime()) {
                        tip.add("从:" + ane.getAlarmTime() + queryTime + "到" + sdf.format(DateUtils.addSeconds(endTime, 1)) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                        ane.setEndTime(sdf.format(endTime));
                    } else {
                        tip.add("从:" + ane.getAlarmTime() + queryTime + "到" + sdf.format(cal) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                        ane.setEndTime(sdf.format(DateUtils.addSeconds(cal, -1)));
                    }
                    xaxis.add(ane.getAlarmTime());
                    ane.setAlarmTime(ane.getAlarmTime() + queryTime);
                    counts.add(ane.getAlarmCount());
                }
                if (xaxis.size() > 0) {
                    Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + queryTime);
                    timeda = DateUtils.addWeeks(timeda, 1);
                    while (endTime.getTime() > timeda.getTime()) {
                        xaxis.add(sdfTip.format(timeda));
                        counts.add(0L);
                        Date timeBeg = timeda;
                        timeda = DateUtils.addWeeks(timeda, 1);
                        tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                    }
                }
                break;
            case Month:
                for (AlarmNumberEntity ane : pageEntity) {
                    if (ane.getId().equals(anm.getId()) == false) {
                        if (xaxis.size() > 0) {
                            Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + "-01" + queryTime);
                            timeda = DateUtils.addMonths(timeda, 1);
                            while (endTime.getTime() > timeda.getTime()) {
                                xaxis.add(sdfMonth.format(timeda));
                                counts.add(0L);
                                Date timeBeg = timeda;
                                timeda = DateUtils.addMonths(timeda, 1);
                                if (timeda.getTime() > endTime.getTime()) {
                                    tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(endTime) + "<br>" + anm.getName() + ":" + 0 + "");
                                } else {
                                    tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                                }
                            }
                        }
                        anm.setXaxis(xaxis);
                        anm.setCounts(counts);
                        anm.setTip(tip);
                        list.add(anm);
                        anm = new AlarmNumberConvertEntity();
                        anm.setId(ane.getId());
                        anm.setName(ane.getName());
                        xaxis = new ArrayList<String>();
                        counts = new ArrayList<Long>();
                        tip = new ArrayList<String>();
                    }
                    if (xaxis.size() == 0) {
                        Date timeda = beginTime;
                        while (sdf.parse(ane.getAlarmTime() + "-01" + queryTime).getTime() > timeda.getTime()) {
                            xaxis.add(sdfMonth.format(timeda));
                            counts.add(0L);
                            Date timeBeg = timeda;
                            if (timeda.getTime() == beginTime.getTime()) {
                                timeda = sdf.parse(sdfMonth.format(timeda) + "-01" + queryTime);
                            }
                            timeda = DateUtils.addMonths(timeda, 1);
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                        }
                    } else {
                        Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + "-01" + queryTime);
                        timeda = DateUtils.addMonths(timeda, 1);
                        while (sdf.parse(ane.getAlarmTime() + "-01" + queryTime).getTime() > timeda.getTime()) {
                            xaxis.add(sdfMonth.format(timeda));
                            counts.add(0L);
                            Date timeBeg = timeda;
                            timeda = DateUtils.addMonths(timeda, 1);
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + ane.getName() + ":" + 0 + "");
                        }
                    }
                    String tipStr = "";
                    xaxis.add(ane.getAlarmTime());
                    String alarmTime = ane.getAlarmTime();
                    if (beginTime.getTime() > sdf.parse(alarmTime + "-01" + queryTime).getTime()) {
                        tipStr = "从:" + sdfTip.format(beginTime) + queryTime + "到";
                        ane.setAlarmTime(sdfTip.format(beginTime) + queryTime);
                    } else {
                        tipStr = "从:" + alarmTime + "-01" + queryTime + "到";
                        ane.setAlarmTime(ane.getAlarmTime() + "-01" + queryTime);
                    }
                    cal = DateUtils.addMonths(sdf.parse(alarmTime + "-01" + queryTime), 1);
                    if (cal.getTime() > endTime.getTime()) {
                        tipStr += sdf.format(DateUtils.addSeconds(endTime, 1));
                        ane.setEndTime(sdf.format(endTime));
                    } else {
                        tipStr += sdf.format(cal);
                        ane.setEndTime(sdf.format(DateUtils.addSeconds(cal, -1)));
                    }
                    tip.add(tipStr += "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
                    counts.add(ane.getAlarmCount());
                }
                if (xaxis.size() > 0) {
                    Date timeda = sdf.parse(xaxis.get(xaxis.size() - 1) + "-01" + queryTime);
                    timeda = DateUtils.addMonths(timeda, 1);
                    while (endTime.getTime() > timeda.getTime()) {
                        xaxis.add(sdfMonth.format(timeda));
                        counts.add(0L);
                        Date timeBeg = timeda;
                        timeda = DateUtils.addMonths(timeda, 1);
                        if (timeda.getTime() > endTime.getTime()) {
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(endTime) + "<br>" + anm.getName() + ":" + 0 + "");
                        } else {
                            tip.add("从:" + sdf.format(timeBeg) + "到" + sdf.format(timeda) + "<br>" + anm.getName() + ":" + 0 + "");
                        }
                    }
                }
                break;
            default:
                break;
        }
        anm.setXaxis(xaxis);
        anm.setCounts(counts);
        anm.setTip(tip);
        list.add(anm);
        list.remove(0);
        if (list.size() > 0) {
            list.get(0).setList(pageEntity.stream().sorted(Comparator.comparing(AlarmNumberEntity::getAlarmTime).thenComparing(AlarmNumberEntity::getName, ComparatorList.orderByASC())).collect(Collectors.toList()));
            list.get(0).setSum(pageEntity.get(0).getSum());
            list.get(0).setAvg(pageEntity.get(0).getAvg());
            list.get(0).setLegend(legend);
            list.get(0).setColor(colorList);
        }

        return list;
    }

    /**
     * 查询车间报警数量评估-报警数-单元显示
     * <p>
     *  * <AUTHOR> 2017-11-08
     *
     * @param workshopCodes 车间编码
     * @param beginTime     报警事件的开始间
     * @param endTime       报警事件的结束时间
     * @return List<AlarmNumberModel> 返回AlarmNumberModel实体类集合
     * @throws Exception 
     */
    @Override
    public List<AlarmNumberConvertEntity> getWorkShopAlarmNumberUnit(String[] workshopCodes, Date beginTime, Date endTime) throws Exception {
        List<UnitEntity> unitList = null;
        if (workshopCodes != null) {
            unitList = basicDataService.getUnitListByWorkshopIds(workshopCodes, true);
        } else {
            unitList = basicDataService.getUnitList(true);
        }
        String[] unitCodes = unitList.stream().map(x -> x.getStdCode()).distinct().toArray(String[]::new);
        List<Object[]> listAlarmEvent = repo.getWorkShopAlarmNumberUnit(unitCodes, beginTime, endTime);
        List<WorkshopEntity> wsList = basicDataService.getWorkshopListByWorkshopIds(workshopCodes);
        List<AlarmNumberEntity> pageEntity = new ArrayList<AlarmNumberEntity>();
        List<AlarmNumberEntity> listTemp = new ArrayList<AlarmNumberEntity>();
        for (UnitEntity ue : unitList) {
            for (Object[] an : listAlarmEvent) {
                if (ue.getStdCode().equals(an[0])) {
                    int i = 0;
                    for (AlarmNumberEntity alnu : listTemp) {
                        if (alnu.getId().equals(ue.getWorkshopCode())) {
                            i++;
                            alnu.setAlarmCount(alnu.getAlarmCount() + (long) an[2]);
                        }
                    }
                    if (i == 0) {
                        AlarmNumberEntity aann = new AlarmNumberEntity();
                        WorkshopEntity we = wsList.stream().filter(x -> x.getStdCode().equals(ue.getWorkshopCode())).findFirst().orElse(new WorkshopEntity());
                        aann.setName(we.getSname());
                        aann.setId(ue.getWorkshopCode());
                        aann.setAlarmCount((long) an[2]);
                        listTemp.add(aann);
                    }
                }
            }
        }
        if (listTemp.size() > 0) {
            pageEntity.addAll(listTemp);
        }

        List<AlarmNumberConvertEntity> list = new ArrayList<AlarmNumberConvertEntity>();
        pageEntity = pageEntity.stream().sorted(Comparator.comparing(AlarmNumberEntity::getName, ComparatorList.orderByASC())).collect(Collectors.toList());
        AlarmNumberConvertEntity anm = new AlarmNumberConvertEntity();
        anm.setId("-1");
        List<String> xaxis = new ArrayList<String>();
        List<String> tip = new ArrayList<String>();
        List<Long> counts = new ArrayList<Long>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (AlarmNumberEntity ane : pageEntity) {
            if (ane.getId().equals(anm.getId()) == false) {
                anm.setXaxis(xaxis);
                anm.setCounts(counts);
                anm.setTip(tip);
                list.add(anm);
                anm = new AlarmNumberConvertEntity();
                anm.setId(ane.getId());
                anm.setName(ane.getName());
                xaxis = new ArrayList<String>();
                counts = new ArrayList<Long>();
                tip = new ArrayList<String>();
            }
            xaxis.add(ane.getName());
            counts.add(ane.getAlarmCount());
            tip.add("从:" + sdf.format(beginTime) + "到" + sdf.format(endTime) + "<br>" + ane.getName() + ":" + ane.getAlarmCount() + "");
            ane.setAlarmTime(sdf.format(beginTime));
            ane.setEndTime(sdf.format(DateUtils.addSeconds(endTime, -1)));
        }
        anm.setXaxis(xaxis);
        anm.setCounts(counts);
        anm.setTip(tip);
        list.add(anm);
        list.remove(0);
        if (list.size() > 0) {
            list.get(0).setList(pageEntity);
        }

        return list;
    }

    /**
     * 查询最频繁报警详情集合
     * <p>
     *  * <AUTHOR> 2018-04-13
     *
     * @param alarmPointTag 位号
     * @param alarmFlagId   报警标识id
     * @param alarmTime     报警时间
     * @param endTime       报结束时间时间
     * @param page          查询分页对象
     * @return PaginationBean<AlarmEventEntity> 返回AlarmEventEntity实体分页对象
     * @throws Exception 
     */
    @SuppressWarnings("unchecked")
    public PaginationBean<AlarmEventEntity> getAlarmDtail(String alarmPointTag, Long alarmFlagId, Date alarmTime, Date endTime, Pagination page) throws Exception {
        List<UnitEntity> units = basicDataService.getUnitList(true);
        Long[] eventTypeIds = new Long[]{1001L};
        PaginationBean<AlarmEvent> listAlarmEvent = eventRepo.getAlarmDtail(alarmPointTag, alarmFlagId, eventTypeIds, alarmTime, endTime, CommonEnum.TimeFilterTypeEnum.ALarmTime.getName(), page);
        PaginationBean<AlarmEventEntity> returnAlarmEvent = new PaginationBean<AlarmEventEntity>(page,
                listAlarmEvent.getTotal());
        returnAlarmEvent.setPageList(ObjectConverter.listConverter(listAlarmEvent.getPageList(), AlarmEventEntity.class));
        // 映射字段
        for (int i = 0; i < returnAlarmEvent.getPageList().size(); i++) {
            AlarmEventEntity alarmEventEntity = returnAlarmEvent.getPageList().get(i);
            AlarmEvent alarmEvent = listAlarmEvent.getPageList().get(i);
            // 填充装置简称
            UnitEntity unit = units.stream().filter(u -> alarmEvent.getAlarmPoint().getPrdtCell().getUnitId().equals(u.getStdCode())).findFirst().orElse(new UnitEntity());
            alarmEventEntity.setUnitName(unit.getSname());
            // 填充生产单元简称
            alarmEventEntity.setPrdtCellName(alarmEvent.getAlarmPoint().getPrdtCell().getSname());
            // 填充报警点位号
            alarmEventEntity.setAlarmPointTag(alarmEvent.getAlarmPoint().getTag());
            // 填充报警标识名称
            alarmEventEntity.setAlarmFlagName(alarmEvent.getAlarmFlag().getName());
            // 填充事件类型
            alarmEventEntity.setEventTypeName(alarmEvent.getEventType().getName());
            // 填充级别
            alarmEventEntity.setCraftRank(alarmEvent.getAlarmPoint().getCraftRank());
            alarmEventEntity.setCraftRankName(alarmEventEntity.getCraftRankName());
            //工艺卡片值 联锁值赋值
            Double culv = alarmEvent.getAlarmPoint().getCraftUpLimitValue();//工艺卡片上限值
            Double cdlv = alarmEvent.getAlarmPoint().getCraftDownLimitValue();//工艺卡片下限值
            Integer culi = alarmEvent.getAlarmPoint().getCraftUpLimitInclude();//工艺卡片上限值是否包含(1是；0否)
            Integer cdli = alarmEvent.getAlarmPoint().getCraftDownLimitInclude();//工艺卡片下限值是否包含(1是；0否)
            Double iulv = alarmEvent.getAlarmPoint().getInterlockUpLimitValue();//联锁上限值
            Double idlv = alarmEvent.getAlarmPoint().getInterlockDownLimitValue();//联锁下限值
            Integer iuli = alarmEvent.getAlarmPoint().getInterlockUpLimitInclude();//联锁上限值是否包含(1是；0否)
            Integer idli = alarmEvent.getAlarmPoint().getInterlockDownLimitInclude();//联锁下限值是否包含(1是；0否)
            if (culv != null && cdlv != null) {
                String culvStr = changeDouble(culv);
                String cdlvStr = changeDouble(cdlv);
                alarmEventEntity.setCraftLimitValue(cdlvStr + "~" + culvStr);
            } else if (culv != null && cdlv == null) {
                if (culi != null && culi.intValue() == 1) {
                    String culvStr = changeDouble(culv);
                    alarmEventEntity.setCraftLimitValue("≤" + culvStr);
                } else if (culi != null && culi.intValue() == 0) {
                    String culvStr = changeDouble(culv);
                    alarmEventEntity.setCraftLimitValue("<" + culvStr);
                }
            } else if (culv == null && cdlv != null) {
                if (cdli != null && cdli.intValue() == 1) {
                    String cdlvStr = changeDouble(cdlv);
                    alarmEventEntity.setCraftLimitValue("≥" + cdlvStr);
                } else if (cdli != null && cdli.intValue() == 0) {
                    String cdlvStr = changeDouble(cdlv);
                    alarmEventEntity.setCraftLimitValue(">" + cdlvStr);
                }
            } else if (culv == null && cdlv == null) {
                alarmEventEntity.setCraftLimitValue("");
            }
            if (iulv != null && idlv != null) {
                String iulvStr = changeDouble(iulv);
                String idlvStr = changeDouble(idlv);
                alarmEventEntity.setInterlockLimitValue(idlvStr + "~" + iulvStr);
            } else if (iulv != null && idlv == null) {
                if (iuli.intValue() == 1) {
                    String iulvStr = changeDouble(iulv);
                    alarmEventEntity.setInterlockLimitValue("≤" + iulvStr);
                } else if (iuli.intValue() == 0) {
                    String iulvStr = changeDouble(iulv);
                    alarmEventEntity.setInterlockLimitValue("<" + iulvStr);
                }
            } else if (iulv == null && idlv != null) {
                if (idli.intValue() == 1) {
                    String idlvStr = changeDouble(idlv);
                    alarmEventEntity.setInterlockLimitValue("≥" + idlvStr);
                } else if (idli.intValue() == 0) {
                    String idlvStr = changeDouble(idlv);
                    alarmEventEntity.setInterlockLimitValue(">" + idlvStr);
                }
            } else if (iulv == null && idlv == null) {
                alarmEventEntity.setInterlockLimitValue("");
            }
            //处理限值
            if (alarmEventEntity.getEventTypeId().toString().startsWith("10")) {
                Long flag = alarmEventEntity.getAlarmFlagId();
                if (flag == CommonEnum.AlarmFlagEnum.PVHH.getIndex()
                        && alarmEvent.getAlarmPoint().getAlarmPointHH() != null) {
                    alarmEventEntity.setLimitValue(alarmEvent.getAlarmPoint().getAlarmPointHH());
                } else if (flag == CommonEnum.AlarmFlagEnum.PVHI.getIndex()
                        && alarmEvent.getAlarmPoint().getAlarmPointHI() != null) {
                    alarmEventEntity.setLimitValue(alarmEvent.getAlarmPoint().getAlarmPointHI());
                } else if (flag == CommonEnum.AlarmFlagEnum.PVLL.getIndex()
                        && alarmEvent.getAlarmPoint().getAlarmPointLL() != null) {
                    alarmEventEntity.setLimitValue(alarmEvent.getAlarmPoint().getAlarmPointLL());
                } else if (flag == CommonEnum.AlarmFlagEnum.PVLO.getIndex()
                        && alarmEvent.getAlarmPoint().getAlarmPointLO() != null) {
                    alarmEventEntity.setLimitValue(alarmEvent.getAlarmPoint().getAlarmPointLO());
                } else {
                    alarmEventEntity.setLimitValue(null);
                }

            } else {
                alarmEventEntity.setLimitValue(null);
            }
        }
        return returnAlarmEvent;
    }

    //Double类型转String
    private String changeDouble(Double num) {
        if ((num + "").endsWith(".0")) {
            return num.intValue() + "";
        }
        return num + "";
    }

    /**
     * 报警详情评估前20数据
     *
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param day           总天数
     * @param startFlag     开始时间标记
     * @param endFlag       结束时间标记
     * @param workUnitCodes 工厂所属装置编码数组
     * @param unitCodes     装置编码数组
     * @param prdtIds       生产单元ID数组
     * @param topType       Top20,Top10切换选择
     * @return
     * <AUTHOR> 2017-11-08
     */
    @SuppressWarnings("unchecked")
    private List<AlarmNumberAssessDataEntity> alarmNumberAssessTop20(Date startTime, Date endTime, Long day, String startFlag, String endFlag, String[] workUnitCodes, String[] unitCodes,
                                                                     Long[] prdtIds, Integer topType) throws Exception {
        List<AlarmNumberAssessDataEntity> alarmEventList = new ArrayList<AlarmNumberAssessDataEntity>();
        List<Object[]> list = eventRepo.getAlarmNumberTop20(unitCodes, prdtIds, workUnitCodes, startTime, endTime, startFlag, endFlag, topType);
        Long sum = list.stream().mapToLong(x -> (Long) x[2]).sum();
        unitCodes = list.stream().map(x -> x[5].toString()).distinct().toArray(String[]::new);
        List<UnitEntity> unitList = basicDataService.getUnitListByIds(unitCodes, true);
        for (Object[] o : list) {
            AlarmNumberAssessDataEntity entity = new AlarmNumberAssessDataEntity();
            entity.setTag((String) o[0]);
            entity.setLocation((String) o[8]);
            entity.setAlarmFlag((String) o[1]);
            entity.setAlarmFlagId((Long) o[7]);
            entity.setAlarmCount((Long) o[2]);
            entity.setPriority((Integer) o[3]);
            entity.setPrdtCellName((String) o[4]);
            entity.setUnitId(o[5].toString());
            entity.setPriorityName(AlarmPriorityEnum.getName(entity.getPriority()));
            entity.setPercent(String.format("%.2f", (Double.parseDouble(entity.getAlarmCount() + "") / Double.parseDouble(sum + ""))));
            entity.setSum(sum + "");
            UnitEntity unit = unitList.stream().filter(y -> entity.getUnitId().equals(y.getStdCode())).findFirst().orElse(new UnitEntity());
            entity.setUnitName(unit.getSname());
            entity.setName(unit.getName());
            alarmEventList.add(entity);
        }
        if (alarmEventList != null && alarmEventList.size() > 20) {
            alarmEventList = alarmEventList.subList(0, 20);
        }
        return alarmEventList;
    }

    /**
     * 报警详情评估前20数据
     *
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param day           总天数
     * @param startFlag     开始时间标记
     * @param endFlag       结束时间标记
     * @param workUnitCodes 工厂所属装置编码数组
     * @param unitCodes     装置编码数组
     * @param prdtIds       生产单元ID数组
     * @param topType       Top20,Top10切换选择
     * @param priority      优先级
     * @return
     * <AUTHOR> 2019-09-30
     */
    private List<AlarmNumberAssessDataEntity> alarmNumberAssessTop20(Date startTime, Date endTime, Long[] alarmFlagId,
                                                                     Long day, String startFlag, String endFlag,
                                                                     String[] workUnitCodes, String[] unitCodes,
                                                                     Long[] prdtIds, Integer topType, Integer[] priority,
                                                                     Boolean priorityFlag, Integer isElimination) throws Exception {
        List<AlarmNumberAssessDataEntity> alarmEventList = new ArrayList<AlarmNumberAssessDataEntity>();
        List<Object[]> list = eventRepo.getAlarmNumberTop20(unitCodes, alarmFlagId, prdtIds, workUnitCodes, startTime,
                endTime, startFlag, endFlag, topType, priority, priorityFlag, isElimination);
        Long sum = list.stream().mapToLong(x -> x[2] != null ? Long.valueOf(x[2].toString()) : 0L).sum();
        List<UnitEntity> unitList = basicDataService.getUnitList(false);
        for (Object[] o : list) {
            AlarmNumberAssessDataEntity entity = new AlarmNumberAssessDataEntity();
            entity.setTag(o[0] != null ? String.valueOf(o[0]) : null);
            entity.setAlarmFlag(o[1] != null ? String.valueOf(o[1]) : null);
            entity.setAlarmFlagId(o[7] != null ? Long.valueOf(o[7].toString()) : null);
            entity.setAlarmCount(o[2] != null ? Long.valueOf(o[2].toString()) : 0L);
            entity.setPriority(o[3] != null ? Integer.valueOf(o[3].toString()) : null);
            entity.setPrdtCellName(o[4] != null ? String.valueOf(o[4]) : null);
            entity.setLocation(o[8] != null ? String.valueOf(o[8]) : null);
            entity.setPriorityName(entity.getPriority() != null ? AlarmPriorityEnum.getName(entity.getPriority()) : "空");
            entity.setPercent(String.format("%.2f", (Double.parseDouble(entity.getAlarmCount() + "") / Double.parseDouble(sum + ""))));
            entity.setSum(sum + "");
            if (o[5] != null) {
                entity.setUnitId(String.valueOf(o[5]));
                UnitEntity unit = unitList.stream().filter(y -> entity.getUnitId().equals(y.getStdCode())).findFirst().orElse(new UnitEntity());
                if (unit != null) {
                    entity.setUnitName(unit.getSname());
                }
            }
            entity.setMonitorTypeName(o[9] != null ? CommonEnum.MonitorTypeEnum.getName(Integer.valueOf(o[9].toString())) : null );
            alarmEventList.add(entity);
        }
        if (alarmEventList != null && alarmEventList.size() > 20) {
            alarmEventList = alarmEventList.subList(0, 20);
        }
        return alarmEventList;
    }

    /**
     * 报警详情评估前20数据（新）
     *
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param day           总天数
     * @param startFlag     开始时间标记
     * @param endFlag       结束时间标记
     * @param workUnitCodes 工厂所属装置编码数组
     * @param unitCodes     装置编码数组
     * @param prdtIds       生产单元ID数组
     * @param topType       Top20,Top10切换选择
     * @param priority      优先级
     * @return
     * <AUTHOR> 2019-09-30
     */
    @SuppressWarnings("unchecked")
    private PaginationBean<AlarmNumberAssessDataEntity> alarmNumberAssessAll(String[] workshopCodes, String[] unitCode,
                                                                             Long[] prdtCellId, Date startTime, Date endTime,
                                                                             Long[] alarmFlagId, Long day, String startFlag,
                                                                             String endFlag, String[] workUnitCodes, String[] unitCodes,
                                                                             Long[] prdtIds, Integer topType, Integer[] priority,
                                                                             Boolean priorityFlag, Integer isElimination, Pagination page) throws Exception {
        List<AlarmNumberAssessDataEntity> alarmEventList = new ArrayList<AlarmNumberAssessDataEntity>();
        List<Object[]> list = eventRepo.getAlarmNumberAll(workshopCodes, unitCode, prdtCellId, unitCodes, alarmFlagId, prdtIds, workUnitCodes, startTime, endTime, startFlag, endFlag, topType, priority, priorityFlag, isElimination, page);
        List<Object[]> listTotal = eventRepo.getAlarmNumberAllTotal(workshopCodes, unitCode, prdtCellId, unitCodes, alarmFlagId, prdtIds, workUnitCodes, startTime, endTime, startFlag, endFlag, topType, priority, priorityFlag, isElimination);

        long tatal = 0;
        if (listTotal != null && listTotal.size() > 0) {
            tatal = (long) listTotal.size();
        }
        PaginationBean<AlarmNumberAssessDataEntity> alarmNumberAssessDataEntityPaginationBean = new PaginationBean<>(page, tatal);
        if (CollectionUtils.isNotEmpty(list)) {
            Long sum = list.stream().mapToLong(x -> ((BigInteger) x[2]).longValue()).sum();
            unitCodes = list.stream().map(x -> x[5].toString()).distinct().toArray(String[]::new);
            List<UnitEntity> unitList = basicDataService.getUnitListByIds(unitCodes, false);
            for (Object[] o : list) {
                AlarmNumberAssessDataEntity entity = new AlarmNumberAssessDataEntity();
                entity.setTag((String) o[0]);
                entity.setAlarmFlag((String) o[1]);
                entity.setAlarmFlagId(o[7] == null ? null : ((BigInteger) o[7]).longValue());
                entity.setAlarmCount(((BigInteger) o[2]).longValue());
                entity.setPriority(((BigInteger) o[3]).intValue());
                entity.setPrdtCellName((String) o[4]);
                entity.setUnitId(o[5].toString());
                entity.setLocation(o[8] == null ? null : o[8].toString());
                entity.setPriorityName(entity.getPriority() != null ? AlarmPriorityEnum.getName(entity.getPriority()) : "空");
                entity.setPercent(String.format("%.2f", (Double.parseDouble(entity.getAlarmCount() + "") / Double.parseDouble(sum + ""))));
                entity.setSum(sum + "");
                UnitEntity unit = unitList.stream().filter(y -> entity.getUnitId().equals(y.getStdCode())).findFirst().orElse(new UnitEntity());
                entity.setUnitName(unit.getSname());
//            alarmEventList.add(entity);
                alarmNumberAssessDataEntityPaginationBean.getPageList().add(entity);
            }
        }
        return alarmNumberAssessDataEntityPaginationBean;
    }
    /**
     * 报警详情评估前20数据（新）
     *
     * @param startTime     时间范围起始
     * @param endTime       时间范围结束
     * @param day           总天数
     * @param startFlag     开始时间标记
     * @param endFlag       结束时间标记
     * @param workUnitCodes 工厂所属装置编码数组
     * @param unitCodes     装置编码数组
     * @param prdtIds       生产单元ID数组
     * @param topType       Top20,Top10切换选择
     * @param priority      优先级
     * @return
     * <AUTHOR> 2019-09-30
     */
    @SuppressWarnings("unchecked")
    private PaginationBean<AlarmNumberAssessDataEntity> alarmNumberAssessAllType(String[] workshopCodes, String[] unitCode,
                                                                             Long[] prdtCellId, Date startTime, Date endTime,
                                                                             Long[] alarmFlagId, Long day, String startFlag,
                                                                             String endFlag, String[] workUnitCodes, String[] unitCodes,
                                                                             Long[] prdtIds, Integer topType, Integer[] monitorType,
                                                                             Boolean priorityFlag, Integer isElimination, Pagination page) throws Exception {
        List<AlarmNumberAssessDataEntity> alarmEventList = new ArrayList<AlarmNumberAssessDataEntity>();
        List<Object[]> list = eventRepo.getAlarmNumberAllType(workshopCodes, unitCode, prdtCellId, unitCodes, alarmFlagId, prdtIds, workUnitCodes, startTime, endTime, startFlag, endFlag, topType, monitorType, priorityFlag, isElimination, page);
        List<Object[]> listTotal = eventRepo.getAlarmNumberAllTotalType(workshopCodes, unitCode, prdtCellId, unitCodes, alarmFlagId, prdtIds, workUnitCodes, startTime, endTime, startFlag, endFlag, topType, monitorType, priorityFlag, isElimination);

        long tatal = 0;
        if (listTotal != null && listTotal.size() > 0) {
            tatal = (long) listTotal.size();
        }
        PaginationBean<AlarmNumberAssessDataEntity> alarmNumberAssessDataEntityPaginationBean = new PaginationBean<>(page, tatal);
        if (CollectionUtils.isNotEmpty(list)) {
            Long sum = list.stream().mapToLong(x -> ((BigInteger) x[2]).longValue()).sum();
            unitCodes = list.stream().map(x -> x[5].toString()).distinct().toArray(String[]::new);
            List<UnitEntity> unitList = basicDataService.getUnitListByIds(unitCodes, false);
            for (Object[] o : list) {
                AlarmNumberAssessDataEntity entity = new AlarmNumberAssessDataEntity();
                entity.setTag((String) o[0]);
                entity.setAlarmFlag((String) o[1]);
                entity.setAlarmFlagId(o[7] == null ? null : ((BigInteger) o[7]).longValue());
                entity.setAlarmCount(((BigInteger) o[2]).longValue());
                entity.setPriority(((BigInteger) o[3]).intValue());
                entity.setPrdtCellName((String) o[4]);
                entity.setUnitId(o[5].toString());
                entity.setLocation(o[8] == null ? null : o[8].toString());
                entity.setPriorityName(entity.getPriority() != null ? AlarmPriorityEnum.getName(entity.getPriority()) : "空");
                entity.setPercent(String.format("%.2f", (Double.parseDouble(entity.getAlarmCount() + "") / Double.parseDouble(sum + ""))));
                entity.setSum(sum + "");
                UnitEntity unit = unitList.stream().filter(y -> entity.getUnitId().equals(y.getStdCode())).findFirst().orElse(new UnitEntity());
                entity.setUnitName(unit.getSname());
//            alarmEventList.add(entity);
                alarmNumberAssessDataEntityPaginationBean.getPageList().add(entity);
            }
        }
        return alarmNumberAssessDataEntityPaginationBean;
    }

    @Override
    public List<AlarmNumberAssessDateTopEntity> getAlarmNumberAssessTop3(String[] workUnitId, String[] unitIds, Date startTime, Date endTime, Integer[] priority, Boolean priorityFlag, Integer topType) throws Exception {
	/*	Map<String, Object> timeMap = basicDataService.getSearchTime(startTime, endTime);
        startTime = (Date) timeMap.get("startDate");
        endTime = (Date) timeMap.get("endDate");
		Long day=(endTime.getTime()-startTime.getTime())/(1000*60*60*24);
		String startFlag = (String) timeMap.get("startFlag");*/
        Long day = null;
        String startFlag = ">=";
        String endFlag = "<";

        List<UnitEntity> unitList = basicDataService.getUnitList(false);

        String[] workUnitIds = null;
        Long[] prdtIds = null;
        if ((null == unitIds || unitIds.length == 0) && null != workUnitId && workUnitId.length > 0) {
            workUnitIds = basicDataService.getUnitListByWorkshopIds(workUnitId, true).stream().map(x -> x.getStdCode()).toArray(String[]::new);
            if (null == workUnitIds || workUnitIds.length == 0) {
                return null;
            }
        }
        List<AlarmNumberAssessDataEntity> alarmNumberAssessDataEntities = alarmNumberAssessTop20(startTime, endTime, null, day, startFlag, endFlag, workUnitIds, unitIds,
                prdtIds, topType, priority, priorityFlag, 1);
        List<AlarmNumberAssessDateTopEntity> returnEntity = new ArrayList<>();

        for (AlarmNumberAssessDataEntity a : alarmNumberAssessDataEntities) {
            AlarmNumberAssessDateTopEntity topEntity = new AlarmNumberAssessDateTopEntity();
            topEntity.setTag(a.getTag());
            topEntity.setUnitCode(a.getUnitId());
            if (StringUtils.isNotBlank(a.getName())) {
                topEntity.setUnitName(a.getName());
            } else {
                UnitEntity unitEntity = unitList.stream().filter(x -> x.getStdCode().equals(a.getUnitId())).findFirst().orElse(new UnitEntity());
                String name = unitEntity.getName();
                topEntity.setUnitName(name);
            }
            if (StringUtils.isNotBlank(a.getUnitName())) {
                topEntity.setUnitSName(a.getUnitName());
            } else {
                UnitEntity unitEntity = unitList.stream().filter(x -> x.getStdCode().equals(a.getUnitId())).findFirst().orElse(new UnitEntity());
                String sName = unitEntity.getSname();
                topEntity.setUnitSName(sName);
            }
            topEntity.setAlarmCount(a.getAlarmCount());
            topEntity.setAlarmFlagId(a.getAlarmFlagId());
            topEntity.setAlarmFlag(a.getAlarmFlag());
            topEntity.setPriority(a.getPriority());
            topEntity.setPriorityName(a.getPriorityName());
            topEntity.setLocation(a.getLocation());
            returnEntity.add(topEntity);
        }

        return returnEntity;
    }

    @Override
    public List<AlarmUnitCountEntity> unitAlarm(String[] unitIds, Date queryStartTime, Date queryEndTime, CommonEnum.DateTypeEnum dateTypeEnum) throws Exception {
        List<AlarmUnitCountEntity> returnList = new ArrayList<>();
        List<UnitEntity> unitList = basicDataService.getUnitListByIds(unitIds, false);
        List<AlarmNumberConvertEntity> list = getAlarmNumber(unitIds, null, queryStartTime, queryEndTime, dateTypeEnum, "", new Long[]{});
        if (null != list && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                AlarmNumberConvertEntity alarmNumberConvertEntity = list.get(i);
                UnitEntity unit = unitList.stream().filter(y -> alarmNumberConvertEntity.getId().equals(y.getStdCode())).findFirst().orElse(new UnitEntity());
                for (int j = 0; j < alarmNumberConvertEntity.getXaxis().size(); j++) {
                    AlarmUnitCountEntity entity = new AlarmUnitCountEntity();
                    entity.setUnitName(unit.getName());
                    entity.setUnitSName(unit.getSname());
                    entity.setUnitCode(alarmNumberConvertEntity.getId());
                    entity.setAlarmTime(alarmNumberConvertEntity.getXaxis().get(j));
                    entity.setAlarmCount(alarmNumberConvertEntity.getCounts().get(j));
                    returnList.add(entity);
                }

            }
			/*List<AlarmNumberEntity> entityList = list.get(0).getList();

			for (AlarmNumberEntity alarmNumberEntity : entityList) {
                AlarmUnitCountEntity entity = new AlarmUnitCountEntity();
                entity.setUnitName(alarmNumberEntity.getUnitName());
                entity.setUnitSName(alarmNumberEntity.getName());
                entity.setUnitCode(alarmNumberEntity.getId());
                entity.setAlarmTime(alarmNumberEntity.getAlarmTime().substring(0, 10));
                entity.setAlarmCount(alarmNumberEntity.getAlarmCount());
                returnList.add(entity);
            }*/
            for (String id : unitIds) {
                if (!list.stream().filter(w -> w.getId().equals(id)).findAny().isPresent()) {
                    AlarmUnitCountEntity entity = new AlarmUnitCountEntity();
                    entity.setUnitCode(id);
                    UnitEntity unit = unitList.stream().filter(y -> id.equals(y.getStdCode())).findFirst().orElse(new UnitEntity());
                    entity.setUnitName(unit.getName());
                    entity.setUnitSName(unit.getSname());
                    entity.setAlarmCount(0L);
                    returnList.add(entity);
                }
            }
        } else {
            for (String id : unitIds) {
                AlarmUnitCountEntity entity = new AlarmUnitCountEntity();
                entity.setUnitCode(id);
                UnitEntity unit = unitList.stream().filter(y -> id.equals(y.getStdCode())).findFirst().orElse(new UnitEntity());
                entity.setUnitName(unit.getName());
                entity.setUnitSName(unit.getSname());
                entity.setAlarmCount(0L);
                returnList.add(entity);
            }
        }
        return returnList;
    }

    @Override
    public List<DeptmUnitAlarmStattEntity> deptmUnitAlarmStatt(String[] workUnitIds, Date queryStartTime, Date displayEndTime) throws Exception {
        List<UnitEntity> unitList = null;
        if (workUnitIds != null && workUnitIds.length > 0) {
            unitList = basicDataService.getUnitListByWorkshopIds(workUnitIds, false);
        } else {
            unitList = basicDataService.getUnitList(false);
        }
        String[] unitCodes = unitList.stream().map(x -> x.getStdCode()).distinct().toArray(String[]::new);
        if (null == unitCodes || unitCodes.length == 0) {
            return null;
        }
        List<Object[]> listAlarmEvent = repo.deptmUnitAlarmStatt(unitCodes, queryStartTime, displayEndTime);
        //List<WorkshopEntity> wsList=basicDataService.getWorkshopListByWorkshopIds(workUnitIds);
        List<DeptmUnitAlarmStattEntity> returnList = new ArrayList<>();
        for (Object[] o : listAlarmEvent) {
            DeptmUnitAlarmStattEntity deptmUnitAlarmStattEntity = new DeptmUnitAlarmStattEntity();
            deptmUnitAlarmStattEntity.setUnitCode(String.valueOf(o[0]));
            deptmUnitAlarmStattEntity.setAlarmCount(Long.valueOf(String.valueOf(o[2])));
            deptmUnitAlarmStattEntity.setAlarmTime(String.valueOf(o[4]).substring(0, 19));
            returnList.add(deptmUnitAlarmStattEntity);
        }
        for (UnitEntity unitEntity : unitList) {
            DeptmUnitAlarmStattEntity deptmUnitAlarmStattEntity = new DeptmUnitAlarmStattEntity();
            if (!returnList.stream().filter(w -> w.getUnitCode().equals(unitEntity.getStdCode())).findAny().isPresent()) {
                deptmUnitAlarmStattEntity.setUnitName(unitEntity.getName());
                deptmUnitAlarmStattEntity.setUnitSName(unitEntity.getSname());
                deptmUnitAlarmStattEntity.setUnitCode(unitEntity.getStdCode());
                deptmUnitAlarmStattEntity.setAlarmCount(0L);
                deptmUnitAlarmStattEntity.setAlarmTime(null);
                returnList.add(deptmUnitAlarmStattEntity);
            } else {
                for (DeptmUnitAlarmStattEntity d : returnList) {
                    if (unitEntity.getStdCode().equals(d.getUnitCode())) {
                        d.setUnitName(unitEntity.getName());
                        d.setUnitSName(unitEntity.getSname());
                    }
                }
            }
        }
        return returnList;
    }
}
