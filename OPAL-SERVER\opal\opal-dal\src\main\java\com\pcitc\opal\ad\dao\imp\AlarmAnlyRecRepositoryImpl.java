package com.pcitc.opal.ad.dao.imp;

import com.pcitc.opal.ad.dao.AlarmAnlyRecRepositoryCustom;
import com.pcitc.opal.ad.dao.vo.AlarmRecAnlyVO;
import com.pcitc.opal.ad.pojo.AlarmAnlyRec;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.util.*;

public class AlarmAnlyRecRepositoryImpl extends BaseRepository<AlarmAnlyRec, Long>
        implements AlarmAnlyRecRepositoryCustom {


    @Override
    public PaginationBean<AlarmRecAnlyVO> getAlarmAnlyRec(String[] unitIds, Long[] prdtCellIds, Long alarmFlagId,
                                                          String tag, Integer alarmStatus, Integer anlyStatus,
                                                          Integer[] prioritys, Date startTime, Date endTime,
                                                          Date startTimeMonthAgo, Integer[] monitorType,
                                                          Pagination page) throws Exception {
        try {
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            StringBuilder hql = new StringBuilder("select new com.pcitc.opal.ad.dao.vo.AlarmRecAnlyVO(");
            hql.append("ar.alarmRecId,aar.alarmAnlyRecId,aar.anlyStatus,ar.unitCode,u.sname, w.name, factory.name, ar.prdtCellId, c.sname," +
                    "ar.tag, ap.location, mu.name,  ap.alarmPointHH,ap.alarmPointHI,ap.alarmPointLO,ap.alarmPointLL, ar.alarmFlagId, f.name, " +
                    "ar.alarmTime,ar.recoveryTime,ar.priority, ap.monitorType," +
                    "aar.reasonType,aar.alarmReasonId,an.name,aar.crtTime,aar.crtUserName,aar.submitTime,aar.submitUserName," +
                    "aar.reasonDes,aar.confirmTime, aar.confirmUserName) " + "from AlarmRec ar " + "left join " +
                    "ar.unit u left join u.workshop w left join w.factory factory left join ar.prdtCell c " +
                     "left join ar.alarmFlag f left join ar.alarmAnlyRec aar left join aar.alarmReason an  " + "left join ar.alarmPoint " +
                    "ap left join ap.measUnit mu where 1=1 and u.inUse=1 and c.inUse=1 and ap.inUse = 1");
            if (anlyStatus != null) {
                if (anlyStatus == 0) {
                    hql.append("and aar.alarmAnlyRecId is null ");
                } else {
                    hql.append("and aar.anlyStatus = :anlyStatus ");
                    paramList.put("anlyStatus", anlyStatus);
                }
            }
            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append("and ar.unitCode in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitIds));
            }
            // 过滤生产单元
            if (ArrayUtils.isNotEmpty(prdtCellIds)) {
                hql.append("and ar.prdtCellId in (:prdtCellIds) ");
                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
            }
            // 过滤报警点标识
            if (alarmFlagId != null && alarmFlagId != -1) {
                hql.append("and ar.alarmFlagId=:alarmFlagId ");
                paramList.put("alarmFlagId", alarmFlagId);
            }
            //过滤位号
            if (!StringUtils.isEmpty(tag)) {
                hql.append("  and (upper(ar.tag) like upper(:tag) escape '/') ");
                paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
            }
            //过滤优先级
            if (!ArrayUtils.isEmpty(prioritys)) {
                if (ArrayUtils.contains(prioritys, 9)) {
                    hql.append(" and (ar.priority in (:priority) or ar.priority is null)");
                } else {
                    hql.append(" and ar.priority in (:priority) ");
                }
                paramList.put("priority", Arrays.asList(prioritys));
            }
            //过滤优先级
            if (!ArrayUtils.isEmpty(monitorType)) {
                hql.append(" and ap.monitorType in (:monitorType) ");
                paramList.put("monitorType", Arrays.asList(monitorType));
            }
            //过滤报警状态
            if (alarmStatus != null && alarmStatus != -9) {
                if (alarmStatus == 0) {
                    hql.append(" and ar.recoveryTime is null ");
                } else if (alarmStatus == 1) {
                    hql.append(" and ar.recoveryTime is not null ");
                }
            }
            // 过滤日期
            if (startTime != null && endTime != null) {
                hql.append("and ar.alarmTime between :startTime and :endTime ");
//                hql.append("and ar.recoveryTime >= :startTime ");
//                paramList.put("startTimeMonthAgo", startTimeMonthAgo);
                paramList.put("endTime", endTime);
                paramList.put("startTime", startTime);
            }
            hql.append(" order by ar.alarmTime desc ");
            Query query = getEntityManager().createQuery(hql.toString());
            setParameterList(query, paramList);
            Long count = Long.valueOf(query.getResultList().size());
            BaseRepository<AlarmRecAnlyVO, Long> br = new BaseRepository();
//            Long count =getCount(hql.toString(),paramList);
            return br.findCusTomAll(this.getEntityManager(), page, count, hql.toString(), paramList,
                    AlarmRecAnlyVO.class);

        } catch (Exception ex) {
            throw ex;
        }
    }


    @Override
    public List<AlarmRecAnlyVO> getAlarmAnlyRec(String[] unitIds, Long[] prdtCellIds, Long alarmFlagId, String tag,
                                                Integer alarmStatus, Integer anlyStatus, Integer[] prioritys,
                                                Date startTime, Date endTime, Date startTimeMonthAgo,
                                                Integer[] monitorType) throws Exception {
        try {
            // 参数集合
            Map<String, Object> paramList = new HashMap<String, Object>();
            StringBuilder hql = new StringBuilder("select new com.pcitc.opal.ad.dao.vo.AlarmRecAnlyVO(\n");
            hql.append(
                    "ar.alarmRecId,aar.alarmAnlyRecId,aar.anlyStatus,ar.unitCode,u.sname, w.name, factory.name, ar.prdtCellId, c.sname," + "ar.tag, ap.location, mu.name,  ar.alarmFlagId, f.name, ar.alarmTime,ar.recoveryTime,ar.priority, ap.monitorType) " + "from AlarmRec ar\n" + "left join ar.unit u \n" + "left join u.workshop w \n" + "left join w.factory factory \n" + "left join ar.prdtCell c \n" + "left join ar.alarmFlag f \n" + "left join ar.alarmAnlyRec aar \n" + "left join ar.alarmPoint ap \n" + "left join ap.measUnit mu \n" + "where 1=1 and u.inUse=1 and c.inUse=1 \n");
            if (anlyStatus != null) {
                if (anlyStatus == 0) {
                    hql.append("and aar.alarmAnlyRecId is null ");
                } else {
                    hql.append("and aar.anlyStatus = :anlyStatus ");
                    paramList.put("anlyStatus", anlyStatus);
                }
            }
            // 过滤装置
            if (ArrayUtils.isNotEmpty(unitIds)) {
                hql.append("and ar.unitCode in (:unitIds) ");
                paramList.put("unitIds", Arrays.asList(unitIds));
            }
            // 过滤生产单元
            if (ArrayUtils.isNotEmpty(prdtCellIds)) {
                hql.append("and ar.prdtCellId in (:prdtCellIds) ");
                paramList.put("prdtCellIds", Arrays.asList(prdtCellIds));
            }
            // 过滤报警点标识
            if (alarmFlagId != null && alarmFlagId != -1) {
                hql.append("and ar.alarmFlagId=:alarmFlagId ");
                paramList.put("alarmFlagId", alarmFlagId);
            }
            //过滤位号
            if (!StringUtils.isEmpty(tag)) {
                hql.append("  and (upper(ar.tag) like upper(:tag) escape '/') ");
                paramList.put("tag", "%" + this.sqlLikeReplace(tag) + "%");
            }
            //过滤优先级
            if (!ArrayUtils.isEmpty(prioritys)) {
                if (ArrayUtils.contains(prioritys, 9)) {
                    hql.append(" and (ar.priority in (:priority) or ar.priority is null)");
                } else {
                    hql.append(" and ar.priority in (:priority) ");
                }
                paramList.put("priority", Arrays.asList(prioritys));
            }
            //过滤优先级
            if (!ArrayUtils.isEmpty(monitorType)) {
                hql.append(" and ap.monitorType in (:monitorType) ");
                paramList.put("monitorType", Arrays.asList(monitorType));
            }
            //过滤报警状态
            if (alarmStatus != null && alarmStatus != -9) {
                if (alarmStatus == 0) {
                    hql.append(" and ar.recoveryTime is null ");
                } else if (alarmStatus == 1) {
                    hql.append(" and ar.recoveryTime is not null ");
                }
            }
            // 过滤日期
            if (startTime != null && endTime != null) {
                hql.append("and ar.alarmTime between :startTime and :endTime ");
//                hql.append("and ar.recoveryTime >= :startTime ");
//                paramList.put("startTimeMonthAgo", startTimeMonthAgo);
                paramList.put("endTime", endTime);
                paramList.put("startTime", startTime);
            }
            hql.append(" order by ar.alarmTime desc ");
//            Query query = getEntityManager().createQuery(hql.toString());
//            setParameterList(query, paramList);
//            Long count = Long.valueOf(query.getResultList().size());
//            BaseRepository<AlarmRecAnlyVO, Long> br = new BaseRepository();
//            Long count =getCount(hql.toString(),paramList);
//            return br.findCusTomAll(this.getEntityManager(), count, hql.toString(), paramList, AlarmRecAnlyVO.class);
            Query query = getEntityManager().createQuery(hql.toString());
            setParameterList(query, paramList);
            return query.getResultList();
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public List<AlarmAnlyRecVO> getAlarmAnlyByRec(Long alarmRecId) throws Exception {
        StringBuilder hql = new StringBuilder("select new com.pcitc.opal.ad.dao.imp.AlarmAnlyRecVO(\n");
        hql.append(
                "ar.alarmAnlyRecId,ar.reasonType,ar.alarmReasonId,an.name,ar.crtTime,ar.crtUserName,ar.submitTime,ar.submitUserName,ar.reasonDes,ar.confirmTime, ar.confirmUserName) \n" + "from AlarmAnlyRec ar\n" + "left join ar.alarmReason an \n" + "where 1=1 \n");
        Map<String, Object> paramList = new HashMap<String, Object>();
        if (alarmRecId != null) {
            hql.append("and ar.alarmRecId=:alarmRecId ");
            paramList.put("alarmRecId", alarmRecId);
        }
        Query query = getEntityManager().createQuery(hql.toString());
        setParameterList(query, paramList);
        return query.getResultList();
    }

    @Override
    @Transactional(noRollbackFor = Exception.class)
    public CommonResult updateAlarmAnlyRecs(List<AlarmAnlyRec> alarmAnlyRec) {
        CommonResult commonResult = new CommonResult();

        try {
            alarmAnlyRec.forEach(ar -> {
                getEntityManager().merge(ar);
            });
            commonResult.setMessage("更新成功");
        } catch (Exception e) {
            commonResult.setMessage("更新失败" + e.getMessage());
            commonResult.setIsSuccess(false);
        }
        return commonResult;
    }

    @Override
    @Transactional(noRollbackFor = Exception.class)
    public CommonResult updateAlarmAnlyRec(AlarmAnlyRec alarmAnlyRec) {
        CommonResult commonResult = new CommonResult();

        try {
            getEntityManager().merge(alarmAnlyRec);
            commonResult.setMessage("更新成功");
        } catch (Exception e) {
            commonResult.setMessage("更新失败" + e.getMessage());
            commonResult.setIsSuccess(false);
        }
        return commonResult;
    }

    @Override
    @Transactional
    public CommonResult addAlarmAnlyRec(AlarmAnlyRec alarmAnlyRec) {
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(alarmAnlyRec);
            commonResult.setResult(alarmAnlyRec);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("保存成功！");
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    @Override
    public List<AlarmAnlyRec> getAlarmAnlyRecsById(Long[] alarmAnlyRecIds) {
        String hql = "from AlarmAnlyRec ar where ar.alarmAnlyRecId in :alarmAnlyRecIds";
        Query query = getEntityManager().createQuery(hql, AlarmAnlyRec.class);
        query.setParameter("alarmAnlyRecIds", Arrays.asList(alarmAnlyRecIds));
        return query.getResultList();
    }

    @Override
    public List<AlarmAnlyRec> getAlarmAnlyRecById(Long alarmAnlyRecId) {
        String hql = "from AlarmAnlyRec ar where ar.alarmAnlyRecId = :alarmAnlyRecId";
        Query query = getEntityManager().createQuery(hql, AlarmAnlyRec.class);
        query.setParameter("alarmAnlyRecId", Arrays.asList(alarmAnlyRecId));
        return query.getResultList();
    }

}
