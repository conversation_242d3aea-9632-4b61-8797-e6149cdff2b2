package com.pcitc.opal.aa.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;

/*
 * 报警时长统计实体
 * 模块编号：pcitc_opal_bll_class_AlarmEventEntity
 * 作       者：kun.zhao
 * 创建时间：2017/10/9
 * 修改编号：1
 * 描       述：报警事件实体
 */
public class AlarmDurationStattEntity extends BasicEntity {
    /**
     * 报警时长统计总计
     */
    private String totalAlarmQuantity;

    /**
     * 紧急报警时长
     */
    private EchartsShowEntity emergencyAlarmQuantityEntity;
    private String emergencyAlarmQuantity;

    /**
     * 重要报警时长
     */
    private EchartsShowEntity importantAlarmQuantityEntity;
    private String importantAlarmQuantity;

    /**
     * 一般报警时长
     */
    private EchartsShowEntity generalAlarmQuantityEntity;
    private String generalAlarmQuantity;

    /**
     * 空报警时长
     */
    private EchartsShowEntity nullAlarmQuantityEntity;
    private String nullAlarmQuantity;

    /**
     * 装置Id
     */
    private String unitId;

    /**
     * 简称
     */
    private String sname;

    public String getTotalAlarmQuantity() {
        return totalAlarmQuantity;
    }

    public void setTotalAlarmQuantity(String totalAlarmQuantity) {
        this.totalAlarmQuantity = totalAlarmQuantity;
    }

    public EchartsShowEntity getEmergencyAlarmQuantityEntity() {
        return emergencyAlarmQuantityEntity;
    }

    public void setEmergencyAlarmQuantityEntity(EchartsShowEntity emergencyAlarmQuantityEntity) {
        this.emergencyAlarmQuantityEntity = emergencyAlarmQuantityEntity;
    }

    public String getEmergencyAlarmQuantity() {
        return emergencyAlarmQuantity;
    }

    public void setEmergencyAlarmQuantity(String emergencyAlarmQuantity) {
        this.emergencyAlarmQuantity = emergencyAlarmQuantity;
    }

    public EchartsShowEntity getImportantAlarmQuantityEntity() {
        return importantAlarmQuantityEntity;
    }

    public void setImportantAlarmQuantityEntity(EchartsShowEntity importantAlarmQuantityEntity) {
        this.importantAlarmQuantityEntity = importantAlarmQuantityEntity;
    }

    public String getImportantAlarmQuantity() {
        return importantAlarmQuantity;
    }

    public void setImportantAlarmQuantity(String importantAlarmQuantity) {
        this.importantAlarmQuantity = importantAlarmQuantity;
    }

    public EchartsShowEntity getGeneralAlarmQuantityEntity() {
        return generalAlarmQuantityEntity;
    }

    public void setGeneralAlarmQuantityEntity(EchartsShowEntity generalAlarmQuantityEntity) {
        this.generalAlarmQuantityEntity = generalAlarmQuantityEntity;
    }

    public String getGeneralAlarmQuantity() {
        return generalAlarmQuantity;
    }

    public void setGeneralAlarmQuantity(String generalAlarmQuantity) {
        this.generalAlarmQuantity = generalAlarmQuantity;
    }

    public EchartsShowEntity getNullAlarmQuantityEntity() {
        return nullAlarmQuantityEntity;
    }

    public void setNullAlarmQuantityEntity(EchartsShowEntity nullAlarmQuantityEntity) {
        this.nullAlarmQuantityEntity = nullAlarmQuantityEntity;
    }

    public String getNullAlarmQuantity() {
        return nullAlarmQuantity;
    }

    public void setNullAlarmQuantity(String nullAlarmQuantity) {
        this.nullAlarmQuantity = nullAlarmQuantity;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getSname() {
        return sname;
    }

    public void setSname(String sname) {
        this.sname = sname;
    }
}
