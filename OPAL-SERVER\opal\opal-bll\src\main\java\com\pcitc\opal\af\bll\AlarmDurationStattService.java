package com.pcitc.opal.af.bll;

import com.pcitc.opal.aa.bll.entity.AlarmDurationStattEntity;
import com.pcitc.opal.aa.bll.entity.AlarmDurationStattCommonEntity;
import com.pcitc.opal.ad.bll.entity.AlarmRecEntity;
import com.pcitc.opal.ad.dao.imp.AlarmDurationDtlEntityVO;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public interface AlarmDurationStattService {
    /**
     * 报警时长统计下方表格二级网格列
     *
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param priority
     * @param endFlag
     * @return
     * @throws Exception
     */
    List<AlarmRecEntity> getAlarmDurationStatt(String[] unitIds, Date startTime, Date endTime, Integer[] priority,
                                               Boolean priorityFlag, String tag, Long alarmFlagId, String endFlag,
                                               Integer isElimination) throws Exception;


    /**
     * 报警时长统计下方表格二级网格列
     *
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param priority
     * @param endFlag
     * @return
     * @throws Exception
     */
    PaginationBean<AlarmRecEntity> getAlarmDurationStattPage(Pagination page,String[] unitIds, Date startTime, Date endTime, Integer[] priority,
                                               Boolean priorityFlag, String tag, Long[] alarmFlagId, String endFlag,
                                               Integer isElimination) throws Exception;

    /**
     * 报警时长统计--下方一级网格列
     *
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param priority
     * @param priorityFlag
     * @param endFlag
     * @param page
     * @return
     * @throws Exception
     */
    PaginationBean<AlarmRecEntity> getAlarmDurationStattMain(String[] unitIds, Long[] alarmFlagIds, Date startTime,
                                                             Date endTime, Integer[] priority, Boolean priorityFlag,
                                                             String endFlag, Pagination page, Integer isElimination)
            throws Exception;

    /**
     * 报警时长统计--下方一级网格列（新）
     *
     * @param workshopCodes
     * @param prdtCellIds
     * @param unitIds
     * @param alarmFlagIds
     * @param startTime
     * @param endTime
     * @param priority
     * @param priorityFlag
     * @param endFlag
     * @param page
     * @param isElimination
     * @return
     * @throws Exception
     */
    PaginationBean<AlarmRecEntity> getAlarmDurationStattMainCommon(String[] unitCodes, String[] workshopCodes,
                                                                   Long[] prdtCellIds, String[] unitIds,
                                                                   Long[] alarmFlagIds, Date startTime, Date endTime,
                                                                   Integer[] priority, Boolean priorityFlag,
                                                                   String endFlag, Pagination page,
                                                                   Integer isElimination ,String tag) throws Exception;

    /**
     * 报警时长统计柱状图
     *
     * @param unitIds
     * @param startTime1
     * @param endTime1
     * @param priority
     * @return
     */
    List<AlarmDurationStattEntity> getAlarmDurationStattTotal(String[] unitIds, Long[] alarmFlagId, Date startTime1,
                                                              Date endTime1, Integer[] priority, Boolean priorityFlag,
                                                              Integer isElimination) throws Exception;

    /**
     * 报警时长统计柱状图(车间)
     *
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param priority
     * @return
     */
    List<AlarmDurationStattCommonEntity> getAlarmDurationStattTotalWorkshop(String[] unitIds, Long[] alarmFlagId,
                                                                            Date startTime, Date endTime,
                                                                            Integer[] priority, Boolean priorityFlag,
                                                                            Integer isElimination) throws Exception;

    /**
     * 报警时长统计柱状图(装置)
     *
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param priority
     * @return
     */
    List<AlarmDurationStattCommonEntity> getAlarmDurationStattTotalUnit(String[] workshopCodes, String[] unitIds,
                                                                        Long[] alarmFlagId, Date startTime,
                                                                        Date endTime, Integer[] priority,
                                                                        Boolean priorityFlag, Integer isElimination)
            throws Exception;

    /**
     * 报警时长统计柱状图(生产单元)
     *
     * @param unitIds
     * @param startTime
     * @param endTime
     * @param priority
     * @return
     */
    List<AlarmDurationStattCommonEntity> getAlarmDurationStattTotalPrdtcell(String[] unitCodes, String[] unitIds,
                                                                            Long[] alarmFlagId, Date startTime,
                                                                            Date endTime, Integer[] priority,
                                                                            Boolean priorityFlag, Integer isElimination)
            throws Exception;

    List<DictionaryEntity> getAlarmPriorityList(boolean isAll);

    /**
     * 报警数量统计——
     *
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    PaginationBean<AlarmDurationDtlEntityVO> getAlarmDurationDtl(
            Date startTime, Date endTime, String unitCode, Integer priority, Long[] alarmFlagIds, Pagination page)
            throws Exception;
}
