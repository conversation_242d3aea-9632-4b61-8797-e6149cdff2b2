package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.DBCompanyEntity;
import com.pcitc.opal.pm.pojo.Company;
import org.springframework.stereotype.Service;

import java.rmi.RemoteException;
import java.util.List;

@Service
public interface CompanyService {

    /**
     * 获取企业分页数据
     * @param name
     * @param stdCode
     * @param inUse
     * @param page
     * @return
     * @throws Exception
     */
    PaginationBean<DBCompanyEntity> getCompany(String name, String stdCode, Integer inUse, Pagination page) throws Exception;

    /**
     * 添加企业
     * @param companyEntity
     * @return
     */
    CommonResult addCompany(DBCompanyEntity companyEntity) throws Exception;

    /**
     * 删除企业
     * @param data
     * @return
     */
    CommonResult deleteCompany(Long[] data) throws Exception;

    CommonResult updateCompany(DBCompanyEntity companyEntity) throws Exception;

    DBCompanyEntity getSingleCompany(Long companyId) throws Exception;

    List<DBCompanyEntity> getCompanyList(boolean isAll);

    List<Company> getAllCompanyId();
}
