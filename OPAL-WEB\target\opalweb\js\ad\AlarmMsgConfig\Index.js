var searchUrl = OPAL.API.adUrl + '/alarmEvents/getAlarmMsgConfig';
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';  //生产单元
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";   //装置
var sendStatusUrl = OPAL.API.adUrl + '/alarmEvents/getAlarmMsgConfigStatus';  //发送状态
var isLoading = true;
$(function() {
    var page = {
        /**
         * 初始化
         */
        init: function() {
            this.bindUi();
            //初始化日期
            page.logic.initDate();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化发送状态
            page.logic.initSendStatus();
            //初始化表格
            page.logic.initTable();

            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("AlarmEvent");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }
            //默认查询数据
            setTimeout(function () {
                if ($("#status").val()!=null && $("#endSendTime").val()!=null && $("#startSendTime").val()!=null) {
                    page.logic.search();
                }
            }, 500);
        },
        bindUi: function() {
            //查询
            $('#search').click(function() {
                isLoading = false;
                page.logic.search();
            })
        },
        data: {
            //查询参数
            param: {}
        },
        logic: {
            initTable: function() {
                OPAL.ui.initBootstrapTable2("table", {
                    columns: [ {
                        title: "发送时间",
                        field: 'sendTime',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "生产单元",
                        field: 'prdtcellName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    },  {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "接收人",
                        field: 'name',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "手机号",
                        field: 'mobile',
                        rowspan: 1,
                        align: 'left',
                        width: '150px'
                    }]
                }, page.logic.queryParams,"search")
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             *初始化日期
             */
            initDate: function(str, num) {
                var startTime = moment().startOf('month').format("YYYY-MM-DD HH:mm:ss");
                var currentTime = moment().format("YYYY-MM-DD 23:59:59");
                laydate.render({
                    elem: '#startSendTime',
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm:ss',
                    value: startTime
                });
                laydate.render({
                    elem: '#endSendTime',
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm:ss',
                    value: currentTime
                });
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function(nodes) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $("#workTeamIds").prop('disabled', false);
                            $('.textbox,.combo').css('background-color','');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('.textbox-disabled').css('background-color','rgb(235, 235, 228)');
                        }
                    }
                }, false, function() {
                    $("#searchPrdt").combotree("checkAllNodes");
                });
            },
            /**
             * 搜索
             */
            search: function() {
                $("#search").attr('disabled',true);
                page.data.param = OPAL.form.getData("searchForm",true);
                if(page.data.param.startSendTime >= page.data.param.endSendTime){
                    layer.msg("开始时间必须小于结束时间！");
                    $("#search").attr('disabled',false);
                    return
                }
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function() {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化发送状态
             */
            initSendStatus: function() {
                OPAL.ui.getCombobox("status", sendStatusUrl, {
                    selectValue: 1,
                    data: {
                        'isAll': true
                    }
                }, null);
            },

            /**
             * 设置参数
             */
            setData: function() {
                page.data.param = OPAL.form.getData('searchForm');
            }

        }
    };
    page.init();
    window.page = page;
});