package com.pcitc.opal.ad.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pcitc.opal.ad.entity.AlarmEventEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pcitc.opal.ad.vo.AlarmEventTableParamVO;
import com.pcitc.opal.ad.vo.AlarmEventTableVO;
import com.pcitc.opal.ad.vo.FloodAlarmPointCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 报警事件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-03
 */
public interface AlarmEventDAO extends BaseMapper<AlarmEventEntity> {

    List<AlarmEventEntity> getNotDisposeData(@Param("currentId") Long currentId, @Param("companyId") Long companyId);


    Page<FloodAlarmPointCountVO> selectFloodAlarmPointCount(@Param("pager") Page<FloodAlarmPointCountVO> pager, @Param("unitCodeList") List<String> unitCodeList, @Param("prdtIds") List<Long> prdtIds,
                                                            @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    List<FloodAlarmPointCountVO> selectFloodAlarmPointCount(@Param("unitCodeList") List<String> unitCodeList, @Param("prdtIds") List<Long> prdtIds,
                                                            @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    Page<AlarmEventTableVO> getAlarmEventTable(@Param("pager") Page<AlarmEventTableVO> pager,
                                                   @Param("vo")AlarmEventTableParamVO vo);

    List<AlarmEventTableVO> getAlarmEventTable(@Param("vo")AlarmEventTableParamVO vo);
}
