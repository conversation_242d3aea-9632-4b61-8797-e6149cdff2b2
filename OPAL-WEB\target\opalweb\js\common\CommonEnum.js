/**************************************************************************************************
 * 作    者：dongsheng.zhao       创始时间：2017-9-25
 * 修 改 人：                     修改时间：
 * 描    述： 枚举类,项目中前台用到的所有枚举都在此处维护
 **************************************************************************************************/

/*=========================================公用枚举=================================================*/
/**
 * 是否启用(-1全部、0否、1是)
 */
var InUseEnum = {
    //0 否
    No: 0,
    //1 是
    Yes: 1
};

/**
 * 页面模式枚举(1新增,2编辑,3查看)
 */
var PageModelEnum = {
    //1 新增
    NewAdd: 1,
    //2 编辑
    Edit: 2,
    //3 查看
    View: 3
};
/**
 * @type {{Refresh: 1(刷新), Reload: 2(重新加载),NO:(不执行操作)}}
 */
var PageLoadMode = {
    //1 刷新
    Refresh: 1,
    //2 重新加载(从第一页开始加载)
    Reload: 2,
    //3 不执行操作
    None: 3
};

/**
 * 日期类型枚举
 * <AUTHOR> 2017-9-26
 * 日期类型枚举(-1未知；0班；1日；2周；3旬；4月；5季；6年；7不定期)
 */
var DateTypeEnum = {
    //"未知"
    Unknown: -1,
    //"班"
    Shift: 0,
    //"日",
    Day: 1,
    //"周"
    Week: 2,
    //"旬"
    TenDays: 3,
    //"月"
    Month: 4,
    //"月"
    Quarter: 5,
    //"年"
    Year: 6,
    //"不定期"
    Irregular: 7
};

/**
 * 机构单元类型枚举
 * <AUTHOR> 2017-9-26
 * 机构单元类型枚举(1生产单位，2职能单位)
 */
var OUTypeEnum = {
    //生产单位
    PrdtCell: 1,
    //职能单位
    FunctionCell: 2
}


/**
 * 专业枚举
 * <AUTHOR> 2017-10-9
 * 1物料；2能源；3质量
 */
var MonitorTypeEnum = {
    //物料
    Mtrl: 1,
    //能源
    Energy: 2,
    //质量
    Quality: 3
};

/**
 * 报警标识枚举
 * <AUTHOR> 2017-10-9
 * 1PVHH、2PVHI、3PVLO、4PVLL
 */
var AlarmFlagEnum = {
    //PVHH
    PVHH: 1,
    //PVHI
    PVHI: 2,
    //"PVLO
    PVLO: 3,
    //PVLL
    PVLL: 4
};

/**
 * 报警优先级枚举
 * <AUTHOR> 2017-10-9
 * 1紧急、2重要、3一般
 */
var AlarmPriorityEnum = {
    //紧急
    Emergency: 1,
    //重要
    Importance: 2,
    //一般
    Normal: 3
};
/**
 *
 * 仪表类型枚举
 * <AUTHOR> 2017-10-9
 * 1监测表；2控制表
 */
var InstrmtTypeEnum = {
    //监测表
    MonitoringInstrmt: 1,
    //控制表
    ControlInstrmt: 2
};

/**
 * 虚实标记枚举
 * <AUTHOR> 2017-10-9
 * 0实表(按读数)；1虚表(按用量)
 */
var VirtualRealityFlagEnum = {
    //实表
    Reality: 0,
    //实表
    Virtual: 1
};
/**
 * 日期类型枚举
 * <AUTHOR> 2017-12-22
 */
var VirtualRealityFlagEnum = {
    //小时
    Hour: 0,
    //天
    Day: 1,
    //周
    Week: 2,
    //月
    Month: 3
};
/**
 * 提交状态枚举
 * <AUTHOR> 2018-03-15
 */
var AlarmChangePlanStatusEnum={
    //未提交
    UnSubmit: 0,
    //已驳回
    Reject:1,
    //已提交
    Submitted:2,
    //已审核
    Audited:3,
    //已下发
    Issued:4,
    //已完成
    Finished:5
};
