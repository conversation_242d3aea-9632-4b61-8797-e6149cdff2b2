package com.pcitc.opal.common;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.Consts;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/*
 * Http请求公用类
 *
 * 模块编号：pcitc_opal_common_class_MicroServiceClient
 * 作       者：pcitc
 * 创建时间：2017/09/10
 * 修改编号：1
 * 描       述：微服务请求公用类
 */
public class MicroServiceClient {
    private final static Log logger = LogFactory.getLog(MicroServiceClient.class);

    /**
     * Http发送Post请求
     *
     * @param url  请求地址
     * @param nvps 请求参数
     * @return 返回内容
     * <AUTHOR> 2018-07-01
     */
    public static String postRequest(String url, List<NameValuePair> nvps) throws Exception {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpResponse response = null;

        try {
            httpPost.setEntity(new UrlEncodedFormEntity(nvps));
            logger.debug("请求开始:" + url);
            response = httpclient.execute(httpPost);
            InputStream responseStream = response.getEntity().getContent();
            String result = StreamUtils.copyToString(responseStream, Charset.forName("utf-8"));
            logger.debug("请求成功:" + url);
            return result;
        } catch (Exception ex) {
            logger.error(String.format("请求失败: 参数- %s，消息：%", url + "," + httpPost.getEntity().getContent(), ex.getMessage()));
            throw new Exception("调用平台服务出现了异常!");
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException ex) {
                throw new Exception("调用平台服务出现了异常!");
            }
        }
    }

    /**
     * 发送Http get请求
     *
     * @param url    请求地址
     * @param params 请求参数
     * @return 请求数据
     * <AUTHOR> 2018-07-01
     */
    public static String getRequest(String url, List<NameValuePair> params) throws Exception {
        if (StringUtils.isEmpty(url)) return "";
        if (params == null) params = new ArrayList<>();
        int index = url.lastIndexOf(":");
        String srr = url.substring(0, index);
        CloseableHttpClient httpClient = HttpUtil.wrapClient();
        if ("https".equals(srr)) {
            httpClient = HttpUtil.wrapSslClient();
        }
        CloseableHttpResponse response = null;
        InputStream inputStream = null;
        String paramStr = null;
        HttpGet httpGet = null;
        StringBuilder sbResult = new StringBuilder();
        try {
            if (params != null && params.size() != 0) {
                paramStr = EntityUtils.toString(new UrlEncodedFormEntity(params, Consts.UTF_8));
            }
            if (paramStr != null) {
                httpGet = new HttpGet(url + "?" + paramStr);
            } else {
                httpGet = new HttpGet(url);
            }
            logger.debug("请求开始:" + httpGet.getURI().toString());
            response = httpClient.execute(httpGet);
            org.apache.http.HttpEntity entity = response.getEntity();
            if (entity != null) {
                inputStream = entity.getContent();
                BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, Consts.UTF_8));
                String body;
                while ((body = br.readLine()) != null) {
                    sbResult.append(body);
                }
            }
        } catch (Exception ex) {
            logger.error(String.format("请求失败: 参数- %s，消息：%s", httpGet.getURI().toString(), ex.getMessage()));
            throw new Exception("调用平台服务出现了异常!");
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        logger.debug("请求成功:" + httpGet.getURI().toString());
        return sbResult.toString();
    }

    /**
     * 使用POST发送Http请求,参数以body方式传递
     *
     * @param url
     * @param nvps
     * @return
     */
    public static String sendHttpPost(String url, String nvps) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
        CloseableHttpResponse response = null;
        InputStream inputStream;
        StringBuilder sbResult = new StringBuilder();
        try {
            StringEntity entity = new StringEntity(nvps, "utf-8");
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            logger.debug("请求开始:" + url);
            response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                org.apache.http.HttpEntity responseEntity = response.getEntity();
                if (responseEntity != null) {
                    inputStream = responseEntity.getContent();
                    BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, Consts.UTF_8));
                    String body;
                    while ((body = br.readLine()) != null) {
                        sbResult.append(body);
                    }
                }
            } else {
                return "";
            }
            logger.debug("请求成功:" + url);
        } catch (Exception ex) {
            logger.error(String.format("请求失败: 参数- %s，消息：%", url + "," + httpPost.getEntity().getContent(), ex.getMessage()));
            throw new Exception("调用平台服务出现了异常!");
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                throw new Exception("调用平台服务出现了异常!");
            }
        }
        return sbResult.toString();
    }

}
