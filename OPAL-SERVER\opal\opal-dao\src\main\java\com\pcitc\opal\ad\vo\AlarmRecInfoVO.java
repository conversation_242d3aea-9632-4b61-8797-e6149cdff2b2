package com.pcitc.opal.ad.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AlarmRecInfoVO implements Serializable {

    private String unitCode;

    private String unitName;

    private String tag;

    /**
     *优先级(1紧急；2重要；3一般)
     */
    private Integer priority;
    /**
     * 报警标识ID
     */
    private Long alarmFlagId;

    private Date recoveryTime;

    private Date responseTime;

    private Date alarmTime;

    private String des;

    private String prdtcellName;

    private String measunitName;

    private String measunitSign;

    private String alarmflagName;

    private Long alarmPointId;

    private Long prdtCellId;

    private String location;

    private Integer monitorType;

}
