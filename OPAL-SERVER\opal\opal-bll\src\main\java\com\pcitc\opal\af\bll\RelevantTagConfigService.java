package com.pcitc.opal.af.bll;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Service;

import com.pcitc.opal.common.CommonEnum.DateTypeEnum;
import com.pcitc.opal.af.bll.entity.RelevantTagConfigDtlEntity;
import com.pcitc.opal.af.bll.entity.RelevantTagConfigEntity;
import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.AlarmPointEntity;

/*
 * 相关性位号配置业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_RelevantTagConfigService
 * 作       者：dageng.sun
 * 创建时间：2018/8/1
 * 修改编号：1
 * 描       述：相关性位号配置业务逻辑层接口 
 */
@Service
public interface RelevantTagConfigService {
	
	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigEntity 相关性位号配置实体
	 * @throws Exception 抛出异常
	 * @return CommonResult 返回消息结果类
	 */
	CommonResult addRelevantTagConfig(RelevantTagConfigEntity relevantTagConfigEntity) throws Exception;
	
	/**
	 * 删除相关性位号配置数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigIds 相关性位号配置主键id集合
	 * @throws Exception 抛出异常
	 * @return CommonResult 返回消息结果类
	 */
	CommonResult deleteRelevantTagConfig(Long[] relevantTagConfigIds) throws Exception;
	
	/**
	 * 相关性位号配置更新数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigEntity 相关性位号配置实体
	 * @throws Exception 抛出异常
	 * @return CommonResult 消息结果类
	 */
	CommonResult updateRelevantTagConfig(RelevantTagConfigEntity relevantTagConfigEntity) throws Exception;
	
	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param relevantTagConfigId 相关性位号配置主键
	 * @throws Exception 抛出异常
	 * @return RelevantTagConfigEntity 返回RelevantTagConfigEntity实体
	 */
	RelevantTagConfigEntity getSingleRelevantTagConfig(Long relevantTagConfigId) throws Exception;
	
	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2018-08-01 
	 * @param unitCodes 装置数组
	 * @param prdtCellIds 生产单元数组
	 * @param tag 主位号
	 * @param page 分页对象
	 * @throws Exception 抛出异常
	 * @return PaginationBean<RelevantTagConfigEntity> 返回RelevantTagConfigEntity分页对象
	 */
	PaginationBean<RelevantTagConfigEntity> getRelevantTagConfig(String[] unitCodes, Long[] prdtCellIds, String tag, Pagination page) throws Exception;
	
	/**
	 * 根据参数“相关性位号配置ID”查询<相关性位号配置明细>数据
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 相关性位号配置ID
	 * @param page 分页对象
	 * @throws Exception 抛出异常
	 * @return PaginationBean<RelevantTagConfigDtlEntity> 返回RelevantTagConfigDtlEntity实体类分页对像
	 */
	PaginationBean<RelevantTagConfigDtlEntity> getRelevantTagConfigDtl(Long relevantTagConfigId, Pagination page) throws Exception;
	
	/**
	 * 删除相关性位号配置明细服务层方法
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigDtlIds 相关性位号配置明细主键Id集合
	 * @throws Exception 抛出异常
	 * @return CommonResult 返回消息结果类
	 */
	CommonResult deleteRelevantTagConfigDtl(Long[] relevantTagConfigDtlIds) throws Exception;
	
	/**
	 * 查询报警点服务层方法
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param unitCode 装置编码
	 * @param prdtCellId 生产单元id
	 * @param tag 主位号
	 * @param page 分页对象
	 * @throws Exception 抛出异常
	 * @return PaginationBean<AlarmPointEntity> 返回AlarmPointEntity实体类集合
	 */
	PaginationBean<AlarmPointEntity> getAlarmPoint(String unitCode, Long prdtCellId, String tag, Pagination page) throws Exception;
	
	/**
	 * 获取报警点列表服务层方法
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 相关性位号配置ID
	 * @param unitCode 装置编码
	 * @param prdtCellIds 生产单元id
	 * @param tag 主位号
	 * @param page 分页对象
	 * @throws Exception 抛出异常
	 * @return PaginationBean<AlarmPointEntity> 返回AlarmPointEntity实体分页对象
	 */
	PaginationBean<AlarmPointEntity> getRelevantTagConfigDtlAdd(Long relevantTagConfigId, String unitCode, Long prdtCellIds, String tag, Pagination page) throws Exception;
	
	/**
	 * 保存<相关性位号配置明细>数据服务层方法
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 关性位号配置ID
	 * @param alarmPointIds 报警点ID数组
	 * @throws Exception 抛出异常
	 * @return CommonResult 返回消息结果类
	 */
	CommonResult addRelevantTagConfigDtl(Long relevantTagConfigId, Long[] alarmPointIds) throws Exception;
	
	/**
	 * 获取相关位号服务层方法
	 * 
	 * <AUTHOR> 2018-08-03 
	 * @param relevantTagConfigId 关性位号配置ID
	 * @throws Exception 抛出异常
	 * @return List<RelevantTagConfigDtlEntity> 返回RelevantTagConfigDtlEntity实体类集合
	 */
	List<RelevantTagConfigDtlEntity> getRelevantTag(Long relevantTagConfigId) throws Exception;
	
	/**
	 * 相关性报警分析服务层方法
	 * 
	 * <AUTHOR> 2018-08-03 
	 * @param alarmPointIds 报警点主键id数组
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param dateTypeEnum 时间粒度枚举
	 * @return Object 返回Object对象
	 */
	Object getRelevantAlarmAnalysis(Long[] alarmPointIds, Date startTime, Date endTime, DateTypeEnum dateTypeEnum) throws Exception;
	
}
