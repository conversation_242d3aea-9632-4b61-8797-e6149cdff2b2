server.port=8081

jasypt.encryptor.password=mesopal

# 多数据源配置
#设置默认的数据源或者数据源组,默认值即为master
spring.datasource.dynamic.primary=enterprise
#严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
spring.datasource.dynamic.strict=false
#总部端数据源
spring.datasource.dynamic.datasource.tenant.url=**************************************************************************************************************************************************
spring.datasource.dynamic.datasource.tenant.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.tenant.username=mes_opal
spring.datasource.dynamic.datasource.tenant.password=Mes_opal123

#企业端数据源
spring.datasource.dynamic.datasource.enterprise.url=********************************************
spring.datasource.dynamic.datasource.enterprise.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.enterprise.username=root
spring.datasource.dynamic.datasource.enterprise.password=123456


### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl.job.admin.addresses=http://**************:8080/xxl-job-admin

### 执行器通讯TOKEN [选填]：非空时启用；
xxl.job.accessToken=default_token

### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
xxl.job.executor.appname=xxl-job-executor-sample
### 调度中心部署根地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
xxl.job.executor.address=
### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
xxl.job.executor.ip=
### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
xxl.job.executor.port=9999
### xxl-job executor log-path
xxl.job.executor.logpath=/data/applogs/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logretentiondays=30


# 企业id
companyId=24

# 配置同步超时时间
sync.await=3
# 每次同步事件表，报警点未匹配表最大记录数
sync.batchSize=5000