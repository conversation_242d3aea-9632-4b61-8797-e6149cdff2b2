var operateAssessUrl = OPAL.API.aaUrl + '/alarmOperateAssess/getAlarmOperateAssess';
var operateTopUrl = OPAL.API.aaUrl + '/alarmOperateAssess/getAlarmOperateAssessTop20';
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var operateChart;
var mostFrequentOperateChart;
var unitChart;
var displayName = "单元";
var highLightColor = '#CCC';
var startDateTime;
var endDateTime;
var userType = "";
var isFactoryMode = false;
var dateType;
var dateArray;
var topCount=20;
var isLoading = true;
$(function () {
    var page = {
        /**
         * 初始化
         */
        init: function () {
            /**
             *绑定事件
             */
            this.bindUi();
            /**
             * 日期扩展
             */
            OPAL.util.extendDate();
            /**
             * 初始化日期时间选择控件组
             */
            OPAL.ui.initDateTimePeriodPicker();
            /**
             * 初始化装置数
             */
            page.logic.initUnitTree();
            /**
             * 初始化表格
             */
            page.logic.initTable();
            page.logic.initOperateChart();
            page.logic.initMostFrequentOperateChart();
            page.logic.initOperateShow();
            page.logic.initOperateTable();
            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("AlarmOperateAssess");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                    document.getElementById("btnSearch").click();
                }
            }
        },
        bindUi: function () {
            /**
             * 设置图表自适应屏幕
             */
            window.onresize = function () {
                operateChart.resize();
                mostFrequentOperateChart.resize();
                unitChart.resize();
            };
            /**
             * 导航切换
             */
            $('.myTab li').click(function () {
                var flag = $(this).attr('showFlag');
                if (flag == 'imgShow') {
                    $(this).find('img').attr('src', '../../../images/one1.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/tweo.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png')
                } else if (flag == 'tableShow') {
                    $(this).find('img').attr('src', '../../../images/tweos.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/trees.png');
                } else if (flag == 'unitShow') {
                    $(this).find('img').attr('src', '../../../images/treese.png');
                    $($(this).siblings()[0]).find('img').attr('src', '../../../images/one.png');
                    $($(this).siblings()[1]).find('img').attr('src', '../../../images/tweo.png');
                }
            });
            /**
             * Top20和Top10切换
             */
            $("#btnTop10").click(function(){
                $(this).removeClass("active").addClass("active").removeClass("btn-select").addClass("btn-select");
                $("#btnTop20").removeClass("active").removeClass("btn-select").removeClass("btn-unselect").addClass("btn-unselect");
                topCount=10;
                $("#topType").val(topCount);
                page.logic.reloadMostTopData();
            });
            $("#btnTop20").click(function(){
                $(this).removeClass("active").addClass("active").removeClass("btn-select").addClass("btn-select");
                $("#btnTop10").removeClass("active").removeClass("btn-select").removeClass("btn-unselect").addClass("btn-unselect");
                topCount=20;
                $("#topType").val(topCount);
                page.logic.reloadMostTopData();
            });
            $(window).resize(function () {
                $('#tableTop20').bootstrapTable('resetView');
                $('#tableOperate').bootstrapTable('resetView');
            });

            /**
             * 查询
             */
            $('#btnSearch').click(function () {
                if (OPAL.util.checkDateIsValid() == true) {
                    var ids = OPAL.ui.getComboMultipleSelect.getValues("unitIds");
                    if (ids == null || ids == undefined || ids.length == 0) {
                        if ($('#checkModel').is(':checked')) {
                            layer.msg("请选择车间！");
                        } else {
                            layer.msg("请选择装置！");
                        }
                        return;
                    }
                    page.logic.search();
                }
                $("#NowTimeSpan").html(OPAL.util.dateFormat(OPAL.util.strToDate(startDateTime), "yyyy-MM-dd") + " 00:00:00 至" + OPAL.util.dateFormat(OPAL.util.strToDate(endDateTime), "yyyy-MM-dd")+" 23:59:59");
            })
            $("#checkModel").click(function () {
                page.logic.showPrdtCellSelect();
            })
        },
        data:{
            param:{},
            //1.查询,2:操作数图形点击,3:操作数表格点击,4:操作数按装置显示点击
            type:-1
        },
        logic: {
            /**
             * 切换工厂 按车间显示
             */
            showPrdtCellSelect: function () {
                if ($("#checkModel").is(':checked')) {
                    $("#prdtIdsSpan").hide();
                    $("#prdtIds").hide();
                    $("#prdtSpan").hide();
                    $("#prdtDiv").hide();
                    $("#unitSpan").html('车间：');
                    $("#unitIds").combotree('toggleDisplayModel', true);
                    displayName = "车间";
                } else {
                    displayName = "装置";
                    $("#prdtIdsSpan").show();
                    $("#prdtSpan").show();
                    $("#prdtDiv").show();
                    $("#unitSpan").html('装置：');
                    $("#unitIds").combotree('toggleDisplayModel', false);
                }
                page.logic.toggleDisplayName();
            },
            /***
             * 查询
             */
            search: function () {
                isLoading = false;
                $("#mostFrequentOperateName").text("");
                //进行时间校验
                if (!OPAL.util.checkDateIsValid()) return;

                var prdtIds = $("#prdtIds").val();
                if ($("#checkModel").is(':checked')) {
                    displayName = "车间";
                }
                else if (prdtIds == "" || prdtIds == undefined || prdtIds.length == 0) {
                    displayName = "装置";
                }
                else {
                    displayName = "单元";
                }
                page.logic.toggleDisplayName();
                page.logic.initTipStartTimeAndEndTime();
                $("#btnSearch").prop('disabled', true);
                $("#random").val(Math.random());
                //操作数图表
                $.ajax({
                    url: operateAssessUrl,
                    data: $('#formSearch').serialize(),
                    dataType: 'json',
                    success: function (data) {
                        page.logic.initOperateChart(data);
                        page.logic.initOperateTable(data);
                        page.logic.initOperateShow(data);
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }

                });
                //最频繁操作图表
                page.logic.querySearchData($('#formSearch').serializeArray());
            },
            /**
             * 查询
             * @param data
             */
            querySearchData:function(data){
                page.data.param=data;
                page.data.type=1;
                $.ajax({
                    url: operateTopUrl,
                    data: $.param(data),
                    dataType: 'json',
                    success: function (data) {
                        page.logic.initMostFrequentOperateChart(data);
                        page.logic.initTop20Table(data);
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
            },
            /**
             * 操作数点击加载数据
             * @param postData
             */
            queryOperateChartClickData:function(postData){
                page.data.param=postData;
                page.data.type=2;
                $.ajax({
                    url: OPAL.API.aaUrl + '/alarmOperateAssess/getAlarmOperateAssessTop',
                    data: OPAL.form.param(postData),
                    dataType: 'json',
                    success: function (data) {
                        page.logic.initMostFrequentOperateChart(data);
                        page.logic.initTop20Table(data);
                    },
                    complete: function () {
                        $("#btnSearch").prop('disabled', false);
                    }
                });
            },
            /**
             * 重新加载数据
             */
            reloadMostTopData:function(){
                if(page.data.param==undefined||!page.data.param instanceof Array||page.data.type==-1)return;
                var exist=false;
                for(var i=0;i<page.data.param.length;i++){
                    if(page.data.param[i]['name']=='topType'){
                        page.data.param[i]['value']=topCount;
                        exist=true;
                        break;
                    }
                }
                if(exist==false){
                    page.data.param.push({'name':'topType','value':topCount});
                }
                if(page.data.type==1){
                   page.logic.querySearchData(page.data.param);
                }else if(page.data.type==2){
                   page.logic.queryOperateChartClickData(page.data.param);
                }
            },
            /**
             * 初始化操作数图表
             * @param data
             */
            initOperateChart: function (data) {
                if (operateChart && !operateChart.isDisposed()) {
                    operateChart.clear();
                    operateChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    operateChart = OPAL.ui.chart.initEmptyChart('operateChart');
                    return;
                }
                var option = {
                    color: ['#6699CC', '#669999', '#CC99CC', '#66CCCC', '#9999CC'],
                    legend: {
                        data: [],
                        left: 50,
                        itemWidth: 10, //设置icon大小
                        itemHeight: 10, //设置icon大小
                        top: 7
                    },
                    grid: {
                        left: '1%',
                        right: '3%',
                        // bottom: '5%',
                        top: '18%',
                        height: '200px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            formatter: function (value, index) {
                                var type = $("#dateType").val();
                                switch (type) {
                                    case "hour":
                                        return moment(value).format("HH:mm");
                                    case "day":
                                        return moment(value).format("MM-DD");
                                    case "week":
                                        return moment(value).format("MM-DD");
                                    case "month":
                                        return moment(value).format("YYYY-MM");
                                    default:
                                        return value;
                                }
                            },
                            interval: 'auto',
                            textStyle: {
                                color: '#333',
                            },
                        }
                    }],
                    tooltip: {
                        trigger: 'item',
                        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                            type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                        },
                        formatter: function (param) {
                            return option.series[param['seriesIndex']]['tooltip'][param['dataIndex']];
                        }
                    },
                    yAxis: [{
                        type: 'value',
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 'auto',

                            textStyle: {
                                color: '#333',
                                fontSize: 8,
                                fontStyle: 'normal'
                            }
                        }
                    }],
                    series: [],
                    dataZoom: [{
                        orient: "horizontal",
                        show: true,
                        start: 0,
                        end: 100,
                        height: 20,
                        bottom: 0,
                    }, {
                        type: 'slider',
                        yAxisIndex: 0,
                        filterMode: 'none',
                        width: 20
                    }],
                    originalData: []
                };
                operateChart = echarts.init(document.getElementById('operateChart'));

                for (var i = 0; i < data.length; i++) {
                    option.legend.data.push({
                        name: data[i]['name'],
                        icon: 'bar',
                    });
                    if (option.xAxis[0].data.length == 0) {
                        option.xAxis[0].data = data[i]['xaxis'];
                    }
                    var customData = new Array();
                    for (var j = 0; j < data[i]['counts'].length; j++) {
                        customData.push({
                            value: data[i]['counts'][j] == "0" ? undefined : data[i]['counts'][j],
                            itemStyle: {
                                normal: {color: data[i]['color']}
                            },
                            ids: data[i]['id']
                        });
                    }
                    option.series.push({
                        name: data[i]['name'],
                        type: 'bar',
                        barMaxWidth: '15',
                        data: customData,
                        tooltip: data[i]['tip'],
                    });
                }
                option.originalData = data[0]['list'];
                //处理小时数据
                dateType = $("#dateType").val();
                isFactoryMode = $("#checkModel").is(':checked');
                operateChart.on('click', function (params) {
                    $("#mostFrequentOperateName").text("（" + params.seriesName + "）");
                    //处理小时格式
                    if (dateType == "hour") {
                        if (!isFactoryMode) {
                            var config = {};
                            config['id'] = params.data['ids'];
                            config['startTime'] = moment(params['name']).format('YYYY-MM-DD HH:mm:ss');
                            config['endDate'] = moment(params['name']).add(1, 'hour').format('YYYY-MM-DD HH:mm:ss');
                            config['checkModel'] = 0;
                            page.logic.onOperateChartClick(config);
                            var seriesArr = option.series;
                            for (var j = 0; j < seriesArr.length; j++) {
                                for (var k = 0; k < seriesArr[j].data.length; k++) {
                                    seriesArr[j].data[k].itemStyle.normal.color = page.logic.getColor(j, option.color);
                                }
                                if (j == params.seriesIndex) {
                                    seriesArr[j].data[params.dataIndex].itemStyle.normal.color = highLightColor;
                                }
                            }
                            for (var i = 0; i < option.originalData.length; i++) {
                                if (params['name'] == option.originalData[i]['groupByTime'] && params['seriesName'] == option.originalData[i]['name'] && params['value'] == option.originalData[i]['counts']) {
                                    if (isFactoryMode) {
                                        option.originalData[i]['checkModel'] = 1;
                                        option.originalData[i]['id'] = option.originalData[i]['workshopId'];
                                    }
                                    else {
                                        option.originalData[i]['checkModel'] = 0;
                                    }
                                    page.logic.onOperateChartClick(option.originalData[i]);
                                    break;
                                }
                            }
                            operateChart.setOption(option);
                            return;
                        }
                    }
                    for (var i = 0; i < option.originalData.length; i++) {
                        if (params['name'] == option.originalData[i]['groupByTime'] && params['seriesName'] == option.originalData[i]['name'] && params['value'] == option.originalData[i]['counts']) {
                            if (isFactoryMode) {
                                option.originalData[i]['checkModel'] = 1;
                                option.originalData[i]['id'] = option.originalData[i]['workshopId'];
                            }
                            else {
                                option.originalData[i]['checkModel'] = 0;
                            }
                            page.logic.onOperateChartClick(option.originalData[i]);
                            break;
                        }
                    }

                    var seriesArr = option.series;
                    for (var j = 0; j < seriesArr.length; j++) {
                        for (var k = 0; k < seriesArr[j].data.length; k++) {
                            seriesArr[j].data[k].itemStyle.normal.color = page.logic.getColor(j, option.color);
                        }
                        if (j == params.seriesIndex) {
                            seriesArr[j].data[params.dataIndex].itemStyle.normal.color = highLightColor;
                        }
                    }
                    operateChart.setOption(option);
                });

                operateChart.setOption(option);
            },
            /**
             * 获取颜色
             * @param index
             * @param list
             * @returns {*}
             */
            getColor: function (index, list) {
                switch (index % 5) {
                    case 0:
                        return list[0];
                    case 1:
                        return list[1];
                    case 2:
                        return list[2];
                    case 3:
                        return list[3];
                    case 4:
                        return list[4];
                }
            },
            /***
             * 操作数图表点击
             * @param item
             */
            onOperateChartClick: function (item) {
                var queryStartTime = OPAL.util.dateFormat(OPAL.util.strToDate(item["startTime"]), "yyyy-MM-dd HH:mm:ss");
                var queryEndTime = OPAL.util.dateFormat(OPAL.util.strToDate(item["endDate"]), "yyyy-MM-dd HH:mm:ss");
                //处理参数
                var postData = new Array();
                var formData = $('#formSearch').serializeArray();
                for (var i = 0; i < formData.length; i++) {
                    if (formData[i]["name"] != "prdtIds" && formData[i]["name"] != "unitIds" && formData[i]["name"] != "startTime" && formData[i]["name"] != "endTime") {
                        postData.push(formData[i]);
                    }
                }
                if (displayName == "装置") {
                    postData.push({'name': 'unitIds', value: item.id});
                } else if (displayName == "单元") {
                    postData.push({'name': 'prdtIds', value: item.id});
                }
                else if (displayName == "车间") {
                    postData.push({'name': 'unitIds', value: item.id});
                }

                //处理查询日期
                postData.push({'name': 'startTime', value: queryStartTime});
                postData.push({'name': 'endTime', value: queryEndTime});
                dateArray = {
                    startTime: queryStartTime,
                    endTime: queryEndTime
                }
                //发送请求
               page.logic.queryOperateChartClickData(postData);
            },

            /**
             * 初始化最频繁操作Top20
             * @param data
             */
            initMostFrequentOperateChart: function (data) {
                if (mostFrequentOperateChart && !mostFrequentOperateChart.isDisposed()) {
                    mostFrequentOperateChart.clear();
                    mostFrequentOperateChart.dispose();
                }
                if (data == null || data == undefined || data.length == 0) {
                    mostFrequentOperateChart = OPAL.ui.chart.initEmptyChart("mostFrequentOperate");
                    return;
                }
                var option = {
                    color: ['#3398DB'],
                    grid: {
                        left: '1%',
                        right: '1%',
                        // bottom: '1%',
                        top: '3%',
                        height: '275px',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        axisTick: {
                            alignWithLabel: true
                        },
                        splitLine: { //网格线
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisLabel: {
                            interval: 0,
                            rotate: 20
                        },
                        flagName: []
                    }],
                    yAxis: [{
                        type: 'value',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },

                    }],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                            type: ''        // 默认为直线，可选为：'line' | 'shadow'
                        },
                        formatter: function (params) {
                            return "位号:" + params[0]['axisValue'] + "&nbsp;&nbsp; 报警标识:" + option.xAxis[0]['flagName'][params[0]['dataIndex']] + "<br>操作数:" + params[0]['value'];
                        }
                    },
                    series: [{
                        type: 'bar',
                        barMaxWidth: '15',
                        data: []
                    }]
                };
                mostFrequentOperateChart = echarts.init(document.getElementById('mostFrequentOperate'));
                var totalArray = new Array();
                var tagArray = new Array();
                var flagArray = new Array();
                var flagIdArray = new Array();
                for (var i = 0; i < data.length; i++) {
                    totalArray.push(data[i]["count"]);
                    tagArray.push(data[i]['tag']);
                    flagArray.push(data[i]['flagName']);
                    flagIdArray.push(data[i]['alarmFlagId']);
                }
                option.xAxis[0].data = tagArray;
                option.series[0].data = totalArray;
                option.xAxis[0].flagName = flagArray;
                mostFrequentOperateChart.setOption(option);
                /**
                * 最频繁的操作详情信息
                */
                mostFrequentOperateChart.on("click",function(params){
                    layer.open({
                        type: 2,
                        title: '',
                        closeBtn: 0,
                        area: ['90%', '75%'],
                        shadeClose: false,
                        content: 'OperateDetail.html?'+ Math.random(),
                        success: function(layero, index) {
                            var body = layer.getChildFrame('body', index);
                            var iframeWin = window[layero.find('iframe')[0]['name']];
                            var data = {
                                "alarmPointTag": tagArray[params.dataIndex],
                                "alarmFlagId": flagIdArray[params.dataIndex],
                                'startTime': $("#mostFrequentOperateName").html() == '' ? $("#startTime").val():dateArray.startTime,
                                'endTime': $("#mostFrequentOperateName").html() == '' ? $("#endTime").val():dateArray.endTime,
                            };
                            iframeWin.page.logic.setData(data);
                        }
                    });
                })
            },
            /**
             * 按照装置显示
             */
            initOperateShow: function (data) {
                if (unitChart && !unitChart.isDisposed()) {
                    unitChart.clear();
                    unitChart.dispose();
                }
                $("#totalCounts").text("0次");
                if (data == null || data == undefined || data.length == 0) {
                    unitChart = OPAL.ui.chart.initEmptyChart('unitChart');
                    return;
                }
                unitChart = echarts.init(document.getElementById('unitChart'));
                var option = {
                    color: ['#3398DB'],
                    grid: {
                        left: '1%',
                        right: '3%',
                        height: '200px',
                        containLabel: true
                    },
                    legend: {
                        data: [],
                        left: 50,
                        right: 100,
                        itemWidth: 10, //设置icon大小
                        itemHeight: 10, //设置icon大小
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                            type: ''        // 默认为直线，可选为：'line' | 'shadow'
                        },
                        formatter: function (param) {
                            return "从: " + startDateTime + " 至:" + endDateTime + "<br>" + param[0]['name'] + " : " + param[0]['value'];
                        }
                    },
                    xAxis: [{
                        type: 'category',
                        data: [],
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                        axisTick: {
                            alignWithLabel: true
                        }
                    }],
                    yAxis: [{
                        type: 'value',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#e5e5e5'],
                                type: 'solid'
                            }
                        },
                    }],
                    series: [{
                        name: '',
                        type: 'bar',
                        barMaxWidth: '15px',
                        itemStyle: {
                            normal: {color: '#348fe2'}
                        },
                        data: [],
                        toolTip: [],
                    }],
                    dataZoom: [{
                        orient: "horizontal",
                        show: true,
                        start: 0,
                        end: 100,
                        height: 20,
                        bottom: 0,
                    }, {
                        type: 'slider',
                        yAxisIndex: 0,
                        filterMode: 'none',
                        width: 20
                    }],
                    originalData: []
                };


                var dataArray = new Array();
                var nameArray = new Array();
                var toolTipArray = new Array();
                var totalCounts = 0;
                for (var i = 0; i < data.length; i++) {
                    totalCounts = totalCounts + parseFloat(data[i]["totalCounts"]);
                    dataArray.push(data[i]["totalCounts"]);
                    nameArray.push(data[i]["name"]);
                    toolTipArray.push(data[i]["tip"]);
                }
                option.series[0].data = dataArray;
                option.series[0].toolTip = toolTipArray;
                option.xAxis[0].data = nameArray;
                option.originalData = data[0]['list'];
                $("#totalCounts").text(totalCounts + "次");
                unitChart.on('click', function (params) {
                    $("#mostFrequentOperateName").text("（" + params.name + "）");
                    for (var i = 0; i < option.originalData.length; i++) {
                        if (params['name'] == option.originalData[i]['name']) {
                            //处理查询时间
                            var queryData = option.originalData[i];
                            queryData["startTime"] = startDateTime;
                            queryData["endDate"] = endDateTime;
                            if (isFactoryMode) {
                                queryData['checkModel'] = 1;
                                queryData['id'] = queryData['workshopId'];
                            }
                            else {
                                queryData['checkModel'] = 0;
                            }
                            page.logic.onOperateChartClick(queryData);
                            break;
                        }
                    }
                    var i = params.dataIndex;
                    option.series[0].itemStyle = {
                        normal: {
                            color: function (param) {
                                if (param.dataIndex == i) {
                                    return 'rgba(107,109,117,0.2)';
                                } else {
                                    return "#348fe2";
                                }
                            }
                        }
                    }
                    unitChart.setOption(option);
                });
                unitChart.setOption(option);
            },
            /**
             * 初始化最频繁操作Top20 列表
             */
            initTop20Table: function (data) {
                $("#tableTop20").bootstrapTable("removeAll");
                if (data == null || data == undefined || data.length == 0) {
                    return;
                }
                $("#tableTop20").bootstrapTable("load", data);
                $("#tableTop20").bootstrapTable("refreshOptions", {
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        field: 'tag',
                        align: 'left',
                        width: '100px',
                        title: '位号'
                    }, {
                        field: 'location',
                        align: 'left',
                        width: '100px',
                        title: '位置描述'
                    }, {
                        field: 'flagName',
                        align: 'center',
                        title: '报警标识',
                        width: '100px'
                    }, {
                        field: 'count',
                        align: 'right',
                        title: '操作数',
                        width: '100px'
                    }, {
                        field: 'unitName',
                        align: 'left',
                        title: '装置',
                        width: '120px'
                    }, {
                        field: 'prdtName',
                        align: 'left',
                        title: '生产单元',
                        width: '120px'
                    }],
                    sidePagination: "client",
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100]
                });

            },
            /**
             * 初始化表格
             */
            initTable: function () {
                $('#tableTop20').bootstrapTable({
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '50px'
                    }, {
                        field: 'tag',
                        align: 'left',
                        width: '100px',
                        title: '位号'
                    }, {
                        field: 'flagName',
                        align: 'center',
                        title: '报警标识',
                        width: '100px'
                    }, {
                        field: 'count',
                        align: 'right',
                        title: '操作数',
                        width: '100px'
                    }, {
                        field: 'unitName',
                        align: 'left',
                        title: '装置',
                        width: '120px'
                    }, {
                        field: 'prdtName',
                        align: 'left',
                        title: '生产单元',
                        width: '120px'
                    }],
                    sidePagination: "client",
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100]
                });
                $('#tableOperate').bootstrapTable({
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'startTime',
                        align: 'center',
                        title: '时间',
                        formatter: function (value, row, index) {
                            return OPAL.util.dateFormat(OPAL.util.strToDate(value), "yyyy-MM-dd HH:mm:ss");
                        },
                        width: '200px'
                    }, {
                        field: 'name',
                        title: '生产单元',
                        align: 'left',
                        width: '200px'
                    }, {
                        field: 'counts',
                        align: 'right',
                        title: '操作数',
                        width: '200px'
                    }],
                    sidePagination: "client",
                    queryParamsType: "undefined",
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    cache: false,
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    onClickRow: function (row) {
                        $("#mostFrequentOperateName").text("（" + row['name'] + "）");
                        page.logic.onOperateChartClick(row);
                    }
                });
            },
            /**
             * 初始化操作数列表
             */
            initOperateTable: function (data) {
                $("#tableOperate").bootstrapTable("removeAll");
                if (data == null || data == undefined || data.length == 0) {
                    $("#tableOperate").bootstrapTable("load", []);
                } else {
                    $("#tableOperate").bootstrapTable("load", data[0]['list']);
                }
                $('#tableOperate').bootstrapTable('refreshOptions', {
                    columns: [{
                        title: "序号",
                        formatter: function (value, row, index) {
                            return index + 1;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        field: 'startTime',
                        title: '时间',
                        align: 'center',
                        width: '30%',
                        formatter: function (value, row, index) {
                            return OPAL.util.dateFormat(new Date(value), "yyyy-MM-dd HH:mm:ss");
                        }
                    }, {
                        field: 'name',
                        align: 'left',
                        width: '30%',
                        title: displayName == "单元" ? "生产单元" : displayName,
                    }, {
                        field: 'counts',
                        title: '操作数',
                        align: 'right',
                        formatter: function (value, row, index) {
                            return "<a style='text-decoration: underline;color: #348fe2;cursor: pointer;' onclick='page.logic.showOperateDetail(" + JSON.stringify(row) + ")'>" + value + "</a>";
                        }
                    }],
                    sidePagination: "client",
                    queryParamsType: "undefined",
                    formatNoMatches: function () {
                        return "";
                    },
                    formatLoadingMessage: function () {
                        return "";
                    },
                    cache: false,
                    pagination: true,
                    pageSize: 5,
                    paginationPreText: '<',
                    paginationNextText: '>',
                    showColumns: false,
                    pageList: [5, 10, 20, 50, 100],
                    onClickRow: function (row, element) {
                        var trs = $("#tableOperate tr");
                        for (var i = 0; i < trs.length; i++) {
                            $(trs[i]).removeClass("selectedd");
                        }
                        element.addClass("selectedd");
                        $("#mostFrequentOperateName").text("（" + row['name'] + "）");
                        var prdtIds = $("#prdtIds").val();
                        if (prdtIds == "" || prdtIds == undefined || prdtIds.length == 0) {
                            displayName = "装置";
                        }
                        else {
                            displayName = "单元";
                        }
                        if (isFactoryMode) {
                            row['checkModel'] = 1;
                            row['id'] = row['workshopId'];
                        }
                        else {
                            row['checkModel'] = 0;
                        }
                        page.logic.onOperateChartClick(row);
                    }
                });
                //解决table刷新时位置移动的bug
                $('#tableOperate').attr('style', 'border-top:1px solid #cecece ;margin-top: 43px;');
            },
            /**
             * 切换显示名称
             */
            toggleDisplayName: function () {
                $("#displayName").text("按" + displayName + "显示");
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function () {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function (node, checked) {
                        var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (unitIds != undefined && unitIds.length == 1) {
                            $("#prdtIds").combo('enable');
                            $('#prdtIds').combotree('setValues', []);
                            page.logic.searchUnitPrdt(unitIds[0]);
                            $('.textbox,.combo').css('background-color','');
                        } else {
                            $('#prdtIds').combotree('setValues', []);
                            $("#prdtIds").combo('disable');
                            $('.textbox-disabled').css('background-color','rgb(235, 235, 228)');
                        }
                    }
                }, false);
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect('prdtIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function() {
                    var treeView = $("#prdtIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtIds").combotree("checkAllNodes");
                });
            },
            /**
             * 操作数详情
             */
            showOperateDetail: function (row) {
                layer.open({
                    type: 2,
                    title: '操作数详情',
                    closeBtn: 1,
                    area: ['85%', '420px'],
                    shadeClose: false,
                    content: 'Detail.html?'+ Math.random(),
                    success: function (layero, index) {
                        var body = layer.getChildFrame('body', index);
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        var prdtIds = $("#prdtIds").val();
                        if (prdtIds == "" || prdtIds == undefined || prdtIds.length == 0) {
                            displayName = "装置";
                        }
                        else {
                            displayName = "单元";
                        }
                        row['type'] = (displayName == "装置" ? "unit" : "prdt");
                        if (isFactoryMode) {
                            row['checkModel'] = 1;
                            row['id'] = row['workshopId'];
                        }
                        else {
                            row['checkModel'] = 0;
                        }
                        iframeWin.page.logic.setData(row);
                    }
                });
            },

            /**
             * 初始化装置或者单元的提示开始时间和结束时间
             */
            initTipStartTimeAndEndTime: function () {
                OPAL.util.getSearchTime({
                    startTime: $("#startTime").val(),
                    endTime: $("#endTime").val()
                }, function (data) {
                    startDateTime = OPAL.util.dateFormat(OPAL.util.strToDate(data["startDate"]), "yyyy-MM-dd HH:mm:ss");
                    endDateTime = OPAL.util.dateFormat(OPAL.util.strToDate(data["endDate"]), "yyyy-MM-dd HH:mm:ss");
                });
            },
            /**
             * 初始化用户类型
             */
            initUserType: function () {
                OPAL.util.getUserType(function (data) {
                    if (data != undefined && data[0] != undefined && data[0]["value"] != undefined) {
                        //如果是工厂用户
                        if (data[0]["value"] == 1) {
                            userType = data[0]["value"];
                            $('#checkModel').trigger('click');
                        }
                    }
                });
            }
        }
    };
    page.init();
    window.page = page;
});
