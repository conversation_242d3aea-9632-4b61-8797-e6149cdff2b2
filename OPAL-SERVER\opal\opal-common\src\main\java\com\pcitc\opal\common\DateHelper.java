package com.pcitc.opal.common;

import org.apache.commons.lang.time.DateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/*
 * Date日期转换实现类
 * 模块编号：pcitc_opal_common_class_DateHelper
 * 作    者：jiangtao.xue
 * 创建时间：2017/11/16
 * 修改编号：1
 * 描    述：Date日期转换实现类
 */
public class DateHelper {
    /**
     * default date format: year-month-day hour:minute:second
     * 默认日期格式(日期和时间);
     */
    public final static String DATE_FMT_DEFAULT = "yyyy-MM-dd HH:mm:ss";

    /**
     * date format1;
     * 日期格式;
     */
    public final static String DATE_FMT_1 = "yyyy-MM-dd";

    public final static String[] DATE_PATTERN = new String[]{
            DATE_FMT_DEFAULT,
            DATE_FMT_1,
            "yyyy/MM/dd"};

    /**
     * format the date to special str;
     * 日期格式转换成指定字符串格式
     * @param date
     * @param pattern
     * @return
     */
    public static String format(Date date, String pattern){
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    /**
     * format the date by default;
     * 日期格式转换成默认格式
     * @param date
     * @return
     */
    public static String format(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FMT_DEFAULT);
        return sdf.format(date);
    }

    public static Date parseDate(String str){
        Date result = null;
        try {
            result =DateUtils.parseDate(str, DATE_PATTERN);
        } catch (ParseException e) {
            result = null;
            e.printStackTrace();
        }
        return result;
    }

    public static Date now(){
        return new Date();
    }

    public static String strDate(Date beginTime){
        String str = "";
        Calendar c= Calendar.getInstance();
        c.setTime(beginTime);
        int weekDay = c.get(Calendar.DAY_OF_WEEK);
        if(Calendar.MONDAY == weekDay){
            str = "MONDAY";
        }else if(Calendar.TUESDAY==weekDay){
            str = "TUESDAY";
        }else if(Calendar.WEDNESDAY==weekDay){
            str ="WEDNESDAY";
        }else if(Calendar.THURSDAY==weekDay){
            str ="THURSDAY";
        }else if(Calendar.FRIDAY==weekDay){
            str ="FRIDAY";
        }else if(Calendar.SATURDAY==weekDay){
            str ="SATURDAY";
        }else {
            str ="SUNDAY";
        }
        return str;
    }
}
