package com.pcitc.opal.pm.bll.imp;

import com.pcitc.opal.pm.bll.SendMsgService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @USER: chenbo
 * @DATE: 2023/1/30
 * @TIME: 11:16
 * @DESC: 洛阳短信发送实现类
 **/
@Slf4j
@Component
@ConditionalOnProperty(name = "send_type", havingValue = "ly", matchIfMissing = false)
public class SendMsgServiceImplLY implements SendMsgService {
    @Override
    public Map<String, String> sendBatch(List<String> mobiles, String content) {
        //key->手机号，value->发送结果
        HashMap<String, String> resMap = new HashMap<>();
        OkHttpClient client = new OkHttpClient();


        StringBuilder msg = new StringBuilder();
        //拼接发送短信报文
        for (int i = 0; i < mobiles.size(); i++) {
            msg.append("<message>").append("<id>").append(i).append("</id>").append("<mobile>").append(mobiles.get(i)).append("</mobile>").append("<content>").append(content).append("</content>").append("</message>");
        }


        StringBuilder result = new StringBuilder();

        RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_XML_VALUE),
                "<?xml version='1.0' encoding='utf-8'?>\n" +
                        "<haikemisc namespace=\"http://www.haikemobile.cn/design/misc/smsapi\" version=\"1.0\">\n" +
                        "    <head>\n" +
                        "        <authinfo>\n" +
                        "            <account>wuhm1</account>\n" +
                        "            <password>hhz!@#123</password>\n" +
                        "            <apicode>gypw</apicode>\n" +
                        "        </authinfo>\n" +
                        "        <opertype>SmsSendMuli</opertype>\n" +
                        "        <actioncode>1</actioncode>\n" +
                        "        <snaptime>************</snaptime>\n" +
                        "        <token>9c9b7d9c7c03e4fc0170a29a61fce4bf</token>\n" +
                        "        <version>1.50</version>\n" +
                        "    </head>\n" +
                        "    <body>\n" +
                        "        <svcsms>\n" +
                        msg.toString()+
                        "        </svcsms>\n" +
                        "        <svccmd>\n" +
                        "            <priority>2</priority>\n" +
                        "            <regdeliver>1</regdeliver>\n" +
                        "            <appendname>true</appendname>\n" +
                        "            <needreply>true</needreply>\n" +
                        "            <useoriginaladdr>false</useoriginaladdr>\n" +
                        "            <sendattime>false</sendattime>\n" +
                        "            <sendtime>2015-01-12 01:02:12</sendtime>\n" +
                        "        </svccmd>\n" +
                        "    </body>\n" +
                        "</haikemisc>");


        Request request = new Request.Builder()
                .url("http://172.168.19.25/sms/sms/api/misc.do?mt")
                .post(body)
                .addHeader("content-type", "application/xml")
                .build();
        try {
            Response response = client.newCall(request).execute();
            boolean successful = response.isSuccessful();
            if (successful) {
                String res = Objects.requireNonNull(response.body()).string();
                Document document = DocumentHelper.parseText(res);
//                document.getRootElement().element("body").element("resultcode").getData()
                Element root = document.getRootElement().element("body");
                String resultMsg = root.elementText("resultmsg");//返回结果
                String accessCount = root.elementText("accesscount");//请求中的手机号个数
                String acceptCount = root.elementText("acceptcount");//发送成功的手机号个数
                result.append("返回结果：").append(resultMsg).append(",请求中的手机号个数：").append(accessCount).append(",发送成功的手机号个数：").append(acceptCount);
            }
        } catch (IOException e) {
            log.error("当前发送短信接口调用失败，手机号为{}，短信内容为{}，异常内容{}", StringUtils.join(mobiles, ","), content, e.getMessage());
            result.append("当前发送短信接口调用失败，手机号为").append(StringUtils.join(mobiles, ",")).append("，异常内容").append(e.getMessage());
            e.printStackTrace();
        } catch (DocumentException e) {
            log.error("当前发送短信返回结果解析失败，手机号为{}，短信内容为{}，异常内容{}", StringUtils.join(mobiles, ","), content, e.getMessage());
            result.append("当前发送短信接口调用失败，手机号为").append(StringUtils.join(mobiles, ",")).append("，异常内容").append(e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            log.error("发送异常" + e.toString());
        }
        //批量发短信接口无法查看某个手机号是否接受短信，只能确定发送成功的个数
        mobiles.forEach(x -> resMap.put(x, result.toString()));
        return resMap;
    }
}
