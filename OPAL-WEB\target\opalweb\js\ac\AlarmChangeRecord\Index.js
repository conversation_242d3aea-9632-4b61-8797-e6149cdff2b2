var searchUrl = OPAL.API.acUrl + '/alarmChangeRecord';
var exportAlarmChangeRecordUrl = OPAL.API.acUrl + '/alarmChangeRecord/exportAlarmChangeRecord';
var commonUnitTreeUrl = OPAL.API.commUrl + "/getAllUnit";
var prdtCellUrl = OPAL.API.commUrl + '/getUnitPrdtCell';
var workTeamUrl = OPAL.API.commUrl + "/getWorkTeam";
var isLoading = true;
var AlarmChangeRecordBusinessType;
$(function() {
    var page = {
        /**
         * 初始化
         */
        init: function() {
            AlarmChangeRecordBusinessType = page.logic.getUrlParam("AlarmChangeRecordBusinessType");
            if(AlarmChangeRecordBusinessType == 2){
                $("#titles").html("调整记录");
            }else {
                $("#titles").html("调整记录");
            }

            this.bindUi();
            //扩展日期插件
            OPAL.util.extendDate();
            // 初始化 时间设置
            page.logic.initTime();
            //初始化查询装置树
            page.logic.initUnitTree();
            //初始化表格
            page.logic.initTable();
            $('#workTeamIds').html("");
            $("#workTeamIds").prop('disabled', true);

            //装置赋值
            if (isLoading&&(page.data.param.unitIds==null||page.data.param.unitIds==undefined||page.data.param.unitIds.length==0)) {
                var cookieValue = OPAL.util.getCookieByPageCode("AlarmChangeRecord");
                if (cookieValue !== null && cookieValue !== undefined && cookieValue.length > 0) {
                    $('#unitIds').combotree('setValue', cookieValue);
                }
            }

            //默认查询数据
            page.logic.search();
        },
        bindUi: function() {
            //查询
            $('#search').click(function() {
                isLoading=false;
                page.logic.search();
            });
            // 导出
            $('#AlarmChangeRecordExport').click(function() {
                page.logic.exportExcel();
            });
            $("#startTime").unbind("change");
            $("#endTime").unbind("change");
            $("#startTime").change(function() {
                page.logic.initWorkTeam();
            })
            $("#endTime").change(function() {
                page.logic.initWorkTeam();
            })
        },
        data: {
            // 设置查询参数
            param: {}
        },
        logic: {
            initTable: function() {
                OPAL.ui.initBootstrapTable2("table", {
                    columns: [{
                        title: "序号",
                        formatter: function(value, row, index) {
                            var data = page.data.param;
                            var pageNumber = data.pageNumber;
                            var pageSize = data.pageSize;
                            return index + 1 + (pageNumber - 1 ) * pageSize;
                        },
                        rowspan: 1,
                        align: 'center',
                        width: '80px'
                    }, {
                        title: "调整时间",
                        field: 'startTime',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }, {
                        title: "装置",
                        field: 'unitName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px',
                        
                    }, {
                        title: "生产单元",
                        field: 'prdtCellName',
                        rowspan: 1,
                        align: 'left',
                        width: '120px'
                    }, {
                        title: "班组",
                        field: 'workTeamSName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "位号",
                        field: 'tag',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "参数描述",
                        field: 'location',
                        rowspan: 1,
                        align: 'left',
                        width: '150px'
                    }, {
                        title: "报警标识",
                        field: 'alarmFlagName',
                        rowspan: 1,
                        align: 'center',
                        width: '100px'
                    }, {
                        title: "调整类型",
                        field: 'eventTypeName',
                        rowspan: 1,
                        align: 'left',
                        width: '100px'
                    }, {
                        title: "调整前的值",
                        field: 'previousValue',
                        rowspan: 1,
                        align: 'right',
                        width: '100px'
                    }, {
                        title: "当前值",
                        field: 'nowValue',
                        rowspan: 1,
                        align: 'right',
                        width: '100px'
                    }, {
                        title: "工艺卡片值",
                        field: 'craft',
                        rowspan: 1,
                        align: 'center',
                        width: '150px'
                    }],
                    rowStyle: function(row, index) {
                         var style;
                         if(row.flag == 1){
                             style = 'redRow'
                         }
                         return {
                             classes: style
                         }
                     }
                }, page.logic.queryParams,"search")
            },
            /**
             * 查询参数
             * @param params
             * @returns {{pageSize: *, pageNumber: *}}
             */
            queryParams: function(p) {
                var param = {
                    pageSize: p.pageSize,
                    pageNumber: p.pageNumber,
                    sortOrder: p.sortOrder
                };
                return $.extend(page.data.param, param);
            },
            /**
             * 设置日期插件
             */
            initTime: function() {
                OPAL.ui.initDateTimePeriodPicker({
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm:ss'
                },function(){
                    page.logic.initWorkTeam();
                })
            },
            /**
             * 初始化装置树
             */
            initUnitTree: function() {
                OPAL.ui.getEasyUIComboTreeSelect('unitIds', commonUnitTreeUrl, 'id', 'parentId', 'sname', {
                    onChange: function(nodes) {
                        var nodeIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                        if (nodeIds.length == 1) {
                            $("#prdtCellIds").combo('enable');
                            $("#prdtCellIds").combotree('setValues', []);
                            page.logic.searchUnitPrdt(nodeIds[0]);
                            $("#workTeamIds").prop('disabled', false);
                            page.logic.initWorkTeam();
                            $('.textbox,.combo').css('background-color','');
                        } else {
                            $("#prdtCellIds").combotree('setValues', []);
                            $("#prdtCellIds").combo('disable');
                            $('#workTeamIds').html("");
                            $("#workTeamIds").prop('disabled', true);
                            $('.textbox-disabled').css('background-color','rgb(235, 235, 228)');
                        }
                    }
                }, false);
            },
            /**
             * 设置参数
             */
            setParams: function() {
                page.data.param = OPAL.form.getData("searchForm");
                page.data.param.AlarmChangeRecordBusinessType = AlarmChangeRecordBusinessType;
            },
            /**
             * 搜索
             */
            search: function() {
                if (!OPAL.util.checkDateIsValid()) {
                    return false;
                }
                $("#search").attr('disabled', true);
                page.logic.setParams();
                $("#table").bootstrapTable('refresh', {
                    "url": searchUrl,
                    "pageNumber": 1
                });
            },
            /**
             * 查询装置下的生产单元
             */
            searchUnitPrdt: function(unitId) {
                OPAL.ui.getComboMultipleSelect('prdtCellIds', prdtCellUrl, {
                    keyField: "prdtCellId",
                    valueField: "sname",
                    data: {
                        "unitId": unitId,
                        'isAll': true
                    }
                }, true, function() {
                    var treeView = $("#prdtCellIds").combotree('tree');
                    var nd = treeView.tree('find', -1);
                    if (nd != null) {
                        treeView.tree('update', {
                            target: nd.target,
                            text: '全选'
                        });
                    }
                    $("#prdtCellIds").combotree("checkAllNodes");
                });
            },
            /**
             * 初始化班组选择
             */
            initWorkTeam: function() {
                var unitIds = OPAL.ui.getComboMultipleSelect.getValues('unitIds');
                if (unitIds.length != 1) return;
                if ($("#startTime").val() == '' || $("#endTime").val() == ''){
                    $('#workTeamIds').html("");
                    $("#workTeamIds").prop('disabled', true);
                    return;
                }else {
                    $("#workTeamIds").prop('disabled', false);
                }
                OPAL.ui.getCombobox("workTeamIds", workTeamUrl, {
                    keyField: "workTeamId",
                    valueField: "workTeamSName",
                    selectFirstRecord: true,
                    mapManyValues: true, //是否一条记录匹配多个隐藏值
                    mapManyDataFieldName: 'workTeamIdList',
                    data: {
                        "startTime": $("#startTime").val(),
                        "endTime": $("#endTime").val(),
                        "unitId": unitIds[0],
                    }
                }, null);
            },
            /**
             * 导出
             */
            exportExcel: function() {
                var titleArray = new Array();
                var tableTitle = $('#table').bootstrapTable('getOptions').columns[0];
                $.each(tableTitle, function(i, el) {
                    if (i >= 1) {
                        titleArray.push({
                            'key': el.field,
                            'value': el.title
                        })
                    }
                })
                var data = {};
                var pageSize = $('#table').bootstrapTable('getOptions').pageSize;
                var pageNumber = $('#table').bootstrapTable('getOptions').pageNumber;
                data.titles = JSON.stringify(titleArray);
                data.pageSize = pageSize;
                data.pageNumber = pageNumber;
                page.logic.setParams();
                $.extend(data, page.data.param);
                $('#form').attr('action', exportAlarmChangeRecordUrl);
                $('#titlesExport').val(data.titles);
                $('#pageSizeExport').val(data.pageSize);
                $('#pageNumberExport').val(data.pageNumber);
                $('#unitIdsExport').val(data.unitIds);
                $('#prdtCellIdsExport').val(data.prdtCellIds);
                $('#startTimeForm').val(data.startTime);
                $('#endTimeForm').val(data.endTime);
                $('#workTeamIdsForm').val(data.workTeamIds);
                $('#AlarmChangeRecordBusinessType').val(data.AlarmChangeRecordBusinessType);
                $('#form').submit();
            },
            //获取浏览器地址栏参数
            getUrlParam: function(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) {
                    return unescape(r[2]);
                }
                return null;
            }
        }
    };
    page.init();
    window.page = page;
});