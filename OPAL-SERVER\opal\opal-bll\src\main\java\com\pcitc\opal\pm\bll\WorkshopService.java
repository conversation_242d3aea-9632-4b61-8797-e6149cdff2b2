package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.bll.entity.DBFactoryEntity;
import com.pcitc.opal.pm.bll.entity.DBWorkshopEntity;
import com.pcitc.opal.pm.pojo.Workshop;
import org.springframework.stereotype.Service;

import java.util.List;

/*
 * 车间业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_WorkshopService
 * 作       者：dageng.sun
 * 创建时间：2017/12/11
 * 修改编号：1
 * 描       述：车间业务逻辑层接口 
 */
@Service
public interface WorkshopService {
	
	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopEntity 车间实体
	 * @return
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult addWorkshop(DBWorkshopEntity workshopEntity) throws Exception;
	
	/**
	 * 删除车间维护数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopIds 车间主键id集合
	 * @return
	 * @throws Exception 
	 * @return CommonResult 消息结果类
	 */
	CommonResult deleteWorkshop(Long[] workshopIds) throws Exception;
	
	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopEntity 车间实体
	 * @return 
	 * @return CommonResult 消息结果类
	 */
	CommonResult updateWorkshop(DBWorkshopEntity workshopEntity) throws Exception;
	
	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param workshopId 车间实体id
	 * @return
	 * @throws Exception 
	 * @return DBWorkshopEntity 车间实体
	 */
	DBWorkshopEntity getSingleWorkshop(Long workshopId) throws Exception;
	
	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-12-11
	 * @param factoryId 工厂id
	 * @param workshopName 车间的名称或简称
	 * @param stdCode 标准编码
	 * @param inUse 是否启用
	 * @param page 分页对象
	 * @return
	 * @throws Exception 
	 * @return PaginationBean<DBWorkshopEntity> 翻页对象
	 */
	PaginationBean<DBWorkshopEntity> getWorkshop(Long factoryId, String workshopName, String stdCode, Integer inUse, Pagination page) throws  Exception;

	/**
	 * 获取工厂列表
	 * @param isAll  是否显示全部选项
	 * @return 工厂实体
	 */
    List<DBFactoryEntity> getFactoryList(boolean isAll) throws Exception;

	Workshop getWorkShopByStdCode(String stdCode);
	//定时job更新数据专用
	CommonResult updateWorkshopInfoById(Workshop workshop) throws Exception;
}
