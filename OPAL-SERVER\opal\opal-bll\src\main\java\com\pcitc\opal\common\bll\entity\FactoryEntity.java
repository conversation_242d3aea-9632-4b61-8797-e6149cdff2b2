package com.pcitc.opal.common.bll.entity;

/*
 * 工厂实体
 * 模块编号：pcitc_opal_bll_class_FactoryEntity
 * 作       者：xuelei.wang
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描       述：工厂实体
 */
public class FactoryEntity extends BasicEntity {

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 简称
	 */
	private String sname;

	/**
	 * 标准编码
	 */
	private String stdCode;

	/**
	 * 排序
	 */
	private Integer sortNum;
	
	/**
	 * 描述
	 */
	private String des;
	/**
	 * 组织机构编码
	 */
	private String orgCode;
	/**
	 * 组织机构名称
	 */
	private String orgName;
	/**
	 * 组织机构简称
	 */
	private String orgAlias;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSname() {
		return sname;
	}

	public void setSname(String sname) {
		this.sname = sname;
	}

	public String getStdCode() {
		return stdCode;
	}

	public void setStdCode(String stdCode) {
		this.stdCode = stdCode;
	}

	public Integer getSortNum() {
		return sortNum;
	}

	public void setSortNum(Integer sortNum) {
		this.sortNum = sortNum;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgAlias() {
		return orgAlias;
	}

	public void setOrgAlias(String orgAlias) {
		this.orgAlias = orgAlias;
	}
}