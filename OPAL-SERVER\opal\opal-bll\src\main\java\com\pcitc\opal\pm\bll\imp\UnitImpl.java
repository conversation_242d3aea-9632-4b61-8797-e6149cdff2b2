package com.pcitc.opal.pm.bll.imp;

import com.pcitc.imp.common.exception.BusiException;
import com.pcitc.opal.common.*;
import com.pcitc.opal.common.bll.BasicDataService;
import com.pcitc.opal.common.bll.ShiftService;
import com.pcitc.opal.common.bll.entity.ShiftWorkTeamEntity;
import com.pcitc.opal.pm.bll.UnitService;
import com.pcitc.opal.pm.bll.entity.DBFactoryEntity;
import com.pcitc.opal.pm.bll.entity.DBUnitEntity;
import com.pcitc.opal.pm.bll.entity.DBWorkshopEntity;
import com.pcitc.opal.pm.bll.entity.UnitPersonEntity;
import com.pcitc.opal.pm.dao.FactoryRepository;
import com.pcitc.opal.pm.dao.UnitPersonRepository;
import com.pcitc.opal.pm.dao.UnitRepository;
import com.pcitc.opal.pm.dao.WorkshopRepository;
import com.pcitc.opal.pm.pojo.Factory;
import com.pcitc.opal.pm.pojo.Unit;
import com.pcitc.opal.pm.pojo.UnitPerson;
import com.pcitc.opal.pm.pojo.Workshop;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.log4j.LogManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import pcitc.imp.common.ettool.utils.ObjectConverter;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/*
 * 装置维护业务逻辑层实现类
 * 模块编号：pcitc_opal_bll_class_UnitImpl
 * 作       者：xuelei.wang
 * 创建时间：2017-12-11
 * 修改编号：1
 * 描       述：装置维护业务逻辑层实现类
 */
@Service
@Component
public class UnitImpl implements UnitService {
    @Autowired
    private UnitRepository repository;
    @Autowired
    private BasicDataService basicDataService;
    @Autowired
    private ShiftService shiftService;
    @Autowired
    private UnitPersonRepository unitPersonRepository;
    @Autowired
    private FactoryRepository factoryRepository;
    @Autowired
    private WorkshopRepository workshopRepository;

    //private static final Logger logger = LogManager.getLogger(UnitImpl.class);
    private static final Logger logger = LoggerFactory.getLogger(UnitImpl.class);


    /**
     * 新增
     *
     * @param unitEntity
     * @return
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    @Transactional
    @Override
    public CommonResult add(DBUnitEntity unitEntity) throws Exception {
        // 实体转换为持久层实体
        Unit unitPo = ObjectConverter.entityConverter(unitEntity, Unit.class);
        // 数据校验
        unitValidation(unitPo);
        // 赋值 创建人、创建名称、创建时间
        CommonProperty commonProperty = new CommonProperty();
        unitPo.setCompanyId(Integer.valueOf(commonProperty.getCompanyId()));
        // 赋值 创建人、创建名称、创建时间
        CommonUtil.returnValue(unitPo, CommonEnum.PageModelEnum.NewAdd.getIndex());
        CommonResult commonResult = repository.addUnit(unitPo);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false) {
            throw new Exception(commonResult.getMessage());
        }
        UnitPerson unitPerson = new UnitPerson();
        unitPerson.setUnitId(unitPo.getStdCode().toString());
        unitPerson.setOperNum(unitEntity.getOperatorNum());
        unitPerson.setInUse(CommonEnum.InUseEnum.Yes.getIndex());
        // 赋值 创建人、创建名称、创建时间
        CommonUtil.returnValue(unitPerson, CommonEnum.PageModelEnum.NewAdd.getIndex());
        commonResult = unitPersonRepository.saveUnitPerson(unitPerson);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false) {
            throw new Exception(commonResult.getMessage());
        }
        return commonResult;
    }

    /**
     * 合法性校验
     *
     * @param entity
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    private void unitValidation(Unit entity) throws Exception {
        CommonResult commonResult = new CommonResult();
        // 实体不能为空
        if (entity == null) {
            throw new BusiException("00", "没有装置数据！");
        }
        // 调用DAL与数据库相关的校验
        commonResult = repository.unitValidation(entity);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
    }

    /**
     * 删除
     *
     * @param unitIds
     * @return
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    @Transactional
    @Override
    public CommonResult delete(Long[] unitIds) throws Exception {
        // 判断ID集合是否可用
        if (unitIds == null || unitIds.length <= 0) {
            throw new Exception("没有需要删除的装置数据！");
        }
        List<Unit> unitList = repository.getUnit(unitIds);
        if (unitList == null || unitList.isEmpty())
            return new CommonResult();
        String[] uIds = unitList.stream().map(item -> item.getStdCode()).toArray(String[]::new);

        // 1.删除装置人员信息表
        CommonResult commonResult = unitPersonRepository.deleteByUnitId(uIds);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());

        // 2.删除装置表
         commonResult = repository.deleteUnit(unitList.stream().map(item -> item.getUnitId()).toArray(Long[]::new));
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    }

    /**
     * 更新
     *
     * @param unitEntity
     * @return
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    @Transactional
    @Override
    public CommonResult update(DBUnitEntity unitEntity) throws Exception {
        // 实体转换持久层实体
        Unit unitPo = ObjectConverter.entityConverter(unitEntity, Unit.class);
        // 校验
        unitValidation(unitPo);
        // 实体转换为持久层实体
        unitPo = repository.getSingleUnit(unitPo.getUnitId());
        CommonUtil.objectExchange(unitEntity, unitPo);
        //企业
        CommonProperty commonProperty = new CommonProperty();
        unitPo.setCompanyId(Integer.valueOf(commonProperty.getCompanyId()));
        // 赋值 修改人、修改名称、修改时间
        CommonUtil.returnValue(unitPo, CommonEnum.PageModelEnum.Edit.getIndex());
        logger.error("测试乱码： "+ unitPo.getName());
        logger.error("测试乱码： "+unitPo.getSname());
        // 调用DAL更新方法
        CommonResult commonResult = repository.updateUnit(unitPo);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());

        UnitPerson unitPerson = unitPersonRepository.getUnitPersonByUnitId(unitPo.getStdCode());
        unitPerson.setUnitId(unitPo.getStdCode());
        unitPerson.setOperNum(unitEntity.getOperatorNum());
        unitPerson.setInUse(CommonEnum.InUseEnum.Yes.getIndex());
        // 赋值 创建人、创建名称、创建时间
        CommonUtil.returnValue(unitPerson, CommonEnum.PageModelEnum.NewAdd.getIndex());
        commonResult = unitPersonRepository.updateUnitPerson(unitPerson);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false) {
            throw new Exception(commonResult.getMessage());
        }
        return commonResult;
    }

    /**
     * 获取单个实体
     *
     * @param unitId 装置ID
     * @return
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    @Override
    public DBUnitEntity getSingle(Long unitId) throws Exception {
        Unit unit = repository.getSingleUnit(unitId);
        DBUnitEntity unitEntity = ObjectConverter.entityConverter(unit, DBUnitEntity.class);

        //1.处理FactoryID
        unitEntity.setFactoryId(unit.getWorkshop().getFactoryId());
        //2.处理操作工人数
        unitEntity.setOperatorNum(basicDataService.getOperatePersonCountByUnitId(unitEntity.getStdCode()));
        return unitEntity;
    }

    /**
     * 获取装置分页信息
     *
     * @param factoryId   工厂ID
     * @param workshopId  车间ID
     * @param name        名称或简称
     * @param stdCode     编码
     * @param inUse       是否启用
     * @param page        分页信息
     * @return
     * @throws Exception
     * <AUTHOR> 2017-12-12
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
    public PaginationBean<DBUnitEntity> getUnitList(Long factoryId, Long workshopId, String name, String stdCode, Integer inUse, Pagination page) throws Exception {
        Long[] workshopIds = null;
        if (workshopId != null) {
            workshopIds = new Long[]{workshopId};
        }

        PaginationBean<Unit> pageBean = repository.getUnitList(factoryId, workshopIds, name, stdCode, inUse, page);
        PaginationBean<DBUnitEntity> returnList = new PaginationBean<>(page, pageBean.getTotal());
        //returnList.setPageList(ObjectConverter.listConverter(pageBean.getPageList(), DBUnitEntity.class));
        List<UnitPersonEntity> unitPersonList = new ArrayList();


        //3.处理操作工人数;
        unitPersonList = basicDataService.getUnitPersonListByUnitIds(pageBean.getPageList().stream().map(item -> item.getStdCode()).collect(Collectors.toList()));

        //4.处理轮班域简称;
        List<ShiftWorkTeamEntity> workTeamList=shiftService.getShiftAreaList(pageBean.getPageList().stream().map(u->u.getShiftAreaId().toString()).collect(Collectors.toList()));
        for (Unit unit : pageBean.getPageList()) {
            DBUnitEntity entity = new DBUnitEntity();
            CommonUtil.objectExchange(unit,entity);
            entity.setCrtDate(unit.getCrtDate());
            entity.setCrtUserId(unit.getCrtUserId());
            entity.setCrtUserName(unit.getCrtUserName());
            entity.setMntDate(unit.getMntDate());
            entity.setMntUserId(unit.getMntUserId());
            entity.setMntUserName(unit.getMntUserName());
            //4.1处理车间
            entity.setWorkshopName(unit.getWorkshop().getSname());
            //4.2处理工厂ID
            entity.setFactoryId(unit.getWorkshop().getFactoryId());
            entity.setFactoryName(unit.getWorkshop().getFactory().getSname());
            entity.setOperatorNum(unitPersonList.stream().filter(item -> item.getUnitId().equals(entity.getStdCode())).findFirst().orElse(new UnitPersonEntity()).getOperNum());
            //4.3处理班组简称
            entity.setShiftWorkName(workTeamList.stream().filter(item->item.getShiftAreaId().equals(entity.getShiftAreaId())).findFirst().orElse(new ShiftWorkTeamEntity()).getShiftAreaName());
            returnList.getPageList().add(entity);
        }
        return returnList;
    }

    /**
     * 获取工厂列表
     * @param isAll  是否显示全部选项
     * @return 工厂实体
     */
    @Override
    public List<DBFactoryEntity> getFactoryList(boolean isAll) throws Exception {
        List<Factory> factoryList = factoryRepository.getFactory(null);
        List<DBFactoryEntity> factoryEntityList = ObjectConverter.listConverter(factoryList, DBFactoryEntity.class);
        if (isAll && factoryList.size() > 1) {
            DBFactoryEntity factoryEntity = new DBFactoryEntity();
            factoryEntity.setStdCode("-1");
            factoryEntity.setSname("全部");
            factoryEntityList.add(0, factoryEntity);
        }
        return factoryEntityList;
    }

    /**
     * 根据工厂ID获取车间列表
     * @param  factoryId 工厂ID
     * @param isAll  是否显示全部
     * @return 车间列表
     * @throws Exception
     */
    @Override
    public List<DBWorkshopEntity> getWorkshopListByFactoryId(Long factoryId, boolean isAll) throws Exception{
        Pagination page= new Pagination();
        page.setPageNumber(0);
        page.setPageSize(Integer.MAX_VALUE);
        PaginationBean<Workshop> factoryList = workshopRepository.getWorkshop(factoryId,null,null,1,page);
        List<DBWorkshopEntity> workshopEntities = ObjectConverter.listConverter(factoryList.getPageList(), DBWorkshopEntity.class);
        if (isAll && workshopEntities.size() > 1) {
            DBWorkshopEntity workshopEntity = new DBWorkshopEntity();
            workshopEntity.setWorkshopId(-1L);
            workshopEntity.setStdCode("-1");
            workshopEntity.setSname("全部");
            workshopEntities.add(0, workshopEntity);
        }
        return workshopEntities;
    }

    @Override
    public Unit getUnitInfoByStdCode(String stdCode) {
        return repository.getUnitInfoByStdCode(stdCode);
    }
    //定时JOB专用勿动
    public CommonResult updateUnitInfoById(Unit unitEntity) throws Exception{
        // 实体转换持久层实体
        Unit unitPo = ObjectConverter.entityConverter(unitEntity, Unit.class);
        // 校验
        unitValidation(unitPo);
        // 实体转换为持久层实体
        unitPo = repository.getSingleUnit(unitPo.getUnitId());
        CommonUtil.objectExchange(unitEntity, unitPo);
        unitPo.setMntDate(new Date());
        unitPo.setMntUserId("admin");
        // 调用DAL更新方法
        CommonResult commonResult = repository.updateUnit(unitPo);
        // 如果失败，直接throw
        if (commonResult.getIsSuccess() == false)
            throw new Exception(commonResult.getMessage());
        return commonResult;
    };
}
