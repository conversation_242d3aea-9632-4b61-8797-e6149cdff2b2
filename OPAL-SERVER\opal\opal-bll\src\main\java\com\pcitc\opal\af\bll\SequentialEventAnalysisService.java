package com.pcitc.opal.af.bll;

import java.util.Date;
import java.util.Map;

import org.springframework.stereotype.Service;

/*
 * 时序事件分析业务逻辑层接口 
 * 模块编号：pcitc_opal_bll_interface_SequentialEventAnalysisService
 * 作       者：dageng.sun
 * 创建时间：2017/11/14
 * 修改编号：1
 * 描       述：时序事件分析业务逻辑层接口 
 */
@Service
public interface SequentialEventAnalysisService {
	
	/**
	 * 查询时序事件分析图形业务逻辑层
	 * 
	 * <AUTHOR> 2017-11-15
	 * @param unitCodes 装置编码数组
	 * @param prdtCellIds 生产单元id数组
	 * @param beginTime 报警事件的开始间
	 * @param endTime 报警事件的结束时间
	 * @throws Exception 
	 * @return Map<String, Object> 返回Map<String, Object>对象
	 */
	Map<String, Object> getSequentialEventGraph(String[] unitCodes, Long[] prdtCellIds, Date beginTime, Date endTime) throws Exception;
	
}
