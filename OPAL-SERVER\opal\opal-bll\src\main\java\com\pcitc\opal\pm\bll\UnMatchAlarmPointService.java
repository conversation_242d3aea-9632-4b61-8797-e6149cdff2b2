package com.pcitc.opal.pm.bll;

import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.dao.imp.UnMatchAlarmPointEntityVO;

/**
 * @USER: chenbo
 * @DATE: 2023/4/24
 * @TIME: 9:21
 * @DESC:
 **/
public interface UnMatchAlarmPointService {

    /**
     * 获取分页数据
     * <p>
     *  * <AUTHOR> 2017-10-11
     *
     * @param unitIds
     * @param page    翻页实现类
     * @return PaginationBean<AlarmPointEntity> 分页对象
     * @throws Exception 
     */
    PaginationBean<UnMatchAlarmPointEntityVO> getUnMatchAlarmPoint(String tag, Long prdtCellId, Long dcsCode, String[] unitIds, Pagination page) throws Exception;
}
