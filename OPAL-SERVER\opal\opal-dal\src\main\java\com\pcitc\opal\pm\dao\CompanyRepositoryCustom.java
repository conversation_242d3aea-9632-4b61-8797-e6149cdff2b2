package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.Company;

import java.util.List;

/*
 * Company实体的Repository的JPA自定义接口
 * 模块编号：pcitc_opal_dal_interface_CompanyRepositoryCustom
 * 作       者：shufei.sui
 * 创建时间：2021/02/22
 * 修改编号：1
 * 描       述：Company实体的Repository的JPA自定义接口
 */
public interface CompanyRepositoryCustom {

    /**
     * 获取企业分页数据
     * @param name
     * @param stdCode
     * @param inUse
     * @param page
     * @return
     */
    PaginationBean<Company> getCompany(String name, String stdCode, Integer inUse, Pagination page);

    /**
     * 数据校验
     * @param company
     * @return
     */
    CommonResult companyValidation(Company company);

    CommonResult addCompany(Company company);

    List<Company> getCompany(Long[] companyIds);

    CommonResult deleteCompany(Long[] anlyCompanyIdList);

    Company getSingleCompany(Long companyId);

    CommonResult updateCompany(Company company);
    Integer getCompanyIdByStdCode(String strCode);
}
