package com.pcitc.opal.pm.dao;

import java.util.List;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.pojo.MeasUnit;

/*
 * MeasUnit实体的Repository的JPA自定义接口 
 * 模块编号：pcitc_opal_dal_interface_MeasUnitRepositoryCustom
 * 作       者：jiangtao.xue
 * 创建时间：2017/09/25
 * 修改编号：1
 * 描       述：MeasUnit实体的Repository的JPA自定义接口 
 */
public interface MeasUnitRepositoryCustom {

	/**
	 * 校验数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param measUnitEntity
	 *            计量单位实体
	 * @return 返回结果信息类
	 */
	CommonResult measUnitValidation(MeasUnit measUnitEntity);

	/**
	 * 新增数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param measUnitEntity
	 *            计量单位实体
	 * @return 返回结果信息类
	 */
	CommonResult addMeasUnit(MeasUnit measUnitEntity);

	/**
	 * 删除数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param measUnitIds
	 *            计量单位ID集合
	 * @return 返回结果信息类
	 */
	CommonResult deleteMeasUnit(Long[] measUnitIds);

	/**
	 * 更新数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param measUnitEntity
	 * @return 返回结果信息类
	 */
	CommonResult updateMeasUnit(MeasUnit measUnitEntity);

	/**
	 * 获取单条数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param measUnitId
	 *            计量单位ID
	 * @return 计量单位实体
	 */
	MeasUnit getSingleMeasUnit(Long measUnitId);

	/**
	 * 获取多条数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param measUnitIds
	 *            计量单位ID集合
	 * @return 计量单位实体集合
	 */
	List<MeasUnit> getMeasUnit(Long[] measUnitIds);

	/**
	 * 获取分页数据
	 * 
	 * <AUTHOR> 2017-09-25
	 * @param name
	 *            计量单位名称
	 * @param sign
	 * 			符号
	 * @param inUse
	 *            是否启动
	 * @param page
	 *            分页参数
	 * @return 计量单位实体集合
	 */
	PaginationBean<MeasUnit> getMeasUnit(String name,String sign, Integer inUse, Pagination page);
	
}
