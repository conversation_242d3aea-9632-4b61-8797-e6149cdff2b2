<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="opal-alarmrec-executor" />
        <module name="opal-common" />
        <module name="opal-pojo" />
        <module name="opal-dal" />
        <module name="opal-bll" />
        <module name="opal-sendmsg-executor" />
        <module name="OPAL-WEB" />
        <module name="opal-workflow" />
        <module name="opal-syndata-executor" />
        <module name="opal-webservice" />
        <module name="opal-service" />
        <module name="opal-collects-executor" />
        <module name="opal-job" />
        <module name="opal-alarmpush-executor" />
        <module name="opal-dao" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="opal-alarmpush-executor" options="-parameters" />
      <module name="opal-alarmrec-executor" options="-parameters" />
      <module name="opal-bll" options="-parameters" />
      <module name="opal-collects-executor" options="-parameters" />
      <module name="opal-common" options="-parameters" />
      <module name="opal-dal" options="-parameters" />
      <module name="opal-dao" options="-parameters" />
      <module name="opal-job" options="-parameters" />
      <module name="opal-pojo" options="-parameters" />
      <module name="opal-sendmsg-executor" options="-parameters" />
      <module name="opal-service" options="-parameters" />
      <module name="opal-syndata-executor" options="-parameters" />
      <module name="opal-webservice" options="-parameters" />
      <module name="opal-workflow" options="-parameters" />
    </option>
  </component>
</project>