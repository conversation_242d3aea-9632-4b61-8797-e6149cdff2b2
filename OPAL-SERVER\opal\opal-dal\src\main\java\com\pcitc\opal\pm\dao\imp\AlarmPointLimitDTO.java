package com.pcitc.opal.pm.dao.imp;


import com.pcitc.opal.pm.pojo.AlarmPointType;
import com.pcitc.opal.pm.pojo.MeasUnit;
import com.pcitc.opal.pm.pojo.PrdtCell;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/*
 * 报警点分组实体
 * 模块编号：pcitc_opal_bll_class_AlarmPointGroupModel
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点分组实体
 */
@Data
public class AlarmPointLimitDTO {

	/**
	 * 装置编码
	 */
	private String unitCode;
	/**
	 * 报警点ID
	 */
	private Long alarmPointId;
	/**
	 * 生产单元ID
	 */
	private Long prdtCellId;
	/**
	 * 位号
	 */
	private String tag;
	/**
	 * 工艺卡片上限值是否包含(1是；0否)
	 */
	private Integer craftUpLimitInclude;

	/**
	 * 工艺卡片下限值是否包含(1是；0否)
	 */
	private Integer craftDownLimitInclude;

	/**
	 * 工艺卡片上限值
	 */
	private Double craftUpLimitValue;

	/**
	 * 工艺卡片下限值
	 */
	private Double craftDownLimitValue;

	/**
	 * 联锁上限值是否包含(1是；0否)
	 */
	private Integer interlockUpLimitInclude;

	/**
	 * 联锁下限值是否包含(1是；0否)
	 */
	private Integer interlockDownLimitInclude;

	/**
	 * 联锁上限值
	 */
	private Double interlockUpLimitValue;

	/**
	 * 联锁下限值
	 */
	private Double interlockDownLimitValue;

	/**
	 * 级别(1A；2B)
	 */
	private Integer craftRank;
	/**
	 * 位置
	 */
	private String location;
	/**
	 * PID图号
	 */
	private String pidCode;
	/**
	 * 报警点类型ID
	 */
	private Long  alarmPointTypeId;
	/**
	 * 监测类型（1物料；2能源；3质量）
	 */
	private Integer monitorType;
	/**
	 * 计量单位ID
	 */
	private Long measunitId;
	/**
	 * 仪表类型（1监测表；2控制表）
	 */
	private Integer instrmtType;

	/**
	 * 仪表优先级(1紧急；2重要；3一般)
	 */
	private Integer instrmtPriority;

	/**
	 *  是否虚表（0实表(按读数)；1虚表(按用量)）
	 */
	private Integer virtualRealityFlag;
	/**
	 *  虚拟标识（0否；1是）
	 */
	private Integer virtualFlag;
	/**
	 * 报警点高高报
	 */
	private Double alarmPointHH;
	/**
	 * 报警点高报
	 */
	private Double alarmPointHI;
	/**
	 * 报警点低报
	 */
	private Double alarmPointLO;
	/**
	 * 报警点低低报
	 */
	private Double alarmPointLL;

	/**
	 * 是否启用（1是；0否）
	 */
	private Integer inUse;

	/**
	 * 是否发送报警短信（1是；0否）
	 */
	private Integer inSendMsg;

	/**
	 * 手机号（多个手机号用英文逗号隔开）
	 */
	private String mobilePhone;

	/**
	 * 排序
	 */
	private Integer sortNum;

	/**
	 * 描述
	 */
	private String des;

	/**
	 * 实时数据库位号
	 */
	private String rtdbTag;

	/**
	 * 企业ID
	 */
	private Integer companyId;

	/**
	 * 创建时间
	 */
	private Date crtDate;

	/**
	 * 创建人ID
	 */
	private String crtUserId;

	/**
	 * 创建人名称
	 */
	private String crtUserName;

	/**
	 * 修改时间
	 */
	private Date mntDate;

	/**
	 * 修改人ID
	 */
	private String mntUserId;

	/**
	 * 修改人
	 */
	private String mntUserName;


	public AlarmPointLimitDTO(String unitCode, Long alarmPointId, Long prdtCellId, String tag, Integer craftUpLimitInclude, Integer craftDownLimitInclude, Double craftUpLimitValue, Double craftDownLimitValue, Integer interlockUpLimitInclude, Integer interlockDownLimitInclude, Double interlockUpLimitValue, Double interlockDownLimitValue, Integer craftRank, String location, String pidCode, Long alarmPointTypeId, Integer monitorType, Long measunitId, Integer instrmtType, Integer instrmtPriority, Integer virtualRealityFlag, Integer virtualFlag, Double alarmPointHH, Double alarmPointHI, Double alarmPointLO, Double alarmPointLL, Integer inUse, Integer inSendMsg, String mobilePhone, Integer sortNum, String des, String rtdbTag, Integer companyId, Date crtDate, String crtUserId, String crtUserName, Date mntDate, String mntUserId, String mntUserName) {
		this.unitCode = unitCode;
		this.alarmPointId = alarmPointId;
		this.prdtCellId = prdtCellId;
		this.tag = tag;
		this.craftUpLimitInclude = craftUpLimitInclude;
		this.craftDownLimitInclude = craftDownLimitInclude;
		this.craftUpLimitValue = craftUpLimitValue;
		this.craftDownLimitValue = craftDownLimitValue;
		this.interlockUpLimitInclude = interlockUpLimitInclude;
		this.interlockDownLimitInclude = interlockDownLimitInclude;
		this.interlockUpLimitValue = interlockUpLimitValue;
		this.interlockDownLimitValue = interlockDownLimitValue;
		this.craftRank = craftRank;
		this.location = location;
		this.pidCode = pidCode;
		this.alarmPointTypeId = alarmPointTypeId;
		this.monitorType = monitorType;
		this.measunitId = measunitId;
		this.instrmtType = instrmtType;
		this.instrmtPriority = instrmtPriority;
		this.virtualRealityFlag = virtualRealityFlag;
		this.virtualFlag = virtualFlag;
		this.alarmPointHH = alarmPointHH;
		this.alarmPointHI = alarmPointHI;
		this.alarmPointLO = alarmPointLO;
		this.alarmPointLL = alarmPointLL;
		this.inUse = inUse;
		this.inSendMsg = inSendMsg;
		this.mobilePhone = mobilePhone;
		this.sortNum = sortNum;
		this.des = des;
		this.rtdbTag = rtdbTag;
		this.companyId = companyId;
		this.crtDate = crtDate;
		this.crtUserId = crtUserId;
		this.crtUserName = crtUserName;
		this.mntDate = mntDate;
		this.mntUserId = mntUserId;
		this.mntUserName = mntUserName;
	}
}
