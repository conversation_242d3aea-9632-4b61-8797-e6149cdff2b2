package com.pcitc.opal.aa.bll.entity;

/*
 * 网格列数据实体
 * 模块编号：pcitc_opal_bll_class_GridViewData
 * 作    者：jiangtao.xue
 * 创建时间：2017/10/17
 * 修改编号：1
 * 描    述：网格列数据实体
 */
public class GridViewEntity {
    /**
     * 装置编码
     */
    private String unitId;
    /**
     * 装置名称
     */
    private String unitName;
    /**
     * 报警点id
     */
    private long alarmPointId;
    /**
     * 报警总数
     */
    private long sumTimes;
    /**
     * 平均报警率
     */
    private Object avgAlarmRate;

    /**
     * 平均报警率对比值
     */
    private int avgAlarmContrast;
    /**
     * 峰值报警率
     */
    private Object peakAlarmRate;
    /**
     * 峰值报警率对比值
     */
    private int peakAlarmContrast;
    /**
     * 扰动率
     */
    private Object excitationRate;
    /**
     * 扰动率对比值
     */
    private int excitationContrast;
    /**
     * 评估等级
     */
    private Long assessLevel;
    /**
     * 评估等级名称
     */
    private String assessLevelName;

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    public long getSumTimes() {
        return sumTimes;
    }

    public void setSumTimes(long sumTimes) {
        this.sumTimes = sumTimes;
    }

    public Object getAvgAlarmRate() {
        return avgAlarmRate;
    }

    public void setAvgAlarmRate(Object avgAlarmRate) {
        this.avgAlarmRate = avgAlarmRate;
    }

    public int getAvgAlarmContrast() {
        return avgAlarmContrast;
    }

    public void setAvgAlarmContrast(int avgAlarmContrast) {
        this.avgAlarmContrast = avgAlarmContrast;
    }

    public Object getPeakAlarmRate() {
        return peakAlarmRate;
    }

    public void setPeakAlarmRate(Object peakAlarmRate) {
        this.peakAlarmRate = peakAlarmRate;
    }

    public int getPeakAlarmContrast() {
        return peakAlarmContrast;
    }

    public void setPeakAlarmContrast(int peakAlarmContrast) {
        this.peakAlarmContrast = peakAlarmContrast;
    }

    public Object getExcitationRate() {
        return excitationRate;
    }

    public void setExcitationRate(Object excitationRate) {
        this.excitationRate = excitationRate;
    }

    public int getExcitationContrast() {
        return excitationContrast;
    }

    public void setExcitationContrast(int excitationContrast) {
        this.excitationContrast = excitationContrast;
    }

    public Long getAssessLevel() {
        return assessLevel;
    }

    public void setAssessLevel(Long assessLevel) {
        this.assessLevel = assessLevel;
    }

    public String getAssessLevelName() {
        return assessLevelName;
    }

    public void setAssessLevelName(String assessLevelName) {
        this.assessLevelName = assessLevelName;
    }
}
