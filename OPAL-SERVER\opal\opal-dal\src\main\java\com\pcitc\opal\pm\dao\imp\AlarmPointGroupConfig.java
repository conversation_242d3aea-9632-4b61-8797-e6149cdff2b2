package com.pcitc.opal.pm.dao.imp;

import pcitc.imp.common.ettool.Annotaion.QueryContract;
import pcitc.imp.common.ettool.Annotaion.ResourceContract;

import java.io.Serializable;
import java.util.Date;


/*
 * 报警点实体
 * 模块编号：pcitc_opal_bll_class_AlarmPointModel
 * 作       者：dageng.sun
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：报警点实体
 */
public class AlarmPointGroupConfig  {

	public AlarmPointGroupConfig() {
	}

	public AlarmPointGroupConfig(Long alarmPointGroupId, String unitName, String groupName, String alarmFlagName, String des, String unitCode, String alarmFlagId) {
		this.alarmPointGroupId = alarmPointGroupId;
		this.unitName = unitName;
		this.groupName = groupName;
		this.alarmFlagName = alarmFlagName;
		this.des = des;
		this.unitCode = unitCode;
		this.alarmFlagId = alarmFlagId;
	}

	private Long alarmPointGroupId;
	private String unitName;
	private String groupName;
	private String alarmFlagName;
	private String des;
	/**
	 * 装置编码
	 */
	private String unitCode;
	/**
	 * 报警标识ID
	 */
	private String alarmFlagId;

	public String getUnitCode() {
		return unitCode;
	}

	public void setUnitCode(String unitCode) {
		this.unitCode = unitCode;
	}

	public String getAlarmFlagId() {
		return alarmFlagId;
	}

	public void setAlarmFlagId(String alarmFlagId) {
		this.alarmFlagId = alarmFlagId;
	}

	public Long getAlarmPointGroupId() {
		return alarmPointGroupId;
	}

	public void setAlarmPointGroupId(Long alarmPointGroupId) {
		this.alarmPointGroupId = alarmPointGroupId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}

	public String getAlarmFlagName() {
		return alarmFlagName;
	}

	public void setAlarmFlagName(String name) {
		this.alarmFlagName = name;
	}
}
