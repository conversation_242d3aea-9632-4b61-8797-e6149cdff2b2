<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.mapper.UnMatchAlarmPointMapper">

    <resultMap id="BaseResultMap" type="com.pcitc.opal.pojo.UnMatchAlarmPoint">
            <id property="unMatchAlarmPointId" column="Unmatch_Alarm_Point_Id" jdbcType="INTEGER"/>
            <result property="companyId" column="Company_Id" jdbcType="BIGINT"/>
            <result property="dcsCode" column="DCS_Code" jdbcType="BIGINT"/>
            <result property="prdtcellId" column="PrdtCell_Id" jdbcType="BIGINT"/>
            <result property="opcCodeId" column="opc_code_id" jdbcType="BIGINT"/>
            <result property="tag" column="Tag" jdbcType="VARCHAR"/>
            <result property="writeDate" column="Write_Date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Unmatch_Alarm_Point_Id,Company_Id,DCS_Code,
        PrdtCell_Id,Tag,Write_Date,opc_code_id
    </sql>
    <select id="selectByMoreThanUnMatchIdToEnterpriseDB" resultType="com.pcitc.opal.pojo.UnMatchAlarmPoint">
        select <include refid="Base_Column_List"/>
            from t_pm_unmatchalarmpoint where Unmatch_Alarm_Point_Id > #{unMatchAlarmPointId,jdbcType=NUMERIC} order by Unmatch_Alarm_Point_Id limit #{batchSize,jdbcType=NUMERIC}
    </select>
</mapper>
