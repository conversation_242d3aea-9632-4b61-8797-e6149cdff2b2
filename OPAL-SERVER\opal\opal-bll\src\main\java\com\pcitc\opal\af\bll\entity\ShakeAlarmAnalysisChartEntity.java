package com.pcitc.opal.af.bll.entity;

import com.pcitc.opal.common.bll.entity.DictionaryEntity;

import java.util.List;
import java.util.Map;

/*
 * 震荡报警分析图表数据实体
 * 模块编号：pcitc_opal_bll_class_ShakeAlarmAnalysisChartEntity
 * 作	者：jiangtao.xue
 * 创建时间：2018-04-23
 * 修改编号：1
 * 描	述：震荡报警分析图表数据实体
 */
public class ShakeAlarmAnalysisChartEntity {
	/**
	 * 报警标识
	 */
	private Long alarmFlag;

	/**
	 * 报警次数
	 */
	private Long alarmTimes;

	/**
	 * 报警值
	 */
	private Double alarmValue;

	/**
	 * 计量单位
	 */
	private String unitFlag;

	/**
	 * 现报警数
	 */
	private Long nowAlarmTimes;

	/**
	 * 减少比例
	 */
	private Double reduceRate;

	/**
	 * 最大值
	 */
	private Double maxValue;

	/**
	 * 最小值
	 */
	private Double minValue;

	/**
	 * 限值
	 */
	private Object limitValue;

	/**
	 * 工艺卡片上限值
	 */
	private DictionaryEntity upLimitValue;

	/**
	 * 工艺卡片下限值
	 */
	private DictionaryEntity downLimitValue;

	/**
	 * 柱状图数据
	 */
	private List<DictionaryEntity> histogramData;

	/**
	 * 折线图数据
	 */
	private List<DictionaryEntity> lineChartData;

	public Long getAlarmTimes() {
		return alarmTimes;
	}

	public void setAlarmTimes(Long alarmTimes) {
		this.alarmTimes = alarmTimes;
	}

	public Double getAlarmValue() {
		return alarmValue;
	}

	public void setAlarmValue(Double alarmValue) {
		this.alarmValue = alarmValue;
	}

	public Long getNowAlarmTimes() {
		return nowAlarmTimes;
	}

	public void setNowAlarmTimes(Long nowAlarmTimes) {
		this.nowAlarmTimes = nowAlarmTimes;
	}

	public Double getReduceRate() {
		return reduceRate;
	}

	public void setReduceRate(Double reduceRate) {
		this.reduceRate = reduceRate;
	}

	public Double getMaxValue() {
		return maxValue;
	}

	public void setMaxValue(Double maxValue) {
		this.maxValue = maxValue;
	}

	public Double getMinValue() {
		return minValue;
	}

	public void setMinValue(Double minValue) {
		this.minValue = minValue;
	}

	public Object getLimitValue() {
		return limitValue;
	}

	public void setLimitValue(Object limitValue) {
		this.limitValue = limitValue;
	}

	public DictionaryEntity getUpLimitValue() {
		return upLimitValue;
	}

	public void setUpLimitValue(DictionaryEntity upLimitValue) {
		this.upLimitValue = upLimitValue;
	}

	public DictionaryEntity getDownLimitValue() {
		return downLimitValue;
	}

	public void setDownLimitValue(DictionaryEntity downLimitValue) {
		this.downLimitValue = downLimitValue;
	}

	public List<DictionaryEntity> getHistogramData() {
		return histogramData;
	}

	public void setHistogramData(List<DictionaryEntity> histogramData) {
		this.histogramData = histogramData;
	}

	public String getUnitFlag() {
		return unitFlag;
	}

	public void setUnitFlag(String unitFlag) {
		this.unitFlag = unitFlag;
	}

	public Long getAlarmFlag() {
		return alarmFlag;
	}

	public void setAlarmFlag(Long alarmFlag) {
		this.alarmFlag = alarmFlag;
	}

	public List<DictionaryEntity> getLineChartData() {
		return lineChartData;
	}

	public void setLineChartData(List<DictionaryEntity> lineChartData) {
		this.lineChartData = lineChartData;
	}
}
