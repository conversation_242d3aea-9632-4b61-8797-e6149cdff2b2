package com.pcitc.opal.af.bll;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pcitc.opal.aa.bll.entity.AlarmNumberAssessDataEntity;
import com.pcitc.opal.ad.vo.FloodAlarmPointCountVO;
import com.pcitc.opal.af.bll.entity.FloodAlarmChartOptionEntity;
import com.pcitc.opal.common.CommonEnum;
import com.pcitc.opal.common.Pagination;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/*
 * 高频报警分析业务逻辑层接口
 * 模块编号：pcitc_opal_bll_interface_FloodAlarmAnalysisService
 * 作  　者：xuelei.wang
 * 创建时间：2017-11-15
 * 修改编号：1
 * 描    述：高频报警分析业务逻辑层接口
 */
@Service
public interface FloodAlarmAnalysisService {
    /**
     * 根据装置或者生产单元和时间区间获取高频报警清单列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param unitCodes 装置编码集合
     * @param prdtIds   生产单元集合
     * @param dateType  日期类型(day|month|week|hour)
     * @return 高频报警清单
     * <AUTHOR> 2017-11-15
     */
    FloodAlarmChartOptionEntity getFloodAlarmList(Date startTime, Date endTime, String[] unitCodes, Long[] prdtIds, String dateType) throws Exception;

    Page<FloodAlarmPointCountVO> getAlarmStatistics(List<String> unitCodes, Date startTime, Date endTime, List<Long> prdtIds, Pagination page) throws Exception;


    List<FloodAlarmPointCountVO> getAlarmStatistics(List<String> unitCodes, Date startTime, Date endTime, List<Long> prdtIds) throws Exception;


}
