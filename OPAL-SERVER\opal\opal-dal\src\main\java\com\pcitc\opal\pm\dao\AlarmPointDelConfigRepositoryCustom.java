package com.pcitc.opal.pm.dao;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.pm.dao.imp.AlarmPointDelConfigDTO;
import com.pcitc.opal.pm.pojo.AlarmPointDelConfig;

import java.util.Date;
import java.util.List;

public interface AlarmPointDelConfigRepositoryCustom {
    /**
     * 查询报警剔除配置详细信息
     * @param unitCodes
     * @param startTime
     * @param endTime
     */
    List<AlarmPointDelConfig> selectAlarmPointDelConfigDetail(String[] unitCodes, Date startTime, Date endTime);

    /**
     * 根据剔除配置id查询配置
     *
     * @param id        配置id
     * @param companyId 企业id
     * @return 配置类实体
     */
    List<AlarmPointDelConfig> selectAlarmPointDelConfigDetailById(Integer[] id, Integer companyId);



    /**
     * 查询报警分组剔除配置
     *
     * @param unitCodes
     * @param startTime
     * @param endTime
     * @param delStatus
     */
    PaginationBean<AlarmPointDelConfigDTO> getAlarmPointDelConfigPage(String[] unitCodes, String groupName, Date startTime, Date endTime, Integer inUse, Pagination page, Integer delStatus);

    /**
     * 根据ID查询单一分组剔除配置
     * @param alarmPointDelConfigId
     */
    AlarmPointDelConfigDTO getSingleAlarmPointDel(Long alarmPointDelConfigId);

    /**
     * 新增数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPointDelConfig 报警点剔除实体
     * @return CommonResult 消息结果类
     */
    CommonResult addAlarmPointDel(AlarmPointDelConfig alarmPointDelConfig);

    /**
     * 删除数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPointDelConfigId 报警点ID集合
     * @return Integer 受影响的行数
     */
    Integer deleteAlarmPointDel(List<Long> alarmPointDelConfigId);

    /**
     * 更新数据
     *
      * <AUTHOR> 2017-10-11
     * @param alarmPointDelConfig 报警点剔除实体
     * @return CommonResult 消息结果类
     */
    CommonResult updateAlarmPointDel(AlarmPointDelConfig alarmPointDelConfig);


    /**
     * 将剔除状态修改已通过
     * @param alarmPointDelConfigId 报警剔除配置ID
     * @param delStatus 要修改的剔除状态
     */
    Integer updateDelStatusByAlarmPointDelConfig(Integer[] alarmPointDelConfigId, Integer delStatus);


    /**
     * 修改剔除数据的状态
     * @param alarmPointDelConfigId id
     * @param delDataStatus 要修改的状态
     * @return 修改的行数
     */
    Integer updateDelDataStatusById(Integer[] alarmPointDelConfigId, Integer delDataStatus);

    /**
     * 查询剔除失败的数据
     * @return 返回剔除失败的id
     */
    List<AlarmPointDelConfig> selectDelDataFailure();
}
