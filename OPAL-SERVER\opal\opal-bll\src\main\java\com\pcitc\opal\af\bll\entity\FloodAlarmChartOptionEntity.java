package com.pcitc.opal.af.bll.entity;
import com.pcitc.opal.ad.bll.entity.AlarmEventViewEntity;
import com.pcitc.opal.common.bll.entity.DictionaryEntity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/*
 * 高频报警分析图表展示实体
 * 模块编号：pcitc_opal_bll_class_FloodAlarmChartOptionEntity
 * 作  　者：xuelei.wang
 * 创建时间：2017-11-16
 * 修改编号：1
 * 描    述：高频报警分析图表展示实体
 */
@SuppressWarnings("serial")
public class FloodAlarmChartOptionEntity  implements Serializable{
    /**
     * 图例
     */
    private List<String> legend=new ArrayList<>();
    /**
     * xAxis数据
     */
    private List<DictionaryEntity> xaxis=new ArrayList<>();
    /**
     * 高频报警总次数
     */
    private Long counts;
    /**
     * 高频报警原始数据;
     */
    private List<AlarmEventViewEntity> originalList;
    /**
     * series
     */
    private List<FloodAlarmChartSeriesItemEntity> list=new ArrayList<>();
    /**
     * 报警分组数据
     */
    private List<FloodAlarmChartEntity> floodData=new ArrayList<>();
    /**
     * 列表主表数据
     */
    private List<FloodMainTableEntity> mainTableEntityList=new ArrayList<>();

    public List<FloodAlarmChartSeriesItemEntity> getList() {
        return list;
    }

    public void setList(List<FloodAlarmChartSeriesItemEntity> list) {
        this.list = list;
    }

    public List<String> getLegend() {
        return legend;
    }

    public void setLegend(List<String> legend) {
        this.legend = legend;
    }

    public Long getCounts() {
        return counts;
    }

    public void setCounts(Long counts) {
        this.counts = counts;
    }

    public List<AlarmEventViewEntity> getOriginalList() {
        return originalList;
    }

    public void setOriginalList(List<AlarmEventViewEntity> originalList) {
        this.originalList = originalList;
    }

    public void setFloodData(List<FloodAlarmChartEntity> floodData) {
        this.floodData = floodData;
    }

    public List<FloodAlarmChartEntity> getFloodData() {
        return floodData;
    }

    public List<FloodMainTableEntity> getMainTableEntityList() {
        return mainTableEntityList;
    }

    public void setMainTableEntityList(List<FloodMainTableEntity> mainTableEntityList) {
        this.mainTableEntityList = mainTableEntityList;
    }

    public List<DictionaryEntity> getXaxis() {
        return xaxis;
    }

    public void setXaxis(List<DictionaryEntity> xaxis) {
        this.xaxis = xaxis;
    }
}
