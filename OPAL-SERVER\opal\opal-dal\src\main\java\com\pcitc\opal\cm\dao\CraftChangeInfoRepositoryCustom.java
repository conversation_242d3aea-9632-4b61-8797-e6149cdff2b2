package com.pcitc.opal.cm.dao;

import com.pcitc.opal.ac.pojo.AlarmChangePlanDetail;
import com.pcitc.opal.cm.pojo.CraftChangeInfo;
import com.pcitc.opal.common.CommonResult;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

/*
 * AlarmEventTypeComp实体的Repository的JPA自定义接口
 * 模块编号：pcitc_opal_dal_interface_AlarmEventTypeCompRepositoryCustom
 * 作       者：xuelei.wang
 * 创建时间：2018-03-30
 * 修改编号：1
 * 描       述：AlarmEventTypeComp实体的Repository的JPA自定义接口
 */
public interface CraftChangeInfoRepositoryCustom {

    /**
     * 新增工艺变更单信息
     *
     * @param craftChangeInfo 工艺变更单信息
     * @return 返回结果信息类
     * <AUTHOR> 2018-01-22
     */
    CommonResult addCraftChangeInfo(CraftChangeInfo craftChangeInfo);

    /**
     * 根据企业ID查询工艺变更单最大的“发布时间”
     *
     * @param comId 企业ID
     * @return
     * <AUTHOR> 2018-01-22
     */
    Date getCraftChangeInfoMaxRlsTime(Long comId);

    /**
     * 根据企业ID查询工艺变更单最大的“发布时间”
     *
     * @param  unitCode 装置编码（工艺）
     * @param  tag 位号（工艺）
     * @param  alarmFlagName 报警标识NAME
     * @param  startTime 发生时间
     * @param  nowValue 值
     * @return
     * <AUTHOR> 2018-01-22
     */
    CraftChangeInfo getCraftChangeInfoInfo(String unitCode, String tag, String alarmFlagName, Date startTime, String nowValue,Integer val);
}
