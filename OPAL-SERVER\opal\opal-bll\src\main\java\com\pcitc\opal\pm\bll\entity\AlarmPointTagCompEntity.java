
package com.pcitc.opal.pm.bll.entity;

import com.pcitc.opal.common.bll.entity.BasicEntity;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.Date;


/**
 * <p>
 * 报警点位号对照
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-31
 */
public class AlarmPointTagCompEntity extends BasicEntity {


    /**
     * 报警点位号对照ID
     */
    private Long alarmPointTagCompId;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 报警点ID
     */
    private Long alarmPointId;

    /**
     * 位号
     */
    private String tag;

    /**
     * 系统类型(1工艺系统)
     */
    private Long sysType;

    /**
     * 装置编码（工艺）
     */
    private String unitcode;

    /**
     * 装置简称（工艺）
     */
    private String unitsname;

    /**
     * 位号（工艺）
     */
    private String crafttag;

    /**
     * 创建时间
     */
    private Date crtDate;

    /**
     * 维护时间
     */
    private Date mntDate;

    /**
     * 创建人ID
     */
    private String crtUserId;

    /**
     * 最后维护人ID
     */
    private String mntUserId;

    /**
     * 创建人名称
     */
    private String crtUserName;

    /**
     * 最后维护人名称
     */
    private String mntUserName;

    public Long getAlarmPointTagCompId() {
        return alarmPointTagCompId;
    }

    public void setAlarmPointTagCompId(Long alarmPointTagCompId) {
        this.alarmPointTagCompId = alarmPointTagCompId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getAlarmPointId() {
        return alarmPointId;
    }

    public void setAlarmPointId(Long alarmPointId) {
        this.alarmPointId = alarmPointId;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Long getSysType() {
        return sysType;
    }

    public void setSysType(Long sysType) {
        this.sysType = sysType;
    }

    public String getUnitcode() {
        return unitcode;
    }

    public void setUnitcode(String unitcode) {
        this.unitcode = unitcode;
    }

    public String getUnitsname() {
        return unitsname;
    }

    public void setUnitsname(String unitsname) {
        this.unitsname = unitsname;
    }

    public String getCrafttag() {
        return crafttag;
    }

    public void setCrafttag(String crafttag) {
        this.crafttag = crafttag;
    }

    @Override
    public Date getCrtDate() {
        return crtDate;
    }

    public void setCrtDate(Date crtDate) {
        this.crtDate = crtDate;
    }

    @Override
    public Date getMntDate() {
        return mntDate;
    }

    public void setMntDate(Date mntDate) {
        this.mntDate = mntDate;
    }

    @Override
    public String getCrtUserId() {
        return crtUserId;
    }

    @Override
    public void setCrtUserId(String crtUserId) {
        this.crtUserId = crtUserId;
    }

    @Override
    public String getMntUserId() {
        return mntUserId;
    }

    @Override
    public void setMntUserId(String mntUserId) {
        this.mntUserId = mntUserId;
    }

    @Override
    public String getCrtUserName() {
        return crtUserName;
    }

    @Override
    public void setCrtUserName(String crtUserName) {
        this.crtUserName = crtUserName;
    }

    @Override
    public String getMntUserName() {
        return mntUserName;
    }

    @Override
    public void setMntUserName(String mntUserName) {
        this.mntUserName = mntUserName;
    }
}
