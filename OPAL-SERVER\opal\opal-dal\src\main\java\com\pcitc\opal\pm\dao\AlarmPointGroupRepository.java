package com.pcitc.opal.pm.dao;

import com.pcitc.opal.pm.pojo.AlarmPoint;
import com.pcitc.opal.pm.pojo.AlarmPointGroup;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * AlarmPointGroup实体的Repository的JPA标准接口
 * 模块编号：pcitc_opal_dal_interface_AlarmPointGroupRepository
 * 作       者：guoganxin
 * 创建时间：2017/10/10
 * 修改编号：1
 * 描       述：AlarmPointGroup实体的Repository实现
 */
public interface AlarmPointGroupRepository extends JpaRepository<AlarmPointGroup, Long>, AlarmPointGroupRepositoryCustom {

}
