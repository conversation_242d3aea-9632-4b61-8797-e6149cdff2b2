package com.pcitc.opal.common;

import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/*
 * common.properties公共属性配置读取
 * 模块编号：pcitc_opal_common_class_CommonPropertiesReader
 * 作  　者：xuelei.wang
 * 创建时间：2017-10-17
 * 修改编号：1
 * 描    述：common.properties公共属性配置读取
 */
@Component
@PropertySource(value = "classpath:common.properties",ignoreResourceNotFound = true,encoding="UTF-8")
@SuppressWarnings("static-access")
public class CommonPropertiesReader implements EnvironmentAware {
    /**
     * 环境
     */
    private static Environment env;

    /**
     * 设置环境
     *
     * @param environment
     * <AUTHOR> 2018-3-18
     */
    @Override
    public void setEnvironment(Environment environment) {
        CommonPropertiesReader.env=environment;
    }

    /**
     * 根据properties Key获取其值
     *
     * @param key common.properties key名称
     * @return    common.properties 匹配的key的值
     * <AUTHOR> 2018-3-18
     */
    public static String getValue(String key) {
        if(env==null) return null;
        try {
            return env.getProperty(key);
        }catch (Exception ex){
            throw ex;
        }
    }

}
