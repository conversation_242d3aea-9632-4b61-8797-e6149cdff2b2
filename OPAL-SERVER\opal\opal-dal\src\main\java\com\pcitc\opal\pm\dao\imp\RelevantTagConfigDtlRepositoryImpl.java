package com.pcitc.opal.pm.dao.imp;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.TypedQuery;

import com.pcitc.opal.common.CommonProperty;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.pcitc.opal.common.CommonResult;
import com.pcitc.opal.common.Pagination;
import com.pcitc.opal.common.PaginationBean;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.RelevantTagConfigDtlRepositoryCustom;
import com.pcitc.opal.pm.pojo.RelevantTagConfigDtl;

/*
 * RelevantTagConfigDtl实体的Repository实现   
 * 模块编号：pcitc_opal_dal_class_RelevantTagConfigDtlRepositoryImpl
 * 作    者：dageng.sun
 * 创建时间：2018/8/2
 * 修改编号：1
 * 描    述：RelevantTagConfigDtl实体的Repository实现
 */
public class RelevantTagConfigDtlRepositoryImpl extends BaseRepository<RelevantTagConfigDtl, Long>
		implements RelevantTagConfigDtlRepositoryCustom {

	/**
	 * 删除相关性位号配置明细实体
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigDtlIds 相关性位号配置明细主键id集合
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult deleteRelevantTagConfigDtl(Long[] relevantTagConfigDtlIds) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			String hql = " from RelevantTagConfigDtl t where t.relevantTagConfigDtlId in (:relevantTagConfigDtlIds)";
			Map<String, Object> paramList = new HashMap<String, Object>();
			List<Long> relevantTagConfigDtlIdList = Arrays.asList(relevantTagConfigDtlIds);
			paramList.put("relevantTagConfigDtlIds", relevantTagConfigDtlIdList);

			TypedQuery<RelevantTagConfigDtl> query = getEntityManager().createQuery(hql, RelevantTagConfigDtl.class);
			this.setParameterList(query, paramList);
			List<RelevantTagConfigDtl> relevantTagConfigList = query.getResultList();
			relevantTagConfigList.forEach(x -> {
				this.getEntityManager().remove(x);
			});

			commonResult.setIsSuccess(true);
			commonResult.setMessage("删除成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		// 返回消息结果对象
		return commonResult;
	}
	
	/**
	 * 获取相关性位号配置明细集合
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigDtlIds 相关性位号配置明细主键Id集合
	 */
	@Override
	public List<RelevantTagConfigDtl> getRelevantTagConfigDtl(Long[] relevantTagConfigDtlIds) {
		try {
			// 查询字符串
			String hql = "from RelevantTagConfigDtl t join fetch t.alarmPoint ap where ap.companyId=:companyId ";
			Map<String, Object> paramList = new HashMap<String, Object>();
			if (relevantTagConfigDtlIds.length > 0) {
				hql += " and  t.relevantTagConfigDtlId in (:relevantTagConfigDtlIds)";
				List<Long> relevantTagConfigDtlIdsList = Arrays.asList(relevantTagConfigDtlIds);
				paramList.put("relevantTagConfigDtlIds", relevantTagConfigDtlIdsList);
			}
			//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			TypedQuery<RelevantTagConfigDtl> query = getEntityManager().createQuery(hql, RelevantTagConfigDtl.class);
			this.setParameterList(query, paramList);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}
	
	/**
	 * 根据参数“相关性位号配置ID”查询<相关性位号配置明细>数据
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 相关性位号配置ID
	 * @param page 分页对象
	 */
	@Override
	public PaginationBean<RelevantTagConfigDtl> getRelevantTagConfigDtl(Long relevantTagConfigId, Pagination page) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("select t from RelevantTagConfigDtl t ");
			hql.append("left join fetch t.alarmPoint ap ");
			hql.append("left join fetch ap.prdtCell pc ");
			hql.append("left join fetch ap.alarmPointType apt ");
			hql.append("left join fetch ap.measUnit mu where 1=1 and ap.companyId=:companyId and pc.companyId=:companyId ");
//			hql.append(" and case when ae.alarm_point_id is not null then ap.in_use  end =1 ");
			hql.append(" and case when ap.alarmPointId is not null then ap.inUse  end =1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			// 相关性位号配置ID
			if (!StringUtils.isEmpty(relevantTagConfigId) && relevantTagConfigId != null) {
				hql.append("and t.relevantTagConfigId=:relevantTagConfigId ");
				paramList.put("relevantTagConfigId", relevantTagConfigId);
			}
//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			// 调用基类方法查询返回结果
			PaginationBean<RelevantTagConfigDtl> bean = this.findAll(page, hql.toString(), paramList);
			return bean;
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 根据参数“相关性位号配置ID”查询<相关性位号配置明细>数据
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 相关性位号配置ID
	 */
	@Override
	public List<RelevantTagConfigDtl> getRelevantTagConfigDtl(Long relevantTagConfigId) {
		try {
			// 查询字符串
			StringBuilder hql = new StringBuilder("select t from RelevantTagConfigDtl t ");
			hql.append("left join fetch t.alarmPoint ap ");
			hql.append("left join fetch ap.prdtCell pc ");
			hql.append("left join fetch ap.alarmPointType apt ");
			hql.append("left join fetch ap.measUnit mu where 1=1 and ap.companyId=:companyId and pc.companyId=:companyId ");
//			hql.append(" and case when ae.alarm_point_id is not null then ap.in_use  end =1 ");
			hql.append(" and case when ap.alarmPointId  is not null then ap.inUse  end =1 ");
			// 参数集合
			Map<String, Object> paramList = new HashMap<String, Object>();
			// 相关性位号配置ID
			if (!StringUtils.isEmpty(relevantTagConfigId) && relevantTagConfigId != null) {
				hql.append("and t.relevantTagConfigId=:relevantTagConfigId ");
				paramList.put("relevantTagConfigId", relevantTagConfigId);
			}
//企业
			CommonProperty commonProperty = new CommonProperty();
			paramList.put("companyId",commonProperty.getCompanyId());
			TypedQuery<RelevantTagConfigDtl> query = getEntityManager().createQuery(hql.toString(), RelevantTagConfigDtl.class);
			this.setParameterList(query, paramList);
			return query.getResultList();
		} catch (Exception ex) {
			throw ex;
		}
	}

	/**
	 * 数据校验
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param relevantTagConfigId 关性位号配置ID
	 * @param alarmPointIds 报警点ID数组
	 */
	@Override
	public CommonResult relevantTagConfigDtlValidation(Long relevantTagConfigId, Long[] alarmPointIds) {
		CommonResult commonResult = new CommonResult();
        try {
            // “相关性位号配置ID”和“报警点ID”做联合唯一校验
            StringBuilder hql = new StringBuilder("from RelevantTagConfigDtl t where 1=1 ");
            Map<String, Object> paramList = new HashMap<String, Object>();
            if(!StringUtils.isEmpty(relevantTagConfigId) && relevantTagConfigId!=null){
            	hql.append("and t.relevantTagConfigId = :relevantTagConfigId ");
            	paramList.put("relevantTagConfigId", relevantTagConfigId);
            }
            if(ArrayUtils.isNotEmpty(alarmPointIds)){
            	hql.append("and t.alarmPointId in (:alarmPointIds) ");
            	List<Long> alarmPointIdsList = Arrays.asList(alarmPointIds);
            	paramList.put("alarmPointIds", alarmPointIdsList);
            }

            long index = this.getCount(hql.toString(), paramList);
            if (index > 0) {
                throw new Exception("相关位号配置重复！");
            }
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
	}

	/**
	 * 保存<相关性位号配置明细>数据
	 * 
	 * <AUTHOR> 2018-08-02 
	 * @param rtcd 相关性位号配置明细实体类
	 */
	@Override
	@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
	public CommonResult addRelevantTagConfigDtl(RelevantTagConfigDtl rtcd) {
		// 初始化消息结果类
		CommonResult commonResult = new CommonResult();
		try {
			this.getEntityManager().persist(rtcd);
			commonResult.setResult(rtcd);
			commonResult.setIsSuccess(true);
			commonResult.setMessage("保存成功！");
		} catch (Exception ex) {
			// 保存出现异常，绑定异常信息在消息结果对象
			commonResult.setIsSuccess(false);
			commonResult.setMessage(ex.getMessage());
		}
		return commonResult;
	}

}
