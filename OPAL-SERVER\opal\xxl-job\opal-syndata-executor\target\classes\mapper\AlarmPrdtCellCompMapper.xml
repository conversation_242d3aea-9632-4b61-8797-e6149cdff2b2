<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pcitc.opal.mapper.AlarmPrdtCellCompMapper">

    <resultMap id="BaseResultMap" type="com.pcitc.opal.pojo.AlarmPrdtCellComp">
            <id property="alarmprdtcellcompId" column="alarmprdtcellcomp_id" jdbcType="BIGINT"/>
            <result property="dcsCodeId" column="dcs_code_id" jdbcType="BIGINT"/>
            <result property="prdtcellSource" column="prdtcell_source" jdbcType="VARCHAR"/>
            <result property="prdtcellId" column="prdtcell_id" jdbcType="BIGINT"/>
            <result property="inUse" column="in_use" jdbcType="BIGINT"/>
            <result property="crtDate" column="crt_date" jdbcType="TIMESTAMP"/>
            <result property="mntDate" column="mnt_date" jdbcType="TIMESTAMP"/>
            <result property="crtUserId" column="crt_user_id" jdbcType="VARCHAR"/>
            <result property="mntUserId" column="mnt_user_id" jdbcType="VARCHAR"/>
            <result property="crtUserName" column="crt_user_name" jdbcType="VARCHAR"/>
            <result property="mntUserName" column="mnt_user_name" jdbcType="VARCHAR"/>
            <result property="opcCodeId" column="opc_code_id" jdbcType="BIGINT"/>
            <result property="unitCode" column="unit_code" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        alarmprdtcellcomp_id,dcs_code_id,prdtcell_source,
        prdtcell_id,in_use,crt_date,
        mnt_date,crt_user_id,mnt_user_id,
        crt_user_name,mnt_user_name,opc_code_id,
        unit_code,company_id
    </sql>
    <select id="selectByCompanyIdByTenantDb" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_ad_alarmprdtcellcomp
        where
        company_id = #{companyId,jdbcType=NUMERIC}
    </select>
</mapper>
