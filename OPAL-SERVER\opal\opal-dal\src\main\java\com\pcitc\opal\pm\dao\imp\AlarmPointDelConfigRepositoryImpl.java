package com.pcitc.opal.pm.dao.imp;

import com.pcitc.opal.common.*;
import com.pcitc.opal.common.dao.BaseRepository;
import com.pcitc.opal.pm.dao.AlarmPointDelConfigRepositoryCustom;
import com.pcitc.opal.pm.pojo.AlarmPointDelConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.boot.CommandLineRunner;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.Transactional;
import java.util.*;

/**
 * @USER: chenbo
 * @DATE: 2022/12/2
 * @TIME: 11:27
 * @DESC:
 **/
@Slf4j
public class AlarmPointDelConfigRepositoryImpl extends BaseRepository<AlarmPointDelConfig, Long> implements AlarmPointDelConfigRepositoryCustom{
    @Override
    public List<AlarmPointDelConfig> selectAlarmPointDelConfigDetail(String[] unitCodes, Date startTime, Date endTime) {

        // 参数集合s
        Map<String, Object> paramList = new HashMap<String, Object>();

        StringBuilder sql = new StringBuilder();
        sql.append("select tpa.unit_code,\n" +
                "       ((select group_concat(t.alarm_point_id) from t_pm_alarmpointgroupdtl t where t.alarm_point_group_id = tpa.alarm_point_group_id)) alarmpoints,\n" +
                "       alarm_flag_id,\n" +
                "       Del_Start_Time,\n" +
                "       Del_End_Time\n" +
                "from t_pm_alarmpointdelconfig f\n" +
                "         inner join t_pm_alarmpointgroup tpa on\n" +
                "        f.Alarm_Point_Group_Id = tpa.alarm_point_group_id\n" +
                "where f.in_use = 1 and f.Company_Id = :companyId and tpa.company_id = :companyId and f.del_status = 2 ");
        //过滤装置
        if (ArrayUtils.isNotEmpty(unitCodes)){
            sql.append(" and tpa.unit_code in (:unit_code) ");
            paramList.put("unit_code", Arrays.asList(unitCodes));
        }

        //<报警剔除配置>“剔除开始时间”小于等于查询条件“结束时间”，并且<报警剔除配置>“剔除结束时间”大于等于查询条件“开始时间”
        sql.append(" and f.Del_Start_Time <= :endTime and f.Del_End_Time >= :startTime");
        paramList.put("endTime", DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss"));
        paramList.put("startTime", DateFormatUtils.format(startTime, "yyyy-MM-dd HH:mm:ss"));

        paramList.put("companyId", new CommonProperty().getCompanyId());

        Query query = getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, paramList);

        query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);

        List<Map<String, Object>> resultList = query.getResultList();

        List<AlarmPointDelConfig> returnList = new ArrayList<>();
        for (Map<String, Object> map : resultList) {
            returnList.add(new AlarmPointDelConfig()
                    .setAlarmPointId(ObjectUtil.objectToString(map.get("alarmpoints")))
                    .setUnitCode(ObjectUtil.objectToString(map.get("unit_code")))
                    .setAlarmFlagId(ObjectUtil.objectToString(map.get("alarm_flag_id")))
                    .setDelStartTime(ObjectUtil.objectToDate(map.get("Del_Start_Time")))
                    .setDelEndTime(ObjectUtil.objectToDate(map.get("Del_End_Time")))
            );
        }
        return returnList;
    }

    @Override
    public List<AlarmPointDelConfig> selectAlarmPointDelConfigDetailById(Integer[] id, Integer companyId) {

        // 参数集合s
        Map<String, Object> paramList = new HashMap<String, Object>();

        StringBuilder sql = new StringBuilder();
        sql.append("select tpa.unit_code,\n" +
                "       ((select group_concat(t.alarm_point_id) from t_pm_alarmpointgroupdtl t where t.alarm_point_group_id = tpa.alarm_point_group_id)) alarmpoints,\n" +
                "       alarm_flag_id,\n" +
                "       Del_Start_Time,\n" +
                "       Del_End_Time," +
                "       Alarm_Point_Del_Config_Id," +
                "       f.company_id\n" +
                "from t_pm_alarmpointdelconfig f\n" +
                "         inner join t_pm_alarmpointgroup tpa on\n" +
                "        f.Alarm_Point_Group_Id = tpa.alarm_point_group_id\n" +
                "where f.in_use = 1 and f.del_status = 2 and Alarm_Point_Del_Config_Id in (:alarmPointDelConfigId) ");


        paramList.put("alarmPointDelConfigId", Arrays.asList(id));

        if (companyId != null){
            sql.append(" and f.Company_Id = :companyId  and tpa.company_id = :companyId ");
            paramList.put("companyId", companyId);
        }


        Query query = getEntityManager().createNativeQuery(sql.toString());
        this.setParameterList(query, paramList);

        query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);

        List<Map<String, Object>> resultList = query.getResultList();

        List<AlarmPointDelConfig> returnList = new ArrayList<>();
        for (Map<String, Object> map : resultList) {
            returnList.add(new AlarmPointDelConfig()
                    .setAlarmPointId(ObjectUtil.objectToString(map.get("alarmpoints")))
                    .setUnitCode(ObjectUtil.objectToString(map.get("unit_code")))
                    .setAlarmFlagId(ObjectUtil.objectToString(map.get("alarm_flag_id")))
                    .setDelStartTime(ObjectUtil.objectToDate(map.get("Del_Start_Time")))
                    .setDelEndTime(ObjectUtil.objectToDate(map.get("Del_End_Time")))
                    .setAlarmPointDelConfigId(ObjectUtil.objectToLong(map.get("Alarm_Point_Del_Config_Id")))
                    .setCompanyId(ObjectUtil.objectToLong(map.get("company_id")))
            );
        }
        return returnList;
    }

    @Override
    public PaginationBean<AlarmPointDelConfigDTO> getAlarmPointDelConfigPage(
            String[] unitCodes, String groupName, Date startTime, Date endTime, Integer inUse, Pagination page, Integer delStatus) {
        // 参数集合
        Map<String, Object> paramList = new HashMap<String, Object>();
        try{
            StringBuilder sql = new StringBuilder();
            sql.append("select new com.pcitc.opal.pm.dao.imp.AlarmPointDelConfigDTO\n" +
                    "(f.alarmPointDelConfigId,\n" +
                    "   tpa.unitCode,\n" +
                    "   u.sname,\n" +
                    "   tpa.groupName,\n" +
                    "   f.alarmPointGroupId,\n" +
                    "   f.delStartTime,\n" +
                    "   f.delEndTime,\n" +
                    "   f.inUse,\n" +
                    "   f.crtDate,f.mntDate,f.crtUserName,f.mntUserName,f.des,f.delStatus,f.delDataStatus)\n" +
                    "   from AlarmPointDelConfig f\n" +
                    "         left join AlarmPointGroup tpa on\n" +
                    "        f.alarmPointGroupId = tpa.alarmPointGroupId\n" +
                    "         left join Unit u on\n" +
                    "        u.stdCode = tpa.unitCode\n" +
                    "where f.companyId = :companyId and tpa.companyId = :companyId ");
            //过滤装置
            if (ArrayUtils.isNotEmpty(unitCodes)){
                sql.append(" and tpa.unitCode in (:unitCodes) ");
                paramList.put("unitCodes", Arrays.asList(unitCodes));
            }
            if (StringUtils.isNotEmpty(groupName)){
                sql.append(" and upper(tpa.groupName) like upper(:groupName) escape '/' ");
                paramList.put("groupName","%" + this.sqlLikeReplace(groupName) + "%");
            }
            if (inUse!=null && inUse!=-1){
                sql.append(" and f.inUse = (:inUse) ");
                paramList.put("inUse",inUse);
            }
            if (delStatus != null && delStatus != -1){
                sql.append(" and f.delStatus = :delStatus ");
                paramList.put("delStatus", delStatus);
            }

            //<报警剔除配置>“剔除开始时间”小于等于查询条件“结束时间”，并且<报警剔除配置>“剔除结束时间”大于等于查询条件“开始时间”
            sql.append(" and f.delStartTime <= :endTime and f.delEndTime >= :startTime");
            paramList.put("endTime", endTime);
            paramList.put("startTime", startTime);

            paramList.put("companyId",Long.valueOf(new CommonProperty().getCompanyId()));

            Query query = getEntityManager().createQuery(sql.toString());
            this.setParameterList(query, paramList);
            Long count =Long.valueOf(query.getResultList().size());

            //重写分页
            BaseRepository br =new BaseRepository();
            PaginationBean<AlarmPointDelConfigDTO> bean = br.findCusTomAll(this.getEntityManager(),page,count, sql.toString(), paramList,AlarmPointDelConfigDTO.class);
            return bean;
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public AlarmPointDelConfigDTO getSingleAlarmPointDel(Long alarmPointDelConfigId) {

        Map<String, Object> paramList = new HashMap<String, Object>();

        StringBuilder sql = new StringBuilder();
        sql.append("select new com.pcitc.opal.pm.dao.imp.AlarmPointDelConfigDTO\n" +
                "(f.alarmPointDelConfigId,\n" +
                "   tpa.unitCode,\n" +
                "   u.sname,\n" +
                "   tpa.groupName,\n" +
                "   f.alarmPointGroupId,\n" +
                "   f.delStartTime,\n" +
                "   f.delEndTime,\n" +
                "   f.inUse,\n" +
                "   f.crtDate,f.mntDate,f.crtUserName,f.mntUserName,f.des,f.delStatus,f.delDataStatus)\n" +
                "   from AlarmPointDelConfig f\n" +
                "         left join AlarmPointGroup tpa on\n" +
                "        f.alarmPointGroupId = tpa.alarmPointGroupId\n" +
                "         left join Unit u on\n" +
                "        u.stdCode = tpa.unitCode\n" +
                "where f.alarmPointDelConfigId = :alarmPointDelConfigId ");
            paramList.put("alarmPointDelConfigId",alarmPointDelConfigId);

        TypedQuery<AlarmPointDelConfigDTO> query  =this.getEntityManager().createQuery(sql.toString(),AlarmPointDelConfigDTO.class);
        this.setParameterList(query, paramList);
        return query.getSingleResult();
    }

    @Override
    @Transactional
    public CommonResult addAlarmPointDel(AlarmPointDelConfig alarmPointDelConfig) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            this.getEntityManager().persist(alarmPointDelConfig);
            commonResult.setResult(alarmPointDelConfig);
            commonResult.setIsSuccess(true);
            if (alarmPointDelConfig.getDelStatus().equals(0)){
                commonResult.setMessage("保存成功！");
            }else if (alarmPointDelConfig.getDelStatus().equals(1)){
                commonResult.setMessage("提交成功！");
            }
        } catch (Exception ex) {
            // 保存出现异常，绑定异常信息在消息结果对象
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        return commonResult;
    }

    @Override
    @Transactional
    public CommonResult updateAlarmPointDel(AlarmPointDelConfig alarmPointDelConfig) {
        // 初始化消息结果类
        CommonResult commonResult = new CommonResult();
        try {
            getEntityManager().merge(alarmPointDelConfig);
            commonResult.setResult(alarmPointDelConfig);
            commonResult.setIsSuccess(true);
            commonResult.setMessage("更新成功！");
        } catch (Exception ex) {
            commonResult.setIsSuccess(false);
            commonResult.setMessage(ex.getMessage());
        }
        // 返回消息结果对象
        return commonResult;
    }

    @Transactional
    @Override
    public Integer updateDelStatusByAlarmPointDelConfig(Integer[] alarmPointDelConfigId, Integer delStatus) {
        StringBuilder sql = new StringBuilder();
        sql.append("update t_pm_alarmpointdelconfig " +
                "set DEL_STATUS     = :delStatus," +
                "    APRO_TIME      = :aproTime," +
                "    APRO_USER_ID   = :aproUserId," +
                "    APRO_USER_NAME = :aproUserName "+
                "where Alarm_Point_Del_Config_Id in (:ids) and DEL_STATUS = 1 and Company_Id = :companyId");
        Query nativeQuery = this.getEntityManager().createNativeQuery(sql.toString());

        CommonProperty commonProperty = new CommonProperty();
        String userName = null;
        try {
            userName = commonProperty.getUserName();
        }catch (Exception e){
            log.error("获取用户名异常" + e.getMessage());
        }
        nativeQuery.setParameter("ids", Arrays.asList(alarmPointDelConfigId))
                .setParameter("companyId", commonProperty.getCompanyId())
                .setParameter("aproUserId", commonProperty.getUserId())
                .setParameter("aproUserName", userName)
                .setParameter("aproTime", new Date())
                .setParameter("delStatus", delStatus);

        try {
            return nativeQuery.executeUpdate();
        }catch (Exception e){
            log.error("审批失败" + e.getMessage());
        }
        return 0;
    }

    @Transactional
    @Override
    public Integer updateDelDataStatusById(Integer[] alarmPointDelConfigId, Integer delDataStatus) {

        String sql = "update t_pm_alarmpointdelconfig set del_data_status = :delDataStatus where Alarm_Point_Del_Config_Id in (:alarmPointDelConfigId)";

        Query query = getEntityManager().createNativeQuery(sql);

        query.setParameter("delDataStatus", delDataStatus);
        query.setParameter("alarmPointDelConfigId", Arrays.asList(alarmPointDelConfigId));

        return query.executeUpdate();
    }

    @Transactional
    @Override
    public List<AlarmPointDelConfig> selectDelDataFailure() {

        StringBuilder sql = new StringBuilder();
        sql.append("select tpa.unit_code,\n" +
                "       ((select group_concat(t.alarm_point_id) from t_pm_alarmpointgroupdtl t where t.alarm_point_group_id = tpa.alarm_point_group_id)) alarmpoints,\n" +
                "       alarm_flag_id,\n" +
                "       Del_Start_Time,\n" +
                "       Del_End_Time," +
                "       Alarm_Point_Del_Config_Id," +
                "       f.company_id\n" +
                "from t_pm_alarmpointdelconfig f\n" +
                "         inner join t_pm_alarmpointgroup tpa on\n" +
                "        f.Alarm_Point_Group_Id = tpa.alarm_point_group_id\n" +
                "where f.in_use = 1 and del_data_status not in  (-1,5) and DEL_STATUS = 2 and TIMESTAMPDIFF(DAY,APRO_TIME,SYSDATE()) < 7");



        Query query = getEntityManager().createNativeQuery(sql.toString());

        query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);

        List<Map<String, Object>> resultList = query.getResultList();

        List<AlarmPointDelConfig> returnList = new ArrayList<>();
        for (Map<String, Object> map : resultList) {
            returnList.add(new AlarmPointDelConfig()
                    .setAlarmPointId(ObjectUtil.objectToString(map.get("alarmpoints")))
                    .setUnitCode(ObjectUtil.objectToString(map.get("unit_code")))
                    .setAlarmFlagId(ObjectUtil.objectToString(map.get("alarm_flag_id")))
                    .setDelStartTime(ObjectUtil.objectToDate(map.get("Del_Start_Time")))
                    .setDelEndTime(ObjectUtil.objectToDate(map.get("Del_End_Time")))
                    .setAlarmPointDelConfigId(ObjectUtil.objectToLong(map.get("Alarm_Point_Del_Config_Id")))
                    .setCompanyId(ObjectUtil.objectToLong(map.get("company_id")))
            );
        }
        return returnList;
    }

    @Transactional
    @Override
    public Integer deleteAlarmPointDel(List<Long> alarmPointDelConfigId) {
        // 初始化消息结果类
        Integer res = 0;
        try {

            //只能删除状态为未提交和已驳回的数据
            String sql = "delete from t_pm_alarmpointdelconfig where Alarm_Point_Del_Config_Id in (:alarmPointDelConfigId) and DEL_STATUS in (0,3) and company_id = :companyId";

            Query nativeQuery = getEntityManager().createNativeQuery(sql);

            nativeQuery.setParameter("alarmPointDelConfigId", alarmPointDelConfigId)
                    .setParameter("companyId", new CommonProperty().getCompanyId());


            res = nativeQuery.executeUpdate();

        } catch (Exception ex) {
           log.error("删除异常" + ex.getMessage());
        }
        return res;
    }


}
