package com.pcitc.opal.af.bll.entity;

import java.util.ArrayList;
import java.util.List;

/*
 * 时序事件分析-时序事件实体
 * 模块编号：pcitc_opal_service_class_seqEventAnalysisModel
 * 作  　者：dageng.sun
 * 创建时间：2017-11-15
 * 修改编号：1
 * 描    述：时序事件分析-时序事件实体
 */
public class SeqEventAnalysisEntity {
	
    /**
	 * 横坐标集合
	 */
	private List<ArrayList<Object>> yaxisList;
	/**
	 * 提示语集合
	 */
	private List<String> tipsList;
	
	
	public List<ArrayList<Object>> getYaxisList() {
		return yaxisList;
	}
	public void setYaxisList(List<ArrayList<Object>> yaxisList) {
		this.yaxisList = yaxisList;
	}
	
	public List<String> getTipsList() {
		return tipsList;
	}
	public void setTipsList(List<String> tipsList) {
		this.tipsList = tipsList;
	}
}
